# How to Contribute

If you've made it this far, welcome to the project and thank you for your contribution! 🎉

The following guide should provide all the information needed to integrate smoothly into the
development process for NASM Edge.

### Table of contents

- [Helpful Links](#helpful-links)
- [Quick Note about Branching](#quick-note-about-branching)
- [Where do I start?](#where-do-i-start) 
- [What if I am ready for code now?](#what-if-i-am-ready-for-code-now)
  - [I am working on a bug fix](#i-am-working-on-a-bug-fix)
  - [I am working on a new feature](#i-am-working-on-a-new-feature)
- [Pull Requests](#pull-requests)
  - [Pull Request Rules](#pull-request-rules)
- [Reviewing Pull Requests](#reviewing-pull-requests)
  - [Examples of review criteria](#examples-of-review-criteria)
- [Still have questions on how to get started?](#still-have-questions-on-how-to-get-started)

# Helpful Links

- [GitFlow Workflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow)
- [Sourcetree](https://www.sourcetreeapp.com)
- [React Native](https://reactnative.dev/docs/getting-started)

# Quick Note about Branching

In an effort to support a continuous delivery cycle, it is strongly recommended to understand and
utilize the [GitFlow Workflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow).
This workflow defines a set of practices in branching and merging within git and includes a
command line tool to aid in implementing these practices.

In addition to using GitFlow Workflow, it is highly recommended to use a GUI tool to manage branching, commits, pushes, etc.
The recommended is Sourcetree since it is free and comes with the GitFlow built in. In Sourcetree, it can be
found in the menu bar at Repository -> Git Flow / Hg Flow.

# Where do I start?

Reviewing he "Helpful Links" above is a great place to begin getting acquainted with the project.

After the above, the next place to look would be our Project Management Tool, Jira.

- [MEDL Jira](https://medlenterprises.atlassian.net/secure/RapidBoard.jspa?rapidView=4&projectKey=NE)
- [REEA Jira](https://reeaglobal.atlassian.net/jira/software/projects/NAS/boards/9)

# What if I am ready for code now?

After understanding the [GitFlow Workflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow),
the answer to where you base your contributions are: **it depends.**

## I am working on a bug fix

When addressing bugs in the project, take a moment to understand the priority, severity, and affected
environments.

- Highest Priority affecting live version of the application:
    - Create a hotfix branch according to [GitFlow Workflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow).
    If a hotfix branch is already open, apply the fix to the currently open hotfix branch. Refer to ticket/PM to confirm if there are doubts.
- Bugs that affect an open release:
    - Submit PRs to the currently open release.
- Low priority bugs or bugs that will be release on the next cycle.
    - Create a branch off of develop with the prefix `bugfix/`. PRs should target `develop` for the merge.

## I am working on a new feature

Create a feature branch according to [GitFlow Workflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow).

In order to increase visibility in the Jira tickets, the following naming convention should be followed:
- `feature/{Jira Ticket Number}-{short descriptive feature summary}`

For example, If I am assigned the Jira ticket `NE-1234` and this ticket's story is to allow a user to sign in or sign up
with "Google" then I would create a branch off `origin/develop` and name it something like:
- `feature/NE-1234-google-authentication`

# Pull Requests

The `origin/master` and `origin/develop` branches are protected and strictly follow the guidelines of the
GitFlow workflow. These branches are updated through merging of child branches, and the merging is facilitated
through the process of submitting a Pull Request (PR) in Github.

PRs serve a few purposes in our development process. A PR serves as a space to get feedback on the code
we plan to integrate into the application. Getting the rest of the team involved in the review is an opportunity
to gain feedback on the code and allows the other devs a chance to anticipate how the changes affect other
areas of the app.

**Every PR submitted should have a title that contains the Jira Ticket ID and should contain a description.**

The general naming convention for the PR title should contain the Jira Ticket ID. A good rule of thumb is
to use the name of the branch itself since it should have a Jira Ticket ID in its name.

The PR's description should contain more information than just the link to a Jira ticket. Please be courteous to
your fellow developers and provide a short description of the changes in the code. It helps to call out
particular areas of the code that have a specific function but might seem odd to a reviewer. For example,
if a bug fix requires a dependency to be downgraded, one should describe why a downgrade is necessary otherwise it
might be assumed it was a mistake.

Pictures and videos can speak a thousand words, so screenshots and demo videos are ALWAYS a welcome addition to any PR 
description. This is especially true for features that require a fair amount of setup beforehand to experience. 
Providing this kind of media can really help a reviewer better understand what the changes in the PR bring and
could help the review go a little smoother ;D.

When you are ready to submit a PR, use the following checklist to ensure PR consistency:

- [ ] The code I am about to submit has been tested by myself to the best of my ability and is free of defects.
- [ ] The code represents a complete feature or fix.
- [ ] The title of my PR follows the above naming convention.
- [ ] The description of my PR provides a summary of the changes.
- [ ] The description contains a link back to the ticket/s the PR addresses.
- [ ] I have added the appropriate amount of reviewers and those reviewers are active contributors of the project.

## Pull Request Rules

- At least one reviewer must approve the changes before merging into target branch.
- Any merge conflicts found while the PR is open will need to be addressed in the branch the PR represents before merging.
- If at least one reviewer has "Requested Changes", make sure to address the comments of the reviewer and request a re-review
  from all reviewers. 
  - Looks something like this: ![Screen Shot 2021-02-26 at 1 42 05 PM](https://user-images.githubusercontent.com/25021133/109362545-145fe100-7840-11eb-87e5-a7d3962e933f.png)
  - This is true even if one reviewer has approved the changes already. Sometimes another set of eyes sees something
    that another person's does not ;D.
- When a PR is merged and closed, please delete the remote branch.
  - Once a branch is merged into its target branch, it should never gain life again. If something needs to be addressed
    related to changes from a recently closed branch, open a new branch ideally with a different name.

# Reviewing Pull Requests

Yes, reviewing other developers code is another important contribution to the project code!

First and foremost, we ask that the feedback provided during the PR process be kept in a professional and constructive
format. 

Since a PR can be considered "complete" after one reviewer has approved the changes, one should be confident in the 
changes before placing their "stamp of approval". 

- Should you have concerns, submit the review as a "Comment" or "Request Changes". 
- If requesting changes, please be sure to provide details and possibly suggestions on the changes.
  - If another reviewer has "Approved" the changes, be sure to call them out in your comments using `@` reference
    so they can re-review.

## Examples of review criteria

- Check for syntax consistency.
  - Ideally any changes or new code should follow similar syntax with the rest of the project.
- Logic errors.
- Regression bugs.
  - We want to do our best to ensure changes we make to existing files won't have a negative impact on other areas of 
    the application.
    
This isn't an exhaustive list of criteria so use your best judgment when reviewing ;D.

The goal is to, first, have self describing code that is clean and readable for new and existing contributors. The second goal
is for the code to be efficient and bug free 🪳🙅 ‍😎.

# Still have questions on how to get started?

Maybe you've come across a scenario that doesn't exactly fit those mentioned above. If you still have questions or
concerns on how to proceed, connect with a fellow developer on the project or reach out to the PM; 
together they should be able to get you back on track ;D.
