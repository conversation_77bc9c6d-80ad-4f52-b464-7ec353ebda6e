{"name": "nasm", "version": "3.19.2", "private": true, "scripts": {"android": "react-native run-android --mode=developmentDebug --appIdSuffix=debug", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint 'src/**/*.js'", "clean": "watchman watch-del-all && rm -rf node_modules && npm install && (cd ios && rm -rf Pods && pod install) && npm start -- --reset-cache", "flow": "flow", "android-release-bundle": "mkdir android/app/src/main/assets; react-native bundle --platform android --dev false --entry-file index.js   --bundle-output android/app/src/main/assets/index.android.bundle", "postinstall": "npx jetify", "lintstaged": ".git/hooks/pre-commit"}, "dependencies": {"@react-native-async-storage/async-storage": "~1.17.3", "@react-native-camera-roll/camera-roll": "~5.0.4", "@react-native-clipboard/clipboard": "~1.13.2", "@react-native-community/datetimepicker": "~6.1.2", "@react-native-community/netinfo": "~11.3.1", "@react-native-community/slider": "~4.2.1", "@react-native-firebase/analytics": "~14.9.0", "@react-native-firebase/app": "~14.9.0", "@react-native-masked-view/masked-view": "~0.2.6", "@react-native-picker/picker": "~2.4.2", "@react-navigation/bottom-tabs": "~6.5.7", "@react-navigation/material-top-tabs": "~6.6.2", "@react-navigation/native": "~6.1.6", "@react-navigation/stack": "~6.3.16", "@reduxjs/toolkit": "~1.3.6", "@sentry/react-native": "~6.0.0", "@stream-io/flat-list-mvcp": "~0.10.1", "@stripe/stripe-react-native": "~0.37.0", "accordion-collapse-react-native": "~0.4.0", "agora-react-native-rtm": "~1.5.1", "axios": "~0.17.1", "babel-plugin-transform-inline-environment-variables": "~0.3.0", "country-code-lookup": "0.0.19", "fbjs": "~3.0.4", "formik": "~2.2.5", "jail-monkey": "~2.8.0", "js-base64": "~3.7.5", "libphonenumber-js": "~1.7.53", "lodash.clonedeep": "~4.5.0", "lodash.mergewith": "~4.6.2", "mixpanel-react-native": "~2.4.0", "moment": "~2.26.0", "normalizr": "~3.6.0", "obfuscator-io-metro-plugin": "~2.1.1", "prop-types": "~15.7.2", "querystring": "~0.2.0", "react": "18.2.0", "react-native": "0.73.5", "react-native-actions-sheet": "~0.2.6", "react-native-agora": "4.3.0", "react-native-autolink": "~3.0.0", "react-native-awesome-alerts": "~1.4.1", "react-native-branch": "~6.0.0", "react-native-calendar-strip": "~2.2.5", "react-native-calendars": "~1.296.0", "react-native-communications": "~2.2.1", "react-native-device-info": "~10.13.1", "react-native-draggable-flatlist": "~4.0.1", "react-native-flipper": "0.212.0", "react-native-fs": "~2.19.0", "react-native-gesture-handler": "~2.15.0", "react-native-get-random-values": "~1.7.2", "react-native-google-autocomplete": "~0.1.9", "react-native-haptic-feedback": "~1.13.1", "react-native-iap": "~12.12.2", "react-native-idle-timer": "^2.1.6", "react-native-image-crop-picker": "~0.41.4", "react-native-image-resizer": "~1.4.5", "react-native-in-app-review": "~4.3.3", "react-native-keyboard-aware-scroll-view": "~0.9.5", "react-native-launch-arguments": "~3.1.2", "react-native-localize": "~3.0.6", "react-native-markdown-display": "~7.0.0-alpha.2", "react-native-menu": "~0.23.0", "react-native-modal-datetime-picker": "~13.1.2", "react-native-modal-overlay": "~1.3.1", "react-native-onesignal": "~5.2.1", "react-native-orientation-locker": "~1.6.0", "react-native-pager-view": "~5.4.11", "react-native-permissions": "4.1.5", "react-native-picker-select": "~8.0.3", "react-native-reanimated": "^3.15.0", "react-native-rook-sdk-apple-health": "~0.12.3", "react-native-rook-sdk-health-connect": "~1.0.0", "react-native-safe-area-context": "~4.5.1", "react-native-screens": "~3.29.0", "react-native-share": "~10.0.2", "react-native-simple-radio-button": "~2.7.4", "react-native-snap-carousel": "~3.9.1", "react-native-splash-screen": "~3.3.0", "react-native-svg": "~12.3.0", "react-native-tab-view": "~3.1.1", "react-native-textarea": "~1.0.4", "react-native-tracking-transparency": "~0.1.0", "react-native-vector-icons": "~10.0.3", "react-native-video": "~6.4.5", "react-native-video-controls": "~2.8.1", "react-native-webview": "~11.23.1", "react-native-wheel-pick": "~1.2.0", "react-redux": "~7.2.4", "redux": "~4.0.5", "redux-flipper": "~2.0.1", "redux-saga": "~1.1.3", "redux-thunk": "~2.3.0", "stream-chat-react-native": "~5.22.1", "uuid": "~8.3.2", "victory-native": "~36.3.2", "yup": "~0.28.5"}, "devDependencies": {"@babel/core": "~7.20.0", "@babel/eslint-parser": "~7.21.3", "@babel/eslint-plugin": "~7.19.1", "@babel/plugin-syntax-export-namespace-from": "~7.8.3", "@babel/preset-env": "^7.20.0", "@babel/runtime": "~7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-cli": "~6.26.0", "babel-jest": "~29.6.3", "babel-plugin-module-resolver": "~5.0.0", "babel-plugin-transform-export-extensions": "~6.22.0", "babel-preset-flow": "~6.23.0", "babel-preset-react-native": "~5.0.2", "deprecated-react-native-prop-types": "~4.0.0", "detox": "~20.1.2", "eslint": "~8.19.0", "eslint-config-airbnb": "~18.2.1", "eslint-plugin-import": "~2.27.5", "eslint-plugin-jsx-a11y": "~6.4.1", "eslint-plugin-react": "~7.23.2", "eslint-plugin-react-hooks": "~4.6.0", "eslint-plugin-react-native": "~4.0.0", "eslint-plugin-react-redux": "~4.0.0", "flow-bin": "~0.49.1", "husky": "~3.1.0", "jest": "^29.6.3", "jetifier": "~1.6.6", "lint-staged": "~10.5.4", "prettier": "~2.8.8", "react-native-dotenv": "~0.2.0", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "rnpm": {"assets": ["assets/fonts"]}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,css}|!package-lock.json|!node_modules/*": ["./node_modules/.bin/prettier --write", "./node_modules/.bin/eslint --cache --fix"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}