const path = require('path');

module.exports = {
  env: {
    'react-native/react-native': true,
  },
  extends: [
    'plugin:react/recommended',
    'airbnb',
    'plugin:react-redux/recommended',
    'plugin:react-hooks/recommended',
  ],
  globals: {
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly',
  },
  parser: '@babel/eslint-parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 2018,
    requireConfigFile: false,
    sourceType: 'module',
  },
  plugins: ['react', 'react-native', 'react-redux', '@babel'],
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.json', '.native.js'],
      },
    },
  },
  rules: {
    'react-native/no-unused-styles': 2,
    'react-native/split-platform-components': 2,
    'react-native/no-inline-styles': 'warn',
    'react-native/no-color-literals': 'warn',
    'react-native/no-raw-text': 'off',
    'react-native/no-single-element-style-arrays': 2,
    'no-use-before-define': ['error', { functions: true, classes: true, variables: false }],
    camelcase: 'off',
    'global-require': 'off',
    'max-len': 'off',
    'react/destructuring-assignment': 'off',
    'react/prop-types': 'warn',
    'react/require-default-props': ['warn'],
    'react/forbid-prop-types': ['off'],
    'react/default-props-match-prop-types': ['off'],
    'react/no-access-state-in-setstate': ['warn'],
    'prefer-const': [
      'error',
      {
        destructuring: 'all',
        ignoreReadBeforeAssign: false,
      },
    ],
    'react-redux/connect-prefer-named-arguments': 'warn',
    'react/no-unused-state': 'warn',
    'react/jsx-filename-extension': 'off',
    'no-useless-escape': 'off',
    'no-param-reassign': ['error', { props: false }],
    'import/no-extraneous-dependencies': [
      'error',
      { packageDir: path.resolve(__dirname) },
    ],
    'no-restricted-syntax': 'warn',
    'react/jsx-props-no-spreading': 'off',
    'react/no-array-index-key': 'warn',
    'no-unused-expressions': 'off',
    '@babel/no-unused-expressions': 'error',
    'class-methods-use-this': 'warn',
  },
};
