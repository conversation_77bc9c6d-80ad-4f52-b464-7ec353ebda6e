# Report build status next to github commit.
# 
# - Fill in USER and APP with your user and appname in appcenter
# - Also provide GITHUB_TOKEN env variable in build config
#   (create token in GitHub Settings / Developer Settings / Personal access tokens, with 'repo:status' scope)
#
# Contributed by: <PERSON><PERSON><PERSON>
# https://zeyomir.github.io

if [ "$PLATFORM" = "ios" ]; then
    APP="NASM-Edge-iOS-Staging"
else
    APP="NASM-Edge-Android-Staging"
fi

match="/"
replace="%2F"
branch_url=${APPCENTER_BRANCH//$match/$replace}
build_url=https://appcenter.ms/orgs/MEDL-Mobile/apps/$APP/build/branches/$branch_url/builds/$APPCENTER_BUILD_ID

github_set_status() {
    local status job_status
    local "${@}"
    github_endpoint=https://api.github.com/repos/medlmobileenterprises/$BUILD_REPOSITORY_NAME/statuses/$BUILD_SOURCEVERSION
    echo "github endpoint: $github_endpoint"
    curl -X POST https://api.github.com/repos/medlmobileenterprises/$BUILD_REPOSITORY_NAME/statuses/$BUILD_SOURCEVERSION -d \
        "{
            \"state\": \"$status\", 
            \"target_url\": \"$build_url\",
            \"description\": \"Build status: $job_status\",
            \"context\": \"ci/appcenter/$PLATFORM\"
        }" \
        -H "Authorization: token $GITHUB_TOKEN" \
        -H "Accept: application/vnd.github.v3.raw+json"
}

github_set_status_pending() {
    github_set_status status="pending" job_status="In progress"
}

github_set_status_success() {
    github_set_status status="success" job_status="$AGENT_JOBSTATUS"
}

github_set_status_fail() {
    github_set_status status="failure" job_status="$AGENT_JOBSTATUS"
}