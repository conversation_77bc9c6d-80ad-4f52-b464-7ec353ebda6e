# Firebase App Distribution Setup Guide

This guide explains how to set up Firebase App Distribution for the NASM Edge app using GitHub Actions.

## Prerequisites

1. A Firebase project with the Android and iOS apps already registered
2. Firebase CLI installed on your local machine
3. GitHub repository with necessary permissions

## Step 1: Set Up Firebase Project

If not already done:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Create or select your project
3. Add Android and iOS apps with the bundle ID `com.nasm.edge`
4. Download and replace the configuration files:
   - For Android: `google-services.json` in `android/app/`
   - For iOS: `GoogleService-Info.plist` in `ios/`

## Step 2: Generate Service Account Key

1. In the Firebase console, go to Project Settings > Service Accounts
2. Click "Generate new private key" and save the JSON file
3. Keep this file secure - it will be used in GitHub Secrets

## Step 3: Configure GitHub Secrets

Add the following secrets to your GitHub repository:

1. `FIREBASE_SERVICE_ACCOUNT_JSON`: The entire content of the service account JSON file
2. `FIREBASE_ANDROID_APP_ID`: Your Android app ID from Firebase (found in Project Settings > Your Apps)
3. `FIREBASE_IOS_APP_ID`: Your iOS app ID from Firebase (found in Project Settings > Your Apps)
4. `IOS_BUILD_CERTIFICATE_BASE64`: Base64-encoded iOS distribution certificate
5. `IOS_BUILD_PROVISION_PROFILE_BASE64`: Base64-encoded iOS provisioning profile for the main app
6. `IOS_ONESIGNAL_PROVISION_PROFILE_BASE64`: Base64-encoded iOS provisioning profile for OneSignal extension
7. `KEYCHAIN_PASSWORD`: A password for the temporary keychain

## Step 4: Prepare iOS Certificates and Profiles

1. Export your iOS distribution certificate as a p12 file
2. Get the provisioning profiles for both the main app and OneSignal extension
3. Base64 encode these files:

```bash
cat distribution_certificate.p12 | base64 | pbcopy
cat profile.mobileprovision | base64 | pbcopy
cat onesignal_profile.mobileprovision | base64 | pbcopy
```

4. Add these encoded strings to the respective GitHub secrets

## Step 5: Set Up Tester Groups in Firebase

1. In the Firebase console, go to App Distribution
2. Create a tester group (e.g., "testers")
3. Add the email addresses of your testers

## Step 6: Verify GitHub Workflow

1. Check that `.github/workflows/firebase-deploy.yml` exists in your repository
2. Make sure scripts are executable:

```bash
chmod +x .github/scripts/update_version_codes.sh
chmod +x .github/scripts/install_certs_and_profiles.sh
```

## Step 7: Test the Workflow

1. Push a change to the `develop` branch or open a PR against it
2. Monitor the GitHub Actions tab to see if the workflow runs successfully
3. Check Firebase App Distribution to see if the build was uploaded

## Troubleshooting

- If builds fail in GitHub Actions, check the workflow logs for specific errors
- Ensure all provisioning profiles and certificates are valid and not expired
- Verify that the bundle ID in the code matches the one in Firebase (`com.nasm.edge`)
- Check that the Firebase service account has the necessary permissions 