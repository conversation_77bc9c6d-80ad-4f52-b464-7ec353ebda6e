# ClubConnect Analytics Implementation Summary

## Overview

This document summarizes the implementation of ClubConnect analytics tracking across both the mobile app (nasm) and web app (nasm-edge-web) projects. The implementation tracks four key user behaviors as requested by the PM:

1. **Profile Switching**: Users switching between personal and club profiles
2. **Workout Assignment**: Users assigning workouts from club profile
3. **Client Group Management**: Users creating and managing client groups from club profile
4. **Chat Functionality**: Users using chat features from club profile

## Implementation Details

### Analytics Infrastructure

#### Mobile App (nasm)
- **File**: `src/util/Analytics.js`
- **Services**: Mixpanel + Firebase Analytics (dual tracking)
- **Method**: Enhanced existing universal `track()` function
- **New Functions Added**:
  - `logClubConnectProfileSwitch()`
  - `logClubConnectWorkoutAssigned()`
  - `logClubConnectClientGroupCreated()`
  - `logClubConnectClientGroupUpdated()`
  - `logClubConnectChatInitiated()`
  - `logClubConnectChatMessageSent()`

#### Web App (nasm-edge-web)
- **File**: `src/util/Analytics.js` (newly created)
- **Services**: Google Tag Manager (GTM)
- **Method**: GTM dataLayer events
- **New Functions Added**: Same as mobile app

### Event Schemas

All events follow consistent schema across platforms:

#### 1. Profile Switching Event
```javascript
{
  event_name: 'club_connect_profile_switch',
  user_id: string,
  from_profile_type: 'personal' | 'club',
  to_profile_type: 'personal' | 'club',
  club_id: string | null,
  club_name: string | null,
  location_id: string | null,
  location_name: string | null,
  timestamp: ISO_string
}
```

#### 2. Workout Assignment Event
```javascript
{
  event_name: 'club_connect_workout_assigned',
  user_id: string,
  club_id: string,
  club_name: string,
  location_id: string | null,
  workout_type: 'individual' | 'program' | 'custom',
  client_count: number,
  assignment_method: 'individual' | 'group',
  client_group_id: string | null,
  timestamp: ISO_string
}
```

#### 3. Client Group Events
```javascript
// Creation
{
  event_name: 'club_connect_client_group_created',
  user_id: string,
  club_id: string,
  club_name: string,
  location_id: string | null,
  group_id: string,
  group_name: string,
  client_count: number,
  timestamp: ISO_string
}

// Updates
{
  event_name: 'club_connect_client_group_updated',
  user_id: string,
  club_id: string,
  club_name: string,
  location_id: string | null,
  group_id: string,
  group_name: string,
  client_count: number,
  update_type: 'name_change' | 'members_added' | 'members_removed',
  timestamp: ISO_string
}
```

#### 4. Chat Events
```javascript
// Chat Initiation
{
  event_name: 'club_connect_chat_initiated',
  user_id: string,
  club_id: string,
  club_name: string,
  location_id: string | null,
  chat_type: 'individual' | 'group',
  participant_count: number,
  client_group_id: string | null,
  timestamp: ISO_string
}

// Message Sending
{
  event_name: 'club_connect_chat_message_sent',
  user_id: string,
  club_id: string,
  club_name: string,
  location_id: string | null,
  chat_type: 'individual' | 'group',
  message_type: 'text' | 'image' | 'file',
  timestamp: ISO_string
}
```

## Files Modified

### Mobile App (nasm)

#### Core Analytics
- `src/util/Analytics.js` - Added ClubConnect analytics functions and constants

#### Profile Switching
- `src/screens/MainStack/ProfileList.js` - Added profile switch tracking
- `src/reducers/clubConnectReducer.js` - Added analytics import

#### Workout Assignment
- `src/reducers/selectedProgramReducer.js` - Added workout assignment tracking in `scheduleProgram()` function

#### Client Groups
- `src/screens/GroupTabs/AddGroup.js` - Added group creation tracking
- `src/components/Dialogs/AddNewGroupForm.js` - Added group creation and update tracking

#### Chat Functionality
- `src/screens/Chat/NewConversation.js` - Added chat initiation tracking for both individual and group chats

### Web App (nasm-edge-web)

#### Core Analytics
- `src/util/Analytics.js` - Created new analytics module with GTM integration

#### Profile Switching
- `src/reducers/clubConnectReducer.js` - Enhanced `handleProfileClick()` with analytics tracking

#### Workout Assignment
- `src/reducers/selectedProgramReducer.js` - Added workout assignment tracking in `scheduleProgram()` function

#### Client Groups
- `src/components/Dialogs/AddNewGroupForm.js` - Added group creation and update tracking

#### Chat Functionality
- Chat functionality is primarily backend-focused in web app, main tracking implemented in mobile app

## Key Implementation Features

### 1. Conditional Tracking
- Analytics only fire when user is in club profile mode (`club_id` present)
- Graceful handling when club data is not available
- No tracking for personal profile activities

### 2. Error Handling
- All analytics calls wrapped in try-catch blocks
- Errors logged but don't block user experience
- Fallback behavior when analytics services unavailable

### 3. Data Consistency
- Identical event schemas across mobile and web platforms
- Consistent field naming and data types
- Standardized timestamp format (ISO strings)

### 4. Performance Optimization
- Non-blocking analytics calls
- Minimal impact on user experience
- Efficient data collection without UI delays

### 5. Privacy Compliance
- No PII collected beyond user IDs
- Club and location data only when relevant
- Anonymized user behavior tracking

## Testing and Validation

### Testing Guide
- Comprehensive testing document created: `CLUBCONNECT_ANALYTICS_TESTING_GUIDE.md`
- Covers all event types and edge cases
- Platform-specific validation procedures
- Debug and troubleshooting instructions

### Validation Points
- Event firing verification
- Data accuracy checks
- Analytics platform integration
- Error handling validation
- Performance impact assessment

## Deployment Considerations

### Environment Configuration
- Different analytics tokens for dev/staging/production
- Feature flag compatibility
- Club Connect multi-location support

### Monitoring Setup
- Analytics dashboard configuration
- Data quality monitoring
- Alert setup for anomalies
- Regular reporting cadence

## Success Metrics

The implementation enables tracking of:

1. **Profile Switcher Usage**: Number of users switching between personal and club profiles
2. **Workout Assignment Activity**: Volume and patterns of workout assignments from club profiles
3. **Client Group Management**: Group creation, modification, and usage patterns
4. **Chat Engagement**: Chat initiation and usage from club profiles

## Next Steps

1. **Testing Phase**: Execute comprehensive testing using provided guide
2. **Staging Deployment**: Deploy to staging environment for validation
3. **User Acceptance Testing**: Validate with actual ClubConnect users
4. **Production Deployment**: Roll out to production environment
5. **Monitoring Setup**: Configure dashboards and alerts
6. **Data Analysis**: Begin collecting and analyzing ClubConnect usage patterns

## Technical Notes

### Dependencies
- Mobile: Existing Mixpanel and Firebase Analytics setup
- Web: Existing Google Tag Manager configuration
- No new external dependencies required

### Backward Compatibility
- All changes are additive
- No breaking changes to existing functionality
- Existing analytics continue to work unchanged

### Maintenance
- Analytics functions are self-contained
- Easy to modify or extend event schemas
- Clear separation between ClubConnect and general analytics

This implementation provides comprehensive tracking of ClubConnect user behavior while maintaining high code quality, performance, and user experience standards.
