# ClubConnect Analytics Testing Guide

This document provides comprehensive testing instructions for all implemented ClubConnect analytics events across both the mobile app (nasm) and web app (nasm-edge-web).

## Overview

The following ClubConnect analytics events have been implemented:

1. **Profile Switching** - Track switching between personal and club profiles
2. **Workout Assignment** - Track workout assignments from club profile
3. **Client Group Creation/Management** - Track group creation and updates
4. **Chat Initiation** - Track chat functionality usage from club profile

## Analytics Infrastructure

### Mobile App (nasm)
- **Services**: Mixpanel + Firebase Analytics (dual tracking)
- **Implementation**: `src/util/Analytics.js`
- **Event Tracking**: Universal `track()` function

### Web App (nasm-edge-web)
- **Services**: Google Tag Manager (GTM)
- **Implementation**: `src/util/Analytics.js`
- **Event Tracking**: GTM dataLayer events

## Testing Prerequisites

### Environment Setup
1. Ensure you have access to development/staging environments
2. Verify analytics services are configured:
   - Mobile: Mixpanel and Firebase Analytics dashboards
   - Web: Google Tag Manager and Google Analytics dashboards
3. Have test club profiles and client data available
4. Ensure ClubConnect features are enabled in test environment

### Test User Setup
1. Create test trainer account with ClubConnect access
2. Set up test club profile with location data
3. Create test client accounts
4. Create test client groups

## Event Testing Procedures

### 1. Profile Switching Analytics

#### Mobile App Testing
**Event**: `club_connect_profile_switch`

**Test Steps**:
1. Open mobile app and log in as trainer
2. Navigate to profile switcher (ProfileList screen)
3. Switch from personal to club profile
4. Switch from club to personal profile
5. Switch between different club profiles (if available)

**Expected Data**:
```javascript
{
  event_name: 'club_connect_profile_switch',
  user_id: 'trainer_user_id',
  from_profile_type: 'personal' | 'club',
  to_profile_type: 'personal' | 'club',
  club_id: 'club_id_string',
  club_name: 'Club Name',
  location_id: 'location_id_string',
  location_name: 'Location Name',
  timestamp: 'ISO_timestamp'
}
```

#### Web App Testing
**Event**: `club_connect_profile_switch`

**Test Steps**:
1. Open web app and log in as trainer
2. Use profile menu to switch profiles
3. Test switching between personal and club profiles

**Validation**:
- Check GTM dataLayer for events
- Verify all required fields are present
- Confirm club data is included when switching to/from club profiles

### 2. Workout Assignment Analytics

#### Mobile App Testing
**Event**: `club_connect_workout_assigned`

**Test Steps**:
1. Switch to club profile
2. Navigate to client schedule
3. Assign a single workout to individual client
4. Assign a program (multiple workouts) to individual client
5. Assign workout/program to client group
6. Test both "Schedule Once" and "Schedule Repeat" options

**Expected Data**:
```javascript
{
  event_name: 'club_connect_workout_assigned',
  user_id: 'trainer_user_id',
  club_id: 'club_id_string',
  club_name: 'Club Name',
  location_id: 'location_id_string',
  workout_type: 'individual' | 'program' | 'custom',
  client_count: 1 | group_size,
  assignment_method: 'individual' | 'group',
  client_group_id: 'group_id' | null,
  timestamp: 'ISO_timestamp'
}
```

#### Web App Testing
**Event**: `club_connect_workout_assigned`

**Test Steps**:
1. Switch to club profile
2. Navigate to client or group schedule
3. Assign workouts using ScheduleProgram flow
4. Test both individual and group assignments

### 3. Client Group Analytics

#### Mobile App Testing
**Events**: 
- `club_connect_client_group_created`
- `club_connect_client_group_updated`

**Test Steps**:
1. Switch to club profile
2. Navigate to Groups section
3. Create new client group with multiple members
4. Edit existing group (change name, add/remove members)
5. Test group creation from AddGroup screen

**Expected Data for Creation**:
```javascript
{
  event_name: 'club_connect_client_group_created',
  user_id: 'trainer_user_id',
  club_id: 'club_id_string',
  club_name: 'Club Name',
  location_id: 'location_id_string',
  group_id: 'created_group_id',
  group_name: 'Group Name',
  client_count: number_of_clients,
  timestamp: 'ISO_timestamp'
}
```

#### Web App Testing
**Events**: 
- `club_connect_client_group_created`
- `club_connect_client_group_updated`

**Test Steps**:
1. Switch to club profile
2. Use AddNewGroupForm to create groups
3. Edit existing groups
4. Test different update types (name change, member changes)

### 4. Chat Analytics

#### Mobile App Testing
**Events**:
- `club_connect_chat_initiated`
- `club_connect_chat_message_sent`

**Test Steps**:
1. Switch to club profile
2. Navigate to Chat/NewConversation
3. Start individual chat with client
4. Start group chat with client group
5. Send messages in both chat types

**Expected Data for Chat Initiation**:
```javascript
{
  event_name: 'club_connect_chat_initiated',
  user_id: 'trainer_user_id',
  club_id: 'club_id_string',
  club_name: 'Club Name',
  location_id: 'location_id_string',
  chat_type: 'individual' | 'group',
  participant_count: number_of_participants,
  client_group_id: 'group_id' | null,
  timestamp: 'ISO_timestamp'
}
```

## Validation Checklist

### Data Quality Checks
- [ ] All required fields are present in events
- [ ] User IDs are correctly captured
- [ ] Club data (ID, name, location) is accurate
- [ ] Timestamps are in correct ISO format
- [ ] Event names match specification exactly

### Analytics Platform Verification

#### Mobile App (Mixpanel + Firebase)
- [ ] Events appear in Mixpanel dashboard
- [ ] Events appear in Firebase Analytics
- [ ] Event properties are correctly mapped
- [ ] User properties are set correctly

#### Web App (GTM + GA)
- [ ] Events appear in GTM debug mode
- [ ] Events are forwarded to Google Analytics
- [ ] Custom dimensions are populated
- [ ] Event parameters are correctly structured

### Edge Cases Testing
- [ ] Test with missing club data
- [ ] Test profile switching without club access
- [ ] Test with empty client groups
- [ ] Test with network connectivity issues
- [ ] Test error handling in analytics calls

## Debugging and Troubleshooting

### Mobile App Debug Steps
1. Enable debug logging in Analytics.js
2. Check device logs for analytics errors
3. Use Mixpanel debug mode
4. Verify Firebase Analytics in debug view

### Web App Debug Steps
1. Open browser developer tools
2. Check GTM debug mode
3. Monitor dataLayer events
4. Verify network requests to analytics services

### Common Issues
- **Missing Events**: Check if user is in club profile mode
- **Missing Data**: Verify club profile data is available
- **Duplicate Events**: Check for multiple event triggers
- **Wrong Data**: Validate data mapping in analytics functions

## Performance Considerations

### Mobile App
- Analytics calls are wrapped in try-catch blocks
- Events are batched where possible
- No blocking of user experience

### Web App
- GTM handles event queuing
- Analytics errors don't affect user interface
- Events are sent asynchronously

## Reporting and Monitoring

### Key Metrics to Monitor
1. **Profile Switching Frequency**: How often users switch profiles
2. **Workout Assignment Volume**: Number of assignments from club profiles
3. **Group Management Activity**: Group creation and modification rates
4. **Chat Engagement**: Chat initiation and usage patterns

### Dashboard Setup
- Create custom dashboards in analytics platforms
- Set up alerts for unusual patterns
- Monitor data quality and completeness
- Track adoption of ClubConnect features

## Next Steps

After successful testing:
1. Deploy to staging environment
2. Conduct user acceptance testing
3. Monitor analytics data quality
4. Deploy to production
5. Set up ongoing monitoring and reporting
