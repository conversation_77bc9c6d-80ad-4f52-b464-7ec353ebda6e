const NASMStrings = {
  edit: 'EDIT',
  delete: 'DELETE',

  trainerDashDatePrompt: "Let's do something awesome!",
  trainerDashWelcomeBack: 'WELCOME, ',
  trainerDashSessions: 'SESSIONS',
  trainerDashActive: 'ACTIVE',
  trainerDashClients: 'CLIENTS',

  clientDashEditProgram: 'EDIT PROGRAM',
  clientDashDeleteProgram: 'DELETE PROGRAM',

  profileLogout: 'LOGOUT',
  profileEdit: 'EDIT PROFILE',
  removeClient: 'REMOVE CLIENT',
  profileNoGoalSet: 'NO GOAL SET',
  trainerDashNameSort: 'NAME (A-Z)',
  trainerDashRecentSort: 'RECENT ACTIVITY',
  trainerDashGoalSort: 'GOAL',
  trainerDashAllFilter: 'ALL',
  trainerDashActiveFilter: 'ACTIVE',
  trainerDashInactiveFilter: 'IN-ACTIVE',

  tlSelectActivity: 'SELECT AN ACTIVITY TYPE',
  tlSelectIntentsity: 'SELECT AN INTENSITY LEVEL',
  tlSelectTime: 'SELECT AN AMOUNT OF TIME',
  tlWeight: 'WEIGHT LBS',

  exerciseDetailsLevelAssessment: 'This is a CES or PES level assessment.',
  exerciseDetailsNasmLink: 'See nasm.org for more details.',

  clientDashAddProgram: 'Add Program',
  clientDashNewAssessment: 'New Assessment',

  programCatalogAscending: 'Ascending',
  programCatalogDescending: 'Descending',
  programCatalogAll: 'All',
  programCatalogMove: 'Move',
  programCatalogPerform: 'Perform',
  programCatalogRecover: 'Recover',

  addWorkoutSortAZ: 'A-Z',
  addWorkoutSortZA: 'Z-A',

  scheduleWorkoutDays: 'WORKOUT DAYS',
  scheduleWorkoutProgramDuration: 'PROGRAM DURATION',
  scheduleWorkoutRestDays: "Don't forget to schedule rest days!",
  scheduleWorkoutProgramName: 'PROGRAM NAME',

  updateTermsAlertTitle: 'Terms and Conditions has been updated',
  updateTermsAlertDescription:
    'There has been a very important update to the Terms and Conditions. Read them over now.',
  declineTermsAlertTitle: 'Decline the Terms',
  declineTermsAlertDescription:
    'You won’t be able to continue through the app unless you agree to the updated Terms and Conditions. Are you sure you want to decline the Terms?',
  declinePrivacyPolicyAlertTitle: 'Decline the Privacy Policy',
  declinePrivacyPolicyAlertDescription:
    'You won’t be able to continue through the app unless you agree to the updated Privacy Policy. Are you sure you want to decline the Privacy Policy?',

  appUpdateRequired: 'REQUIRED',
  appUpdateRecommended: 'RECOMMENDED',
  appUpdateNotRequired: 'NOT_REQUIRED',

  helpPhone: '************',
  helpEmail: '<EMAIL>',
  helpWeb: 'https://www.nasm.org/edge',
  helpPhoneUK: '0333-400-0353',
  helpEmailUK: '<EMAIL>',
  helpWebUK: 'https://www.nasm.org/edge',

  // CONNECT AVATAR NUTRITION ACCOUNT STRINGS
  connectWithAvatar: 'Connect with Avatar',
  connectedToAvatar: 'Connected to Avatar',
  titleConnectAccount: 'Share your nutrition\ntracking with your trainer.',
  titleAccountConnected: 'Your account is connected.\n',
  subTitleConnectAccount: 'Allow trainer to monitor your:',
  subTitleCAccountConnected: 'Trainer can monitor your:',
  macronutrients: 'Macronutrients',
  dailyFoodTracking: 'Daily Food Tracking',
  enterYourCredentials: 'Enter your credentials',
  forgotPassword: 'Forgot Password',
  avatarTermsAndConditions:
    'I agree to share my Avatar Nutrition tracking\ninformation with my trainer.',
  notAMember: 'Not a member?',
  downloadApp: 'Download app',
  submit: 'Submit',
  remove: 'Remove',
  continue: 'Continue',
  cancel: 'Cancel',
  submitConfirmationTitle:
    'Avatar Nutrition data will\nreplace current nutrition data',
  submitConfirmationDescription:
    'This action is not reversible, are you sure you would like to continue?',
  removeConfirmationTitle: 'Remove Account',
  removeConfirmationDescription:
    'Are you sure you would like to remove your Avatar Nutrition account?',
  avatarUnlinkTitle: 'Unlink account?',
  avatarUnlinkText:
    'The account you are attempting to link is already associated with EDGE. Would you like to use it with this account instead?',
};

module.exports = { NASMStrings };
