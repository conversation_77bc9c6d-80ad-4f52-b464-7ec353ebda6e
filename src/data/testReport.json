{"DateTaken": "2017-05-22T00:00:00", "GeneratedTest": null, "GeneratableTest": {"ID": 2, "Abbreviation": "ROM", "Name": "Range of Motion Test", "ShortName": "ROM Test", "Description": "Perform Range of Motion testing utilizing a goniometer to measure and record joint range of motion. Enter the value measured per joint in the corresponding box for the Right and Left side. Items highlighted in blue are those identified as key joints to test based on Movement Efficiency. Upon completion of ROM measurements click Complete Test to proceed forward.", "Route": "romtest", "DisplayOrder": 3, "TestType": {"TestTypeID": 2, "TestTypeName": "ROM", "Category": "Range of Motion"}, "DefaultTest": true, "Incomplete": true, "CanTakeTest": false}, "GeneratedProgramId": -1, "UnlinkedPreviousTest": null, "CanGenerateProgram": true, "CanAddReport": true, "GivenBy": "<PERSON>", "ClinicalReport": null, "TestResults": [{"RiskLevel": {"ID": 1, "Name": "High Risk", "Color": "Red", "LowerBound": null, "UpperBound": null}, "BodyRegionResult": {"UpperRightScore": 37.5, "UpperLeftScore": 37.5, "LowerRightScore": 37.23, "LowerLeftScore": 70.07, "RightScore": 37.35, "LeftScore": 54.86, "UpperDifferenceScore": 0.0, "LowerDifferenceScore": 61.21, "TotalDifferenceScore": 37.98, "UpperRightRiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "UpperLeftRiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "LowerRightRiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "LowerLeftRiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "RightRiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "LeftRiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "UpperDifferenceRiskLevel": {"ID": 3, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "LowerDifferenceRiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "TotalDifferenceRiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "ExerciseResults": [{"TestExerciseID": 1, "TestExerciseName": "2-<PERSON><PERSON>", "Score": 52.22, "Pain": true, "ImageId": "exercise1", "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "TestCheckPoints": [{"Name": "Foot/Ankle", "Measurements": [{"ID": 1, "Name": "Foot Turns Out", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 1, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 2, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 2, "Name": "Foot Flattens", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 3, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 4, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 9, "Name": "<PERSON><PERSON> of Foot Lifts", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 13, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 14, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "Knee", "Measurements": [{"ID": 3, "Name": "<PERSON><PERSON> Moves In (Valgus)", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 5, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 6, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 4, "Name": "<PERSON><PERSON> Moves Out (Varus)", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 7, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 8, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "L-P-H-C", "Measurements": [{"ID": 5, "Name": "Excessive Forward Lean", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 9, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 6, "Name": "Low Back Arches", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 10, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 7, "Name": "Low Back Rounds", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 11, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 10, "Name": "Asymmetrical Weight Shift", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 15, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 16, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "Shoulder", "Measurements": [{"ID": 8, "Name": "Arms Fall Forward", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 12, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}]}, {"TestExerciseID": 2, "TestExerciseName": "2-<PERSON><PERSON> with Heel Lift", "Score": 40.0, "Pain": true, "ImageId": "exercise2", "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "TestCheckPoints": [{"Name": "Foot/Ankle", "Measurements": [{"ID": 11, "Name": "Foot Turns Out", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 17, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 18, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 12, "Name": "Foot Flattens", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 19, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 20, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "Knee", "Measurements": [{"ID": 13, "Name": "<PERSON><PERSON> Moves In (Valgus)", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 21, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 22, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 14, "Name": "<PERSON><PERSON> Moves Out (Varus)", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 23, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 24, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "L-P-H-C", "Measurements": [{"ID": 15, "Name": "Excessive Forward Lean", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 25, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 16, "Name": "Low Back Arches", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 26, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 17, "Name": "Low Back Rounds", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 27, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 19, "Name": "Asymmetrical Weight Shift", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 29, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 30, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "Shoulder", "Measurements": [{"ID": 18, "Name": "Arms Fall Forward", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 28, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}]}, {"TestExerciseID": 3, "TestExerciseName": "1-<PERSON><PERSON>", "Score": 41.66, "Pain": false, "ImageId": "exercise3", "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "TestCheckPoints": [{"Name": "Foot/Ankle", "Measurements": [{"ID": 20, "Name": "Foot Flattens", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 31, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 32, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "Knee", "Measurements": [{"ID": 21, "Name": "<PERSON><PERSON> Moves In (Valgus)", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 33, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 34, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 22, "Name": "<PERSON><PERSON> Moves Out (Varus)", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 35, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 36, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "L-P-H-C", "Measurements": [{"ID": 23, "Name": "Uncontrolled Trunk: Trunk Flexion, Trunk Rotation and/or Hip Shift", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 37, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 38, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 24, "Name": "Loss of Balance", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 39, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 40, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}]}, {"TestExerciseID": 4, "TestExerciseName": "Push Up", "Score": 40.0, "Pain": false, "ImageId": "exercise4", "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "TestCheckPoints": [{"Name": "Spine", "Measurements": [{"ID": 25, "Name": "Head Moves Forward", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 41, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "L-P-H-C", "Measurements": [{"ID": 26, "Name": "Low Back Arches/<PERSON><PERSON><PERSON> Protrudes", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 42, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 27, "Name": "Scapular Winging", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 43, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}, {"Name": "Knee", "Measurements": [{"ID": 28, "Name": "Knees Bend", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 44, "MeasurementType": null, "BodySide": "WholeBody", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}]}, {"TestExerciseID": 5, "TestExerciseName": "Shoulder Movement", "Score": 50.0, "Pain": true, "ImageId": "exercise5", "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "TestCheckPoints": [{"Name": "Shoulder", "Measurements": [{"ID": 29, "Name": "Flexion: Compensation during movement/Unable to bring hand to wall", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 45, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 46, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 30, "Name": "Internal Rotation: Compensation during movement/Unable to bring hand to mid-line of trunk", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 47, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 48, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 31, "Name": "External Rotation: Compensation during movement/Unable to bring hand to wall", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 49, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 50, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 32, "Name": "Horizontal Abduction: Compensation during movement/Unable to bring hand to wall", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 51, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 52, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}]}, {"TestExerciseID": 6, "TestExerciseName": "Trunk/Lumbar Spine Movements", "Score": 0.0, "Pain": false, "ImageId": "exercise6", "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "TestCheckPoints": [{"Name": "L-P-H-C", "Measurements": [{"ID": 33, "Name": "Trunk Lateral Flexion: Compensation during movement/Unable to reach lateral joint line of knee", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 53, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 54, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 34, "Name": "Trunk Rotation: Compensation during movement/Unable to rotate shoulder to midline of trunk", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 55, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 56, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}]}, {"TestExerciseID": 7, "TestExerciseName": "Cervical Spine Movement", "Score": 50.0, "Pain": true, "ImageId": "exercise7", "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}, "TestCheckPoints": [{"Name": "Cervical Spine", "Measurements": [{"ID": 35, "Name": "Lateral Flexion: Compensation during movement/Unable to side-bend half the distance to shoulder", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 57, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 58, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}, {"ID": 36, "Name": "Rotation: Compensation during movement/Unable to rotate chin to shoulder", "WrittenInstructions": null, "Media": null, "IncludeInScore": false, "ScoreWeight": 0.0, "CheckForSymmetry": false, "SymmetryWeight": null, "Optimal": null, "Inputs": [{"ID": 59, "MeasurementType": null, "BodySide": "Right", "SideLabel": null, "GroupLabel": null, "MovementDetected": false, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}, {"ID": 60, "MeasurementType": null, "BodySide": "Left", "SideLabel": null, "GroupLabel": null, "MovementDetected": true, "MeasuredValue": null, "Score": null, "PairedInputID": null, "MetricLabel": null, "ImperialLabel": null, "Placeholder": null}], "Score": null, "IsRequired": null, "IsRecommended": null, "MeasurementGroup": false, "PerformTemplate": null}]}]}], "BodyAreaMap": {"FootAnkleRight": {"BodyAreaID": 1, "BodyAreaName": "Foot/Ankle", "BodySideID": 1, "BodySideName": "Right", "Score": 43.9, "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "KneeRight": {"BodyAreaID": 2, "BodyAreaName": "Knee", "BodySideID": 1, "BodySideName": "Right", "Score": 37.68, "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "LPHCRight": {"BodyAreaID": 3, "BodyAreaName": "L-P-H-C", "BodySideID": 1, "BodySideName": "Right", "Score": 33.34, "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "ShoulderRight": {"BodyAreaID": 6, "BodyAreaName": "Shoulder", "BodySideID": 1, "BodySideName": "Right", "Score": 57.14, "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "SpineRight": {"BodyAreaID": 7, "BodyAreaName": "Spine", "BodySideID": 1, "BodySideName": "Right", "Score": 58.33, "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "LowBackRight": {"BodyAreaID": 9, "BodyAreaName": "Low Back", "BodySideID": 1, "BodySideName": "Right", "Score": 42.86, "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "FootAnkleLeft": {"BodyAreaID": 1, "BodyAreaName": "Foot/Ankle", "BodySideID": 2, "BodySideName": "Left", "Score": 56.1, "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "KneeLeft": {"BodyAreaID": 2, "BodyAreaName": "Knee", "BodySideID": 2, "BodySideName": "Left", "Score": 71.02, "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "LPHCLeft": {"BodyAreaID": 3, "BodyAreaName": "L-P-H-C", "BodySideID": 2, "BodySideName": "Left", "Score": 66.66, "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "ShoulderLeft": {"BodyAreaID": 6, "BodyAreaName": "Shoulder", "BodySideID": 2, "BodySideName": "Left", "Score": 57.14, "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "SpineLeft": {"BodyAreaID": 7, "BodyAreaName": "Spine", "BodySideID": 2, "BodySideName": "Left", "Score": 58.33, "RiskLevel": {"ID": 2, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}, "LowBackLeft": {"BodyAreaID": 9, "BodyAreaName": "Low Back", "BodySideID": 2, "BodySideName": "Left", "Score": 31.93, "RiskLevel": {"ID": 1, "Name": "", "Color": "", "LowerBound": null, "UpperBound": null}}}, "Id": 198221, "DateTaken": "2017-05-22T00:00:00", "Score": 39.72, "Test": {"ID": 1, "Abbreviation": "ME", "Name": "Movement Efficiency Test", "ShortName": "ME Test", "Description": "Perform the sequence of movements listed and for each identify and record whether compensations occur by clicking the appropriate selection. A YES response indicates that a compensation is present and/or the athlete cannot successfully perform the movement. Upon completing each step in the test process click Save and Continue to proceed forward.", "Route": "metest", "DisplayOrder": 2, "TestType": {"TestTypeID": 1, "TestTypeName": "ME", "Category": "Movement Efficiency"}, "DefaultTest": true, "Incomplete": true, "CanTakeTest": false}, "TestTitle": "Movement Efficiency Test", "IsCurrentTest": true, "TestNotes": null, "GivenBy": "<PERSON>", "CompletedDate": "2017-05-22T19:06:10"}]}