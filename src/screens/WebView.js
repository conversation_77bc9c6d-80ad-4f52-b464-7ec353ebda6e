import React, { Component } from 'react';
import { StatusBar, StyleSheet, KeyboardAvoidingView } from 'react-native';
import { WebView } from 'react-native-webview';
import PropTypes from 'prop-types';
import analytics from '@react-native-firebase/analytics';
import DeviceInfo from 'react-native-device-info';
import { ROOK_WEB_URL } from '../api/rook/RookConstants';

// Constants
const defaultUri = 'https://www.nasm.org';
let isback = false;

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    state: PropTypes.shape({
      params: PropTypes.shape({
        title: PropTypes.string,
        uri: PropTypes.string,
      }),
    }),
  }).isRequired,
};

// Class
class WebViewContainer extends Component {
  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      const uri = this.props.route.params?.uri ?? this.props.uri ?? defaultUri;
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'webpage', uri });
    });
    isback = false;
    this.setNavigationOptions();
  }

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
  }

  setNavigationOptions = () => {
    this.props.navigation.setOptions({
      title: this.props.route.params?.title ?? 'WEB',
    });
  };

  render() {
    const uri = this.props.route.params?.uri ?? this.props.uri ?? defaultUri;
    return (
      <KeyboardAvoidingView style={styles.container}>
        <WebView
          // startInLoadingState
          style={styles.container}
          androidHardwareAccelerationDisabled
          source={{ uri }}
          cacheEnabled={false}
          onNavigationStateChange={(event) => {
            if (event.url.includes(ROOK_WEB_URL) && !isback) {
              isback = true;
              this.props.navigation.goBack();
            }
          }}
          userAgent={`${DeviceInfo.getUserAgent()} - NASM - android`}
        />
      </KeyboardAvoidingView>
    );
  }
}

// Export
WebViewContainer.propTypes = propTypes;
export default WebViewContainer;

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
