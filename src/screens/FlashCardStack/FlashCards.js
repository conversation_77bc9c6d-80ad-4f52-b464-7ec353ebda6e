import React, { Component } from 'react';

// Components
import {
  View, TouchableOpacity, Text, Dimensions, Image,
} from 'react-native';
import Carousel from 'react-native-snap-carousel';
import _cloneDeep from 'lodash.clonedeep';
import analytics from '@react-native-firebase/analytics';
import { FlashCard } from '../../components';
import { scaleWidth, scaleHeight, curvedScale } from '../../util/responsive';

// Analytics

// Styles
import { colors } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import ProgressBar from '../../components/ProgressBar';

// Images
const rightArrow = require('../../resources/imgRightArrowGray.png');
const leftArrow = require('../../resources/imgLeftArrowGray.png');
const flipButton = require('../../resources/btnFlipCardNrm.png');

const flipButtonHeight = curvedScale(Image.resolveAssetSource(flipButton).height) / 1.3;
const flipButtonWidth = curvedScale(Image.resolveAssetSource(flipButton).width) / 1.3;

class FlashCards extends Component {
  constructor(props) {
    super(props);
    const randomCards = this.randomizeCards(this.props.route.params?.cards);
    this.state = {
      cards: randomCards,
      color: this.props.route.params?.color,
      allDomainColors: this.props.route.params?.allDomainColors,
      domains: this.props.route.params?.domains,
      cptVersion: this.props.route.params?.cptVersion,
    };
    this.cardRefs = {};
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      const domainName = this.props.route.params?.domainName ?? 'n/a';
      analytics().logEvent('screen_view', {
        screen_name: 'flashcards_domain',
        domain: domainName,
        cpt_version: this.state.cptVersion,
      });
    });
  }

  componentWillUnmount() {
    const domain = this.props.route.params?.domainName ?? 'n/a';
    let carousel_index = 0;
    if (this.carousel) {
      carousel_index = this.carousel.currentIndex;
    }
    analytics().logEvent('flashcard_exit', {
      domain,
      carousel_index,
      cpt_version: this.state.cptVersion,
    });
    this.unsubscribeFocus();
  }

  onPressFlip = () => {
    if (this.carousel) {
      const cardId = this.state.cards[this.carousel.currentIndex].id;
      this.cardRefs[cardId].card.flip();
    }
  };

  onPressNext = () => {
    this.carousel.snapToNext();
  };

  onPressPrev = () => {
    this.carousel.snapToPrev();
  };

  onSnapToItem = (index) => {
    const total = this.state.cards.length;
    this.props.navigation.setParams({ title: `${index + 1} of ${total}` });
  };

  getProcessPercentage = () => {
    let currentValue = 1;
    if (this.carousel && this.carousel.currentIndex) {
      currentValue = this.carousel.currentIndex + 1;
    }
    return (currentValue / this.state.cards.length) * 100;
  };

  getDomainFromId(domainId) {
    const domain = this.state.domains.filter(
      (domains) => domains.domainId === domainId,
    );
    return domain[0].domainName;
  }

  getIndividualCardColor = (domain) => this.state.allDomainColors[domain];

  static navigationOptions = ({ navigation, route }) => {
    const title = route.params?.title ?? 'Flash Cards';
    return {
      title,
      headerLeft: () => (
        <HeaderLeftButton
          onPress={() => navigation.goBack()}
          titleStyle={styles.headerText}
        />
      ),
    };
  };

  randomizeCards = (cards) => {
    const shuffle = _cloneDeep(cards);
    for (let c = shuffle.length - 1; c > 0; c -= 1) {
      const r = Math.floor(Math.random() * c);
      const temp = shuffle[c];
      shuffle[c] = shuffle[r];
      shuffle[r] = temp;
    }
    return shuffle;
  };

  renderItem = (item) => {
    const domainId = this.state.cptVersion === 6 ? item.item.tags[1] : item.item.tags[0];
    return (
      <FlashCard
        ref={(ref) => {
          this.cardRefs[item.item.id] = ref;
        }}
        key={item.item.id}
        cardData={item}
        domainName={this.getDomainFromId(domainId)}
        color={
          this.state.color
            ? this.state.color
            : this.getIndividualCardColor(domainId)
        }
        cptVersion={this.state.cptVersion}
        onCardPress={(_card) => {
          if (this.carousel.currentIndex === item.index) {
            _card.flip();
          } else {
            this.carousel.snapToItem(item.index);
          }
        }}
      />
    );
  };

  renderProgressBar = () => (
    <ProgressBar
      progress={this.getProcessPercentage()}
      progressColor={colors.medYellow}
      barStyle={styles.barStyle}
      style={styles.progressBackground}
    />
  );

  render() {
    return (
      <View style={styles.container}>
        {this.renderProgressBar()}
        <Carousel
          ref={(c) => {
            this.carousel = c;
          }}
          data={this.state.cards}
          renderItem={this.renderItem}
          enableSnap
          sliderWidth={width}
          itemWidth={scaleWidth(70)}
          inactiveSlideOpacity={0.6}
          extraData={this.state}
          containerCustomStyle={{ paddingBottom: scaleHeight(2) }}
          removeClippedSubviews={false}
          initialNumToRender={5}
          maxToRenderPerBatch={10}
          onSnapToItem={this.onSnapToItem}
        />
        <Text style={styles.cptVersion}>
          CPT
          {this.state.cptVersion}
        </Text>
        <View style={styles.carouselButtonContainer}>
          <TouchableOpacity
            style={{ right: scaleWidth(22) }}
            onPress={this.onPressPrev}
          >
            <View style={styles.arrowBorder}>
              <Image style={styles.backButton} source={leftArrow} />
            </View>
          </TouchableOpacity>
          <TouchableOpacity onPress={this.onPressFlip}>
            <Image
              style={styles.flipButton}
              source={flipButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={{ left: scaleWidth(22) }}
            onPress={this.onPressNext}
          >
            <View style={styles.arrowBorder}>
              <Image style={styles.backButton} source={rightArrow} />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}

const { width } = Dimensions.get('window');

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#FFF',
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.white,
  },
  carouselButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: scaleHeight(4),
  },
  backButton: {
    padding: 14,
    resizeMode: 'contain',
  },
  flipButton: {
    width: flipButtonWidth,
    height: flipButtonHeight,
  },
  arrowBorder: {
    borderWidth: 2,
    borderColor: colors.colorsFillLight2,
    borderRadius: 80,
    justifyContent: 'center',
    padding: scaleHeight(1.7),
  },
  cptVersion: {
    fontSize: 14,
    color: 'rgb(182, 189, 195)',
    textAlign: 'center',
    marginTop: scaleHeight(-3.5),
    paddingBottom: scaleHeight(2.5),
  },
  barStyle: {
    borderRadius: 0,
  },
  progressBackground: {
    borderRadius: 0,
    width: '100%',
    borderWidth: 0,
    backgroundColor: colors.nasmBlue,
    height: 10,
  },
};

export default FlashCards;
