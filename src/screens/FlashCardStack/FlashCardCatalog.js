import React, { Component } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { connect } from 'react-redux';
// Components
import {
  View,
  TouchableOpacity,
  Image,
  FlatList,
  StatusBar,
  Text,
  LayoutAnimation,
  SafeAreaView,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import {
  CardDomain,
  LoadingSpinner,
  ScaledText,
  CPTVersionSelector,
} from '../../components';
import { scaleHeight, scaleWidth } from '../../util/responsive';
import nasm from '../../dataManager/apiConfig';
import { userHasCpt7Permission } from '../../util/PermissionUtils';
import { FEATURE_FLAGS, androidSafeLayoutAnimation } from '../../constants';

// Analytics

// Styles
import { colors, shadow } from '../../styles';

const CPT7 = require('../../resources/cpt7Img.png');
const closeBtn = require('../../resources/closeCircle.png');

// Images
const xBtn = require('../../resources/btnCloseWhite.png');
const flashCardImg = require('../../../assets/imgFlashCards.png');

const cardColors = [
  colors.peaGreen,
  colors.nasmBlue,
  colors.macaroniAndCheese,
  colors.pinkishPurple,
  colors.azure,
  colors.goodGreen,
];

class FlashCardCatalog extends Component {
  static navigationOptions = {
    title: null,
    headerShown: false,
  };

  constructor(props) {
    super(props);
    const hasCpt7 = userHasCpt7Permission(props.currentUser);
    this.state = {
      flashCardData: {},
      domains: [],
      domainColors: {},
      combinedFlashCards: [],
      selectedSet: {},
      isLoading: true,
      cptLoading: false,
      cptVersion: FEATURE_FLAGS.CPT7_ENABLED && hasCpt7 ? 7 : 6,
      CPT7FlashCards: false,
    };
  }

  componentDidMount() {
    this.getAllFlashCards(this.state.cptVersion);
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', {
        screen_name: 'flashcard_catalog',
        cpt_version: this.state.cptVersion,
      });
    });
    this.showCPTv7Notification(this.props.currentUser.id);
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressDomain = (domain) => {
    const cards = this.getFlashCardsByDomain(domain.domainId);
    this.props.navigation.navigate('FlashCards', {
      title: `1 of ${cards.length}`,
      domainName: domain.domainName,
      color: this.state.domainColors[domain.domainId],
      cards,
      domains: this.state.domains,
      cptVersion: this.state.cptVersion,
    });
    analytics().logEvent('flashcard_start', {
      domain: domain.domainName,
      cpt_version: this.state.cptVersion,
    });
  };

  onPressPracticeAll = () => {
    this.props.navigation.navigate('FlashCards', {
      title: 'Practice All',
      cards: this.state.combinedFlashCards,
      domains: this.state.domains,
      allDomainColors: this.state.domainColors,
      cptVersion: this.state.cptVersion,
    });
  };

  getAllFlashCards = (cptVersion) => {
    nasm.api.getAllFlashCards('', cptVersion).then((flashCardData) => {
      this.setState({ flashCardData: flashCardData.flashcardGroups }, () => {
        this.setState({ combinedFlashCards: this.combineAllCards() });
        this.getDomainData(cptVersion);
      });
    });
  };

  getDomainData(cptVersion) {
    nasm.api.getAllDomains(cptVersion).then((domains) => {
      this.setState({ domains });
      this.generateDomainColors(domains);
      this.setState({ isLoading: false, cptLoading: false });
    });
  }

  getFlashCardsByDomain(domainId) {
    const cpt7Data = [];

    if (this.state.cptVersion === 7) {
      for (const cpt7FlashCardData of this.state.flashCardData[0].cards) {
        if (cpt7FlashCardData.tags[0] === domainId) cpt7Data.push(cpt7FlashCardData);
      }
    }

    return this.state.cptVersion === 6
      ? this.state.flashCardData.find((domain) => domain.name === domainId).cards
      : cpt7Data;
  }

  changeCPTVersion = (cptVersion) => {
    this.setState({ cptVersion, cptLoading: true });
    this.getAllFlashCards(cptVersion);
  };

  hideCTPv7Notification = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ CPT7FlashCards: false });
  };

  combineAllCards() {
    let combinedFlashCards = [];
    this.state.flashCardData.forEach((domain) => {
      combinedFlashCards = combinedFlashCards.concat(domain.cards);
    });
    return combinedFlashCards;
  }

  generateDomainColors(domains) {
    let domainColors = {};
    let colorIndex = 0;
    domains.forEach((domain) => {
      domainColors = {
        ...domainColors,
        [domain.domainId]: cardColors[colorIndex],
      };
      colorIndex += 1;
    });
    this.setState({ domainColors });
  }

  async showCPTv7Notification(trainerId) {
    if (!FEATURE_FLAGS.CPT7_ENABLED) {
      return false;
    }
    const CPT7FlashCardsShown = await AsyncStorage.getItem(
      `${trainerId}_CPTv7_flashcards_notification_shown`,
    );
    if (!CPT7FlashCardsShown) {
      this.setState({ CPT7FlashCards: true });
      AsyncStorage.setItem(
        `${trainerId}_CPTv7_flashcards_notification_shown`,
        'true',
      );
      return true;
    }
    return false;
  }

  renderListFooter = () => (
    <TouchableOpacity
      style={styles.practiceButton}
      onPress={this.onPressPracticeAll}
    >
      <ScaledText style={styles.practiceButtonText}>Practice All</ScaledText>
    </TouchableOpacity>
  );

  renderListHeader = () => (
    <View>
      <TouchableOpacity
        style={styles.xBtnContainer}
        onPress={() => this.props.navigation.navigate('Account')}
      >
        <Image style={styles.xBtn} source={xBtn} />
      </TouchableOpacity>
      {FEATURE_FLAGS.CPT7_ENABLED && (
        <View style={styles.cptVersionSelector}>
          <CPTVersionSelector
            cptVersion={this.state.cptVersion}
            changeCPTVersion={this.changeCPTVersion}
            title="Flash Prep"
          />
        </View>
      )}
      <ScaledText style={styles.titleText}>Flash Cards</ScaledText>
      <ScaledText style={styles.subHeaderText}>
        Test your memory in each domain card stack or shuffle things up with
        Practice All.
      </ScaledText>
    </View>
  );

  renderNotification() {
    return (
      <View style={styles.background}>
        <TouchableOpacity
          style={styles.closeBtn}
          onPress={this.hideCTPv7Notification}
        >
          <Image source={closeBtn} />
        </TouchableOpacity>
        <Image source={CPT7} />
        <Text style={styles.title}>CPT 7 is Now Available!</Text>
        <Text style={styles.description}>
          We have updated the app with the newest study material. Switch between
          CPT 6 or 7 using the dropdown at the top right of either the exam or
          flash card screen.
        </Text>
        <TouchableOpacity
          style={styles.startBtn}
          onPress={this.hideCTPv7Notification}
        >
          <ScaledText style={styles.practiceButtonText}>Okay!</ScaledText>
        </TouchableOpacity>
      </View>
    );
  }

  renderSpinner = () => {
    const loading = this.state.isLoading || this.state.cptLoading;

    if (!loading) return null;
    return (
      <View style={styles.spinner}>
        <StatusBar barStyle="dark-content" />
        <LoadingSpinner
          backgroundColor="rgba(0, 0, 0, 0.5)"
          color={colors.white}
          titleTextStyle={{ color: colors.white }}
          visible={loading}
          title="Loading Flash Cards..."
        />
      </View>
    );
  };

  render() {
    return (
      <View style={styles.container}>
        <Image style={styles.topImg} source={flashCardImg} />
        <FlatList
          ListHeaderComponent={this.renderListHeader}
          ListFooterComponent={this.renderListFooter}
          contentContainerStyle={styles.content}
          columnWrapperStyle={{ justifyContent: 'space-between' }}
          keyExtractor={(item) => item.domainId}
          data={this.state.domains}
          renderItem={({ item, index }) => (
            <CardDomain
              key={index}
              domain={item}
              color={this.state.domainColors[item.domainId]}
              onPressDomain={this.onPressDomain}
            />
          )}
          numColumns={2}
          extraData={this.props}
          showsVerticalScrollIndicator={false}
        />
        {this.state.CPT7FlashCards && this.renderNotification()}
        {this.renderSpinner()}
        <SafeAreaView />
      </View>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    paddingHorizontal: '5%',
  },
  topImg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: scaleHeight(40),
    width: scaleWidth(100),
  },
  xBtnContainer: {
    alignSelf: 'flex-start',
    paddingTop: scaleHeight(8),
    paddingBottom: 7,
  },
  cptVersionSelector: {
    position: 'absolute',
    top: scaleHeight(8),
    right: 0,
  },
  xBtn: {
    tintColor: colors.veryLightBlue,
  },
  content: {
    flexGrow: 1,
  },
  titleText: {
    fontWeight: 'bold',
    fontSize: 30,
    color: colors.white,
    marginBottom: 5,
  },
  subHeaderText: {
    color: colors.white,
    marginTop: 7,
    marginBottom: 7,
  },
  practiceButton: {
    borderRadius: scaleWidth(5.5),
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    padding: '3.8%',
    backgroundColor: colors.duskBlue,
    ...shadow,
  },
  practiceButtonText: {
    textAlign: 'center',
    fontSize: 15,
    fontWeight: 'bold',
    color: colors.white,
  },
  spinner: {
    flex: 1,
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  notificationContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  background: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    backgroundColor: colors.transparentBlack,
    justifyContent: 'center',
    alignItems: 'center',
  },
  startBtn: {
    borderRadius: scaleWidth(5.5),
    bottom: 30,
    marginBottom: 25,
    width: '25%',
    height: '4.8%',
    backgroundColor: colors.azure,
    position: 'absolute',
    justifyContent: 'center',
  },
  title: {
    fontFamily: 'Avenir',
    fontSize: 25,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.white,
    marginHorizontal: 20,
  },
  description: {
    fontFamily: 'Avenir-Roman',
    textAlign: 'center',
    marginTop: 15,
    fontSize: 15,
    color: colors.white,
    marginHorizontal: 20,
  },
  closeBtn: {
    position: 'absolute',
    top: 40,
    left: 16,
  },
};

const mapStateToProps = ({ currentUser }) => ({ currentUser });
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(FlashCardCatalog);
