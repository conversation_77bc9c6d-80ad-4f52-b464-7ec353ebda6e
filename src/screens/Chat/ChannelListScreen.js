import React, { PureComponent } from 'react';
import {
  View,
  SafeAreaView,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  StyleSheet,
} from 'react-native';
import {
  <PERSON><PERSON>,
  ChannelList,
  ChannelPreviewMessenger,
} from 'stream-chat-react-native';
import { connect } from 'react-redux';
// eslint-disable-next-line import/no-extraneous-dependencies
import { debounce } from 'lodash';
import moment from 'moment';
import { chatClient, FEATURE_FLAGS, ROLES } from '../../constants';
import { FloatingButton } from '../../components';
import Swipeable from '../../components/Swipeable';
import hideIcon from '../../resources/btnDelete.png';
import nasm from '../../dataManager/apiConfig';
import newChatIcon from '../../resources/newChatIcon.png';
import HeaderSearchBar from '../../components/HeaderSearchBar';
import { colors } from '../../styles';
import UpgradePlan from './UpgradePlan';
import { isConnectedMode } from '../../util/PermissionUtils';
import { curvedScale } from '../../util/responsive';

class ChannelListScreen extends PureComponent {
  static formatLatestMessageDate(date) {
    const now = new Date();
    const messageDate = moment(date);
    const yesterday = moment().subtract(1, 'day');
    const minutes = messageDate.diff(now, 'minutes');
    if (minutes === 0) {
      return 'Just now';
    }
    if (messageDate.isSame(now, 'day')) {
      return messageDate.format('h:mm a');
    }
    if (messageDate.isSame(yesterday, 'day')) {
      return 'Yesterday';
    }
    if (messageDate.isSame(now, 'year')) {
      return messageDate.format('MMM D');
    }
    return messageDate.format('DD/MM/YY');
  }

  static renderEmptyState() {
    return (
      <View
        style={{
          backgroundColor: 'white',
          width: '100%',
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <View style={{ width: '65%', alignItems: 'center' }}>
          <Image style={{ marginBottom: 18 }} source={newChatIcon} />
          <Text
            style={{
              color: 'black',
              fontFamily: 'Avenir-Roman',
              fontSize: 20,
            }}
          >
            Tap the + below to start a conversation with a client.
          </Text>
        </View>
      </View>
    );
  }

  constructor(props) {
    super(props);
    this.state = {
      userSet: false,
      searchText: '',
      // the channel query won't accept empty values so default
      // to the current user, this will show all channels.
      filteredUsers: [chatClient.userID],
      notificationId: null,
    };
    this.swipeableCells = new Map();
    this.searchChatUsers = debounce(this.handleChatSearch.bind(this), 500);
  }

  componentDidMount() {
    this.props.navigation.setParams({ tabBarVisible: true });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.handlePageSetup();
      for (const swipeable of this.swipeableCells.values()) {
        if (swipeable && swipeable.recenter) swipeable.recenter();
      }
    });

    chatClient.on(async (event) => {
      if (event.type === 'message.new') {
        const channel = chatClient.activeChannels[event.cid];
        const { name, image } = event.user;

        if (
          this.props.selectedClient
          || event.user.id === this.props.currentUser.id
        ) return;
        if (channel.data.name === name && channel.data.image === image) return;

        channel.data.name = name;
        channel.data.image = image;
        chatClient.dispatchEvent({
          type: 'channel.updated',
          cid: event.cid,
        });
      }
    });
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.route?.params?.notificationId
      !== this.props.route?.params?.notificationId
    ) {
      this.handlePageSetup();
    }
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  handlePageSetup = () => {
    if (this.state.notificationId !== this.props.route.params?.notificationId) {
      this.setState(
        {
          notificationId: this.props.route.params?.notificationId,
        },
        () => {
          this.pageSetup();
        },
      );
    } else if (this.props.route?.params?.refresh) {
      this.pageSetup();
    }
  };

  async handleChatSearch(searchText) {
    if (!searchText) {
      // reset to current user
      this.setState({ filteredUsers: [chatClient.userID] });
      return;
    }
    if (chatClient != null) {
      // Find all the users that match, this returns even users
      // that are not clients of the trainer but they are filtered
      // in the channels list
      const { users } = await chatClient.queryUsers({
        name: { $autocomplete: searchText },
      });
      let filteredUsers = [...users].map((u) => u.id);
      const result = await chatClient.queryChannels({
        name: { $autocomplete: searchText },
      });
      if (result.length) {
        result.forEach((res) => {
          filteredUsers.push(res?.data?.id);
        });
      }
      if (!filteredUsers.length) {
        const res = await chatClient.queryUsers({});
        filteredUsers = res.users.map((u) => u.id);
      }
      this.setState({ filteredUsers });
    }
  }

  onPressAdd = () => {
    this.props.navigation.navigate('NewConversation');
  };

  getButtonsForChannel = (channel) => [
    <TouchableOpacity
      style={styles.hideButton}
      key="hide"
      onPress={() => {
        // FIXME: Workaround for https://github.com/GetStream/stream-chat-react-native/issues/196
        channel.stopWatching();
        channel.hide();
      }}
    >
      <View style={styles.swipeButtonContainer}>
        <Image style={styles.hideIconStyle} source={hideIcon} />
        <Text style={styles.swipeButtonText}>Hide</Text>
      </View>
    </TouchableOpacity>,
  ];

  channelPreview = (props) => (
    <Swipeable
      ref={(ref) => this.swipeableCells.set(props.channel.id, ref)}
      rightButtonWidth={90}
      rightButtons={this.getButtonsForChannel(props.channel)}
    >
      <ChannelPreviewMessenger
        {...props}
        formatLatestMessageDate={this.formatLatestMessageDate}
      />
    </Swipeable>
  );

  pageSetup = async () => {
    let channel;
    if (this.props.route.params?.clientTabs) {
      try {
        if (this.props.currentUser.role === ROLES.TRAINER) {
          const clientUserResponse = await chatClient.queryUsers({
            id: this.props.selectedClient?.id,
          });
          if (clientUserResponse.users?.length === 0) {
            // client not in stream yet.
            // lets make them now
            await nasm.api.createChatUser(this.props.selectedClient?.id);
          }
          channel = chatClient.channel('messaging', {
            members: [this.props.currentUser.id, this.props.selectedClient?.id],
          });
        } else {
          const trainerUserResponse = await chatClient.queryUsers({
            id: this.props.currentUser.client_user.trainer.user_id,
          });
          if (trainerUserResponse.users?.length === 0) {
            // trainer not in stream yet.
            // lets make them now
            await nasm.api.createChatUser(
              this.props.currentUser.client_user.trainer.user_id,
            );
          }
          channel = chatClient.channel('messaging', {
            members: [
              this.props.currentUser.client_user.trainer.user_id,
              this.props.currentUser.id,
            ],
          });
        }
      } catch (error) {
        Alert.alert('error', error.message);
      }
    } else {
      this.setState({ userSet: true });
      const channelId = this.props.route.params?.channelId;
      this.props.navigation.setParams({ channelId: null });
      if (channelId) {
        const [channelType, channelMembers] = channelId.split(':');
        // deep linking, go to conversation
        channel = chatClient.channel(channelType, channelMembers);
      }
    }
    if (channel) {
      await channel.watch();
      this.props.navigation.navigate('Channel', {
        channel,
      });
    }
  };

  updateChannelsList = (searchText) => {
    this.setState({ searchText });
    this.searchChatUsers(searchText);
  };

  customChannelFilters = (channels) => {
    if (this.props.currentUser?.role === ROLES.CLIENT) {
      return channels;
    }
    if (this.props.trainerActiveProfile?.ClubId) {
      return channels.filter((ch) => {
        if (ch.data?.club_id) {
          if (
            FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED
            && ch.data?.location_id
          ) {
            return (
              ch.data?.club_id === this.props.trainerActiveProfile?.ClubId
              && ch.data?.location_id
                === this.props.trainerActiveProfile?.Locations?.Id
            );
          }
          return ch.data?.club_id === this.props.trainerActiveProfile?.ClubId;
        }
        return null;
      });
    }
    return channels.filter((ch) => !ch.data?.club_id);
  };

  render() {
    let trainerUser;
    const isTrainer = this.props.currentUser.role === ROLES.TRAINER;
    if (isTrainer) {
      trainerUser = this.props.currentUser;
    } else if (this.props.currentUser.client_user.trainer) {
      trainerUser = this.props.currentUser.client_user.trainer;
    }

    if (
      !this.props.trainerActiveProfile?.ClubId
      && isConnectedMode(trainerUser)
    ) {
      return <UpgradePlan navigation={this.props.navigation} />;
    }

    if (!this.state.userSet || !chatClient.user) {
      return null;
    }
    const placeholder = isTrainer
      ? 'Search for Group or Client by name'
      : 'Search for Group or Trainer by name';
    return (
      <SafeAreaView>
        <Chat client={chatClient} style={styles.theme}>
          <View
            style={{
              height: '100%',
              padding: 10,
              backgroundColor: 'white',
            }}
          >
            <HeaderSearchBar
              searchText={this.state.searchText}
              onChangeText={this.updateChannelsList}
              clearable
              paddingTop={1}
              light
              shadow={false}
              placeholder={placeholder}
            />
            <ChannelList
              Preview={this.channelPreview}
              sort={{ last_message_at: -1 }}
              options={{}}
              filters={{
                $or: [
                  { id: { $in: this.state.filteredUsers } },
                  { members: { $in: this.state.filteredUsers } },
                ],
              }}
              channelRenderFilterFn={this.customChannelFilters}
              onSelect={(channel) => {
                this.props.navigation.navigate('Channel', {
                  channel,
                  title: channel.data?.name ?? '',
                  isGroup: channel.data?.isGroup,
                });
              }}
              EmptyStateIndicator={this.renderEmptyState}
              additionalFlatListProps={{
                style: { flex: 1 },
              }}
            />
            {isTrainer ? <FloatingButton onPress={this.onPressAdd} /> : null}
          </View>
        </Chat>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  hideButton: {
    backgroundColor: colors.dustyRed,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: 90,
  },
  swipeButtonContainer: {
    width: 90,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  hideIconStyle: {
    width: 18,
    height: 18,
  },
  swipeButtonText: {
    fontFamily: 'Avenir-Bold',
    fontSize: curvedScale(12),
    color: colors.white,
    marginTop: 3,
  },
  theme: {
    channelPreview: {
      container: `
        align-items: center;
      `,
      title: `
        color: ${colors.subGrey};
        font-family: Avenir-Medium;
        font-size: 17px;
      `,
      message: {
        color: colors.subGrey,
        'font-size': 13,
        'font-family': 'Avenir-Roman',
        unreadColor: 'black',
      },
      date: {
        color: colors.subGrey,
      },
    },
    avatar: {
      image: `
        width: 56px;
        height: 56px;
        border-radius: 28;
      `,
      fallback: `
        width: 56px;
        height: 56px;
        border-radius: 28;
    `,
    },
  },
});

const mapStateToProps = ({
  currentUser,
  selectedClient,
  trainerActiveProfile,
}) => ({
  currentUser,
  selectedClient,
  trainerActiveProfile,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(ChannelListScreen);
