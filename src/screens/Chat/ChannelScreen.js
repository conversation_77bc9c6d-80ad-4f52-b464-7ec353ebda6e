/* eslint-disable react/sort-comp */
import React, { PureComponent } from 'react';
import {
  View,
  SafeAreaView,
  TouchableOpacity,
  Text,
  Platform,
  Image,
  NativeModules,
} from 'react-native';
import {
  Chat,
  Channel,
  MessageList,
  MessageInput,
  MessageSimple,
  FileIcon,
  MessageContent,
  useMessagesContext,
  Avatar,
  AttachmentPickerContext,
} from 'stream-chat-react-native';
import { connect } from 'react-redux';
import { CardStyleInterpolators } from '@react-navigation/stack';
import analytics from '@react-native-firebase/analytics';
import { chatClient, ROLES } from '../../constants';
import { colors } from '../../styles';
import MessageWithMenu from './MessageWithMenu';
import videoChatIcon from '../../resources/videoAttachmentIcon.png';
import { CustomMessageStatus } from './CustomMessageStatus';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const { AppKeyboardHandlerMethods } = NativeModules;

function borderRadiusForPosition(alignment) {
  const [borderBottomLeftRadius, borderBottomRightRadius] = alignment === 'left' ? [2, 16] : [16, 2];
  return {
    borderBottomLeftRadius,
    borderBottomRightRadius,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  };
}

const FailedMessage = (props) => {
  const messagesContext = useMessagesContext();
  return (
    <TouchableOpacity
      onPress={() => messagesContext.retrySendMessage(props.message)}
      style={[
        borderRadiusForPosition('right'),
        {
          flex: 1,
          flexDirection: 'column',
          backgroundColor: colors.dustyRed,
          maxWidth: 250,
          padding: 5,
          paddingLeft: 8,
          marginTop: 2,
          paddingRight: 8,
          alignSelf: 'flex-end',
        },
      ]}
    >
      <Text
        style={[
          styles.failedMessageText,
          { fontSize: 14, marginBottom: 3, alignSelf: 'center' },
        ]}
      >
        Message failed - Tap to try again
      </Text>
      <Text style={styles.failedMessageText}>{props.message.text}</Text>
    </TouchableOpacity>
  );
};

class ChannelScreen extends PureComponent {
  static contextType = AttachmentPickerContext;

  static navigationOptions = ({ navigation, route }) => {
    const tabBarVisible = route.params?.clientTabs ?? false;
    const title = route.params?.title ?? '';
    return {
      title,
      headerShown: !tabBarVisible,
      cardStyleInterpolator: tabBarVisible
        ? CardStyleInterpolators.forNoAnimation
        : undefined,
      headerLeft: () => (
        <HeaderLeftButton
          onPress={() => navigation.popToTop()}
          titleStyle={styles.headerButtonText}
        />
      ),
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      trainer: null,
      isGroup: props.route?.params?.isGroup,
    };
  }

  componentDidMount() {
    const channel = this.props.route.params?.channel;
    const chatTitle = this.props.route.params?.title;
    channel
      .queryMembers({ id: { $ne: chatClient.userID } })
      .then(({ members = [] }) => {
        if (!members.length) return;
        const title = chatTitle || members[0].user.name;
        this.setState({
          trainer: chatTitle.length ? { name: chatTitle } : members[0].user,
        });
        this.props.navigation.setParams({ title });
      });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustResize();
      }
    });
    this.unsubscribeBlur = this.props.navigation.addListener('blur', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustPan();
      }
      this.context.closePicker();
    });
    this.logChatEvent();
  }

  logChatEvent = () => {
    let eventName = 'screen_view';
    const eventProps = {
      screen_name: 'view_chat',
      user_id: this.props.currentUser?.id,
      full_name: this.props.currentUser?.full_name,
    };
    if (this.props.trainerActiveProfile?.ClubId) {
      eventName = `club_connect_${eventName}`;
      eventProps.screen_name = `club_connect_${eventProps.screen_name}`;
      eventProps.club_name = this.props.trainerActiveProfile?.ClubName;
    }
    analytics().logEvent(eventName, eventProps);
  };

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
    if (this.unsubscribeBlur) {
      this.unsubscribeBlur();
    }
  }

  customMessage = (props) => {
    const isMyMessage = props.isMyMessage(props.message);
    const alignment = isMyMessage ? 'right' : 'left';
    const borders = borderRadiusForPosition(alignment);
    if (props.message.deleted_at) {
      const style = { ...styles.deletedMessage };
      style.message.content.deletedContainer.css = {
        ...style.message.content.deletedContainer.css,
        ...borders,
      };
      return <MessageSimple {...props} style={style} />;
    }
    const style = isMyMessage ? styles.sendMessage : styles.receivedMessage;
    if (props.message.status === 'failed') {
      return (
        <MessageSimple
          MessageContent={FailedMessage}
          {...props}
          style={style}
        />
      );
    }
    return (
      <MessageWithMenu
        isMyMessage={isMyMessage}
        alignment={isMyMessage ? 'flex-end' : 'flex-start'}
      >
        <MessageSimple
          {...props}
          style={style}
          MessageAvatar={() => (
            <View
              style={isMyMessage ? styles.AvatarMargin : styles.AvatarNoMargin}
            >
              {typeof props.message.readBy === 'number' ? (
                <Text style={styles.readByCount}>{props.message.readBy}</Text>
              ) : null}
              {isMyMessage ? (
                <View />
              ) : (
                <Avatar
                  image={props.message.user?.image}
                  name={props.message.user?.name}
                  size={16}
                />
              )}
            </View>
          )}
          MessageContent={(messageProps) => (
            <MessageContent
              {...messageProps}
              hideReactionCount
              hideReactionOwners
              handleReaction={(type, e) => {
                // Make the reaction picker hide after selecting a single reaction.
                messageProps.dismissReactionPicker();
                messageProps.handleReaction(type, e);
              }}
            />
          )}
          AttachmentFileIcon={this.attachmentIconForVideo}
          MessageStatus={() => <CustomMessageStatus {...props} />}
        />
      </MessageWithMenu>
    );
  };

  attachmentIconForVideo = ({ mimeType }) => {
    if (mimeType.includes('video')) {
      return <Image source={videoChatIcon} />;
    }
    return <FileIcon />;
  };

  renderEmptyState = () => {
    let role = this.props.currentUser.role === ROLES.CLIENT ? 'trainer' : 'client';
    if (this.state.isGroup && this.props.currentUser.role === ROLES.TRAINER) {
      role = 'clients';
    }
    const { trainer } = this.state;
    return (
      <View style={styles.emptyChat}>
        {trainer && (
          <>
            <Image
              style={styles.chatTrainerImage}
              source={{ uri: trainer.image }}
            />
            <Text style={styles.chatTrainerName}>{trainer.name}</Text>
            <Text style={styles.chatTrainerText}>
              {`Start a conversation with your ${role}.`}
            </Text>
          </>
        )}
      </View>
    );
  };

  render() {
    if (!chatClient.user) {
      return null;
    }

    const channel = this.props.route.params?.channel;

    return (
      <SafeAreaView>
        <Chat client={chatClient} style={styles.theme}>
          <Channel
            client={chatClient}
            channel={channel}
            disableKeyboardCompatibleView={Platform.OS === 'android'}
            EmptyStateIndicator={this.renderEmptyState}
            myMessageTheme={styles.myMessageTheme}
          >
            <View
              style={{
                display: 'flex',
                height: '100%',
                backgroundColor: 'white',
              }}
            >
              <MessageList />
              <View>
                <View
                  style={{
                    height: 0.9,
                    width: '100%',
                    backgroundColor: colors.subGreyLight,
                  }}
                />
                <MessageInput
                  AttachmentFileIcon={this.attachmentIconForVideo}
                />
              </View>
            </View>
          </Channel>
        </Chat>
      </SafeAreaView>
    );
  }
}

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.white,
  },
  sendMessage: {
    message: {
      file: {
        container: 'background-color: rgb(37, 146, 236)',
        title: 'color: white; font-family: Avenir-Medium; font-size: 14;',
        size: 'color: white; font-family: Avenir-Medium; font-size: 12;',
      },
      content: {
        // blue
        textContainer: 'backgroundColor: rgb(37, 146, 236);',
        markdown: {
          text: { color: 'white', fontFamily: 'Avenir-Medium', fontSize: 14 },
        },
      },
    },
  },
  receivedMessage: {
    message: {
      file: {
        container: 'background-color: rgb(248, 248, 249)',
        title: 'color: black; font-family: Avenir-Medium; font-size: 14;',
        size: 'color: black; font-family: Avenir-Medium; font-size: 12;',
      },
      content: {
        textContainer: 'backgroundColor: rgb(248, 248, 249);',
        markdown: {
          text: { color: 'black', fontFamily: 'Avenir-Medium', fontSize: 14 },
        },
      },
    },
  },
  deletedMessage: {
    message: {
      content: {
        deletedContainer: {
          css: {
            borderColor: 'rgb(214, 214, 214)',
            borderWidth: 0.7,
          },
        },
        deletedText: {
          css: {
            color: 'rgb(124, 128, 132)',
            fontFamily: 'Avenir-Medium',
            fontSize: 14,
          },
        },
      },
    },
  },
  failedMessageText: {
    color: 'white',
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
  },
  theme: {
    messageSimple: {
      content: {
        containerInner: {
          backgroundColor: 'rgb(248, 248, 249)',
          borderWidth: 0,
        },
        deletedContainerInner: {
          backgroundColor: 'transparent',
          borderColor: 'rgb(214, 214, 214)',
          borderWidth: 1,
        },
      },
    },
  },
  myMessageTheme: {
    messageSimple: {
      content: {
        containerInner: {
          backgroundColor: colors.azure,
        },
        markdown: {
          text: {
            color: colors.white,
          },
        },
      },
    },
  },
  emptyChat: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  chatTrainerImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  chatTrainerName: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    marginTop: 10,
    textColor: colors.black,
  },
  chatTrainerText: {
    color: colors.black,
    fontSize: 22,
    textAlign: 'center',
    fontFamily: 'Avenir-Roman',
    maxWidth: 272,
    marginTop: 18,
  },
  AvatarMargin: {
    marginLeft: 10,
  },
  AvatarNoMargin: {
    marginLeft: 0,
  },
};

const mapStateToProps = ({
  currentUser,
  selectedClient,
  trainerActiveProfile,
}) => ({
  currentUser,
  selectedClient,
  trainerActiveProfile,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(ChannelScreen);
