import React, { useState } from 'react';
import { Text, View, Keyboard } from 'react-native';
import Menu, { MenuOptions, MenuOption, MenuTrigger } from 'react-native-menu';
import _cloneDeep from 'lodash.clonedeep';
import Clipboard from '@react-native-clipboard/clipboard';
import { colors } from '../../styles';
import { chatClient } from '../../constants';

const optionsForMenu = (props) => {
  const { message } = props.children.props;
  const hasAttachment = Boolean(
    message.attachments && message.attachments.length,
  );
  const Copy = () => (
    <MenuOption value={1} style={{ alignItems: 'center' }}>
      <Text
        style={{ color: 'white', fontFamily: 'Avenir-Heavy', fontSize: 14 }}
      >
        Copy
      </Text>
    </MenuOption>
  );
  const Delete = () => (
    <MenuOption value={2} style={{ alignItems: 'center' }}>
      <Text
        style={{ color: 'white', fontFamily: 'Avenir-Heavy', fontSize: 14 }}
      >
        Delete
      </Text>
    </MenuOption>
  );
  // Nothing for the user to do if the message isn't theirs and has attachments
  if (!props.isMyMessage && hasAttachment) {
    return [];
  }
  if (!props.isMyMessage) {
    return [Copy()];
  }
  if (hasAttachment) {
    return [Delete()];
  }
  return [
    Copy(),
    <View
      key={2}
      style={{ height: 1, width: '100%', backgroundColor: '#dadde1' }}
    />,
    Delete(),
  ];
};

const MessageWithMenu = (props) => {
  const style = _cloneDeep(props.children.props.style);
  const menuRef = React.createRef();
  const [isOpen, setIsOpen] = useState(false);
  const color = isOpen
    ? `backgroundColor: ${colors.cloudyBlue}`
    : style.message.content.textContainer;
  style.message.content.textContainer = color;
  return (
    <Menu
      style={{ alignSelf: props.alignment }}
      ref={menuRef}
      onSelect={(value) => {
        switch (value) {
          case 1:
            Clipboard.setString(props.children.props.message.text);
            break;
          case 2: {
            const { message } = props.children.props;
            chatClient.deleteMessage(message.id);
            break;
          }
          default:
            break;
        }
      }}
      onOpen={() => setIsOpen(true)}
      onClose={() => setIsOpen(false)}
    >
      <MenuTrigger style={{ width: 200 }} onPress={() => {}}>
        {React.cloneElement(props.children, {
          style: {
            ...props.children.props.style,
            ...style,
          },
          onLongPress: async () => {
            if (!menuRef.current) return;
            menuRef.current.context.menuController.toggle(
              menuRef.current.getName(),
            );
          },
          onPress: async () => {
            await Keyboard.dismiss();
            props.children.props.openReactionPicker();
          },
        })}
      </MenuTrigger>
      <MenuOptions
        optionsContainerStyle={[
          { backgroundColor: colors.cloudyBlue, borderRadius: 17 },
        ]}
      >
        {/* Needs to be binded to the context of the Menu */}
        {optionsForMenu.bind(this)(props)}
      </MenuOptions>
    </Menu>
  );
};
export default MessageWithMenu;
