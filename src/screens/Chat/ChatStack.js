import React from 'react';
import { View } from 'react-native';
import { connect } from 'react-redux';

import { createStackNavigator } from '@react-navigation/stack';

import { header } from '../../styles';
import { HeaderLeftButton } from '../../components';
import { ROLES } from '../../constants';

import ChannelListScreen from './ChannelListScreen';
import ChannelScreen from './ChannelScreen';
import NewConversation from './NewConversation';
import UpgradePlan from './UpgradePlan';
import { isConnectedMode } from '../../util/PermissionUtils';

const Stack = createStackNavigator();

const ChatStack = (props) => {
  const clientTabs = props.clientTabs ?? false;
  const refresh = props.refresh ?? false;

  let trainerUser;
  if (props.currentUser.role === ROLES.TRAINER) {
    trainerUser = props.currentUser;
  } else if (props.currentUser.client_user.trainer) {
    trainerUser = props.currentUser.client_user.trainer;
  }

  return (
    <>
      {props.trainerActiveProfile?.ClubId || !isConnectedMode(trainerUser) ? (
        <Stack.Navigator
          screenOptions={({ navigation }) => ({
            title: '',
            gestureEnabled: false,
            headerBackTitle: '',
            headerLeft: () => (
              <HeaderLeftButton onPress={() => navigation.goBack()} />
            ),
            headerRight: () => <View />, // empty view helps to center headers on Android
            ...header.default,
          })}
        >
          <Stack.Screen
            name="ChannelList"
            component={ChannelListScreen}
            options={ChannelListScreen.navigationOptions}
            initialParams={{ clientTabs, refresh }}
          />
          <Stack.Screen
            name="Channel"
            component={ChannelScreen}
            options={ChannelScreen.navigationOptions}
            initialParams={{ clientTabs }}
          />
          <Stack.Screen
            name="NewConversation"
            component={NewConversation}
            options={NewConversation.navigationOptions}
            initialParams={{ clientTabs }}
          />
        </Stack.Navigator>
      ) : (
        <UpgradePlan navigation={props.navigation} />
      )}
    </>
  );
};

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  trainerActiveProfile: state.trainerActiveProfile,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(ChatStack);
