import React from 'react';
import { Image, Text, View } from 'react-native';
import { useChatContext, styled } from 'stream-chat-react-native';
import { colors } from '../../styles';

const iconDeliveredUnseen = require('../../resources/delivered_unseen.png');
const loadingGif = require('../../resources/loading.gif');

const CheckMark = styled.Image`
  height: 6px;
  width: 8px;
  ${({ theme }) => theme.message.status.checkMark.css};
`;
const DeliveredCircle = styled.View`
  align-items: center;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 16px;
  height: 16px;
  justify-content: center;
  padding: 6px;
  width: 16px;
  ${({ theme }) => theme.message.status.deliveredCircle.css};
`;
const DeliveredContainer = styled.View`
  align-items: center;
  height: 20px;
  ${({ theme }) => theme.message.status.deliveredContainer.css};
`;
const SendingContainer = styled.View`
  align-items: center;
  ${({ theme }) => theme.message.status.sendingContainer.css};
`;
const SendingImage = styled.Image`
  height: 10px;
  width: 10px;
  ${({ theme }) => theme.message.status.sendingImage.css};
`;
const Spacer = styled.View`
  height: 10px;
`;
const StatusContainer = styled.View`
  flex-direction: row;
  justify-content: center;
  width: 20px;
`;
const customStyles = {
  roundedAvatar: {
    height: 20,
    width: 20,
    borderRadius: 10,
  },
  nameInitialsContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    backgroundColor: colors.nasmBlue,
  },
  nameInitialsText: {
    fontSize: 10,
    textAlign: 'center',
    color: colors.white,
    fontWeight: 'bold',
  },
};

// eslint-disable-next-line import/prefer-default-export
export const CustomMessageStatus = (props) => {
  let _user;
  const {
    lastReceivedId, message, readBy = [], threadList,
  } = props;
  const { client } = useChatContext();
  const justReadByMe = readBy.length === 1
    && readBy[0].id
      === ((_user = client.user) === null || _user === void 0 ? void 0 : _user.id);
  if (message.status === 'sending') {
    return React.createElement(
      StatusContainer,
      null,
      React.createElement(
        SendingContainer,
        { testID: 'sending-container' },
        React.createElement(SendingImage, { source: loadingGif }),
      ),
    );
  }
  if (readBy.length !== 0 && !threadList && !justReadByMe) {
    const lastReadUser = readBy.filter((item) => {
      let _user;
      return (
        item.id
        !== ((_user = client.user) === null || _user === void 0 ? void 0 : _user.id)
      );
    })[0];
    const nameInitials = lastReadUser.name.split(' ');
    let initials = nameInitials[0].substring(0, 1).toUpperCase();
    if (nameInitials.length > 1) {
      initials += nameInitials[nameInitials.length - 1].substring(0, 1).toUpperCase();
    }
    if (lastReadUser.image !== '') {
      return <Image style={customStyles.roundedAvatar} source={{ uri: lastReadUser.image }} />;
    }
    return (
      <View style={customStyles.nameInitialsContainer}>
        <Text style={customStyles.nameInitialsText}>{initials}</Text>
      </View>
    );
  }
  if (
    message.status === 'received'
    && message.type !== 'ephemeral'
    && message.id === lastReceivedId
    && !threadList
  ) {
    return React.createElement(
      StatusContainer,
      null,
      React.createElement(
        DeliveredContainer,
        { testID: 'delivered-container' },
        React.createElement(
          DeliveredCircle,
          null,
          React.createElement(CheckMark, { source: iconDeliveredUnseen }),
        ),
      ),
    );
  }
  return React.createElement(
    StatusContainer,
    null,
    React.createElement(Spacer, { testID: 'spacer' }),
  );
};
