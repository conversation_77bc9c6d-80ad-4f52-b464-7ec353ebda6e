import React from 'react';
import {
  StyleSheet, Image, Text, View, TouchableOpacity,
} from 'react-native';
import { colors } from '../../styles';

const chat = require('../../resources/chat.png');

export default function UpgradePlan({ navigation }) {
  return (
    <View style={styles.container}>
      <Image style={styles.icon} source={chat} />
      <Text style={styles.heading}>Upgrade Plan</Text>
      <Text style={styles.description}>
        Guide your client’s training with in-app messaging and gain access to
        tools to help boost progress.
      </Text>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          activeOpacity={0.5}
          onPress={() => {
            navigation.navigate('ConnectedUpgradeModal');
          }}
        >
          <Text style={styles.buttonText}>Learn More</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginBottom: 20,
  },
  heading: {
    fontFamily: 'Avenir-Roman',
    fontSize: 22,
    color: colors.black,
    marginBottom: 10,
  },
  description: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    lineHeight: 22,
    color: colors.fillDarkGrey,
    maxWidth: 273,
    textAlign: 'center',
    marginBottom: 30,
  },
  button: {
    height: 55,
    paddingHorizontal: 30,
    backgroundColor: colors.macaroniAndCheese,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 27.5,
  },
  buttonText: {
    fontFamily: 'Avenir-Heavy',
    color: colors.white,
    fontSize: 17,
  },
});
