import React, { Component } from 'react';
import {
  View,
  Text,
  SectionList,
  TouchableOpacity,
  Image,
  StatusBar,
  Alert,
  RefreshControl,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { connect } from 'react-redux';
import analytics from '@react-native-firebase/analytics';
import nasm from '../../dataManager/apiConfig';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  HeaderSearchBar,
  ExerciseCell,
  BottomBannerActionView,
} from '../../components';
import { curvedScale } from '../../util/responsive';

// Redux
import {
  addExercises,
  removeExercise,
} from '../../reducers/selectedWorkoutReducer';
import { assignExercises } from '../../reducers/selectedProgramReducer';

// Constants
import { EXERCISE_FILTER_CATEGORIES, debounce } from '../../constants';

// Styles
import { colors } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import HeaderRightButton from '../../components/HeaderRightButton';
import ROUTINE_TYPES from '../../types/RoutineTypes';
import { programContexts } from '../../reducers/programContextReducer';

const filterIcon = require('../../resources/allGray.png');
const emptyStateImage = require('../../resources/magnifyinglassLarge.png');
const plusSign = require('../../resources/btnAddGray.png');

class AddExercises extends Component {
  constructor(props) {
    super(props);
    this.state = {
      exercises: [],
      isLoading: true,
      searchText: '',
      refreshing: false,
      searching: false,
      nextPage: 1,
      resultSize: 50,
      selectedFilters: {},
      filterOptions: {},
      selectedExercises: this.props.route?.params?.exercises?.length
        ? [...this.props.route?.params?.exercises]
        : [],
      addSelected: false,
      loadingExercises: false,
      isComingFromCircuitBuilder:
        this.props.route?.params?.comingFrom === 'CircuitBuilder',
      isComingFromSuperSetBuilder:
        this.props.route?.params?.comingFrom === 'SuperSetBuilder',
      super_sets: [],
      hasMoreSuperSet: false,
      page: 1,
    };
  }

  componentDidMount() {
    this.props.navigation.setParams({ clearAll: this.clearSelections });
    this.props.navigation.setOptions({
      title:
        this.state.isComingFromSuperSetBuilder
        || this.state.isComingFromCircuitBuilder
          ? 'Choose Exercise'
          : 'Add Exercises',
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'add_exercises' });
    });
    if (
      this.state.isComingFromSuperSetBuilder
      || this.state.isComingFromCircuitBuilder
    ) {
      this.getExercises(true);
    } else {
      this.getCompoundRoutines();
    }
    this.getFilters();
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onLoadNextPage = () => {
    if (!this.state.isLoading) {
      this.setState({ isLoading: true }, () => {
        if (this.state.hasMoreSuperSet) {
          this.getCompoundRoutines();
        } else {
          this.getExercises();
        }
      });
    }
  };

  onPressExercise = (exercise) => {
    const { selectedExercises } = this.state;
    const exerciseIndex = this.exerciseIsSelectedIndex(exercise);
    if (exerciseIndex === -1) {
      // workout not selected
      if (this.state.isComingFromSuperSetBuilder) {
        selectedExercises.length = 0;
      }
      selectedExercises.push(exercise);
    } else {
      selectedExercises.splice(exerciseIndex, 1);
    }
    this.setState({ selectedExercises });
  };

  onPressFilter = () => {
    this.props.navigation.navigate('FilterModal', {
      title: 'FILTER EXERCISES',
      filters: this.state.filterOptions,
      selectedFilters: this.state.selectedFilters,
      updateFilters: this.updateFilters,
    });
  };

  onRefresh = () => {
    this.setState(
      {
        refreshing: true,
        nextPage: 1,
        page: 1,
        exercises: [],
      },
      () => {
        this.getCompoundRoutines();
      },
    );
  };

  getExercises(refreshing) {
    let nextPage = refreshing ? 1 : this.state.nextPage;
    if (nextPage === -1 || this.state.loadingExercises) {
      this.setState({ isLoading: false, refreshing: false });
      return;
    }
    let bodyRegionIds = [];
    let fitnessComponentIds = [];
    if (this.state.selectedFilters[EXERCISE_FILTER_CATEGORIES.BODY_REGION]) {
      bodyRegionIds = this.state.selectedFilters[EXERCISE_FILTER_CATEGORIES.BODY_REGION];
    }
    if (
      this.state.selectedFilters[EXERCISE_FILTER_CATEGORIES.FITNESS_COMPONENTS]
    ) {
      fitnessComponentIds = this.state.selectedFilters[
        EXERCISE_FILTER_CATEGORIES.FITNESS_COMPONENTS
      ];
    }
    const newState = {
      nextPage,
      loadingExercises: true,
    };
    this.setState(newState, () => {
      nasm.api
        .getNasmExercises(
          this.state.nextPage,
          this.state.resultSize,
          this.state.searchText,
          bodyRegionIds,
          fitnessComponentIds,
        )
        .then((exerciseResult) => {
          const { exercises } = exerciseResult;
          let updatedExr = [...exercises];
          if (this.state.nextPage > 1) {
            updatedExr = [...this.state.exercises, ...exercises];
          }
          nextPage = -1;
          if (exerciseResult.nextPage.length > 0) {
            nextPage = this.state.nextPage + 1;
          }
          this.setState({
            exercises: updatedExr,
            nextPage,
          });
        })
        .catch((error) => {
          Alert.alert('Error', error.message);
        })
        .finally(() => {
          this.setState({
            isLoading: false,
            refreshing: false,
            searching: false,
            loadingExercises: false,
          });
        });
    });
  }

  getFilters = async () => {
    const fitnessComponents = await nasm.api.getFitnessComponents();
    const muscleGroups = await nasm.api.getMuscleGroups();
    const sourcesOptions = await nasm.api.getExerciseSourceFilters();

    const sourceFilters = sourcesOptions.map((source) => ({
      name: source.display_value,
      id: source.filter_value,
    }));

    const filterOptions = {};
    filterOptions[EXERCISE_FILTER_CATEGORIES.SOURCE] = {
      label: 'SOURCE',
      filters: sourceFilters,
    };
    filterOptions[EXERCISE_FILTER_CATEGORIES.BODY_REGION] = {
      label: 'BODY REGION',
      filters: muscleGroups,
    };
    filterOptions[EXERCISE_FILTER_CATEGORIES.FITNESS_COMPONENTS] = {
      label: 'FITNESS COMPONENTS',
      filters: fitnessComponents,
    };

    const sections = Object.keys(filterOptions);
    const { selectedFilters } = this.state;
    for (let i = 0; i < sections.length; i += 1) {
      selectedFilters[sections[i]] = [];
    }

    this.setState({ filterOptions, selectedFilters });
  };

  getCompoundRoutines = async (shouldClearPreviousSectionExercises) => {
    if (
      this.state.isComingFromSuperSetBuilder
      || this.state.isComingFromCircuitBuilder
    ) {
      return;
    }
    if (shouldClearPreviousSectionExercises) {
      // Clear state data if we're fetching a new list
      this.setState({ isLoading: true, super_sets: [] });
    } else {
      this.setState({ isLoading: true });
    }
    try {
      const { page, resultSize, searchText } = this.state;
      nasm.api
        .fetchAllSuperSets({
          page,
          size: resultSize,
          search: searchText,
        })
        .then((result) => {
          this.setState({
            super_sets:
              page === 1
                ? result.compoundRoutines
                : [...this.state.super_sets, ...result.compoundRoutines],
            hasMoreSuperSet: result.hasMore,
            page: page + 1,
            refreshing: false,
            isLoading: false,
          });
          if (page === 1 && !result.hasMore) {
            this.setState({ isLoading: true }, () => {
              this.getExercises(shouldClearPreviousSectionExercises);
            });
          }
        })
        .catch((err) => err);
    } catch (error) {
      this.setState({ super_sets: [] });
    }
  };

  setSearchText = (searchText) => {
    this.setState({ searchText, searching: true }, () => {
      if (this.exerciseList) {
        this.exerciseList.scrollToIndex({ index: 0, viewPosition: 0 });
      }
      this.searchTextUpdated();
    });
  };

  getSections = () => {
    const { exercises, super_sets } = this.state;
    if (!exercises?.length && !super_sets.length) {
      return [];
    }

    const sourceSelectedFilters = this.state.selectedFilters[EXERCISE_FILTER_CATEGORIES.SOURCE];

    const isAnyFiltersSelected = sourceSelectedFilters?.length > 0;

    const showCustomExercises = sourceSelectedFilters?.find(
      (filter) => filter === 'trainer',
    );
    const showNasmExercises = sourceSelectedFilters?.find(
      (filter) => filter === 'nasm'
        || filter === 'hyperice'
        || filter === 'trx'
        || filter === 'usaw'
        || filter === 'therabody',
    );

    const myCircuits = super_sets.filter(
      (superset) => superset.routine_type === ROUTINE_TYPES.CIRCUIT
        && !isAnyFiltersSelected,
    );

    const mySuperSets = super_sets.filter(
      (superset) => superset.routine_type === ROUTINE_TYPES.SUPER_SET
        && !isAnyFiltersSelected,
    );

    const myExercises = exercises.filter(
      (exercise) => !!exercise.owner_id
        && !exercise.is_favorite
        && (!!showCustomExercises || !isAnyFiltersSelected),
    );
    const nasmExercises = exercises.filter(
      (exercise) => !exercise.owner_id
        && !exercise.is_favorite
        && (!!showNasmExercises || !isAnyFiltersSelected),
    );

    const sections = [];

    if (myCircuits?.length) {
      sections.push({
        title: 'Circuits',
        data: myCircuits,
      });
    }

    if (mySuperSets?.length) {
      sections.push({
        title: 'Super Sets',
        data: mySuperSets,
      });
    }

    if (myExercises?.length) {
      sections.push({
        title: 'My Exercises',
        data: myExercises,
      });
    }

    if (nasmExercises?.length) {
      sections.push({
        title: 'EDGE Exercises',
        data: nasmExercises,
      });
    }

    return sections;
  };

  searchTextUpdated = debounce(() => {
    if (
      this.state.isComingFromSuperSetBuilder
      || this.state.isComingFromCircuitBuilder
    ) {
      this.setState({ nextPage: 1 }, () => {
        this.getExercises();
      });
    } else {
      this.setState(
        {
          page: 1,
          nextPage: 1,
          isLoading: true,
          exercises: [],
        },
        () => {
          this.getCompoundRoutines(true);
        },
      );
    }
  }, 500);

  addExercisesToWorkout = () => {
    const quickAdd = this.props.route.params?.quickAdd;
    const sectionId = this.props.route.params?.sectionId;

    if (quickAdd) {
      this.props.assignExercises();
    }
    this.setState({ isLoading: true }, async () => {
      if (this.props.addExercises && !this.state.addSelected) {
        try {
          const selectedExercises = [...this.state.selectedExercises].map(
            (ex) => {
              const compound_routine_exercises = [];
              if ('compound_routine_exercises' in ex) {
                ex.compound_routine_exercises.forEach((compoundExe) => {
                  compound_routine_exercises.push({
                    ...compoundExe.exercise,
                    compound_routine_id: compoundExe.compound_routine_id,
                  });
                });
                return {
                  ...ex,
                  compound_routine_exercises,
                };
              }
              return ex;
            },
          );
          await this.props.addExercises({
            exercises: selectedExercises,
            sectionId,
          });
          if (quickAdd) {
            this.quickAddExercisesAdded();
          } else {
            this.props.navigation.goBack();
          }
        } catch (error) {
          Alert.alert('Error', error.message);
          this.state.selectedExercises.forEach((exercise) => this.props.removeExercise({
            exercise,
            sectionId,
            flagChanges: false,
          }));
        } finally {
          this.setState({ isLoading: false });
        }
      }
    });
  };

  clearSelections = () => {
    this.setState({ selectedExercises: [] });
  };

  quickAddExercisesAdded = () => {
    if (this.props.programContext === programContexts.SCHEDULING) {
      this.props.navigation.navigate('AddingToWorkout', {
        quickAdd: this.props.route.params?.quickAdd,
      });
    }
  };

  updateFilters = (selectedFilters) => {
    this.setState({ selectedFilters, refreshing: true, nextPage: 1 }, () => this.getExercises());
  };

  exerciseIsSelectedIndex(exercise) {
    const { selectedExercises } = this.state;
    const index = selectedExercises.findIndex(
      (element) => element.id === exercise.id,
    );
    return index;
  }

  exerciseIsSelected(exercise) {
    const { selectedExercises } = this.state;
    const found = selectedExercises.some((element) => element.id === exercise.id);
    return found;
  }

  addToCircuit = () => {
    if (this.state.selectedExercises?.length) {
      this.props.route?.params?.onSelectExercise(this.state.selectedExercises);
      this.props.navigation.goBack();
    } else {
      Alert.alert('Alert', 'Please select an exercise to continue.');
    }
  };

  renderEmptyState = () => {
    if (
      !this.state.isLoading
      && this.state.exercises.length === 0
      && this.state.super_sets.length === 0
    ) {
      return (
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            paddingTop: 40,
          }}
        >
          <Image style={styles.emptyStateImage} source={emptyStateImage} />
          <Text style={styles.emptyStateTitleText}>NO RESULTS FOUND</Text>
          <Text style={styles.emptyStateSubTitleText}>
            If you have filters active, it might help to deactivate them.
          </Text>
        </View>
      );
    }
    return null;
  };

  renderLoader = () => (
    <View style={{ justifyContent: 'center', height: 100 }}>
      <ActivityIndicator animating={this.state.isLoading} size="large" />
    </View>
  );

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => {
        this.props.navigation.goBack();
      }}
      titleStyle={{ fontFamily: 'Avenir', fontSize: 14, color: colors.white }}
    />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => {
        if (this.state.isComingFromSuperSetBuilder) {
          if (this.state.selectedExercises?.length) {
            this.props.route?.params?.onSelectExercise(
              this.state.selectedExercises[0],
            );
            this.props.navigation.goBack();
          } else {
            Alert.alert('Alert', 'Please select an exercise to continue.');
          }
        } else {
          const clearAll = this.props.route.params?.clearAll ?? (() => {});
          clearAll();
        }
      }}
      title={this.state.isComingFromSuperSetBuilder ? 'Save' : 'Clear Exercises'}
      titleStyle={{
        fontFamily: 'Avenir', fontSize: 14, color: colors.white, width: 95,
      }}
    />
  );

  renderBottomBar = () => {
    if (this.state.selectedExercises.length > 0) {
      let buttonTitle = 'Add to Workout';
      if (this.state.isComingFromCircuitBuilder) {
        buttonTitle = 'Add to Circuit';
      } else {
        buttonTitle = this.props.programContext === programContexts.SCHEDULING
          ? 'Next'
          : 'Add to Workout';
      }
      return (
        <BottomBannerActionView
          title={`${this.state.selectedExercises.length} Selected`}
          buttonTitle={buttonTitle}
          buttonDisabled={this.state.addSelected}
          onPress={
            this.state.isComingFromCircuitBuilder
              ? this.addToCircuit
              : this.addExercisesToWorkout
          }
        />
      );
    }
    return null;
  };

  renderFilterButton = () => (
    <TouchableOpacity onPress={this.onPressFilter}>
      <Image source={filterIcon} style={{ marginRight: 16 }} />
    </TouchableOpacity>
  );

  renderList = () => {
    if (this.state.searching) {
      return (
        <LoadingSpinner
          visible={this.state.searching}
          size="large"
          backgroundColor={colors.white}
          color={colors.subGrey}
          viewStyle={styles.searchingListSpinner}
        />
      );
    }
    return (
      <SectionList
        ref={(ref) => {
          this.exerciseList = ref;
        }}
        keyExtractor={(item) => item.id}
        stickySectionHeadersEnabled
        sections={this.getSections()}
        renderItem={this.renderRow}
        renderSectionHeader={this.renderSectionHeader}
        renderSectionFooter={this.renderNoContent}
        initialNumToRender={200}
        maxToRenderPerBatch={200}
        updateCellsBatchingPeriod={75}
        onEndReachedThreshold={0.5}
        onEndReached={this.onLoadNextPage}
        windowSize={51}
        ListEmptyComponent={this.renderEmptyState}
        ListFooterComponent={this.renderLoader}
        refreshControl={(
          <RefreshControl
            refreshing={this.state.refreshing}
            onRefresh={this.onRefresh}
          />
        )}
        getItemLayout={(data, index) => ({
          length: this.state.exercises.length,
          offset: curvedScale(100) * index,
          index,
        })}
      />
    );
  };

  renderRow = ({ item }) => (
    <ExerciseCell
      exercise={item}
      onPressExercise={this.onPressExercise}
      isSelected={this.exerciseIsSelected(item)}
      showsSelectionImage
    />
  );

  renderSectionHeader = ({ section }) => {
    const sectionHeader = (
      <View style={styles.sectionHeaderContainer}>
        <Text style={styles.sectionHeaderText}>{section.title}</Text>
      </View>
    );

    if (section.title === 'NASM Exercises' || section.data.length) return sectionHeader;
    // Create a custom header to show the option to add a custom
    // exercise if the custom section is empty
    return sectionHeader;
  };

  renderNoContent = ({ section: { title, data } }) => {
    if (title === 'My Exercises' && data.length === 0) {
      return (
        <View style={[styles.container, { backgroundColor: colors.white }]}>
          <TouchableOpacity style={styles.cell} onPress={this.onPressAdd}>
            <View style={styles.leftContainer}>
              <Text style={styles.customRowText}>
                Create your own exercise or superset
              </Text>
            </View>
            <Image style={styles.arrow} source={plusSign} />
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  };

  renderTopBar = () => (
    <HeaderSearchBar
      searchText={this.state.searchText}
      onChangeText={this.setSearchText}
      clearable
      renderActionButton={this.renderFilterButton}
      paddingTop={10}
      light
      shadow={false}
      placeholder="Search"
    />
  );

  render() {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor:
            this.state.selectedExercises.length > 0
              ? colors.medYellow
              : colors.white,
        }}
      >
        <View style={{ flex: 1, backgroundColor: colors.white }}>
          {this.renderTopBar()}
          {this.renderList()}
          {this.state.isComingFromSuperSetBuilder
            ? null
            : this.renderBottomBar()}
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  emptyStateImage: {
    alignSelf: 'center',
    width: 50,
    height: 50,
  },
  emptyStateTitleText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 14,
    color: colors.subGrey,
    textAlign: 'center',
    alignSelf: 'center',
    marginTop: 8,
  },
  emptyStateSubTitleText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: colors.subGrey,
    textAlign: 'center',
    alignSelf: 'center',
    marginTop: 10,
    width: '65%',
  },
  searchingListSpinner: {
    justifyContent: 'flex-start',
    position: 'relative',
    paddingTop: '8%',
  },
  sectionHeaderContainer: {
    backgroundColor: colors.paleGray,
    height: 34,
    paddingHorizontal: 16,
    justifyContent: 'center',
  },
  sectionHeaderText: {
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '600',
    color: colors.subGrey,
  },
});

const mapState = ({ selectedDay, programContext }) => ({
  selectedDay,
  programContext,
});
const mapDispatch = {
  addExercises,
  assignExercises,
  removeExercise,
};
export default connect(mapState, mapDispatch)(AddExercises);
