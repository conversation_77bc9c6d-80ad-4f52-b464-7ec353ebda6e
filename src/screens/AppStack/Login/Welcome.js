import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import analytics from '@react-native-firebase/analytics';

// Components
import {
  Alert,
  Animated,
  Dimensions,
  Image,
  LayoutAnimation,
  Linking,
  Platform,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import SplashScreen from 'react-native-splash-screen';
import InAppReview from 'react-native-in-app-review';
import Video from 'react-native-video';
import { LaunchArguments } from 'react-native-launch-arguments';
import { version } from '../../../../package.json';
import {
  track,
  flushMixpanel,
  setUser,
  setUserProp,
} from '../../../util/Analytics';

// Components
import {
  Button,
  PageContainer,
  TextInput,
  ScaledText,
} from '../../../components';
import { NASMStrings } from '../../../strings';
import {
  ROLES,
  SubscriptionItemSKUs,
  getAppStoreURLForPlatform,
  androidSafeLayoutAnimation,
} from '../../../constants';
import {
  relogin,
  selectClient,
  reset,
  logout,
  deselectDeepLink,
} from '../../../actions';
import { scaleWidth, scaleHeight } from '../../../util/responsive';
import * as PurchaseManager from '../../../util/PurchaseManager';
import * as validate from '../../../util/validate';
import * as db from '../../../dataManager';
import nasm from '../../../dataManager/apiConfig';
import subscriptionLevels from '../../../util/subscriptionLevels';

// Styles
import { colors, shadow, header } from '../../../styles';
import { logComponentException } from '../../../util/logging';
import { resetTrainerActiveProfile } from '../../../reducers/trainerActiveProfileReducer';

// Constants
const bgVideo = require('../../../resources/nasmAppVideo_3.mp4');
const cornerX = require('../../../resources/imgCircleClose.png');

const launchArgs = {};
Object.entries(LaunchArguments.value()).forEach(([k, v]) => {
  launchArgs[k] = v;
});

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
  }).isRequired,
  deepLink: PropTypes.object,
};

// (W) - Welcome
class Welcome extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loginButtonContainerFadeAnimation: new Animated.Value(0),
      forceUpdateContainerFadeAnimation: new Animated.Value(0),
      forceUpdateZIndex: 0,
      didHideSplashScreen: false,
      products: null,
      isGettingStarted: false,
      email: '',
      rememberMe: false,
      checkingEmail: false,
    };
  }

  componentDidMount() {
    try {
      this.props.resetTrainerActiveProfile();
    } catch (error) {
      // TODO: Handle error here
    }
    this.setNavigationOptions();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      if (this.state.didHideSplashScreen) {
        StatusBar.setBarStyle('light-content');
        if (Platform.OS === 'android') {
          StatusBar.setTranslucent(true);
          StatusBar.setBackgroundColor('rgba(0, 0, 0, 0)');
        }
      }
      analytics().logEvent('screen_view', { screen_name: 'welcome' });
    });
    setTimeout(this.getLoginPrefs, 100);
    this.checkAppVersion();
    PurchaseManager.initConnection()
      .then(() => {
        PurchaseManager.getProducts(SubscriptionItemSKUs)
          .then((products) => {
            this.setState({ products }, () => Promise.resolve());
          })
          .catch((error) => {
            this.setState({ products: null }, () => Promise.reject(error));
          });
      })
      .catch(() => {});
  }

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
    PurchaseManager.endConnection();
  }

  handleLoggedInUser = (user, navigation) => {
    setUser(user.id);
    setUserProp('LevelOfSubscription', subscriptionLevels(user));
    const { deepLink } = this.props;
    if (user.role === ROLES.TRAINER) {
      analytics().logEvent('login', { user_role: 'trainer' });
      analytics().setUserProperty('role', 'trainer');
    } else {
      const walkIn = this.isWalkinClient(user);
      analytics().logEvent('login', {
        user_role: walkIn ? 'walk_in' : 'client',
      });
      analytics().setUserProperty('role', walkIn ? 'walk_in' : 'client');
      this.props.selectClient(user);
    }
    // Navigate to deep link if available
    if (deepLink && !deepLink.includes('channelId')) {
      this.props.route.params?.linkTo(this.props.deepLink);
      this.props.deselectDeepLink();
    } else {
      reset(navigation, 'ModalStack', null, null);
    }
    this.hideSplashScreen();
    this.handleRatingPrompt();
  };

  handleRatingPrompt = async () => {
    const userRatingResponse = await nasm.api.checkRatingPromptCriteria();
    const { meetCriteria } = userRatingResponse;
    if (userRatingResponse && meetCriteria) {
      // Check whether the user's device is supported or not!
      if (InAppReview.isAvailable()) {
        // Trigger InAppReview UI
        track('in_app_review_requested');
        InAppReview.RequestInAppReview()
          .then((hasFlowFinishedSuccessfully) => {
            if (hasFlowFinishedSuccessfully) {
              const data = {
                rating_given: true,
              };
              nasm.api.updateRatingGivenByUser(data);
            }
            track('in_app_review_callback_received', {
              finished_successfully: `${hasFlowFinishedSuccessfully}`,
            });
          })
          .catch((error) => {
            logComponentException('RatingPrompt', error);
          });
      }
    }
  };

  onEmailEntered = () => {
    track('Email_entered');
    // Flush call is just for testing, it forces the mixpanel dashboard to update immediately
    flushMixpanel();
    this.setState({ checkingEmail: true }, async () => {
      if (!validate.email(this.state.email)) {
        Alert.alert('Error', 'Please provide a valid email address.');
        this.setState({ checkingEmail: false });
        return;
      }
      try {
        const result = await nasm.api.checkEmail(this.state.email);
        const {
          edgeResult,
          uaResult,
          uaAccountInEDGE,
          edgeAccountInUA,
          edgeUserRole,
        } = result;
        analytics().logEvent('welcome_email_entered', {
          email: this.state.email,
          edge_user: edgeResult ? 'true' : 'false',
          ua_user: uaResult ? 'true' : 'false',
          already_linked: uaAccountInEDGE ? 'true' : 'false',
          is_client: edgeUserRole === 'CLIENT' ? 'true' : 'false',
        });
        if (!edgeResult && !uaResult) {
          this.toggleGettingStarted(true);
        } else if (edgeResult && !uaAccountInEDGE && edgeAccountInUA) {
          Alert.alert('Please use your NASM account to log in');
        } else {
          this.props.navigation.navigate('Login', {
            email: this.state.email,
            rememberMe: this.state.rememberMe,
            hasEdgeAccount: edgeResult,
            hasUAAccount: uaResult,
            uaAccountInEDGE,
            edgeAccountInUA,
            edgeUserRole,
          });
        }
      } catch (error) {
        if (error.code === 5003) {
          this.props.navigation.navigate('Maintenance');
        } else {
          Alert.alert('Error', error.message);
        }
      } finally {
        this.setState({ checkingEmail: false });
      }
    });
  };

  onTextBlur = (text, field) => {
    if (text) {
      this.setState({ [field]: text.trim() });
    }
  };

  setNavigationOptions = () => {
    const showingForceUpdate = this.props.route?.params?.showingForceUpdate ?? false;
    let options = {};
    if (showingForceUpdate) {
      options = {
        ...header.default,
        headerLeft: null,
        title: 'Update Required',
      };
    }
    options = {
      headerTransparent: true,
      headerLeft: null,
      headerStyle: {
        height: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.25)',
        borderBottomWidth: 0,
      },
    };
    this.props.navigation.setOptions(options);
  };

  getLoginPrefs = async () => {
    const loginPrefs = await db.getLoginPreferences();
    const { rememberMe, email } = loginPrefs;
    this.setState({ rememberMe, email });
  };

  checkLogin = () => {
    db.hasToken()
      .then((hasToken) => {
        if (hasToken && !launchArgs.doNotLogIn) {
          return this.props.relogin().then((myUser) => {
            if (myUser) {
              return nasm.api
                .checkTerms()
                .then(({ requires_pp_update, requires_tc_update }) => {
                  if (requires_tc_update) {
                    // display new terms
                    this.hideSplashScreen().then(() => {
                      this.props.navigation.navigate('TermsAndConditions', {
                        updatePrivacy: requires_pp_update,
                        navWhenDone: (navigation) => {
                          this.handleLoggedInUser(myUser, navigation);
                        },
                      });
                    });
                  } else if (requires_pp_update) {
                    this.hideSplashScreen().then(() => {
                      this.props.navigation.navigate('PrivacyPolicy', {
                        navWhenDone: (navigation) => {
                          this.handleLoggedInUser(myUser, navigation);
                        },
                      });
                    });
                  } else {
                    this.handleLoggedInUser(myUser, this.props.navigation);
                  }
                });
            }
            return this.fadeInLoginContainer();
          });
        }
        // couldn't relogin, continue as normal
        return this.fadeInLoginContainer();
      })
      .catch((error) => {
        this.setState({ isLoading: false }, () => {
          if (error.code === 5003) {
            this.props.navigation.navigate('Maintenance');
          } else {
            Alert.alert('Login Error', error.message);
          }
        });
      });
  };

  hideSplashScreen = () => {
    if (this.state.didHideSplashScreen) {
      return Promise.resolve(true);
    }
    SplashScreen.hide();
    StatusBar.setHidden(false);
    StatusBar.setBarStyle('light-content');
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('rgba(0, 0, 0, 0)');
    }
    this.setState({ didHideSplashScreen: true });
    return Promise.resolve(true);
  };

  isWalkinClient = (user) => {
    if (user.role === ROLES.CLIENT) {
      return !user.client_user.trainer;
    }
    return false;
  };

  platformSpecificStoreName = () => {
    if (Platform.OS === 'android') {
      return 'Play Store';
    }
    return 'App Store';
  };

  toggleGettingStarted = (bool) => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ isGettingStarted: bool });
  };

  launchAppStore = () => {
    const url = getAppStoreURLForPlatform(Platform.OS);
    Linking.canOpenURL(url).then((supported) => {
      if (supported) {
        Linking.openURL(url);
      }
    });
  };

  presentRecommendedUpdateAlert() {
    this.hideSplashScreen().then(() => {
      Linking.canOpenURL(getAppStoreURLForPlatform(Platform.OS))
        .then((supported) => {
          if (supported) {
            const buttons = [
              {
                text: this.platformSpecificStoreName(),
                onPress: () => this.launchAppStore(),
                style: 'cancel',
              },
              { text: 'Cancel' },
            ];
            this.showNewVersionAlertWithButtons(buttons);
          } else {
            this.showNewVersionAlertWithButtons(null);
          }
        })
        .catch(() => {
          this.showNewVersionAlertWithButtons(null);
        });
    });
  }

  showNewVersionAlertWithButtons(buttons) {
    const name = this.platformSpecificStoreName();
    Alert.alert(
      'New Version Available',
      `Looks like there is a new version of NASM EDGE available on the ${name}. Would you like to update your app?`,
      buttons || [{ text: 'Okay', style: 'Cancel' }],
    );
    this.checkLogin();
  }

  checkAppVersion() {
    db.checkAppVersion()
      .then((response) => {
        const { updateRequired } = response;
        switch (updateRequired) {
          case NASMStrings.appUpdateRequired:
            this.fadeInForceUpdateView();
            break;
          case NASMStrings.appUpdateRecommended:
            this.presentRecommendedUpdateAlert();
            break;
          default:
            this.checkLogin();
            break;
        }
      })
      .catch(() => {
        this.checkLogin();
      });
  }

  fadeInForceUpdateView() {
    this.props.navigation.setParams({ showingForceUpdate: true });
    this.hideSplashScreen().then(() => {
      this.setState({ forceUpdateZIndex: 1 }, () => {
        Animated.timing(this.state.forceUpdateContainerFadeAnimation, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }).start();
      });
    });
  }

  fadeInLoginContainer() {
    this.hideSplashScreen().then(() => {
      Animated.timing(this.state.loginButtonContainerFadeAnimation, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    });
  }

  renderGettingStarted = () => (
    <View style={styles.viewContainer}>
      <SafeAreaView style={styles.cornerXContainer}>
        <TouchableOpacity onPress={() => this.toggleGettingStarted(false)}>
          <Image source={cornerX} />
        </TouchableOpacity>
      </SafeAreaView>

      <View style={[styles.logo, { justifyContent: 'center' }]}>
        <ScaledText style={styles.gettingStartedText}>
          Welcome to EDGE,
        </ScaledText>
        <ScaledText style={styles.gettingStartedText}>
          tell us about yourself.
        </ScaledText>
      </View>
      <View style={styles.roundedButtonContainer}>
        <Button
          testID="TrainerButton"
          title="I am a Trainer"
          buttonStyle={[
            styles.roundedButton,
            {
              backgroundColor: 'transparent',
              borderWidth: 2,
              borderColor: colors.white,
            },
          ]}
          textStyles={styles.roundedButtonText}
          onPress={() => {
            this.props.navigation.navigate('AllUserRegister', {
              freeTrialPeriod: PurchaseManager.getFreeTrialPeriod(
                this.state.products,
              ),
              type: 'trainer',
              email: this.state.email,
            });
          }}
        />
        <Button
          testID="CPTButton"
          title="I will be an NASM CPT "
          buttonStyle={[
            styles.roundedButton,
            {
              backgroundColor: 'transparent',
              borderWidth: 2,
              borderColor: colors.white,
            },
          ]}
          textStyles={styles.roundedButtonText}
          onPress={() => {
            this.props.navigation.navigate('AllUserRegister', {
              freeTrialPeriod: PurchaseManager.getFreeTrialPeriod(
                this.state.products,
              ),
              type: 'student',
              email: this.state.email,
            });
          }}
        />
        <Button
          testID="WorkoutButton"
          title="I want to Workout"
          buttonStyle={[
            styles.roundedButton,
            {
              backgroundColor: 'transparent',
              borderWidth: 2,
              borderColor: colors.white,
            },
          ]}
          textStyles={styles.roundedButtonText}
          onPress={() => {
            this.props.navigation.navigate('AllUserRegister', {
              type: 'user',
              email: this.state.email,
            });
          }}
        />
      </View>
    </View>
  );

  renderOriginalView = () => {
    const emailValid = validate.email(this.state.email);
    return (
      <View style={styles.viewContainer}>
        <View style={{ flex: 1, justifyContent: 'center', width: '100%' }}>
          <ScaledText style={styles.welcomText}>
            {"Let's start with\nyour email."}
          </ScaledText>
          <TextInput
            value={this.state.email}
            onChangeText={(email) => this.setState({ email })}
            onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'email')}
            placeholder="Email Address"
            placeholderTextColor={colors.white}
            keyboardType="email-address"
            returnKeyType="next"
            showIcon={false}
            validation={validate.email}
            containerStyle={[
              styles.inputContainerStyle,
              { marginTop: 60 * scale },
            ]}
            inputText={styles.inputTextStyle}
            onSubmitEditing={this.onEmailEntered}
            selectionColor={
              Platform.OS === 'android' ? colors.selectionColor : colors.white
            }
            keyboardShouldPersistTaps="handled"
            testID="EmailTextInput"
          />
        </View>
        <View style={{ paddingBottom: 35 }}>
          <Button
            title="Next"
            buttonStyle={[
              styles.roundedButton,
              {
                backgroundColor: 'transparent',
                borderWidth: 2,
                borderColor: emailValid ? colors.white : colors.subGrey,
              },
            ]}
            textStyles={[
              styles.roundedButtonText,
              { color: emailValid ? colors.white : colors.subGrey },
            ]}
            onPress={this.onEmailEntered}
            isLoading={this.state.checkingEmail}
            loadingSpinnerSize="small"
            disabled={!emailValid}
            testID="SubmitNext"
          />
        </View>

        <Text style={styles.versionText}>{`v${version}`}</Text>
      </View>
    );
  };

  render() {
    console.disableYellowBox = true;
    return (
      <PageContainer
        containerStyle={{ paddingHorizontal: 0 }}
        testID="WelcomeScreen"
      >
        {/* Background Video */}
        <Video
          source={bgVideo} // Can be a URL or a local file.
          ref={(ref) => {
            this.player = ref;
          }} // Store reference
          rate={1.0} // 0 is paused, 1 is normal.
          volume={1.0} // 0 is muted, 1 is normal.
          muted={false} // Mutes the audio entirely.
          repeat // Repeat forever.
          playInBackground={false} // Audio continues to play when app entering background.
          playWhenInactive // [iOS] Video continues to play when control or notification center are shown.
          disableFocus // [Android] Video audio is allowed to play in the background from other apps.
          ignoreSilentSwitch="obey" // [iOS] "obey" will allow audio to play in the background, "ignore" will stop audio from playing in the background.
          progressUpdateInterval={250.0} // [iOS] Interval to fire onProgress (default to ~250ms)
          resizeMode="cover"
          selectedAudioTrack={{ type: 'disabled' }}
          style={styles.bgImage}
          paused={false}
          controls={false}
        />

        {/* Main View */}
        <Animated.View
          style={[
            styles.viewContainer,
            {
              zIndex: 1,
              opacity: this.state.loginButtonContainerFadeAnimation,
            },
          ]}
        >
          {/* Original */}
          {!this.state.isGettingStarted && this.renderOriginalView()}

          {/* Getting Started */}
          {this.state.isGettingStarted && this.renderGettingStarted()}
        </Animated.View>

        {/* Force Update View */}
        <Animated.View
          style={[
            styles.forceUpdateContainer,
            {
              opacity: this.state.forceUpdateContainerFadeAnimation,
              zIndex: this.state.forceUpdateZIndex,
            },
          ]}
        >
          <Text style={styles.updateMainText}>{'Update\nRequired'}</Text>
          <Text style={styles.updateSubText}>
            {`An update is required to continue using the NASM EDGE app. Please visit the ${this.platformSpecificStoreName()} to download the latest version`}
          </Text>
          <TouchableOpacity
            style={styles.goToStoreButton}
            onPress={this.launchAppStore}
          >
            <Text style={styles.goToStoreText}>
              {`Go To The ${this.platformSpecificStoreName()}`}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </PageContainer>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

// Styles
const styles = StyleSheet.create({
  bgImage: {
    position: 'absolute',
    bottom: 0,
    top: 0,
    right: 0,
    left: 0,
  },
  cornerXContainer: {
    position: 'absolute',
    left: Platform.OS === 'android' ? 15 * scale : 14 * scale,
    top: 0,
    bottom: 0,
    right: 0,
  },
  roundedButtonContainer: {
    position: 'absolute',
    bottom: scaleHeight(6),
    left: 0,
    right: 0,
    flexDirection: 'column',
  },
  roundedButton: {
    alignSelf: 'center',
    borderRadius: 27.5 * scale,
    width: scaleWidth(75),
    height: scaleHeight(6.4),
    marginBottom: scaleHeight(3),
  },
  roundedButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    fontStyle: 'normal',
    lineHeight: 32,
    letterSpacing: 0,
    textAlign: 'center',
    color: colors.white,
  },
  logo: {
    alignSelf: 'center',
    resizeMode: 'center',
    top: scaleHeight(15),
    left: 0,
    right: 0,
    height: scaleHeight(20),
    width: scaleWidth(75),
  },
  gettingStartedText: {
    color: colors.white,
    fontSize: 24,
    letterSpacing: 0,
    fontFamily: 'Avenir-Black',
    fontWeight: '900',
    alignSelf: 'center',
  },
  // Force Update Styles
  updateMainText: {
    fontFamily: 'Avenir-Heavy',
    fontWeight: '900',
    fontSize: 46 * scale,
    color: colors.medYellow,
    textAlign: 'center',
  },
  updateSubText: {
    marginTop: 16 * scale,
    marginHorizontal: 33 * scale,
    fontFamily: 'Avenir-Book',
    fontSize: 15 * scale,
    lineHeight: 21 * scale,
    color: colors.subGrey,
    textAlign: 'center',
  },
  viewContainer: {
    flex: 1,
    width: '100%',
    paddingTop: '5%',
    alignItems: 'center',
  },
  forceUpdateContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  goToStoreButton: {
    position: 'absolute',
    height: 55 * scale,
    bottom: 40 * scale,
    left: 0,
    right: 0,
    marginHorizontal: 19 * scale,
    backgroundColor: colors.macaroniAndCheese,
    borderRadius: 27.5 * scale,
    justifyContent: 'center',
    ...shadow,
  },
  goToStoreText: {
    color: colors.white,
    alignSelf: 'center',
    fontSize: 16 * scale,
    letterSpacing: 0,
    fontFamily: 'Avenir-Heavy',
  },
  welcomText: {
    fontFamily: 'Avenir',
    fontSize: 24,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.white,
  },
  inputContainerStyle: {
    borderColor: colors.white,
    marginHorizontal: scaleWidth(12),
    paddingHorizontal: 0,
  },
  inputTextStyle: {
    color: colors.white,
  },
  versionText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
    textAlign: 'left',
    color: colors.white,
    alignSelf: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
});

// Export
Welcome.propTypes = propTypes;
const mapStateToProps = ({ deepLink }) => ({ deepLink });
const mapDispatchToProps = {
  relogin,
  selectClient,
  logout,
  deselectDeepLink,
  resetTrainerActiveProfile,
};
export default connect(mapStateToProps, mapDispatchToProps)(Welcome);
