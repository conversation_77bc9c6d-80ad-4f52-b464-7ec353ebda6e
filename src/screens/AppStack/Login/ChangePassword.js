import React, { Component } from 'react';
import { connect } from 'react-redux';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import { StatusBar, View, Alert } from 'react-native';
import {
  Button,
  PageContainer,
  TextInput,
  ScaledText,
} from '../../../components';
import * as validate from '../../../util/validate';
import { colors } from '../../../styles';
import { ROLES } from '../../../constants';
import { baseScale, scaleHeight, curvedScale } from '../../../util/responsive';
import nasm from '../../../dataManager/apiConfig';
import * as db from '../../../dataManager';
import {
  login, selectClient, reset, deselectDeepLink,
} from '../../../actions';

class ChangePassword extends Component {
  static navigationOptions = {
    headerTransparent: true,
    title: null,
  };

  constructor(props) {
    super(props);
    this.inputRefs = {};
    this.state = {
      email: props.route.params?.email,
      rememberMe: props.route.params?.rememberMe,
      newPassword: '',
      newPasswordConfirmation: '',
      validationPassed: false,
      isLoading: false,
      passwordTextRed: false,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'change_password' });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onBlurText = () => {
    this.setState({
      passwordTextRed: !validate.password(this.state.newPassword),
    });
  };

  onPressSave = async () => {
    this.setState({ isLoading: true }, () => {
      this.createNewUAAccount();
    });
  };

  createNewUAAccount = async () => {
    const { email, newPassword } = this.state;
    try {
      await nasm.api.migrateEdgeAccountToUA(email, newPassword);
      const loggedInUser = await this.props.login({
        email,
        password: newPassword,
      });
      db.updateLoginPreferences(this.state.rememberMe, email).catch(() => {});
      nasm.api
        .checkTerms()
        .then(({ requires_pp_update, requires_tc_update }) => {
          if (requires_tc_update) {
            // display new terms
            this.props.navigation.navigate('TermsAndConditions', {
              updatePrivacy: requires_pp_update,
              navWhenDone: (navigation) => {
                this.handleLoggedInUser(loggedInUser, navigation);
              },
            });
          } else if (requires_pp_update) {
            this.props.navigation.navigate('PrivacyPolicy', {
              navWhenDone: (navigation) => {
                this.handleLoggedInUser(loggedInUser, navigation);
              },
            });
          } else {
            this.handleLoggedInUser(loggedInUser, this.props.navigation);
          }
        });
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      this.setState({ isLoading: false });
    }
  };

  focusInput = (input) => {
    const target = this.inputRefs[input];
    if (target && target.textInput) target.textInput.focus();
  };

  handleLoggedInUser = (user, navigation) => {
    this.setState(
      {
        isLoading: false,
      },
      () => {
        if (user.role === ROLES.TRAINER) {
          analytics().logEvent('login', { user_role: 'trainer' });
          analytics().setUserProperty('role', 'trainer');
          if (this.props.deepLink) {
            this.props.route.params?.linkTo(this.props.deepLink);
            this.props.deselectDeepLink();
          } else {
            reset(navigation, 'ModalStack', null, null);
          }
        } else {
          const walkIn = this.isWalkinClient(user);
          analytics().logEvent('login', {
            user_role: walkIn ? 'walk_in' : 'client',
          });
          analytics().setUserProperty('role', walkIn ? 'walk_in' : 'client');
          this.props.selectClient(user);

          // Navigate
          if (this.props.deepLink) {
            this.props.route.params?.linkTo(this.props.deepLink);
            this.props.deselectDeepLink();
          } else {
            reset(navigation, 'ModalStack', null, null);
          }
        }
      },
    );
  };

  updateInput = (value, key) => {
    this.setState({ [key]: value }, this.validateInputs);
  };

  validateInputs = () => {
    let { passwordTextRed } = this.state;
    if (validate.password(this.state.newPassword)) {
      passwordTextRed = false;
    }
    const fields = Object.keys(this.inputRefs)
      .filter((key) => !!this.inputRefs[key])
      .map((key) => {
        const { validation, value } = this.inputRefs[key].props;
        return validation(value);
      });
    const validationPassed = !fields.map((field) => !!field).includes(false);
    this.setState({ validationPassed, passwordTextRed });
    return validationPassed;
  };

  render() {
    const passwordHintStyle = [
      styles.passwordCriteria,
      this.state.passwordTextRed && { color: colors.macaroniAndCheese },
    ];
    return (
      <PageContainer containerStyle={styles.pageContainerStyle}>
        <View style={{ flex: 1, marginTop: scaleHeight(10) }}>
          <ScaledText style={styles.title}>
            We have updated our password policy
          </ScaledText>
          <ScaledText
            style={styles.description}
            ellipsizeMode="tail"
            numberOfLines={this.state.oneLineMessage ? 1 : undefined}
          >
            Please create a new password with
          </ScaledText>
          <ScaledText style={passwordHintStyle}>
            At least 8 characters, 1 uppercase, 1 lowercase
          </ScaledText>
          <ScaledText style={passwordHintStyle}>
            1 number and 1 special character.
          </ScaledText>
          <TextInput
            ref={(ref) => {
              this.inputRefs.newPassword = ref;
            }}
            value={this.state.newPassword}
            onChangeText={(newPassword) => this.updateInput(newPassword, 'newPassword')}
            placeholder="New Password"
            returnKeyType="next"
            validation={validate.password}
            placeholderTextColor={colors.white}
            secureTextEntry
            inputText={styles.inputTextStyle}
            selectionColor={colors.white}
            onSubmitEditing={() => this.focusInput('newPasswordConfirmation')}
            whiteIcons
            onBlur={this.onBlurText}
          />
          <TextInput
            ref={(ref) => {
              this.inputRefs.newPasswordConfirmation = ref;
            }}
            value={this.state.newPasswordConfirmation}
            onChangeText={(newPasswordConfirmation) => this.updateInput(
              newPasswordConfirmation,
              'newPasswordConfirmation',
            )}
            placeholder="Confirm New Password"
            returnKeyType="done"
            validation={(value) => validate.confirmPassword(this.state.newPassword, value)}
            placeholderTextColor={colors.white}
            secureTextEntry
            inputText={styles.inputTextStyle}
            selectionColor={colors.white}
            whiteIcons
            onBlur={this.onBlurText}
          />
        </View>
        <Button
          title="Save New Password"
          containerStyle={[
            styles.saveButtonStyle,
            {
              backgroundColor: this.state.validationPassed
                ? colors.azure
                : 'rgba(37, 146, 236, 0.25)',
            },
          ]}
          onPress={() => this.onPressSave()}
          disabled={!this.state.validationPassed}
          isLoading={this.state.isLoading}
        />
      </PageContainer>
    );
  }
}

const styles = {
  pageContainerStyle: {
    backgroundColor: colors.duskBlue,
    paddingHorizontal: '10%',
  },
  saveButtonStyle: {
    alignSelf: 'center',
    borderRadius: scaleHeight(3),
    borderWidth: 0,
    width: '100%',
    height: scaleHeight(6),
    marginVertical: baseScale(40),
  },
  inputTextStyle: {
    color: colors.white,
  },
  title: {
    fontFamily: 'Avenir',
    fontSize: 24,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.white,
  },
  description: {
    fontFamily: 'Avenir',
    fontSize: 18,
    textAlign: 'center',
    color: colors.white,
    marginTop: curvedScale(20),
  },
  passwordCriteria: {
    fontFamily: 'Avenir',
    fontSize: 14,
    textAlign: 'center',
    color: colors.white,
  },
};

// Export
const mapStateToProps = ({ deepLink }) => ({ deepLink });
const mapDispatchToProps = { login, selectClient, deselectDeepLink };

export default connect(mapStateToProps, mapDispatchToProps)(ChangePassword);
