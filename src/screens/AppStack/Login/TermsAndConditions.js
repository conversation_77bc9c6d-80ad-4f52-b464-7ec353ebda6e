import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Components
import {
  Alert,
  Image,
  SafeAreaView,
  StyleSheet,
  View,
  StatusBar,
  ScrollView,
} from 'react-native';
import Markdown from 'react-native-markdown-display';
import analytics from '@react-native-firebase/analytics';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Button, ScaledText } from '../../../components';

// Helpers
import { reset, logout } from '../../../actions';
import nasm from '../../../dataManager/apiConfig';
import { NASMStrings } from '../../../strings';
import { colors, header, androidSafeAreaStyle } from '../../../styles';

// PropTypes
const propTypes = {
  currentUser: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    pop: PropTypes.func,
  }).isRequired,
  logout: PropTypes.func.isRequired,
};

const closeBtn = require('../../../resources/closeCircle.png');

// Class
class TermsAndConditions extends Component {
  static navigationOptions = () => ({
    ...header.transparent,
    title: '',
    headerLeft: () => <View />,
  });

  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
      newUser: props.route.params?.newUser,
      newUserType: props.route.params?.newUserType,
      navWhenDone: props.route.params?.navWhenDone,
      updatePrivacy: props.route.params?.updatePrivacy,
      readOnly: props.route.params?.readOnly,
      terms: '',
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('dark-content');
      analytics().logEvent('screen_view', {
        screen_name: 'terms_and_conditions',
      });
    });
    this.unsubscribeBlur = this.props.navigation.addListener('blur', () => {
      StatusBar.setBarStyle('light-content');
    });
    nasm.api
      .getTermsAndConditions()
      .then(({ url }) => fetch(url))
      .then((file) => file.text())
      .then((terms) => {
        this.setState({ terms, isLoading: false });
      })
      .catch((error) => {
        Alert.alert('Error loading Terms and Conditions', error.message);
      });
  }

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
    if (this.unsubscribeBlur) {
      this.unsubscribeBlur();
    }
  }

  onPressAccept = async () => {
    if (!this.state.isLoading) {
      try {
        if (this.state.newUser) {
          this.props.navigation.navigate('PrivacyPolicy', {
            newUser: this.state.newUser,
            newUserType: this.state.newUserType,
          });
        } else {
          this.setState({ isLoading: true });

          if (this.state.updatePrivacy) {
            this.setState({ isLoading: false });
            this.props.navigation.navigate('PrivacyPolicy', {
              navWhenDone: this.state.navWhenDone,
            });
          } else {
            await nasm.api.acceptTerms();

            if (this.state.navWhenDone) {
              this.state.navWhenDone(this.props.navigation);
            } else {
              this.props.navigation.goBack();
            }
          }
        }
      } catch (error) {
        Alert.alert('Error', error.message);
        this.setState({ isLoading: false });
      } finally {
        this.setState({ isLoading: false });
      }
    }
  };

  onPressDecline = () => {
    if (this.state.newUser || this.state.readOnly) {
      this.props.navigation.pop(1);
    } else {
      Alert.alert(
        NASMStrings.declineTermsAlertTitle,
        NASMStrings.declineTermsAlertDescription,
        [
          {
            text: 'Cancel',
            onPress: () => {},
          },
          {
            text: 'Continue',
            onPress: () => {
              if (this.state.newUser) {
                this.props.navigation.pop(1);
              } else {
                this.props.logout();
                reset(this.props.navigation, 'Welcome', null, null);
              }
            },
          },
        ],
        { cancelable: true },
      );
    }
  };

  render() {
    return (
      <SafeAreaView style={{ flex: 1, ...androidSafeAreaStyle }}>
        <View style={styles.header}>
          <TouchableOpacity onPress={this.onPressDecline}>
            <Image source={closeBtn} />
          </TouchableOpacity>
          <ScaledText style={styles.title}>Terms and Conditions</ScaledText>
        </View>
        <ScrollView contentContainerStyle={styles.termsContainerStyle}>
          <Markdown>{this.state.terms}</Markdown>
        </ScrollView>

        {!this.state.readOnly && (
          <View style={styles.buttonsContainer}>
            <Button
              testID="TermsAcceptButton"
              title="Accept and Continue"
              onPress={this.onPressAccept}
              isLoading={this.state.isLoading}
              containerStyle={{ borderRadius: 27.5 }}
            />
          </View>
        )}
      </SafeAreaView>
    );
  }
}

// styles
const styles = StyleSheet.create({
  header: {
    paddingBottom: 30,
    paddingTop: 20,
    paddingHorizontal: 16,
    borderColor: colors.actionSheetDivider,
    borderBottomWidth: 1,
  },
  title: {
    fontFamily: 'Avenir-Black',
    fontSize: 24,
    textAlign: 'center',
    marginTop: 20,
  },
  buttonsContainer: {
    paddingTop: 25,
    paddingBottom: 35,
    paddingHorizontal: 20,
    borderColor: colors.actionSheetDivider,
    borderTopWidth: 1,
  },
  termsContainerStyle: {
    paddingHorizontal: 20,
    paddingVertical: 25,
  },
});

// export
TermsAndConditions.propTypes = propTypes;
const mapStateToProps = () => ({});
const mapDispatchToProps = { logout };
export default connect(mapStateToProps, mapDispatchToProps)(TermsAndConditions);
