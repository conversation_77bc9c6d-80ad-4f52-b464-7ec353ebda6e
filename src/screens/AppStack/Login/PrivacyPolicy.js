import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Components
import {
  Alert,
  Image,
  SafeAreaView,
  StyleSheet,
  View,
  StatusBar,
  ScrollView,
} from 'react-native';
import Markdown from 'react-native-markdown-display';
import analytics from '@react-native-firebase/analytics';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Button, ScaledText } from '../../../components';

// Helpers
import {
  reset, logout, login, selectClient,
} from '../../../actions';
import nasm from '../../../dataManager/apiConfig';
import { NASMStrings } from '../../../strings';
import { colors, header, androidSafeAreaStyle } from '../../../styles';
import { DEVICE_LOCALE } from '../../../constants';
import { API_VERSION } from '../../../api/nasm_sdk/constants';

// PropTypes
const propTypes = {
  currentUser: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    pop: PropTypes.func,
  }).isRequired,
  logout: PropTypes.func.isRequired,
};

const closeBtn = require('../../../resources/closeCircle.png');

// Class
class PrivacyPolicy extends Component {
  static navigationOptions = () => ({
    ...header.transparent,
    title: '',
    headerLeft: () => <View />,
  });

  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
      newUser: props.route.params?.newUser,
      newUserType: props.route.params?.newUserType,
      navWhenDone: props.route.params?.navWhenDone,
      updateTerms: props.route.params?.updatePrivacy,
      readOnly: props.route.params?.readOnly,
      privacyPolicy: '',
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('dark-content');
      analytics().logEvent('screen_view', { screen_name: 'privacy_policy' });
    });
    this.unsubscribeBlur = this.props.navigation.addListener('blur', () => {
      StatusBar.setBarStyle('light-content');
    });
    nasm.api
      .getPrivacyPolicy()
      .then(({ url }) => fetch(url))
      .then((file) => file.text())
      .then((privacyPolicy) => {
        this.setState({ privacyPolicy, isLoading: false });
      })
      .catch((error) => {
        Alert.alert('Error loading Privacy Policy', error.message);
      });
  }

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
    if (this.unsubscribeBlur) {
      this.unsubscribeBlur();
    }
  }

  onPressAccept = async () => {
    if (!this.state.isLoading) {
      try {
        if (this.state.newUser) {
          this.setState({ isLoading: true });
          this.createAccount(
            { ...this.state.newUser, accepted_terms: true },
            this.state.newUserType,
          );
        } else if (this.state.updateTerms) {
          this.props.navigation.navigate('TermsAndConditions', {
            navWhenDone: this.state.navWhenDone,
          });
        } else {
          this.setState({ isLoading: true });
          await nasm.api.acceptTerms();

          if (this.state.navWhenDone) {
            this.state.navWhenDone(this.props.navigation);
          } else {
            this.props.navigation.goBack();
          }
          this.setState({ isLoading: false });
        }
      } catch (error) {
        Alert.alert('Error', error.message);
        this.setState({ isLoading: false });
      }
    }
  };

  onPressDecline = () => {
    if (this.state.newUser || this.state.readOnly) {
      this.props.navigation.pop(1);
    } else {
      Alert.alert(
        NASMStrings.declinePrivacyPolicyAlertTitle,
        NASMStrings.declinePrivacyPolicyAlertDescription,
        [
          {
            text: 'Cancel',
            onPress: () => {},
          },
          {
            text: 'Continue',
            onPress: () => {
              if (this.state.newUser) {
                this.props.navigation.pop(1);
              } else {
                this.props.logout();
                reset(this.props.navigation, 'Welcome', null, null);
              }
            },
          },
        ],
        { cancelable: true },
      );
    }
  };

  createAccount = (user, type) => {
    if (type === 'trainer') {
      this.createTrainerAccountAndContinue(user);
    } else if (type === 'student') {
      this.createStudentAccountAndContinue(user);
    } else if (type === 'user') {
      this.createUserAccountAndContinue(user);
    }
  };

  createStudentAccountAndContinue = (user) => {
    if (!user) {
      return;
    }
    this.setState({ isLoading: true }, () => {
      nasm.api
        .registerTrainer(user)
        .then(() => {
          const { email, password } = user;
          return this.props
            .login({ email, password, version: API_VERSION })
            .then(async (loggedInUser) => {
              if (DEVICE_LOCALE === 'en_US') {
                await nasm.api.setMeasurementSystem('in', 'lb');
              } else {
                await nasm.api.setMeasurementSystem('cm', 'kg');
              }
              // analytics
              analytics().logEvent('sign_up', {
                user_role: 'trainer',
                user_id: loggedInUser.id,
              });
              analytics().setUserProperty('role', 'trainer');
              this.setState({ isLoading: false }, () => {
                reset(this.props.navigation, 'ModalStack', null, {
                  calendarActive: false,
                });
                this.props.navigation.navigate('Account');
                this.props.navigation.navigate('StudentTab');
              });
            });
        })
        .catch((error) => {
          this.setState({ isLoading: false });
          Alert.alert(
            'Error',
            error.message || 'Server error occurred. Please try again later.',
          );
        });
    });
  };

  createTrainerAccountAndContinue = (user) => {
    if (!user) {
      return;
    }
    this.setState({ isLoading: true }, () => {
      nasm.api
        .registerTrainer(user)
        .then(() => {
          const { email, password } = user;
          return this.props
            .login({ email, password, version: API_VERSION })
            .then(async (loggedInUser) => {
              if (DEVICE_LOCALE === 'en_US') {
                await nasm.api.setMeasurementSystem('in', 'lb');
              } else {
                await nasm.api.setMeasurementSystem('cm', 'kg');
              }
              // analytics
              analytics().logEvent('sign_up', {
                user_role: 'trainer',
                user_id: loggedInUser.id,
              });
              analytics().setUserProperty('role', 'trainer');
              this.setState({ isLoading: false }, () => {
                reset(this.props.navigation, 'ModalStack', null, {
                  calendarActive: false,
                });
              });
            });
        })
        .catch((error) => {
          this.setState({ isLoading: false });
          Alert.alert(
            'Error',
            error.message || 'Server error occurred. Please try again later.',
          );
        });
    });
  };

  createUserAccountAndContinue = (user) => {
    if (!user) {
      return;
    }
    this.setState({ isLoading: true }, () => {
      nasm.api
        .registerClient(user)
        .then(() => {
          const { email, password } = user;
          return this.props
            .login({ email, password, version: 'v1.7' })
            .then(async (loggedInUser) => {
              if (DEVICE_LOCALE === 'en_US') {
                await nasm.api.setMeasurementSystem('in', 'lb');
              } else {
                await nasm.api.setMeasurementSystem('cm', 'kg');
              }
              // analytics
              analytics().logEvent('sign_up', {
                user_role: 'walk_in',
                user_id: loggedInUser.id,
              });
              analytics().setUserProperty('role', 'walk_in');

              this.props.selectClient(loggedInUser);
              this.setState({ isLoading: false }, () => {
                reset(this.props.navigation, 'ModalStack', null, {
                  calendarActive: false,
                });
              });
            });
        })
        .catch((error) => {
          this.setState({ isLoading: false });
          Alert.alert(
            'Error',
            error.message || 'Server error occurred. Please try again later.',
          );
        });
    });
  };

  render() {
    return (
      <SafeAreaView style={{ flex: 1, ...androidSafeAreaStyle }}>
        <View style={styles.header}>
          <TouchableOpacity onPress={this.onPressDecline}>
            <Image source={closeBtn} />
          </TouchableOpacity>
          <ScaledText style={styles.title}>Privacy Policy</ScaledText>
        </View>
        <ScrollView contentContainerStyle={styles.privacyContainerStyle}>
          <Markdown>{this.state.privacyPolicy}</Markdown>
        </ScrollView>

        {!this.state.readOnly && (
          <View style={styles.buttonsContainer}>
            <Button
              testID="PrivacyPolicyAcceptButton"
              title="Accept and Continue"
              onPress={this.onPressAccept}
              isLoading={this.state.isLoading}
              containerStyle={{ borderRadius: 27.5 }}
            />
          </View>
        )}
      </SafeAreaView>
    );
  }
}

// styles
const styles = StyleSheet.create({
  header: {
    paddingBottom: 30,
    paddingTop: 20,
    paddingHorizontal: 16,
    borderColor: colors.actionSheetDivider,
    borderBottomWidth: 1,
  },
  title: {
    fontFamily: 'Avenir-Black',
    fontSize: 24,
    textAlign: 'center',
    marginTop: 20,
  },
  buttonsContainer: {
    paddingTop: 25,
    paddingBottom: 35,
    paddingHorizontal: 20,
    borderColor: colors.actionSheetDivider,
    borderTopWidth: 1,
  },
  privacyContainerStyle: {
    paddingHorizontal: 20,
    paddingVertical: 25,
  },
});

// export
PrivacyPolicy.propTypes = propTypes;
const mapStateToProps = () => ({});
const mapDispatchToProps = { logout, login, selectClient };
export default connect(mapStateToProps, mapDispatchToProps)(PrivacyPolicy);
