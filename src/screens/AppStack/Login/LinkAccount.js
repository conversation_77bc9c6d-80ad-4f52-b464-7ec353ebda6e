// Libraries
import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import analytics from '@react-native-firebase/analytics';
import {
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
  Alert,
} from 'react-native';
import { Button, TextInput, PageContainer } from '../../../components';
import { passwordRecoveryUAURL } from '../../../constants';

// Analytics

// Components
import { colors, headerTitleStyle } from '../../../styles';

// Helpers
import { reset, login, deselectDeepLink } from '../../../actions';
import nasm from '../../../dataManager/apiConfig';

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
  }).isRequired,
  deepLink: PropTypes.object,
  deselectDeepLink: PropTypes.func,
};

const defaultProps = {
  loginPreferences: { rememberMe: false },
};

class LinkAccount extends Component {
  static navigationOptions = () => ({
    headerTransparent: true,
    headerStyle: {
      backgroundColor: 'transparent',
      borderBottomWidth: 0,
      elevation: 0,
      height: Platform.OS === 'android' ? StatusBar.currentHeight + 46 : 46,
    },
    headerTintColor: colors.subGrey,
    headerTitleStyle: [headerTitleStyle, { color: colors.subGrey }],
    title: null,
  });

  constructor(props) {
    super(props);
    const email = props.route.params?.email;
    this.state = {
      rememberMe: props.route.params?.rememberMe,
      email,
      password: '',
      validationPassed: false,
      isLoading: false,
      updateRequired: false,
      title: 'Link Account',
      message: 'We found a NASM account',
      showEmail: true,
      resettingPassword: false,
    };
  }

  componentDidMount = () => {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', {
        screen_name: 'ua_auto_link_account',
      });
    });
  };

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressForgotPassword = () => {
    passwordRecoveryUAURL().then((UAURL) => {
      this.props.navigation.navigate('WebView', {
        title: 'NASM.ORG',
        uri: UAURL,
      });
    });
  };

  focusInput = (input) => {
    const target = this.inputRefs[input];
    if (target && target.textInput) target.textInput.focus();
  };

  goToDashboard = () => {
    if (this.props.deepLink) {
      this.props.route.params?.linkTo(this.props.deepLink);
      this.props.deselectDeepLink();
    } else {
      reset(this.props.navigation, 'ModalStack', null, null);
    }
  };

  linkAccount = () => {
    this.setState(
      {
        isLoading: true,
      },
      () => {
        nasm.api
          .linkMatchingAccounts(this.state.email, this.state.password)
          .then((user) => {
            this.props
              .login({ email: this.state.email, password: this.state.password })
              .then(() => {
                this.goToDashboard();
              });

            analytics().logEvent('ua_account_link_success', {
              email: this.state.email,
              ua_guid: user.ua_guid,
              auto_link: true,
            });
          })
          .catch((error) => {
            this.setState(
              { isLoading: false, password: '', validationPassed: false },
              () => {
                try {
                  if (typeof error.response.data.message === 'object') {
                    const errorData = error.response.data.message;
                    if (errorData.description) {
                      this.setState({
                        title: errorData.title,
                        message: errorData.description,
                        oneLineMessage: false,
                      });
                    } else {
                      this.setState({
                        title: errorData.title,
                      });
                    }
                  } else {
                    Alert.alert('Error', error.response.data.message);
                  }
                } catch (e) {
                  Alert.alert('Error', error.message);
                }
              },
            );
          });
      },
    );
  };

  updateInput = (nextState) => {
    this.setState(nextState, this.validateInputs);
  };

  validateInputs = () => {
    const validationPassed = this.state.password.length > 0;
    this.setState({ validationPassed });
    return validationPassed;
  };

  render() {
    const isLinkDisabled = !this.state.validationPassed || this.state.updateRequired;
    return (
      <PageContainer
        containerStyle={styles.pageContainerStyle}
        testID="LoginScreen"
      >
        <View style={{ flex: 1, justifyContent: 'center' }}>
          <View style={{ paddingHorizontal: 20, marginBottom: 30 }}>
            <Text style={styles.title}>{this.state.title}</Text>
            <Text style={styles.email}>{this.state.message}</Text>
            {this.state.showEmail && (
              <Text ellipsizeMode="tail" numberOfLines={1} style={styles.email}>
                {this.state.email}
              </Text>
            )}
          </View>

          <TextInput
            value={this.state.password}
            onChangeText={(password) => this.updateInput({ password })}
            placeholder="Password"
            placeholderTextColor={colors.white}
            secureTextEntry
            showIcon={false}
            returnKeyType="done"
            validation={(value) => !!value}
            containerStyle={styles.inputContainerStyle}
            inputText={styles.inputTextStyle}
            selectionColor={colors.white}
            testID="PasswordTextInput"
          />
          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={this.onPressForgotPassword}
          >
            <Text
              style={[
                styles.forgotPasswordText,
                this.state.resettingPassword && { color: 'transparent' },
              ]}
            >
              Forgot Password
            </Text>
          </TouchableOpacity>
        </View>

        <View>
          <View style={styles.loginButtonContainer}>
            <Button
              title="Link"
              onPress={this.linkAccount}
              disabled={isLinkDisabled}
              isLoading={this.state.isLoading}
              testID="SubmitLoginButton"
              containerStyle={[
                styles.loginButton,
                {
                  backgroundColor: isLinkDisabled
                    ? 'rgba(37, 146, 236, 0.25)'
                    : colors.azure,
                },
              ]}
            />
          </View>
        </View>
      </PageContainer>
    );
  }
}

// Styles
const styles = StyleSheet.create({
  pageContainerStyle: {
    backgroundColor: colors.duskBlue,
  },
  inputContainerStyle: {
    borderColor: colors.white,
    marginHorizontal: 20,
    paddingHorizontal: 0,
    marginBottom: 30,
  },
  inputTextStyle: {
    color: colors.white,
  },
  forgotPasswordContainer: {
    alignSelf: 'flex-end',
    paddingVertical: 10,
    padding: 20,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: colors.white,
    letterSpacing: 0,
    fontFamily: 'Avenir-Roman',
  },
  loginButtonContainer: {
    left: 0,
    right: 0,
    marginHorizontal: 20,
    marginTop: 43,
    marginBottom: 35,
  },
  loginButton: {
    borderRadius: 27.5,
    borderWidth: 0,
  },
  title: {
    fontFamily: 'Avenir',
    fontSize: 24,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.white,
    marginBottom: 20,
  },
  email: {
    fontFamily: 'Avenir',
    fontSize: 18,
    lineHeight: 31,
    textAlign: 'center',
    color: colors.white,
  },
});

// Export
LinkAccount.propTypes = propTypes;
LinkAccount.defaultProps = defaultProps;

const mapStateToProps = ({ loginPreferences, User }) => ({
  loginPreferences,
  User,
});
const mapDispatchToProps = { login, deselectDeepLink };

export default connect(mapStateToProps, mapDispatchToProps)(LinkAccount);
