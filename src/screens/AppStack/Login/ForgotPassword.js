import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import {
  Alert,
  Dimensions,
  StatusBar,
  StyleSheet,
  Text,
  View,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Button, PageContainer, TextInput } from '../../../components';

// Styles
import { colors, header } from '../../../styles';

// Helpers
import * as validate from '../../../util/validate';
import nasm from '../../../dataManager/apiConfig';

// Props
const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
  }).isRequired,
};

// Class
class ForgotPassword extends Component {
  static navigationOptions = {
    title: 'Reset Password',
    ...header.default,
  };

  constructor(props) {
    super(props);
    this.inputRefs = {};
    this.state = {
      email: '',
      enableResetButton: false,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'reset_password' });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  handleSubmit = async () => {
    this.setState(
      {
        enableResetButton: false,
      },
      async () => {
        try {
          await nasm.api.createPasswordResetRequest(this.state.email);
          Alert.alert(
            'Password Reset',
            'Check your email for further instructions',
            [{ text: 'OK', onPress: () => this.props.navigation.goBack(null) }],
            { cancelable: false },
          );
        } catch (error) {
          this.setState(
            {
              enableResetButton: true,
            },
            () => {
              let message = 'Failed to reset password. Please try again.';
              if (error.message) {
                if (error.message === 'Network Error') {
                  Alert.alert(
                    'Network Error',
                    'Please check your internet connection and try again.',
                  );
                  return;
                }
                message = error.message;
              }
              Alert.alert('Error', message);
            },
          );
        }
      },
    );
  };

  updateInput = (nextState) => {
    this.setState(nextState, this.validateInputs);
  };

  validateInputs = () => {
    const fields = Object.keys(this.inputRefs)
      .filter((key) => !!this.inputRefs[key])
      .map((key) => {
        const { validation, value } = this.inputRefs[key].props;
        return validation(value);
      });
    const enableResetButton = !fields.map((field) => !!field).includes(false);
    this.setState({ enableResetButton });
    return enableResetButton;
  };

  render() {
    return (
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : null}
      >
        <PageContainer>
          <View style={styles.textContainer}>
            <Text style={styles.text}>
              Provide your email and we’ll send you a link to reset your
              password.
            </Text>
            <TextInput
              ref={(ref) => { this.inputRefs.email = ref; }}
              value={this.state.email}
              onChangeText={(email) => this.updateInput({ email })}
              placeholder="Enter Email"
              keyboardType="email-address"
              returnKeyType="done"
              validation={validate.email}
            />
          </View>
          <View style={styles.buttonContainer}>
            <Button
              title="Reset Password"
              onPress={this.handleSubmit}
              disabled={!this.state.enableResetButton}
              variant="yellow"
              containerStyle={styles.resetPasswordButton}
            />
          </View>
        </PageContainer>
      </KeyboardAvoidingView>
    );
  }
}

// Export
ForgotPassword.propTypes = propTypes;
export default ForgotPassword;

const scale = Dimensions.get('window').width / 400;

// Styles
const styles = StyleSheet.create({
  textContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 22 * scale,
    marginHorizontal: '6%',
  },
  text: {
    width: 260 * scale,
    fontFamily: 'Avenir-Book',
    fontSize: 15 * scale,
    lineHeight: 21.0 * scale,
    textAlign: 'center',
    color: colors.subGrey,
  },
  buttonContainer: {
    flex: 1,
    marginVertical: 22 * scale,
    marginHorizontal: '6%',
  },
  resetPasswordButton: {
    borderRadius: 27.5 * scale,
  },
});
