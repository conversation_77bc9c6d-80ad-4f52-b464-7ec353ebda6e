import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  Dimensions,
  Image,
  StatusBar,
  TouchableOpacity,
  View,
  NativeModules,
  Platform,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import { PageContainer, SignUpForm, ScaledText } from '../../../components';
import { login, selectClient } from '../../../actions';
import { scaleWidth, scaleHeight } from '../../../util/responsive';

// Styles
import { colors } from '../../../styles';

const { AppKeyboardHandlerMethods } = NativeModules;

const DeviceInfo = require('react-native-device-info');

const image = require('../../../resources/imgWuwoSub.png');
const cornerX = require('../../../resources/imgCircleClose.png');

class RegisterClient extends Component {
  static navigationOptions = () => ({
    headerLeft: null,
    headerTransparent: true,
    headerStyle: {
      height: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.25)',
      borderBottomWidth: 0,
    },
  });

  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'signup_client' });
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustResize();
      }
    });
    this.unsubscribeBlur = this.props.navigation.addListener('blur', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustPan();
      }
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  render() {
    const type = this.props.route.params?.type;
    return (
      <PageContainer
        testID="AllUserRegister"
        scrollEnabled
        containerStyle={styles.pageContainer}
      >
        <Image style={styles.heroImage} source={image} />

        {/* Content Container */}
        <View style={styles.contentContainer}>
          <ScaledText style={styles.workoutTodayText}>
            Create Account
          </ScaledText>
          <View
            style={{
              justifyContent: 'center',
              marginTop: DeviceInfo.isTablet()
                ? scaleHeight(22)
                : scaleHeight(28),
              marginHorizontal: scaleWidth(2.5),
            }}
          >
            <SignUpForm
              isLoading={this.state.isLoading}
              onPressCreateAccount={(user) => {
                this.props.navigation.navigate('TermsAndConditions', {
                  newUser: user,
                  newUserType: type,
                });
              }}
              initialEmail={this.props.route.params?.email}
              navigation={this.props.navigation}
            />
          </View>
        </View>

        <TouchableOpacity
          style={styles.closeImageContainer}
          onPress={() => this.props.navigation.goBack()}
        >
          <Image source={cornerX} />
        </TouchableOpacity>

        {this.state.isLoading && <View style={styles.loadingBlocker} />}
      </PageContainer>
    );
  }
}

const { width } = Dimensions.get('window');

const styles = {
  pageContainer: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  contentContainer: {
    marginTop: -180,
  },
  heroImage: {
    width,
    height: scaleHeight(35),
  },
  closeImageContainer: {
    position: 'absolute',
    left: scaleWidth(4),
    top: '5%',
  },
  workoutTodayText: {
    position: 'absolute',
    fontFamily: 'Avenir-Black',
    fontWeight: 'bold',
    color: colors.white,
    fontSize: 28,
    top: DeviceInfo.isTablet() ? scaleHeight(4) : scaleHeight(12),
    marginLeft: scaleWidth(5),
  },
  loadingBlocker: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
};

const mapStateToProps = () => ({});
const mapDispatchToProps = { login, selectClient };
export default connect(mapStateToProps, mapDispatchToProps)(RegisterClient);
