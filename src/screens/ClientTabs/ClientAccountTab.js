import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import IconFeader from 'react-native-vector-icons/Feather';
import {
  Text,
  View,
  Image,
  FlatList,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { CommonActions } from '@react-navigation/native';
import analytics from '@react-native-firebase/analytics';
import ScaledText from '../../components/ScaledText';
import { APPLICATION_CLIENT_ROUTES } from '../../constants';
import nasm from '../../dataManager/apiConfig';
import { NASMStrings } from '../../strings';

// Analytics
import { logout, updateCurrentUser } from '../../actions';
import { curvedScale } from '../../util/responsive';

// Styles
import { colors } from '../../styles';
import { getUserDataAction } from '../../actions/artichoke/User.actions';
import {
  CALCULATOR_CONTEXTS,
  setCalculatorContext,
} from '../../reducers/calculatorContextReducer';
import RookUtils from './Rook/RookUtils';

const defaultProfile = require('../../resources/defaultProfile.png');
const avatarNutritionLogo = require('../../resources/anLogo.png');
const avatarNutritionLogoDisabled = require('../../resources/anLogoDisabled.png');
const healthServicesnLogo = require('../../resources/health-logo.png');
const infoIcon = require('../../assets/tooltip.png');

class ClientAccountTab extends Component {
  constructor(props) {
    super(props);
    this.rookUtilsRef = createRef();
    this.state = {
      trainerData: {},
      avatarConnected: this.props.currentUser.avatar_connected,
      defaultSections: [
        {
          title: 'Payment Method',
          destination: APPLICATION_CLIENT_ROUTES.ADD_CREDIT_CARD,
          isPro: true,
        },
        {
          title: 'Health Data',
          decorator: this.renderHealthServices,
          destination: 'RookConnect',
          isPro: true,
        },
        {
          title: 'Nutrition',
          decorator: this.renderAvatarNutrition,
          destination: 'ConnectAvatarNutrition',
        },
        {
          title: 'Measurement Units',
          destination: 'MeasurementSystem',
        },
        {
          title: 'Subscriptions',
          destination: APPLICATION_CLIENT_ROUTES.CLIENT_SUBSCRIPTIONS,
          isPro: true,
        },
        {
          title: 'Calculators',
          destination: 'Calculators',
        },
        {
          title: 'Terms of Use',
          destination: 'TermsAndConditions',
          params: { readOnly: true },
        },
        {
          title: 'Privacy Policy',
          destination: 'PrivacyPolicy',
          params: { readOnly: true },
        },
        { title: 'Help', destination: 'Resources' },
      ],
    };
    this.renderRow = this.renderRow.bind(this);
  }

  componentDidMount() {
    this.getTrainerInfo();
    this.props.getUserDataAction();
    this.unsubscribeFocus = this.props.navigation.addListener(
      'focus',
      async () => {
        StatusBar.setBarStyle('light-content');
        analytics().logEvent('screen_view', {
          screen_name: 'client_information',
        });
        nasm.api.getMyUser(this.props.currentUser.id).then((user) => {
          this.props.updateCurrentUser(user);
          let { defaultSections } = this.state;
          const hasTrainerPro = user.client_user.trainer?.bypass_subscription
            || user.client_user.trainer?.subscription === 'edge_trainer_pro';
          if (!hasTrainerPro) {
            defaultSections = defaultSections.filter((item) => !item.isPro);
          }

          this.setState({
            avatarConnected: user.avatar_connected,
            defaultSections,
          });
        });
      },
    );
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressLogout = async () => {
    if (this.rookUtilsRef.current) {
      await this.rookUtilsRef.current.clearUser();
    }
    this.props.logout();
    this.props.navigation.dispatch(
      CommonActions.reset({
        routes: [{ name: 'Welcome' }],
      }),
    );
  };

  getTrainerInfo = async () => {
    let trainer = {};
    if (this.props.currentUser.client_user.trainer) {
      trainer = await nasm.api.getUserById(
        this.props.currentUser.client_user.trainer.user_id,
      );
    }
    this.setState({ trainerData: trainer });
  };

  renderRow({ item }) {
    if (
      item.title === 'Payment Method'
      && this.props.user.details.account?.StripeAccount?.stripeSecretKey === null
    ) {
      return null;
    }
    if (
      item.title === 'Nutrition'
      && this.props?.currentUser?.client_user?.club_id
    ) {
      return null;
    }
    return (
      <TouchableOpacity
        style={styles.sectionView}
        onPress={() => {
          if (!item.destination) return;
          if (item.destination === 'Calculators') {
            this.props.setCalculatorContext(CALCULATOR_CONTEXTS.CLIENT);
          }
          this.props.navigation.navigate(item.destination, item.params);
        }}
      >
        <Text style={styles.sectionTitle}>{item.title}</Text>
        {item.decorator && (
          <View style={{ alignSelf: 'flex-end' }}>{item.decorator()}</View>
        )}
        <IconFeader
          style={{ alignSelf: 'flex-end' }}
          name="chevron-right"
          size={27}
          color={colors.fillDarkGrey}
        />
      </TouchableOpacity>
    );
  }

  renderListHeader = () => {
    const profileImage = this.props.currentUser.avatar_url
      ? { uri: this.props.currentUser.avatar_url }
      : defaultProfile;
    return (
      <View style={styles.profileInfo}>
        <Image style={styles.profileImage} source={profileImage} />
        <ScaledText style={styles.profileNameText}>
          {`${this.props.currentUser.first_name} ${this.props.currentUser.last_name}`}
        </ScaledText>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => this.props.navigation.navigate({
              name: 'EditProfile',
              params: {
                role: this.props.currentUser.role,
              },
              merge: true,
            })}
          >
            <Text style={styles.buttonText}>Edit</Text>
          </TouchableOpacity>
          <View style={styles.button}>
            <TouchableOpacity onPress={this.onPressLogout}>
              <Text style={styles.buttonText}>Logout</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  renderHealthServices = () => (
    <View style={styles.containerRow}>
      <Image
        source={healthServicesnLogo}
        style={styles.decoratorLogoContainer}
      />
      <Text
        style={[{ color: colors.fillDarkGrey }, styles.decoratorTextContainer]}
      >
        Connect services
      </Text>
      <TouchableOpacity
        style={{ paddingHorizontal: 10, justifyContent: 'center' }}
        hitSlop={20}
        onPress={() => this.props.navigation.navigate('RookFTUE', {
          userId: this.props.currentUser.id,
          userRole: this.props.currentUser.role,
        })}
      >
        <Image source={infoIcon} style={{ width: 20, height: 20 }} />
      </TouchableOpacity>
    </View>
  );

  renderAvatarNutrition = () => {
    const logo = this.state.avatarConnected
      ? avatarNutritionLogo
      : avatarNutritionLogoDisabled;
    const text = this.state.avatarConnected
      ? NASMStrings.connectedToAvatar
      : NASMStrings.connectWithAvatar;
    const textColor = this.state.avatarConnected
      ? colors.goodGreen
      : colors.fillDarkGrey;

    return (
      <View style={styles.containerRow}>
        <Image source={logo} style={styles.decoratorLogoContainer} />
        <Text style={[{ color: textColor }, styles.decoratorTextContainer]}>
          {text}
        </Text>
      </View>
    );
  };

  render() {
    return (
      <View style={styles.container}>
        <ScrollView style={styles.sectionsContainer}>
          <RookUtils ref={this.rookUtilsRef} />
          {this.renderListHeader()}
          <FlatList
            data={this.state.defaultSections}
            renderItem={this.renderRow}
            scrollEnabled={false}
          />
        </ScrollView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
  profileInfo: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 24,
  },
  profileImage: {
    height: curvedScale(80),
    width: curvedScale(80),
    borderRadius: curvedScale(40),
    backgroundColor: colors.duskBlue,
  },
  profileNameText: {
    fontSize: curvedScale(17),
    fontFamily: 'Avenir-Medium',
    color: colors.black,
    marginVertical: curvedScale(10),
  },
  buttonContainer: {
    flexDirection: 'row',
  },
  button: {
    width: curvedScale(94),
    height: curvedScale(35),
    borderRadius: curvedScale(17.5),
    borderWidth: 2,
    borderColor: colors.fillDarkGrey,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: curvedScale(12),
  },
  buttonText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(14),
    color: colors.fillDarkGrey,
  },
  decoratorLogoContainer: {
    width: 20,
    height: 20,
    alignSelf: 'center',
    marginVertical: 0,
    marginHorizontal: 5,
  },
  decoratorTextContainer: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    alignSelf: 'center',
    marginLeft: 2,
  },
  sectionsContainer: {
    flex: 1,
  },
  sectionView: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    borderColor: colors.bordergrey,
    borderBottomWidth: 1,
    padding: 20,
    alignItems: 'center',
  },
  sectionTitle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    color: colors.black,
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  selectedClient: state.selectedClient,
  user: state.user,
});
const mapDispatchToProps = {
  logout,
  getUserDataAction,
  updateCurrentUser,
  setCalculatorContext,
};
export default connect(mapStateToProps, mapDispatchToProps)(ClientAccountTab);
