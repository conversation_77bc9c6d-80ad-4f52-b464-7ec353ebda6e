import React, { useEffect, useState } from 'react';
import {
  Alert, Platform, StyleSheet, View,
} from 'react-native';
import {
  useRookConfiguration,
  useRookPermissions,
  useRookSummaries,
  useRookBackGround,
} from 'react-native-rook-sdk-apple-health';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import moment from 'moment';
import nasm from '../../../dataManager/apiConfig';
import { colors } from '../../../styles';
import RookAPI from '../../../api/rook/RookConstants';
import RookConnectionPage from './RookConnectionPage';

const propTypes = {
  navigation: PropTypes.any.isRequired,
};

const defaultProps = {};

const APPLE_HEALTH_ICON = require('../../../resources/AppleHealthIcon.png');

const stateSelector = (state) => state;

const RookConnect = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [disableConnectBtn, setDisableConnectBtn] = useState(false);
  const [dataSources, setDataSources] = useState(Array.prototype);
  const [showAwesomeAlert, setShowAwesomeAlert] = useState(false);
  const [showConnectionSuccessAlert, setShowConnectionSuccessAlert] = useState(false);
  const [selectedSource, setSelectedSource] = useState({});
  const { currentUser } = useSelector(stateSelector);

  const { ready, requestAllPermissions } = useRookPermissions();
  const {
    getUserID, clearUserID, updateUserID, enableSyncYesterday,
  } = useRookConfiguration();
  const {
    syncSummaries,
    reSyncFailedSummaries,
    syncPhysicalSummary,
    syncSleepSummary,
    syncBodySummary,
  } = useRookSummaries();

  const { enableBackgroundForSummaries } = useRookBackGround();

  const yesterdayDate = moment().subtract(1, 'day').format('YYYY-MM-DD');
  const rookApi = new RookAPI();

  useEffect(() => {
    navigation.setOptions({
      title: 'Health Data',
    });
    const unsubscribeFocus = navigation.addListener('focus', () => {
      if (ready) {
        checkUserAndFetchData();
      }
    });
    return unsubscribeFocus;
  }, [ready, navigation, selectedSource]);

  const checkUserAndFetchData = async () => {
    await fetchDataSources();
  };

  const clearRookUser = () => {
    clearUserID().catch((error) => {
      showAlert(error?.message ?? error);
    });
  };

  const updateRookUser = (userId) => {
    setLoading(true);
    updateUserID(userId)
      .then(async () => {
        await handlePermissions();
      })
      .catch((error) => {
        showAlert(error?.message ?? error);
      })
      .finally(() => {
        enableSyncYesterday().catch((error) => error);
      });
  };

  const handlePermissions = async () => {
    await requestAllPermissions()
      .then(async (res) => {
        if (!res) {
          showAlert('Permission was not granted');
          return;
        }
        fetchDataSources();
      })
      .catch((error) => {
        showAlert(error?.message ?? error);
      });
  };

  const startDataSyncing = async () => {
    const syncSummariesResponse = await syncSummaries();
    await enableBackgroundForSummaries().catch((error) => error);

    await reSyncFailedSummaries().catch((error) => error);

    if (!syncSummariesResponse) {
      await handleSleepSummarySync();
      await handlePhysicalSummarySync();
      await handleBodySummarySync();
    }
  };

  const handleSleepSummarySync = async () => {
    await syncSleepSummary(yesterdayDate).catch((error) => showAlert(error?.message ?? error));
  };

  const handlePhysicalSummarySync = async () => {
    await syncPhysicalSummary(yesterdayDate).catch((error) => showAlert(error?.message ?? error));
  };

  const handleBodySummarySync = async () => {
    await syncBodySummary(yesterdayDate).catch((error) => showAlert(error?.message ?? error));
  };

  const fetchDataSources = async () => {
    try {
      setLoading(true);
      setDisableConnectBtn(false);
      const display_sources = [];
      let userId = null;
      getUserID()
        .then(async (res) => {
          if (res) {
            userId = res;
          }
        })
        .catch(() => {
          userId = null;
        });
      const source_connection_status = await rookApi.getConnectionStatus(
        currentUser?.id,
      );
      if (Platform.OS === 'ios') {
        display_sources.push({
          name: 'Apple Health',
          description: '',
          icon: APPLE_HEALTH_ICON,
          connected:
            (source_connection_status?.apple_health && userId) ?? false,
          authorization_url: null,
        });
      }
      const data_sources = await rookApi.getDataSources(currentUser?.id);
      if (!data_sources || !data_sources?.length) {
        Alert.alert(
          'Error',
          'We hit a snag while fetching data sources. Please try again later',
          [
            {
              text: 'Ok',
              onPress: () => navigation.goBack(),
            },
          ],
        );
        return;
      }
      data_sources.map((source) => display_sources.push(source));
      const findSource = display_sources?.length
        && display_sources?.find((src) => src.name === selectedSource?.name);
      if (findSource) {
        if (findSource?.connected !== selectedSource?.connected) {
          const jsonData = {
            data_source: findSource?.name?.replace(' ', '_')?.toUpperCase(),
            is_connected: findSource?.connected,
          };
          await nasm.api.toggleRookDataSource(jsonData).then(() => {
            setShowConnectionSuccessAlert(!!findSource?.connected);
          });
        }
      }
      const isAnySourceConnected = display_sources?.length && display_sources?.some((src) => src.connected);
      if (isAnySourceConnected) {
        setDisableConnectBtn(true);
      }
      setDataSources(display_sources);
      if (userId) {
        startDataSyncing();
      }
    } catch (error) {
      showAlert(error?.message ?? error);
    } finally {
      setLoading(false);
    }
  };

  const openConnectionPage = (item) => {
    navigation.navigate('WebView', {
      uri: item.authorization_url,
      title: 'Health Data',
    });
  };

  const revokeAuthFromRook = async () => {
    setLoading(true);
    const revokeResponse = await rookApi.disconnectSource(
      currentUser?.id,
      selectedSource?.name,
    );
    if (revokeResponse) {
      if (selectedSource.name === 'Apple Health') {
        clearRookUser();
      }
      /**
       * @requires
       * This arbitrary delay is the amount of time that Rook is taking
       * to update the status of a data source.
       */
      setTimeout(() => {
        fetchDataSources();
      }, 500);
    } else {
      setLoading(false);
      Alert.alert('Error', 'Something went wrong! Please try again later.');
    }
  };

  const showAlert = (message) => {
    Alert.alert(
      'Error',
      message ?? 'Something went wrong! Please try again later.',
    );
  };

  const onPressSource = (item) => {
    setSelectedSource({
      name: item.name,
      connected: item.connected,
    });
    if (!item.connected && !disableConnectBtn) {
      if (item.name === 'Apple Health') {
        updateRookUser(currentUser?.id);
      } else {
        openConnectionPage(item);
      }
    } else if (item.connected) {
      setShowAwesomeAlert(true);
    }
  };

  return (
    <View style={styles.container}>
      <RookConnectionPage
        loading={loading}
        dataSources={dataSources}
        selectedSource={selectedSource}
        onPressSource={onPressSource}
        disableConnectBtn={disableConnectBtn}
        showConnectionSuccessAlert={showConnectionSuccessAlert}
        setShowConnectionSuccessAlert={setShowConnectionSuccessAlert}
        showDisconnectAlert={showAwesomeAlert}
        setShowDisconnectAlert={setShowAwesomeAlert}
        revokeAuthFromRook={revokeAuthFromRook}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});

RookConnect.propTypes = propTypes;
RookConnect.defaultProps = defaultProps;

export default RookConnect;
