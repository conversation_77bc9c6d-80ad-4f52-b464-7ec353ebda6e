import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  Text,
  View,
  Image,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  RefreshControl,
  LayoutAnimation,
  ActivityIndicator,
} from 'react-native';
import moment from 'moment';
import PropTypes from 'prop-types';
import AwesomeAlert from 'react-native-awesome-alerts';
import { CalendarWithToggle } from '../../../../components';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';
import { androidSafeLayoutAnimation } from '../../../../constants';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import nasm from '../../../../dataManager/apiConfig';

const stateSelector = (state) => state;
const DATE_FORMAT = 'YYYY-MM-DD';

const alertIcon = require('../../../../assets/alert-triangle.png');

const PhysicalHealth = ({ navigation }) => {
  const [state, setState] = useState({
    weekView: true,
    calendarLoading: false,
    selectedDay: moment().subtract(1, 'd').format(DATE_FORMAT),
  });

  useEffect(() => {
    navigation.setOptions({
      title: 'Health Data',
      headerLeft: () => (
        <HeaderLeftButton onPress={() => navigation.goBack()} />
      ),
      headerRight: () => <View />,
    });
  }, [navigation]);

  const setValues = (key, value) => {
    setState((prevState) => ({
      ...prevState,
      [key]: value,
    }));
  };

  const setSelectedDate = (day) => {
    setValues('selectedDay', moment(day).format(DATE_FORMAT));
  };

  const onRefresh = () => {
    setValues('calendarLoading', true);
  };

  const hideLoading = () => {
    setValues('calendarLoading', false);
  };

  const onToggleCalendar = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    setState({ ...state, weekView: !state.weekView });
  };

  const getMarkedDates = () => {
    const markedDates = {
      [state.selectedDay]: {
        marked: true,
        selected: true,
        dark: true,
        selectedColor: 'black',
      },
    };
    return markedDates;
  };

  return (
    <SafeAreaView style={styles.parentView}>
      <ScrollView
        contentContainerStyle={styles.container}
        testID="PhysicalHealth"
        refreshControl={(
          <RefreshControl
            refreshing={state.calendarLoading}
            onRefresh={onRefresh}
          />
        )}
      >
        <View style={{ backgroundColor: colors.white }}>
          <CalendarWithToggle
            weekView={state.weekView}
            isLoading={false}
            selectedDay={state.selectedDay}
            onToggle={onToggleCalendar}
            markedDates={getMarkedDates()}
            onDateSelected={setSelectedDate}
            maxDate={new Date()}
            isDateDisablingEnabled
          />
        </View>
        <PageComp
          selectedDay={state.selectedDay}
          calendarLoading={state.calendarLoading}
          onDataFetched={hideLoading}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const propTypes = {
  selectedDay: PropTypes.any,
  calendarLoading: PropTypes.bool,
  onDataFetched: PropTypes.func,
};

const defaultProps = {
  selectedDay: moment().format(DATE_FORMAT),
  calendarLoading: false,
  onDataFetched: () => {},
};

const PageView = (props) => {
  const { currentUser, selectedClient } = useSelector(stateSelector);
  const [state, setState] = useState({
    pageIsLoading: false,
    physicalSummary: {},
    sleepSummary: {},
    showNoDataAlert: false,
  });

  const defaultValue = 'N/A';
  const client_id = selectedClient?.id ?? currentUser?.id;

  useEffect(() => {
    getPhysicalSummary();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.selectedDay, props.calendarLoading, client_id]);

  const setValues = (key, value) => {
    setState((prevState) => ({
      ...prevState,
      [key]: value,
    }));
  };

  const showAlert = () => {
    setValues('showNoDataAlert', true);
  };

  const hideAlert = () => {
    setValues('showNoDataAlert', false);
  };

  const getPhysicalSummary = async () => {
    nasm.api
      .getDateWiseHealthSummary(client_id, props.selectedDay)
      .then((res) => {
        if (
          res?.event_data?.physical_summary
          || res?.event_data?.sleep_summary
        ) {
          if (res?.event_data?.physical_summary) {
            setValues(
              'physicalSummary',
              res?.event_data?.physical_summary || {},
            );
          } else {
            setValues('physicalSummary', {});
          }
          if (res?.event_data?.sleep_summary) {
            setValues('sleepSummary', res?.event_data?.sleep_summary || {});
          } else {
            setValues('sleepSummary', {});
          }
        } else {
          showAlert();
          setValues('physicalSummary', {});
          setValues('sleepSummary', {});
        }
      })
      .catch((err) => {
        showAlert(err?.message ?? err);
      })
      .finally(() => props.onDataFetched());
  };

  const getDurationFromSeconds = (seconds) => {
    if (!seconds) {
      return defaultValue;
    }
    if (seconds > 0 && seconds < 60) {
      const duration = `${seconds} sec`;
      return duration;
    }
    if (seconds >= 60 && seconds < 3600) {
      const minutes = Math.floor(
        moment.duration(seconds, 'seconds').asMinutes(),
      );
      const remainingSeconds = seconds - minutes * 60;
      const formattedSeconds = remainingSeconds > 0 ? ` ${remainingSeconds} sec` : '';
      let duration = `${minutes} min`;
      if (formattedSeconds?.length) {
        duration += formattedSeconds;
      }
      return duration;
    }
    const hours = Math.floor(moment.duration(seconds, 'seconds').asHours());
    const minutes = Math.floor(moment.duration(seconds, 'seconds').asMinutes());
    const remainingMinutes = minutes - hours * 60;
    const formattedMinutes = remainingMinutes > 0 ? ` ${remainingMinutes}min` : '';
    // alert(minutes);
    let duration = `${hours}hr`;
    if (formattedMinutes?.length) {
      duration += formattedMinutes;
    }
    return duration;
  };

  const renderSectionTitle = (title) => (
    <Text style={styles.sectionLabel}>{title}</Text>
  );

  const renderSectionValues = (label, value, unit) => (
    <View style={styles.contentView}>
      <Text style={styles.contentText}>{`${label}:`}</Text>
      <View style={styles.valueView}>
        <Text style={styles.contentText}>{value}</Text>
        {unit ? <Text style={styles.contentText}>{unit}</Text> : null}
      </View>
    </View>
  );

  const roundOffValue = (value) => {
    if (!value) {
      return defaultValue;
    }
    return Math.round(value);
  };

  const renderPhysicalData = () => (
    <View style={styles.dataView}>
      {renderSectionTitle('Physical Activity')}
      {renderSectionValues(
        'Steps',
        roundOffValue(state.physicalSummary?.distance?.steps_int),
      )}
      {renderSectionValues(
        'Total Calories',
        roundOffValue(
          state.physicalSummary?.calories
            ?.calories_basal_metabolic_rate_kcal_float
            ?? state.physicalSummary?.calories?.calories_expenditure_kcal_float,
        ),
      )}
      {renderSectionValues(
        'Workout Duration',
        state.physicalSummary?.activity
          ? getDurationFromSeconds(
            state.physicalSummary?.activity?.active_seconds_int,
          )
          : defaultValue,
      )}
    </View>
  );

  const renderSleepData = () => (
    <View style={styles.dataView}>
      {renderSectionTitle('Sleep Activity')}
      {renderSectionValues(
        'Time in bed',
        state.sleepSummary?.duration?.time_in_bed_seconds_int
          ? getDurationFromSeconds(
            state.sleepSummary?.duration?.time_in_bed_seconds_int,
          )
          : defaultValue,
        state.sleepSummary?.time_in_bed_seconds_int ? 'sec' : null,
      )}
    </View>
  );

  const renderReadinessData = () => (
    <View style={styles.dataView}>
      {renderSectionTitle('Heart Rate')}
      {renderSectionValues(
        'HR min',
        state.physicalSummary?.heart_rate
          ? state.physicalSummary?.heart_rate?.hr_minimum_bpm_int
              ?? defaultValue
          : defaultValue,
        state.physicalSummary?.heart_rate?.hr_minimum_bpm_int ? 'bpm' : null,
      )}
      {renderSectionValues(
        'HR avg',
        state.physicalSummary?.heart_rate
          ? state.physicalSummary?.heart_rate?.hr_avg_bpm_int ?? defaultValue
          : defaultValue,
        state.physicalSummary?.heart_rate?.hr_avg_bpm_int ? 'bpm' : null,
      )}
    </View>
  );

  const getDayText = () => {
    const selectedDay = moment(props.selectedDay);
    let dayText = '';

    if (selectedDay.isSame(moment(), 'day')) {
      dayText += 'Today, ';
    } else {
      dayText += `${selectedDay.format('dddd')}, `;
    }

    dayText += selectedDay.format('MMMM D');

    return dayText;
  };

  if (!state.pageIsLoading) {
    const selectedDay = moment(props.selectedDay);
    const isToday = selectedDay.isSame(moment(), 'day');
    const alertTitle = isToday ? 'Syncing...' : 'No Data Found';
    const alertMessage = isToday
      ? "Your today's data is currently getting synced. It will take 24 hours to sync current day's data"
      : 'Your data is not available for this day';
    return (
      <>
        <Text style={styles.dayText}>{getDayText()}</Text>
        {renderPhysicalData()}
        {renderSleepData()}
        {renderReadinessData()}
        <AwesomeAlert
          show={state.showNoDataAlert}
          showProgress={false}
          useNativeDriver
          closeOnTouchOutside
          closeOnHardwareBackPress
          showConfirmButton
          showCancelButton={false}
          customView={(
            <View style={styles.alertContainer}>
              <Image source={alertIcon} />
              <Text style={styles.alertTitle}>{alertTitle}</Text>
              <Text style={styles.alertText}>{alertMessage}</Text>
            </View>
          )}
          confirmText="Got It"
          confirmButtonTextStyle={styles.alertButtonText}
          confirmButtonStyle={styles.alertButton}
          actionContainerStyle={styles.actionContainerStyle}
          contentContainerStyle={styles.alertContainer}
          onConfirmPressed={() => {
            hideAlert();
          }}
          onCancelPressed={() => {
            hideAlert();
          }}
        />
      </>
    );
  }
  return (
    <View style={styles.loadingView}>
      <ActivityIndicator animating size="large" />
    </View>
  );
};

PageView.propTypes = propTypes;
PageView.defaultProps = defaultProps;

const PageComp = React.memo(PageView);

const styles = StyleSheet.create({
  parentView: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flexGrow: 1,
    paddingHorizontal: curvedScale(10),
  },
  dataView: {
    paddingHorizontal: curvedScale(20),
    paddingVertical: curvedScale(10),
  },
  contentView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderColor: colors.gray_1,
    borderWidth: 1.5,
    borderBottomWidth: 5,
    borderRadius: 6,
    padding: curvedScale(10),
    paddingHorizontal: curvedScale(15),
    marginBottom: curvedScale(8),
  },
  contentText: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir',
    fontWeight: '400',
  },
  valueView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: curvedScale(3),
  },
  sectionLabel: {
    color: colors.black,
    fontSize: curvedScale(17),
    fontFamily: 'Avenir-Heavy',
    fontWeight: '800',
    marginVertical: curvedScale(10),
  },
  dayText: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(24),
    lineHeight: 32.78,
    color: colors.black,
    margin: curvedScale(10),
  },
  actionContainerStyle: {
    flexDirection: 'column',
    backgroundColor: colors.white,
  },
  alertContainer: {
    borderRadius: curvedScale(20),
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: curvedScale(120),
    width: curvedScale(270),
    backgroundColor: colors.white,
  },
  alertTitle: {
    fontSize: curvedScale(20),
    fontFamily: 'Avenir-Black',
    textAlign: 'center',
    color: colors.black,
    marginVertical: curvedScale(20),
  },
  alertText: {
    fontSize: curvedScale(14),
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    textAlign: 'center',
    paddingHorizontal: curvedScale(20),
  },
  alertButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.buttonBlue,
    borderColor: colors.gray_1,
    borderWidth: 1.5,
    borderBottomWidth: 3,
    borderRadius: curvedScale(20),
    minWidth: curvedScale(100),
    minHeight: curvedScale(40),
  },
  alertButtonText: {
    fontSize: curvedScale(14),
    color: colors.white,
    fontFamily: 'Avenir-Black',
    textAlign: 'center',
  },
});

export default PhysicalHealth;
