import React, { useRef, useState } from 'react';
import {
  Text,
  View,
  Image,
  SafeAreaView,
  TouchableOpacity,
  Dimensions,
  StyleSheet,
} from 'react-native';
import Carousel from 'react-native-snap-carousel';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { androidSafeAreaStyle, colors } from '../../../styles';
import { ROLES } from '../../../constants';

const closeBtn = require('../../../resources/closeCircle.png');
const rookFTUImg = require('../../../assets/rookftu.png');

const { width } = Dimensions.get('window');

const BoldText = ({ text }) => (
  <Text style={styles.pageDescription('bold')}>{text}</Text>
);

const getFormattedText = (trainer = false) => (
  <Text>
    ROOK empowers software and apps with more meaningful health metrics and
    recommendations through a single API. Easily integrate, process, and embed
    data from hundreds of wearables and other data sources.
    {'\n\n'}
    <BoldText text="ROOK" />
    {' and '}
    <BoldText text="NASM" />
    {' '}
    have teamed up to allow you to go above and beyond
    in improving your
    {trainer && " client's "}
    quality of life and daily habits through intelligent data analysis.
  </Text>
);

const clientPages = [
  {
    id: 1,
    image: rookFTUImg,
    description: getFormattedText(),
  },
];

const trainerPages = [
  {
    id: 1,
    image: rookFTUImg,
    description: getFormattedText(true),
  },
];

const RookFTUE = ({ route, navigation }) => {
  const [activePage, setActivePage] = useState(0);
  const carouselRef = useRef();
  const { userId, userRole } = route.params;

  const ftuePages = userRole === ROLES.TRAINER ? [...trainerPages] : [...clientPages];

  const onPressNext = () => {
    if (activePage === ftuePages.length - 1) {
      AsyncStorage.setItem(`${userId}_rook_ftu_shown`, 'true').then(() => {
        if (userRole === ROLES.CLIENT) {
          navigation.navigate('RookConnect');
        } else {
          goBack();
        }
      });
    } else {
      setActivePage(activePage + 1);
      carouselRef?.current.snapToNext();
    }
  };

  const renderPage = ({ item, index }) => (
    <View key={`${index + item.id}`} style={styles.pageContainer}>
      <Image source={item.image} />
      <Text style={styles.pageDescription()}>{item.description}</Text>
    </View>
  );

  const getButtonText = () => {
    let buttonText = 'Got It';
    switch (userRole) {
      case ROLES.TRAINER:
        buttonText = activePage === ftuePages.length - 1 ? 'Got It!' : 'Next';
        break;
      case ROLES.CLIENT:
        buttonText = activePage === ftuePages.length - 1
          ? 'Connect Health Data'
          : 'Got It!';
        break;
      default:
        break;
    }
    return buttonText;
  };

  const goBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView
      testID="RookFTUE"
      style={[androidSafeAreaStyle, styles.safeArea]}
    >
      <View style={styles.container}>
        <View style={styles.topContainer}>
          <TouchableOpacity testID="RookFTUECloseButton" onPress={goBack}>
            <Image source={closeBtn} />
          </TouchableOpacity>
        </View>
        <View style={styles.contentContainer}>
          <Carousel
            ref={carouselRef}
            data={ftuePages}
            renderItem={renderPage}
            keyExtractor={(item) => item.id}
            sliderWidth={width}
            itemWidth={width}
            initialNumToRender={5}
            onSnapToItem={(index) => setActivePage(index)}
            removeClippedSubviews={false}
          />
          <TouchableOpacity style={styles.nextBtn} onPress={onPressNext}>
            <Text style={styles.nextBtnText}>{getButtonText()}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.transparentBlack,
  },
  container: {
    flex: 1,
    flexDirection: 'column',
    marginVertical: '4%',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  topContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '90%',
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pageDescription: (weight = 'normal') => ({
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    fontWeight: weight,
    color: colors.white,
    textAlign: 'center',
    lineHeight: 20,
    marginTop: 28,
    width: '80%',
  }),
  nextBtn: {
    backgroundColor: colors.nasmBlue,
    minWidth: 133,
    padding: 15,
    height: 55,
    borderRadius: 27.5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextBtnText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    color: colors.white,
  },
});

export default RookFTUE;
