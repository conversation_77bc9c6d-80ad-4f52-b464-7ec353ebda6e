import React from 'react';
import {
  Text,
  View,
  Image,
  FlatList,
  Animated,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import AwesomeAlert from 'react-native-awesome-alerts';
import LoadingSpinner from '../../../components/LoadingSpinner';
import { deleteAlertStyles } from '../../../styles/alertStyle';
import { curvedScale } from '../../../util/responsive';
import { colors } from '../../../styles';

const AVATAR_SIZE = 50;
const CHECKMARK_ICON = require('../../../resources/Checkmark_2.png');

const propTypes = {
  loading: PropTypes.bool,
  dataSources: PropTypes.array.isRequired,
  selectedSource: PropTypes.object.isRequired,
  onPressSource: PropTypes.func,
  disableConnectBtn: PropTypes.bool,
  showConnectionSuccessAlert: PropTypes.bool,
  setShowConnectionSuccessAlert: PropTypes.func,
  showDisconnectAlert: PropTypes.bool,
  setShowDisconnectAlert: PropTypes.func,
  revokeAuthFromRook: PropTypes.func,
};

const defaultProps = {
  loading: false,
  onPressSource: () => {},
  disableConnectBtn: false,
  showConnectionSuccessAlert: false,
  setShowConnectionSuccessAlert: () => {},
  showDisconnectAlert: false,
  setShowDisconnectAlert: () => {},
  revokeAuthFromRook: () => {},
};

const RookConnectionPage = ({
  loading,
  dataSources = [],
  selectedSource = {},
  onPressSource = () => {},
  disableConnectBtn,
  showConnectionSuccessAlert,
  setShowConnectionSuccessAlert = () => {},
  showDisconnectAlert,
  setShowDisconnectAlert = () => {},
  revokeAuthFromRook = () => {},
}) => {
  const renderDataSources = ({ item }) => (
    <View style={styles.cardView}>
      <View style={styles.sourceBrandingView}>
        <View style={styles.avatarView}>
          <Image
            style={styles.sourceAvatar}
            source={item.image ? { uri: item.image } : item.icon}
          />
        </View>
        <Text allowFontScaling={false} style={styles.sourceLabel}>
          {item.name}
        </Text>
      </View>
      <TouchableOpacity
        activeOpacity={0.6}
        style={styles.connectBtn(item.connected, disableConnectBtn)}
        onPress={() => onPressSource(item)}
        disabled={item.isLoading || (!item.connected && disableConnectBtn)}
      >
        <Animated.Text allowFontScaling={false} style={styles.connectLabel}>
          {item.connected ? 'Disconnect' : 'Connect'}
        </Animated.Text>
      </TouchableOpacity>
    </View>
  );

  const renderLoader = () => (
    <LoadingSpinner
      visible={loading}
      size="large"
      backgroundColor="rgba(0, 0, 0, 0.25)"
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.pageContainer}>
        <FlatList
          data={dataSources}
          renderItem={renderDataSources}
          showsVerticalScrollIndicator={false}
        />
      </View>
      <AwesomeAlert
        show={showConnectionSuccessAlert}
        showProgress={false}
        useNativeDriver
        closeOnTouchOutside={false}
        closeOnHardwareBackPress={false}
        showConfirmButton
        showCancelButton={false}
        customView={(
          <View style={styles.alertContainer}>
            <Image source={CHECKMARK_ICON} />
            <Text style={styles.alertTitle}>Success!</Text>
            <Text style={styles.alertText}>
              You have successfully connected to
              {' '}
              {selectedSource?.name}
              !
            </Text>
            <Text style={styles.noteText}>
              Please note that syncing your health data and updating your health
              card may take some time.
            </Text>
          </View>
        )}
        confirmText="Got it!"
        confirmButtonTextStyle={styles.alertButtonText}
        confirmButtonStyle={styles.alertButton}
        actionContainerStyle={styles.actionContainerStyle}
        contentContainerStyle={styles.alertContainer}
        onConfirmPressed={() => {
          setShowConnectionSuccessAlert(false);
        }}
      />
      <AwesomeAlert
        show={showDisconnectAlert}
        showProgress={false}
        useNativeDriver
        title={`Disconnect from ${selectedSource.name}?`}
        message={`You can reconnect ${selectedSource.name} again at any time.`}
        closeOnTouchOutside={false}
        closeOnHardwareBackPress={false}
        showConfirmButton
        showCancelButton
        titleStyle={deleteAlertStyles.alertTitle}
        messageStyle={deleteAlertStyles.alertText}
        confirmText="Close"
        cancelText="Disconnect"
        confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
        confirmButtonStyle={deleteAlertStyles.alertButton}
        cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
        cancelButtonStyle={deleteAlertStyles.alertButton}
        // eslint-disable-next-line react-native/no-inline-styles
        actionContainerStyle={{
          flexDirection: 'column',
          backgroundColor: colors.white,
          borderRadius: 5,
        }}
        contentContainerStyle={deleteAlertStyles.alertContainer}
        onCancelPressed={() => {
          setShowDisconnectAlert(false);
          revokeAuthFromRook();
        }}
        onConfirmPressed={() => {
          setShowDisconnectAlert(false);
        }}
      />
      {renderLoader()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  pageContainer: {
    flex: 1,
    paddingHorizontal: curvedScale(20),
  },
  cardView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(100),
    backgroundColor: colors.paleGray3,
    borderRadius: curvedScale(10),
    margin: curvedScale(10),
    padding: curvedScale(10),
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: 7,
    },
    shadowRadius: 4,
    shadowOpacity: 0.5,
    elevation: 6,
  },
  sourceBrandingView: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
  },
  avatarView: {
    width: curvedScale(AVATAR_SIZE),
    height: curvedScale(AVATAR_SIZE),
  },
  sourceAvatar: {
    width: '100%',
    height: '100%',
  },
  sourceLabel: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Black',
    textAlign: 'center',
    marginHorizontal: curvedScale(15),
  },
  connectBtn: (isConnectedToSource, disableConnectBtn) => {
    let background;
    if (isConnectedToSource) {
      background = colors.royalBlue;
    } else if (disableConnectBtn) {
      background = colors.subGreyTwo;
    } else {
      background = colors.buttonBlue;
    }
    return {
      borderRadius: curvedScale(20),
      padding: curvedScale(5),
      backgroundColor: background,
      margin: curvedScale(10),
    };
  },
  connectLabel: {
    color: colors.white,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
    textAlign: 'center',
    marginHorizontal: curvedScale(15),
  },
  actionContainerStyle: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderColor: colors.gray_1,
    borderTopWidth: 1.5,
    width: '100%',
    borderRadius: curvedScale(5),
  },
  alertContainer: {
    borderRadius: curvedScale(5),
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: curvedScale(120),
    width: curvedScale(270),
    backgroundColor: colors.white,
    paddingTop: curvedScale(20),
    paddingHorizontal: 0,
    paddingBottom: 0,
  },
  alertTitle: {
    fontSize: curvedScale(17),
    fontFamily: 'Avenir-Medium',
    textAlign: 'center',
    color: colors.black,
    marginTop: curvedScale(20),
  },
  alertText: {
    fontSize: curvedScale(12),
    color: colors.subGrey,
    fontFamily: 'Avenir-Medium',
    textAlign: 'center',
    paddingHorizontal: curvedScale(20),
    marginTop: curvedScale(10),
    marginBottom: curvedScale(20),
  },
  noteText: {
    fontSize: curvedScale(10),
    color: colors.subGreyTwo,
    fontFamily: 'Avenir-Medium',
    textAlign: 'center',
    paddingHorizontal: curvedScale(10),
    marginBottom: curvedScale(20),
  },
  alertButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.transparent,
    width: '100%',
    minHeight: curvedScale(40),
    marginBottom: 0,
  },
  alertButtonText: {
    fontSize: curvedScale(17),
    color: colors.subGrey,
    fontFamily: 'Avenir-Black',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

RookConnectionPage.propTypes = propTypes;
RookConnectionPage.defaultProps = defaultProps;

export default RookConnectionPage;
