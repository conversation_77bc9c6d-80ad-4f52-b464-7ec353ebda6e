import React, { useEffect, useState } from 'react';
import {
  Alert,
  Platform,
  StyleSheet,
  SafeAreaView,
  NativeModules,
} from 'react-native';
import {
  useRookSyncConfiguration,
  useRookSyncPermissions,
} from 'react-native-rook-sdk-health-connect';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import nasm from '../../../dataManager/apiConfig';
import { colors } from '../../../styles';
import RookAPI from '../../../api/rook/RookConstants';
import RookConnectionPage from './RookConnectionPage';

const propTypes = {
  navigation: PropTypes.any.isRequired,
};

const defaultProps = {};

const HEALTH_CONNECT_ICON = require('../../../resources/HealthConnectIcon.png');

const stateSelector = (state) => state;

const RookConnect = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [disableConnectBtn, setDisableConnectBtn] = useState(false);
  const [dataSources, setDataSources] = useState(Array.prototype);
  const [showConnectionSuccessAlert, setShowConnectionSuccessAlert] = useState(false);
  const [showAwesomeAlert, setShowAwesomeAlert] = useState(false);
  const [selectedSource, setSelectedSource] = useState({});
  const { currentUser } = useSelector(stateSelector);
  const {
    ready,
    checkAvailability,
    openHealthConnectSettings,
    checkPermissions,
    requestPermissions,
  } = useRookSyncPermissions();

  const { getUserID, clearUserID, updateUserID } = useRookSyncConfiguration();

  const { RNSyncRookModule } = NativeModules;

  const rookApi = new RookAPI();

  useEffect(() => {
    navigation.setOptions({
      title: 'Health Data',
    });
    const unsubscribeFocus = navigation.addListener('focus', () => {
      if (ready) {
        checkUserAndFetchData();
      }
    });
    return unsubscribeFocus;
  }, [ready, navigation, selectedSource]);

  const checkUserAndFetchData = async () => {
    await fetchDataSources();
  };

  const clearRookUser = () => {
    try {
      clearUserID().catch((error) => {
        showAlert(error?.message ?? error);
      });
    } catch (error) {
      showAlert(error?.message ?? error);
    }
  };

  const updateRookUser = async (userId) => {
    setLoading(true);
    try {
      await updateUserID(userId)
        .then(() => {
          handleAvailability();
          onRequestPermissions();
        })
        .catch((error) => {
          showAlert(error?.message ?? error);
        });
    } catch (error) {
      showAlert(error?.message ?? error);
    } finally {
      setLoading(false);
      await fetchDataSources();
    }
  };

  const handleAvailability = async () => {
    try {
      const result = await checkAvailability();
      if (result !== 'INSTALLED') {
        await openHealthConnectSettings();
      }
    } catch (error) {
      showAlert(error?.message ?? error);
    }
  };

  const fetchDataSources = async () => {
    try {
      setLoading(true);
      setDisableConnectBtn(false);
      const display_sources = [];
      const userId = await getUserID();
      const source_connection_status = await rookApi.getConnectionStatus(
        currentUser?.id,
      );
      if (Platform.OS === 'android') {
        display_sources.push({
          name: 'Health Connect',
          description: '',
          icon: HEALTH_CONNECT_ICON,
          connected:
            (!userId
              ? false
              : source_connection_status?.health_connect
                || source_connection_status?.android) ?? false,
          authorization_url: null,
        });
      }
      const data_sources = await rookApi.getDataSources(currentUser?.id);
      if (!data_sources || !data_sources?.length) {
        Alert.alert(
          'Error',
          'We hit a snag while fetching data sources. Please try again later',
          [
            {
              text: 'Ok',
              onPress: () => navigation.goBack(),
            },
          ],
        );
        return;
      }
      data_sources.map((source) => display_sources.push(source));
      const findSource = display_sources?.length
        && display_sources?.find((src) => src.name === selectedSource?.name);
      if (findSource) {
        setShowConnectionSuccessAlert(!!findSource?.connected);
        if (findSource?.connected !== selectedSource?.connected) {
          const jsonData = {
            data_source: findSource?.name.replace(' ', '_')?.toUpperCase(),
            is_connected: findSource?.connected,
          };
          await nasm.api.toggleRookDataSource(jsonData);
        }
      }
      const isAnySourceConnected = display_sources?.length && display_sources?.some((src) => src.connected);
      if (isAnySourceConnected) {
        setDisableConnectBtn(true);
      }
      setDataSources(display_sources);
      startDataSyncing();
    } catch (error) {
      showAlert(error?.message ?? error);
    } finally {
      setLoading(false);
    }
  };

  const startDataSyncing = async () => {
    RNSyncRookModule.rookYesterdaySyncPermissions();
  };

  const onRequestPermissions = async () => {
    checkPermissions('ALL')
      .then((connected) => {
        if (!connected) {
          requestPermissions('ALL')
            .then((res) => {
              if (!res) {
                showAlert('Permission was not granted');
              }
            })
            .catch((error) => {
              showAlert(error?.message ?? error);
            });
        }
      })
      .catch((error) => showAlert(error?.message ?? error));
  };

  const openConnectionPage = (item) => {
    navigation.navigate('WebView', {
      uri: item.authorization_url,
      title: 'Health Data',
    });
  };

  const revokeAuthFromRook = async () => {
    setLoading(true);
    const revokeResponse = await rookApi.disconnectSource(
      currentUser?.id,
      selectedSource?.name,
    );
    if (revokeResponse) {
      if (selectedSource.name === 'Health Connect') {
        clearRookUser();
      }

      /**
       * @requires
       * This arbitrary delay is the amount of time that Rook is taking
       * to update the status of a data source.
       */
      setTimeout(() => {
        fetchDataSources();
      }, 500);
    } else {
      setLoading(false);
      Alert.alert('Error', 'Something went wrong! Please try again later.');
    }
  };

  const showAlert = (message) => {
    Alert.alert(
      'Error',
      message ?? 'Something went wrong! Please try again later.',
    );
  };

  const onPressSource = (item) => {
    setSelectedSource({
      name: item.name,
      connected: item.connected,
    });
    if (!item.connected && !disableConnectBtn) {
      if (item.name === 'Health Connect') {
        updateRookUser(currentUser?.id);
      } else {
        openConnectionPage(item);
      }
    } else if (item.connected) {
      setShowAwesomeAlert(true);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <RookConnectionPage
        loading={loading}
        dataSources={dataSources}
        selectedSource={selectedSource}
        onPressSource={onPressSource}
        disableConnectBtn={disableConnectBtn}
        showConnectionSuccessAlert={showConnectionSuccessAlert}
        setShowConnectionSuccessAlert={setShowConnectionSuccessAlert}
        showDisconnectAlert={showAwesomeAlert}
        setShowDisconnectAlert={setShowAwesomeAlert}
        revokeAuthFromRook={revokeAuthFromRook}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});

RookConnect.propTypes = propTypes;
RookConnect.defaultProps = defaultProps;

export default RookConnect;
