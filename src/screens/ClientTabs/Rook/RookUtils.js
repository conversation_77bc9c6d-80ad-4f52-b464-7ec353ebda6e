import { useFocusEffect } from '@react-navigation/native';
import React from 'react';
import { useRookSyncConfiguration } from 'react-native-rook-sdk-health-connect';
import { useRookConfiguration } from 'react-native-rook-sdk-apple-health';
import { Platform } from 'react-native';

const RookUtils = (props, ref) => {
  const rookSyncHook = Platform.OS === 'ios' ? useRookConfiguration : useRookSyncConfiguration;
  const { getUserID, clearUserID } = rookSyncHook();

  React.useImperativeHandle(ref, () => ({
    clearUser: async () => {
      let isUserIdClear = false;
      try {
        const isClear = await clearUserID();
        if (isClear) {
          isUserIdClear = true;
        }
      } catch (error) {
        isUserIdClear = false;
      }
      return isUserIdClear;
    },
  }));

  useFocusEffect(
    React.useCallback(() => {
      getRookUser();
    }, []),
  );

  const getRookUser = async () => {
    let isUserConnected = false;
    try {
      const userId = await getUserID();
      if (userId) {
        isUserConnected = true;
      }
    } catch (error) {
      return;
    } finally {
      if (props.onGetRookUser) {
        props.onGetRookUser(isUserConnected);
      }
    }
  };
};

export default React.forwardRef(RookUtils);
