import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import moment from 'moment';
import analytics from '@react-native-firebase/analytics';
import {
  Alert,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  View,
  Text,
  Image,
  Linking,
  ActivityIndicator,
  Platform,
  FlatList,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  NutritionSection,
  MeasurementsSection,
  WeeklyCompletion,
  GoalsSection,
  OHSASection,
  PerformanceAssessmentSection,
  ScaledText,
} from '../../components';
import nasm from '../../dataManager/apiConfig';
import NotesSection from '../../components/NotesSection';
import HealthDataSection from '../../components/HealthDataSection';
import DailyReadinessSection from '../../components/DailyReadinessSection';
import WorkoutActivityCard from '../MainStack/ActivityFeed/WorkoutActivityCard';
import { ROLES } from '../../constants';
import { colors, shadow } from '../../styles';
import { isConnectedMode } from '../../util/PermissionUtils';
import { logComponentException } from '../../util/logging';
import { curvedScale, scaleWidth } from '../../util/responsive';
import RookAPI from '../../api/rook/RookConstants';

const tempProfilePic = require('../../resources/defaultProfile.png');
const callIcon = require('../../resources/imgCallIcon.png');
const emailIcon = require('../../resources/emailIcon.png');
const connectedIcon = require('../../resources/connectedDashboardEmptyState.png');
const arrowIcon = require('../../resources/btnArrowGray.png');

const rookApi = new RookAPI();

const propTypes = {
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
    last_name: PropTypes.string,
    avatar_url: PropTypes.string,
    client_user: PropTypes.object,
  }).isRequired,
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    client_user: PropTypes.object,
    role: PropTypes.string,
    avatar_connected: PropTypes.number,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
};

class ClientOverview extends Component {
  constructor(props) {
    super(props);

    let trainerUser;
    if (this.props.currentUser.role === ROLES.TRAINER) {
      trainerUser = this.props.currentUser;
    } else if (this.props.currentUser.client_user.trainer) {
      trainerUser = this.props.currentUser.client_user.trainer;
    }

    this.state = {
      avatarConnected: 0,
      loading: false,
      nutritionInformation: null,
      measurementInformation: null,
      milestonesList: [],
      goalsUpdatedOn: '',
      assessments: [],
      ohsaInformation: null,
      notesList: [],
      trainerInformation: {},
      milestoneIsUpdating: false,
      visibleGoals: this.props.selectedClient?.client_user.visible_goals,
      visibleDailyReadiness:
        this.props.selectedClient?.client_user.visible_daily_readiness,
      visibleMeasures: this.props.selectedClient?.client_user.visible_measures,
      visibleNutrition:
        this.props.selectedClient?.client_user.visible_nutrition,
      visibleOhsa: this.props.selectedClient?.client_user.visible_ohsa,
      visiblePerfAssess:
        this.props.selectedClient?.client_user.visible_perf_assess,
      connectedMode: isConnectedMode(trainerUser),
      workoutActivityResult: [],
      readinessData: [],
      club_id: this.props.trainerActiveProfile?.ClubId,
      location_id: this.props.trainerActiveProfile?.Locations?.Id,
      healthData: [],
      isFTUShown: false,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener(
      'focus',
      async () => {
        StatusBar.setBarStyle('light-content');
        analytics().logEvent('screen_view', {
          screen_name: 'client_dashboard',
        });
        if (this.weeklyCompletion) {
          this.weeklyCompletion.didFocus();
        }

        this.setState({ loading: true }, () => {
          this.checkRookFTU();
          this.getWorkoutActivity();
          this.getAllData(true);
        });
      },
    );
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  handleNutritionDeleted = (nutrition) => {
    this.setState({ nutritionInformation: nutrition });
  };

  handleNutritionUpdate = (nutritionInformation) => {
    this.setState({ nutritionInformation });
  };

  onMilestonePressed = async (milestone) => {
    if (!this.state.milestoneIsUpdating) {
      this.setState({ milestoneIsUpdating: true });
      milestone.status = !milestone.status;
      try {
        await nasm.api.updateMilestone(
          milestone,
          this.props.selectedClient?.id,
        );
        const goalsUpdatedOn = moment().format('M/D/YY');
        this.setState({ goalsUpdatedOn, milestoneIsUpdating: false });
      } catch (error) {
        milestone.status = !milestone.status;
        Alert.alert('Error', error.message);
        this.setState({ milestoneIsUpdating: false });
      }
    }
  };

  onPressCall = () => {
    Linking.openURL(`tel:${this.state.trainerInformation.phone_number}`);
  };

  onPressEmail = () => {
    Linking.openURL(`mailto:${this.state.trainerInformation.ua_email}`);
  };

  getUserId = () => {
    let userId = null;
    if (this.props.currentUser.role === ROLES.CLIENT) {
      userId = this.props.currentUser.id;
    } else if (this.props.selectedClient?.id) {
      userId = this.props.selectedClient?.id;
    }
    return userId;
  };

  getWorkoutActivity = async () => {
    if (this.props.currentUser.role === ROLES.CLIENT) {
      return null;
    }
    const userId = this.getUserId();
    const { club_id, location_id } = this.state;
    try {
      const response = await nasm.api.getAllActivityFeed({
        userId,
        club_id,
        location_id,
      });
      this.setState({ workoutActivityResult: response ?? [] });
    } catch (error) {
      return error;
    }
    return null;
  };

  getMostRecentWorkoutActivity = () => {
    const sortedActivities = this.state.workoutActivityResult.sort(
      (a, b) => new Date(b.created_at) > new Date(a.created_at),
    );
    const [item] = sortedActivities;
    return item;
  };

  getMacrosData = async (userId, date) => nasm.api.getMacrosData(userId, date).catch((error) => {
    logComponentException('Macros', error);
  });

  getAllData = async (shouldAnimate = true) => {
    if (!this.props.selectedClient?.id) {
      this.getAllData();
      return;
    }
    let {
      visibleGoals,
      visibleDailyReadiness,
      visibleMeasures,
      visibleNutrition,
      visibleOhsa,
      visiblePerfAssess,
      clientGroups,
    } = this.state;
    let goalsUpdatedOn = '';
    try {
      const client = await nasm.api.getUserById(this.props.selectedClient?.id);
      const { client_user } = client;
      visibleGoals = client_user.visible_goals;
      visibleDailyReadiness = client_user.visible_daily_readiness;
      visibleMeasures = client_user.visible_measures;
      visibleNutrition = client_user.visible_nutrition;
      visibleOhsa = client_user.visible_ohsa;
      visiblePerfAssess = client_user.visible_perf_assess;
      clientGroups = client_user.client_group;
      this.setState({
        avatarConnected: client.avatar_connected,
        visibleGoals,
        visibleDailyReadiness,
        visibleMeasures,
        visibleNutrition,
        visibleOhsa,
        visiblePerfAssess,
        clientGroups,
      });
    } catch (error) {
      Alert.alert(
        'Error',
        error.message
          || 'Unable to update client overview. Please try again later.',
      );
    }
    let {
      nutritionInformation,
      milestonesList,
      measurementInformation,
      assessments,
      ohsaInformation,
      notesList,
      trainerInformation,
      readinessData,
    } = this.state;
    try {
      const [
        nutritionResponse,
        milestones,
        measurementsResponse,
        assessmentsResponse,
        ohsaResponse,
        latestNotes,
        trainer,
        readinessResponse,
      ] = await Promise.all([
        nasm.api.getLatestNutrition(this.props.selectedClient?.id),
        nasm.api.getLatestMilestones(this.props.selectedClient?.id),
        nasm.api.getLatestMeasurements(this.props.selectedClient?.id),
        nasm.api.getLatestAssessments(this.props.selectedClient?.id),
        nasm.api.getAssessmentResult(this.props.selectedClient?.id),
        nasm.api.getLatestTrainerNotesByClientId(
          this.props.selectedClient?.id,
          this.props.currentUser.role === ROLES.TRAINER,
        ),
        this.props.currentUser.client_user?.trainer
          ? nasm.api.getUserById(
            this.props.currentUser.client_user.trainer.user_id,
          )
          : {},
        nasm.api.getReadinessGraphAverageResult(this.props.selectedClient?.id),
      ]);

      if (this.state.avatarConnected) {
        const macrosData = await this.getMacrosData(
          this.props.selectedClient?.id,
          moment().format('YYYY-MM-DD'),
        );
        if (macrosData && macrosData.tracked) {
          nutritionInformation = {
            calories: parseInt(
              this.roundedValue(macrosData.tracked.calories),
              10,
            ),
            protein: parseInt(
              this.roundedValue(macrosData.tracked.protein),
              10,
            ),
            carbohydrates: parseInt(
              this.roundedValue(macrosData.tracked.carbs),
              10,
            ),
            fat: parseInt(this.roundedValue(macrosData.tracked.fat), 10),
          };
        }
      } else {
        nutritionInformation = nutritionResponse;
      }
      milestonesList = milestones.milestones;
      measurementInformation = measurementsResponse;
      assessments = assessmentsResponse;
      ohsaInformation = ohsaResponse;
      notesList = latestNotes;
      trainerInformation = trainer;
      readinessData = readinessResponse;
      if (milestones.length > 3) {
        milestones.length = 3;
      }
      if (milestones.latest_activity_date) {
        goalsUpdatedOn = moment(milestones.latest_activity_date).format(
          'M/D/YY',
        );
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      if (shouldAnimate) {
        // LayoutAnimation.easeInEaseOut();
      }
      this.setState(
        {
          nutritionInformation,
          measurementInformation,
          assessments,
          ohsaInformation,
          trainerInformation,
          loading: false,
          milestonesList,
          goalsUpdatedOn,
          notesList,
          readinessData,
        },
        () => {
          this.getHealthData();
        },
      );
    }
  };

  getHealthData = async () => {
    const source_connection_status = await rookApi.getConnectionStatus(
      this.props.selectedClient?.id,
    );
    const data_sources = await rookApi.getDataSources(
      this.props.selectedClient?.id,
    );

    if (
      Platform.OS === 'ios'
      && !(
        data_sources?.some((src) => src.connected)
        || source_connection_status?.apple_health
      )
    ) {
      this.setState({ isRookConnected: false });
    } else if (
      Platform.OS === 'android'
      && !(
        data_sources?.some((src) => src.connected)
        || source_connection_status?.health_connect
      )
    ) {
      this.setState({ isRookConnected: false });
    } else {
      this.setState({ isRookConnected: true });
      const healthData = await nasm.api.getDateWiseHealthSummary(
        this.props.selectedClient?.id,
        moment().subtract(1, 'days').format('YYYY-MM-DD'),
      );

      if (
        healthData?.event_data?.physical_summary
        || healthData?.event_data?.sleep_summary
      ) {
        this.setState({ healthData: healthData.event_data });
      }
    }
  };

  checkRookFTU = async () => {
    if (!this.state.isFTUShown) {
      this.setState({ isFTUShown: true }, async () => {
        const rookFtuShown = await AsyncStorage.getItem(
          `${this.props.currentUser.id}_rook_ftu_shown`,
        );
        if (!rookFtuShown) {
          this.props.navigation.navigate('RookFTUE', {
            userId: this.props.currentUser.id,
            userRole: this.props.currentUser.role,
          });
        }
      });
    }
  };

  roundedValue = (value) => (Math.round(value * 10) / 10).toFixed(0);

  goToWorkoutActivity = () => {
    this.props.navigation.navigate('ActivityFeed', {
      comingFrom: 'ClientOverview',
    });
  };

  goToWorkoutSummary = (item) => {
    this.props.navigation.navigate({
      name: 'SummaryCard',
      params: {
        userId:
          this.props.currentUser.role === ROLES.CLIENT
            ? this.props.currentUser.id
            : this.props.selectedClient?.id,
        scheduledWorkoutId: item.user_schedule_workout_id,
      },
      merge: true,
    });
  };

  navigateToSelectedSection = (name, params) => {
    this.props.navigation.navigate({
      name,
      params,
      merge: true,
    });
  };

  renderConnectedState = () => (
    <View style={styles.connectedContainer}>
      <Image source={connectedIcon} />
      <ScaledText style={styles.connectedTitle}>Get more from EDGE</ScaledText>
      <ScaledText style={styles.connectedDescription}>
        Upgrade your plan to track your client’s progress, view history, and
        compare nutrition, measurements and performance assessments.
      </ScaledText>
      <TouchableOpacity
        style={styles.connectedButton}
        onPress={() => this.props.navigation.navigate('ConnectedUpgradeModal')}
      >
        <ScaledText style={styles.connectedButtonText}>Learn More</ScaledText>
      </TouchableOpacity>
    </View>
  );

  renderGroupName = ({ item }) => (
    <View style={styles.groupInfoView}>
      <Text style={styles.groupNameLabel}>{item.title}</Text>
      <Text style={styles.groupMembersCount}>
        {`${item.total_members} Members`}
      </Text>
    </View>
  );

  renderClientGroupsList = () => (
    <View style={styles.groupInfoContainer}>
      <FlatList
        data={this.state.clientGroups}
        renderItem={this.renderGroupName}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={() => (
          <Text style={styles.groupsHeader}>Groups</Text>
        )}
      />
    </View>
  );

  renderWorkoutActivity = () => {
    const { workoutActivityResult } = this.state;
    const hasWorkoutActivity = workoutActivityResult.length > 0;
    const recentActivity = this.getMostRecentWorkoutActivity();

    return (
      <View style={styles.cardStyle}>
        {this.state.clientGroups?.length ? this.renderClientGroupsList() : null}
        <WeeklyCompletion
          getRef={(ref) => {
            this.weeklyCompletion = ref;
          }}
          onPress={() => this.props.navigation.navigate({
            name: 'Schedule',
            params: {
              isClientOverview: true,
            },
            merge: true,
          })}
          showingWorkoutActivity
        />
        {hasWorkoutActivity && (
          <>
            <TouchableOpacity
              activeOpacity={0.5}
              style={styles.workoutActivityContainer}
              onPress={this.goToWorkoutActivity}
            >
              <View style={styles.workoutActivityView}>
                <Text style={styles.workoutActivity}>Workout Activity</Text>
                <Image style={styles.arrow} source={arrowIcon} />
              </View>
            </TouchableOpacity>
            <WorkoutActivityCard
              item={recentActivity}
              containerStyle={styles.containerStyle}
              goToWorkoutSummary={() => this.goToWorkoutSummary(recentActivity)}
            />
          </>
        )}
      </View>
    );
  };

  renderTrainerNotes = () => {
    if (this.props.currentUser.role !== ROLES.TRAINER) {
      return null;
    }
    return (
      <NotesSection
        notes={this.state.notesList}
        updatedOn={
          this.state.notesList.length > 0
            ? this.state.notesList[0].updated_at
            : ''
        }
        showUpdatedText={this.state.notesList.length > 0}
        loading={this.state.loading}
        onPress={() => {
          this.navigateToSelectedSection('NotesList');
        }}
        onPressNoRecords={() => {
          this.navigateToSelectedSection('CreateOrEditNote', {
            type: 'new',
          });
        }}
      />
    );
  };

  renderHealthDataSection = () => (
    <HealthDataSection
      healthData={this.state.healthData ?? {}}
      isRookConnected={this.state.isRookConnected}
      updatedOn={
        this.state.healthData
          ? moment().subtract(1, 'days').format('M/D/YY')
          : null
      }
      showUpdatedText
      loading={this.state.loading}
      onPress={() => {
        if (this.state.isRookConnected) {
          this.navigateToSelectedSection('PhysicalHealth');
        } else {
          this.navigateToSelectedSection('RookConnect');
        }
      }}
      onPressNoRecords={() => this.navigateToSelectedSection('PhysicalHealth')}
    />
  );

  render() {
    let focus;
    if (
      this.props.selectedClient?.client_user.goals
      && this.props.selectedClient?.client_user.goals.length !== 0
    ) {
      [focus] = this.props.selectedClient?.client_user.goals;
    }
    const loaderStyle = {
      marginTop: Platform.OS === 'android' ? 10 : 0,
      marginBottom:
        Platform.OS === 'android'
        && this.props.currentUser.role === ROLES.TRAINER
          ? 10
          : 30,
    };
    return (
      <ScrollView style={styles.container}>
        <SafeAreaView
          style={{ paddingTop: 0, backgroundColor: colors.background }}
          pointerEvents={this.state.loading ? 'none' : 'auto'}
        >
          {this.state.loading && (
            <View style={loaderStyle}>
              <ActivityIndicator
                animating
                size="large"
                color={colors.buttonBorderDisabled}
              />
            </View>
          )}
          {this.props.currentUser.role === ROLES.CLIENT
            && this.props.currentUser.client_user?.trainer && (
              <View
                style={{
                  alignItems: 'center',
                  marginBottom: 20,
                }}
              >
                <Image
                  style={{
                    width: 80,
                    height: 80,
                    borderRadius: 40,
                  }}
                  source={
                    this.state.trainerInformation.avatar_url
                      ? { uri: this.state.trainerInformation.avatar_url }
                      : tempProfilePic
                  }
                />
                <Text
                  style={{
                    fontFamily: 'Avenir-Medium',
                    fontSize: 13,
                    marginTop: 5,
                    textColor: colors.fillDarkGrey,
                  }}
                >
                  Your Fitness Professional
                </Text>
                <Text
                  style={{
                    fontFamily: 'Avenir-Medium',
                    fontSize: 17,
                    marginTop: 5,
                    textColor: colors.fillDarkGrey,
                  }}
                >
                  {this.state.trainerInformation.full_name}
                </Text>
                <View style={{ flex: 1, flexDirection: 'row', marginTop: 27 }}>
                  {this.state.trainerInformation.phone_number ? (
                    <TouchableOpacity
                      style={[styles.trainerContactInfo, { marginRight: 18 }]}
                      onPress={this.onPressCall}
                    >
                      <Image
                        style={styles.trainerContactImage}
                        source={callIcon}
                      />
                      <Text style={styles.trainerContactText}> Call </Text>
                    </TouchableOpacity>
                  ) : null}
                  <TouchableOpacity
                    style={styles.trainerContactInfo}
                    onPress={this.onPressEmail}
                  >
                    <Image
                      style={styles.trainerContactImage}
                      source={emailIcon}
                    />
                    <Text style={styles.trainerContactText}> Email </Text>
                  </TouchableOpacity>
                </View>
              </View>
          )}
          {this.renderWorkoutActivity()}
          {this.state.visibleDailyReadiness && !this.state.connectedMode && (
            <DailyReadinessSection
              assessments={this.state.readinessData}
              onPress={() => this.props.navigation.navigate({
                name: 'DailyReadinessTabView',
                params: {
                  userId:
                      this.props.currentUser.role === ROLES.CLIENT
                        ? this.props.currentUser.id
                        : this.props.selectedClient?.id,
                },
                merge: true,
              })}
            />
          )}
          {this.renderTrainerNotes()}
          {this.renderHealthDataSection()}
          {this.state.visibleGoals && !this.state.connectedMode && (
            <GoalsSection
              ref={(ref) => {
                this.goalsSection = ref;
              }}
              goals={this.state.milestonesList}
              focus={focus}
              onPress={() => this.navigateToSelectedSection('GoalsTabView')}
              onPressFocus={() => this.navigateToSelectedSection('GoalsTabView', {
                initialRouteName: 'Activity',
              })}
              onPressGoal={this.onMilestonePressed}
              updatedOn={this.state.goalsUpdatedOn}
              loading={this.state.loading}
              updating={this.state.milestoneIsUpdating}
            />
          )}
          {!this.state.club_id
            && this.state.visibleNutrition
            && !this.state.connectedMode && (
              <TouchableOpacity
                onPress={() => {
                  if (this.state.avatarConnected) {
                    this.navigateToSelectedSection('NutritionTabView', {
                      avatarConnected: this.state.avatarConnected,
                      nutrition: this.state.nutritionInformation,
                      nutritionUpdateCallback: this.handleNutritionUpdate,
                      nutritionDeletedCallback: this.handleNutritionDeleted,
                    });
                  } else {
                    this.navigateToSelectedSection('BWPTabView');
                  }
                }}
              >
                <NutritionSection
                  avatarConnected={this.state.avatarConnected}
                  nutrition={this.state.nutritionInformation}
                  loading={this.state.loading}
                />
              </TouchableOpacity>
          )}
          {!this.state.club_id
            && this.state.visibleMeasures
            && !this.state.connectedMode && (
              <TouchableOpacity
                onPress={() => this.navigateToSelectedSection('MeasurementTabView')}
              >
                <MeasurementsSection
                  measurements={this.state.measurementInformation}
                  loading={this.state.loading}
                  weightUnit={this.props.currentUser.unit_weight}
                />
              </TouchableOpacity>
          )}

          {this.state.visiblePerfAssess && !this.state.connectedMode && (
            <TouchableOpacity
              onPress={() => this.navigateToSelectedSection('PerformanceAssessmentTabView')}
            >
              <PerformanceAssessmentSection
                assessments={this.state.assessments}
                loading={this.state.loading}
              />
            </TouchableOpacity>
          )}

          {(this.state.visibleOhsa
            || this.props.currentUser.role === ROLES.TRAINER) && (
            <OHSASection
              onPress={() => this.navigateToSelectedSection('OHSATabView')}
              overactiveMuscles={
                this.state.ohsaInformation
                  ? this.state.ohsaInformation.over_active_muscles
                  : undefined
              }
              underactiveMuscles={
                this.state.ohsaInformation
                  ? this.state.ohsaInformation.under_active_muscles
                  : undefined
              }
              updatedOn={
                this.state.ohsaInformation
                  ? this.state.ohsaInformation.assessment_date
                  : undefined
              }
              loading={this.state.loading}
            />
          )}
          {!this.state.club_id
            && this.state.connectedMode
            && this.renderConnectedState()}
        </SafeAreaView>
      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background,
    flex: 1,
  },
  trainerContactText: {
    alignSelf: 'center',
    fontFamily: 'Avenir-Heavy',
    fontSize: 14,
    color: colors.subGrey,
  },
  trainerContactImage: {
    width: 16,
    height: 16,
    alignSelf: 'center',
    marginRight: 6,
  },
  connectedContainer: {
    alignItems: 'center',
    paddingVertical: 45,
    width: '80%',
    alignSelf: 'center',
  },
  connectedTitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 22,
    color: colors.black,
    paddingTop: 20,
    paddingBottom: 10,
  },
  connectedDescription: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    lineHeight: 22,
    color: colors.fillDarkGrey,
    textAlign: 'center',
    paddingBottom: 30,
  },
  connectedButton: {
    ...shadow,
    height: 55,
    borderRadius: 27.5,
    backgroundColor: colors.macaroniAndCheese,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 30,
  },
  connectedButtonText: {
    fontFamily: 'Avenir',
    fontSize: 17,
    fontWeight: 'bold',
    color: colors.white,
  },
  cardStyle: {
    backgroundColor: colors.white,
    marginHorizontal: scaleWidth(5),
    borderRadius: 5,
    ...shadow,
  },
  workoutActivityContainer: {
    borderColor: colors.colorsFillLight2,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    marginVertical: 20,
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  workoutActivityView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'space-between',
    paddingLeft: 10,
    paddingRight: 5,
  },
  workoutActivity: {
    color: colors.black,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(15),
  },
  arrow: {
    tintColor: colors.chatSelected,
    height: curvedScale(10),
    width: curvedScale(20),
  },
  containerStyle: {
    paddingTop: 0,
    paddingHorizontal: 0,
  },
  trainerContactInfo: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderColor: colors.subGrey,
    width: 110,
    height: 33,
    borderStyle: 'solid',
    borderWidth: 2,
    borderRadius: 17.5,
    justifyContent: 'center',
  },
  groupInfoContainer: {
    paddingHorizontal: curvedScale(20),
    paddingVertical: curvedScale(9),
    borderColor: colors.colorsFillLight2,
    borderBottomWidth: 1,
  },
  groupsHeader: {
    color: colors.eerieBlack,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(15),
  },
  groupInfoView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: curvedScale(9),
    justifyContent: 'space-between',
  },
  groupNameLabel: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(13),
    width: '70%',
  },
  groupMembersCount: {
    color: colors.chatSelected,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(12),
  },
});

ClientOverview.propTypes = propTypes;
const mapStateToProps = ({
  currentUser,
  selectedClient,
  trainerActiveProfile,
}) => ({
  currentUser,
  selectedClient,
  trainerActiveProfile,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(ClientOverview);
