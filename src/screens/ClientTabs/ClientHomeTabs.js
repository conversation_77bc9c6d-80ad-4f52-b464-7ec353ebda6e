import React, { Component } from 'react';
import {
  View, Text, Dimensions, StyleSheet,
  Platform,
} from 'react-native';
import { connect } from 'react-redux';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import PropTypes from 'prop-types';
import DeviceInfo from 'react-native-device-info';
import ProfileClient from './ProfileClient/ProfileClient';
import DashboardClient from './DashboardClient';
import ClientOverview from './ClientOverview';
import ChannelListScreen from '../Chat/ChannelListScreen';
import { clearDay } from '../../reducers/selectedDayReducer';
import { ROLES, chatClient } from '../../constants';
import { registerForPush, deselectClient } from '../../actions';
import { showHomeVideoIfAble } from '../../FTUEVideoManager';
import { isConnectedMode } from '../../util/PermissionUtils';
import { colors, materialTabBarOptions } from '../../styles';

const propTypes = {
  selectedClient: PropTypes.shape({
    full_name: PropTypes.string,
  }).isRequired,
  currentUser: PropTypes.shape({
    role: PropTypes.string,
  }).isRequired,
};

const { width } = Dimensions.get('window');
const TabNav = createMaterialTopTabNavigator();

class ClientHomeTabs extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isWalkIn: false,
      connectedMode: false,
    };
  }

  componentDidMount() {
    if (this.props.currentUser.role === ROLES.CLIENT) {
      this.props.navigation.setParams({
        isBadged: chatClient.user.total_unread_count > 0,
      });
      chatClient.on((event) => {
        if (event.total_unread_count > 0) {
          this.props.navigation.setParams({ isBadged: true });
        } else if (event.total_unread_count === 0) {
          this.props.navigation.setParams({ isBadged: false });
        }
      });
      const trainerData = {
        has_trainer: `${false}`,
        enrolled_in_wakeup_workout: `${false}`,
      };
      if (this.props.currentUser.client_user?.trainer) {
        trainerData.has_trainer = `${true}`;
        this.props.showHomeVideoIfAble(
          this.props.currentUser.id,
          this.props.navigation,
        );
        if (
          isConnectedMode({
            role: ROLES.TRAINER,
            ...this.props.currentUser.client_user.trainer,
          })
        ) {
          this.setState({ connectedMode: true });
          this.props.navigation.setParams({
            title: this.props.currentUser.full_name,
          });
        }
      } else {
        this.setState({ isWalkIn: true });
        this.props.navigation.setParams({
          title: this.props.currentUser.full_name,
        });
      }
      if (this.props.currentUser.enroll_wakeup_workout) {
        trainerData.enrolled_in_wakeup_workout = `${!!this.props.currentUser
          .enroll_wakeup_workout}`;
      }
      this.props.registerForPush(this.props.currentUser, trainerData);
    } else {
      this.props.navigation.setParams({
        title: this.props.selectedClient.full_name,
        clearDay: this.props.clearDay,
      });
    }
  }

  // Update Selected Client Name Top Center Header
  componentDidUpdate(prevProps) {
    if (this.props.currentUser.role === ROLES.TRAINER) {
      if (
        this.props.selectedClient
        && prevProps.selectedClient
        && this.props.selectedClient.full_name
          !== prevProps.selectedClient.full_name
      ) {
        this.props.navigation.setParams({
          title: this.props.selectedClient.full_name,
        });
      }
    }
  }

  componentWillUnmount() {
    this.props.deselectClient();
  }

  renderTabBarLabel = (label, focused, isBadged) => (
    <View style={styles.tabBarLabel}>
      <Text
        allowFontScaling={false}
        style={{
          ...materialTabBarOptions.tabBarOptions.tabBarLabelStyle,
          color: focused ? colors.white : colors.inactiveWhite,
        }}
      >
        {label}
      </Text>
      {isBadged && <View style={styles.tabIndicator} />}
    </View>
  );

  render() {
    const hasNotch = DeviceInfo.hasNotch();
    const isTrainer = this.props.currentUser.role === ROLES.TRAINER;
    const tabStyle = !isTrainer && !this.state.isWalkIn && !this.state.connectedMode
      ? {
        paddingTop:
              Platform.OS === 'android' || (Platform.OS === 'ios' && hasNotch)
                ? 28
                : 17,
      }
      : {};
    const tabWidth = width / (this.state.isWalkIn ? 2 : 3);
    const indicatorWidth = tabWidth / 1.2;
    const backgroundColor = this.props.trainerActiveProfile?.ClubId
      ? colors.black
      : colors.duskBlue;

    return (
      <TabNav.Navigator
        tabBar={
          this.state.isWalkIn || this.state.connectedMode
            ? () => null
            : undefined
        }
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarStyle,
            backgroundColor,
            ...tabStyle,
          },
          tabBarActiveTintColor: colors.white,
          tabBarInactiveTintColor: '#8aa1bd',
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            backgroundColor: colors.medYellow,
            width: indicatorWidth,
            // Based of https://github.com/satya164/react-native-tab-view/issues/944#issuecomment-599224375
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
          lazy: false,
        }}
        style={{
          ...materialTabBarOptions.tabBarOptions.tabBarStyle,
          ...tabStyle,
        }}
      >
        <TabNav.Screen
          name="Schedule"
          component={DashboardClient}
          options={{
            tabBarLabel: ({ focused }) => this.renderTabBarLabel('Schedule', focused),
            tabBarTestID: 'ClientTabSchedule',
          }}
        />
        <TabNav.Screen
          name="Dashboard"
          component={ClientOverview}
          options={{
            tabBarLabel: ({ focused }) => this.renderTabBarLabel('Dashboard', focused),
            tabBarTestID: 'ClientTabDashboard',
          }}
        />
        {!this.state.isWalkIn && !isTrainer && (
          <TabNav.Screen
            name="Chat"
            component={ChannelListScreen}
            params={{ isBadged: this.props.route.params?.isBadged }}
            options={{
              tabBarLabel: ({ focused }) => {
                const isBadged = this.props.route.params?.isBadged;
                return this.renderTabBarLabel('Chat', focused, isBadged);
              },
              tabBarTestID: 'ClientTabChat',
            }}
          />
        )}
        {isTrainer && (
          <TabNav.Screen
            name="Profile"
            component={ProfileClient}
            options={{
              tabBarTestID: 'ClientTabProfile',
            }}
          />
        )}
      </TabNav.Navigator>
    );
  }
}

ClientHomeTabs.propTypes = propTypes;

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  selectedClient: state.selectedClient,
  trainerActiveProfile: state.trainerActiveProfile,
});

const mapDispatchToProps = {
  registerForPush,
  showHomeVideoIfAble,
  clearDay,
  deselectClient,
};

const styles = StyleSheet.create({
  tabBarLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tabIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.medYellow,
    marginLeft: 5,
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(ClientHomeTabs);
