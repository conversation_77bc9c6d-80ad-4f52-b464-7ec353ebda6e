import React, { Component } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import _cloneDeep from 'lodash.clonedeep';
import analytics from '@react-native-firebase/analytics';
import * as RNLocalize from 'react-native-localize';

import {
  View, StyleSheet, Text, TouchableOpacity, Alert,
} from 'react-native';

import {
  PageContainer,
  AvatarPicker,
  TextInput,
  DatePicker,
  DropDownPicker,
} from '../../../components';

import CountryPicker from '../../../components/CountryPicker/CountryPicker';

import { selectClient, updateCurrentUser, reset } from '../../../actions';

import LoadingSpinner from '../../../components/LoadingSpinner';

import nasm from '../../../dataManager/apiConfig';

import * as validate from '../../../util/validate';
import { chatClient, ROLES } from '../../../constants';

import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';

import { getCountryCode } from '../../../util/utils';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

// Images
const plusIcon = require('../../../resources/plusIcon.png');

const propTypes = {
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
    role: PropTypes.string,
    unit_height: PropTypes.string,
    unit_weight: PropTypes.string,
    account_removal_requested: PropTypes.bool,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    dispatch: PropTypes.func,
    state: PropTypes.object,
    goBack: PropTypes.func,
    setParams: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.object,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    avatar_url: PropTypes.string,
  }).isRequired,

  // dispatch functions injected via redux connect function
  selectClient: PropTypes.func,
  updateCurrentUser: PropTypes.func,
};

const currentCountry = RNLocalize.getCountry();

class EditProfile extends Component {
  constructor(props) {
    super(props);

    this.state = {
      firstName: '',
      lastName: '',
      phone: '',
      prevPhone: '',
      birthDate: null,
      feet: null,
      inches: null,
      cms: null,
      gender: null,
      unitHeight: props.currentUser.unit_height,
      currentUser: null,
      profileData: null,
      trainerData: {},
      photoURL: props.selectedClient?.avatar_url,
      pendingImgBlob: null,
      isLoading: true,
      countryCode: getCountryCode(currentCountry),
      countryISO: currentCountry,
    };
  }

  componentDidMount() {
    this.props.navigation.setParams({
      role: this.props.currentUser.role,
      renderHeaderLeft: this.renderHeaderLeft,
      renderHeaderRight: this.renderHeaderRight,
    });

    this.pageSetup();
    analytics().logEvent('screen_view', { screen_name: 'edit_profile' });
  }

  onPressSave = () => {
    const areFieldsValid = this.validateFields();
    if (areFieldsValid) {
      this.updateProfileInfo();
    }
  };

  onTextBlur = (text, field) => {
    if (text) {
      this.setState({ [field]: text.trim() });
    }
  };

  getProfileInfo = async () => {
    const client = await nasm.api.getUserById(this.props.selectedClient?.id);
    this.props.selectClient(client);

    let gender;
    let birthDate;
    if (client.gender_type) {
      gender = client.gender_type;
    }

    if (client.birth_date) {
      birthDate = moment(client.birth_date).toDate();
    }

    const { height } = client.client_user;
    let feet = null;
    let inches = null;
    const cms = height;
    if (height) {
      feet = Math.floor(height / 12);
      inches = height - feet * 12;
    }

    this.setState({
      firstName: client.first_name,
      lastName: client.last_name,
      phone: client.phone_number,
      feet: feet != null ? feet.toString() : null,
      inches: inches != null ? inches.toString() : null,
      cms: cms != null ? cms.toString() : null,
      gender,
      birthDate,
    });

    if (client.phone_number) {
      const countryObj = validate.getCountryISO(client.phone_number);
      const countryISO = countryObj !== undefined ? countryObj.country : '';

      if (countryISO.length > 0) {
        this.setState({
          countryCode: getCountryCode(countryISO),
          countryISO,
        });
      }
    }
  };

  validateFields = () => {
    const {
      firstName, lastName, feet, inches, phone,
    } = this.state;
    if (
      !validate.name(firstName)
      || validate.containsAccentedChars(firstName)
    ) {
      Alert.alert(
        'First name invalid',
        'Please enter a valid first name to continue.',
      );
      return false;
    }
    if (!validate.name(lastName) || validate.containsAccentedChars(lastName)) {
      Alert.alert(
        'Last name invalid',
        'Please enter a valid last name to continue.',
      );
      return false;
    }
    if (phone && phone !== 0 && !validate.phone(phone)) {
      Alert.alert(
        'Phone number invalid',
        'Please enter a valid phone number to continue.',
      );
      return false;
    }
    if ((feet || inches) && !validate.heightFeet(feet)) {
      Alert.alert(
        'Height Invalid',
        'Please enter a value for feet that is greater than 0.',
      );
      return false;
    }
    if ((feet || inches) && !validate.heightInches(inches)) {
      Alert.alert(
        'Height Invalid',
        'Please enter a value for inches between 0 and 11.',
      );
      return false;
    }

    return true;
  };

  updateProfileInfo = () => {
    const updatedProfile = _cloneDeep(this.props.selectedClient);
    const height = this.state.unitHeight === 'in'
      ? parseInt(this.state.feet, 10) * 12 + parseInt(this.state.inches, 10)
      : this.state.cms;
    updatedProfile.first_name = this.state.firstName;
    updatedProfile.last_name = this.state.lastName;
    updatedProfile.phone_number = this.state.phone;
    updatedProfile.height = height;
    updatedProfile.birth_date = this.state.birthDate;
    updatedProfile.gender_type = this.state.gender;
    updatedProfile.countryCode = this.state.countryCode;
    try {
      this.props.navigation.setParams({ isLoading: true });
      this.setState({ isLoading: true }, async () => {
        if (this.state.pendingImgBlob != null) {
          await this.saveProfilePicture(this.state.pendingImgBlob);
        }

        const user = await nasm.api.updateClientUser(
          updatedProfile,
          this.props.selectedClient?.id,
        );

        const update = {
          id: user.id,
          name: `${user.first_name} ${user.last_name}`,
          image: user.avatar_url,
        };

        if (this.props.route.params.role === ROLES.CLIENT) {
          await chatClient.upsertUser(update);
        }

        this.props.navigation.goBack();
        analytics().logEvent('edit_profile_client');
      });
    } catch (error) {
      this.setState({ isLoading: false }, () => {
        Alert.alert(
          'Error',
          error.message
            || 'Unable to save profile data. Please try again later.',
        );
      });
      this.props.navigation.setParams({ isLoading: false });
    }
  };

  scrollToBottom = () => {
    if (this.editPageContainer && this.editPageContainer.keyboardHandler) {
      this.editPageContainer.keyboardHandler.scrollToEnd();
    }
  };

  updateInput = (nextState) => {
    const [field, value] = Object.entries(nextState)[0];
    this.setState({ [field]: value });
  };

  updatePhoneNumber = (phoneNumber, isCountrySelected) => {
    let phone_number = phoneNumber;
    const number = phone_number
      .substr(phone_number.indexOf(' ') + 1)
      .replace(/\s/g, '');
    if (number.length === 10) {
      phone_number = validate.formatPhoneNumber(number, this.state.countryCode);
    } else if (isCountrySelected) {
      phone_number = validate.formatPhoneNumber(number, this.state.countryCode);
    }
    this.setState({ prevPhone: phone_number, phone: phone_number });
  };

  updateProfilePicture = (image) => {
    if (image && image.uri) {
      this.setState({ photoURL: image.uri, pendingImgBlob: image });
    }
  };

  saveProfilePicture = async (image) => {
    try {
      const avatarResult = await nasm.api.updateUserAvatar(
        this.props.selectedClient?.id,
        image,
      );
      if (avatarResult.url) {
        const client = this.props.selectedClient;
        client.avatar_url = avatarResult.url;
        this.props.selectClient(client);
        if (this.props.currentUser.role === ROLES.CLIENT) {
          this.props.updateCurrentUser(client);
        }
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error.message
          || 'Unable to update profile picture. Please try again later.',
      );
    }
  };

  pageSetup = async () => {
    try {
      await this.getProfileInfo();
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      this.setState({ isLoading: false });
    }
  };

  static navigationOptions = ({ route }) => {
    const role = route.params?.role;
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);

    if (role && role === ROLES.CLIENT) {
      return {
        title: 'Account Settings',
        headerRight: () => renderHeaderRight(route.params),
        headerLeft: () => renderHeaderLeft(route.params),
      };
    }
    if (role && role === ROLES.TRAINER) {
      return {
        title: 'Edit Profile',
        headerRight: () => renderHeaderRight(route.params),
        headerLeft: () => renderHeaderLeft(route.params),
      };
    }
    return {};
  };

  showRemoveClientConfirmationAlert = () => {
    Alert.alert(
      'Are You Sure?',
      'Removing a client will delete their profile from your dashboard.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'default',
          onPress: () => {
            this.removeClient();
          },
        },
      ],
    );
  };

  removeClient = () => {
    this.setState(
      {
        isLoading: true,
      },
      () => {
        const clientId = this.props.selectedClient?.id;
        nasm.api
          .deleteTrainerClient(clientId)
          .then(async () => {
            const channels = await chatClient.queryChannels({
              type: 'messaging',
              members: [this.props.currentUser.id, clientId],
            });
            channels.forEach((channel) => {
              channel.delete();
            });
            reset(this.props.navigation, 'ModalStack');
          })
          .catch((error) => {
            this.setState({ isLoading: false }, () => {
              Alert.alert(
                'Error',
                error.message
                  || 'Failed To remove client. Please try again later.',
              );
            });
          });
      },
    );
  };

  formatPhoneNumber = (phone, isCountrySelected) => {
    const currentDigits = validate.getDigits(phone);

    if (phone) {
      const number = phone.substr(phone.indexOf(' ') + 1).replace(/\s/g, '');
      if (this.state.prevPhone) {
        if (
          phone.length > this.state.prevPhone.length
          && currentDigits.length === 10
        ) {
          return validate.formatPhoneNumber(number, this.state.countryCode);
        }
        return phone;
      }
      if (
        phone.length > 0
        && currentDigits.length >= 10
        && isCountrySelected === undefined
      ) {
        const formattedNumber = validate.formatPhoneNumber(
          number,
          this.state.countryCode,
        );
        this.state.phone = formattedNumber;
        return validate.formatPhoneNumber(number, this.state.countryCode);
      }

      return phone;
    }
    return '';
  };

  selectedValue = (index) => {
    this.setState({ countryCode: index }, () => (this.state.prevPhone
      ? this.updatePhoneNumber(this.state.prevPhone, true)
      : this.formatPhoneNumber(this.state.phone, true)));
  };

  checkDeletionRequest = () => {
    if (this.props.currentUser.account_removal_requested) {
      Alert.alert(
        'Error',
        'The process to delete your NASM Edge account has been initiated. Account deletion may take 7-10 business days, until then you can still access your account.',
      );
      return;
    }
    this.props.navigation.navigate('DeleteAccount');
  };

  renderHeaderLeft = (params) => {
    if (!params) {
      return <View />;
    }

    return (
      <HeaderLeftButton
        onPress={() => this.props.navigation.goBack()}
        title="Cancel"
      />
    );
  };

  renderHeaderRight = (params) => {
    if (!params) {
      return <View />;
    }

    return (
      <HeaderRightButton
        onPress={() => this.onPressSave()}
        disabled={params.isLoading}
        title="Save"
        titleStyle={styles.headerButtonText}
      />
    );
  };

  render() {
    if (this.state.isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <LoadingSpinner visible />
        </View>
      );
    }

    return (
      <PageContainer
        ref={(ref) => {
          this.editPageContainer = ref;
        }}
        containerStyle={styles.pageContainerStyle}
        scrollEnabled
      >
        <View style={styles.profilePicContainer}>
          <AvatarPicker
            onSelectImage={(image) => this.updateProfilePicture(image)}
            iconSize={30}
            disabled={false}
            imageUri={this.state.photoURL}
            containerStyle={styles.avatarPickerContainerStyle}
            icon={plusIcon}
            iconContainerStyle={styles.avatarPickerIconContainerStyle}
            shadow={false}
            navigation={this.props.navigation}
          />
        </View>
        <Text style={styles.editHeaderTextStyle}>Email</Text>
        <View style={styles.emailTextContainer}>
          <Text style={styles.emailText}>
            {this.props.selectedClient?.email}
          </Text>
        </View>
        <Text style={styles.editHeaderTextStyle}>First Name</Text>
        <TextInput
          value={this.state.firstName}
          onChangeText={(value) => {
            const firstName = validate.removeAllSpecialCharacters(value);
            this.updateInput({ firstName: firstName.trimLeft() });
          }}
          onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'firstName')}
          placeholder="FIRST NAME"
          returnKeyType="done"
          validation={validate.name}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(this.state.firstName)
              ? validate.accentValidationErrorMsg('first name')
              : ''
          }
          maxLength={64}
        />
        <Text style={styles.editHeaderTextStyle}>Last Name</Text>
        <TextInput
          value={this.state.lastName}
          onChangeText={(value) => {
            const lastName = validate.removeAllSpecialCharacters(value);
            this.updateInput({ lastName: lastName.trimLeft() });
          }}
          onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'lastName')}
          placeholder="LAST NAME"
          returnKeyType="done"
          validation={validate.name}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(this.state.lastName)
              ? validate.accentValidationErrorMsg('last name')
              : ''
          }
          maxLength={64}
        />
        <Text style={styles.editHeaderTextStyle}>Country</Text>
        <TouchableOpacity testID="CountryPicker">
          <CountryPicker
            testID="CountryList"
            disable={false}
            animationType="slide"
            containerStyle={styles.pickerContainerStyle}
            hideCountryFlag
            hideCountryCode
            searchBarStyle={styles.searchBarStyle}
            selectedValue={this.selectedValue}
            country={this.state.countryISO}
          />
        </TouchableOpacity>
        <Text style={styles.editHeaderTextStyle}>Mobile Number</Text>
        <TextInput
          testID="MobileNumberInput"
          value={this.formatPhoneNumber(this.state.phone)}
          onChangeText={(phone) => this.updatePhoneNumber(phone)}
          onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'phone')}
          placeholder="### ### ####"
          placeholderTextColor={colors.subGreyTwo}
          keyboardType="phone-pad"
          returnKeyType="done"
          validation={validate.phone}
          maxLength={20}
        />
        <Text style={styles.editHeaderTextStyle}>Height</Text>
        <View style={styles.heightContainer}>
          <View style={styles.innerHeightContainer}>
            <Text style={styles.unitHeightText}>
              {this.state.unitHeight === 'in' ? 'Feet' : 'Centimeters'}
            </Text>
            <TextInput
              testID="HeightFeetInput"
              value={
                this.state.unitHeight === 'in'
                  ? this.state.feet
                  : this.state.cms
              }
              onChangeText={
                this.state.unitHeight === 'in'
                  ? (feet) => this.updateInput({ feet })
                  : (cms) => this.updateInput({ cms })
              }
              onBlur={(event) => {
                this.onTextBlur(
                  event.nativeEvent.text,
                  this.state.unitHeight === 'in' ? 'feet' : 'cms',
                );
              }}
              placeholder="5"
              keyboardType="number-pad"
              returnKeyType="done"
              validation={
                this.state.unitHeight === 'in'
                  ? validate.heightFeet
                  : validate.heightCms
              }
              maxLength={this.state.unitHeight === 'in' ? 1 : 3}
            />
          </View>
          {this.state.unitHeight === 'in' && (
            <View style={styles.inchContainer}>
              <Text style={styles.inchText}>
                {this.state.unitHeight === 'in' ? 'Inches' : ''}
              </Text>
              <TextInput
                testID="HeightInchInput"
                value={this.state.inches}
                onChangeText={(inches) => this.updateInput({ inches })}
                onBlur={(event) => {
                  this.onTextBlur(event.nativeEvent.text, 'inches');
                }}
                placeholder="0"
                keyboardType="number-pad"
                returnKeyType="done"
                validation={validate.heightInches}
                maxLength={2}
              />
            </View>
          )}
        </View>
        <Text style={styles.editHeaderTextStyle}>Birthday</Text>
        <DatePicker
          testID="DatePicker"
          value={this.state.birthDate}
          placeholder="Birthday"
          maxValue={new Date()}
          onValueChange={(birthDate) => this.setState({ birthDate })}
          onIosPickerVisible={(visible) => {
            if (visible) this.scrollToBottom();
          }}
          defaultDate={moment().subtract(18, 'years').toDate()}
        />
        <Text style={styles.editHeaderTextStyle}>Assigned Sex</Text>
        <DropDownPicker
          testID="GenderPicker"
          containerStyle={styles.inputUnderline}
          useNativeAndroidPickerStyle
          data={genderTypes}
          placeholder="Assigned Sex"
          selected={this.state.gender}
          onValueChange={(gender) => this.setState({ gender: gender.id })}
          onIosPickerVisible={(visible) => {
            if (visible) this.scrollToBottom();
          }}
          textStyle={{
            textAlign: 'left',
          }}
        />
        {this.props.currentUser.role === ROLES.TRAINER && (
          <TouchableOpacity
            testID="removeClient"
            style={styles.profileActionButton}
            onPress={this.showRemoveClientConfirmationAlert}
          >
            <Text style={styles.profileActionButtonText}>Remove Client</Text>
          </TouchableOpacity>
        )}
        {this.props.currentUser.role === ROLES.CLIENT && (
          <>
            <TouchableOpacity
              style={[styles.profileActionButton, { width: 157 }]}
              onPress={() => this.props.navigation.navigate('ForgotPassword')}
            >
              <Text style={styles.profileActionButtonText}>Reset Password</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.deleteContainer}
              onPress={this.checkDeletionRequest}
            >
              <Text style={styles.deleteText}>Delete Account</Text>
            </TouchableOpacity>
          </>
        )}
      </PageContainer>
    );
  }
}

const styles = StyleSheet.create({
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  profilePicContainer: {
    flex: 0,
    borderRadius: 66,
    alignSelf: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  editHeaderTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    color: colors.subGrey,
    marginTop: 28,
    marginBottom: 5,
  },
  emailTextContainer: {
    height: 46,
    borderColor: colors.subGreyLight,
    borderBottomWidth: 1,
    justifyContent: 'center',
  },
  emailText: {
    fontFamily: 'Avenir',
    fontSize: curvedScale(14),
    fontWeight: '500',
    color: colors.subGrey,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  heightContainer: {
    flexDirection: 'row',
    flex: 1,
    marginTop: 10,
  },
  innerHeightContainer: {
    flex: 1,
    marginRight: 15,
  },
  unitHeightText: {
    color: colors.subGreyTwo,
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
  },
  profileActionButton: {
    width: 125,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.subGrey,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginBottom: 28,
    marginTop: 28,
  },
  profileActionButtonText: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    color: colors.subGrey,
  },
  pageContainerStyle: {
    backgroundColor: colors.white,
    marginHorizontal: '2%',
  },
  avatarPickerContainerStyle: {
    borderWidth: 0,
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPickerIconContainerStyle: {
    backgroundColor: colors.macaroniAndCheese,
    width: 33,
    height: 33,
    borderRadius: 16.5,
  },
  inchContainer: { flex: 1 },
  inchText: {
    color: colors.subGreyTwo,
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
  },
  searchBarStyle: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'row',
    marginRight: 10,
    marginLeft: 20,
  },
  pickerContainerStyle: {
    borderBottomWidth: 1,
    borderBottomColor: colors.silver,
    flex: 1,
    paddingTop: 20,
    paddingBottom: 12,
    marginBottom: 10,
  },
  deleteContainer: {
    alignItems: 'center',
  },
  deleteText: {
    textDecorationLine: 'underline',
    color: colors.subGrey,
  },
});

// Picker Data
const genderTypes = [
  { label: 'Male', id: 1 },
  { label: 'Female', id: 2 },
];

// Export
EditProfile.propTypes = propTypes;
const mapStateToProps = (state) => ({
  selectedClient: state.selectedClient,
  currentUser: state.currentUser,
});
const mapDispatchToProps = { selectClient, updateCurrentUser };
export default connect(mapStateToProps, mapDispatchToProps)(EditProfile);
