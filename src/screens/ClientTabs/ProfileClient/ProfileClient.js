// Libraries
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import _cloneDeep from 'lodash.clonedeep';

import IconFeader from 'react-native-vector-icons/Feather';
// Imports
import {
  Alert,
  LayoutAnimation,
  Platform,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native';
import moment from 'moment';
import analytics from '@react-native-firebase/analytics';
import {
  selectClient,
  updateCurrentUser,
  startAssessment,
} from '../../../actions';
import nasm from '../../../dataManager/apiConfig';

// Components
import { ContactCard, LoadingSpinner } from '../../../components';

import {
  ROLES,
  chatClient,
  APPLICATION_ROUTES,
  FEATURE_FLAGS,
  androidSafeLayoutAnimation,
} from '../../../constants';
import * as validate from '../../../util/validate';
import BottomBannerActionView from '../../../components/BottomBannerActionView';
import {
  CALCULATOR_CONTEXTS,
  setCalculatorContext,
} from '../../../reducers/calculatorContextReducer';

// Styles
import { colors } from '../../../styles';
import Button from '../../../components/Button';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

// PropTypes
const propTypes = {
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
    role: PropTypes.string,
    unit_height: PropTypes.string,
    client_user: PropTypes.object,
    permissions: PropTypes.array,
    bypass_subscription: PropTypes.bool,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    dispatch: PropTypes.func,
    state: PropTypes.object,
    goBack: PropTypes.func,
    setParams: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.func,
  }).isRequired,
  onClientUpdated: PropTypes.func,
  selectClient: PropTypes.func,
  setCalculatorContext: PropTypes.func,
};

const defaultProps = {
  onClientUpdated: null,
  selectClient: null,
  setCalculatorContext: null,
};

// Class
class ProfileClient extends Component {
  constructor(props) {
    super(props);

    this.state = {
      firstname: '',
      lastname: '',
      phone: '',
      birthdate: null,
      feet: null,
      inches: null,
      cms: null,
      gender: null,
      unit_height: props.currentUser.unit_height,
      profileData: null,
      trainerData: {},
      isLoading: true,
      shouldResendInvite: false,
      isResendingClientInvitation: false,
    };
    this.defaultSections = [
      {
        title: 'Payment Method',
        destination: APPLICATION_ROUTES.ADD_CREDIT_CARD,
        isPro: true,
      },
      {
        title: 'Dashboard Settings',
        destination: 'DashboardSettingsScreen',
        isPro: false,
      },
      {
        title: 'Calculators',
        destination: 'Calculators',
      },
      {
        title: 'Bookings',
        destination: APPLICATION_ROUTES.LIST_CLIENT_BOOKINGS,
        isPro: true,
      },
      {
        title: 'Subscriptions',
        destination: APPLICATION_ROUTES.CLIENT_SUBSCRIPTIONS,
        isPro: true,
      },
      {
        title: 'Transactions & Balances',
        destination: APPLICATION_ROUTES.CLIENT_TRANSACTIONS,
        isPro: true,
      },
    ];
    this.renderSections = this.renderSections.bind(this);
    this.renderRow = this.renderRow.bind(this);
  }

  componentDidMount() {
    this.props.navigation.setParams({
      role: this.props.currentUser.role,
      isLoading: this.state.isLoading,
      renderHeaderLeft: this.renderHeaderLeft,
      renderHeaderRight: this.renderHeaderRight,
    });

    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      this.pageSetup();
      analytics().logEvent('screen_view', { screen_name: 'profile_client' });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressEditProfile = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.props.navigation.navigate('EditProfile', {
      role: this.props.currentUser.role,
    });
  };

  onPressResendClientInvite = () => {
    this.setState({ isResendingClientInvitation: true }, () => {
      nasm.api
        .resendClientInvite(this.props.selectedClient?.id)
        .then(() => {
          this.setState({ shouldResendInvite: false });
        })
        .catch((error) => {
          Alert.alert(
            'Error',
            error.message
              || 'Unable to resend invitation. Please try again later.',
          );
        })
        .finally(() => {
          this.setState({ isResendingClientInvitation: false });
        });
    });
  };

  onPressSave = () => {
    // Check if all inputs are valid
    const { feet, inches, phone } = this.state;
    if (phone && phone !== 0 && !validate.phone(phone)) {
      Alert.alert(
        'Phone number invalid',
        'Please enter a valid phone number to continue.',
      );
      return;
    }
    if ((feet || inches) && !validate.heightFeet(feet)) {
      Alert.alert(
        'Height Invalid',
        'Please enter a value for feet that is greater than 0.',
      );
      return;
    }
    if ((feet || inches) && !validate.heightInches(inches)) {
      Alert.alert(
        'Height Invalid',
        'Please enter a value for inches between 0 and 11.',
      );
      return;
    }

    // If all fields are valid update the profile
    this.updateProfileInfo();
  };

  getProfileInfo = async () => {
    const client = await nasm.api.getUserById(this.props.selectedClient?.id);
    // Make sure redux gets the latest data as other screen depend on it
    this.props.selectClient(client);
    if (this.props.onClientUpdated) {
      this.props.onClientUpdated(client);
    }

    // Set up state data
    let { trainerData } = this.state;
    if (this.props.currentUser.role === ROLES.TRAINER) {
      trainerData = this.props.currentUser;
    }
    let gender;
    let birthdate;
    if (client.gender_type) {
      gender = client.gender_type;
    }
    if (client.birth_date) {
      birthdate = moment(client.birth_date).toDate();
    }
    let shouldResendInvite = false;
    if (
      this.props.route.params
      && this.props.route.params.role === ROLES.TRAINER
    ) {
      shouldResendInvite = !client.has_set_password;
    }

    const { height } = client.client_user;
    let feet = null;
    let inches = null;
    const cms = height;
    if (height) {
      feet = Math.floor(height / 12);
      inches = height - feet * 12;
    }
    this.setState({
      profileData: client,
      firstname: client.first_name,
      lastname: client.last_name,
      phone: client.phone_number,
      feet: feet != null ? feet.toString() : null,
      inches: inches != null ? inches.toString() : null,
      cms: cms != null ? cms.toString() : null,
      gender,
      birthdate,
      trainerData,
      shouldResendInvite,
    });
  };

  getTrainerInfo = async () => {
    let trainer = {};
    if (this.props.currentUser.client_user.trainer) {
      trainer = await nasm.api.getUserById(
        this.props.currentUser.client_user.trainer.user_id,
      );
    }
    this.setState({ trainerData: trainer });
  };

  pageSetup = async () => {
    try {
      await this.getProfileInfo();
      if (this.props.currentUser.role === ROLES.CLIENT) {
        await this.getTrainerInfo();
      }
      if (Platform.OS === 'ios') {
        LayoutAnimation.configureNext(androidSafeLayoutAnimation);
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      this.setState({ isLoading: false });
      this.props.navigation.setParams({ isLoading: false });
    }
  };

  resetEditableData = () => {
    const { profileData: client } = this.state;

    const { height } = client.client_user;
    let feet;
    let inches;
    const cms = height;
    if (height) {
      feet = Math.floor(height / 12);
      inches = height - feet * 12;
    }

    let gender;
    if (client.gender_type) {
      gender = client.gender_type;
    }

    let birthdate;
    if (client.birth_date) {
      birthdate = moment(client.birth_date).toDate();
    }

    this.setState({
      firstname: client.first_name,
      lastname: client.last_name,
      phone: client.phone_number,
      feet: feet ? feet.toString() : null,
      inches: inches ? inches.toString() : null,
      cms: cms ? cms.toString() : null,
      gender,
      birthdate,
    });
  };

  scrollToBottom = () => {
    if (this.editPageContainer && this.editPageContainer.keyboardHandler) {
      this.editPageContainer.keyboardHandler.scrollToEnd();
    }
  };

  isTrainerAvailable() {
    return this.state.trainerData && this.state.trainerData.id;
  }

  updateProfileInfo() {
    const updatedProfile = _cloneDeep(this.props.selectedClient);
    const height = this.state.unit_height === 'in'
      ? parseInt(this.state.feet, 10) * 12 + parseInt(this.state.inches, 10)
      : this.state.cms;
    if (this.state.profileData) {
      if (this.state.firstname !== this.state.profileData.first_name) {
        updatedProfile.first_name = this.state.firstname;
      }
      if (this.state.lastname !== this.state.profileData.last_name) {
        updatedProfile.last_name = this.state.lastname;
      }
      if (this.state.phone !== this.state.profileData.phone_number) {
        updatedProfile.phone_number = this.state.phone;
      }
      if (height !== this.state.profileData.height) {
        updatedProfile.height = height;
      }
      if (this.state.birthdate !== this.state.profileData.birth_date) {
        updatedProfile.birth_date = this.state.birthdate;
      }
      if (this.state.gender !== this.state.profileData.gender_type) {
        updatedProfile.gender_type = this.state.gender;
      }
    }

    try {
      this.setState({ isLoading: true }, async () => {
        const user = await nasm.api.updateClientUser(
          updatedProfile,
          this.state.profileData.id,
        );
        const update = {
          id: user.id,
          name: `${user.first_name} ${user.last_name}`,
          image: user.avatar_url,
        };

        if (this.props.route.params.role === ROLES.CLIENT) {
          await chatClient.upsertUser(update);
        }
        this.props.navigation.goBack();
        analytics().logEvent('edit_profile_client');
      });
    } catch (error) {
      this.setState({ isLoading: false }, () => {
        Alert.alert(
          'Error',
          error.message
            || 'Unable to save profile data. Please try again later.',
        );
      });
    }
  }

  renderEmailVerificationBanner() {
    if (this.state.shouldResendInvite) {
      return (
        <BottomBannerActionView
          buttonTitle="Resend Invite"
          isLoading={this.state.isResendingClientInvitation}
          onPress={this.onPressResendClientInvite}
          title="Email Validation Needed"
        />
      );
    }
    return null;
  }

  renderHeaderLeft = (params) => {
    if (!params) {
      return <View />;
    }
    if (params.editEnabled || (params.role && params.role === ROLES.CLIENT)) {
      return (
        <HeaderLeftButton
          onPress={() => this.props.navigation.goBack()}
          title="Cancel"
        />
      );
    }
    return <View />;
  };

  renderHeaderRight = (params) => {
    if (params && params.editEnabled) {
      return (
        <HeaderRightButton
          onPress={this.onPressSave}
          disabled={params.isLoading}
          title="Save"
          titleStyle={styles.headerButtonText}
        />
      );
    }
    return <View />;
  };

  renderRow(item, idx) {
    return (
      <TouchableOpacity
        key={idx}
        style={styles.sectionsContainer}
        onPress={() => {
          if (!item.destination) return;
          if (item.destination === 'Calculators') {
            this.props.setCalculatorContext(
              CALCULATOR_CONTEXTS.SELECTED_CLIENT,
            );
          }
          this.props.navigation.navigate({
            name: item.destination,
            params: item.params,
            merge: true,
          });
        }}
      >
        <Text style={styles.sectionTitle}>{item.title}</Text>
        <IconFeader
          style={styles.arrowRight}
          name="chevron-right"
          size={27}
          color={colors.fillDarkGrey}
        />
      </TouchableOpacity>
    );
  }

  renderSections() {
    const hasTrainerPro = this.props.currentUser.bypass_subscription
      || this.props.currentUser.permissions?.some(
        (p) => p.name === 'edge_trainer_pro',
      );
    if (
      this.props.trainerActiveProfile?.ClubId
      || !FEATURE_FLAGS.TRAINER_PRO_ENABLED
      || !hasTrainerPro
    ) {
      return this.defaultSections.filter((s) => !s.isPro).map(this.renderRow);
    }
    return this.defaultSections.map(this.renderRow);
  }

  renderUserInfo = () => {
    const startDate = this.state.profileData.client_user
      ? this.state.profileData.client_user.started_training_at
      : undefined;
    return (
      <View>
        <ContactCard
          name={this.state.profileData.full_name}
          imageUrl={this.state.profileData.avatar_url}
          phone={this.state.profileData.phone_number}
          email={this.state.profileData.email}
          startDate={startDate}
          isCard={false}
          style={styles.contactsCard}
          hideButtons={this.props.currentUser.role === ROLES.CLIENT}
          profileBorderColor="transparent"
        />
      </View>
    );
  };

  render() {
    if (!this.state.profileData || this.state.isLoading) {
      return (
        <View style={styles.loaderView}>
          <LoadingSpinner visible />
        </View>
      );
    }

    return (
      <View style={styles.container}>
        <ScrollView>
          {this.renderUserInfo()}
          <View style={styles.divider} />
          {this.renderSections()}
        </ScrollView>
        <Button
          title="Edit Profile"
          testID="EditProfile"
          containerStyle={styles.editClientButton}
          textStyles={styles.textEditProfile}
          onPress={this.onPressEditProfile}
        />
        {this.renderEmailVerificationBanner()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  editClientButton: {
    alignSelf: 'center',
    borderRadius: 30,
    borderWidth: 1,
    width: '80%',
    height: 50,
    backgroundColor: colors.white,
    marginBottom: 40,
    borderColor: colors.fillDarkGrey,
  },
  sectionsContainer: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    borderColor: colors.bordergrey,
    borderBottomWidth: 1,
    padding: 20,
    alignItems: 'center',
  },
  sectionTitle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    color: colors.black,
    flex: 1,
  },
  arrowRight: {
    alignSelf: 'flex-end',
  },
  contactsCard: {
    marginVertical: 15,
  },
  loaderView: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: colors.bordergrey,
  },
  textEditProfile: {
    fontFamily: 'Avenir-Heavy',
    color: colors.fillDarkGrey,
    fontSize: 40,
  },
});

// Export
ProfileClient.propTypes = propTypes;
ProfileClient.defaultProps = defaultProps;

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  selectedClient: state.selectedClient,
  trainerActiveProfile: state.trainerActiveProfile,
});

const mapDispatchToProps = {
  selectClient,
  startAssessment,
  updateCurrentUser,
  setCalculatorContext,
};

export default connect(mapStateToProps, mapDispatchToProps)(ProfileClient);
