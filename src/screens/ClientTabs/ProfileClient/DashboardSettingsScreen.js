import React, { useState } from 'react';
import { View, Alert } from 'react-native';
import { connect } from 'react-redux';
import nasm from '../../../dataManager/apiConfig';
import DashboardSettings from '../../../components/DashboardSettings';

function DashboardSettingsScreen(props) {
  const [preferences, setPreferences] = useState({
    visibleGoals: props.selectedClient?.client_user.visible_goals,
    visibleDailyReadiness:
      props.selectedClient?.client_user.visible_daily_readiness,
    visibleMeasures: props.selectedClient?.client_user.visible_measures,
    visibleNutrition: props.selectedClient?.client_user.visible_nutrition,
    visibleOhsa: props.selectedClient?.client_user.visible_ohsa,
    visiblePerfAssess: props.selectedClient?.client_user.visible_perf_assess,
  });

  const onVisibilityPrefChanged = async (pref, newValue) => {
    try {
      const userId = props.selectedClient?.client_user.user_id;
      const data = { [pref]: newValue };
      const response = await nasm.api.updateClientPreferences(userId, data);
      const visibleGoals = response.client_user.visible_goals;
      const visibleDailyReadiness = response.client_user.visible_daily_readiness;
      const visibleMeasures = response.client_user.visible_measures;
      const visibleNutrition = response.client_user.visible_nutrition;
      const visibleOhsa = response.client_user.visible_ohsa;
      const visiblePerfAssess = response.client_user.visible_perf_assess;
      setPreferences({
        visibleGoals,
        visibleDailyReadiness,
        visibleMeasures,
        visibleNutrition,
        visibleOhsa,
        visiblePerfAssess,
      });
    } catch (error) {
      let {
        visibleGoals,
        visibleDailyReadiness,
        visibleMeasures,
        visibleNutrition,
        visibleOhsa,
        visiblePerfAssess,
      } = preferences;
      switch (pref) {
        case 'Goals':
          visibleGoals = !newValue;
          break;
        case 'Daily Readiness':
          visibleDailyReadiness = !newValue;
          break;
        case 'Nutrition':
          visibleMeasures = !newValue;
          break;
        case 'Measurements':
          visibleNutrition = !newValue;
          break;
        case 'Performance Assessments':
          visibleOhsa = !newValue;
          break;
        case 'Overhead Squat':
          visiblePerfAssess = !newValue;
          break;
        default:
          break;
      }
      setPreferences({
        visibleGoals,
        visibleDailyReadiness,
        visibleMeasures,
        visibleNutrition,
        visibleOhsa,
        visiblePerfAssess,
      });
      Alert.alert(
        'Error',
        error.message
          || 'Unable to update dashboard settings. Please try again later.',
      );
    }
  };
  return (
    <View>
      <DashboardSettings
        visibleGoals={preferences.visibleGoals}
        visibleDailyReadiness={preferences.visibleDailyReadiness}
        visibleNutrition={preferences.visibleNutrition}
        visibleMeasures={preferences.visibleMeasures}
        visiblePerfAssess={preferences.visiblePerfAssess}
        visibleOhsa={preferences.visibleOhsa}
        clientName={props.selectedClient?.first_name}
        onSettingToggled={onVisibilityPrefChanged}
      />
    </View>
  );
}
const mapStateToProps = (state) => ({
  selectedClient: state.selectedClient,
});
export default connect(mapStateToProps, {})(DashboardSettingsScreen);
