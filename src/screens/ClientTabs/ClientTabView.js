import React, { PureComponent } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import ChannelScreen from '../Chat/ChannelScreen';
import ClientHomeTabs from './ClientHomeTabs';
import { colors } from '../../styles';

const Stack = createStackNavigator();

class ClientTabView extends PureComponent {
  render() {
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="ClientHomeTabs" component={ClientHomeTabs} />
        <Stack.Screen
          name="Channel"
          component={ChannelScreen}
          options={({ route, navigation }) => ({
            headerShown: true,
            headerLeft: () => (
              <HeaderLeftButton onPress={() => navigation.goBack()} />
            ),
            headerStyle: {
              backgroundColor: colors.duskBlue,
            },
            headerTitleStyle: {
              color: colors.white,
              fontSize: 14,
            },
            headerTitleAlign: 'center',
            headerTitle: route.params?.title ?? '',
          })}
        />
      </Stack.Navigator>
    );
  }
}

export default ClientTabView;
