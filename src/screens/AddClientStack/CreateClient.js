import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import analytics from '@react-native-firebase/analytics';

// Components
import {
  Alert, StyleSheet, StatusBar, View,
} from 'react-native';
import {
  Button, PageContainer, TextInput, ScaledText,
} from '../../components';
import * as db from '../../dataManager';
import nasm from '../../dataManager/apiConfig';

// Styles
import { colors } from '../../styles';

// Helper
import * as validate from '../../util/validate';
import { selectClient, reset } from '../../actions';
import { CREATE_USER_FLOW } from '../../constants';
import { showAddClientVideoIfAble } from '../../FTUEVideoManager';
import { scaleHeight } from '../../util/responsive';
import HeaderLeftButton from '../../components/HeaderLeftButton';

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
  }).isRequired,
  currentUser: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  selectClient: PropTypes.func.isRequired,
  showAddClientVideoIfAble: PropTypes.func.isRequired,
};

// Class
class CreateClient extends Component {
  static navigationOptions = ({ navigation }) => ({
    title: 'Add New Client',
    headerLeft: () => CreateClient.renderHeaderLeft(navigation),
  });

  static renderHeaderLeft = (navigation) => {
    if (navigation) {
      return (
        <HeaderLeftButton
          title="Cancel"
          onPress={() => {
            const navParent = navigation.getParent();
            if (navParent) {
              const navParentState = navParent.getState();
              if (navParentState) {
                if (navParentState.index !== 0) {
                  navigation.navigate('MainStack');
                  return;
                }
              }
            }
            reset(navigation, 'MainStack');
          }}
          titleStyle={styles.headerText}
        />
      );
    }
    return null;
  };

  constructor() {
    super();
    this.inputRefs = {};
    this.state = {
      email: '',
      firstName: '',
      lastName: '',
      canContinue: false,
      isInviteLoading: false,
      accentValidationPassed: true,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'adding_new_client' });
    });

    this.props.showAddClientVideoIfAble(
      this.props.currentUser.id,
      this.props.navigation,
    );
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressInvite = async () => {
    try {
      this.toggleClientInviteLoader(true);
      const validateEmailResponse = await db.validateEmail(this.state.email);
      // this.toggleClientInviteLoader(false);

      switch (validateEmailResponse.status) {
        case CREATE_USER_FLOW.CREATE:
          this.handleCreateClient();
          break;
        case CREATE_USER_FLOW.INVITE:
          this.toggleClientInviteLoader(false);
          Alert.alert(
            'Invite Client?',
            validateEmailResponse.message,
            [
              {
                text: 'Invite',
                onPress: () => this.handleInviteExistingClient(),
              },
              { text: 'Cancel' },
            ],
            { cancelable: false },
          );
          break;
        case CREATE_USER_FLOW.ERROR:
          Alert.alert('Oops', validateEmailResponse.message);
          this.toggleClientInviteLoader(false);
          break;
        default:
          break;
      }
    } catch (error) {
      this.toggleClientInviteLoader(false);
      Alert.alert('Error creating new user', error.message);
    }
  };

  onTextBlur = (text, field) => {
    if (text) {
      this.setState({ [field]: text.trim() });
    }
  };

  focusInput = (input) => {
    const target = this.inputRefs[input];
    if (target && target.textInput) target.textInput.focus();
  };

  handleCreateClient = async () => {
    this.toggleClientInviteLoader(true);
    try {
      const result = await db.createNewClient(
        this.state.firstName,
        this.state.lastName,
        this.state.email,
      );
      if (result.id) {
        analytics().logEvent('add_client', {
          client_email: this.state.email,
          client_first_name: this.state.firstName,
          client_last_name: this.state.lastName,
          new_client: 'true',
        });
        this.props.selectClient(result);
        this.props.navigation.navigate('HealthWarning');
      } else {
        Alert.alert('Error creating new user');
      }
      this.toggleClientInviteLoader(false);
    } catch (error) {
      this.toggleClientInviteLoader(false);
      Alert.alert('Error creating new user', error.message);
    }
  };

  handleInviteExistingClient = async () => {
    try {
      this.toggleClientInviteLoader(true);
      await nasm.api.inviteExistingClient({ email: this.state.email });
      this.toggleClientInviteLoader(false);
      analytics().logEvent('add_client', {
        client_email: this.state.email,
        client_first_name: this.state.firstName,
        client_last_name: this.state.lastName,
        existing_client: 'true',
      });
      Alert.alert(
        'Success',
        'An email has been sent and is waiting for approval.',
        [{ text: 'OK', onPress: () => this.props.navigation.goBack(null) }],
        { onDismiss: () => this.props.navigation.goBack(null) },
      );
    } catch (err) {
      this.toggleClientInviteLoader(false);
      Alert.alert('Error', 'Could not invite this user. Please try again');
    }
  };

  updateInput = (nextState) => {
    const [field, value] = Object.entries(nextState)[0];
    this.setState({ [field]: value }, this.validateInputs);
  };

  validateInputs = () => {
    const fields = Object.keys(this.inputRefs)
      .filter((key) => !!this.inputRefs[key])
      .map((key) => {
        const { validation, value } = this.inputRefs[key].props;
        return validation(value);
      });
    const canContinue = !fields.map((field) => !!field).includes(false);
    this.setState({ canContinue });
    return canContinue;
  };

  checkAccentValidation = () => {
    let accentValidationPassed = true;
    const { email, firstName, lastName } = this.state;
    if (
      validate.containsAccentedChars(email)
      || validate.containsAccentedChars(firstName)
      || validate.containsAccentedChars(lastName)
    ) {
      accentValidationPassed = false;
    }
    this.setState({ accentValidationPassed });
  };

  toggleClientInviteLoader(isVisible) {
    this.setState({ isInviteLoading: isVisible });
  }

  render() {
    return (
      <PageContainer
        scrollEnabled
        containerStyle={styles.pageContainer}
        testID="CreateClientScreen"
      >
        <View style={styles.container}>
          <View>
            <ScaledText style={styles.formTitleText}>Client Details</ScaledText>
            <ScaledText style={styles.bodyText}>
              Provide the new client’s Full Name and Email to send an invitation
              and get them started with using NASM EDGE.
            </ScaledText>
          </View>
          <View>
            <ScaledText style={styles.inputLabel}>Email</ScaledText>
            <TextInput
              ref={(ref) => {
                this.inputRefs.email = ref;
              }}
              value={this.state.email}
              placeholder="Email"
              keyboardType="email-address"
              returnKeyType="next"
              onChangeText={(email) => {
                this.updateInput({ email });
                setTimeout(() => {
                  this.checkAccentValidation();
                }, 100);
              }}
              onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'email')}
              validation={validate.email}
              accentValidation={validate.containsAccentedChars}
              validationErrorMsg={
                validate.containsAccentedChars(this.state.email)
                  ? validate.accentValidationErrorMsg('email')
                  : ''
              }
              onSubmitEditing={() => this.focusInput('firstName')}
              testID="NewClientEmailInput"
              containerStyle={styles.inputContainer}
            />
            <ScaledText style={styles.inputLabel}>First Name</ScaledText>
            <TextInput
              ref={(ref) => {
                this.inputRefs.firstName = ref;
              }}
              value={this.state.firstName}
              placeholder="First Name"
              autoCapitalize="words"
              onChangeText={(value) => {
                const firstName = validate.removeAllSpecialCharacters(value);
                this.updateInput({ firstName });
                setTimeout(() => {
                  this.checkAccentValidation();
                }, 100);
              }}
              onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'firstName')}
              returnKeyType="next"
              validation={validate.name}
              accentValidation={validate.containsAccentedChars}
              validationErrorMsg={
                validate.containsAccentedChars(this.state.firstName)
                  ? validate.accentValidationErrorMsg('first name')
                  : ''
              }
              onSubmitEditing={() => this.focusInput('lastName')}
              testID="NewClientFirstNameInput"
              containerStyle={styles.inputContainer}
            />
            <ScaledText style={styles.inputLabel}>Last Name</ScaledText>
            <TextInput
              ref={(ref) => {
                this.inputRefs.lastName = ref;
              }}
              value={this.state.lastName}
              placeholder="Last Name"
              autoCapitalize="words"
              onChangeText={(value) => {
                const lastName = validate.removeAllSpecialCharacters(value);
                this.updateInput({ lastName });
                setTimeout(() => {
                  this.checkAccentValidation();
                }, 100);
              }}
              onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'lastName')}
              returnKeyType="done"
              validation={validate.name}
              accentValidation={validate.containsAccentedChars}
              validationErrorMsg={
                validate.containsAccentedChars(this.state.lastName)
                  ? validate.accentValidationErrorMsg('last name')
                  : ''
              }
              testID="NewClientLastNameInput"
              containerStyle={styles.inputContainer}
            />
          </View>
          <Button
            title="Send Invitation"
            buttonStyle={styles.sendInvitationButtonStyle}
            disabled={
              !this.state.canContinue || !this.state.accentValidationPassed
            }
            onPress={() => this.onPressInvite()}
            isLoading={this.state.isInviteLoading}
            testID="SubmitClientInvitationButton"
            variant="azure"
          />
        </View>
      </PageContainer>
    );
  }
}

// Export
CreateClient.propTypes = propTypes;
const mapStateToProps = ({ currentUser }) => ({ currentUser });
const mapDispatchToProps = { selectClient, showAddClientVideoIfAble };
export default connect(mapStateToProps, mapDispatchToProps)(CreateClient);

// Styles
const styles = StyleSheet.create({
  pageContainer: {
    paddingTop: 50,
    marginHorizontal: '5%',
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    justifyContent: 'space-evenly',
  },
  bodyText: {
    marginBottom: 25,
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    lineHeight: scaleHeight(3),
    textAlign: 'center',
    color: colors.subGrey,
  },
  formTitleText: {
    fontFamily: 'Avenir',
    fontSize: 46,
    fontWeight: 'bold',
    textAlign: 'center',
    color: colors.medYellow,
  },
  inputLabel: {
    marginVertical: 15,
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.subGrey,
  },
  inputContainer: {
    marginTop: 0,
    paddingHorizontal: 0,
    paddingRight: 20,
    marginBottom: 20,
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  sendInvitationButtonStyle: {
    borderRadius: 35,
    marginVertical: 15,
  },
});
