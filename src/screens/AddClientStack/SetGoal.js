import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import {
  Alert,
  StatusBar,
  StyleSheet,
  BackHandler,
  View,
  SafeAreaView,
} from 'react-native';
import { Button, FocusView, ScaledText } from '../../components';
import { selectClient, reset } from '../../actions';
import nasm from '../../dataManager/apiConfig';
import { curvedScale } from '../../util/responsive';

// Styles
import { colors } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  selectClient: PropTypes.func.isRequired,
};

// Class
class SetGoal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      goals: [],
      selectedGoal: null,
      canContinue: false,
      loading: false,
    };
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onPressSkip);
    this.props.navigation.setParams({ onPressSkip: this.onPressSkip });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'client_goal' });
    });
    this.getGoals();
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
    BackHandler.removeEventListener('hardwareBackPress', this.onPressSkip);
  }

  onPressItem = (selectedGoal) => {
    this.setState({ selectedGoal }, this.validatePage);
  };

  onPressSetGoal = async () => {
    if (!this.state.selectedGoal) {
      return;
    }
    const clientId = this.props.selectedClient?.id;
    try {
      this.setState({ loading: true });
      const client = await nasm.api.updateClientUser(
        { goal_id: this.state.selectedGoal.id },
        clientId,
      );
      this.props.selectClient(client);
      this.props.navigation.navigate('HeightWeight');
    } catch (error) {
      Alert.alert('Error setting goal');
    } finally {
      this.setState({ loading: false });
    }
  };

  onPressSkip = () => {
    const navParent = this.props.navigation.getParent();
    if (navParent) {
      const navParentState = navParent.getState();
      if (navParentState) {
        if (navParentState.index !== 0) {
          this.props.navigation.navigate('MainStack');
          return;
        }
      }
    }
    reset(this.props.navigation, 'MainStack');
  };

  getGoals = () => {
    this.setState({ loading: true }, async () => {
      try {
        const goals = await nasm.api.getAllGoals();
        this.setState({ goals, loading: false });
      } catch (error) {
        Alert.alert('Error', error.message);
        this.setState({ loading: false });
      }
    });
  };

  static navigationOptions = ({ route }) => ({
    title: 'Add New Client',
    headerLeft: () => (
      <HeaderLeftButton
        title="Cancel"
        onPress={route.params?.onPressSkip}
        titleStyle={styles.headerText}
      />
    ),
  });

  validatePage = () => {
    const canContinue = !!this.state.selectedGoal;
    this.setState({ canContinue });
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.textContainer}>
          <ScaledText style={styles.headlineText}>Client Goals</ScaledText>
          <ScaledText style={styles.subTitleText}>
            Select one of the following. You are able to change this at any
            time.
          </ScaledText>
        </View>
        <View style={styles.focusContainer}>
          {this.state.goals && (
            <FocusView
              testID="clientsGoal"
              data={this.state.goals?.sort((a, b) => a.name.localeCompare(b.name))}
              value={
                this.state.selectedGoal ? this.state.selectedGoal.id : undefined
              }
              selectedChangedCallback={this.onPressItem}
              title="What are your current Goals?"
              focusCustomStyle={styles.focusCustomStyle}
              itemCustomStyle={styles.itemCustomStyle}
              itemTitleCustomStyle={styles.itemTitleCustomStyle}
              itemDescriptionCustomStyle={styles.itemDescriptionCustomStyle}
              isShowItemSeparator
              isShowBullet
            />
          )}
        </View>
        <Button
          title="Set Client Goal"
          buttonStyle={{ borderRadius: 35, margin: 20 }}
          containerStyle={styles.buttonContainer}
          disabled={!this.state.canContinue}
          onPress={() => this.onPressSetGoal()}
          isLoading={this.state.loading}
          testID="SubmitClientGoalButton"
          variant="yellow"
        />
      </SafeAreaView>
    );
  }
}

// Export
SetGoal.propTypes = propTypes;
const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = { selectClient };
export default connect(mapStateToProps, mapDispatchToProps)(SetGoal);

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-around',
  },
  subTitleText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    lineHeight: curvedScale(21),
    textAlign: 'center',
    color: colors.subGrey,
    marginBottom: 30,
  },
  buttonContainer: {
    marginHorizontal: '8%',
  },
  textContainer: {
    marginHorizontal: '5%',
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  headlineText: {
    fontFamily: 'Avenir',
    fontSize: 46,
    fontWeight: 'bold',
    textAlign: 'center',
    color: colors.medYellow,
    marginTop: 60,
    marginBottom: 12,
  },
  focusContainer: {
    marginHorizontal: '5%',
    flex: 1,
  },
  focusCustomStyle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    color: colors.eerieBlack,
    paddingHorizontal: 0,
    paddingTop: 10,
  },
  itemCustomStyle: {
    flexDirection: 'row',
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  itemTitleCustomStyle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.eerieBlack,
  },
  itemDescriptionCustomStyle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
    color: colors.subGrey,
  },
});
