import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import moment from 'moment';
import * as RNLocalize from 'react-native-localize';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import {
  Alert,
  StatusBar,
  Text,
  View,
  TouchableOpacity,
  BackHandler,
} from 'react-native';
import { selectClient, startAssessment, reset } from '../../actions';

import {
  Button,
  PageContainer,
  TextInput,
  AvatarPicker,
  DatePicker,
  ScaledText,
  DropDownPicker,
} from '../../components';
import nasm from '../../dataManager/apiConfig';
import CountryPicker from '../../components/CountryPicker/CountryPicker';

// Styles
import { colors } from '../../styles';

// Helpers
import * as validate from '../../util/validate';
import { curvedScale } from '../../util/responsive';
import { getCountryCode } from '../../util/utils';
import HeaderRightButton from '../../components/HeaderRightButton';

// Images
const plusIcon = require('../../resources/plusIcon.png');
const cameraIcon = require('../../resources/cameraIcon.png');

const currentCountry = RNLocalize.getCountry();

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  selectClient: PropTypes.func.isRequired,
};

// Styles
const styles = {
  pageContainer: {
    flex: 1,
    backgroundColor: colors.white,
    marginHorizontal: '5%',
  },
  inputTitleText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.subGrey,
    marginTop: 25,
  },
  heightContainer: {
    flexDirection: 'row',
    alignItems: 'stretch',
    alignSelf: 'stretch',
  },
  spacer: {
    width: 25,
  },
  buttonContainer: {
    marginTop: 30,
  },
  profilePicContainer: {
    flex: 0,
    alignSelf: 'center',
    alignItems: 'center',
    marginVertical: 30,
  },
  headlineText: {
    fontFamily: 'Avenir',
    fontSize: 46,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.medYellow,
    marginTop: 12,
  },
  subTitleText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    lineHeight: curvedScale(21),
    textAlign: 'center',
    color: colors.subGrey,
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  inputContainer: {
    flex: 1,
    marginTop: 0,
    paddingHorizontal: 0,
    paddingRight: 20,
    marginBottom: 20,
  },
  pickerItem: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Light',
    fontWeight: '500',
    paddingHorizontal: curvedScale(8),
    textAlign: 'left',
  },
  dropdownText: {
    textAlign: 'left',
    fontSize: curvedScale(14),
  },
  searchBarStyle: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'row',
    marginRight: 10,
    marginLeft: 20,
  },
  pickerContainerStyle: {
    borderBottomWidth: 1,
    borderBottomColor: colors.silver,
    flex: 1,
    paddingTop: 11,
    paddingBottom: 10,
    marginBottom: 10,
  },
};

// Class
class HeightWeight extends Component {
  static navigationOptions = ({ route }) => {
    const renderHeaderRight = route.params?.renderHeaderRight
      ?? (() => (
        <Text style={[styles.headerText, { marginHorizontal: 16 }]}>Skip</Text>
      ));
    return {
      title: 'Add New Client',
      headerLeft: () => <View />,
      headerRight: renderHeaderRight,
    };
  };

  constructor(props) {
    super(props);

    const isEditing = !!this.props.route.params?.isEditing;
    let feet = '5';
    let inches = '5';
    let cms = '167';
    if (isEditing) {
      if (
        this.props.selectedClient?.client_user.height
        && this.props.selectedClient?.client_user.unit_height === 'in'
      ) {
        feet = `${Math.floor(
          this.props.selectedClient?.client_user.height / 12,
        )}`;
        inches = `${Math.floor(
          this.props.selectedClient?.client_user.height % 12,
        )}`;
      } else {
        cms = `${Math.floor(this.props.selectedClient?.client_user.height)}`;
      }
    }

    this.state = {
      feet,
      inches,
      cms,
      pickerVisible: false,
      canContinue: true,
      loading: false,
      phoneNumber: props.selectedClient?.phone_number,
      photoURL: props.selectedClient?.avatar_url,
      firstname: props.selectedClient?.first_name,
      lastname: props.selectedClient?.last_name,
      unit_height: props.currentUser.unit_height,
      birthdate: null,
      gender: null,
      prevPhone: '',
      countryCode: getCountryCode(currentCountry),
    };
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onPressSkip);
    this.props.navigation.setParams({
      renderHeaderRight: this.renderHeaderRight,
    });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', {
        screen_name: 'set_client_details',
      });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
    BackHandler.removeEventListener('hardwareBackPress', this.onPressSkip);
  }

  validateFields = () => {
    const { firstname, lastname } = this.state;
    if (
      !validate.name(firstname)
      || validate.containsAccentedChars(firstname)
    ) {
      Alert.alert(
        'First name invalid',
        'Please enter a valid first name to continue.',
      );
      return false;
    }
    if (!validate.name(lastname) || validate.containsAccentedChars(lastname)) {
      Alert.alert(
        'Last name invalid',
        'Please enter a valid last name to continue.',
      );
      return false;
    }
    return true;
  };

  onPressConfirm = async () => {
    const canContinue = this.validateFields();
    if (canContinue) {
      const clientId = this.props.selectedClient?.id;
      const clientInfo = {
        // prettier-ignore
        height: this.state.unit_height === 'in' ? (parseInt(this.state.feet, 10) * 12) + parseInt(this.state.inches, 10) : this.state.cms,
        phone_number: this.state.phoneNumber,
        first_name: this.state.firstname,
        last_name: this.state.lastname,
        gender_type: this.state.gender,
        birth_date: this.state.birthdate,
        countryCode: this.state.countryCode,
      };

      try {
        this.setState({ loading: true });
        const client = await nasm.api.updateClientUser(clientInfo, clientId);
        this.props.selectClient(client);
        if (this.props.route.params?.isEditing) {
          this.props.navigation.goBack();
        } else {
          this.backToDashboard();
        }
      } catch (error) {
        Alert.alert('Error setting client info');
      } finally {
        this.setState({ loading: false });
      }
    }
  };

  onTextBlur = (text, field) => {
    if (text) {
      this.setState({ [field]: text.trim() });
    }
  };

  backToDashboard = () => {
    const navParent = this.props.navigation.getParent();
    if (navParent) {
      const navParentState = navParent.getState();
      if (navParentState) {
        if (navParentState.index !== 0) {
          this.props.navigation.navigate('MainStack');
          return;
        }
      }
    }
    reset(this.props.navigation, 'MainStack');
  };

  scrollToBottom = () => {
    if (this.editPageContainer && this.editPageContainer.keyboardHandler) {
      this.editPageContainer.keyboardHandler.scrollToEnd();
    }
  };

  updatePhoneNumber = (phoneNumber, val) => {
    let phone_number = phoneNumber;
    const number = phone_number
      .substr(phone_number.indexOf(' ') + 1)
      .replace(/\s/g, '');
    if (number.length === 10) {
      phone_number = validate.formatPhoneNumber(number, this.state.countryCode);
    } else if (val) {
      phone_number = validate.formatPhoneNumber(number, this.state.countryCode);
    }
    this.setState({ prevPhone: phone_number });
    this.updateInput({ phoneNumber: phone_number });
  };

  updateProfilePicture = (image) => {
    this.setState({ photoURL: image.path }, async () => {
      try {
        await nasm.api.updateUserAvatar(this.props.selectedClient?.id, image);
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || 'Unable to update profile picture. Please try again later.',
        );
      }
    });
  };

  renderHeaderRight = () => (
    <HeaderRightButton
      testID="SkipButton"
      onPress={this.backToDashboard}
      title="Skip"
      titleStyle={styles.headerText}
    />
  );

  formatPhoneNumber = (phone) => {
    const currentDigits = validate.getDigits(phone);

    if (phone) {
      if (this.state.prevPhone) {
        if (
          phone.length > this.state.prevPhone.length
          && currentDigits.length === 10
        ) {
          return validate.formatPhoneNumber(phone, this.state.countryCode);
        }
        return phone;
      }
      if (phone.length > 0 && currentDigits.length >= 10) {
        return validate.formatPhoneNumber(phone, this.state.countryCode);
      }
      return phone;
    }
    return '';
  };

  selectedValue = (index) => {
    this.setState({ countryCode: index }, () => (this.state.prevPhone
      ? this.updatePhoneNumber(this.state.prevPhone, true)
      : this.formatPhoneNumber(this.state.prevPhone)));
  };

  validateInputs() {
    const {
      firstname, lastname, feet, inches, phoneNumber,
    } = this.state;
    const validation = [
      validate.name(firstname),
      validate.name(lastname),
      validate.height(feet),
      validate.height(inches),
      !phoneNumber || phoneNumber.length === 0 || validate.phone(phoneNumber),
    ];
    const canContinue = !validation.includes(false);
    this.setState({ canContinue });
    return canContinue;
  }

  updateInput(nextState) {
    const [field, value] = Object.entries(nextState)[0];
    this.setState({ [field]: value }, this.validateInputs);
  }

  render() {
    return (
      <PageContainer
        scrollEnabled
        testID="HeightWeightScreen"
        ref={(ref) => {
          this.editPageContainer = ref;
        }}
        containerStyle={styles.pageContainer}
      >
        <ScaledText style={styles.headlineText}>Client Details</ScaledText>
        <ScaledText style={styles.subTitleText}>
          Help your Client wrap up their account details, or skip this step and
          finish anytime.
        </ScaledText>
        <View style={styles.profilePicContainer}>
          <AvatarPicker
            onSelectImage={(image) => this.updateProfilePicture(image)}
            iconSize={30}
            disabled={false}
            imageUri={this.state.photoURL}
            containerStyle={{
              borderWidth: 0,
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: '#f2f3f3',
            }}
            icon={plusIcon}
            iconContainerStyle={{
              backgroundColor: colors.macaroniAndCheese,
              width: 33,
              height: 33,
              borderRadius: 16.5,
            }}
            shadow={false}
            placeholderImage={cameraIcon}
            resizeMode={this.state.photoURL ? 'cover' : 'center'}
            navigation={this.props.navigation}
          />
        </View>

        <ScaledText style={styles.inputTitleText}>Email</ScaledText>
        <View
          style={{
            height: curvedScale(50),
            borderColor: colors.subGreyLight,
            borderBottomWidth: 1,
            justifyContent: 'center',
          }}
        >
          <ScaledText style={styles.inputTitleText}>
            {this.props.selectedClient?.email}
          </ScaledText>
        </View>

        <ScaledText style={styles.inputTitleText}>First Name</ScaledText>

        <TextInput
          value={this.state.firstname}
          onChangeText={(value) => {
            const firstname = validate.removeAllSpecialCharacters(value);
            this.setState({ firstname });
          }}
          onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'firstname')}
          placeholder="First Name"
          returnKeyType="done"
          validation={validate.name}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(this.state.firstname)
              ? validate.accentValidationErrorMsg('first name')
              : ''
          }
          containerStyle={styles.inputContainer}
        />

        <ScaledText style={styles.inputTitleText}>Last Name</ScaledText>

        <TextInput
          value={this.state.lastname}
          onChangeText={(value) => {
            const lastname = validate.removeAllSpecialCharacters(value);
            this.setState({ lastname });
          }}
          onBlur={(event) => this.onTextBlur(event.nativeEvent.text, 'lastname')}
          placeholder="Last Name"
          returnKeyType="done"
          validation={validate.name}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(this.state.lastname)
              ? validate.accentValidationErrorMsg('last name')
              : ''
          }
          containerStyle={styles.inputContainer}
        />

        <Text style={styles.inputTitleText}>Country</Text>
        <TouchableOpacity testID="CountryPicker">
          <CountryPicker
            testID="CountryList"
            disable={false}
            animationType="slide"
            containerStyle={styles.pickerContainerStyle}
            hideCountryFlag
            hideCountryCode
            searchBarStyle={styles.searchBarStyle}
            selectedValue={this.selectedValue}
            country={currentCountry}
          />
        </TouchableOpacity>

        <ScaledText style={styles.inputTitleText}>Mobile Number</ScaledText>
        <TextInput
          testID="MobileNumberInput"
          placeholder="Allows Trainer To Contact Client"
          value={this.formatPhoneNumber(this.state.phoneNumber)}
          onChangeText={this.updatePhoneNumber}
          keyboardType="phone-pad"
          maxLength={20}
          validation={validate.phone}
          containerStyle={styles.inputContainer}
        />

        <ScaledText style={styles.inputTitleText}>Height</ScaledText>
        {this.state.unit_height === 'in' ? (
          <View style={styles.heightContainer}>
            <View style={{ flex: 0.5 }}>
              <DropDownPicker
                data={feet}
                selected={this.state.feet}
                visible={this.state.pickerVisible}
                onPress={() => this.setState({ pickerVisible: !this.state.pickerVisible })}
                onValueChange={(feet) => this.updateInput({ feet: feet.id })}
                containerStyle={styles.inputContainer}
                labelStyle={styles.pickerItem}
                textStyle={styles.dropdownText}
              />
            </View>
            <View style={styles.spacer} />
            <View style={{ flex: 0.5 }}>
              <DropDownPicker
                data={inches}
                selected={this.state.inches}
                visible={this.state.pickerVisible}
                onPress={() => this.setState({ pickerVisible: !this.state.pickerVisible })}
                onValueChange={(inches) => this.updateInput({ inches: inches.id })}
                containerStyle={styles.inputContainer}
                labelStyle={styles.pickerItem}
                textStyle={styles.dropdownText}
              />
            </View>
          </View>
        ) : (
          <TextInput
            placeholder="cm"
            value={this.state.cms}
            onChangeText={(cms) => this.updateInput({ cms })}
            keyboardType="number-pad"
            maxLength={3}
            validation={validate.isNumber}
            containerStyle={styles.inputContainer}
          />
        )}

        <ScaledText style={styles.inputTitleText}>Birthday</ScaledText>
        <DatePicker
          testID="DatePicker"
          value={this.state.birthdate}
          placeholder="Birthday"
          maxValue={new Date()}
          onValueChange={(birthdate) => this.setState({ birthdate })}
          onIosPickerVisible={(visible) => {
            if (visible) this.scrollToBottom();
          }}
          defaultDate={moment().subtract(18, 'years').toDate()}
          style={styles.inputContainer}
          textStyle={{ fontSize: curvedScale(14) }}
        />

        <ScaledText style={styles.inputTitleText}>Assigned Sex</ScaledText>
        <DropDownPicker
          testID="GenderPicker"
          data={gender}
          placeholder="Assigned Sex"
          selected={this.state.gender}
          onValueChange={(gender) => this.setState({ gender: gender.id })}
          containerStyle={styles.inputContainer}
          labelStyle={styles.pickerItem}
          textStyle={styles.dropdownText}
        />
        <View style={styles.buttonContainer}>
          <Button
            title="Continue"
            buttonStyle={{ borderRadius: 35, marginHorizontal: '3%' }}
            disabled={!this.state.canContinue}
            onPress={() => this.onPressConfirm()}
            isLoading={this.state.loading}
            testID="SubmitClientHeightWeightButton"
            variant="yellow"
          />
        </View>
      </PageContainer>
    );
  }
}

// Export
HeightWeight.propTypes = propTypes;
const mapStateToProps = ({ selectedClient, currentUser }) => ({
  selectedClient,
  currentUser,
});
const mapDispatchToProps = { selectClient, startAssessment };
export default connect(mapStateToProps, mapDispatchToProps)(HeightWeight);

// Data
const feet = Array(8)
  .fill(0)
  .map((value, index) => ({ label: `${index + 1}'`, id: `${index + 1}` }));
const inches = Array(12)
  .fill(0)
  .map((value, index) => ({ label: `${index}"`, id: `${index}` }));
const gender = [
  {
    id: 1,
    label: 'Male',
  },
  {
    id: 2,
    label: 'Female',
  },
];
