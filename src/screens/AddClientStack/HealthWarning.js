import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import { StyleSheet, StatusBar, View } from 'react-native';
import {
  BackHandler,
  Button,
  PageContainer,
  ScaledText,
} from '../../components';
import { scaleHeight } from '../../util/responsive';

// Styles
import { colors } from '../../styles';

import { reset } from '../../actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
  }).isRequired,
};

// Class
class HealthWarning extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Add New Client',
    headerLeft: () => (
      <HeaderLeftButton
        title="Cancel"
        onPress={route.params?.onPressSkip}
        titleStyle={styles.headerText}
      />
    ),
  });

  componentDidMount() {
    this.props.navigation.setParams({ onPressSkip: this.onPressSkip });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'Health_Warning' });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressSkip = () => {
    const navParent = this.props.navigation.getParent();
    if (navParent) {
      const navParentState = navParent.getState();
      if (navParentState) {
        if (navParentState.index !== 0) {
          this.props.navigation.navigate('MainStack');
          return;
        }
      }
    }
    reset(this.props.navigation, 'MainStack');
  };

  render() {
    return (
      <PageContainer containerStyle={styles.pageContainer}>
        <BackHandler allowed={false} />
        <View style={styles.bodyContainer}>
          <ScaledText style={styles.headlineText}>Client Health</ScaledText>
          <ScaledText style={styles.bodyText}>
            Before continuing, make sure your client is healthy enough for
            physical activity and/or has a physician’s permission to workout.
          </ScaledText>
        </View>
        <Button
          title="I Understand, Continue"
          buttonStyle={{ borderRadius: 35, marginBottom: 20 }}
          onPress={() => this.props.navigation.navigate('SetGoal')}
          testID="AcceptHealthWarningButton"
          variant="yellow"
        />
      </PageContainer>
    );
  }
}

// Export
HealthWarning.propTypes = propTypes;
export default HealthWarning;

// Styles
const styles = StyleSheet.create({
  pageContainer: {
    backgroundColor: colors.white,
    marginHorizontal: '10%',
  },
  bodyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headlineText: {
    fontFamily: 'Avenir',
    fontSize: 46,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.medYellow,
    marginTop: 12,
  },
  bodyText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    lineHeight: scaleHeight(3),
    textAlign: 'center',
    color: colors.subGrey,
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
});
