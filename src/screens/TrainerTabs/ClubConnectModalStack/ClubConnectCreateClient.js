/* eslint-disable react-redux/useSelector-prefer-selectors */
import React, { useState } from 'react';
import {
  View, Text, StyleSheet, Image, Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import AwesomeAlert from 'react-native-awesome-alerts';
import analytics from '@react-native-firebase/analytics';
import { Button, PageContainer, TextInput } from '../../../components';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import * as db from '../../../dataManager';
import nasm from '../../../dataManager/apiConfig';
import * as validate from '../../../util/validate';
import { deleteAlertStyles } from '../../../styles/alertStyle';
import { reset, selectClient } from '../../../actions';
import { curvedScale } from '../../../util/responsive';
import { colors } from '../../../styles';
import { CREATE_USER_FLOW, FEATURE_FLAGS } from '../../../constants';

const defaultProfile = require('../../../resources/defaultProfile.png');
const Checkmark_2 = require('../../../resources/Checkmark_2.png');

const INVITE_MSG = 'A user already exists with this email. Would you like to invite this user to train under you?';

const ClubConnectCreateClient = () => {
  const inputRefs = {};
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const trainerActiveProfile = useSelector((state) => state.trainerActiveProfile);
  const [showInviteAlert, setShowInviteAlert] = useState(false);
  const [inviteMessage, setInviteMessage] = useState(INVITE_MSG);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [isInviteLoading, setInviteLoading] = useState(false);
  const [canContinue, setCanContinue] = useState(false);
  const [accentValidationPassed, setAccentValidationPassed] = useState(true);
  const [formState, setFormState] = useState({
    email: '',
    firstName: '',
    lastName: '',
    clubName: trainerActiveProfile?.ClubName,
    club_id: trainerActiveProfile?.ClubId,
    location_id: trainerActiveProfile?.Locations?.Id,
  });

  let profileUri = defaultProfile;
  if (trainerActiveProfile?.ClubLogoUrl) {
    profileUri = { uri: trainerActiveProfile?.ClubLogoUrl };
  }
  React.useLayoutEffect(() => {
    navigation.setOptions({
      title: 'Add New Client',
      headerLeft: renderHeaderLeft,
    });
  }, [navigation]);

  const renderHeaderLeft = () => {
    if (navigation) {
      return (
        <HeaderLeftButton
          title="Cancel"
          onPress={() => {
            const navParent = navigation.getParent();
            if (navParent) {
              const navParentState = navParent.getState();
              if (navParentState) {
                if (navParentState.index !== 0) {
                  navigation.navigate('MainStack');
                  return;
                }
              }
            }
            reset(navigation, 'MainStack');
          }}
          titleStyle={styles.headerText}
        />
      );
    }
    return null;
  };

  const setValues = (key, value) => {
    setFormState((prevState) => ({
      ...prevState,
      [key]: value,
    }));
    setTimeout(() => {
      validateInputs();
      checkAccentValidation();
    }, 200);
  };

  const focusInput = (input) => {
    const target = inputRefs[input];
    if (target && target.textInput) target.textInput.focus();
  };

  const validateInputs = () => {
    const { email, firstName, lastName } = formState;
    const validation = [
      email?.length ? validate.email(email) : false,
      firstName?.length ? validate.name(firstName) : false,
      lastName?.length ? validate.name(lastName) : false,
    ];
    const isValidated = !validation.includes(false);
    setCanContinue(isValidated);
    return isValidated;
  };

  const checkAccentValidation = () => {
    let accentValidated = true;
    const { email, firstName, lastName } = formState;
    if (
      validate.containsAccentedChars(email)
      || validate.containsAccentedChars(firstName)
      || validate.containsAccentedChars(lastName)
    ) {
      accentValidated = false;
    }
    setAccentValidationPassed(accentValidated);
  };

  const onPressInvite = async () => {
    try {
      toggleClientInviteLoader(true);
      const validateEmailResponse = await db.validateEmail(
        formState.email,
        formState.club_id,
        formState.location_id,
      );
      switch (validateEmailResponse.status) {
        case CREATE_USER_FLOW.CREATE:
          handleCreateClient();
          break;
        case CREATE_USER_FLOW.INVITE:
          toggleClientInviteLoader(false);
          setShowInviteAlert(true);
          setInviteMessage(validateEmailResponse.message);
          break;
        case CREATE_USER_FLOW.ERROR:
          toggleClientInviteLoader(false);
          Alert.alert('Oops', validateEmailResponse.message);
          break;
        default:
          break;
      }
    } catch (error) {
      toggleClientInviteLoader(false);
      Alert.alert('Error creating new user', error.message);
    }
  };

  const handleCreateClient = async () => {
    toggleClientInviteLoader(true);
    try {
      const result = await db.createNewClient(
        formState.firstName,
        formState.lastName,
        formState.email,
        formState.club_id,
        formState.location_id,
      );
      if (result.id) {
        analytics().logEvent('add_client_club_connect', {
          client_email: formState.email,
          client_first_name: formState.firstName,
          client_last_name: formState.lastName,
          new_client: 'true',
        });
        dispatch(selectClient(result));
        navigation.goBack();
      } else {
        Alert.alert('Error creating new user');
      }
      toggleClientInviteLoader(false);
    } catch (error) {
      toggleClientInviteLoader(false);
      Alert.alert('Error creating new user', error.message);
    }
  };

  const handleInviteExistingClient = async () => {
    try {
      toggleClientInviteLoader(true);
      const emailObj = {
        email: formState.email,
        club_id: formState.club_id,
      };
      if (
        FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED
        && formState.location_id
      ) {
        emailObj.location_id = formState.location_id;
      }
      await nasm.api.inviteExistingClient(emailObj);
      toggleClientInviteLoader(false);
      analytics().logEvent('add_client_club_connect', {
        client_email: formState.email,
        client_first_name: formState.firstName,
        client_last_name: formState.lastName,
        existing_client: 'true',
      });
      setShowSuccessAlert(true);
    } catch (err) {
      toggleClientInviteLoader(false);
      Alert.alert('Error', 'Could not invite this user. Please try again');
    }
  };

  const toggleClientInviteLoader = (isVisible) => {
    setInviteLoading(isVisible);
  };

  return (
    <PageContainer
      scrollEnabled
      containerStyle={styles.pageContainer}
      testID="ClubConnectCreateClient"
    >
      <View>
        <View>
          <View style={styles.topTitleView}>
            <Text style={styles.topTitle}>
              {`You are adding a client to ${formState.clubName}`}
            </Text>
          </View>
          <View style={styles.clientDetailView}>
            <Text style={styles.clientDetailTitle}>Client Details</Text>
            <Text style={styles.clientDetailText}>
              Provide the new client&apos;s Full Name and Email to send an
              invitation and get then started with using NASM Edge.
            </Text>
          </View>
          <View>
            <Text style={styles.inputLabel}>Email</Text>
            <TextInput
              value={formState.email}
              placeholder="Email"
              keyboardType="email-address"
              returnKeyType="next"
              onChangeText={(val) => {
                setValues('email', val);
              }}
              validation={validate.email}
              accentValidation={validate.containsAccentedChars}
              validationErrorMsg={
                validate.containsAccentedChars(formState.email)
                  ? validate.accentValidationErrorMsg('email')
                  : ''
              }
              onSubmitEditing={() => focusInput('firstName')}
              testID="NewClientEmailInput"
              containerStyle={styles.inputContainer}
            />
          </View>
          <View>
            <Text style={styles.inputLabel}>First Name</Text>
            <TextInput
              ref={(ref) => {
                inputRefs.firstName = ref;
              }}
              value={formState.firstName}
              placeholder="First Name"
              autoCapitalize="words"
              onChangeText={(text) => {
                const val = validate.removeAllSpecialCharacters(text);
                setValues('firstName', val);
              }}
              returnKeyType="next"
              validation={validate.name}
              accentValidation={validate.containsAccentedChars}
              validationErrorMsg={
                validate.containsAccentedChars(formState.firstName)
                  ? validate.accentValidationErrorMsg('first name')
                  : ''
              }
              onSubmitEditing={() => focusInput('lastName')}
              testID="NewClientFirstNameInput"
              containerStyle={styles.inputContainer}
            />
          </View>
          <View>
            <Text style={styles.inputLabel}>Last Name</Text>
            <TextInput
              ref={(ref) => {
                inputRefs.lastName = ref;
              }}
              value={formState.lastName}
              placeholder="Last Name"
              autoCapitalize="words"
              onChangeText={(text) => {
                const val = validate.removeAllSpecialCharacters(text);
                setValues('lastName', val);
              }}
              returnKeyType="done"
              validation={validate.name}
              accentValidation={validate.containsAccentedChars}
              validationErrorMsg={
                validate.containsAccentedChars(formState.lastName)
                  ? validate.accentValidationErrorMsg('last name')
                  : ''
              }
              testID="NewClientLastNameInput"
              containerStyle={styles.inputContainer}
            />
          </View>
        </View>
      </View>
      <View style={styles.bottomContainer}>
        {!showInviteAlert && (
          <View style={styles.avatarParentContainer}>
            <Text
              style={styles.titleText}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              This client is being added under your profile for
            </Text>
            <View style={styles.avatarContainer}>
              <Image style={styles.profileImageStyle} source={profileUri} />

              <Text
                style={styles.nameTextStyle}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {formState?.clubName}
              </Text>
            </View>
          </View>
        )}
        <Button
          title="Send Invite"
          buttonStyle={styles.sendInvitationButtonStyle}
          onPress={onPressInvite}
          isLoading={isInviteLoading}
          disabled={!canContinue || !accentValidationPassed}
          testID="SubmitClientInviteButton"
        />
      </View>
      <AwesomeAlert
        show={showInviteAlert}
        showProgress={false}
        useNativeDriver
        title="Invite Existing Client"
        message={inviteMessage}
        closeOnTouchOutside={false}
        closeOnHardwareBackPress={false}
        showConfirmButton
        showCancelButton
        titleStyle={deleteAlertStyles.alertTitle}
        messageStyle={deleteAlertStyles.alertText}
        cancelText="Invite"
        confirmText="Cancel"
        confirmButtonTextStyle={styles.alertButtonText}
        confirmButtonStyle={deleteAlertStyles.alertButton}
        cancelButtonTextStyle={styles.alertButtonText}
        cancelButtonStyle={deleteAlertStyles.alertButton}
        actionContainerStyle={styles.successActionConStyle}
        contentContainerStyle={deleteAlertStyles.alertContainer}
        onCancelPressed={() => {
          setShowInviteAlert(false);
          handleInviteExistingClient();
        }}
        onConfirmPressed={() => {
          setShowInviteAlert(false);
        }}
      />
      <AwesomeAlert
        show={showSuccessAlert}
        showProgress={false}
        useNativeDriver
        customView={(
          <View>
            <Image style={styles.successImageStyle} source={Checkmark_2} />
            <Text style={styles.successText}>Success!</Text>
            <Text style={styles.alertText}>
              Your clien&apos;s invitation has been sent.
            </Text>
          </View>
        )}
        closeOnTouchOutside={false}
        closeOnHardwareBackPress={false}
        showConfirmButton
        titleStyle={deleteAlertStyles.alertTitle}
        messageStyle={deleteAlertStyles.alertText}
        confirmText="Continue"
        confirmButtonTextStyle={styles.alertButtonText}
        confirmButtonStyle={deleteAlertStyles.alertButton}
        cancelButtonTextStyle={styles.alertButtonText}
        cancelButtonStyle={styles.alertButton}
        actionContainerStyle={styles.successActionConStyle}
        contentContainerStyle={deleteAlertStyles.alertContainer}
        onConfirmPressed={() => {
          setShowSuccessAlert(false);
          navigation.goBack();
        }}
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  pageContainer: {
    backgroundColor: colors.white,
  },
  bottomContainer: {
    height: curvedScale(270),
    flex: 1,
    justifyContent: 'flex-end',
  },
  topTitleView: {
    marginHorizontal: -20,
    marginTop: -30,
    backgroundColor: colors.subGrey,
  },
  topTitle: {
    fontFamily: 'Avenir',
    fontWeight: 500,
    fontSize: curvedScale(13),
    color: colors.white,
    padding: curvedScale(10),
    paddingLeft: curvedScale(15),
  },
  clientDetailView: {
    marginHorizontal: -20,
    padding: curvedScale(17),
    paddingBottom: curvedScale(20),
    marginBottom: curvedScale(20),
    backgroundColor: colors.gray_1,
  },
  clientDetailTitle: {
    fontFamily: 'Avenir',
    fontWeight: 800,
    fontSize: curvedScale(24),
    color: colors.black,
    paddingVertical: curvedScale(10),
  },
  clientDetailText: {
    fontFamily: 'Avenir',
    fontWeight: 500,
    fontSize: curvedScale(17),
    color: colors.black,
  },
  inputLabel: {
    marginTop: curvedScale(10),
    fontFamily: 'Avenir',
    fontSize: curvedScale(14),
    fontWeight: 500,
    color: colors.subGrey,
  },
  inputContainer: {
    paddingBottom: curvedScale(10),
    marginBottom: curvedScale(10),
    borderBottomColor: colors.subGrey,
    borderBottomWidth: 1,
  },
  avatarParentContainer: {
    padding: curvedScale(10),
    paddingTop: curvedScale(20),
    backgroundColor: colors.gray_1,
    alignItems: 'center',
    borderRadius: curvedScale(5),
  },
  avatarContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: curvedScale(10),
  },
  profileImageStyle: {
    width: curvedScale(50),
    height: curvedScale(50),
    borderRadius: curvedScale(50) / 2,
    borderWidth: 1,
    borderColor: colors.medYellow,
    marginRight: curvedScale(15),
  },
  titleText: {
    fontFamily: 'Avenir',
    fontSize: curvedScale(13),
    fontWeight: '500',
    color: colors.black,
  },
  nameTextStyle: {
    fontFamily: 'Avenir',
    fontSize: curvedScale(17),
    fontWeight: '900',
    color: colors.black,
    textTransform: 'capitalize',
  },
  sendInvitationButtonStyle: {
    borderRadius: 35,
    marginVertical: 20,
  },
  successImageStyle: {
    alignSelf: 'center',
    marginTop: 25,
  },
  successText: {
    fontFamily: 'Avenir',
    fontSize: 17,
    fontWeight: '500',
    color: colors.black,
    textAlign: 'center',
    marginVertical: 15,
  },
  alertText: {
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '500',
    color: colors.subGrey,
    textAlign: 'center',
    marginBottom: 25,
  },
  alertButtonText: {
    fontSize: 17,
    fontFamily: 'Avenir',
    fontWeight: '800',
    color: colors.subGrey,
    textAlign: 'center',
  },
  successActionConStyle: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    borderRadius: 5,
  },
});

export default ClubConnectCreateClient;
