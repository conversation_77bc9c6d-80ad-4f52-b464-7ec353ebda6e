import React, { Component } from 'react';

// Components
import {
  Dimensions,
  Image,
  TouchableOpacity,
  View,
  FlatList,
  SafeAreaView,
  Platform,
} from 'react-native';
import { ScaledText, ExamDomainScore } from '../../components';
import { curvedScale, scaleHeight, scaleWidth } from '../../util/responsive';

// Styles
import { colors, shadow } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const backBtn = require('../../resources/btnBackWhite.png');
const topImg = require('../../resources/instructions2.png');

const domainColors = [
  colors.peaGreen,
  colors.nasmBlue,
  colors.macaroniAndCheese,
  colors.pinkishPurple,
  colors.azure,
  colors.goodGreen,
];

class DomainOverview extends Component {
  static navigationOptions = ({ navigation }) => ({
    headerTransparent: true,
    headerStyle: {
      backgroundColor: 'rgba(0, 0, 0, 0)',
      borderBottomWidth: 0,
    },
    headerLeft: () => (
      <HeaderLeftButton
        onPress={() => navigation.goBack()}
        buttonImage={<Image style={styles.backButton} source={backBtn} />}
      />
    ),

    headerRight: () => (
      <View style={styles.rightNavView}>
        <View style={styles.openCircle} />
        <View style={styles.closedCircle} />
      </View>
    ),
  });

  constructor(props) {
    super(props);
    this.state = {
      domains: this.props.route.params?.domains,
    };
  }

  onPressStart = () => {
    this.props.navigation.navigate('PracticeExam', {
      domains: this.state.domains,
      practiceExam: this.props.route.params?.practiceExam,
      resetTest: this.props.route.params?.resetTest,
      cptVersion: this.props.route.params?.cptVersion,
    });
  };

  calculateDomainWeight = (weight) => Math.round(120 * (weight / 100));

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <Image style={styles.topImg} source={topImg} />
        <View style={styles.headerContainer}>
          <ScaledText style={styles.header}>
            Each Domain Makes Up Your Overall Score
          </ScaledText>
          <ScaledText style={styles.subHeader}>
            The chart below shows the weight of each of the 6 domains areas.
            Each of these makes up a percentage of your overall score.
          </ScaledText>
        </View>
        <View style={styles.listContainer}>
          <ScaledText style={styles.fullExam}>
            Full Exam 120 Questions
          </ScaledText>
          <FlatList
            keyExtractor={(item) => item.domainId}
            data={this.state.domains}
            renderItem={({ item, index }) => (
              <ExamDomainScore
                key={index}
                domain={item.domainName}
                color={domainColors[index]}
                score={this.calculateDomainWeight(item.practiceRatioPercent)}
                domainTotal={this.calculateDomainWeight(
                  item.practiceRatioPercent,
                )}
              />
            )}
            extraData={this.props}
            contentContainerStyle={styles.domainList}
          />
        </View>
        <TouchableOpacity
          style={styles.startButton}
          onPress={this.onPressStart}
        >
          <ScaledText style={styles.startButtonText}>Start</ScaledText>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }
}

const { width } = Dimensions.get('window');

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  listContainer: {
    flex: 8,
  },
  backButton: {
    tintColor: colors.veryLightBlue,
    padding: 1,
    marginTop: Platform.OS === 'ios' ? scaleHeight(1.5) : scaleHeight(5),
  },
  rightNavView: {
    padding: 30,
    flexDirection: 'row',
    marginTop: Platform.OS === 'ios' ? scaleHeight(1.5) : scaleHeight(5),
  },
  closedCircle: {
    width: 12,
    height: 12,
    backgroundColor: colors.white,
    borderRadius: 6,
  },
  openCircle: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: colors.white,
    marginRight: 12,
  },
  topImg: {
    width: width + 6,
    height: scaleHeight(35),
    top: '-0.5%',
    left: '-0.3%',
    backgroundColor: colors.subGrey,
  },
  headerContainer: {
    position: 'absolute',
    marginHorizontal: scaleWidth(9),
    marginTop: scaleHeight(10),
  },
  header: {
    fontSize: 27,
    fontFamily: 'Avenir',
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 15,
  },
  subHeader: {
    color: colors.white,
  },
  fullExam: {
    fontSize: 23,
    textAlign: 'center',
    padding: '3%',
    marginTop: -8,
    backgroundColor: colors.paleGray,
    width,
    paddingVertical: curvedScale(18),
  },
  domainList: {
    alignSelf: 'center',
    marginTop: 15,
    paddingBottom: 35,
    width,
  },
  startButton: {
    flex: 1,
    padding: 10,
    width: '40%',
    backgroundColor: colors.duskBlue,
    alignSelf: 'center',
    justifyContent: 'center',
    borderRadius: scaleWidth(10),
    margin: 30,
    ...shadow,
  },
  startButtonText: {
    fontWeight: 'bold',
    textAlign: 'center',
    color: colors.white,
  },
};

export default DomainOverview;
