import React, { Component } from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  FlatList,
  Text,
  LayoutAnimation,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { connect } from 'react-redux';
import analytics from '@react-native-firebase/analytics';
import { FEATURE_FLAGS, androidSafeLayoutAnimation } from '../../constants';
import { userHasCpt7Permission } from '../../util/PermissionUtils';

// Components
import {
  CardDomain,
  LoadingSpinner,
  ScaledText,
  CPTVersionSelector,
} from '../../components';
import nasm from '../../dataManager/apiConfig';
import { scaleWidth, scaleHeight } from '../../util/responsive';

// Styles
import { colors } from '../../styles';

const xBtn = require('../../resources/btnCloseWhite.png');
const examImg = require('../../resources/imgPackageExamBanner.png');
const CPT7 = require('../../resources/cpt7Img.png');
const closeBtn = require('../../resources/closeImg.png');

const cardColors = [
  colors.peaGreen,
  colors.nasmBlue,
  colors.macaroniAndCheese,
  colors.pinkishPurple,
  colors.azure,
  colors.goodGreen,
];

class ExamQuestionsCatalog extends Component {
  static navigationOptions = {
    title: null,
    headerShown: false,
  };

  constructor(props) {
    super(props);
    const hasCpt7 = userHasCpt7Permission(props.currentUser);
    this.state = {
      domains: [],
      practiceExam: null,
      quizSets: [],
      answeredQuestions: [],
      totalIncorrect: 0,
      totalCorrect: 0,
      isLoading: true,
      cptLoading: false,
      cptVersion: FEATURE_FLAGS.CPT7_ENABLED && hasCpt7 ? 7 : 6,
      CPT7ExamPrep: false,
    };
  }

  componentDidMount() {
    this.getDomainInfo(this.state.cptVersion);
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.getPracticeExamQuestions(this.state.cptVersion);
      analytics().logEvent('screen_view', {
        screen_name: 'exam_catalog',
        cpt_version: this.state.cptVersion,
      });
    });
    this.showCPTv7Notification(this.props.currentUser.id);
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressDomain = (domain) => {
    this.resetTest();
    this.getQuizSet(domain);
    analytics().logEvent('exam_start', {
      domain: domain.domainName,
      cpt_version: this.state.cptVersion,
    });
  };

  onPressPracticeExam = () => {
    this.resetTest();
    this.props.navigation.navigate('PracticeExamInstructions', {
      domains: this.state.domains,
      practiceExam: this.state.practiceExam,
      cptVersion: this.state.cptVersion,
      resetTest: this.resetTest,
    });
    analytics().logEvent('exam_start', {
      domain: 'practice_exam_full',
      cpt_version: this.state.cptVersion,
    });
  };

  getDomainInfo(cptVersion) {
    nasm.api.getAllDomains(cptVersion).then((response) => {
      this.setState({ domains: response, isLoading: false });
    });
  }

  getPracticeExamQuestions(cptVersion) {
    nasm.api.getPracticeExamQuestions(cptVersion).then((response) => {
      this.setState({
        practiceExam: response,
        isLoading: false,
        cptLoading: false,
      });
    });
  }

  getQuizSet(domain) {
    nasm.api
      .getQuizSetByDomain(domain.domainId, this.state.cptVersion)
      .then((response) => {
        this.props.navigation.navigate({
          name: 'DomainQuiz',
          params: {
            domain,
            quizSet: response,
            resetTest: this.resetTest,
            cptVersion: this.state.cptVersion,
          },
          merge: true,
        });
      });
  }

  changeCPTVersion = (cpt) => {
    this.setState({ cptVersion: cpt, cptLoading: true });
    this.getPracticeExamQuestions(cpt);
    this.getDomainInfo(cpt);
  };

  hideCPTv7Notification = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ CPT7ExamPrep: false });
  };

  resetTest = () => {
    this.setState({
      totalCorrect: 0,
      totalIncorrect: 0,
      answeredQuestions: [],
    });
  };

  async showCPTv7Notification(trainerId) {
    if (!FEATURE_FLAGS.CPT7_ENABLED) {
      return false;
    }
    const CPT7FlashCardsShown = await AsyncStorage.getItem(
      `${trainerId}_CPTv7_exam_notification_shown`,
    );
    if (!CPT7FlashCardsShown) {
      this.setState({ CPT7ExamPrep: true });
      AsyncStorage.setItem(
        `${trainerId}_CPTv7_exam_notification_shown`,
        'true',
      );
      return true;
    }
    return false;
  }

  renderListFooter = () => {
    const disabled = !this.state.practiceExam;
    return (
      <TouchableOpacity
        style={styles.practiceButton}
        onPress={this.onPressPracticeExam}
        disabled={disabled}
      >
        {disabled ? (
          <ActivityIndicator color={colors.white} />
        ) : (
          <ScaledText style={styles.practiceButtonText}>
            Practice Exam
          </ScaledText>
        )}
      </TouchableOpacity>
    );
  };

  renderListHeader = () => (
    <View>
      <TouchableOpacity
        style={styles.xBtnContainer}
        onPress={() => this.props.navigation.navigate('Account')}
      >
        <Image style={styles.xBtn} source={xBtn} />
      </TouchableOpacity>
      {FEATURE_FLAGS.CPT7_ENABLED && (
        <View style={styles.cptVersionSelector}>
          <CPTVersionSelector
            cptVersion={this.state.cptVersion}
            changeCPTVersion={this.changeCPTVersion}
            title="Exam Prep"
          />
        </View>
      )}
      <ScaledText style={styles.titleText}>Exam Questions</ScaledText>
      <ScaledText style={styles.subHeaderText}>
        Check your knowledge for each domain or practice the full exam
        experience.
      </ScaledText>
    </View>
  );

  renderNotification() {
    return (
      <View style={styles.background}>
        <TouchableOpacity
          style={styles.closeBtn}
          onPress={this.hideCPTv7Notification}
        >
          <Image source={closeBtn} />
        </TouchableOpacity>
        <Image source={CPT7} />
        <Text style={styles.title}>CPT 7 is Now Available!</Text>
        <Text style={styles.description}>
          We have updated the app with the newest study material. Switch between
          CPT 6 or 7 using the dropdown at the top right of either the exam or
          flash card screen.
        </Text>
        <TouchableOpacity
          style={styles.startBtn}
          onPress={this.hideCPTv7Notification}
        >
          <ScaledText style={styles.practiceButtonText}>Okay!</ScaledText>
        </TouchableOpacity>
      </View>
    );
  }

  renderSpinner = () => {
    const loading = this.state.isLoading || this.state.cptLoading;

    if (!loading) return null;
    return (
      <View style={styles.spinner}>
        <StatusBar barStyle="dark-content" />
        <LoadingSpinner
          backgroundColor="rgba(0, 0, 0, 0.5)"
          color={colors.white}
          titleTextStyle={{ color: colors.white }}
          visible={loading}
          title="Loading Domains..."
        />
      </View>
    );
  };

  render() {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.bgImage}>
          <Image style={styles.topImg} source={examImg} />
          <View style={[styles.topImg, styles.topImgOpacity]} />
        </View>
        <FlatList
          ListHeaderComponent={this.renderListHeader}
          ListFooterComponent={this.renderListFooter}
          contentContainerStyle={styles.content}
          columnWrapperStyle={{ justifyContent: 'space-between' }}
          keyExtractor={(item) => item.domainId}
          data={this.state.domains}
          renderItem={({ item, index }) => (
            <CardDomain
              domain={item}
              key={index}
              color={cardColors[index]}
              onPressDomain={this.onPressDomain}
              position={index}
            />
          )}
          numColumns={2}
          extraData={this.props}
          showsVerticalScrollIndicator={false}
        />
        {this.state.CPT7ExamPrep && this.renderNotification()}
        {this.renderSpinner()}
      </View>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    paddingHorizontal: '5%',
  },
  bgImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  topImg: {
    height: scaleHeight(40),
    width: scaleWidth(100),
  },
  topImgOpacity: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'black',
    opacity: 0.5,
  },
  xBtnContainer: {
    alignSelf: 'flex-start',
    paddingTop: scaleHeight(8),
    paddingBottom: 7,
  },
  cptVersionSelector: {
    position: 'absolute',
    top: scaleHeight(8),
    right: 0,
  },
  xBtn: {
    tintColor: colors.veryLightBlue,
  },
  content: {
    flexGrow: 1,
  },
  titleText: {
    fontWeight: 'bold',
    fontSize: 30,
    color: colors.white,
    marginBottom: 5,
  },
  subHeaderText: {
    color: colors.white,
    marginTop: 7,
    marginBottom: 7,
  },
  practiceButton: {
    borderRadius: scaleWidth(5.5),
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    padding: '3.8%',
    backgroundColor: colors.azure,
  },
  practiceButtonText: {
    textAlign: 'center',
    fontSize: 15,
    fontWeight: 'bold',
    color: colors.white,
  },
  spinner: {
    flex: 1,
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  notificationContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  background: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    backgroundColor: colors.transparentBlack,
    justifyContent: 'center',
    alignItems: 'center',
  },
  startBtn: {
    borderRadius: scaleWidth(5.5),
    bottom: 30,
    marginBottom: 25,
    width: '25%',
    height: '4.8%',
    backgroundColor: colors.azure,
    position: 'absolute',
    justifyContent: 'center',
  },
  title: {
    fontFamily: 'Avenir',
    fontSize: 25,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.white,
    marginHorizontal: 20,
  },
  description: {
    fontFamily: 'Avenir-Roman',
    textAlign: 'center',
    marginTop: 15,
    fontSize: 15,
    color: colors.white,
    marginHorizontal: 20,
  },
  closeBtn: {
    position: 'absolute',
    top: 40,
    left: 16,
  },
};

const mapStateToProps = ({ currentUser }) => ({ currentUser });
const mapDispatchToProps = {};
export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ExamQuestionsCatalog);
