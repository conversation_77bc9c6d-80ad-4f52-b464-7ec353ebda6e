import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  FlatList,
  Dimensions,
  StatusBar,
} from 'react-native';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import {
  ExamProgressBar,
  LoadingSpinner,
  ScaledText,
  HoverHeader,
} from '../../components';

import { scaleWidth, scaleHeight, curvedScale } from '../../util/responsive';

// Styles
import { colors, shadow } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const { width } = Dimensions.get('window');
const checkMark = require('../../resources/imgCheckmarkM.png');
const noCheckMark = require('../../resources/imgCheckmarkOffM.png');
const incorrectX = require('../../resources/imgXSmallGray.png');
const rightArrow = require('../../resources/imgRightArrowGray.png');
const leftArrow = require('../../resources/imgLeftArrowGray.png');

class DomainQuiz extends Component {
  static navigationOptions = ({ route }) => {
    const title = route.params?.title ?? '1 of 25';
    const quitQuiz = route.params?.quitQuiz ?? (() => {});
    return {
      title,
      headerLeft: () => (
        <HeaderLeftButton onPress={quitQuiz} titleStyle={styles.headerText} />
      ),
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      currentPage: this.props.route.params?.currentPage ?? 1,
      maxPages: this.props.route.params?.maxPages ?? 25,
      domain: this.props.route.params?.domain,
      isFirstQuestion: this.props.route.params?.isFirstQuestion ?? true,
      isLastQuestion: this.props.route.params?.isLastQuestion ?? false,
      totalCorrect: this.props.route.params?.totalCorrect ?? 0,
      totalIncorrect: this.props.route.params?.totalIncorrect ?? 0,
      quizSet: this.props.route.params?.quizSet,
      bubbleWidth: curvedScale(Image.resolveAssetSource(noCheckMark).width),
      bubbleHeight: curvedScale(Image.resolveAssetSource(noCheckMark).height),
      answeredQuestions: [],
      selectedAnswer: {},
      answerPressed: false,
      submitPressed: false,
      correctAnswerSelected: false,
      currentQuestion: {},
      isLoading: true,
      cptVersion: this.props.route.params?.cptVersion,
    };
  }

  componentDidMount() {
    this.configureQuizSet();
    this.props.navigation.setParams({ quitQuiz: this.quitQuiz });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', {
        screen_name: 'domain_quiz',
        domain: this.state.domain.domainName,
        cpt_version: this.state.cptVersion,
      });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  handleStateUpdate(previous) {
    const { answeredQuestions } = this.state;
    this.props.navigation.setParams({
      title: `${this.state.currentPage + (previous ? -1 : 1)} of ${
        this.state.maxPages
      }`,
    });
    let selectedAnswer = {};
    if (
      answeredQuestions.length
      > this.state.currentPage - (previous ? 2 : 0)
    ) {
      // This question has already been submitted
      if (answeredQuestions[this.state.currentPage - (previous ? 2 : 0)]) {
        const { answers } = answeredQuestions[
          this.state.currentPage - (previous ? 2 : 0)
        ];
        const [getAnswer] = answers.filter((answer) => answer.selected === true);
        selectedAnswer = getAnswer;
        this.setState({
          currentQuestion:
            answeredQuestions[this.state.currentPage - (previous ? 2 : 0)],
          submitPressed: true,
          answerPressed: true,
          selectedAnswer,
        });
      } else {
        this.setState(
          {
            currentQuestion: this.state.quizSet[
              this.state.currentPage - (previous ? 2 : 0)
            ],
            answerPressed: false,
            submitPressed: false,
            selectedAnswer: {},
          },
          () => this.configureQuizSet(),
        );
      }
    } else {
      this.setState(
        {
          currentQuestion: this.state.quizSet[
            this.state.currentPage - (previous ? 2 : 0)
          ],
          answerPressed: false,
          submitPressed: false,
          selectedAnswer: {},
        },
        () => this.configureQuizSet(),
      );
    }
    this.setState({
      currentPage: this.state.currentPage + (previous ? -1 : 1),
      isLastQuestion:
        this.state.currentPage + (previous ? -1 : 1) === this.state.maxPages,
      isFirstQuestion: this.state.currentPage + (previous ? -1 : 1) === 1,
    });
  }

  onPressAnswerChoice(item) {
    // • Make sure that if another answer is selected, all other answer
    //   choices are deselected
    // • Set new selected answer
    const { currentQuestion } = this.state;
    const answerChoices = this.state.currentQuestion.answers;
    const oldItem = item;
    item.selected = true;
    answerChoices.map((answer) => {
      if (answer.id !== item.id) {
        answer.selected = false;
      }
      return answer;
    });
    const itemIndex = answerChoices.indexOf(oldItem);
    answerChoices[itemIndex] = item;
    currentQuestion.answers = answerChoices;
    this.setState({
      selectedAnswer: item,
      answerPressed: true,
      currentQuestion,
    });
    return item;
  }

  onPressNext = () => {
    // Navigate to either next question or summary report
    // (if the last question was just answered)
    if (this.state.isLastQuestion) {
      this.props.navigation.navigate('ExamSummaryReport', {
        totalCorrect: this.state.totalCorrect,
        resetTest: this.props.route.params?.resetTest,
        maxQuestions: this.state.maxPages,
        domain: this.state.domain,
        isPracticeExam: false,
        cptVersion: this.state.cptVersion,
      });
    } else {
      this.handleStateUpdate();
    }
  };

  onPressPrevious = () => {
    this.handleStateUpdate(true);
  };

  onPressSubmit = () => {
    // Total correct/incorrect answers are updated in state and in history
    let correctAnswerSelected;
    let { totalCorrect } = this.state;
    let { totalIncorrect } = this.state;
    if (this.state.selectedAnswer.correct) {
      correctAnswerSelected = true;
      totalCorrect = this.state.totalCorrect + 1;
    } else {
      correctAnswerSelected = false;
      totalIncorrect = this.state.totalIncorrect + 1;
    }
    this.setState(
      {
        currentQuestion: { ...this.state.currentQuestion, skipped: false },
        correctAnswerSelected,
        totalCorrect,
        totalIncorrect,
        submitPressed: true,
      },
      () => {
        this.updateTestStatus(
          this.state.currentQuestion,
          this.state.currentPage - 1,
        );
      },
    );
  };

  getAnswerChoiceButton(item) {
    // Configure radio button image based on selected/submitted answer choice
    if (item.selected && this.state.submitPressed && !item.correct) {
      return incorrectX;
    }
    if (item.selected) {
      return checkMark;
    }
    return noCheckMark;
  }

  getAnswerChoiceStyle(item) {
    // Answer choice styling is determined based on:
    // • Whether or not the answer has been submitted
    // • Whether or not the answer has been selected
    // • Whether or not the correct answer has been selected (red or green highlighting)
    if (item.correct && this.answerPressed && this.state.submitPressed) {
      return { ...styles.selectedAnswerText, color: colors.goodGreen };
    }
    if (item.correct && this.state.submitPressed) {
      return { ...styles.answerText, color: colors.goodGreen };
    }
    if (
      !item.correct
      && this.state.submitPressed
      && item === this.state.selectedAnswer
    ) {
      return { ...styles.answerText, color: colors.badRed };
    }
    if (item.selected) {
      return styles.selectedAnswerText;
    }
    return styles.answerText;
  }

  getScorePercentage() {
    return (
      ((this.state.maxPages - this.state.totalIncorrect)
        / this.state.maxPages)
      * 100
    ).toFixed(0);
  }

  getSubmitOpacity() {
    if (!this.state.submitPressed) {
      return this.state.answerPressed ? 1 : 0.5;
    }
    return 0;
  }

  quitQuiz = () => {
    this.props.navigation.goBack();
    analytics().logEvent('exam_exit', {
      domain: this.state.domain.domainName,
      questions_completed: this.state.currentPage,
      current_score: `${this.getScorePercentage()}`,
      cpt_version: this.state.cptVersion,
    });
  };

  randomizeAnswerChoices = (answerList) => {
    const answerListUpdated = answerList.sort(() => 0.5 - Math.random());
    return answerListUpdated;
  };

  updateTestStatus = (question, index) => {
    const { answeredQuestions } = this.state;
    answeredQuestions[index] = question;
    this.setState({ answeredQuestions });
  };

  configureQuizSet() {
    // • Randomizes answer choices so the correct answer is not predictable
    // • Adds a new key/value pair to each answer object stating whether the
    //   user selected it or not
    const currentQuestion = this.props.route.params?.quizSet[
      this.state.currentPage - 1
    ];
    currentQuestion.answers = this.randomizeAnswerChoices(
      currentQuestion.answers,
    );
    currentQuestion.answers.forEach((answer) => {
      answer.selected = false;
    });
    this.setState({ currentQuestion, selectedAnswer: {}, isLoading: false });
  }

  renderAnswerChoice = ({ item }) => (
    <TouchableOpacity
      key={item.id}
      style={styles.answerContainer}
      disabled={this.state.submitPressed}
      onPress={() => this.onPressAnswerChoice(item)}
    >
      <ScaledText style={this.getAnswerChoiceStyle(item)}>
        {item.text}
      </ScaledText>
      <View
        style={
          this.state.submitPressed && !item.correct && item.selected
            ? {
              ...styles.incorrectAnswerBorder,
              width: this.state.bubbleWidth,
              height: this.state.bubbleHeight,
            }
            : null
        }
      >
        <Image
          style={
            this.state.submitPressed && !item.correct && item.selected
              ? styles.incorrectAnswerButton
              : {
                width: this.state.bubbleWidth,
                height: this.state.bubbleHeight,
              }
          }
          source={this.getAnswerChoiceButton(item)}
        />
      </View>
    </TouchableOpacity>
  );

  renderFeedback() {
    // Only display question feedback if a question is submitted
    if (this.state.submitPressed) {
      if (!this.state.selectedAnswer.correct) {
        return (
          <HoverHeader
            backgroundColor="rgb(252, 231, 233)"
            textColor={colors.badRed}
            title={this.state.currentQuestion.feedback.incorrect.heading.text}
          />
        );
      }
      return (
        <HoverHeader
          backgroundColor="rgb(223, 245, 223)"
          textColor={colors.goodGreen}
          title={this.state.currentQuestion.feedback.correct.heading.text}
        />
      );
    }
    return null;
  }

  renderSubmitButton() {
    // • Bold submit button if an answer is selected
    // • Hide submit button if an question is submitted
    return (
      <TouchableOpacity
        disabled={!this.state.answerPressed}
        onPress={this.onPressSubmit}
      >
        <View
          style={{ ...styles.submitButton, opacity: this.getSubmitOpacity() }}
        >
          <ScaledText style={styles.submitButtonText}>Submit</ScaledText>
        </View>
      </TouchableOpacity>
    );
  }

  render() {
    if (this.state.isLoading) {
      return (
        <LoadingSpinner
          backgroundColor={colors.background}
          color={colors.duskBlue}
          titleTextStyle={{ color: colors.duskBlue }}
          visible={this.state.isLoading}
          title="Loading Question..."
        />
      );
    }
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <ExamProgressBar
          currentPage={this.state.currentPage}
          maxPages={this.state.maxPages}
        />
        {this.renderFeedback()}
        <View style={styles.listContainer}>
          <FlatList
            keyExtractor={(item) => item.id}
            data={this.state.currentQuestion.answers}
            renderItem={({ item }) => this.renderAnswerChoice({ item })}
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={(
              <View style={styles.questionContainer}>
                <ScaledText style={{ ...styles.domainText }}>
                  {this.state.domain.domainName}
                </ScaledText>
                <View style={styles.subheaderContainer}>
                  <ScaledText style={styles.scoreText}>
                    Score:
                    {' '}
                    {this.getScorePercentage()}
                    %
                  </ScaledText>
                </View>
                <ScaledText style={styles.questionText}>
                  {this.state.currentQuestion.question.text}
                </ScaledText>
              </View>
            )}
          />
          <Text style={styles.cptVersion}>
            CPT
            {this.state.cptVersion}
          </Text>
        </View>
        <View
          style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
            left: 0,
          }}
        >
          <View>
            <View style={styles.bottomButtonContainer}>
              <TouchableOpacity
                style={{ padding: 6, right: scaleWidth(9) }}
                disabled={this.state.isFirstQuestion}
                onPress={this.onPressPrevious}
              >
                <View
                  style={{
                    ...styles.arrowButtonBorder,
                    opacity: this.state.isFirstQuestion ? 0 : 1,
                  }}
                >
                  <Image source={leftArrow} style={styles.arrowButton} />
                </View>
              </TouchableOpacity>
              {this.renderSubmitButton()}
              <TouchableOpacity
                style={{ padding: 6, left: scaleWidth(9) }}
                onPressIn={this.onPressNext}
              >
                <View style={styles.arrowButtonBorder}>
                  <Image source={rightArrow} style={styles.arrowButton} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: colors.white,
  },
  listContainer: {
    flex: 1,
    padding: 10,
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.white,
  },
  questionContainer: {
    marginHorizontal: '2.5%',
    marginTop: 35,
    marginBottom: 50,
  },
  subheaderContainer: {
    flexDirection: 'row',
    marginTop: 15,
  },
  domainText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  scoreText: {
    color: colors.subGrey,
  },
  questionText: {
    marginTop: 15,
    marginBottom: 17,
    fontSize: 17,
  },
  answerContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderColor: 'rgba(124, 128, 132, 0.1)',
    borderBottomWidth: 2,
    padding: scaleWidth(4),
  },
  answerText: {
    flex: 1,
    fontFamily: 'Avenir',
    fontSize: 16,
    color: colors.subGrey,
    marginRight: scaleWidth(2),
  },
  selectedAnswerText: {
    flex: 1,
    fontFamily: 'Avenir',
    fontSize: 16,
    fontWeight: '700',
    color: colors.black,
    marginRight: scaleWidth(2),
  },
  incorrectAnswerButton: {
    marginTop: width > 750 ? 10 : '18%',
    alignSelf: 'center',
    padding: width > 750 ? 10 : '1.3%',
  },
  incorrectAnswerBorder: {
    borderRadius: 20,
    backgroundColor: 'rgb(208, 2, 27)',
  },
  bottomButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: scaleHeight(4),
    marginTop: 6,
  },
  arrowButton: {
    flex: 1,
    height: 5,
    width: 5,
    padding: scaleHeight(1.6),
  },
  arrowButtonBorder: {
    borderWidth: scaleHeight(0.3),
    borderColor: 'rgb(232, 234, 236)',
    borderRadius: 80,
    justifyContent: 'center',
    padding: scaleWidth(2.8),
    marginHorizontal: 20,
    backgroundColor: colors.white,
  },
  submitButton: {
    backgroundColor: colors.macaroniAndCheese,
    borderRadius: scaleWidth(10),
    minWidth: '30%',
    padding: '10%',
    ...shadow,
  },
  submitButtonText: {
    color: colors.white,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cptVersion: {
    fontSize: 14,
    color: 'rgb(182, 189, 195)',
    textAlign: 'center',
    marginTop: scaleHeight(2),
    marginBottom: scaleHeight(14),
  },
};

export default DomainQuiz;
