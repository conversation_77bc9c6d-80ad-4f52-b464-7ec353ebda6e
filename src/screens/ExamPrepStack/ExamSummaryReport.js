import React, { Component } from 'react';
import {
  View,
  TouchableOpacity,
  Dimensions,
  Image,
  FlatList,
  ScrollView,
  BackHandler,
  StatusBar,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';

// Components
import { ExamTotalScore, ExamDomainScore, ScaledText } from '../../components';
import nasm from '../../dataManager/apiConfig';
import { scaleHeight, scaleWidth } from '../../util/responsive';

// Styles
import { colors, shadow } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const DeviceInfo = require('react-native-device-info');

const xBtn = require('../../resources/btnCloseWhite.png');

const domainColors = [
  colors.peaGreen,
  colors.nasmBlue,
  colors.macaroniAndCheese,
  colors.pinkishPurple,
  colors.azure,
  colors.goodGreen,
];

class ExamSummaryReport extends Component {
  static navigationOptions = ({ navigation }) => ({
    headerTransparent: true,
    headerStyle: {
      backgroundColor: 'rgba(0, 0, 0, 0)',
      borderBottomWidth: 0,
    },
    headerLeft: () => (
      <HeaderLeftButton
        onPress={() => navigation.navigate('Account')}
        buttonImage={(
          <Image
            style={{ tintColor: colors.veryLightBlue, opacity: 0 }}
            source={xBtn}
          />
        )}
      />
    ),
  });

  constructor(props) {
    super(props);
    this.state = {
      isPracticeExam: this.props.route.params?.isPracticeExam ?? false,
      domains: this.props.route.params?.domains,
      domainScores: this.props.route.params?.domainScores,
      retakeDisabled: false,
      cptVersion: this.props.route.params?.cptVersion,
    };
  }

  componentDidMount() {
    // Making sure the physical back button on Android devices will lead back to the catalog page
    BackHandler.addEventListener('hardwareBackPress', this.onPressBack);
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', {
        screen_name: 'practice_exam',
        cpt_version: this.state.cptVersion,
      });
      this.setState({ retakeDisabled: false });
    });
    const score = `${this.props.route.params?.totalCorrect ?? 0} / ${
      this.props.route.params?.maxQuestions ?? 25
    }`;
    if (this.state.isPracticeExam) {
      analytics().logEvent('exam_complete', {
        domain: 'practice_exam_full',
        score,
        cpt_version: this.state.cptVersion,
      });
    } else {
      const domain = this.props.route.params?.domain ?? {};
      analytics().logEvent('exam_complete', {
        score,
        domain: domain.domainName,
        cpt_version: this.state.cptVersion,
      });
    }
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onPressBack);
    this.unsubscribeFocus();
  }

  onPressBack = () => {
    this.props.navigation.navigate('ExamQuestionsCatalog');
    return true;
  };

  onPressDone = () => {
    this.props.navigation.navigate('ExamQuestionsCatalog');
  };

  onPressTakeAgain = () => {
    if (this.state.isPracticeExam) {
      this.generateNewPracticeExam();
    } else {
      this.generateNewDomainQuiz(this.props.route.params?.domain);
    }
  };

  calculateDomainWeight(weight) {
    return Math.round(this.props.route.params?.maxQuestions * (weight / 100));
  }

  generateNewDomainQuiz(domain) {
    this.setState({ retakeDisabled: true });
    nasm.api
      .getQuizSetByDomain(domain.domainId, this.state.cptVersion)
      .then((response) => {
        const resetTest = this.props.route.params?.resetTest;
        resetTest();
        this.props.navigation.push('DomainQuiz', {
          domain,
          quizSet: response,
          resetTest,
          cptVersion: this.state.cptVersion,
        });
      });
  }

  generateNewPracticeExam() {
    nasm.api.getPracticeExamQuestions(this.state.cptVersion).then((response) => {
      const resetTest = this.props.route.params?.resetTest;
      resetTest();
      this.props.navigation.navigate('PracticeExamInstructions', {
        domains: this.state.domains,
        cptVersion: this.state.cptVersion,
        practiceExam: response,
        resetTest,
      });
    });
  }

  renderDomainScores() {
    if (this.state.isPracticeExam) {
      return (
        <View>
          <FlatList
            scrollEnabled={false}
            keyExtractor={(item) => item.domainId}
            data={this.props.route.params?.domains}
            renderItem={({ item, index }) => (
              <ExamDomainScore
                key={index}
                domain={item.domainName}
                color={domainColors[index]}
                score={this.state.domainScores[item.domainId]}
                domainTotal={this.calculateDomainWeight(
                  item.practiceRatioPercent,
                )}
              />
            )}
            extraData={this.props}
            contentContainerStyle={styles.domainList}
          />
        </View>
      );
    }
    return null;
  }

  renderResults = () => (
    <View
      style={{
        ...styles.resultsContainer,
        marginTop: this.state.isPracticeExam ? scaleHeight(-6) : 0,
      }}
    >
      <View
        style={
          !this.state.isPracticeExam
            ? { flex: 1, justifyContent: 'center' }
            : null
        }
      >
        <ExamTotalScore
          totalCorrect={this.props.route.params?.totalCorrect ?? 0}
          maxQuestions={this.props.route.params?.maxQuestions ?? 25}
        />
      </View>
      {this.renderDomainScores()}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          disabled={this.state.retakeDisabled}
          onPress={this.onPressTakeAgain}
          style={styles.takeItAgainButtonView}
        >
          <ScaledText style={styles.takeItAgainButton}>
            Take it again
          </ScaledText>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.doneButtonView}
          onPress={this.onPressDone}
        >
          <ScaledText style={styles.doneButton}>Done</ScaledText>
        </TouchableOpacity>
      </View>
    </View>
  );

  renderResultsContainer = () => {
    if (this.state.isPracticeExam) {
      return (
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'space-between',
          }}
        >
          {this.renderResults()}
        </ScrollView>
      );
    }
    return this.renderResults();
  };

  render() {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="dark-content" />
        {this.renderResultsContainer()}
      </View>
    );
  }
}

const { width } = Dimensions.get('window');

const styles = {
  container: {
    flex: 1,
  },
  resultsContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  buttonContainer: {
    flexDirection: DeviceInfo.isTablet() ? 'row' : 'column',
    justifyContent: DeviceInfo.isTablet() ? 'space-around' : 'flex-start',
    marginTop: DeviceInfo.isTablet() ? 18 : 22,
    marginBottom: 45,
    marginHorizontal: DeviceInfo.isTablet() ? 10 : 0,
  },
  domainList: {
    alignSelf: 'center',
    width,
  },
  takeItAgainButtonView: {
    borderRadius: DeviceInfo.isTablet() ? scaleWidth(4.5) : scaleWidth(6.4),
    minWidth: DeviceInfo.isTablet() ? '40%' : '85%',
    padding: DeviceInfo.isTablet() ? '3%' : '4%',
    backgroundColor: colors.duskBlue,
    color: colors.white,
    textAlign: 'center',
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    ...shadow,
  },
  takeItAgainButton: {
    fontSize: 15,
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    letterSpacing: 0.8,
  },
  doneButtonView: {
    minWidth: DeviceInfo.isTablet() ? '40%' : '85%',
    borderWidth: scaleHeight(0.2),
    borderRadius: DeviceInfo.isTablet() ? scaleWidth(4.5) : scaleWidth(6.4),
    borderColor: colors.bordergrey,
    padding: DeviceInfo.isTablet() ? '3%' : '4%',
    textAlign: 'center',
    alignSelf: 'center',
    alignItems: 'center',
    marginTop: DeviceInfo.isTablet() ? 0 : 20,
  },
  doneButton: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Heavy',
    letterSpacing: 0.8,
  },
};

export default ExamSummaryReport;
