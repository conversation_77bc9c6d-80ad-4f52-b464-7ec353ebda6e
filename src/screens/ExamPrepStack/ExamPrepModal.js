import React, { Component } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { View } from 'react-native';
import { getScreensFromRoutes } from '../../constants';
import { HeaderLeftButton } from '../../components';
import { header } from '../../styles';
import screens from './index';

const Stack = createStackNavigator();

class ExamPrepModal extends Component {
  render() {
    return (
      <Stack.Navigator
        screenOptions={({ route, navigation }) => {
          const headerOption = { ...header.default };
          if (route.name === 'ModalStack') {
            headerOption.headerShown = false;
          }
          return {
            title: null,
            gestureEnabled: false,
            headerBackTitle: null,
            headerTitleAlign: 'center',
            headerLeft: () => (
              <HeaderLeftButton onPress={() => navigation.goBack()} />
            ),
            headerRight: () => <View />, // empty view helps to center headers on Android
            ...headerOption,
          };
        }}
      >
        {getScreensFromRoutes(Stack, screens)}
      </Stack.Navigator>
    );
  }
}

export default ExamPrepModal;
