import React, { Component } from 'react';

// Components
import {
  View,
  TouchableOpacity,
  Image,
  ImageBackground,
  Platform,
} from 'react-native';
import { ScaledText } from '../../components';
import { scaleWidth, scaleHeight } from '../../util/responsive';

// Styles
import { colors } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';

// Images
const backgroundImg = require('../../resources/instructionscopy.png');
const backBtn = require('../../resources/btnBackWhite.png');

class PracticeExamInstructions extends Component {
  static navigationOptions = ({ navigation }) => ({
    headerTransparent: true,
    headerStyle: {
      backgroundColor: 'rgba(0, 0, 0, 0)',
      borderBottomWidth: 0,
    },
    headerLeft: () => (
      <HeaderLeftButton
        onPress={() => navigation.goBack()}
        buttonImage={<Image style={styles.backButton} source={backBtn} />}
      />
    ),

    headerRight: () => (
      <View style={styles.rightNavView}>
        <View style={styles.closedCircle} />
        <View style={styles.openCircle} />
      </View>
    ),
  });

  onPressStart = () => {
    this.props.navigation.navigate('DomainOverview', {
      domains: this.props.route.params?.domains,
      practiceExam: this.props.route.params?.practiceExam,
      resetTest: this.props.route.params?.resetTest,
      cptVersion: this.props.route.params?.cptVersion,
    });
  };

  render() {
    return (
      <View style={styles.container}>
        <ImageBackground style={styles.background} source={backgroundImg}>
          <View style={styles.content}>
            <View style={styles.textContainer}>
              <ScaledText style={styles.titleText}>Before You Begin</ScaledText>
              <ScaledText style={styles.contentText}>
                The Practice Exam emulates the experience of your proctored
                final exam. You will receive an overall score and a score per
                domain upon completion. Use the results to identify areas for
                improvement and return to the individual domain quizzes to
                improve in each area before attempting the Practice Exam again.
                You have unlimited attempts with new questions each time.
              </ScaledText>
            </View>
            <TouchableOpacity
              style={styles.startButton}
              onPress={this.onPressStart}
            >
              <ScaledText style={styles.startButtonText}>Start</ScaledText>
            </TouchableOpacity>
          </View>
        </ImageBackground>
      </View>
    );
  }
}

const styles = {
  container: {
    flex: 1,
  },
  backButton: {
    marginTop: Platform.OS === 'ios' ? scaleHeight(1.5) : scaleHeight(5),
    tintColor: colors.veryLightBlue,
    padding: 1,
  },
  rightNavView: {
    padding: 30,
    flexDirection: 'row',
    marginTop: Platform.OS === 'ios' ? scaleHeight(1.5) : scaleHeight(5),
  },
  closedCircle: {
    width: 12,
    height: 12,
    backgroundColor: colors.white,
    borderRadius: 6,
    marginRight: 12,
  },
  openCircle: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: colors.white,
  },
  background: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    marginVertical: 70,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
    marginHorizontal: scaleWidth(12),
  },
  titleText: {
    fontSize: 32,
    color: colors.white,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  contentText: {
    color: colors.white,
    textAlign: 'center',
    lineHeight: scaleHeight(3),
  },
  startButton: {
    width: '30%',
    height: '8%',
    alignSelf: 'center',
    justifyContent: 'center',
    backgroundColor: colors.azure,
    borderRadius: scaleWidth(10),
  },
  startButtonText: {
    fontWeight: 'bold',
    textAlign: 'center',
    color: colors.white,
  },
};

export default PracticeExamInstructions;
