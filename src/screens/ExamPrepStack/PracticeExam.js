import React, { Component } from 'react';

// Components
import {
  FlatList, Image, Text, TouchableOpacity, View,
} from 'react-native';

import analytics from '@react-native-firebase/analytics';
import { ExamProgressBar, ScaledText } from '../../components';
import { curvedScale, scaleHeight, scaleWidth } from '../../util/responsive';

// Analytics

// Styles
import { colors, shadow } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';

// Images
const checkMark = require('../../resources/imgCheckmarkM.png');
const noCheckMark = require('../../resources/imgCheckmarkOffM.png');
const rightArrow = require('../../resources/imgRightArrowGray.png');
const leftArrow = require('../../resources/imgLeftArrowGray.png');
const gradient = require('../../resources/whiteGradient.png');

class PracticeExam extends Component {
  static navigationOptions = ({ route }) => {
    const title = route.params?.title ?? '1 of 120';
    const quitExam = route.params?.quitExam ?? (() => {});
    return {
      title,
      headerLeft: () => (
        <HeaderLeftButton onPress={quitExam} titleStyle={styles.headerText} />
      ),
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      currentPage: this.props.route.params?.currentPage ?? 1,
      maxPages: this.props.route.params?.maxPages ?? 120,
      domain: this.props.route.params?.domain,
      practiceExam: this.props.route.params?.practiceExam,
      isFirstQuestion: this.props.route.params?.isFirstQuestion ?? true,
      isLastQuestion: this.props.route.params?.isLastQuestion ?? false,
      totalCorrect: this.props.route.params?.totalCorrect ?? 0,
      domainScores: this.props.route.params?.domainScores ?? {},
      isLoading: this.props.route.params?.isLoading ?? true,
      cptVersion: this.props.route.params?.cptVersion,
      bubbleWidth: curvedScale(Image.resolveAssetSource(noCheckMark).width),
      bubbleHeight: curvedScale(Image.resolveAssetSource(noCheckMark).height),
      answerPressed: false,
      submitPressed: false,
      previouslyAnswered: false,
      answerChanged: false,
      previouslySelectedAnswer: null,
      selectedAnswer: {},
      allSelectedAnswers: {},
      currentQuestion: {},
      answeredQuestions: [],
      quittingExam: false,
    };
  }

  componentDidMount() {
    if (this.state.isFirstQuestion) {
      this.initializeDomainScores();
    }
    this.configurePracticeQuestion();
    this.props.navigation.setParams({ quitExam: this.quitExam });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', {
        screen_name: 'practice_exam',
        cpt_version: this.state.cptVersion,
      });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  handleStateUpdate(previous) {
    // Checking to see if a given question has already been answered.
    // If so, then the user is met with their previously selected answer
    // to the question
    this.props.navigation.setParams({
      title: `${this.state.currentPage + (previous ? -1 : 1)} of ${
        this.state.maxPages
      }`,
    });
    const { answeredQuestions } = this.state;
    let selectedAnswer = {};
    if (
      answeredQuestions.length
      > this.state.currentPage - (previous ? 2 : 0)
    ) {
      if (answeredQuestions[this.state.currentPage - (previous ? 2 : 0)]) {
        const { answers } = answeredQuestions[
          this.state.currentPage - (previous ? 2 : 0)
        ];
        const [getAnswer] = answers.filter((answer) => answer.selected === true);
        selectedAnswer = getAnswer;
        this.setState({
          currentQuestion:
            answeredQuestions[this.state.currentPage - (previous ? 2 : 0)],
          answerPressed: true,
          previouslyAnswered: !!selectedAnswer,
          selectedAnswer,
        });
      }
    } else {
      this.setState(
        {
          currentQuestion: this.state.practiceExam[
            this.state.currentPage - (previous ? 2 : 0)
          ],
          answerPressed: false,
          previouslyAnswered: false,
          selectedAnswer: {},
        },
        () => this.configurePracticeQuestion(),
      );
    }

    this.setState({
      currentPage: this.state.currentPage + (previous ? -1 : 1),
      isLastQuestion:
        this.state.currentPage + (previous ? -1 : 1) === this.state.maxPages,
      isFirstQuestion: this.state.currentPage + (previous ? -1 : 1) === 1,
      answerChanged: false,
    });
  }

  onPressAnswerChoice(item) {
    // • Set current selected answer as previously selected answer
    // • Make sure that if another answer is selected, all other answer
    //   choices are deselected
    // • Set new selected answer
    let { currentQuestion } = this.state;
    const answerChoices = this.state.currentQuestion.answers;
    currentQuestion = {
      ...currentQuestion,
      previouslySelectedAnswer: this.state.selectedAnswer,
    };
    const oldItem = item;
    item.selected = true;
    answerChoices.map((answer) => {
      if (answer.id !== item.id) {
        answer.selected = false;
      }
      return answer;
    });
    const itemIndex = answerChoices.indexOf(oldItem);
    answerChoices[itemIndex] = item;
    currentQuestion.answers = answerChoices;
    this.setState({
      selectedAnswer: item,
      answerPressed: true,
      currentQuestion,
      answerChanged: true,
    });
    return item;
  }

  onPressSubmit = (totalCorrect, domainScores) => {
    // • Navigate to summary report page
    this.props.navigation.navigate('ExamSummaryReport', {
      totalCorrect,
      domainScores,
      maxQuestions: this.state.maxPages,
      isPracticeExam: true,
      domains: this.props.route.params?.domains,
      resetTest: this.props.route.params?.resetTest,
      cptVersion: this.props.route.params?.cptVersion,
    });
  };

  getAnswerChoiceButton = (item) => {
    // Configuring answer choice radio button
    if (item.selected) {
      return checkMark;
    }
    return noCheckMark;
  };

  getAnswerChoiceStyle = (item) => {
    // bold/un-bold answer choice text if answer is selected/deselected
    if (item.selected) {
      return styles.selectedAnswerText;
    }
    return styles.answerText;
  };

  getSubmitOpacity() {
    if (this.state.isLastQuestion) {
      return 1;
    }
    return 0;
  }

  calculateScores = () => {
    const { allSelectedAnswers } = this.state;
    let totalCorrect = 0;
    const domainScores = this.initializeDomainScores();
    const finalAnswers = Object.values(allSelectedAnswers);
    for (const item in finalAnswers) {
      if (
        finalAnswers[item]
        && finalAnswers[item].answer
        && finalAnswers[item].answer.correct
      ) {
        totalCorrect += 1;
        const { domain } = finalAnswers[item];
        domainScores[domain] += 1;
      }
    }
    if (!this.state.quittingExam) {
      this.onPressSubmit(totalCorrect, domainScores);
    } else {
      return totalCorrect;
    }
    return null;
  };

  onPressPrevious = () => {
    this.handleStateUpdate(true);
  };

  onPressNext = () => {
    // • Make sure score is updated correctly
    // • Navigate to next question with updated info
    this.updateAnswerList();
    if (this.state.isLastQuestion) {
      this.calculateScores();
    } else {
      this.handleStateUpdate();
    }
  };

  quitExam = () => {
    this.setState({ quittingExam: true }, () => {
      const totalCorrect = this.calculateScores();
      analytics().logEvent('exam_exit', {
        domain: 'practice_exam_full',
        questions_completed: this.state.currentPage,
        current_score: `${totalCorrect || 0} / ${this.state.maxPages}`,
        cpt_version: this.state.cptVersion,
      });
      this.props.navigation.navigate('ExamQuestionsCatalog');
    });
  };

  randomizeAnswerChoices = (answerList) => {
    const answerListUpdate = answerList.sort(() => 0.5 - Math.random());
    return answerListUpdate;
  };

  updateTestStatus = (question, index) => {
    const { answeredQuestions } = this.state;
    answeredQuestions[index] = question;
    this.setState({ answeredQuestions });
  };

  updateAnswerList() {
    this.updateTestStatus(
      this.state.currentQuestion,
      this.state.currentPage - 1,
    );
    const domain = this.state.currentQuestion.details.tags[0].text;
    const selectedAnswer = { domain, answer: this.state.selectedAnswer };
    const { allSelectedAnswers } = this.state;
    allSelectedAnswers[
      this.state.currentQuestion.details.questionId
    ] = selectedAnswer;
    this.setState({ allSelectedAnswers });
  }

  initializeDomainScores() {
    // Setting separate scores for each domain represented in the practice exam
    const domainScores = {};
    this.props.route.params?.domains?.forEach((domain) => {
      domainScores[domain.domainId] = 0;
    });
    return domainScores;
  }

  configurePracticeQuestion() {
    // • Randomizes answer choices so the correct answer is not predictable
    // • Adds a new key/value pair to each answer object stating whether the
    //   user selected it or not
    const currentQuestion = this.state.practiceExam[this.state.currentPage - 1];
    if (currentQuestion) {
      currentQuestion.answers = this.randomizeAnswerChoices(
        currentQuestion.answers,
      );
      currentQuestion.answers.forEach((answer) => {
        answer.selected = false;
      });
    }
    this.setState({ currentQuestion, isLoading: false });
  }

  renderAnswerChoice = ({ item }) => (
    <TouchableOpacity
      key={item.id}
      style={styles.answerContainer}
      disabled={this.state.submitPressed}
      onPress={() => this.onPressAnswerChoice(item)}
    >
      <ScaledText style={this.getAnswerChoiceStyle(item)}>
        {item.text}
      </ScaledText>
      <Image
        source={this.getAnswerChoiceButton(item)}
        style={{
          width: this.state.bubbleWidth,
          height: this.state.bubbleHeight,
        }}
      />
    </TouchableOpacity>
  );

  renderSubmitButton() {
    // Only render submit button on last question
    return (
      <TouchableOpacity
        disabled={!this.state.isLastQuestion}
        onPress={this.calculateScores}
      >
        <View
          style={{ ...styles.submitButton, opacity: this.getSubmitOpacity() }}
        >
          <ScaledText style={styles.submitButtonText}>Submit</ScaledText>
        </View>
      </TouchableOpacity>
    );
  }

  render() {
    if (this.state.isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ScaledText style={styles.loadingText}>
            Loading Question...
          </ScaledText>
        </View>
      );
    }
    return (
      <View style={styles.container}>
        <ExamProgressBar
          currentPage={this.state.currentPage}
          maxPages={this.state.maxPages}
        />
        <View style={styles.listContainer}>
          {this.state.currentQuestion.question
          && this.state.currentQuestion.question.text ? (
            <View style={styles.questionContainer}>
              <ScaledText style={styles.questionText}>
                {this.state.currentQuestion.question.text}
              </ScaledText>
            </View>
            ) : null}
          <FlatList
            contentContainerStyle={{ flex: 1, paddingBottom: 120 }}
            keyExtractor={(item) => item.id}
            data={this.state.currentQuestion.answers}
            renderItem={({ item }) => this.renderAnswerChoice({ item })}
          />
        </View>
        <View
          style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
            left: 0,
          }}
        >
          <Text style={styles.cptVersion}>
            CPT
            {this.state.cptVersion}
          </Text>
          <View>
            <Image
              source={gradient}
              resizeMode="stretch"
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                right: 0,
                bottom: 0,
                height: '100%',
              }}
            />
            <View
              style={{
                ...styles.bottomButtonContainer,
                marginBottom: scaleHeight(4),
              }}
            >
              <TouchableOpacity
                style={{ padding: 6, right: scaleWidth(9) }}
                disabled={this.state.isFirstQuestion}
                onPress={this.onPressPrevious}
              >
                <View
                  style={{
                    ...styles.arrowButtonBorder,
                    opacity: this.state.isFirstQuestion ? 0 : 1,
                  }}
                >
                  <Image source={leftArrow} style={styles.arrowButton} />
                </View>
              </TouchableOpacity>
              {this.renderSubmitButton()}
              <TouchableOpacity
                style={{ padding: 6, left: scaleWidth(9) }}
                onPress={this.onPressNext}
              >
                <View style={{ ...styles.arrowButtonBorder }}>
                  <Image source={rightArrow} style={styles.arrowButton} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  listContainer: {
    flex: 1,
    padding: 10,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 20,
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.white,
  },
  questionContainer: {
    marginHorizontal: '2.5%',
    marginVertical: 20,
    marginBottom: 50,
  },
  questionText: {
    marginTop: 15,
    fontSize: 17,
  },
  answerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderColor: 'rgba(124, 128, 132, 0.1)',
    borderBottomWidth: 2,
    padding: scaleWidth(4),
  },
  answerText: {
    flex: 1,
    fontFamily: 'Avenir',
    fontSize: 16,
    paddingRight: 10,
    color: colors.subGrey,
    marginRight: '2%',
  },
  selectedAnswerText: {
    flex: 1,
    fontFamily: 'Avenir',
    fontSize: 16,
    fontWeight: '700',
    color: colors.black,
    marginRight: '2%',
  },
  bottomButtonContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: scaleHeight(4),
    marginTop: 6,
  },
  arrowButton: {
    flex: 1,
    height: 5,
    width: 5,
    padding: scaleHeight(1.6),
  },
  arrowButtonBorder: {
    borderWidth: scaleHeight(0.3),
    borderColor: 'rgb(232, 234, 236)',
    borderRadius: 80,
    justifyContent: 'center',
    padding: scaleHeight(1.7),
    marginHorizontal: 20,
    backgroundColor: colors.white,
  },
  submitButton: {
    backgroundColor: colors.macaroniAndCheese,
    borderRadius: scaleWidth(10),
    minWidth: '30%',
    padding: '10%',
    ...shadow,
  },
  submitButtonText: {
    color: colors.white,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cptVersion: {
    fontSize: 14,
    color: 'rgb(182, 189, 195)',
    textAlign: 'center',
    paddingBottom: scaleHeight(1),
  },
};

export default PracticeExam;
