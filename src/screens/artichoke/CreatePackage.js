import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, StyleSheet, Platform } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  createPackageAction,
  clearSelectedServiceAction,
  clearNewPackageAction,
  setTaxRateAction,
  setPaymentIntervalAction,
  setExpireTypeAction,
  setExpireDaysCustomAction,
  setPackageNameAction,
  setPackagePriceAction,
  setPaymentCountAction,
  setSellPackageOnlineEnableAction,
  setPackageServicesAction,
  setPackageTaxAction,
  setPackageTaxRateAction,
  setPackageDescriptionAction,
  setServicesTotalValueAction,
} from '../../actions/artichoke/Services.actions';
import CreatePackageForm from '../../components/artichoke/screensComponents/services/CreatePackageForm';
import { removeEmojis } from '../../util/utils';

class CreatePackage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      flatRateTax: 0.0,
    };
    this.props.clearNewPackageAction();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.newPackage.price !== this.props.newPackage.price) {
      this.props.setPackageTaxAction(this.calculateTax());
      if (this.props.newPackage.taxRate === 'FLATRATE') {
        this.onChangeFlatRateTax(this.props.newPackage.taxPercentage);
      }
    }
    if (
      prevProps.newPackage.servicesTotalValue
      !== this.props.newPackage.servicesTotalValue
    ) {
      this.props.setPackageTaxAction(this.calculateTax());
    }
  }

  onChangeFlatRateTax = (value) => {
    this.setState({ flatRateTax: value });
    if (this.props.newPackage.price) {
      const tax = parseFloat(this.props.newPackage.price) * (parseFloat(value) / 100);
      this.props.setPackageTaxAction(tax.toFixed(2).toString());
      this.props.setPackageTaxRateAction(value);
    } else {
      this.props.setPackageTaxAction('0.00');
    }
  };

  onSubmitPackage = () => {
    const newPackage = {
      expireType: this.props.newPackage.expireType,
      expireDaysCustom: this.props.newPackage.expireDaysCustom,
      name: removeEmojis(this.props.newPackage.packageName),
      packageProducts: this.mapSelectedServicesToPackageProducts(
        this.props.newPackage.selectedServices,
      ),
      paymentInterval: this.props.newPackage.paymentInterval,
      price: this.props.newPackage.price,
      repeatCount: parseInt(this.props.newPackage.repeatCount, 10),
      tax: this.props.newPackage.tax,
      taxRate: this.props.newPackage.taxPercentage,
      taxType: this.props.newPackage.taxRate,
      sellOnlineEnabled: this.props.newPackage.sellOnlineEnabled,
      packageDescription: removeEmojis(
        this.props.newPackage.packageDescription,
      ),
    };
    this.props.createPackageAction(newPackage);
  };

  calculateTax = () => {
    if (
      this.props.newPackage.price === null
      || this.props.newPackage.price === 0
    ) {
      return 0;
    }
    let tax = 0.0;
    if (this.props.newPackage.selectedServices.length) {
      const totalservicesValue = this.props.newPackage.servicesTotalValue;
      this.props.newPackage.selectedServices.map((serviceItem) => {
        if (serviceItem.tax !== 0) {
          const productValue = serviceItem.price * serviceItem.quantity;
          const split = productValue / totalservicesValue;
          tax += (split * this.props.newPackage.price * serviceItem.tax) / 100;
        }
        return tax;
      });
    }
    if (!tax || Number.isNaN(tax)) {
      return 0;
    }
    return tax.toFixed(2);
  };

  mapSelectedServicesToPackageProducts = (services) => {
    const packageProducts = services.map((item) => ({
      productId: item.productId,
      quantity: item.quantity,
      unlimited: item.unlimited,
    }));
    return packageProducts;
  };

  render() {
    return (
      <KeyboardAwareScrollView
        resetScrollToCoords={{ x: 0, y: 0 }}
        contentContainerStyle={styles.container}
        scrollEnabled={false}
        extraHeight={100}
        extraScrollHeight={Platform.OS === 'ios' ? 0 : 40}
        enableOnAndroid
        enableAutomaticScroll
        keyboardShouldPersistTaps="handled"
      >
        <SafeAreaView style={styles.container}>
          <CreatePackageForm
            onSubmitForm={this.onSubmitPackage}
            navigation={this.props.navigation}
            newPackage={this.props.newPackage}
            setTaxRateAction={this.props.setTaxRateAction}
            setPaymentIntervalAction={this.props.setPaymentIntervalAction}
            setExpireTypeAction={this.props.setExpireTypeAction}
            setExpireDaysCustomAction={this.props.setExpireDaysCustomAction}
            setPackageNameAction={this.props.setPackageNameAction}
            setPackagePriceAction={this.props.setPackagePriceAction}
            setPaymentCountAction={this.props.setPaymentCountAction}
            setSellPackageOnlineEnableAction={
              this.props.setSellPackageOnlineEnableAction
            }
            setPackageServicesAction={this.props.setPackageServicesAction}
            setPackageTaxAction={this.props.setPackageTaxAction}
            setPackageTaxRateAction={this.props.setPackageTaxRateAction}
            setPackageDescriptionAction={this.props.setPackageDescriptionAction}
            setServicesTotalValueAction={this.props.setServicesTotalValueAction}
            calculateTax={this.calculateTax}
            flatRateTax={this.state.flatRateTax}
            onChangeFlatRateTax={this.onChangeFlatRateTax}
            savePackageLoading={this.props.savePackageLoading}
            symbol={this.props.symbol}
          />
        </SafeAreaView>
      </KeyboardAwareScrollView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newPackage: state.services.newPackage,
  savePackageLoading: state.loadingComponents.savePackageLoading,
  symbol: state.user.accountSettings?.symbol,
});
export default connect(mapStateToProps, {
  createPackageAction,
  clearSelectedServiceAction,
  clearNewPackageAction,
  setTaxRateAction,
  setPaymentIntervalAction,
  setExpireTypeAction,
  setExpireDaysCustomAction,
  setPackageNameAction,
  setPackagePriceAction,
  setPaymentCountAction,
  setSellPackageOnlineEnableAction,
  setPackageServicesAction,
  setPackageTaxAction,
  setPackageTaxRateAction,
  setPackageDescriptionAction,
  setServicesTotalValueAction,
})(CreatePackage);
