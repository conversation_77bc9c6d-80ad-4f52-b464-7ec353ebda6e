import React, { Component } from 'react';
import { Text } from 'react-native';
import { connect } from 'react-redux';
import { getUserDataAction } from '../../actions/artichoke/User.actions';
import { APPLICATION_ROUTES } from '../../constants';

class StripeCallback extends Component {
  constructor(props) {
    super(props);
    this.props.getUserDataAction();
  }

  componentDidMount() {
    this.props.navigation.navigate('BottomTab', { screen: 'Account' });
  }

  render() {
    return <Text>Please wait for redirection</Text>;
  }
}

const mapStateToProps = (state) => ({
  user: state.user.details,
});

export default connect(mapStateToProps, { getUserDataAction })(StripeCallback);
