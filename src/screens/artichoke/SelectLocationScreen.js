import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, FlatList, StyleSheet, View,
} from 'react-native';
import _cloneDeep from 'lodash.clonedeep';
import AwesomeAlert from 'react-native-awesome-alerts';
import { colors } from '../../styles';
import {
  setNewAppointmentValuesAction,
  setNewClassValuesAction,
} from '../../actions/artichoke/Appointments.actions';
import {
  getAvailableLocationsAction,
  setFilteredLocationsAction,
} from '../../actions/artichoke/Locations.actions';
import LocationListItem from '../../components/artichoke/screensComponents/booked/LocationListItem';
import EmptyLocationList from '../../components/artichoke/screensComponents/locations/EmptyLocationList';
import { HeaderSearchBar } from '../../components';
import { APPLICATION_ROUTES } from '../../constants';
import AddButton from '../../components/artichoke/common/AddButton';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { readFromStorage, saveToStorage } from '../../util/Storage.utils';
import { deleteAlertStyles } from '../../styles/alertStyle';

const noLocationImage = require('../../assets/iconsGlobalAddLocation.png');

class SelectLocationScreen extends Component {
  // eslint-disable-next-line react/sort-comp
  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Select Location',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      showAlert: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerLeft: this.renderHeaderLeft,
    });
    this.checkVideoCallAlert();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.listAvailableLocations();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
  );

  checkVideoCallAlert = async () => {
    const isVideoCallAlertShowed = await readFromStorage('showVideoCallAlert');
    this.setState({ showAlert: !isVideoCallAlertShowed });
  };

  hideAlert = async () => {
    this.setState({ showAlert: false });
    await saveToStorage('showVideoCallAlert', true);
  };

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  listAvailableLocations = () => {
    const { selfBookings, locations } = this.props;

    let items;
    if (this.props.route.params.mode === 'appointment') {
      items = this.props.newAppointment?.service?.selfBookingAddresses
        ? _cloneDeep(this.props.newAppointment.service.selfBookingAddresses)
        : [];
    } else {
      items = this.props.newClassValues?.service?.selfBookingAddresses
        ? _cloneDeep(this.props.newClassValues.service.selfBookingAddresses)
        : [];
    }

    if (items?.length > 0) {
      const enabledCustomLocations = [];
      items.forEach((item) => {
        locations?.forEach((location) => {
          if (item.id === location.id && location.checked === true) {
            enabledCustomLocations.push(item);
          }
        });
      });
      items = enabledCustomLocations;
    }

    if (items.filter((e) => e.id === 0).length === 0) {
      const isEnabled = !!(
        selfBookings
        && selfBookings.offsite_services_available
        && selfBookings.offsite_services_available === 'true'
      );
      if (isEnabled) {
        items.unshift({
          id: 0,
          addressName: "Client's location",
          checked: true,
          workHours: JSON.stringify(
            JSON.parse(selfBookings?.workHours).offsite,
          ),
        });
      }
    }
    if (items.filter((e) => e.id === 1).length === 0) {
      const isEnabled = !!(
        selfBookings
        && selfBookings.remote_services_available
        && selfBookings.remote_services_available === 'true'
      );
      if (isEnabled) {
        items.unshift({
          id: 1,
          addressName: 'Remotely',
          checked: true,
          workHours: JSON.stringify(JSON.parse(selfBookings?.workHours).remote),
        });
      }
    }
    if (items.filter((e) => e.id === 2).length === 0) {
      const isEnabled = !!(
        selfBookings
        && selfBookings.inapp_services_available
        && selfBookings.inapp_services_available === 'true'
      );
      if (isEnabled) {
        items.unshift({
          id: 2,
          addressName: 'Video Call',
          checked: true,
          workHours: JSON.stringify(JSON.parse(selfBookings?.workHours).inapp),
        });
      }
    }
    this.props.setFilteredLocationsAction(items);
  };

  renderAddLocationButton = () => (
    <AddButton
      onPressAction={() => this.props.navigation.navigate({
        name: APPLICATION_ROUTES.CREATE_NEW_LOCATION,
      })}
    />
  );

  setNewAppointmentLocation = (item) => {
    if (this.props.route.params.mode === 'appointment') {
      this.props.setNewAppointmentValuesAction({
        key: 'appointmentLocation',
        value: item,
      });
      if (!(item.id === 2)) {
        this.props.setNewAppointmentValuesAction({
          key: 'inappEnabled',
          value: 'false',
        });
      }
      if (!(item.id === 1)) {
        this.props.setNewAppointmentValuesAction({
          key: 'remotely',
          value: 'false',
        });
      }
      if (!(item.id === 0)) {
        this.props.setNewAppointmentValuesAction({
          key: 'clientAddressEnable',
          value: 'false',
        });
      }
      this.props.setNewAppointmentValuesAction({
        key: 'date',
        value: null,
      });
      this.props.setNewAppointmentValuesAction({
        key: 'time',
        value: null,
      });
      this.props.setNewAppointmentValuesAction({
        key: 'isEdit',
        value: true,
      });
    } else {
      this.props.setNewClassValuesAction({
        key: 'selfBookingAddressId',
        value: item,
      });
    }
    this.props.navigation.goBack(null);
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    let items;
    if (this.props.route.params.mode === 'appointment') {
      items = this.props.newAppointment?.service.selfBookingAddresses
        ? _cloneDeep(this.props.newAppointment.service.selfBookingAddresses)
        : [];
    } else {
      items = this.props.newClassValues?.service.selfBookingAddresses
        ? _cloneDeep(this.props.newClassValues.service.selfBookingAddresses)
        : [];
    }

    if (
      this.props.selfBookings.offsite_services_available === 'true'
      && items.filter((e) => e.id === 0).length === 0
    ) {
      items.push({
        id: 0,
        addressName: "Client's location",
        checked: true,
      });
    }
    if (
      this.props.selfBookings.remote_services_available === 'true'
      && items.filter((e) => e.id === 1).length === 0
    ) {
      items.push({
        id: 1,
        addressName: 'Remotely',
        checked: true,
      });
    }
    const newData = items.filter((item) => {
      const itemData = `${item.addressName.toUpperCase()}`;
      const textData = text.toUpperCase();
      return itemData.indexOf(textData) > -1;
    });
    this.props.setFilteredLocationsAction(newData);
  };

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Location"
    />
  );

  redirectToHoursTab = async () => {
    this.setState({ showAlert: false });
    await saveToStorage('showVideoCallAlert', true);
    this.props.navigation.navigate(APPLICATION_ROUTES.VIDEO_CALL_LOCATION, {
      returnRoute: APPLICATION_ROUTES.SELECT_LOCATION_SCREEN,
    });
  };

  render() {
    const { showAlert } = this.state;
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.filteredLocationsList}
          renderItem={({ item }) => (
            <LocationListItem
              item={item}
              setNewAppointmentLocation={this.setNewAppointmentLocation}
            />
          )}
          keyExtractor={(item) => item.id}
          refreshing={this.props.serviceListLoading}
          ListEmptyComponent={() => (
            <EmptyLocationList
              message="No Location Enabled"
              subTitle="Customize your list of locations by using the Hours tab, or add a new location tapping the “+” button below."
              hasIcon={noLocationImage}
            />
          )}
          ListHeaderComponent={this.renderHeader}
        />
        <AwesomeAlert
          show={showAlert}
          showProgress={false}
          useNativeDriver
          title="Video Call Available"
          message="Sessions can now be hosted via video call. This feature can be enabled/disabled and work hours can be edited at any time in the Hours tab."
          closeOnTouchOutside={false}
          closeOnHardwareBackPress={false}
          showConfirmButton
          showCancelButton
          titleStyle={deleteAlertStyles.alertTitle}
          messageStyle={deleteAlertStyles.alertText}
          confirmText="Cancel"
          cancelText="Enable Video Call"
          confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
          confirmButtonStyle={deleteAlertStyles.alertButton}
          cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
          cancelButtonStyle={deleteAlertStyles.alertButton}
          actionContainerStyle={{
            flexDirection: 'column',
            backgroundColor: colors.white,
          }}
          contentContainerStyle={deleteAlertStyles.alertContainer}
          onConfirmPressed={() => {
            this.hideAlert();
          }}
          onCancelPressed={() => {
            this.hideAlert();
            this.redirectToHoursTab();
          }}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  locations: state.locations.locationsList,
  filteredLocationsList: state.locations.filteredLocations,
  newAppointment: state.appointments.newAppointmentValues,
  newClassValues: state.appointments.newClassValues,
  selfBookings: state.locations.selfBookings,
});

export default connect(mapStateToProps, {
  getAvailableLocationsAction,
  setFilteredLocationsAction,
  setNewAppointmentValuesAction,
  setNewClassValuesAction,
})(SelectLocationScreen);
