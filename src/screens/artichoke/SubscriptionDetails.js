import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
} from 'react-native';
import moment from 'moment';
import { colors } from '../../styles';
import { APPLICATION_ROUTES } from '../../constants';
import { refundClientTransactionAction } from '../../actions/artichoke/Clients.actions';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const bgImage = require('../../assets/imgServiceBackground.png');

const dateFormat = 'MM/DD/YYYY hh:mm a';

class SubscriptionDetails extends Component {
  static navigationOptions = ({ route }) => ({
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  goBack = () => {
    this.props.navigation.goBack();
  };

  goToCancelSubscription = () => {
    this.props.navigation.navigate(APPLICATION_ROUTES.CANCEL_SUBSCRIPTION);
  };

  showPaymentExpireValue = (expireType, expireDays) => {
    if (expireType === 'NEVER') {
      return 'Never';
    }
    if (expireType === 'PERIODEND') {
      return 'On Next Payment Date';
    }
    if (expireType === 'CUSTOM') {
      return `After ${expireDays} Days`;
    }
    return '';
  };

  showPaymentIntervalValue = (value) => {
    switch (value) {
      case 'ONETIME':
        return 'Once';
      case 'WEEKLY':
        return 'Every Week';
      case 'BIWEEKLY':
        return 'Every Other Week';
      case 'MONTHLY':
        return 'Every Month';
      case 'ANNUALLY':
        return 'Every Year';
      default:
        return '';
    }
  };

  calculateExpireDate = (pack) => {
    switch (pack.paymentInterval) {
      case 'ONETIME':
        return moment(
          this.props.selectedTransaction.clientPurchase.created,
          dateFormat,
        );
      case 'WEEKLY':
        return moment(
          this.props.selectedTransaction.clientPurchase.created,
          dateFormat,
        ).add(pack.repeatCount, 'w');
      case 'BIWEEKLY':
        return moment(
          this.props.selectedTransaction.clientPurchase.created,
          dateFormat,
        ).add(pack.repeatCount * 2, 'w');
      case 'MONTHLY':
        return moment(
          this.props.selectedTransaction.clientPurchase.created,
          dateFormat,
        ).add(pack.repeatCount, 'M');
      case 'ANNUALLY':
        return moment(
          this.props.selectedTransaction.clientPurchase.created,
          dateFormat,
        ).add(pack.repeatCount, 'y');
      default:
        return moment(
          this.props.selectedTransaction.clientPurchase.created,
          dateFormat,
        ).add(pack.repeatCount, 'y');
    }
  };

  renderPackage = (purchaseType) => (
    <View>
      <View>
        <Text style={styles.label}>Services</Text>
        {this.props.selectedTransaction.clientPurchase.pack.packageProducts.map(
          (product) => (
            <View style={styles.frame}>
              <Text style={styles.labelSubtotals}>
                {`${product.productName} (${product.duration} min)`}
              </Text>
              <Text style={styles.value}>
                {product.unlimited ? 'Unlimited' : product.quantity}
              </Text>
            </View>
          ),
        )}
      </View>
      <View>
        <Text style={styles.label}>Payment</Text>
        <Text style={styles.description}>{purchaseType}</Text>
        <View style={{ ...styles.frame, paddingTop: 10 }}>
          <Text style={styles.labelSubtotals}>Payment Interval</Text>
          <Text style={styles.value}>
            {this.showPaymentIntervalValue(
              this.props.selectedTransaction.clientPurchase.pack
                .paymentInterval,
            )}
          </Text>
        </View>
        <View style={styles.frame}>
          <Text style={styles.labelSubtotals}>Payment Count</Text>
          <Text style={styles.value}>
            {this.props.selectedTransaction.clientPurchase.pack.repeatCount
            !== 99999
              ? this.props.selectedTransaction.clientPurchase.pack.repeatCount
              : 'Unlimited'}
          </Text>
        </View>
        <View style={styles.frame}>
          <Text style={styles.labelSubtotals}>Balances Expire</Text>
          <Text style={styles.value}>
            {this.showPaymentExpireValue(
              this.props.selectedTransaction.clientPurchase.pack.expireType,
              this.props.selectedTransaction.clientPurchase.pack
                .expireDaysCustom,
            )}
          </Text>
        </View>
      </View>
    </View>
  );

  render() {
    const productName = this.props.selectedTransaction.clientPurchase.pack.name;

    let purchaseType = '';

    if (
      this.props.selectedTransaction.clientPurchase.purchaseType
        === 'CREDIT_CARD'
      && this.props.selectedClientCreditCart
    ) {
      purchaseType = `${this.props.selectedClientCreditCart.creditCardType} ...${this.props.selectedClientCreditCart.lastFour}`;
    } else if (
      this.props.selectedTransaction.clientPurchase.purchaseType
        === 'CASH_OR_CHECK'
      || !this.props.selectedClientCreditCart
    ) {
      purchaseType = 'Cash Or Check';
    }

    return (
      <SafeAreaView style={styles.container}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <View style={styles.contentUser}>
              <Image
                source={
                  this.props.selectedClient?.user.avatarUrl
                    ? { uri: this.props.selectedClient?.user.avatarUrl }
                    : bgImage
                }
                style={styles.userImage}
              />
              <View style={styles.userDetails}>
                <Text style={styles.userName}>
                  {this.props.selectedClient?.user.firstName}
                  {' '}
                  {this.props.selectedClient?.user.lastName}
                </Text>
                <Text style={styles.userJob}>{productName}</Text>
              </View>
              <View style={styles.userSubscriptionDetails}>
                <Text style={styles.userName}>
                  {this.props.selectedTransaction.clientPurchase.amountPaid}
                </Text>
                <Text style={styles.userJob}>
                  {
                    this.props.selectedTransaction.clientPurchase.pack
                      .paymentInterval
                  }
                </Text>
              </View>
            </View>
            {this.renderPackage(purchaseType)}
          </View>
        </ScrollView>
        <View style={styles.bottom}>
          <ScrollView style={styles.paymentPrice}>
            <View style={styles.frameDates}>
              <Text style={styles.description}>
                Expires:
                {' '}
                {this.props.selectedTransaction.clientPurchase.pack
                  .repeatCount > 90000
                  ? 'On cancel'
                  : this.calculateExpireDate(
                    this.props.selectedTransaction.clientPurchase.pack,
                  ).format('MM/DD/YYYY')}
              </Text>
              <Text style={styles.description}>
                Last payment date:
                {' '}
                {moment(
                  this.props.selectedTransaction.clientPurchase.checkDate,
                  dateFormat,
                ).format('MM/DD/YYYY')}
              </Text>
            </View>
            <View style={styles.frame}>
              <Text
                style={styles.refund}
                onPress={() => this.goToCancelSubscription()}
              >
                Cancel Subscription
              </Text>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bottom: {
    flexDirection: 'row',
    display: 'flex',
    height: 120,
    justifyContent: 'flex-end',
    marginBottom: 38,
    marginHorizontal: 20,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    marginTop: 20,
    marginBottom: 20,
    paddingHorizontal: 20,
    width: '100%',
  },
  userImage: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userDetails: {
    flex: 6,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  userSubscriptionDetails: {
    flex: 6,
    alignItems: 'flex-end',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    marginTop: 5,
    marginLeft: 10,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    lineHeight: 22,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  label: {
    ...generalStyles.avenirHeavy17,
    paddingTop: 24,
  },
  description: {
    marginTop: 8,
    ...generalStyles.avenirRoman17,
    color: colors.fillDarkGrey,
  },
  frame: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 10,
  },
  frameDates: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 10,
  },
  labelSubtotals: {
    textAlign: 'left',
    ...generalStyles.avenirMedium17,
    color: colors.fillDarkGrey,
    borderWidth: 0,
  },
  value: {
    textAlign: 'right',
    ...generalStyles.avenirMedium17,
    flex: 2.5,
    color: colors.fillDarkGrey,
    borderWidth: 0,
  },
  paymentPrice: {
    display: 'flex',
    width: '100%',
    height: 240,
    flexDirection: 'column',
  },
  refund: {
    ...generalStyles.avenirRoman17,
    color: colors.lightblue,
    marginTop: 26,
    textDecorationLine: 'underline',
  },
});

const mapStateToProps = (state) => ({
  selectedTransaction: state.clients.selectedClientTransaction,
  selectedClient: state.clients.selectedClient,
  selectedClientCreditCart: state.clients.selectedClient?.creditCard,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  refundClientTransactionAction,
})(SubscriptionDetails);
