import React, { Component, createRef, Fragment } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, View, StyleSheet, Text, FlatList,
} from 'react-native';
import moment from 'moment';
import ServiceListItem from '../../components/artichoke/screensComponents/services/ServiceListItem';
import {
  archiveServiceAction,
  setSelectedServiceAction,
  getServiceByIdAction,
  getServicesAction,
  getActivePackagesAction,
  getPackageByIdAction,
  setSelectedPackageAction,
  clearSelectedServiceAction,
  clearSelectedPackage,
  clearNewPackageAction,
} from '../../actions/artichoke/Services.actions';
import { clearServiceAppointmentsAction } from '../../actions/artichoke/Appointments.actions';
import EmptyServiceList from '../../components/artichoke/screensComponents/services/EmptyServiceList';
import { APPLICATION_ROUTES } from '../../constants';
import { colors } from '../../styles';
import AddButton from '../../components/artichoke/common/AddButton';
import { generalStyles } from '../../styles/generalStyle';
import PackageListItem from '../../components/artichoke/screensComponents/services/PackageListItem';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import FormGroup from '../../components/artichoke/common/FormGroup';
import FormSubmitButton from '../../components/artichoke/common/FormSubmitButton';
import LoadingSpinner from '../../components/LoadingSpinner';
import { setLoaderStateAction } from '../../actions/artichoke/Loaders.actions';
import {
  getSelfBookingAction,
  getAvailableLocationsAction,
  clearSelectedLocationAction,
  clearSelectedExtraLocationAction,
} from '../../actions/artichoke/Locations.actions';

const emptyServiceListImage = require('../../assets/imgAddChatFtue.png');

const servicesActionSheetRef = createRef();

class ServicesScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedService: null,
    };
  }

  componentDidMount = () => {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      const currentDate = moment().unix() * 1000;
      this.props.getSelfBookingAction();
      this.props.getAvailableLocationsAction();
      this.props.getServicesAction(currentDate);
      this.props.getActivePackagesAction();
      this.props.clearServiceAppointmentsAction();
    });
  };

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onAddPressed() {
    if (!this.props.navigation) return;
    const actions = [];
    actions.push({
      text: 'Service',
      onPress: () => this.addNewService(),
    });
    actions.push({
      text: 'Package',
      onPress: () => {
        this.addNewPackage();
      },
    });
    this.props.navigation.navigate('ActionSheet', {
      title: 'Create a ...',
      actions,
    });
  }

  onOpenBookedScreen = (service) => {
    this.props.setSelectedServiceAction(service);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.DASHBOARD,
    });
  };

  addNewPackage = () => {
    this.props.clearSelectedPackage();
    this.props.clearNewPackageAction();
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.CREATE_PACKAGE,
    });
    servicesActionSheetRef.current?.setModalVisible(false);
  };

  addNewService = () => {
    this.props.clearSelectedServiceAction();
    this.props.clearSelectedLocationAction();
    this.props.clearSelectedExtraLocationAction();
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.CREATE_SERVICE,
    });
    servicesActionSheetRef.current?.setModalVisible(false);
  };

  goToEditPackageScreen = (pack) => {
    this.props.clearSelectedPackage();
    this.props.getPackageByIdAction(pack.id);
    this.props.setSelectedPackageAction(pack);
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.EDIT_PACKAGE });
  };

  goToEditServiceScreen = (service) => {
    this.props.clearSelectedServiceAction();
    this.props.getServiceByIdAction(service.id);
    this.props.setSelectedServiceAction(service);
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.EDIT_SERVICES });
  };

  renderAddServiceButton = () => (
    <AddButton
      onPressAction={() => {
        servicesActionSheetRef.current?.setModalVisible(true);
      }}
    />
  );

  renderContent = () => (
    <SafeAreaView style={styles.container}>
      {this.props.servicesList.length ? (
        <>
          <View style={styles.container}>
            <FlatList
              contentContainerStyle={{ flexGrow: 1 }}
              style={styles.list}
              data={this.props.packagesList}
              onRefresh={() => {
                this.props.getActivePackagesAction();
              }}
              refreshing={this.props.packageListLoading}
              renderItem={({ item }) => (
                <PackageListItem
                  item={item}
                  onGoToEditScreen={this.goToEditPackageScreen}
                  onOpenBooked={this.onOpenBookedScreen}
                  navigation={this.props.navigation}
                  getPackageByIdAction={this.props.getPackageByIdAction}
                  symbol={this.props.symbol}
                />
              )}
              keyExtractor={(item) => item.id.toString()}
              ListEmptyComponent={() => (
                <EmptyServiceList
                  message="Tap the + below to create a Package"
                  hasIcon={emptyServiceListImage}
                />
              )}
              ListHeaderComponent={() => (
                <View style={generalStyles.container}>
                  <Text style={styles.titleTop}>Packages</Text>
                </View>
              )}
              ListFooterComponent={() => (
                <View style={styles.titleTop}>
                  <View style={generalStyles.container}>
                    <Text style={generalStyles.title}>Services</Text>
                  </View>
                  <FlatList
                    contentContainerStyle={{ flexGrow: 1 }}
                    style={styles.list}
                    data={this.props.servicesList}
                    onRefresh={() => {
                      const currentDate = moment().unix() * 1000;
                      this.props.getServicesAction(currentDate);
                      this.props.clearServiceAppointmentsAction();
                    }}
                    refreshing={this.props.serviceListLoading}
                    renderItem={({ item }) => (
                      <ServiceListItem
                        item={item}
                        onGoToEditScreen={this.goToEditServiceScreen}
                        onOpenBooked={this.onOpenBookedScreen}
                        navigation={this.props.navigation}
                        getServiceByIdAction={this.props.getServiceByIdAction}
                        symbol={this.props.symbol}
                      />
                    )}
                    keyExtractor={(item) => item.id}
                  />
                </View>
              )}
            />
          </View>
          <View style={styles.footerList}>{this.renderAddServiceButton()}</View>
        </>
      ) : (
        <>
          <EmptyServiceList
            message="Tap the + below to create a Service"
            hasIcon={emptyServiceListImage}
          />
          <View style={styles.footerList}>{this.renderAddServiceButton()}</View>
        </>
      )}
      <CustomActionSheet
        actionSheetRef={servicesActionSheetRef}
        title="Create a ..."
      >
        <FormGroup formStyle={styles.bottomSubmitButton}>
          <FormSubmitButton
            title="Service"
            onPressButton={() => this.addNewService()}
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.white,
              marginVertical: 15,
            }}
            buttonLabelStyle={{ color: colors.azure }}
          />
          <FormSubmitButton
            title="Package"
            onPressButton={() => this.addNewPackage()}
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.white,
            }}
            buttonLabelStyle={{ color: colors.azure }}
          />
        </FormGroup>
      </CustomActionSheet>
    </SafeAreaView>
  );

  render() {
    return (this.props.servicesList.length === 0
      && this.props.serviceListLoading)
      || (this.props.packagesList.length === 0
        && this.props.packageListLoading) ? (
          <LoadingSpinner
            visible
            size="large"
            title="LOADING SERVICES..."
            backgroundColor={colors.white}
          />
      ) : (
        this.renderContent()
      );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    zIndex: 0,
  },
  list: {
    flex: 1,
    marginBottom: 20,
  },
  titleTop: {
    fontFamily: 'Avenir-Medium',
    fontSize: 24,
    color: colors.black,
    marginTop: 30,
  },
  footerList: {
    flex: 1,
    position: 'absolute', // Here is the trick
    bottom: 0, // Here is the trick
    right: 0,
    height: 70,
    flexGrow: 1,
  },
});

const mapStateToProps = (state) => ({
  servicesList: state.services.servicesList,
  packagesList: state.services.packagesList,
  serviceListLoading: state.loadingComponents.serviceListLoading,
  packageListLoading: state.loadingComponents.packageListLoading,
  symbol: state.user.accountSettings?.symbol,
});
export default connect(mapStateToProps, {
  archiveServiceAction,
  setSelectedServiceAction,
  getServiceByIdAction,
  getServicesAction,
  clearServiceAppointmentsAction,
  getActivePackagesAction,
  getPackageByIdAction,
  setSelectedPackageAction,
  setLoaderStateAction,
  getSelfBookingAction,
  getAvailableLocationsAction,
  clearSelectedServiceAction,
  clearSelectedLocationAction,
  clearSelectedExtraLocationAction,
  clearSelectedPackage,
  clearNewPackageAction,
})(ServicesScreen);
