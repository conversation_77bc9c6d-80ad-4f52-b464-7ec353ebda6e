import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, StyleSheet } from 'react-native';
import AwesomeAlert from 'react-native-awesome-alerts';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  archivePackageAction,
  updatePackageAction,
  getInactivePackagesAction,
  setSelectedPackageForEditValuesAction,
  setSelectedOfferingServiceAction,
  getPackageByIdAction,
  setSelectedPackageAction,
  setServicesTotalValueAction,
} from '../../actions/artichoke/Services.actions';
import EditPackageForm from '../../components/artichoke/screensComponents/services/EditPackageForm';
import { APPLICATION_ROUTES } from '../../constants';
import { colors } from '../../styles';
import { deleteAlertStyles } from '../../styles/alertStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { removeEmojis } from '../../util/utils';
import LoadingSpinner from '../../components/LoadingSpinner';
import HeaderRightButton from '../../components/HeaderRightButton';
import { Platform } from 'react-native';

class EditPackageScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: APPLICATION_ROUTES.EDIT_PACKAGE,
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      selectedService: null,
      flatRateTax: this.flatRateTaxCalculatePercentage(),
      showAlert: false,
    };
    this.props.navigation.setParams({
      onArchivePackage: this.onArchivePackage,
      goBack: this.goBack,
    });
  }

  componentDidUpdate(prevProps) {
    if (prevProps.selectedPackage.price !== this.props.selectedPackage.price) {
      this.props.setSelectedPackageForEditValuesAction({
        key: 'tax',
        value: this.calculateTax(),
      });
      if (this.props.selectedPackage.taxType === 'FLATRATE') {
        this.onChangeFlatRateTax(this.props.selectedPackage.taxRate);
      }
    }
    if (prevProps.totalValue !== this.props.totalValue) {
      this.props.setSelectedPackageForEditValuesAction({
        key: 'tax',
        value: this.calculateTax(),
      });
    }
    this.calculateTotalValue(this.props.selectedPackage.packageProducts);
    if (this.props.selectedPackage?.active === 'true') {
      this.props.navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            onPress={() => this.onArchivePackage()}
            title="Archive"
            titleStyle={{
              color: colors.white,
              margin: 15,
            }}
          />
        ),
      });
    }
  }

  onArchivePackage = () => {
    this.showAlert();
  };

  onChangeFlatRateTax = (value) => {
    this.setState({ flatRateTax: value });
    if (this.props.selectedPackage.price) {
      const tax = (parseFloat(this.props.selectedPackage.price) * parseFloat(value))
        / 100;
      this.props.setSelectedPackageForEditValuesAction({
        key: 'tax',
        value: tax.toFixed(2).toString(),
      });
      this.props.setSelectedPackageForEditValuesAction({
        key: 'taxRate',
        value,
      });
    } else {
      this.props.setSelectedPackageForEditValuesAction({
        key: 'tax',
        value: '0.00',
      });
      this.props.setSelectedPackageForEditValuesAction({
        key: 'taxRate',
        value: '0.00',
      });
    }
  };

  onUpdatePackage = () => {
    const editPackage = {
      id: this.props.selectedPackage.id,
      expireType: this.props.selectedPackage.expireType,
      expireDaysCustom: this.props.selectedPackage.expireDaysCustom,
      name: removeEmojis(this.props.selectedPackage.name),
      packageProducts: this.props.selectedPackage.packageProducts,
      paymentInterval: this.props.selectedPackage.paymentInterval,
      price: this.props.selectedPackage.price,
      repeatCount: this.props.selectedPackage.repeatCount,
      tax: this.props.selectedPackage.tax,
      taxRate: this.props.selectedPackage.taxRate,
      taxType: this.props.selectedPackage.taxType,
      sellOnlineEnabled: this.props.selectedPackage.sellOnlineEnabled,
      packageDescription: removeEmojis(
        this.props.selectedPackage.packageDescription,
      ),
    };
    this.props.updatePackageAction(editPackage);
  };

  calculateTax = () => {
    if (
      this.props.selectedPackage.price === null
      || this.props.selectedPackage.price === 0
    ) {
      return 0;
    }
    let tax = 0.0;
    if (this.props.selectedPackage.packageProducts.length) {
      const totalServicesValue = this.props.totalValue;
      this.props.selectedPackage.packageProducts.map((serviceItem) => {
        if (serviceItem.tax !== 0) {
          const productValue = serviceItem.price * serviceItem.quantity;
          const split = productValue / totalServicesValue;
          tax
            += (split * this.props.selectedPackage.price * serviceItem.tax) / 100;
        }
        return null;
      });
    }
    if (!tax || Number.isNaN(tax)) {
      return 0;
    }
    return tax.toFixed(2);
  };

  calculateTotalValue = (services) => {
    let totalPrice = 0.0;
    services.map((serviceItem) => {
      totalPrice += serviceItem.price * serviceItem.quantity;
      return null;
    });
    this.props.setServicesTotalValueAction(totalPrice.toFixed(2));
    return totalPrice.toFixed(2);
  };

  confirmArchivePackage = () => {
    this.props.archivePackageAction(this.props.selectedPackage.id);
    this.props.getInactivePackagesAction();
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.ARCHIVED_SERVICES,
      params: { isFromPackageOrService: true },
    });
  };

  flatRateTaxCalculatePercentage = () => {
    const value = this.props.selectedPackage.tax;
    if (this.props.selectedPackage.taxType === 'FLATRATE') {
      return (value / this.props.selectedPackage.price) * 100;
    }
    return value.toFixed(2);
  };

  goBack = () => {
    this.props.getPackageByIdAction(this.props.selectedPackage.id);
    this.props.navigation.goBack();
  };

  hideAlert = () => {
    this.setState({
      showAlert: false,
    });
  };

  showAlert = () => {
    this.setState({
      showAlert: true,
    });
  };

  render() {
    return this.props.getPackageLoading ? (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    ) : (
      <KeyboardAwareScrollView
        resetScrollToCoords={{ x: 0, y: 0 }}
        contentContainerStyle={styles.container}
        scrollEnabled={false}
        extraHeight={100}
        extraScrollHeight={Platform.OS === 'ios' ? 0 : 40}
        enableOnAndroid
        enableAutomaticScroll
        keyboardShouldPersistTaps="handled"
      >
        <SafeAreaView style={styles.container}>
          <EditPackageForm
            selectedPackage={this.props.selectedPackage}
            onSubmitForm={this.onUpdatePackage}
            navigation={this.props.navigation}
            setSelectedPackageForEditValuesAction={
              this.props.setSelectedPackageForEditValuesAction
            }
            setSelectedOfferingServiceAction={
              this.props.setSelectedOfferingServiceAction
            }
            calculateTax={this.calculateTax}
            setServicesTotalValueAction={this.props.setServicesTotalValueAction}
            totalValue={this.props.totalValue}
            calculateTotalValue={this.calculateTotalValue}
            flatRateTax={this.state.flatRateTax}
            onChangeFlatRateTax={this.onChangeFlatRateTax}
            savePackageLoading={this.props.savePackageLoading}
            symbol={this.props.symbol}
          />
          <AwesomeAlert
            show={this.state.showAlert}
            showProgress={false}
            useNativeDriver
            title="Are you sure?"
            message="You can restore it at any time by navigating to your package archive."
            closeOnTouchOutside={false}
            closeOnHardwareBackPress={false}
            showConfirmButton
            showCancelButton
            titleStyle={deleteAlertStyles.alertTitle}
            messageStyle={deleteAlertStyles.alertText}
            confirmText="Cancel"
            cancelText="Archive"
            confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
            confirmButtonStyle={deleteAlertStyles.alertButton}
            cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
            cancelButtonStyle={deleteAlertStyles.alertButton}
            actionContainerStyle={{
              flexDirection: 'column',
              backgroundColor: colors.white,
              borderRadius: 5,
            }}
            contentContainerStyle={deleteAlertStyles.alertContainer}
            onCancelPressed={() => {
              this.confirmArchivePackage();
              this.hideAlert();
            }}
            onConfirmPressed={() => {
              this.hideAlert();
            }}
          />
        </SafeAreaView>
      </KeyboardAwareScrollView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  selectedPackage: state.services.selectedPackage,
  totalValue: state.services.newPackage.servicesTotalValue,
  savePackageLoading: state.loadingComponents.savePackageLoading,
  getPackageLoading: state.loadingComponents.getPackageLoading,
  symbol: state.user.accountSettings?.symbol,
});
export default connect(mapStateToProps, {
  archivePackageAction,
  updatePackageAction,
  getInactivePackagesAction,
  setSelectedPackageForEditValuesAction,
  setSelectedOfferingServiceAction,
  getPackageByIdAction,
  setSelectedPackageAction,
  setServicesTotalValueAction,
})(EditPackageScreen);
