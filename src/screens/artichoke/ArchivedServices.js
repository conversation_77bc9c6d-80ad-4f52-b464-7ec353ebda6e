import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  FlatList,
  StyleSheet,
  View,
  Text,
  Dimensions,
} from 'react-native';
import ArchivedServiceListItem from '../../components/artichoke/screensComponents/services/ArchivedServiceListItem';
import {
  getArchivedServicesAction,
  restoreServiceAction,
  getServiceByIdAction,
  setSelectedServiceAction,
  getInactivePackagesAction,
  getPackageByIdAction,
  setSelectedPackageAction,
  restorePackageAction,
} from '../../actions/artichoke/Services.actions';
import { generalStyles } from '../../styles/generalStyle';
import EmptyServiceList from '../../components/artichoke/screensComponents/services/EmptyServiceList';
import { APPLICATION_ROUTES } from '../../constants';
import ArchivedPackageListItem from '../../components/artichoke/screensComponents/client/products/ArchivedPackageListItem';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import LoadingSpinner from '../../components/LoadingSpinner';

const archiveIcon = require('../../assets/imgAddChat.png');

class ArchivedServicesScreen extends Component {
  constructor(props) {
    super(props);
    this.props.getArchivedServicesAction();
    this.props.getInactivePackagesAction();
  }

  static navigationOptions = ({ navigation, route }) => ({
    title: 'Archived Services',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => (route?.params?.isFromPackageOrService ? navigation?.pop(2) : navigation?.goBack())} />
    ),
    headerBackTitle: 'Back',
  });

  goToEditPackageScreen = (pack) => {
    this.props.getPackageByIdAction(pack.id);
    this.props.setSelectedPackageAction(pack);
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.EDIT_PACKAGE });
  };

  goToEditServiceScreen = (service) => {
    this.props.getServiceByIdAction(service.id);
    this.props.setSelectedServiceAction(service);
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.EDIT_SERVICES });
  };

  goToViewPackageScreen = (pack) => {
    this.props.getPackageByIdAction(pack.id);
    this.props.setSelectedPackageAction(pack);
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.VIEW_PACKAGE });
  };

  goToViewServiceScreen = (service) => {
    this.props.getServiceByIdAction(service.id);
    this.props.setSelectedServiceAction(service);
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.VIEW_SERVICE });
  };

  restorePackage = (pack) => {
    this.props.restorePackageAction(pack.id);
  };

  restoreService = (service) => {
    const newService = {
      ...service,
      active: true,
    };
    delete this.props.restoreServiceAction(newService);
  };

  renderContent = () => (
    <SafeAreaView style={styles.container}>
      <FlatList
        style={styles.list}
        data={this.props.archivedPackageList}
        renderItem={({ item }) => (
          <ArchivedPackageListItem
            item={item}
            onGoToViewPackageScreen={this.goToViewPackageScreen}
            onGoToEditScreen={this.goToEditPackageScreen}
            onRestorePackage={this.restorePackage}
            symbol={this.props.symbol}
          />
        )}
        keyExtractor={(item) => item.id.toString()}
        ListEmptyComponent={() => (
          <EmptyServiceList
            message="No Archived Packages"
            hasIcon={archiveIcon}
          />
        )}
        ListHeaderComponent={(
          <View style={styles.subContainer}>
            <Text style={generalStyles.title}>Packages</Text>
          </View>
        )}
        ListFooterComponent={(
          <View>
            <View style={styles.subContainer}>
              <Text style={generalStyles.title}>Services</Text>
            </View>
            <FlatList
              data={this.props.servicesList}
              renderItem={({ item }) => (
                <ArchivedServiceListItem
                  item={item}
                  onGoToEditScreen={this.goToEditServiceScreen}
                  onRestoreService={this.restoreService}
                  onGoToViewServiceScreen={this.goToViewServiceScreen}
                  symbol={this.props.symbol}
                />
              )}
              keyExtractor={(item) => item.id}
              ListEmptyComponent={() => (
                <EmptyServiceList
                  message="No Archived Services"
                  hasIcon={archiveIcon}
                />
              )}
            />
          </View>
        )}
      />
    </SafeAreaView>
  );

  render() {
    return this.props.archivedServicesListLoading
      || this.props.archivedPackagesListLoading ? (
        <LoadingSpinner
          visible
          size="large"
          backgroundColor="rgba(0, 0, 0, 0.25)"
        />
      ) : (
        this.renderContent()
      );
  }
}
const HEIGHT = Dimensions.get('window').height;
const WIDTH = Dimensions.get('window').width;

const styles = StyleSheet.create({
  container: {
    width: WIDTH,
    height: HEIGHT,
    flex: 1,
  },
  subContainer: {
    display: 'flex',
    marginHorizontal: 17,
    marginTop: 27,
  },
});

const mapStateToProps = (state) => ({
  servicesList: state.services.archivedServicesList,
  archivedPackageList: state.services.archivedPackageList,
  archivedPackagesListLoading:
    state.loadingComponents.archivedPackagesListLoading,
  archivedServicesListLoading:
    state.loadingComponents.archivedServicesListLoading,
  symbol: state.user.accountSettings?.symbol,
});

const mapDispatchToProps = {
  getArchivedServicesAction,
  restoreServiceAction,
  getServiceByIdAction,
  setSelectedServiceAction,
  getInactivePackagesAction,
  getPackageByIdAction,
  setSelectedPackageAction,
  restorePackageAction,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ArchivedServicesScreen);
