import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, FlatList, StyleSheet, View,
} from 'react-native';
import {
  getClassServiceDetails,
  setNewClassValuesAction,
} from '../../actions/artichoke/Appointments.actions';
import {
  getServicesAction,
  setFilteredServicesAction,
} from '../../actions/artichoke/Services.actions';
import ServiceListItem from '../../components/artichoke/screensComponents/booked/ServiceListItem';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import { APPLICATION_ROUTES } from '../../constants';
import AddButton from '../../components/artichoke/common/AddButton';
import { HeaderSearchBar } from '../../components';
import HeaderLeftButton from '../../components/HeaderLeftButton';

class AddClassService extends Component {
  static navigationOptions = ({ navigation }) => ({
    title: 'Add a service',
    headerLeft: () => <HeaderLeftButton onPress={() => navigation.goBack()} />,
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
  }

  setNewAppointmentService = (item) => {
    this.props.setNewClassValuesAction({
      key: 'selfBookingAddressId',
      value: null,
    });
    this.props.getClassServiceDetails({
      service: item,
      returnRoute:
        this.props.route?.params?.returnRoute
        || APPLICATION_ROUTES.CREATE_NEW_CLASS,
    });
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = this.props.servicesList.filter((item) => {
      const itemData = `${item.name.toUpperCase()}`;
      const textData = text.toUpperCase();
      return itemData.indexOf(textData) > -1;
    });
    this.props.setFilteredServicesAction(newData);
  };

  renderAddServiceButton = () => (
    <AddButton
      onPressAction={() => {
        this.props.navigation.navigate({
          name: APPLICATION_ROUTES.CREATE_SERVICE,
          params: {
            previousScreen: APPLICATION_ROUTES.CLASS_CREATE_SERVICE,
          },
          merge: true,
        });
      }}
    />
  );

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Service"
    />
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.filteredServiceList}
          renderItem={({ item }) => (
            <ServiceListItem
              item={item}
              setNewAppointmentService={this.setNewAppointmentService}
              symbol={this.props.symbol}
            />
          )}
          keyExtractor={(item) => item.id}
          refreshing={this.props.serviceListLoading}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Service Found" />
          )}
          ListHeaderComponent={this.renderHeader}
        />
        <View style={styles.footerList}>{this.renderAddServiceButton()}</View>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  footerList: {
    flex: 1,
    position: 'absolute', // Here is the trick
    bottom: 20, // Here is the trick
    right: 10,
    height: 70,
    flexGrow: 1,
  },
});

const mapStateToProps = (state) => ({
  servicesList: state.services.servicesList,
  filteredServiceList: state.services.filteredServices,
  newAppointment: state.appointments.newAppointmentValues,
  symbol: state.user.accountSettings?.symbol,
});

const mapDispatchToProps = {
  getServicesAction,
  setFilteredServicesAction,
  setNewClassValuesAction,
  getClassServiceDetails,
};

export default connect(mapStateToProps, mapDispatchToProps)(AddClassService);
