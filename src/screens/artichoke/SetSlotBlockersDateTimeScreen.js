import React, { Component } from 'react';
import { <PERSON><PERSON>, SafeAreaView, StyleSheet } from 'react-native';
import moment from 'moment';
import { connect } from 'react-redux';
import { colors } from '../../styles';
import CreateDateStartEndTimeForm from '../../components/artichoke/screensComponents/slotblockers/CreateDateStartEndTimeForm';
import {
  setNewSlotBlockerValuesAction,
  getBlockingAppointmentsByDateAction,
} from '../../actions/artichoke/Appointments.actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import HeaderRightButton from '../../components/HeaderRightButton';

class SetSlotBlockersDateTimeScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Select time',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
    headerRight: () => (
      <HeaderRightButton
        onPress={() => route.params.onSaveDateTime()}
        title="Done"
        titleStyle={{
          color: colors.white,
          margin: 15,
        }}
      />
    ),
  });

  constructor(props) {
    super(props);
    this.state = {
      date: this.props.newSlotBlocker.date || moment().format('MM/DD/YYYY'),
      start: this.props.newSlotBlocker.start,
      end: this.props.newSlotBlocker.end,
      entireDay: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
      onSaveDateTime: this.onSaveDateTime,
    });
    const date = this.props.newSlotBlocker.date || new Date();
    this.props.getBlockingAppointmentsByDateAction(date.toString());
  }

  onChangeDate = (date) => {
    this.setState(
      {
        date,
      },
      () => {
        this.props.getBlockingAppointmentsByDateAction(date.toString());
      },
    );
  };

  onChangeTime = (slot, timeSlots) => {
    if (!this.state.start && !this.state.end) {
      this.setState({
        start: slot,
        end:
          slot === timeSlots[timeSlots.length - 1]
            ? timeSlots[timeSlots.indexOf(slot)]
            : timeSlots[timeSlots.indexOf(slot) + 1],
      });
    } else if (
      this.state.start
      && this.state.end
      && moment(slot, 'h:mma').isAfter(moment(this.state.end, 'h:mma'))
    ) {
      this.setState({
        end:
          slot === timeSlots[timeSlots.length - 1]
            ? timeSlots[timeSlots.indexOf(slot)]
            : timeSlots[timeSlots.indexOf(slot) + 1],
      });
    } else if (
      this.state.start
      && this.state.end
      && moment(slot, 'h:mma').isBefore(moment(this.state.start, 'h:mma'))
    ) {
      this.setState({
        start: slot,
      });
    } else if (
      this.state.start
      && this.state.end
      && (moment(slot, 'h:mma').isSame(moment(this.state.start, 'h:mma'))
        || moment(slot, 'h:mma').isBefore(moment(this.state.end, 'h:mma')))
    ) {
      this.setState({
        start: null,
        end: null,
      });
    }
  };

  onSaveDateTime = () => {
    if (!this.state.date || !this.state.start || !this.state.end) {
      Alert.alert(
        'Pick a Date & Time',
        'A date and time are required to block a time slot',
        [
          {
            text: 'Cancel',
          },
        ],
        { cancelable: false },
      );
    } else {
      this.props.setNewSlotBlockerValuesAction({
        key: 'date',
        value: this.state.date.toString(),
      });
      this.props.setNewSlotBlockerValuesAction({
        key: 'start',
        value: this.state.start,
      });
      this.props.setNewSlotBlockerValuesAction({
        key: 'end',
        value: this.state.end,
      });
      this.props.navigation.navigate({
        name: this.props.route.params.returnRoute,
        merge: true,
        params: { completed: 'true' },
      });
    }
  };

  onSwitch = (bool) => {
    this.setState({
      entireDay: bool,
    });
  };

  calcEndSlot = (time, timeSlots) => timeSlots[timeSlots.indexOf(time) + 1];

  goBack = () => {
    this.props.navigation.goBack();
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <CreateDateStartEndTimeForm
          selectedDate={this.state.date}
          selectedStart={this.state.start}
          selectedEnd={this.state.end}
          blockingAppointments={this.props.blockingAppointments}
          onChangeDate={this.onChangeDate}
          onChangeTime={this.onChangeTime}
          entireDay={this.state.entireDay}
          onSwitch={this.onSwitch}
          workHours={this.props.workHours}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newSlotBlocker: state.appointments.newSlotBlocker,
  blockingAppointments: state.appointments.blockingAppointments,
  workHours:
    state.locations.locationsList && state.locations.locationsList.length
      ? state.locations.locationsList[0].workHours
      : '{}',
});

export default connect(mapStateToProps, {
  setNewSlotBlockerValuesAction,
  getBlockingAppointmentsByDateAction,
})(SetSlotBlockersDateTimeScreen);
