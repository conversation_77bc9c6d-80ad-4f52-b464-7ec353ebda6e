import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, FlatList, StyleSheet, View,
} from 'react-native';
import moment from 'moment';
import {
  getAppointmentServiceDetails,
  setNewAppointmentValuesAction,
} from '../../actions/artichoke/Appointments.actions';
import {
  getServicesAction,
  setFilteredServicesAction,
} from '../../actions/artichoke/Services.actions';
import ServiceListItem from '../../components/artichoke/screensComponents/booked/ServiceListItem';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import { APPLICATION_ROUTES } from '../../constants';
import AddButton from '../../components/artichoke/common/AddButton';
import { HeaderSearchBar } from '../../components';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import LoadingSpinner from '../../components/LoadingSpinner';

class AddService extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  componentDidMount = () => {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      const currentDate = moment().unix() * 1000;
      this.props.getServicesAction(currentDate);
    });
  };

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  setNewAppointmentService = (item) => {
    this.props.setNewAppointmentValuesAction({
      key: 'appointmentLocation',
      value: null,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'clients',
      value: this.chkAllowClients(item),
    });
    this.props.setNewAppointmentValuesAction({
      key: 'date',
      value: null,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'time',
      value: null,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'repeatIntervalType',
      value: null,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'repeatUntilType',
      value: null,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'untilDay',
      value: null,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'count',
      value: null,
    });
    this.props.getAppointmentServiceDetails({
      service: item,
      returnRoute: this.props.route.params.returnRoute,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'isEdit',
      value: true,
    });
  };

  chkAllowClients = (item) => {
    const { newAppointment } = this.props;
    const maxParticipants = item?.maxParticipants ?? newAppointment?.service?.maxParticipants ?? null;
    const clientList = newAppointment?.clients || [];
    return maxParticipants != null && clientList.length <= maxParticipants
      ? clientList
      : [];
  };

  static navigationOptions = ({ route }) => ({
    title: 'Add a service',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
  });

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = this.props.servicesList.filter((item) => {
      const itemData = `${item.name.toUpperCase()}`;
      const textData = text.toUpperCase();
      return itemData.indexOf(textData) > -1;
    });
    this.props.setFilteredServicesAction(newData);
  };

  renderAddServiceButton = () => (
    <AddButton
      onPressAction={() => this.props.navigation.navigate(APPLICATION_ROUTES.CREATE_SERVICE, {
        previousScreen: APPLICATION_ROUTES.APP_CREATE_SERVICE,
      })}
    />
  );

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Service"
    />
  );

  render() {
    return this.props.serviceListLoading ? (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    ) : (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.filteredServiceList}
          renderItem={({ item }) => (
            <ServiceListItem
              item={item}
              setNewAppointmentService={this.setNewAppointmentService}
              symbol={this.props.symbol}
            />
          )}
          keyExtractor={(item) => item.id}
          refreshing={this.props.serviceListLoading}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Service Found" />
          )}
          ListHeaderComponent={this.renderHeader}
        />
        <View style={styles.footerList}>{this.renderAddServiceButton()}</View>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  footerList: {
    flex: 1,
    position: 'absolute', // Here is the trick
    bottom: 20, // Here is the trick
    right: 10,
    height: 70,
    flexGrow: 1,
  },
});

const mapStateToProps = (state) => ({
  servicesList: state.services.servicesList,
  filteredServiceList: state.services.filteredServices,
  newAppointment: state.appointments.newAppointmentValues,
  serviceListLoading: state.loadingComponents.serviceListLoading,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  getServicesAction,
  setFilteredServicesAction,
  setNewAppointmentValuesAction,
  getAppointmentServiceDetails,
})(AddService);
