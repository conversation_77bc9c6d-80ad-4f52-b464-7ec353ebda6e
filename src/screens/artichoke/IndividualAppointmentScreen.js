import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
  Alert,
} from 'react-native';
import moment from 'moment';
import { APPLICATION_ROUTES } from '../../constants';
import { colors } from '../../styles';
import AppointmentServiceView from '../../components/artichoke/screensComponents/booked/AppointmentServiceView';
import ViewClientItem from '../../components/artichoke/screensComponents/booked/ViewClientItem';
import {
  getClientBalance,
  setSelectedClient,
  clearPurchase,
  getClientCreditCard,
} from '../../actions/artichoke/Clients.actions';
import FormGroup from '../../components/artichoke/common/FormGroup';
import FormSubmitButton from '../../components/artichoke/common/FormSubmitButton';
import BookingMultipleClientItemNoTouch from '../../components/artichoke/screensComponents/booked/BookingMultipleClientItemNoTouch';
import {
  getAppointmentAction,
  deleteAppointmentAction,
  clearSelectedClassAction,
  getClassAction,
  checkInFreeAction,
  uncheckinAction,
} from '../../actions/artichoke/Appointments.actions';
import { generalStyles } from '../../styles/generalStyle';
import { deleteAlertStyles } from '../../styles/alertStyle';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import BookingClassItemNoTouch from '../../components/artichoke/screensComponents/booked/BookingClassItemNoTouch';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { LoadingSpinner } from '../../components';
import { track } from '../../util/Analytics';
import { selectClient } from '../../actions';
import nasm from '../../dataManager/apiConfig';
import CustomAwsomeAlert from '../../components/CustomAwsomeAlert';
import { curvedScale } from '../../util/responsive';

const videoBtnIcon = require('../../assets/video_btn_icon.png');
const checkMarkGreen = require('../../resources/btnCheckmarkNav.png');

const changeClassSheetRef = createRef();
const cancelClassSheetRef = createRef();
/* eslint-disable no-nested-ternary */

class IndividualAppointmentScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
      allDates: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  componentDidUpdate() {
    if (this.props.newAppointment) {
      this.props.navigation.setOptions({
        title: this.changePageTitle(this.props.newAppointment),
        headerLeft: () => (
          <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
        ),
      });
    }
  }

  handleSubmitButtonAction = () => {
    if (
      this.props.newAppointment.Invitee.length
      && this.props.newAppointment.Invitee[0].checkedIn === 'true'
    ) {
      Alert.alert(
        'Cancel Checkin',
        'You are about to cancel the checkin. Are you sure you want to proceed?',
        [
          {
            text: 'YES',
            onPress: () => this.onPressUncheckInClient(this.props.newAppointment.Invitee[0]),
          },
          { text: 'NO' },
        ],
        { cancelable: false },
      );
    } else {
      this.onPressCheckInClient(this.props.newAppointment.Invitee[0]);
    }
  };

  onCancelAppointment = () => {
    this.showAlert();
  };

  onPressUncheckInClient = (appInvitee) => {
    this.props.uncheckinAction({
      id: appInvitee.Client.id,
    });
  };

  onPressCheckInClient = (item) => {
    this.props.clearPurchase();
    this.props.setSelectedClient(item);
    if (parseInt(this.props.newAppointment.ProductDuration.price, 10) > 0) {
      this.props.getClientBalance(item.Client.id);
      this.props.getClientCreditCard(item.Client.id);
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.PAYMENTS,
      });
    } else {
      this.props.checkInFreeAction();
    }
  };

  getSelectedClientInfo = async (clientId) => {
    // Getting client details
    const client = await nasm.api.getUserByClientId(clientId);
    // Setting the selected client
    this.props.selectClient(client);
    // Navigating to the client dashboard
    this.props.navigation.navigate('tabClient', {
      role: this.props.currentUser.role,
    });
  };

  changePageTitle = (value) => {
    let title = '';
    if (value.isScheduled) {
      title = `Class - ${value.ProductDuration.name}`;
    } else if (value && value.Invitee.length === 1) {
      title = `${value.Invitee[0].Client.user.firstName} ${value.Invitee[0].Client.user.lastName} - ${value.ProductDuration.name}`;
    } else {
      title = `Group Session - ${value.ProductDuration.name}`;
    }
    return title.length > 25 ? `${title.slice(0, 25)}...` : title;
  };

  displayClient = (appointment) => {
    if (!appointment.isScheduled && appointment.Invitee.length === 1) {
      return (
        <ViewClientItem
          clients={appointment.Invitee}
          selectClientCallback={(clientId) => this.getSelectedClientInfo(clientId)}
          navigation={this.props.navigation}
          inapp={appointment.inapp}
        />
      );
    }
    if (!appointment.isScheduled && appointment.Invitee.length > 1) {
      return (
        <BookingMultipleClientItemNoTouch
          clients={appointment.Invitee}
          navigation={this.props.navigation}
          inapp={appointment.inapp}
        />
      );
    }
    if (appointment.isScheduled) {
      return (
        <BookingClassItemNoTouch
          clients={appointment.Invitee}
          navigation={this.props.navigation}
          inapp={appointment.inapp}
        />
      );
    }
    return null;
  };

  showAlert = () => {
    this.setState({
      showAlert: true,
    });
  };

  hideAlert = () => {
    this.setState({
      showAlert: false,
    });
  };

  cancelBookingAction = async (cancelAllDates) => {
    await this.props.deleteAppointmentAction(cancelAllDates);
  };

  showSelectedAddress = (item) => {
    let addressLine = '';
    if (item) {
      if (item.id === 1) {
        addressLine = 'Offered Remotely';
      } else if (item.id === 0) {
        addressLine = "Client's location";
      } else if (item.id === 2) {
        addressLine = 'Video Call';
      } else {
        addressLine = item.address2 !== ''
          ? `${item.address1}, ${item.address2}, ${item.city}, ${item.state}`
          : `${item.address1}, ${item.city}, ${item.state}`;
      }
    }
    return addressLine;
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  renderCheckInButton = (checkedIn) => {
    const checkInStyle = this.props.newAppointment.Invitee.length
      && this.props.newAppointment.Invitee[0].checkedIn === 'true'
      ? styles.checkedIn
      : styles.uncheckedIn;
    return this.props.newAppointment.Invitee.length > 1 ? (
      <FormSubmitButton
        title={`${checkedIn} of ${this.props.newAppointment.Invitee.length} Checked In`}
        onPressButton={() => {
          this.props.navigation.navigate({
            name: APPLICATION_ROUTES.MULTIPLE_CHECK_IN_CLIENT,
          });
        }}
      />
    ) : (
      <FormSubmitButton
        title={
          this.props.newAppointment.Invitee.length
          && this.props.newAppointment.Invitee[0].checkedIn === 'true'
            ? 'Cancel Checkin'
            : 'Check In'
        }
        onPressButton={() => this.handleSubmitButtonAction()}
        buttonStyle={checkInStyle}
        loading={this.props.checkInCashOrCheckLoading}
        color={colors.white}
      />
    );
  };

  renderClassCheckInButton = (checkedIn) => (
    <FormSubmitButton
      title={`${checkedIn} of ${this.props.newAppointment.Invitee.length} Checked In`}
      onPressButton={() => {
        this.props.navigation.navigate({
          name: APPLICATION_ROUTES.MULTIPLE_CHECK_IN_CLIENT,
        });
      }}
    />
  );

  renderStartSessionButton = () => (
    <FormSubmitButton
      title="Start Session"
      onPressButton={() => {
        this.props.navigation.navigate({
          name: APPLICATION_ROUTES.VIDEO_SCREEN,
          params: {
            channelName: this.props.newAppointment.appointmentToken,
          },
        });
      }}
      icon={(
        <View
          style={{
            display: 'flex',
          }}
        >
          <Image
            source={videoBtnIcon}
            style={{
              width: 56,
              height: 49,
            }}
          />
        </View>
      )}
    />
  );

  renderAppointmentButtons = () => (
    <View style={styles.bottom}>
      <FormSubmitButton
        buttonStyle={{
          backgroundColor: colors.white,
          borderColor: colors.bordergrey,
          borderWidth: 2,
          marginRight: 15,
          flex: 1,
        }}
        buttonLabelStyle={{
          color: colors.subGrey,
          ...generalStyles.fontBold,
        }}
        title="Change"
        onPressButton={() => this.props.navigation.navigate({
          name: APPLICATION_ROUTES.EDIT_APPOINTMENT,
          merge: true,
        })}
      />
      <FormSubmitButton
        buttonStyle={{
          backgroundColor: colors.white,
          borderColor: colors.bordergrey,
          borderWidth: 2,
          marginLeft: 5,
          flex: 1,
        }}
        loading={this.props.cancelAppointmentLoading}
        buttonLabelStyle={{
          color: colors.subGrey,
          ...generalStyles.fontBold,
        }}
        title="Cancel"
        onPressButton={() => this.onCancelAppointment()}
      />
    </View>
  );

  renderClassButtons = () => (
    <View style={styles.bottom}>
      <FormSubmitButton
        buttonStyle={{
          backgroundColor: colors.white,
          borderColor: colors.bordergrey,
          borderWidth: 2,
          marginRight: 15,
          flex: 1,
        }}
        buttonLabelStyle={{
          color: colors.subGrey,
          ...generalStyles.fontBold,
        }}
        title="Change"
        onPressButton={() => changeClassSheetRef.current?.setModalVisible(true)}
      />
      <FormSubmitButton
        buttonStyle={{
          backgroundColor: colors.white,
          borderColor: colors.bordergrey,
          borderWidth: 2,
          marginLeft: 5,
          flex: 1,
        }}
        loading={this.props.cancelAppointmentLoading}
        buttonLabelStyle={{
          color: colors.subGrey,
          ...generalStyles.fontBold,
        }}
        title="Cancel"
        onPressButton={() => cancelClassSheetRef.current?.setModalVisible(true)}
      />
      <CustomActionSheet actionSheetRef={changeClassSheetRef} title="Change">
        <FormGroup formStyle={styles.bottomSubmitModal}>
          <FormSubmitButton
            title="Just this date"
            onPressButton={() => {
              changeClassSheetRef.current?.setModalVisible(false);
              this.props.navigation.navigate({
                name: APPLICATION_ROUTES.EDIT_APPOINTMENT,
              });
              track('video_call_started', {
                channel: this.props.newAppointment.appointmentToken,
              });
            }}
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.white,
              marginVertical: 15,
            }}
            buttonLabelStyle={{ color: colors.azure }}
          />
          <FormSubmitButton
            title="All Dates"
            onPressButton={() => {
              changeClassSheetRef.current?.setModalVisible(false);
              this.props.clearSelectedClassAction();
              this.props.getClassAction(this.props.newAppointment.scheduleId);
            }}
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.white,
            }}
            buttonLabelStyle={{ color: colors.azure }}
          />
        </FormGroup>
      </CustomActionSheet>
      <CustomActionSheet actionSheetRef={cancelClassSheetRef} title="Cancel">
        <FormGroup formStyle={styles.bottomSubmitModal}>
          <FormSubmitButton
            title="Just this date"
            onPressButton={() => {
              cancelClassSheetRef.current?.setModalVisible(false);
              setTimeout(() => this.onCancelAppointment(), 500);
            }}
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.white,
              marginVertical: 15,
            }}
            buttonLabelStyle={{ color: colors.azure }}
          />
          <FormSubmitButton
            title="All Dates"
            onPressButton={() => {
              this.setState({
                allDates: true,
              });
              cancelClassSheetRef.current?.setModalVisible(false);
              setTimeout(() => this.onCancelAppointment(), 500);
            }}
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.white,
            }}
            buttonLabelStyle={{ color: colors.azure }}
          />
        </FormGroup>
      </CustomActionSheet>
    </View>
  );

  render() {
    const checkedIn = this.props.newAppointment
      ? this.props.newAppointment.Invitee.filter(
        (inv) => inv.checkedIn === 'true',
      ).length
      : 0;
    return this.props.newAppointment && !this.props.showAppointmentLoading ? (
      <SafeAreaView style={styles.container}>
        <CustomAwsomeAlert
          show={this.state.showAlert}
          showProgress={false}
          useNativeDriver
          title="Are you sure you want to cancel this booking?"
          message="Your client will be notified of this cancellation."
          closeOnTouchOutside={false}
          closeOnHardwareBackPress={false}
          showConfirmButton
          showCancelButton
          titleStyle={[deleteAlertStyles.alertTitle, { marginTop: 10 }]}
          messageStyle={deleteAlertStyles.alertText}
          confirmText="Nevermind"
          cancelText="Cancel"
          headerCustomView={(
            <View style={styles.iconView}>
              <Image source={checkMarkGreen} style={styles.checkMark} />
            </View>
          )}
          confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
          confirmButtonStyle={deleteAlertStyles.alertButton}
          cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
          cancelButtonStyle={deleteAlertStyles.alertButton}
          actionContainerStyle={{
            flexDirection: 'column',
            backgroundColor: colors.white,
            borderRadius: 5,
          }}
          contentContainerStyle={deleteAlertStyles.alertContainer}
          onCancelPressed={async () => {
            if (checkedIn > 0) {
              Alert.alert(
                'Error on cancelling appointment',
                'Please make sure no client is checked in before trying to cancel an appointment',
              );
            } else {
              this.hideAlert();
              cancelClassSheetRef.current?.setModalVisible(false);
              this.setState({
                deleteAppointment: true,
              });
              await this.cancelBookingAction(false);
              track('booking_cancelled_by_trainer');
            }
          }}
          onConfirmPressed={() => {
            this.hideAlert();
          }}
        />
        <ScrollView style={styles.form}>
          <AppointmentServiceView
            service={this.props.newAppointment}
            symbol={this.props.symbol}
          />
          {this.displayClient(this.props.newAppointment)}
          <View>
            <Text style={styles.dateAndTimeLabel}>Date & Time</Text>
            <View style={styles.dateAndTimeDetails}>
              <Text style={styles.date}>
                {moment(
                  this.props.newAppointment.start,
                  'MM/DD/YYYY hh:mm A',
                ).format('L')}
              </Text>
              <Text style={styles.time}>
                {moment(
                  this.props.newAppointment.start,
                  'MM/DD/YYYY hh:mm A',
                ).format('hh:mm A')}
              </Text>
            </View>
            {this.props.newAppointment.inapp.toString() !== 'true' && (
              <>
                <Text style={styles.locationLabel}>Location</Text>
                <Text style={styles.address}>
                  {this.props.newAppointment.remotely.toString() === 'true'
                    ? 'Offered remotely'
                    : this.props.newAppointment.clientAddressEnable.toString()
                      === 'true'
                      ? "Client's location"
                      : this.showSelectedAddress(
                        this.props.newAppointment.onsiteAddress,
                      )}
                </Text>
              </>
            )}
          </View>
        </ScrollView>
        <View style={styles.bottomButtonsContainer}>
          <FormGroup formStyle={styles.bottomSubmitButton}>
            {this.props.newAppointment.inapp.toString() === 'true'
              ? this.renderStartSessionButton()
              : this.props.newAppointment.isScheduled
                ? this.renderClassCheckInButton(checkedIn)
                : this.renderCheckInButton(checkedIn)}
          </FormGroup>
          {this.props.newAppointment.isScheduled
            ? this.renderClassButtons()
            : this.renderAppointmentButtons()}
        </View>
      </SafeAreaView>
    ) : (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bottomButtonsContainer: {
    flex: 0.25,
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 30,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 30,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    bottom: 89,
  },
  bottom: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginHorizontal: 15,
    position: 'absolute',
    margin: 16,
    bottom: 1,
    right: 1,
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
    maxHeight: '65%',
  },
  dateAndTimeLabel: {
    ...generalStyles.fontBold,
    paddingLeft: 20,
    paddingTop: 10,
    color: colors.black,
  },
  locationLabel: {
    ...generalStyles.fontBold,
    paddingLeft: 20,
    color: colors.black,
  },
  dateAndTimeDetails: {
    fontFamily: 'Avenir-Roman',
    fontSize: 17,
    flexDirection: 'row',
    paddingLeft: 20,
    paddingRight: 20,
    paddingBottom: 20,
    marginTop: 5,
  },
  date: {
    flex: 1,
    fontFamily: 'Avenir-Roman',
    fontSize: 17,
    color: colors.subGrey,
  },
  time: {
    fontFamily: 'Avenir-Roman',
    fontSize: 17,
    color: colors.subGrey,
  },
  address: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: 17,
    paddingHorizontal: 20,
    marginTop: 5,
  },
  uncheckedIn: {
    flex: 0.5,
    display: 'flex',
    width: '100%',
    borderColor: colors.duskBlue,
    backgroundColor: colors.duskBlue,
    textAlign: 'center',
    justifyContent: 'center',
    borderRadius: 28,
    borderWidth: 2,
  },
  checkedIn: {
    display: 'flex',
    width: '100%',
    backgroundColor: colors.greenselect,
    borderColor: colors.greenselect,
    textAlign: 'center',
    justifyContent: 'center',
    borderRadius: 28,
    borderWidth: 2,
  },
  bottomSubmitModal: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    marginBottom: 24,
    justifyContent: 'center',
    alignItems: 'center',
    maxHeight: 80,
  },
  iconView: { marginTop: 25 },
  checkMark: {
    height: curvedScale(45),
    width: curvedScale(50),
    resizeMode: 'contain',
    tintColor: colors.greenselect,
  },
});

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  newAppointment: state.appointments.selectedAppointment,
  showAppointmentLoading: state.loadingComponents.showAppointmentLoading,
  cancelAppointmentLoading: state.loadingComponents.cancelAppointmentLoading,
  checkInCashOrCheckLoading: state.loadingComponents.checkInCashOrCheckLoading,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  getAppointmentAction,
  deleteAppointmentAction,
  checkInFreeAction,
  getClientBalance,
  setSelectedClient,
  clearPurchase,
  clearSelectedClassAction,
  getClassAction,
  getClientCreditCard,
  selectClient,
  uncheckinAction,
})(IndividualAppointmentScreen);
