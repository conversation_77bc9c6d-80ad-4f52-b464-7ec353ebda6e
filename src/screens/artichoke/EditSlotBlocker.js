import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  ActivityIndicator,
  View,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import { colors } from '../../styles';
import EditSlotBlockerForm from '../../components/artichoke/screensComponents/slotblockers/EditSlotBlockerForm';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import {
  setNewSlotBlockerValuesAction,
  deleteSlotBlockerAction,
  updateSlotBlockerAction,
} from '../../actions/artichoke/Appointments.actions';
import { APPLICATION_ROUTES, FREQUENCY_SHORT } from '../../constants';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { LoadingSpinner } from '../../components';
import { track } from '../../util/Analytics';
import HeaderRightButton from '../../components/HeaderRightButton';

const repeatActionSheetRef = createRef();

class EditSlotBlocker extends Component {
  static navigationOptions = ({ route }) => ({
    headerRight: route.params?.saveSlotBlocker ?? (() => <View />),
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.props.navigation.setParams({
      goBack: this.goBack,
      saveSlotBlocker: this.renderHeaderRight,
    });
  }

  componentDidUpdate() {
    if (this.props.newSlotBlocker) {
      this.props.navigation.setOptions({
        title: this.changePageTitle(this.props.newSlotBlocker),
      });
    }
  }

  onChangeRepeatType = (repeat) => {
    if (repeat) {
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.RECURRENCE_SCREEN_SLOT_BLOCKER,
        params: {
          returnRoute: APPLICATION_ROUTES.EDIT_SLOTBLOCKER,
        },
      });
    } else {
      this.onDoesNotRepeat();
    }
  };

  onDoesNotRepeat = () => {
    this.props.setNewSlotBlockerValuesAction({
      key: 'repeatIntervalType',
      value: null,
    });
    this.props.setNewSlotBlockerValuesAction({ key: 'untilDay', value: '' });
    this.props.setNewSlotBlockerValuesAction({ key: 'count', value: 0 });
    this.props.setNewSlotBlockerValuesAction({
      key: 'repeatUntilType',
      value: null,
    });
  };

  changePageTitle = (value) => {
    let title = '';
    title = `Slot Blocker - ${value.description}`;
    return title.length >= 25 ? `${title.slice(0, 25)}...` : title;
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  saveSlotBlocker = () => {
    this.props.updateSlotBlockerAction();
    track('slotblocker_edited');
  };

  renderHeaderRight = () => {
    if (this.props.saveSlotBlockerLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.saveSlotBlocker()}
        title="Save"
        titleStyle={{
          color: colors.white,
        }}
      />
    );
  };

  render() {
    const repeatTypeShort = this.props.newSlotBlocker.repeatIntervalType
      ? 'Custom'
      : null;
    return this.props.newSlotBlocker && !this.props.showSlotBlockerLoading ? (
      <SafeAreaView style={styles.container}>
        <EditSlotBlockerForm
          initialValues={this.props.newSlotBlocker}
          setNewSlotBlockerValuesAction={
            this.props.setNewSlotBlockerValuesAction
          }
          repeatActionSheetRef={repeatActionSheetRef}
          navigation={this.props.navigation}
          deleteSlotBlockerAction={this.props.deleteSlotBlockerAction}
          deleteSlotBlockerLoading={this.props.deleteSlotBlockerLoading}
        />
        <CustomActionSheet actionSheetRef={repeatActionSheetRef} title="Repeat">
          <RadioForm>
            {FREQUENCY_SHORT.map((obj, i) => (
              <RadioButton
                labelHorizontal
                key={i}
                style={{
                  paddingHorizontal: 30,
                  paddingVertical: 20,
                  flex: 1,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.lightgrey,
                }}
              >
                {/*  You can set RadioButtonLabel before RadioButtonInput */}
                <RadioButtonInput
                  obj={obj}
                  index={i}
                  isSelected={repeatTypeShort === obj.value}
                  onPress={() => {
                    this.onChangeRepeatType(obj.value);
                    repeatActionSheetRef.current?.setModalVisible(false);
                  }}
                  borderWidth={1}
                  buttonSize={10}
                  buttonOuterSize={20}
                  buttonStyle={{ borderColor: colors.subGrey }}
                  buttonWrapStyle={{ marginLeft: 73 }}
                />
                <RadioButtonLabel
                  obj={obj}
                  index={i}
                  labelHorizontal
                  onPress={() => {
                    this.onChangeRepeatType(obj.value);
                    repeatActionSheetRef.current?.setModalVisible(false);
                  }}
                  labelStyle={generalStyles.fontBold}
                  labelWrapStyle={{}}
                />
              </RadioButton>
            ))}
          </RadioForm>
        </CustomActionSheet>
      </SafeAreaView>
    ) : (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newSlotBlocker: state.appointments.newSlotBlocker,
  showSlotBlockerLoading: state.loadingComponents.showSlotBlockerLoading,
  saveSlotBlockerLoading: state.loadingComponents.updateSlotBlockerLoading,
  deleteSlotBlockerLoading: state.loadingComponents.deleteSlotBlockerLoading,
});

export default connect(mapStateToProps, {
  setNewSlotBlockerValuesAction,
  updateSlotBlockerAction,
  deleteSlotBlockerAction,
})(EditSlotBlocker);
