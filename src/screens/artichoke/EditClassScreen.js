import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  View,
  ActivityIndicator,
} from 'react-native';
import { colors } from '../../styles';
import {
  getClientBalance,
  setSelectedClient,
  clearPurchase,
} from '../../actions/artichoke/Clients.actions';
import { getAvailableLocationsAction } from '../../actions/artichoke/Locations.actions';
import {
  setNewClassValuesAction,
  updateClassAction,
} from '../../actions/artichoke/Appointments.actions';
import EditClassForm from '../../components/artichoke/screensComponents/booked/EditClassForm';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import HeaderRightButton from '../../components/HeaderRightButton';

class EditClassScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Class',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
    headerRight: () => (
      <HeaderRightButton
        onPress={() => route.params.onUpdateClass()}
        title="Save"
        titleStyle={{
          color: colors.white,
          fontSize: 14,
          fontFamily: 'Avenir',
          fontWeight: '500',
          margin: 15,
        }}
      />
    ),
  });

  constructor(props) {
    super(props);
    this.props.getAvailableLocationsAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  componentDidMount() {
    if (this.props.newClassValues) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.scheduleClassLoading !== this.props.scheduleClassLoading) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  onUpdateClass = () => {
    this.props.updateClassAction();
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  renderHeaderRight = () => {
    if (this.props.scheduleClassLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.onUpdateClass()}
        title="Save"
        titleStyle={{
          color: colors.white,
          fontSize: 14,
          fontFamily: 'Avenir',
          fontWeight: '500',
          margin: 15,
        }}
      />
    );
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <EditClassForm
          initialValues={this.props.newClassValues}
          setNewClassValuesAction={this.props.setNewClassValuesAction}
          navigation={this.props.navigation}
          symbol={this.props.symbol}
        />
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newClassValues: state.appointments.newClassValues,
  selfBookings: state.locations.selfBookings,
  scheduleClassLoading: state.loadingComponents.scheduleClassLoading,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  getClientBalance,
  setSelectedClient,
  clearPurchase,
  setNewClassValuesAction,
  updateClassAction,
  getAvailableLocationsAction,
})(EditClassScreen);
