import React, { Component } from 'react';
import { Dimensions } from 'react-native';
import { connect } from 'react-redux';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { clearSelectedAppointment } from '../../actions/artichoke/Appointments.actions';
import * as screens from './index';
import { colors, materialTabBarOptions } from '../../styles';
import { setLoaderStateAction } from '../../actions/artichoke/Loaders.actions';
import { getUserDataAction } from '../../actions/artichoke/User.actions';

const { width } = Dimensions.get('window');
const tabWidth = width / 3;
const indicatorWidth = tabWidth / 1.2;
const TopTab = createMaterialTopTabNavigator();
class DashboardTopTabs extends Component {
  constructor(props) {
    super(props);
    this.props.getUserDataAction();
    this.props.clearSelectedAppointment();
  }

  render() {
    return (
      <TopTab.Navigator
        initialRouteName="Services"
        style={{
          ...materialTabBarOptions.tabBarOptions.style,
          backgroundColor: colors.duskBlue,
          paddingTop: 17,
        }}
        screenOptions={{
          ...materialTabBarOptions.tabBarOptions,
          tabBarStyle: {
            ...materialTabBarOptions.tabBarOptions.style,
            backgroundColor: colors.duskBlue,
            paddingTop: 17,
            elevation: 0,
          },
          tabBarActiveTintColor: colors.white,
          tabBarInactiveTintColor: '#8aa1bd',
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.indicatorStyle,
            backgroundColor: colors.medYellow,
            width: indicatorWidth,
            // Based of https://github.com/satya164/react-native-tab-view/issues/944#issuecomment-599224375
            left: (tabWidth - indicatorWidth) / 2,
          },
        }}
      >
        <TopTab.Screen
          name="Bookings"
          component={screens.Booked}
          listeners={{
            tabPress: () => {
              this.props.setLoaderStateAction({
                key: 'appointmentsListLoading',
                value: true,
              });
            },
          }}
        />
        <TopTab.Screen
          name="Services"
          component={screens.Services}
          listeners={{
            tabPress: () => {
              this.props.setLoaderStateAction({
                key: 'serviceListLoading',
                value: true,
              });
            },
          }}
        />
        <TopTab.Screen
          name="Hours"
          component={screens.HoursAndLocation}
          listeners={{
            tabPress: () => {
              this.props.setLoaderStateAction({
                key: 'locationsLoading',
                value: true,
              });
            },
          }}
        />
      </TopTab.Navigator>
    );
  }
}
const mapStateToProps = () => ({});

export default connect(mapStateToProps, {
  clearSelectedAppointment,
  setLoaderStateAction,
  getUserDataAction,
})(DashboardTopTabs);
