import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, FlatList, StyleSheet } from 'react-native';
import { setSelectedClientBalance } from '../../actions/artichoke/Clients.actions';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import { APPLICATION_ROUTES } from '../../constants';
import BalanceItem from '../../components/artichoke/screensComponents/transactionsAndBalances/BalanceItem';
import HeaderLeftButton from '../../components/HeaderLeftButton';

class AccountClientsBalancesList extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Select a Client',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  onOpenBalance = (clientPurchase) => {
    this.props.setSelectedClientBalance(clientPurchase);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.VIEW_BALANCE_SCREEN,
    });
  };

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  render() {
    const productBalances = [];
    this.props.clientBalance.forEach((balance) => balance.clientPurchaseProductDurations.forEach((cppd) => productBalances.push(cppd)));
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={productBalances}
          renderItem={({ item }) => (
            <BalanceItem item={item} onPressItem={this.onOpenBalance} />
          )}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Balances Found" />
          )}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  clientBalance: state.clients.clientBalance,
});

export default connect(mapStateToProps, {
  setSelectedClientBalance,
})(AccountClientsBalancesList);
