import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, FlatList, StyleSheet } from 'react-native';
import {
  getActiveClientsAction,
  setFilteredClientsListAction,
  setSelectedClient,
  getClientCreditCard,
} from '../../actions/artichoke/Clients.actions';
import TouchableClientListItem from '../../components/artichoke/screensComponents/services/TouchableClientListItem';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import { HeaderSearchBar, LoadingSpinner } from '../../components';
import { APPLICATION_ROUTES } from '../../constants';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { setLoaderStateAction } from '../../actions/artichoke/Loaders.actions';

class ClientsList extends Component {
  static navigationOptions = ({ navigation }) => ({
    title: 'Select a Client',
    headerLeft: () => <HeaderLeftButton onPress={() => navigation.goBack()} />,
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
    this.props.getActiveClientsAction();
  }

  onPressSelectClient = (item) => {
    this.props.setLoaderStateAction({
      key: 'showBuyProductsLoading',
      value: true,
    });
    this.props.setSelectedClient(item);
    this.props.getClientCreditCard(item.id);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.PACKAGE_PAYMENTS,
      merge: true,
    });
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = this.props.clientsList.filter((item) => {
      const itemData = `${item.user.firstName.toUpperCase()} ${item.user.lastName.toUpperCase()}`;
      const textData = text.toUpperCase();
      return itemData.indexOf(textData) > -1;
    });
    this.props.setFilteredClientsListAction(newData);
  };

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Client First Name"
    />
  );

  render() {
    return !this.props.showActiveClientsLoading ? (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.filteredClientsList}
          renderItem={({ item }) => (
            <TouchableClientListItem
              item={item}
              onPressSelectClient={() => this.onPressSelectClient(item)}
            />
          )}
          keyExtractor={(item) => item.id.toString()}
          refreshing={this.props.clientListLoading}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Client Found" />
          )}
          ListHeaderComponent={this.renderHeader}
        />
      </SafeAreaView>
    ) : (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  clientsList: state.clients.clientsList,
  filteredClientsList: state.clients.filteredClientsList,
  showActiveClientsLoading: state.loadingComponents.showActiveClientsLoading,
});

const mapDispatchToProps = {
  getActiveClientsAction,
  setFilteredClientsListAction,
  setSelectedClient,
  getClientCreditCard,
  setLoaderStateAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(ClientsList);
