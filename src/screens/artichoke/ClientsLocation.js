import React, { Component, createRef } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Alert,
  ActivityIndicator,
  View,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import { connect } from 'react-redux';
import LoadingSpinner from '../../components/LoadingSpinner';
import { colors } from '../../styles';
import ClientLocationForm from '../../components/artichoke/screensComponents/hoursLocations/ClientLocationForm';
import {
  getAvailableLocationsAction,
  getSelfBookingAction,
  setClientLocationValuesAction,
  saveClientLocationAction,
} from '../../actions/artichoke/Locations.actions';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { track } from '../../util/Analytics';
import HeaderRightButton from '../../components/HeaderRightButton';

const travelTimeActionSheetRef = createRef();
const travel_time_props = [
  { label: 'No travel time', value: 0 },
  { label: '15 min', value: 15 },
  { label: '30 min', value: 30 },
  { label: '45 min', value: 45 },
  { label: '60 min', value: 60 },
];

const travelRadiusActionSheetRef = createRef();
const travel_radius_props = [
  { label: 'No limit', value: 0 },
  { label: '5 miles', value: 5 },
  { label: '10 miles', value: 10 },
  { label: '15 miles', value: 15 },
  { label: '20 miles', value: 20 },
  { label: '50 miles', value: 50 },
];

class ClientsLocationScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: "Client's Location",
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {};
    this.props.navigation.setParams({
      onSaveClientLocation: this.onSaveClientLocation,
      goBack: this.goBack,
    });
    this.props.getAvailableLocationsAction();
    this.props.getSelfBookingAction();
  }

  componentDidMount() {
    if (this.props.clientLocation) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.locationsLoading !== this.props.locationsLoading) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  onSaveClientLocation = () => {
    if (this.props.clientLocation.offsiteStartTimes.length === 0) {
      Alert.alert(
        'No start slot selected',
        'Please select at least one start slot for current location',
      );
    } else if (this.props.clientLocation.workHours.indexOf('null') !== -1) {
      Alert.alert('Invalid Workhours', 'Please check the workdays');
    } else {
      this.props.saveClientLocationAction();
      track('client_location_enabled');
    }
  };

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  renderHeaderRight = () => {
    if (this.props.locationsLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.onSaveClientLocation()}
        title="Save"
        titleStyle={{ ...generalStyles.navigationButtonText }}
      />
    );
  };

  onChangeTravelTime = (value) => {
    this.props.setClientLocationValuesAction({
      key: 'travelTime',
      value,
    });
    travelTimeActionSheetRef.current?.setModalVisible(false);
  };

  onChangeTravelRadius = (value) => {
    this.props.setClientLocationValuesAction({
      key: 'travelRadius',
      value,
    });
    travelRadiusActionSheetRef.current?.setModalVisible(false);
  };

  render() {
    return this.props.locationDataLoading ? (
      <LoadingSpinner visible size="large" backgroundColor={colors.white} />
    ) : (
      <SafeAreaView style={styles.container}>
        <ClientLocationForm
          navigation={this.props.navigation}
          selfBookings={this.props.clientLocation}
          onChangeClientLocationsProps={
            this.props.setClientLocationValuesAction
          }
          travelTimeActionSheetRef={travelTimeActionSheetRef}
          travelRadiusActionSheetRef={travelRadiusActionSheetRef}
        />
        <CustomActionSheet
          actionSheetRef={travelTimeActionSheetRef}
          title="Allow For Travel Time"
        >
          <RadioForm>
            {travel_time_props.map((obj, i) => (
              <RadioButton
                labelHorizontal
                key={i}
                style={{
                  paddingHorizontal: 30,
                  paddingVertical: 20,
                  flex: 1,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.lightgrey,
                }}
              >
                {/*  You can set RadioButtonLabel before RadioButtonInput */}
                <RadioButtonInput
                  obj={obj}
                  index={i}
                  isSelected={
                    this.props.clientLocation.travelTime === obj.value
                  }
                  onPress={() => this.onChangeTravelTime(obj.value)}
                  borderWidth={1}
                  buttonSize={10}
                  buttonOuterSize={20}
                  buttonStyle={{ borderColor: colors.subGrey }}
                  buttonWrapStyle={{ marginLeft: 73 }}
                />
                <RadioButtonLabel
                  obj={obj}
                  index={i}
                  labelHorizontal
                  onPress={() => this.onChangeTravelTime(obj.value)}
                  labelStyle={generalStyles.fontBold}
                  labelWrapStyle={{}}
                />
              </RadioButton>
            ))}
          </RadioForm>
        </CustomActionSheet>
        <CustomActionSheet
          actionSheetRef={travelRadiusActionSheetRef}
          title="Travel Radius"
        >
          <RadioForm>
            {travel_radius_props.map((obj, i) => (
              <RadioButton
                labelHorizontal
                key={i}
                style={{
                  paddingHorizontal: 30,
                  paddingVertical: 20,
                  flex: 1,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.lightgrey,
                }}
              >
                {/*  You can set RadioButtonLabel before RadioButtonInput */}
                <RadioButtonInput
                  obj={obj}
                  index={i}
                  isSelected={
                    this.props.clientLocation.travelRadius === obj.value
                  }
                  onPress={() => this.onChangeTravelRadius(obj.value)}
                  borderWidth={1}
                  buttonSize={10}
                  buttonOuterSize={20}
                  buttonStyle={{ borderColor: colors.subGrey }}
                  buttonWrapStyle={{ marginLeft: 73 }}
                />
                <RadioButtonLabel
                  obj={obj}
                  index={i}
                  labelHorizontal
                  onPress={() => this.onChangeTravelRadius(obj.value)}
                  labelStyle={generalStyles.fontBold}
                  labelWrapStyle={{}}
                />
              </RadioButton>
            ))}
          </RadioForm>
        </CustomActionSheet>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  locations: state.locations.locationsList,
  clientLocation: state.locations.clientLocation,
  locationsLoading: state.loadingComponents.clientLocationsLoading,
  locationDataLoading: state.loadingComponents.locationsLoading,
});

export default connect(mapStateToProps, {
  getAvailableLocationsAction,
  getSelfBookingAction,
  setClientLocationValuesAction,
  saveClientLocationAction,
})(ClientsLocationScreen);
