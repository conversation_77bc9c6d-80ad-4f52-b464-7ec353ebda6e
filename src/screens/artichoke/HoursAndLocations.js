import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, FlatList, StyleSheet, Text, View,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import AwesomeAlert from 'react-native-awesome-alerts';
import {
  getAvailableLocationsAction,
  getSelfBookingAction,
  disableClientLocationAction,
  disableRemoteLocationAction,
  setPrepTimeAction,
  setSelectedLocationForEditAction,
  disableLocationAction,
  clearNewLocationValuesAction,
  clearSelectedLocationForEditAction,
  disableInAppVideoLocationAction,
} from '../../actions/artichoke/Locations.actions';
import HoursLocationListItem from '../../components/artichoke/screensComponents/hoursLocations/HoursLocationListItem';
import FormPrepTime from '../../components/artichoke/common/FormPrepTime';
import FormGroup from '../../components/artichoke/common/FormGroup';
import { colors } from '../../styles';
import HoursLocationClientLocationItem from '../../components/artichoke/screensComponents/hoursLocations/HoursLocationClientLocationItem';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import { APPLICATION_ROUTES } from '../../constants';
import HoursLocationRemoteLocationItem from '../../components/artichoke/screensComponents/hoursLocations/HoursLocationRemoteLocationItem';
import { generalStyles, locationStyles } from '../../styles/generalStyle';
import AddButton from '../../components/artichoke/common/AddButton';
import LoadingSpinner from '../../components/LoadingSpinner';
import HoursLocationVideoCallLocationItem from '../../components/artichoke/screensComponents/hoursLocations/HoursLocationVideoCallLocationItem';
import { readFromStorage, saveToStorage } from '../../util/Storage.utils';
import { deleteAlertStyles } from '../../styles/alertStyle';
import { track } from '../../util/Analytics';

const prepTimeActionSheetRef = createRef();
const radio_props = [
  { label: 'No prep time', value: 0 },
  { label: '15 min', value: 15 },
  { label: '30 min', value: 30 },
  { label: '45 min', value: 45 },
  { label: '60 min', value: 60 },
];
class HoursAndLocationsScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: '',
      showAlert: false,
      showDisableAlert: false,
      disableLocationType: '',
      disableLocationItem: null,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.getSelfBookingAction();
      this.props.getAvailableLocationsAction();
      this.checkVideoCallAlert();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onChangeClientLocationsSwitch = (value) => {
    if (value === false) {
      this.setState({ showDisableAlert: true });
      this.setState({ disableLocationType: 'ClientLocation' });
    } else {
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.CLIENTS_LOCATION,
      });
    }
  };

  onChangeRemoteLocationsSwitch = (value) => {
    if (value === false) {
      this.setState({ showDisableAlert: true });
      this.setState({ disableLocationType: 'Remote' });
    } else {
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.REMOTE_LOCATION,
      });
    }
  };

  onChangeVideoCallLocationsSwitch = (value) => {
    if (value === false) {
      this.setState({ showDisableAlert: true });
      this.setState({ disableLocationType: 'VideoCall' });
    } else {
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.VIDEO_CALL_LOCATION,
      });
    }
  };

  onChangeLocationsSwitch = (value, item) => {
    if (value === false) {
      this.setState({ showDisableAlert: true });
      this.setState({ disableLocationType: 'Other' });
      this.setState({ disableLocationItem: item });
    } else {
      this.goToEditLocationScreen(item);
    }
  };

  onChangePrepTime = (value) => {
    this.props.setPrepTimeAction(value);
    prepTimeActionSheetRef.current?.setModalVisible(false);
  };

  checkVideoCallAlert = async () => {
    const isVideoCallAlertShowed = await readFromStorage('showVideoCallAlert');
    this.setState({ showAlert: !isVideoCallAlertShowed });
  };

  goToEditLocationScreen = (location) => {
    this.props.clearSelectedLocationForEditAction();
    this.props.setSelectedLocationForEditAction(location);
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.MY_LOCATIONS });
  };

  clientLocationOfferedServices = () => {
    let offeredServicesCount = 0;
    if (this.props.services) {
      this.props.services.map((serviceItem) => {
        if (serviceItem.clientLocationEnabled) {
          offeredServicesCount += 1;
        }
        return null;
      });
    }
    return offeredServicesCount;
  };

  remoteOfferedServices = () => {
    let offeredServicesCount = 0;
    if (this.props.services) {
      this.props.services.map((serviceItem) => {
        if (serviceItem.offeredOnline) {
          offeredServicesCount += 1;
        }
        return null;
      });
    }
    return offeredServicesCount;
  };

  videoCallOfferedServices = () => {
    let videoCallServicesCount = 0;
    if (this.props.services) {
      this.props.services.map((serviceItem) => {
        if (serviceItem.inappEnabled) {
          videoCallServicesCount += 1;
        }
        return null;
      });
    }
    return videoCallServicesCount;
  };

  hideAlert = async () => {
    this.setState({ showAlert: false });
    await saveToStorage('showVideoCallAlert', true);
  };

  hideDisableAlert = async () => {
    this.setState({ showDisableAlert: false });
  };

  renderAddLocationButton = () => (
    <AddButton
      onPressAction={() => {
        this.props.clearNewLocationValuesAction();
        this.props.navigation.navigate({
          name: APPLICATION_ROUTES.CREATE_NEW_LOCATION,
        });
      }}
    />
  );

  render() {
    const { showAlert } = this.state;
    return this.props.loading ? (
      <LoadingSpinner visible size="large" backgroundColor={colors.white} />
    ) : (
      <SafeAreaView style={styles.container}>
        <View>
          <AwesomeAlert
            show={showAlert}
            showProgress={false}
            useNativeDriver
            title="Video Call Available"
            message="Sessions can now be hosted via video call. This feature can be enabled/disabled at any time. Tap on the Video Call field to edit work hours."
            closeOnTouchOutside
            closeOnHardwareBackPress
            showConfirmButton
            showCancelButton={false}
            titleStyle={deleteAlertStyles.alertTitle}
            messageStyle={deleteAlertStyles.alertText}
            confirmText="Continue"
            confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
            confirmButtonStyle={deleteAlertStyles.alertButton}
            cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
            cancelButtonStyle={deleteAlertStyles.alertButton}
            actionContainerStyle={{
              flexDirection: 'column',
              backgroundColor: colors.white,
            }}
            contentContainerStyle={deleteAlertStyles.alertContainer}
            onConfirmPressed={() => {
              this.hideAlert();
            }}
            onCancelPressed={() => {
              this.hideAlert();
            }}
          />
          <AwesomeAlert
            show={this.state.showDisableAlert}
            showProgress={false}
            useNativeDriver
            title="Please Note"
            message={`Disabling a location will NOT change or cancel any scheduled services already booked at that location.

Please confirm that you understand this prior to disabling the location.`}
            closeOnTouchOutside={false}
            closeOnHardwareBackPress={false}
            showConfirmButton
            showCancelButton
            titleStyle={deleteAlertStyles.alertTitle}
            messageStyle={styles.alertText}
            confirmText="Nevermind"
            cancelText="Disable Location"
            confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
            confirmButtonStyle={deleteAlertStyles.alertButton}
            cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
            cancelButtonStyle={deleteAlertStyles.alertButton}
            actionContainerStyle={{
              flexDirection: 'column',
              backgroundColor: colors.white,
              borderRadius: 5,
            }}
            contentContainerStyle={{
              ...deleteAlertStyles.alertContainer,
              height: 275,
            }}
            onCancelPressed={() => {
              if (this.state.disableLocationType === 'Remote') {
                this.props.disableRemoteLocationAction();
              } else if (this.state.disableLocationType === 'ClientLocation') {
                this.props.disableClientLocationAction();
              } else if (this.state.disableLocationType === 'VideoCall') {
                this.props.disableInAppVideoLocationAction();
              } else if (this.state.disableLocationType === 'Other') {
                this.props.disableLocationAction(
                  this.state.disableLocationItem,
                );
                track('location_disabled');
              }
              this.hideDisableAlert();
            }}
            onConfirmPressed={() => {
              this.hideDisableAlert();
            }}
          />
          <FlatList
            data={this.props.locations}
            renderItem={({ item }) => (
              <HoursLocationListItem
                item={item}
                showDisableAlert={(this.state.disableLocationItem && this.state.disableLocationItem.id === item.id) && this.state.disableLocationType === 'Other' && this.state.showDisableAlert}
                onGoToEditScreen={this.goToEditLocationScreen}
                onChangeLocationsSwitch={this.onChangeLocationsSwitch}
              />
            )}
            contentContainerStyle={styles.flatListContainer}
            keyExtractor={(item) => `location-key-${item.id}`}
            refreshing={this.props.getAvailableLocationsAction}
            ListHeaderComponent={(
              <View>
                <FormGroup formStyle={styles.formStyle}>
                  <Text style={locationStyles.sectionLabel}>
                    Time Between Bookings
                  </Text>
                </FormGroup>
                <FormGroup>
                  <FormPrepTime
                    nLocations={2}
                    onPressFunc={() => {
                      prepTimeActionSheetRef.current?.setModalVisible();
                    }}
                    value={
                      this.props.selfBookings
                      && this.props.selfBookings.prepTime
                        ? this.props.selfBookings.prepTime
                        : 0
                    }
                  />
                </FormGroup>
                <FormGroup formStyle={styles.formStyle}>
                  <Text style={locationStyles.sectionLabel}>
                    Service Locations
                  </Text>
                </FormGroup>
                <HoursLocationVideoCallLocationItem
                  selfBookings={this.props.selfBookings}
                  showDisableAlert={this.state.disableLocationType === 'VideoCall' && this.state.showDisableAlert}
                  onChangeVideoCallLocationsSwitch={
                    this.onChangeVideoCallLocationsSwitch
                  }
                  navigation={this.props.navigation}
                  videoCallOfferedServices={this.videoCallOfferedServices()}
                />
                <HoursLocationRemoteLocationItem
                  selfBookings={this.props.selfBookings}
                  showDisableAlert={this.state.disableLocationType === 'Remote' && this.state.showDisableAlert}
                  onChangeClientLocationsSwitch={
                    this.onChangeRemoteLocationsSwitch
                  }
                  navigation={this.props.navigation}
                  remoteOfferedServices={this.remoteOfferedServices()}
                />
                <HoursLocationClientLocationItem
                  selfBookings={this.props.selfBookings}
                  showDisableAlert={this.state.disableLocationType === 'ClientLocation' && this.state.showDisableAlert}
                  onChangeClientLocationsSwitch={
                    this.onChangeClientLocationsSwitch
                  }
                  navigation={this.props.navigation}
                  clientLocationOfferedServices={this.clientLocationOfferedServices()}
                />
              </View>
            )}
          />
          <CustomActionSheet
            actionSheetRef={prepTimeActionSheetRef}
            title="Prep Time"
          >
            <RadioForm>
              {radio_props.map((obj, i) => (
                <RadioButton
                  labelHorizontal
                  key={i}
                  style={{
                    paddingHorizontal: 30,
                    paddingVertical: 20,
                    flex: 1,
                    borderBottomWidth: 1,
                    borderBottomColor: colors.lightgrey,
                  }}
                >
                  {/*  You can set RadioButtonLabel before RadioButtonInput */}
                  <RadioButtonInput
                    obj={obj}
                    index={i}
                    isSelected={
                      this.props.selfBookings
                      && this.props.selfBookings.prepTime
                      && this.props.selfBookings.prepTime === obj.value
                    }
                    onPress={() => this.onChangePrepTime(obj.value)}
                    buttonStyle={{}}
                    buttonWrapStyle={{ justifyContent: 'center' }}
                    borderWidth={1}
                    buttonSize={10}
                    buttonOuterSize={20}
                    buttonOuterColor={{ color: colors.subGrey }}
                  />
                  <RadioButtonLabel
                    obj={obj}
                    index={i}
                    labelHorizontal
                    onPress={() => this.onChangePrepTime(obj.value)}
                    labelStyle={{
                      ...generalStyles.fontBold,
                      color: colors.black,
                    }}
                    labelWrapStyle={{}}
                  />
                </RadioButton>
              ))}
            </RadioForm>
          </CustomActionSheet>
        </View>
        <View style={styles.footerList}>{this.renderAddLocationButton()}</View>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  formStyle: {
    paddingTop: 20,
  },
  footerList: {
    flex: 1,
    position: 'absolute', // Here is the trick
    bottom: 0, // Here is the trick
    right: 0,
    height: 70,
    flexGrow: 1,
  },
  flatListContainer: {
    paddingBottom: 100,
  },
});

const mapStateToProps = (state) => ({
  locations: state.locations.locationsList,
  selfBookings: state.locations.selfBookings,
  selfBookingsUsers: state.locations.selfBookingsUsers,
  services: state.services.servicesList,
  loading: state.loadingComponents.locationsLoading,
});

const mapDispatchToProps = {
  getAvailableLocationsAction,
  getSelfBookingAction,
  disableClientLocationAction,
  disableRemoteLocationAction,
  disableInAppVideoLocationAction,
  setPrepTimeAction,
  setSelectedLocationForEditAction,
  disableLocationAction,
  clearNewLocationValuesAction,
  clearSelectedLocationForEditAction,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(HoursAndLocationsScreen);
