import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, FlatList, StyleSheet } from 'react-native';
import {
  getTimezonesAction,
  setUpdatedAccountSettingsAction,
  saveAccountSettingsAction,
} from '../../actions/artichoke/User.actions';
import TimeZoneListItem from '../../components/artichoke/screensComponents/login/TimeZoneListItem';
import { HeaderSearchBar, LoadingSpinner } from '../../components';
import { APPLICATION_ROUTES } from '../../constants';
import HeaderLeftButton from '../../components/HeaderLeftButton';

class ListTimeZones extends Component {
  static navigationOptions = ({ route }) => ({
    title: APPLICATION_ROUTES.TIMEZONE_SELECTION,
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      filteredTimezoneList: this.props.timezones,
    };
    this.props.getTimezonesAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  componentDidUpdate(prevProps) {
    if (!prevProps.timezones && this.props.timezones) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({ filteredTimezoneList: this.props.timezones });
    }
  }

  onSelectTimezone = (item) => {
    this.props.setUpdatedAccountSettingsAction({
      key: 'newTimeZone',
      value: item,
    });
    this.props.saveAccountSettingsAction();
    this.goBack();
  };

  checkSelectedTimezone = (item) => (this.props.account.newTimeZone
    ? this.props.account.newTimeZone.id === item.id
    : this.props.account.creator.TimeZone.id === item.id);

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = this.props.timezones.filter((item) => {
      const itemData = `${item.name.toUpperCase()}`;
      const textData = text.toUpperCase();
      return itemData.indexOf(textData) > -1;
    });
    this.setState({
      filteredTimezoneList: newData,
    });
  };

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Search"
    />
  );

  render() {
    return this.state.filteredTimezoneList && !this.props.timezonesLoading ? (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.state.filteredTimezoneList}
          renderItem={({ item }) => (
            <TimeZoneListItem
              item={item}
              isSelected={this.checkSelectedTimezone(item)}
              onSelectItem={this.onSelectTimezone}
            />
          )}
          keyExtractor={(item) => item.id.toString()}
          refreshing={this.props.timezonesLoading}
          ListHeaderComponent={this.renderHeader}
        />
      </SafeAreaView>
    ) : (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  timezones: state.user.timezones,
  account: state.user.accountSettings,
  timezonesLoading: state.loadingComponents.timezonesLoading,
});

export default connect(mapStateToProps, {
  getTimezonesAction,
  setUpdatedAccountSettingsAction,
  saveAccountSettingsAction,
})(ListTimeZones);
