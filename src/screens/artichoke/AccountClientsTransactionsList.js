import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, FlatList, StyleSheet } from 'react-native';
import moment from 'moment';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import TransactionItem from '../../components/artichoke/screensComponents/transactionsAndBalances/TransactionsItem';
import { APPLICATION_ROUTES } from '../../constants';
import {
  setFilteredClientTransactions,
  setSelectedClientTransactionAction,
} from '../../actions/artichoke/Clients.actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { HeaderSearchBar } from '../../components';

const noTransactionIcon = require('../../assets/imgAddChat.png');

class AccountClientsTransactionsList extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Select a Client',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = this.props.clientTransactions.filter((item) => {
      let serviceName = '';
      if (
        item.purchase
        && item.purchase.clientPurchaseProductDurations
        && item.purchase.clientPurchaseProductDurations.length
        && item.purchase.clientPurchaseProductDurations[0].productName
      ) {
        serviceName = item.purchase.clientPurchaseProductDurations[0].productName;
      } else if (
        item.purchase
        && item.purchase.pack
        && item.purchase.pack.name
      ) {
        serviceName = item.purchase.pack.name;
      }
      const itemData = `${serviceName} ${moment(
        item.created,
        'MM/DD/YYYY hh:mm a',
      ).format('MM/DD/YYYY')}`;
      const textData = text.toUpperCase();
      return itemData.toUpperCase().indexOf(textData) > -1;
    });
    this.props.setFilteredClientTransactions(newData);
  };

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Service, Date"
    />
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.filteredClientTransactions}
          renderItem={({ item }) => {
            let redirectRoute = APPLICATION_ROUTES.CHECKIN_DETAILS;
            if (item.purchase) {
              if (item.purchase.refunded) {
                redirectRoute = APPLICATION_ROUTES.REFUND_DETAILS;
              } else {
                redirectRoute = APPLICATION_ROUTES.TRANSACTION_DETAIL;
              }
            }
            return (
              <TransactionItem
                item={item}
                onPressItem={() => {
                  this.props.setSelectedClientTransactionAction(item);
                  this.props.navigation.navigate(redirectRoute);
                }}
                symbol={this.props.symbol}
              />
            );
          }}
          keyExtractor={(item, index) => index.toString()}
          ListHeaderComponent={this.renderHeader}
          ListEmptyComponent={() => (
            <EmptyServiceList
              message="No Transactions Found"
              hasIcon={noTransactionIcon}
            />
          )}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  clientTransactions: state.clients.clientTransactions,
  filteredClientTransactions: state.clients.filteredClientTransactions,
  symbol: state.user.accountSettings?.symbol,
});

const mapDispatchToProps = {
  setSelectedClientTransactionAction,
  setFilteredClientTransactions,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(AccountClientsTransactionsList);
