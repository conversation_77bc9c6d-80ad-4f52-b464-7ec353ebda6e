import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, FlatList, StyleSheet } from 'react-native';
import { colors } from '../../styles';
import {
  getAvailableLocationsAction,
  getSelfBookingAction,
  setSelectedLocationForEditAction,
  setSelectedLocationForEditValuesAction,
  saveSelectedLocationForEditAction,
  setActiveServiceCreation,
  saveInAppVideoLocationAction,
  saveRemoteLocationAction,
  saveClientLocationAction,
} from '../../actions/artichoke/Locations.actions';
import EmptyLocationList from '../../components/artichoke/screensComponents/locations/EmptyLocationList';
import EnableLocationListItem from '../../components/artichoke/screensComponents/locations/EnableLocationListItem';
import LoadingSpinner from '../../components/LoadingSpinner';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const noLocationImage = require('../../assets/iconsGlobalAddLocation.png');

class QuickEnableLocation extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Select Location to Enable',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.props.getAvailableLocationsAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
    this.props.getSelfBookingAction();
  }

  goBack = () => {
    this.props.navigation.goBack();
  };

  onEnableLocation = (item) => {
    if (item.id === 0) {
      this.props.saveClientLocationAction();
    } else if (item.id === 1) {
      this.props.saveRemoteLocationAction();
    } else if (item.id === 2) {
      this.props.saveInAppVideoLocationAction();
    } else {
      this.props.setSelectedLocationForEditAction(item);
      this.props.setSelectedLocationForEditValuesAction({
        key: 'checked',
        value: true,
      });
      this.props.saveSelectedLocationForEditAction();
    }
    this.props.setActiveServiceCreation(false);
    this.props.navigation.goBack();
  };

  listAvailableLocations = () => {
    const uncheckedLocations = this.props.locationsList.filter(
      (loc) => loc.checked !== true,
    );
    const items = [...uncheckedLocations];

    if (this.props.selfBookings.offsite_services_available === 'false') {
      items.push({ id: 0, addressName: "Client's location" });
    }

    if (this.props.selfBookings.remote_services_available === 'false') {
      items.push({ id: 1, addressName: 'Remotely' });
    }

    if (this.props.selfBookings.inapp_services_available === 'false') {
      // add video call as location
      items.push({ id: 2, addressName: 'Video Call' });
    }

    return items;
  };

  render() {
    const items = this.listAvailableLocations().reverse();

    return this.props.locationsLoading ? (
      <LoadingSpinner
        visible
        size="large"
        title="LOADING LOCATIONS..."
        // backgroundColor={'rgba(0, 0, 0, 0.25)'}
        backgroundColor={colors.white}
      />
    ) : (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={items}
          renderItem={({ item }) => (
            <EnableLocationListItem
              item={item}
              onEnableLocation={this.onEnableLocation}
            />
          )}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={() => (
            <EmptyLocationList
              message="No Location to enable"
              subTitle="Customize your list of locations by using the Hours tab"
              hasIcon={noLocationImage}
            />
          )}
        />
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  locationsList: state.locations.locationsList,
  locationsLoading: state.loadingComponents.locationsLoading,
  selfBookings: state.locations.selfBookings,
});
export default connect(mapStateToProps, {
  getAvailableLocationsAction,
  getSelfBookingAction,
  setSelectedLocationForEditAction,
  setSelectedLocationForEditValuesAction,
  saveSelectedLocationForEditAction,
  setActiveServiceCreation,
  saveInAppVideoLocationAction,
  saveRemoteLocationAction,
  saveClientLocationAction,
})(QuickEnableLocation);
