/* eslint-disable import/no-cycle */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import {
  getClientTransactions,
  getClientBalance,
} from '../../actions/artichoke/Clients.actions';
import { colors } from '../../styles';
import * as screens from './index';
import { track } from '../../util/Analytics';

const TopTab = createMaterialTopTabNavigator();
class AccountClientsTransactionsAndBalances extends Component {
  constructor(props) {
    super(props);
    this.props.getClientBalance(this.props?.selectedClient?.id);
    this.props.getClientTransactions(this.props?.selectedClient?.id);
  }

  componentDidUpdate() {
    if (this.props.selectedClient) {
      this.props.navigation.setOptions({
        title: this.changePageTitle(this.props.selectedClient),
      });
    }
  }

  changePageTitle = (value) => {
    let title = '';
    title = `${value?.user?.firstName} ${value?.user?.lastName}`;
    return title;
  };

  render() {
    const backgroundColor = this.props.trainerActiveProfile?.ClubId
      ? colors.black
      : colors.duskBlue;
    return (
      <TopTab.Navigator
        initialRouteName="Transactions"
        screenOptions={{
          swipeEnabled: true,
          tabBarStyle: {
            backgroundColor,
            borderTopColor: backgroundColor,
          },
          tabBarLabelStyle: {
            textTransform: 'none',
            fontFamily: 'Avenir-Heavy',
            fontSize: 17,
          },
          tabBarActiveTintColor: colors.white,
          tabBarInactiveTintColor: '#ffffff50',
          tabBarIndicatorStyle: {
            backgroundColor: colors.medYellow,
            height: 3,
          },
        }}
      >
        <TopTab.Screen
          name="Transactions"
          component={screens.AccountClientsTransactionsList}
        />
        <TopTab.Screen
          name="Balances"
          component={screens.AccountClientsBalancesList}
          listeners={{
            tabPress: () => {
              track('view_balances');
            },
          }}
        />
      </TopTab.Navigator>
    );
  }
}

const mapStateToProps = (state) => ({
  selectedClient: state.clients.selectedClient,
  trainerActiveProfile: state.trainerActiveProfile,
});

export default connect(mapStateToProps, {
  getClientBalance,
  getClientTransactions,
})(AccountClientsTransactionsAndBalances);
