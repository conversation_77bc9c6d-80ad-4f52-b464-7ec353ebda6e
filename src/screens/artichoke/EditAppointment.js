import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Alert,
  View,
  ActivityIndicator,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import { colors } from '../../styles';
import EditBookingForm from '../../components/artichoke/screensComponents/booked/EditBookingForm';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import {
  setNewAppointmentValuesAction,
  updateAppointmentAction,
  clearNewAppointment,
  clearSelectedAppointment,
} from '../../actions/artichoke/Appointments.actions';
import { getAvailableLocationsAction } from '../../actions/artichoke/Locations.actions';
import { APPLICATION_ROUTES, FREQUENCY_SHORT } from '../../constants';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import HeaderRightButton from '../../components/HeaderRightButton';
import LoadingSpinner from '../../components/LoadingSpinner';

const repeatActionSheetRef = createRef();

class EditAppointment extends Component {
  constructor(props) {
    super(props);
    this.props.getAvailableLocationsAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
      onUpdateSession: this.onUpdateSession,
    });
  }

  componentDidMount() {
    if (this.props.selectedAppointment) {
      const service = this.props.newAppointment?.service;
      service.maxParticipants = this.props.selectedAppointment?.maxParticipantsCount;
      this.props.navigation.setOptions({
        title: this.changePageTitle(this.props.selectedAppointment),
        headerLeft: this.renderHeaderLeft,
        headerRight: this.renderHeaderRight,
      });
    }
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.updateAppointmentLoading !== this.props.updateAppointmentLoading
    ) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
    this.mergeWorkHours(this.props.newAppointment.service).then(() => {
      this.preselectAppointmentLocation(this.props.newAppointment);
    });
  }

  onChangeRepeatType = (repeat) => {
    if (repeat) {
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.RECURRENCE_SCREEN,
        params: {
          returnRoute: APPLICATION_ROUTES.EDIT_APPOINTMENT,
        },
      });
    } else {
      this.onDoesNotRepeat();
    }
  };

  onDoesNotRepeat = () => {
    this.props.setNewAppointmentValuesAction({
      key: 'repeatIntervalType',
      value: null,
    });
    this.props.setNewAppointmentValuesAction({ key: 'untilDay', value: null });
    this.props.setNewAppointmentValuesAction({ key: 'count', value: 0 });
    this.props.setNewAppointmentValuesAction({
      key: 'repeatUntilType',
      value: null,
    });
  };

  onUpdateSession = () => {
    if (!this.props.newAppointment?.service) {
      Alert.alert('Please select a service first', '', [
        { text: 'OK', style: 'cancel' },
      ]);
    } else if (!this.props.newAppointment?.clients.length) {
      Alert.alert('Please select a client first', '', [
        { text: 'OK', style: 'cancel' },
      ]);
    } else if (
      !this.props.newAppointment?.appointmentLocation
      && !(
        this.props.newAppointment?.remotely.toString() === 'true'
        || this.props.newAppointment?.clientAddressEnable.toString() === 'true'
        || this.props.newAppointment?.inapp.toString() === 'true'
      )
    ) {
      Alert.alert('Please select a location first', '', [
        { text: 'OK', style: 'cancel' },
      ]);
    } else if (
      !this.props.newAppointment?.date
      || !this.props.newAppointment?.time
    ) {
      Alert.alert('Please select a date and time first', '', [
        { text: 'OK', style: 'cancel' },
      ]);
    } else {
      this.saveAppointment();
    }
  };

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'View Appointment',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  mergeWorkHours = async (service) => {
    const items = service.selfBookingAddresses;
    const savedSelfBookingAddresses = this.props.selfBookings?.selfBookingAddresses;
    if (items && items.length > 0) {
      items.forEach((obj1) => {
        if (savedSelfBookingAddresses && savedSelfBookingAddresses.length > 0) {
          savedSelfBookingAddresses.forEach((obj2) => {
            if (obj1.id === obj2.id) {
              obj1.workHours = obj2.workHours;
            }
          });
        }
      });
    }
  };

  preselectAppointmentLocation = (newAppointment) => {
    let item = {};
    if (!newAppointment.appointmentLocation) {
      if (newAppointment.clientAddressEnable.toString() === 'true') {
        item = {
          id: 0,
          addressName: "Client's location",
          checked: true,
          workHours: JSON.stringify(
            JSON.parse(this.props.selfBookings?.workHours).offsite,
          ),
        };
      } else if (newAppointment.remotely.toString() === 'true') {
        item = {
          id: 1,
          addressName: 'Remotely',
          checked: true,
          workHours: JSON.stringify(
            JSON.parse(this.props.selfBookings?.workHours).remote,
          ),
        };
      } else if (newAppointment.inapp.toString() === 'true') {
        item = {
          id: 2,
          addressName: 'Video Call',
          checked: true,
          workHours: JSON.stringify(
            JSON.parse(this.props.selfBookings?.workHours).inapp,
          ),
        };
      }
      this.props.setNewAppointmentValuesAction({
        key: 'appointmentLocation',
        value: item,
      });
    }
  };

  changePageTitle = (value) => {
    let title = '';
    if (value.isScheduled) {
      title = `Class - ${value.description}`;
    } else if (value && value.Invitee.length === 1) {
      title = `${value.Invitee[0].Client.user.firstName} ${value.Invitee[0].Client.user.lastName} - ${value.description}`;
    } else {
      title = `Group Session - ${value.description}`;
    }
    return title.length > 25 ? `${title.slice(0, 25)}...` : title;
  };

  goBack = () => {
    if (this.props.newAppointment?.isEdit) {
      Alert.alert(
        'Discard Changes',
        'Are you sure want to discard all changes?',
        [
          {
            text: 'YES',
            onPress: () => this.props.navigation.pop(2),
          },
          { text: 'NO' },
        ],
        { cancelable: false },
      );
    } else {
      this.props.navigation.goBack();
    }
  };

  saveAppointment = () => {
    if (
      this.props.selectedAppointment.Repeat
      && this.props.selectedAppointment.Repeat.RepeatInterval.RepeatIntervalType
    ) {
      Alert.alert(
        'Update Series',
        '',
        [
          {
            text: 'Update This Event Only',
            onPress: () => {
              this.props.updateAppointmentAction({ allInSeries: false });
            },
          },
          {
            text: 'Update All Future Events',
            onPress: () => {
              this.props.updateAppointmentAction({ allInSeries: true });
            },
          },
          {
            text: 'Cancel',
          },
        ],
        { cancelable: true },
      );
    } else {
      this.props.updateAppointmentAction(null);
    }
  };

  renderHeaderLeft = () => <HeaderLeftButton onPress={() => this.goBack()} />;

  renderHeaderRight = () => {
    if (this.props.updateAppointmentLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.onUpdateSession()}
        title="Save"
        titleStyle={{ ...generalStyles.navigationButtonText }}
      />
    );
  };

  render() {
    const repeatTypeShort = this.props.selectedAppointment?.Repeat
      && this.props.newAppointment?.repeatIntervalType
      ? 'Custom'
      : null;
    return this.props.locationsLoading ? (
      <LoadingSpinner
        visible
        size="large"
        title="LOADING..."
        backgroundColor={colors.white}
      />
    ) : (
      <SafeAreaView style={styles.container}>
        <EditBookingForm
          repeatActionSheetRef={repeatActionSheetRef}
          initialValues={this.props.newAppointment}
          setNewAppointmentValuesAction={
            this.props.setNewAppointmentValuesAction
          }
          // onSaveAppointment={this.saveAppointment}
          navigation={this.props.navigation}
          symbol={this.props.symbol}
        />

        <CustomActionSheet actionSheetRef={repeatActionSheetRef} title="Repeat">
          <RadioForm>
            {FREQUENCY_SHORT.map((obj, i) => (
              <RadioButton
                labelHorizontal
                key={i}
                style={{
                  paddingHorizontal: 30,
                  paddingVertical: 20,
                  flex: 1,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.lightgrey,
                }}
              >
                {/*  You can set RadioButtonLabel before RadioButtonInput */}
                <RadioButtonInput
                  obj={obj}
                  index={i}
                  isSelected={repeatTypeShort === obj.value}
                  onPress={() => {
                    this.onChangeRepeatType(obj.value);
                    repeatActionSheetRef.current?.setModalVisible(false);
                  }}
                  borderWidth={1}
                  buttonSize={10}
                  buttonOuterSize={20}
                  buttonStyle={{ borderColor: colors.subGrey }}
                  buttonWrapStyle={{ marginLeft: 73 }}
                />
                <RadioButtonLabel
                  obj={obj}
                  index={i}
                  labelHorizontal
                  onPress={() => {
                    this.onChangeRepeatType(obj.value);
                    repeatActionSheetRef.current?.setModalVisible(false);
                  }}
                  labelStyle={generalStyles.fontBold}
                  labelWrapStyle={{}}
                />
              </RadioButton>
            ))}
          </RadioForm>
        </CustomActionSheet>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newAppointment: state.appointments.newAppointmentValues,
  selectedAppointment: state.appointments.selectedAppointment,
  selfBookings: state.locations.selfBookings,
  updateAppointmentLoading: state.loadingComponents.updateAppointmentLoading,
  symbol: state.user.accountSettings?.symbol,
  locationsLoading: state.loadingComponents.locationsLoading,
});

export default connect(mapStateToProps, {
  setNewAppointmentValuesAction,
  updateAppointmentAction,
  clearNewAppointment,
  clearSelectedAppointment,
  getAvailableLocationsAction,
})(EditAppointment);
