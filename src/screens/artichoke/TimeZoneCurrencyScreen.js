import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  ScrollView,
  View,
  TouchableOpacity,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import moment from 'moment';
import { colors, shadow } from '../../styles';
import { APPLICATION_ROUTES, CURRENCIES } from '../../constants';
import {
  getAccountSettingsAction,
  setUpdatedAccountSettingsAction,
  saveAccountSettingsAction,
} from '../../actions/artichoke/User.actions';
import { HeaderLeftButton, LoadingSpinner } from '../../components';
import { generalStyles } from '../../styles/generalStyle';
import SettingsItem from '../../components/artichoke/common/SettingsItem';

class TimeZoneCurrencyScreen extends Component {
  static navigationOptions = ({ navigation, route }) => ({
    title: APPLICATION_ROUTES.TIMEZONE_CURRENCY,
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
    this.props.getAccountSettingsAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  onCurrencyChange = (item) => {
    this.props.setUpdatedAccountSettingsAction({
      key: 'newCurrency',
      value: item,
    });
    this.props.saveAccountSettingsAction();
  };

  goBack = () => {
    this.props.navigation.navigate('BottomTab', { screen: 'Account' });
  };

  hasNotch = () => {
    const hasNotch = DeviceInfo.hasNotch();
    hasNotch ? this.setState({ hasNotch }) : null;
  };

  render() {
    return this.props.account && !this.props.accountDataUpdateLoading ? (
      <View style={styles.container}>
        <Text style={styles.textLabel}>Timezone</Text>
        <SettingsItem
          value={this.props.account.creator.TimeZone.name}
          onPressFunc={() => this.props.navigation.navigate(
            APPLICATION_ROUTES.TIMEZONE_SELECTION,
          )}
        />
        <Text style={styles.textLabel}>Currency</Text>
        <SettingsItem
          value={
            CURRENCIES.find((item) => item.value === this.props.account.currency).label
          }
          onPressFunc={() => this.props.navigation.navigate(
            APPLICATION_ROUTES.CURRENCY_SELECTION,
          )}
        />
      </View>
    ) : (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  textLabel: {
    ...generalStyles.avenirRoman22,
    marginVertical: 24,
    marginHorizontal: 20,
    color: colors.black,
  },
});

const mapStateToProps = (state) => ({
  account: state.user.accountSettings,
  accountDataUpdateLoading: state.loadingComponents.accountDataUpdateLoading,
});

export default connect(mapStateToProps, {
  getAccountSettingsAction,
  setUpdatedAccountSettingsAction,
  saveAccountSettingsAction,
})(TimeZoneCurrencyScreen);
