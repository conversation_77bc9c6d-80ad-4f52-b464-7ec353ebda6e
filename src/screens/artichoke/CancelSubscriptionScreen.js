import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
} from 'react-native';
import { colors } from '../../styles';
import { cancelSubscriptionAction } from '../../actions/artichoke/Clients.actions';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import FormMultilineTextInput from '../../components/artichoke/common/FormMultilineTextInput';
import FormSubmitButtonWithSpinner from '../../components/artichoke/common/FormSubmitButtonWithSpinner';

const bgImage = require('../../assets/imgServiceBackground.png');

class CancelSubscriptionScreen extends Component {
  static navigationOptions = ({ route }) => ({
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  goBack = () => {
    this.props.navigation.goBack();
  };

  renderPackage = () => (
    <View>
      <View>
        <Text style={styles.label}>Reason for Cancellation (Optional)</Text>
      </View>
      <View>
        <FormMultilineTextInput
          style={{ color: colors.black }}
          onChangeText={(value) => {
            console.log('Reason for cancellation', value);
          }}
          placeholder="Description"
          name="description"
          maxLength={360}
        />
      </View>
    </View>
  );

  render() {
    const productName = this.props.selectedTransaction.clientPurchase.pack.name;

    return (
      <SafeAreaView style={styles.container}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <View style={styles.contentUser}>
              <Image
                source={
                  this.props.selectedClient?.user.avatarUrl
                    ? { uri: this.props.selectedClient?.user.avatarUrl }
                    : bgImage
                }
                style={styles.userImage}
              />
              <View style={styles.userDetails}>
                <Text style={styles.userName}>
                  {this.props.selectedClient?.user.firstName}
                  {' '}
                  {this.props.selectedClient?.user.lastName}
                </Text>
                <Text style={styles.userJob}>{productName}</Text>
              </View>
              <View style={styles.userSubscriptionDetails}>
                <Text style={styles.userName}>
                  {this.props.selectedTransaction.clientPurchase.amountPaid}
                </Text>
                <Text style={styles.userJob}>
                  {
                    this.props.selectedTransaction.clientPurchase.pack
                      .paymentInterval
                  }
                </Text>
              </View>
            </View>
            {this.renderPackage()}
          </View>
        </ScrollView>
        <View style={styles.bottom}>
          <View style={styles.frameBottom}>
            <View style={styles.frameSummary}>
              <Text style={styles.labelSummary}>Cancel:</Text>
              <Text style={styles.valueSummary}>
                {this.props.selectedTransaction.clientPurchase.pack.name}
              </Text>
            </View>

            <View style={styles.bottomSubmitButton}>
              <FormSubmitButtonWithSpinner
                style={{ width: '100%', marginHorizontal: 0, marginTop: 15 }}
                title="Confirm"
                onPressButton={() => {
                  this.props.cancelSubscriptionAction();
                }}
                isLoading={this.props.cancelingSubscriptionLoading}
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottom: {
    flexDirection: 'row',
    flex: 2,
    justifyContent: 'flex-end',
    marginBottom: 38,
    marginHorizontal: 20,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    marginTop: 20,
    marginBottom: 20,
    paddingHorizontal: 20,
    width: '100%',
  },
  userImage: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userDetails: {
    flex: 6,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  userSubscriptionDetails: {
    flex: 6,
    alignItems: 'flex-end',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    marginTop: 5,
    marginLeft: 10,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    lineHeight: 22,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  label: {
    ...generalStyles.avenirHeavy17,
    paddingTop: 24,
    marginBottom: 7,
  },
  frameBottom: {
    display: 'flex',
    width: '100%',
    flexDirection: 'column',
    borderTopWidth: 0.5,
    paddingTop: 20,
    paddingBottom: 20,
    borderTopColor: colors.lightGrey,
  },
  labelSummary: {
    textAlign: 'left',
    ...generalStyles.avenirHeavy17,
    flex: 4,
    color: colors.black,
    borderWidth: 0,
  },
  valueSummary: {
    textAlign: 'right',
    ...generalStyles.avenirHeavy17,
    flex: 5,
    color: colors.black,
    borderWidth: 0,
  },
  frameSummary: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const mapStateToProps = (state) => ({
  selectedTransaction: state.clients.selectedClientTransaction,
  selectedClient: state.clients.selectedClient,
  cancelingSubscriptionLoading:
    state.loadingComponents.cancelingSubscriptionLoading,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  cancelSubscriptionAction,
})(CancelSubscriptionScreen);
