import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, FlatList, StyleSheet, Text, View,
} from 'react-native';
import moment from 'moment';
import { HeaderSearchBar } from '../../components';
import EmptyBookedList from '../../components/artichoke/screensComponents/booked/EmptyBookedList';
import ClientBookingsByDate from '../../components/artichoke/screensComponents/client/bookings/ClientBookingsByDate';
import {
  getClientBookingsAction,
  setFilteredClientBookingsAction,
  getAppointmentAction,
} from '../../actions/artichoke/Appointments.actions';
import { APPLICATION_ROUTES } from '../../constants';
import { generalStyles } from '../../styles/generalStyle';
import { getSelfBookingAction } from '../../actions/artichoke/Locations.actions';
import LoadingSpinner from '../../components/LoadingSpinner';

class ListClientsBookingsScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: '',
      date: moment(new Date()).format('YYYY-MM-DD'),
    };
    this.props.getSelfBookingAction();
    this.props.getClientBookingsAction(this.props?.selectedClient?.id);
    this.props.setFilteredClientBookingsAction([]);
  }

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = {};
    Object.keys(this.props.bookingsListByClient).map((date) => {
      const filteredClientBookings = this.props.bookingsListByClient[
        date
      ].filter((item) => {
        const itemDataByServiceName = `${item.description.toUpperCase()}`;
        let itemDataByLocationName = `${item?.onsiteAddress?.address1?.toUpperCase()}`;
        if (item.remotely === 'true') {
          itemDataByLocationName = 'REMOTELY';
        } else if (item.inapp === 'true') {
          itemDataByLocationName = 'VIDEO CALL';
        } else if (item.clientAddressEnable.toString() === 'true') {
          itemDataByLocationName = 'CLIENT ADDRESS';
        }
        const textData = text.toUpperCase();
        if (itemDataByServiceName.indexOf(textData) > -1) {
          return itemDataByServiceName;
        }
        if (itemDataByLocationName.indexOf(textData) > -1) {
          return itemDataByLocationName;
        }
        return null;
      });
      if (filteredClientBookings.length) {
        newData[date] = filteredClientBookings;
      }
      return null;
    });
    this.props.setFilteredClientBookingsAction(newData);
  };

  checkInvitee = (item) => {
    let isInvitee = false;
    if (
      item.Invitee?.filter((e) => e.Client.id === this.props.selectedClient?.id)
        .length > 0
    ) {
      isInvitee = true;
    }
    return isInvitee;
  };

  onSelectAppointment = (appointment) => {
    this.props.getAppointmentAction(appointment.id);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.VIEW_CLIENT_BOOKING,
    });
  };

  renderPageHeader = (newUpcomingDates, newPreviousDates) => {
    if (
      Object.keys(newPreviousDates).length === 0
      && Object.keys(newUpcomingDates).length === 0
    ) {
      return (
        <EmptyBookedList message="There are no upcoming or previous bookings." />
      );
    }
    return (
      <View>
        <View style={styles.headerList}>
          <Text style={styles.label}>Upcoming Bookings</Text>
        </View>
        <FlatList
          data={Object.keys(newUpcomingDates).sort()}
          renderItem={({ item, index }) => (
            <ClientBookingsByDate
              date={item}
              index={index}
              refreshing={this.props.appointmentsListLoading}
              appointments={newUpcomingDates[item]}
              onPressItem={this.onSelectAppointment}
            />
          )}
          keyExtractor={(item) => `booked-list-past-item-${item}`}
          refreshing={this.props.appointmentsListLoading}
          ListEmptyComponent={() => (
            <EmptyBookedList message="There are no upcoming bookings." />
          )}
        />
        <View style={styles.headerList}>
          <Text style={styles.label}>Previous Bookings</Text>
        </View>
      </View>
    );
  };

  getListData = () => {
    const filteredBookings = {};
    const newPreviousDates = {};

    Object.keys(this.props.bookingsListByClient).map((date) => {
      const filteredClientBookingsInitial = this.props.bookingsListByClient[
        date
      ].filter((item) => {
        if (
          (item.isScheduled && this.checkInvitee(item))
          || !item.isScheduled
        ) {
          return item;
        }
        return null;
      });
      if (filteredClientBookingsInitial.length) {
        filteredBookings[date] = filteredClientBookingsInitial;
      }
      return null;
    });

    const previousDates = JSON.parse(
      JSON.stringify(this.props.filteredBookingsByClient),
    );
    Object.keys(previousDates).forEach((key) => {
      if (moment(key) >= moment(this.state.date)) {
        delete previousDates[key];
      }
      return previousDates;
    });

    Object.keys(previousDates).map((date) => {
      const filteredClientBookings = previousDates[date].filter((item) => {
        if (
          (item.isScheduled && this.checkInvitee(item))
          || !item.isScheduled
        ) {
          return item;
        }
        return null;
      });
      if (filteredClientBookings.length) {
        newPreviousDates[date] = filteredClientBookings;
      }
      return null;
    });

    const upcomingDates = JSON.parse(
      JSON.stringify(this.props.filteredBookingsByClient),
    );
    Object.keys(upcomingDates).forEach((key) => {
      if (moment(key) < moment(this.state.date)) {
        delete upcomingDates[key];
      }
      return upcomingDates;
    });

    const newUpcomingDates = {};
    Object.keys(upcomingDates).map((date) => {
      const filteredClientBookings = upcomingDates[date].filter(
        (item) => (item.isScheduled && this.checkInvitee(item)) || !item.isScheduled,
      );
      if (filteredClientBookings.length) {
        newUpcomingDates[date] = filteredClientBookings;
      }
      return null;
    });

    return {
      newUpcomingDates,
      newPreviousDates,
    };
  };

  render() {
    const { newUpcomingDates, newPreviousDates } = this.getListData();
    return this.props.getClientBookingsLoading ? (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    ) : (
      <SafeAreaView style={styles.container}>
        <View style={styles.scrollViewContainer}>
          {Object.keys(newPreviousDates).length === 0
          && Object.keys(newUpcomingDates).length === 0
          && !this.state.value ? null : (
            <HeaderSearchBar
              searchText={this.state.value}
              onChangeText={(text) => this.searchFilterFunction(text)}
              clearable
              paddingTop={10}
              light
              shadow={false}
              placeholder="Booking, Location"
            />
            )}
          <FlatList
            data={Object.keys(newPreviousDates).sort().reverse()}
            renderItem={({ item, index }) => (
              <ClientBookingsByDate
                date={item}
                index={index}
                refreshing={this.props.appointmentsListLoading}
                appointments={newPreviousDates[item]}
                onPressItem={this.onSelectAppointment}
              />
            )}
            keyExtractor={(item) => `booked-list-past-item-${item}`}
            refreshing={this.props.appointmentsListLoading}
            ListEmptyComponent={() => (Object.keys(newPreviousDates).length === 0
              && Object.keys(newUpcomingDates).length === 0 ? null : (
                <EmptyBookedList message="There are no previous bookings." />
              ))}
            ListHeaderComponent={() => this.renderPageHeader(newUpcomingDates, newPreviousDates)}
          />
        </View>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollViewContainer: {
    flex: 1,
  },
  label: {
    fontWeight: 'bold',
    ...generalStyles.avenirBlack17,
    width: '100%',
  },
  headerList: {
    height: 64,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 21,
  },
});

const mapStateToProps = (state) => ({
  bookingsListByClient: state.appointments.bookingsListByClient,
  filteredBookingsByClient: state.appointments.filteredBookingsByClient,
  selectedClient: state.clients.selectedClient,
  getClientBookingsLoading: state.loadingComponents.getClientBookingsLoading,
});

const mapDispatchToProps = {
  setFilteredClientBookingsAction,
  getAppointmentAction,
  getClientBookingsAction,
  getSelfBookingAction,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ListClientsBookingsScreen);
