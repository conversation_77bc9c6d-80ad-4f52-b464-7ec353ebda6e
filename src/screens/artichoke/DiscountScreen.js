/* eslint-disable react/no-did-update-set-state */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, FlatList, StyleSheet, View,
} from 'react-native';
import { colors } from '../../styles';
import {
  clearSelectedDiscountForEditValuesAction,
  getDiscountsAction,
  setPurchaseAction,
  setSelectedDiscountForEditValuesAction,
} from '../../actions/artichoke/Clients.actions';
import DiscountListItem from '../../components/artichoke/screensComponents/booked/DiscountListItem';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import { APPLICATION_ROUTES } from '../../constants';
import AddButton from '../../components/artichoke/common/AddButton';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import LoadingSpinner from '../../components/LoadingSpinner';
import HeaderRightButton from '../../components/HeaderRightButton';

class DiscountScreen extends Component {
  static navigationOptions = ({ route, navigation }) => ({
    title: 'Select a discount',
    headerLeft: () => <HeaderLeftButton onPress={() => navigation?.goBack()} />,
    headerBackTitle: 'Back',
    headerRight: () => (
      <HeaderRightButton
        onPress={() => {
          if (route?.params && route.params.setPurchaseDiscount) {
            route.params?.setPurchaseDiscount();
          }
        }}
        title="Done"
        titleStyle={{
          color: colors.white,
        }}
      />
    ),
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      selectedDiscount: this.props.purchase.discount,
    };
  }

  componentDidMount() {
    this.props.getDiscountsAction();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.navigation.setParams({
        setPurchaseDiscount: this.setPurchaseDiscount,
      });
    });
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.selectedDiscount) {
      const discount = this.props.discountsList.filter(
        (disc) => disc.id === prevState.selectedDiscount.id,
      );
      if (
        discount.length
        && (prevState.selectedDiscount.amount !== discount[0].amount
          || prevState.selectedDiscount.type !== discount[0].type)
      ) {
        this.setState({ selectedDiscount: discount[0] });
      } else if (discount.length === 0) {
        this.setState({ selectedDiscount: null });
      }
    }
    if (prevProps.purchase.discount?.id !== this.props.purchase.discount?.id) {
      this.setState({ selectedDiscount: this.props.purchase.discount });
    }
  }

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
  }

  goBack = () => {
    this.props.navigation.goBack();
  };

  setPurchaseDiscount = () => {
    if (this.state.selectedDiscount && this.state.selectedDiscount.id === 0) {
      this.props.setPurchaseAction({ key: 'discount', value: null });
    } else {
      this.props.setPurchaseAction({
        key: 'discount',
        value: this.state.selectedDiscount,
      });
    }
    this.goBack();
  };

  renderAddDiscountButton = () => (
    <AddButton
      onPressAction={() => {
        this.props.clearSelectedDiscountForEditValuesAction();
        this.props.navigation.navigate({
          name: APPLICATION_ROUTES.EDIT_DISCOUNTS,
          params: {
            discountId: null,
          },
        });
      }}
    />
  );

  onSelectItem = (item) => {
    this.setState({ selectedDiscount: item });
  };

  onUnselectItem = () => {
    this.setState({ selectedDiscount: {} });
  };

  checkIsSelected = (e) => (this.state.selectedDiscount && this.state.selectedDiscount.id === e.id)
    || (e.id === 0 && !this.state.selectedDiscount);

  onEditDiscount = (discountItem) => {
    this.props.setSelectedDiscountForEditValuesAction({
      key: 'name',
      value: discountItem.name,
    });
    this.props.setSelectedDiscountForEditValuesAction({
      key: 'amount',
      value: discountItem.amount,
    });
    this.props.setSelectedDiscountForEditValuesAction({
      key: 'type',
      value: discountItem.type,
    });
    this.props.setSelectedDiscountForEditValuesAction({
      key: 'accountId',
      value: discountItem.accountId,
    });
    this.props.setSelectedDiscountForEditValuesAction({
      key: 'id',
      value: discountItem.id,
    });
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.EDIT_DISCOUNTS,
      params: {
        discountId: discountItem.id,
      },
    });
  };

  render() {
    return this.props.discountLoader ? (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    ) : (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.discountsList}
          renderItem={({ item }) => (
            <DiscountListItem
              item={item}
              selectItem={this.onSelectItem}
              unselectItem={this.onUnselectItem}
              isSelected={this.checkIsSelected(item)}
              onPress={this.onEditDiscount}
              symbol={this.props.symbol}
            />
          )}
          keyExtractor={(item) => item.id}
          refreshing={this.props.clientListLoading}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Discount Found" />
          )}
        />
        <View style={styles.footerList}>{this.renderAddDiscountButton()}</View>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  footerList: {
    flex: 1,
    position: 'absolute', // Here is the trick
    bottom: 20, // Here is the trick
    right: 10,
    height: 70,
    flexGrow: 1,
  },
});

const mapStateToProps = (state) => ({
  discountsList: state.clients.discountsList,
  purchase: state.clients.clientsPurchase,
  symbol: state.user.accountSettings?.symbol,
  discountLoader: state.loadingComponents.discountLoader,
});

export default connect(mapStateToProps, {
  getDiscountsAction,
  setPurchaseAction,
  setSelectedDiscountForEditValuesAction,
  clearSelectedDiscountForEditValuesAction,
})(DiscountScreen);
