import moment from 'moment';
import React, { Component } from 'react';
import {
  FlatList, StyleSheet, View, ActivityIndicator,
} from 'react-native';
import { connect } from 'react-redux';
import {
  getActiveClientsAction,
  getClientCreditCard,
  setFilteredClientTransactions,
  setSelectedClient,
  setSelectedClientTransactionAction,
} from '../../actions/artichoke/Clients.actions';
import {
  getSalesTaxAction,
  getTransactionHistoryAction,
  setPeriodHistoryAction,
} from '../../actions/artichoke/Sales.actions';
import IncomeDetails from '../../components/artichoke/screensComponents/sales/IncomeDetails';
import IncomeTransactionItem from '../../components/artichoke/screensComponents/sales/IncomeTransactionItem';
import EmptyServiceList from '../../components/artichoke/screensComponents/services/EmptyServiceList';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { APPLICATION_ROUTES, SALES_PERIODS } from '../../constants';
import { colors } from '../../styles';

const noTransactionIcon = require('../../assets/imgAddChat.png');

const dateFormat = 'MM-DD-YYYY';

class IncomeSalesScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      transactions: null,
    };
  }

  componentDidMount() {
    const startOfMonth = moment().startOf('month').format('MM-DD-YYYY');
    const endOfMonth = moment().format('MM-DD-YYYY');
    this.props.setPeriodHistoryAction(SALES_PERIODS[0].value);

    this.props.getSalesTaxAction({
      startDate: startOfMonth,
      endDate: endOfMonth,
    });
    this.getTransactionsForSelectedPeriod(
      SALES_PERIODS[0].value,
      startOfMonth,
      endOfMonth,
    );
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.getActiveClientsAction();
    });
  }

  componentDidUpdate(prevProps) {
    if (prevProps.transactions !== this.props.transactions) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({
        transactions: this.props.transactions,
      });
    }
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onSelectTransaction = (item) => {
    const selectedClient = this.props.clientsList.filter(
      // eslint-disable-next-line eqeqeq
      (client) => client.id == item.purchase.clientId,
    );
    if (selectedClient?.length) {
      this.props.setSelectedClient(selectedClient[0]);
      this.props.getClientCreditCard(selectedClient[0].id);
    }
    const refundOrTransactionDetails = item.purchase.refunded === true
      ? APPLICATION_ROUTES.REFUND_DETAILS
      : APPLICATION_ROUTES.TRANSACTION_DETAIL;
    const redirectRoute = item.purchase
      ? refundOrTransactionDetails
      : APPLICATION_ROUTES.CHECKIN_DETAILS;
    this.props.setSelectedClientTransactionAction(item);
    this.props.navigation.navigate(redirectRoute);
  };

  getTransactionsForSelectedPeriod = (period, startOfMonth, endOfMonth) => {
    this.setState({
      transactions: null,
    });
    let days = '365';
    const startDate = moment(startOfMonth, dateFormat).startOf('day').format();
    const endDate = moment(endOfMonth, dateFormat).endOf('day').format();
    switch (period) {
      case SALES_PERIODS[0].value:
        days = '30';
        break;
      case SALES_PERIODS[1].value:
        days = '90';
        break;
      default:
        days = '365';
        break;
    }
    this.props.getTransactionHistoryAction({ days, startDate, endDate });
  };

  static navigationOptions = ({ navigation }) => ({
    title: 'Income',
    headerLeft: () => <HeaderLeftButton onPress={() => navigation?.goBack()} />,
    headerBackTitle: 'Back',
  });

  render() {
    return (
      <View style={styles.container}>
        <FlatList
          data={this.props.transactions}
          renderItem={({ item }) => (
            <IncomeTransactionItem
              item={item}
              onPressItem={() => this.onSelectTransaction(item)}
              symbol={this.props.symbol}
            />
          )}
          keyExtractor={(item, index) => index.toString()}
          ListEmptyComponent={() => (
            <EmptyServiceList
              message="No Transactions Found"
              hasIcon={noTransactionIcon}
            />
          )}
          ListHeaderComponent={() => (
            <View style={styles.indicatorStyle}>
              {!this.state.transactions ? (
                <ActivityIndicator size="large" color={colors.subGrey} />
              ) : null}
              <IncomeDetails
                onSelectPeriod={(period, startDate, endDate) => this.getTransactionsForSelectedPeriod(
                  period,
                  startDate,
                  endDate,
                )}
              />
            </View>
          )}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  indicatorStyle: {
    marginTop: 20,
  },
});

const mapStateToProps = (state) => ({
  transactions: state.sales.transactions,
  clientsList: state.clients.clientsList,
  symbol: state.user.accountSettings?.symbol,
});

const mapDispatchToProps = {
  setSelectedClientTransactionAction,
  setFilteredClientTransactions,
  setSelectedClient,
  getClientCreditCard,
  getSalesTaxAction,
  getTransactionHistoryAction,
  getActiveClientsAction,
  setPeriodHistoryAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(IncomeSalesScreen);
