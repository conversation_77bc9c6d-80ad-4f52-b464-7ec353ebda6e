import React, { Component, createRef } from 'react';
import { StyleSheet, SafeAreaView, Alert } from 'react-native';
import { connect } from 'react-redux';
import { colors } from '../../styles';
import {
  setNewLocationValuesAction,
  saveNewAddressAction,
} from '../../actions/artichoke/Locations.actions';
import CreateLocationForm from '../../components/artichoke/screensComponents/locations/CreateLocationForm';
import { APPLICATION_ROUTES } from '../../constants';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { track } from '../../util/Analytics';
import HeaderRightButton from '../../components/HeaderRightButton';

const formRef = createRef();

class CreateMyLocationScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Create Location',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
    headerRight: () => (
      <HeaderRightButton
        onPress={() => route.params.onSaveAddress()}
        title="Save"
        titleStyle={{
          color: colors.white,
          margin: 15,
        }}
      />
    ),
  });

  constructor(props) {
    super(props);
    this.props.navigation.setParams({
      goBack: this.goBack,
      onSaveAddress: this.onSaveAddress,
    });
  }

  onSaveAddress = () => {
    formRef.current.handleSubmit();
    track('location_created');
  };

  onSubmitAddress = () => {
    if (this.props.location.startTimes.length === 0) {
      Alert.alert(
        'No start slot selected',
        'Please select at least one start slot for current location',
      );
    } else if (this.props.location.workHours.indexOf('null') !== -1) {
      Alert.alert('Invalid Workhours', 'Please check the workdays');
    } else {
      this.props.saveNewAddressAction();
      if (this.props.route?.params?.previousScreen === 'create-service') {
        this.props.navigation.navigate(APPLICATION_ROUTES.LOCATIONS, {
          previousScreen: 'create-service',
        });
      } else {
        this.props.navigation.navigate(APPLICATION_ROUTES.HOURS_LOCATIONS);
      }
    }
  };

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <CreateLocationForm
          refForm={formRef}
          initialValues={this.props.location}
          onSubmitForm={this.onSubmitAddress}
          setSelectedLocationForEditValuesAction={
            this.props.setNewLocationValuesAction
          }
        />
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
const mapStateToProps = (state) => ({
  location: state.locations.newLocationAddress,
});
export default connect(mapStateToProps, {
  setNewLocationValuesAction,
  saveNewAddressAction,
})(CreateMyLocationScreen);
