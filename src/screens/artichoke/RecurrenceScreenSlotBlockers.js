import React, { Component } from 'react';
import { SafeAreaView, StyleSheet, View } from 'react-native';
import moment from 'moment';
import { connect } from 'react-redux';
import CreateRecurrenceForm from '../../components/artichoke/screensComponents/booked/CreateRecurrenceForm';
import { setNewSlotBlockerValuesAction } from '../../actions/artichoke/Appointments.actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { generalStyles } from '../../styles/generalStyle';
import HeaderRightButton from '../../components/HeaderRightButton';

class RecurrenceScreenSlotBlocker extends Component {
  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Repeat',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      repeatIntervalType: this.props.newSlotBlocker.repeatIntervalType,
      untilDay:
        this.props.newSlotBlocker.untilDay || moment().format('MM/DD/YYYY'),
      count: this.props.newSlotBlocker.count,
      repeatUntilType: this.props.newSlotBlocker.repeatUntilType || 'DAY',
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
      onSaveRecurrence: this.onSaveRecurrence,
    });
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
  }

  onChangeRepeatCount = (item) => {
    this.setState({
      count: item,
    });
  };

  onChangeRepeatIntervalType = (item) => {
    this.setState({
      repeatIntervalType: item,
    });
  };

  onChangeRepeatUntilDate = (item) => {
    this.setState({
      untilDay: item,
    });
  };

  onChangeRepeatUntilType = (item) => {
    this.setState({
      repeatUntilType: item,
    });
  };

  onSaveRecurrence = () => {
    this.props.setNewSlotBlockerValuesAction({
      key: 'repeatIntervalType',
      value: this.state.repeatIntervalType,
    });
    this.props.setNewSlotBlockerValuesAction({
      key: 'untilDay',
      value: this.state.untilDay ? this.state.untilDay.toString() : null,
    });
    this.props.setNewSlotBlockerValuesAction({
      key: 'count',
      value: this.state.count,
    });
    this.props.setNewSlotBlockerValuesAction({
      key: 'repeatUntilType',
      value: this.state.repeatUntilType,
    });
    this.props.navigation.navigate({
      name: this.props.route.params.returnRoute,
      merge: true,
    });
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.onSaveRecurrence()}
      title="Done"
      titleStyle={{ ...generalStyles.navigationButtonText }}
    />
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <CreateRecurrenceForm
          repeatIntervalType={this.state.repeatIntervalType}
          repeatUntilType={this.state.repeatUntilType}
          untilDay={this.state.untilDay}
          count={this.state.count}
          onChangeRepeatUntilType={this.onChangeRepeatUntilType}
          onChangeRepeatIntervalType={this.onChangeRepeatIntervalType}
          onChangeRepeatUntilDate={this.onChangeRepeatUntilDate}
          onChangeRepeatCount={this.onChangeRepeatCount}
        />
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newSlotBlocker: state.appointments.newSlotBlocker,
});

export default connect(mapStateToProps, {
  setNewSlotBlockerValuesAction,
})(RecurrenceScreenSlotBlocker);
