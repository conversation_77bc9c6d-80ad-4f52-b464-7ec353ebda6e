import React, { Component } from 'react';
import {
  View, Text, TouchableOpacity, Image, Linking,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import DeviceInfo from 'react-native-device-info';
import { connect } from 'react-redux';
import moment from 'moment';
import { APPLICATION_ROUTES } from '../../constants';
import { colors } from '../../styles';
import { generalStyles } from '../../styles/generalStyle';
import { createLogoutAction } from '../../actions/artichoke/Login.actions';
import {
  getSalesTaxAction,
  getTransactionHistoryAction,
} from '../../actions/artichoke/Sales.actions';
import { getActiveClientsAction } from '../../actions/artichoke/Clients.actions';

const stripeNotConnected = require('../../assets/stripeGrey.png');
const stripeConnected = require('../../assets/stripeGreen.png');

class AccountScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasNotch: false,
    };
  }

  componentDidMount() {
    this.hasNotch();
    const startOfMonth = moment().startOf('month').format('MM-DD-YYYY');
    const endOfMonth = moment().format('MM-DD-YYYY');

    this.props.getSalesTaxAction({
      startDate: startOfMonth,
      endDate: endOfMonth,
    });
    this.props.getActiveClientsAction();
    this.props.getTransactionHistoryAction();
  }

  handleClick = () => {
    Linking.canOpenURL('https://dashboard.stripe.com/').then((supported) => {
      if (supported) {
        Linking.openURL('https://dashboard.stripe.com/');
      }
    });
  };

  handleLogout = () => {
    this.props.createLogoutAction();
  };

  hasNotch = () => {
    const hasNotch = DeviceInfo.hasNotch();
    hasNotch ? this.setState({ hasNotch }) : null;
  };

  renderAlreadyConnectedToStripeItems = () => (
    <TouchableOpacity
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: colors.lightgrey,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightgrey,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onPress={this.handleClick}
    >
      <Text style={{ flex: 1 }}>Payments</Text>
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          justifyContent: 'flex-end',
          alignItems: 'center',
        }}
      >
        <Image
          source={stripeConnected}
          style={{
            width: 18,
            height: 19,
            alignSelf: 'center',
            marginVertical: 0,
          }}
        />
        <Text
          style={{ color: colors.goodGreen, ...generalStyles.avenirRoman14 }}
        >
          {' '}
          Connected
        </Text>
        <IconFeader
          style={{ textAlign: 'right', marginLeft: 10 }}
          name="chevron-right"
          size={25}
          color={colors.bordergrey}
        />
      </View>
    </TouchableOpacity>
  );

  renderIncomeAndTaxes = () => {
    const income = this.props.salesTax && this.props.salesTax.grossSales
      ? this.props.salesTax.grossSales
      : 0;
    return (
      <TouchableOpacity
        style={{
          display: 'flex',
          flexDirection: 'row',
          width: '100%',
          borderTopWidth: 1,
          borderTopColor: colors.lightgrey,
          borderBottomWidth: 1,
          borderBottomColor: colors.lightgrey,
          padding: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onPress={() => this.props.navigation.navigate(APPLICATION_ROUTES.INCOME)}
      >
        <Text style={{ flex: 1 }}>Income & Sales Tax</Text>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'flex-end',
            alignItems: 'center',
          }}
        >
          <IconFeader
            style={{ textAlign: 'right', marginLeft: 10 }}
            name="chevron-right"
            size={25}
            color={colors.bordergrey}
          />
        </View>
      </TouchableOpacity>
    );
  };

  renderLogout = () => (
    <TouchableOpacity
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: colors.lightgrey,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightgrey,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onPress={() => this.handleLogout()}
    >
      <Text style={{ flex: 1 }}>Logout</Text>
      <IconFeader
        style={{ flex: 0.5, textAlign: 'right' }}
        name="chevron-right"
        size={25}
        color={colors.bordergrey}
      />
    </TouchableOpacity>
  );

  renderMenuItems = (item) => (
    <TouchableOpacity
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: colors.lightgrey,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightgrey,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onPress={() => this.props.navigation.navigate(item)}
    >
      <Text style={{ flex: 1 }}>{item}</Text>
      <IconFeader
        style={{ flex: 0.5, textAlign: 'right' }}
        name="chevron-right"
        size={25}
        color={colors.bordergrey}
      />
    </TouchableOpacity>
  );

  renderStripeItems = () => (
    <TouchableOpacity
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: colors.lightgrey,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightgrey,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onPress={() => this.props.navigation.navigate(APPLICATION_ROUTES.CONNECT_WITH_STRIPE)}
    >
      <Text style={{ flex: 0.5 }}>Payments</Text>
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          justifyContent: 'flex-end',
          alignItems: 'center',
        }}
      >
        <Image
          source={stripeNotConnected}
          style={{
            width: 18,
            height: 19,
            alignSelf: 'center',
            marginVertical: 0,
          }}
        />
        <Text
          style={{ color: colors.subGrey, ...generalStyles.avenirRoman14 }}
        >
          {' '}
          Connect with Stripe
        </Text>
        <IconFeader
          style={{ textAlign: 'right', marginLeft: 10 }}
          name="chevron-right"
          size={25}
          color={colors.bordergrey}
        />
      </View>
    </TouchableOpacity>
  );

  render() {
    const arrMenuItems = [
      APPLICATION_ROUTES.ACCOUNT_CLIENTS,
      APPLICATION_ROUTES.ARCHIVED_SERVICES,
      APPLICATION_ROUTES.CLIENT_BOOKINGS,
    ];
    return (
      <View
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'flex-start',
          marginTop: this.state.hasNotch ? 100 : 70,
        }}
      >
        {this.props.user.account.StripeAccount
        && this.props.user.account.StripeAccount.stripeSecretKey
          ? this.renderIncomeAndTaxes()
          : null}
        {this.props.user.account.StripeAccount
        && this.props.user.account.StripeAccount.stripeSecretKey
          ? this.renderAlreadyConnectedToStripeItems()
          : this.renderStripeItems()}
        {arrMenuItems.map((item) => this.renderMenuItems(item))}
        {this.renderLogout()}
      </View>
    );
  }
}

const mapStateToProps = (state) => ({
  user: state.user.details,
  salesTax: state.sales.salesTax,
  clientsList: state.clients.clientsList,
});

export default connect(mapStateToProps, {
  createLogoutAction,
  getSalesTaxAction,
  getTransactionHistoryAction,
  getActiveClientsAction,
})(AccountScreen);
