import React, { Component, createRef } from 'react';
import {
  StyleSheet,
  SafeAreaView,
  View,
  ActivityIndicator,
  Alert,
} from 'react-native';
import AwesomeAlert from 'react-native-awesome-alerts';
import { connect } from 'react-redux';
import { colors } from '../../styles';
import { deleteAlertStyles } from '../../styles/alertStyle';
import {
  setSelectedLocationForEditValuesAction,
  saveSelectedLocationForEditAction,
  deleteSelectedLocationForEditAction,
} from '../../actions/artichoke/Locations.actions';
import EditLocationForm from '../../components/artichoke/screensComponents/locations/EditLocationForm';
import { APPLICATION_ROUTES } from '../../constants';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { generalStyles } from '../../styles/generalStyle';
import { track } from '../../util/Analytics';
import HeaderRightButton from '../../components/HeaderRightButton';

const formRef = createRef();
class EditMyLocationScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'My Location',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
      onSaveAddress: this.onSaveAddress,
    });
  }

  componentDidMount() {
    if (this.props.location) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.locationsLoading !== this.props.locationsLoading) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  onDeleteAddress = () => {
    this.showAlert();
  };

  onSaveAddress = () => {
    formRef.current.handleSubmit();
    track('location_enabled');
  };

  onSubmitAddress = () => {
    if (this.props.location.startTimes.length === 0) {
      Alert.alert(
        'No start slot selected',
        'Please select at least one start slot for current location',
      );
    } else if (this.props.location.workHours.indexOf('null') !== -1) {
      Alert.alert('Invalid Workhours', 'Please check the workdays');
    } else {
      this.props.saveSelectedLocationForEditAction();
    }
  };

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  hideAlert = () => {
    this.setState({
      showAlert: false,
    });
  };

  showAlert = () => {
    this.setState({
      showAlert: true,
    });
  };

  renderHeaderRight = () => {
    if (this.props.locationsLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.onSaveAddress()}
        title="Save"
        titleStyle={{ ...generalStyles.navigationButtonText }}
      />
    );
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <EditLocationForm
          refForm={formRef}
          initialValues={this.props.location}
          onSubmitForm={this.onSubmitAddress}
          onDeleteLocation={this.onDeleteAddress}
          setSelectedLocationForEditValuesAction={
            this.props.setSelectedLocationForEditValuesAction
          }
        />
        <AwesomeAlert
          show={this.state.showAlert}
          showProgress={false}
          useNativeDriver
          title="Delete Location"
          message={`Deleting a location removes the associated work hours from your schedule.

 Please confirm that this is okay.`}
          closeOnTouchOutside={false}
          closeOnHardwareBackPress={false}
          showConfirmButton
          showCancelButton
          titleStyle={deleteAlertStyles.alertTitle}
          messageStyle={styles.alertText}
          confirmText="Nevermind"
          cancelText="Delete"
          confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
          confirmButtonStyle={deleteAlertStyles.alertButton}
          cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
          cancelButtonStyle={deleteAlertStyles.alertButton}
          actionContainerStyle={{
            flexDirection: 'column',
            backgroundColor: colors.white,
            borderRadius: 5,
          }}
          contentContainerStyle={{
            ...deleteAlertStyles.alertContainer,
            height: 275,
          }}
          onCancelPressed={() => {
            this.props.deleteSelectedLocationForEditAction();
            this.hideAlert();
            this.props.navigation.navigate({
              name: APPLICATION_ROUTES.HOURS_LOCATIONS,
            });
            track('location_deleted');
          }}
          onConfirmPressed={() => {
            this.hideAlert();
          }}
        />
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  alertText: {
    fontSize: 13,
    fontFamily: 'SFProText-Regular',
    color: colors.subGrey,
    width: 270,
    textAlign: 'center',
    marginBottom: 39,
    paddingLeft: 10,
  },
});
const mapStateToProps = (state) => ({
  location: state.locations.selectedLocationForEdit,
  locationsLoading: state.loadingComponents.locationsLoading,
});
export default connect(mapStateToProps, {
  setSelectedLocationForEditValuesAction,
  saveSelectedLocationForEditAction,
  deleteSelectedLocationForEditAction,
})(EditMyLocationScreen);
