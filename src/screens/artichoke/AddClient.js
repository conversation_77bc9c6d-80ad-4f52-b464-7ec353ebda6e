import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, FlatList, StyleSheet, View, Alert,
} from 'react-native';
import { setNewAppointmentValuesAction } from '../../actions/artichoke/Appointments.actions';
import {
  getActiveClientsAction,
  setFilteredClientsListAction,
} from '../../actions/artichoke/Clients.actions';
import ClientListItem from '../../components/artichoke/screensComponents/booked/ClientListItem';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import { HeaderSearchBar, LoadingSpinner } from '../../components';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { generalStyles } from '../../styles/generalStyle';
import HeaderRightButton from '../../components/HeaderRightButton';

class AddClient extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: '',
      selectedClientList: this.props.newAppointment.clients,
    };
    this.props.getActiveClientsAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
      setNewAppointmentClients: this.setNewAppointmentClients,
    });
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
  }

  onSelectItem = (item) => {
    const cL = this.state.selectedClientList;
    this.setState({ selectedClientList: cL.concat(item) });
  };

  onUnselectItem = (e) => {
    const rc = this.state.selectedClientList.filter((item) => {
      const clientId = item.Client ? item.Client.id : item.id;
      return clientId !== e.id;
    });
    this.setState({ selectedClientList: rc });
  };

  setNewAppointmentClients = () => {
    if (
      !this.props.newAppointment?.isScheduled
      && this.props.newAppointment?.service
    ) {
      if (
        this.state.selectedClientList.length
        > this.props.newAppointment?.service?.maxParticipants
      ) {
        const clients = this.props.newAppointment?.service?.maxParticipants > 1
          ? "client's"
          : 'client';
        Alert.alert(
          `You can select only maximum ${this.props.newAppointment?.service?.maxParticipants} ${clients}`,
          '',
          [{ text: 'OK', style: 'cancel' }],
        );
        return;
      }
    }
    this.props.setNewAppointmentValuesAction({
      key: 'clients',
      value: this.state.selectedClientList,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'isEdit',
      value: true,
    });
    this.props.navigation.goBack(null);
  };

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Add a Client',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  checkIsSelected = (e) => this.state.selectedClientList.filter((item) => {
    const clientId = item.Client ? item.Client.id : item.id;

    return e.id === clientId;
  }).length;

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = this.props.clientsList.filter((item) => {
      const itemData = `${item.user.firstName.toUpperCase()} ${item.user.lastName.toUpperCase()}`;
      const textData = text.toUpperCase();
      return itemData.indexOf(textData) > -1;
    });
    this.props.setFilteredClientsListAction(newData);
  };

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Client First Name"
    />
  );

  renderHeaderLeft = () => (
    <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.setNewAppointmentClients()}
      title="Done"
      titleStyle={{ ...generalStyles.navigationButtonText }}
    />
  );

  render() {
    return !this.props.showActiveClientsLoading ? (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.filteredClientsList}
          renderItem={({ item }) => (
            <ClientListItem
              item={item}
              selectItem={this.onSelectItem}
              unselectItem={this.onUnselectItem}
              isSelected={this.checkIsSelected(item)}
            />
          )}
          keyExtractor={(item) => item.id}
          refreshing={this.props.clientListLoading}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Client Found" />
          )}
          ListHeaderComponent={this.renderHeader}
        />
      </SafeAreaView>
    ) : (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  clientsList: state.clients.clientsList,
  filteredClientsList: state.clients.filteredClientsList,
  newAppointment: state.appointments.newAppointmentValues,
  showActiveClientsLoading: state.loadingComponents.showActiveClientsLoading,
});

export default connect(mapStateToProps, {
  getActiveClientsAction,
  setFilteredClientsListAction,
  setNewAppointmentValuesAction,
})(AddClient);
