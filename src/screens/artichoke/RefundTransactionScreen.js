import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
} from 'react-native';
import AwesomeAlert from 'react-native-awesome-alerts';
import { colors } from '../../styles';
import { APPLICATION_ROUTES } from '../../constants';
import { refundClientTransactionAction } from '../../actions/artichoke/Clients.actions';
import { clearSuccessAction } from '../../actions/artichoke/Appointments.actions';
import { generalStyles } from '../../styles/generalStyle';
import { successAlertStyles } from '../../styles/alertStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import FormSubmitButton from '../../components/artichoke/common/FormSubmitButton';
import { track } from '../../util/Analytics';

const bgImage = require('../../assets/imgServiceBackground.png');
const successIcon = require('../../assets/imgAlertCheckmark.png');

class RefundTransactionScreen extends Component {
  static navigationOptions = ({ route }) => ({
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  componentDidUpdate(prevProps) {
    if (this.props.newAppointment) {
      this.props.navigation.setOptions({
        title: this.changePageTitle(this.props.newAppointment),
      });
    }
    if (prevProps.success !== this.props.success) {
      if (this.props.success) {
        this.showAlert();
        track('purchase_refund');
      }
    }
  }

  goBack = () => {
    this.props.navigation.goBack();
  };

  goToRefund = () => {
    this.props.navigation.navigate(APPLICATION_ROUTES.REFUND_TRANSACTION);
  };

  hideAlert = () => {
    this.setState({
      showAlert: false,
    });
  };

  showAlert = () => {
    this.setState({
      showAlert: true,
    });
  };

  showSelectedAddress = (item) => {
    let addressLine = '';
    if (item) {
      addressLine = item.address2 !== ''
        ? `${item.address1}, ${item.address2}, ${item.city}, ${item.state}`
        : `${item.address1}, ${item.city}, ${item.state}`;
    }
    return addressLine;
  };

  render() {
    let productName = '';
    if (this.props.selectedTransaction.purchase.pack) {
      productName = this.props.selectedTransaction.purchase.pack.name;
    } else if (
      this.props.selectedTransaction.purchase.clientPurchaseProductDurations
      && this.props.selectedTransaction.purchase.clientPurchaseProductDurations
        .length
    ) {
      productName = this.props.selectedTransaction.purchase
        .clientPurchaseProductDurations[0].productName;
    }

    let purchaseType = '';

    if (
      this.props.selectedTransaction.purchase.purchaseType === 'CREDIT_CARD'
      && this.props.selectedClientCreditCart
    ) {
      purchaseType = `${this.props.selectedClientCreditCart.creditCardType} ...${this.props.selectedClientCreditCart.lastFour}`;
    } else if (
      this.props.selectedTransaction.purchase.purchaseType
        === 'CASH_OR_CHECK'
      || !this.props.selectedClientCreditCart
    ) {
      purchaseType = 'Cash Or Check';
    }

    return (
      <SafeAreaView style={styles.container}>
        {this.props.success ? (
          <AwesomeAlert
            show={this.state.showAlert}
            showProgress={false}
            useNativeDriver
            closeOnTouchOutside={false}
            closeOnHardwareBackPress={false}
            showConfirmButton
            confirmText="Continue"
            confirmButtonTextStyle={successAlertStyles.alertButtonText}
            confirmButtonStyle={successAlertStyles.alertButton}
            actionContainerStyle={{
              flexDirection: 'column',
              backgroundColor: colors.white,
              borderRadius: 5,
            }}
            contentContainerStyle={successAlertStyles.alertContainer}
            customView={(
              <View style={successAlertStyles.alertContainer}>
                <Image source={successIcon} style={{ marginTop: 35 }} />
                <Text style={successAlertStyles.alertTitle}>
                  {this.props.success.title}
                </Text>
                <Text style={successAlertStyles.alertText}>
                  {this.props.success.message}
                </Text>
              </View>
            )}
            onConfirmPressed={() => {
              this.hideAlert();
              this.props.clearSuccessAction();
              this.props.navigation.pop(2);
            }}
          />
        ) : null}
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <View style={styles.contentUser}>
              <Image
                source={
                  this.props.selectedClient?.user.avatarUrl
                    ? { uri: this.props.selectedClient?.user.avatarUrl }
                    : bgImage
                }
                style={styles.userImage}
              />
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{productName}</Text>
                <Text style={styles.userJob}>
                  {this.props.selectedClient?.user.firstName}
                  {' '}
                  {this.props.selectedClient?.user.lastName}
                  {' '}
                </Text>
              </View>
            </View>
            <View>
              <View>
                <Text style={styles.label}>Refund Method</Text>
                <Text style={styles.description}>{purchaseType}</Text>
              </View>
            </View>
          </View>
        </ScrollView>
        <View style={styles.bottom}>
          <View style={styles.paymentPrice}>
            <View style={styles.frameTotal}>
              <Text style={styles.labelTotal}>Refund:</Text>
              <Text style={styles.valueTotal}>
                {this.props.symbol}
                {parseFloat(
                  this.props.selectedTransaction.purchase.amountPaid,
                ).toFixed(2)}
              </Text>
            </View>

            <View style={styles.bottomSubmitButton}>
              <FormSubmitButton
                title="Confirm"
                buttonStyle={styles.confirmButton}
                buttonLabelStyle={styles.labelConfirm}
                onPressButton={() => this.props.refundClientTransactionAction(
                  this.props.selectedTransaction,
                )}
                loading={this.props.refundActionLoading}
                color={colors.white}
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    marginBottom: 24,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottom: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 38,
    marginHorizontal: 20,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    marginTop: 20,
    marginBottom: 120,
    paddingHorizontal: 20,
    width: '100%',
  },
  userImage: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userDetails: {
    flex: 6,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    lineHeight: 22,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  label: {
    ...generalStyles.avenirHeavy17,
    paddingTop: 24,
  },
  description: {
    marginTop: 8,
    ...generalStyles.avenirRoman14,
    color: colors.fillDarkGrey,
  },
  frameTotal: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: 1,
    paddingTop: 26,
    borderTopColor: colors.subGrey,
  },
  paymentPrice: {
    display: 'flex',
    width: '100%',
    flexDirection: 'column',
  },
  labelTotal: {
    textAlign: 'left',
    ...generalStyles.avenirHeavy17,
    flex: 4,
    color: colors.black,
    borderWidth: 0,
  },
  valueTotal: {
    textAlign: 'right',
    ...generalStyles.avenirHeavy17,
    flex: 2.5,
    color: colors.black,
    borderWidth: 0,
  },
  confirmButton: {
    display: 'flex',
    width: '100%',
    borderColor: colors.duskBlue,
    backgroundColor: colors.duskBlue,
    textAlign: 'center',
    justifyContent: 'center',
    borderRadius: 28,
    borderWidth: 1,
    height: 55,
    marginTop: 48,
  },
  labelConfirm: {
    textAlign: 'center',
    fontSize: 17,
    fontWeight: 'bold',
    color: colors.white,
  },
});

const mapStateToProps = (state) => ({
  selectedTransaction: state.clients.selectedClientTransaction,
  selectedClient: state.clients.selectedClient,
  selectedClientCreditCart: state.clients.selectedClient?.creditCard,
  success: state.appointments.success,
  refundActionLoading: state.loadingComponents.refundActionLoading,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  refundClientTransactionAction,
  clearSuccessAction,
})(RefundTransactionScreen);
