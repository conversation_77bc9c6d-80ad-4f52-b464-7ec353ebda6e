import React, { Component } from 'react';
import { SafeAreaView, StyleSheet, View } from 'react-native';
import { connect } from 'react-redux';
import { CLASS_REPEAT } from '../../constants';
import CreateRecurrenceForm from '../../components/artichoke/screensComponents/booked/CreateRecurrenceForm';
import { setNewClassValuesAction } from '../../actions/artichoke/Appointments.actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { generalStyles } from '../../styles/generalStyle';
import HeaderRightButton from '../../components/HeaderRightButton';

class ClassRecurrenceScreen extends Component {
  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Repeat',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      repeatIntervalType: this.props.newAppointment.repeatFrequency,
      untilDay: this.props.newAppointment.repeatUntil || new Date(),
      count: this.props.newAppointment.repeatCount,
      repeatUntilType: this.props.newAppointment.repeatUntilType,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
      onSaveRecurrence: this.onSaveRecurrence,
    });
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
  }

  onChangeRepeatCount = (item) => {
    this.setState({
      count: item,
    });
  };

  onChangeRepeatIntervalType = (item) => {
    this.setState({
      repeatIntervalType: item,
    });
  };

  onChangeRepeatUntilDate = (item) => {
    this.setState({
      untilDay: item,
    });
    this.setState({
      count: 0,
    });
  };

  onChangeRepeatUntilType = (item) => {
    this.setState({
      repeatUntilType: item,
    });
  };

  onSaveRecurrence = () => {
    this.props.setNewClassValuesAction({
      key: 'repeatFrequency',
      value: this.state.repeatIntervalType,
    });
    this.props.setNewClassValuesAction({
      key: 'repeatUntil',
      value: this.state.untilDay ? this.state.untilDay.toString() : null,
    });
    this.props.setNewClassValuesAction({
      key: 'repeatCount',
      value: this.state.count,
    });
    this.props.setNewClassValuesAction({
      key: 'repeatUntilType',
      value: this.state.repeatUntilType,
    });
    this.props.navigation.navigate({
      name: this.props.route.params.returnRoute,
    });
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.onSaveRecurrence()}
      title="Done"
      titleStyle={{ ...generalStyles.navigationButtonText }}
    />
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <CreateRecurrenceForm
          repeatIntervalType={this.state.repeatIntervalType}
          repeatUntilType={this.state.repeatUntilType}
          untilDay={this.state.untilDay}
          count={this.state.count}
          repeatTypes={CLASS_REPEAT}
          onChangeRepeatUntilType={this.onChangeRepeatUntilType}
          onChangeRepeatIntervalType={this.onChangeRepeatIntervalType}
          onChangeRepeatUntilDate={this.onChangeRepeatUntilDate}
          onChangeRepeatCount={this.onChangeRepeatCount}
        />
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newAppointment: state.appointments.newClassValues,
});

export default connect(mapStateToProps, {
  setNewClassValuesAction,
})(ClassRecurrenceScreen);
