import React, { Component } from 'react';
import {
  SafeAreaView, StyleSheet, View, Alert,
} from 'react-native';
import { connect } from 'react-redux';
import moment from 'moment';
import CreateRecurrenceForm from '../../components/artichoke/screensComponents/booked/CreateRecurrenceForm';
import { setNewAppointmentValuesAction } from '../../actions/artichoke/Appointments.actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { generalStyles } from '../../styles/generalStyle';
import HeaderRightButton from '../../components/HeaderRightButton';

class RecurrenceScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      repeatIntervalType: this.props.newAppointment.repeatIntervalType,
      untilDay:
        this.props.newAppointment.untilDay
        || moment(Date()).format('MM/DD/YYYY'),
      count: this.props.newAppointment.count,
      repeatUntilType: this.props.newAppointment.repeatUntilType || 'DAY',
      minimumDate: this.props.newAppointment.date
        ? Date.parse(this.props.newAppointment.date)
        : new Date(),
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
      onSaveRecurrence: this.onSaveRecurrence,
    });
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
  }

  onChangeRepeatCount = (item) => {
    this.setState({
      count: item,
    });
  };

  onChangeRepeatIntervalType = (item) => {
    this.setState({
      repeatIntervalType: item,
    });
  };

  onChangeRepeatUntilDate = (item) => {
    this.setState({
      untilDay: item,
    });
  };

  onChangeRepeatUntilType = (item) => {
    this.setState({
      repeatUntilType: item,
    });
  };

  onSaveRecurrence = () => {
    if (this.state.count == null && this.state.untilDay == null) {
      Alert.alert('Please select date', '', [{ text: 'OK', style: 'cancel' }]);
      return;
    }
    this.props.setNewAppointmentValuesAction({
      key: 'repeatIntervalType',
      value: this.state.repeatIntervalType,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'untilDay',
      value: this.state.untilDay ? this.state.untilDay : null,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'count',
      value: this.state.count,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'repeatUntilType',
      value: this.state.repeatUntilType,
    });
    this.props.setNewAppointmentValuesAction({
      key: 'isEdit',
      value: true,
    });
    this.props.navigation.navigate({
      name: this.props.route.params.returnRoute,
    });
  };

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Repeat',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.onSaveRecurrence()}
      title="Done"
      titleStyle={{ ...generalStyles.navigationButtonText }}
    />
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <CreateRecurrenceForm
          repeatIntervalType={this.state.repeatIntervalType}
          repeatUntilType={this.state.repeatUntilType}
          untilDay={this.state.untilDay}
          count={this.state.count}
          onChangeRepeatUntilType={this.onChangeRepeatUntilType}
          onChangeRepeatIntervalType={this.onChangeRepeatIntervalType}
          onChangeRepeatUntilDate={this.onChangeRepeatUntilDate}
          onChangeRepeatCount={this.onChangeRepeatCount}
          minimumDate={this.state.minimumDate}
        />
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newAppointment: state.appointments.newAppointmentValues,
});

export default connect(mapStateToProps, {
  setNewAppointmentValuesAction,
})(RecurrenceScreen);
