import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView, FlatList, StyleSheet, Text,
} from 'react-native';
import { colors, shadow } from '../../styles';
import {
  getActiveClientsAction,
  setSelectedClient,
} from '../../actions/artichoke/Clients.actions';
import { getClientBookingsAction } from '../../actions/artichoke/Appointments.actions';
import TouchableClientListItem from '../../components/artichoke/screensComponents/services/TouchableClientListItem';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import { APPLICATION_ROUTES } from '../../constants';
import HeaderLeftButton from '../../components/HeaderLeftButton';

class ClientBookings extends Component {
  static navigationOptions = ({ navigation, route }) => ({
    title: 'Select a Client',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
    this.props.getActiveClientsAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  onPressSelectClient = (item) => {
    this.props.setSelectedClient(item);
    this.props.getClientBookingsAction(item.id);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.LIST_CLIENT_BOOKINGS,
      params: {
        clientId: item.id,
      },
    });
  };

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.filteredClientsList}
          renderItem={({ item }) => (
            <TouchableClientListItem
              item={item}
              onPressSelectClient={() => this.onPressSelectClient(item)}
            />
          )}
          keyExtractor={(item) => item.id.toString()}
          refreshing={this.props.clientListLoading}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Client Found" />
          )}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 32,
  },
  footerList: {
    flex: 1,
    position: 'absolute', // Here is the trick
    bottom: 20, // Here is the trick
    right: 10,
    height: 40,
    flexGrow: 1,
  },
  addButton: {
    flex: 1,
    width: 40,
    height: 40,
    backgroundColor: colors.medYellow,
    alignSelf: 'center',
    color: colors.white,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadow,
  },
  addButtonText: {
    fontSize: 28,
    color: colors.white,
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  clientsList: state.clients.clientsList,
  filteredClientsList: state.clients.filteredClientsList,
});

export default connect(mapStateToProps, {
  getActiveClientsAction,
  setSelectedClient,
  getClientBookingsAction,
})(ClientBookings);
