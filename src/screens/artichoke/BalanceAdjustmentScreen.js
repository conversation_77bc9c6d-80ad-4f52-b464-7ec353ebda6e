import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  StyleSheet, View, Text, Image, TouchableOpacity,
} from 'react-native';
import { colors } from '../../styles';
import { HeaderLeftButton } from '../../components';
import { generalStyles } from '../../styles/generalStyle';
import { saveBalanceAdjustmentAction } from '../../actions/artichoke/Clients.actions';
import FormSubmitButtonWithSpinner from '../../components/artichoke/common/FormSubmitButtonWithSpinner';
import { track } from '../../util/Analytics';

const substractImage = require('../../assets/btnMinusGray.png');
const addImage = require('../../assets/btnAddGray.png');
const exclamationImage = require('../../assets/btnDelete.png');

class BalanceAdjustmentScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Balance Adjustment',
    headerLeft: () => (
      <HeaderLeftButton
        title="Cancel"
        onPress={() => route.params.goBack()}
        titleStyle={{ color: colors.white, ...generalStyles.avenirMedium14 }}
      />
    ),
    headerBackTitle: 'Cancel',
  });

  constructor(props) {
    super(props);
    this.state = {
      qty: parseInt(this.props.balanceQuantity, 10),
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  onIncrement = () => {
    this.setState({
      qty: this.state.qty + 1,
    });
  };

  onSaveBalanceAdjustment = () => {
    const payload = {
      note: '',
      referenceNumber: '0',
      value: this.state.qty - this.props.balanceQuantity,
    };
    this.props.saveBalanceAdjustmentAction(payload);
  };

  onSubstract = () => {
    this.setState({
      qty: this.state.qty - 1,
    });
  };

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  render() {
    return (
      <View style={styles.container}>
        <View style={styles.balance}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => this.onSubstract()}
            style={styles.substractSign}
          >
            <Image source={substractImage} />
          </TouchableOpacity>
          <Text style={styles.balanceNumber}>{this.state.qty}</Text>
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => this.onIncrement()}
            style={styles.addSign}
          >
            <Image source={addImage} />
          </TouchableOpacity>
        </View>
        <View>
          <View style={styles.banner}>
            <Image source={exclamationImage} />
            <Text
              style={{
                ...generalStyles.avenirMedium13,
                color: colors.white,
                marginLeft: 10,
              }}
            >
              Please read carefully
            </Text>
          </View>
          <View style={styles.bottomContainer}>
            <View style={styles.headerList}>
              <Text style={styles.label}>Balance Adjustment</Text>
            </View>
            <View style={styles.textInfo}>
              <Text style={styles.textInfoLabel}>
                Manual balance adjustments on this screen should be limited to
                the rare correction of errors or other one-time adjustments.
                {'\n\n'}
                Adjusting session balances here
                {' '}
                <Text style={{ ...generalStyles.avenirHeavy17 }}>
                  WILL NOT
                </Text>
                {' '}
                change the client lifetime value or income reported for
                purchases previously made, creating inconsistency in your
                records. To adjust income and session balances together, try
                locating a specific payment in client transactions and refunding
                it instead of completing a balance adjustment.
              </Text>
            </View>
            <View style={styles.bottomSubmitButton}>
              <FormSubmitButtonWithSpinner
                buttonStyle={{ ...styles.subButton }}
                title="Save"
                onPressButton={() => {
                  this.onSaveBalanceAdjustment();
                  track('balance_adjustment_made');
                }}
                isLoading={this.props.saveBalanceAdjustmentLoader}
              />
            </View>
          </View>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    display: 'flex',
    justifyContent: 'space-between',
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    marginTop: 25,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 38,
  },
  subButton: {
    display: 'flex',
    width: '100%',
    borderColor: colors.duskBlue,
    backgroundColor: colors.duskBlue,
    textAlign: 'center',
    justifyContent: 'center',
    borderRadius: 28,
    borderWidth: 1,
  },
  balance: {
    flexDirection: 'row',
    flex: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  substractSign: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    height: 30,
    width: 30,
  },
  balanceNumber: {
    display: 'flex',
    flex: 2,
    textAlign: 'center',
    color: colors.black,
    ...generalStyles.avenirHeavy80,
  },
  addSign: {
    display: 'flex',
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    height: 30,
    width: 30,
  },
  banner: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 44,
    backgroundColor: colors.dustyRed,
  },
  headerList: {
    height: 65,
    display: 'flex',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomColor: colors.lightgrey,
  },
  label: {
    fontWeight: 'bold',
    ...generalStyles.avenirHeavy17,
    width: '100%',
  },
  textInfo: {
    display: 'flex',
    width: '100%',
  },
  textInfoLabel: {
    ...generalStyles.avenirRoman17,
    color: colors.subGrey,
    lineHeight: 22,
    width: '100%',
  },
  bottomContainer: {
    paddingHorizontal: 20,
    backgroundColor: colors.lightgrey,
  },
});

const mapStateToProps = (state) => ({
  balanceQuantity: state.clients.selectedClientBalance.quantity,
  saveBalanceAdjustmentLoader:
    state.loadingComponents.saveBalanceAdjustmentLoader,
});

export default connect(mapStateToProps, { saveBalanceAdjustmentAction })(
  BalanceAdjustmentScreen,
);
