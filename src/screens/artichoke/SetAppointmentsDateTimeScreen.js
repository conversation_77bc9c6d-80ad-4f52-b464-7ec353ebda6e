import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON>AreaView, StyleSheet } from 'react-native';
import moment from 'moment';
import { connect } from 'react-redux';
import { colors } from '../../styles';
import { APPLICATION_ROUTES } from '../../constants';
import CreateDateAndTimeForm from '../../components/artichoke/screensComponents/booked/CreateDateAndTimeForm';
import {
  setNewAppointmentValuesAction,
  getBlockingAppointmentsByDateAction,
} from '../../actions/artichoke/Appointments.actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import HeaderRightButton from '../../components/HeaderRightButton';

class SetAppointmentsDateTimeScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      date:
        this.props.newAppointment.date != null
          ? moment(this.props.newAppointment.date, 'MM/DD/YYYY')
          : moment(),
      time: this.props.newAppointment.time,
    };
    this.props.navigation.setParams({
      onSaveDateTime: this.onSaveDateTime,
    });
    const date = this.props.newAppointment.date || moment();
    this.props.getBlockingAppointmentsByDateAction(date.toString());
  }

  onChangeDate = (date) => {
    this.setState(
      {
        date,
      },
      () => {
        this.props.getBlockingAppointmentsByDateAction(date.toString());
      },
    );
  };

  onChangeTime = (time) => {
    this.setState({
      time,
    });
  };

  onSaveDateTime = () => {
    if (!this.state.date || !this.state.time) {
      Alert.alert(
        'Pick a Date & Time',
        'A date and time are required to book a session',
        [
          {
            text: 'Cancel',
          },
        ],
        { cancelable: false },
      );
    } else {
      this.props.setNewAppointmentValuesAction({
        key: 'date',
        value: moment(this.state.date).format('MM/DD/YYYY'),
      });
      this.props.setNewAppointmentValuesAction({
        key: 'time',
        value: this.state.time.toString(),
      });
      this.props.setNewAppointmentValuesAction({
        key: 'isEdit',
        value: true,
      });
      this.props.navigation.navigate({
        name: this.props.route.params.returnRoute,
        merge: true,
      });
    }
  };

  static navigationOptions = ({ route, navigation }) => ({
    title: APPLICATION_ROUTES.APPOINTMENTS_DATE_AND_TIME,
    headerLeft: () => <HeaderLeftButton onPress={() => navigation.goBack()} />,
    headerBackTitle: 'Back',
    headerRight: () => (
      <HeaderRightButton
        onPress={() => route.params.onSaveDateTime()}
        title="Done"
        titleStyle={{
          color: colors.white,
          margin: 15,
        }}
      />
    ),
  });

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <CreateDateAndTimeForm
          selectedDate={this.state.date}
          selectedTime={this.state.time}
          blockingAppointments={this.props.blockingAppointments}
          onChangeDate={this.onChangeDate}
          onChangeTime={this.onChangeTime}
          workHours={this.props.route?.params?.workHours ?? []}
        />
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newAppointment: state.appointments.newAppointmentValues,
  blockingAppointments: state.appointments.blockingAppointments,
});

export default connect(mapStateToProps, {
  setNewAppointmentValuesAction,
  getBlockingAppointmentsByDateAction,
})(SetAppointmentsDateTimeScreen);
