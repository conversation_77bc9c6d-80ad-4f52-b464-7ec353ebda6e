import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  FlatList,
  StyleSheet,
  LayoutAnimation,
  Text,
  View,
} from 'react-native';
import moment from 'moment';
import { colors } from '../../styles';
import BookedListItem from '../../components/artichoke/screensComponents/booked/BookedListItem';
import SlotBlockerListItem from '../../components/artichoke/screensComponents/slotblockers/SlotBlockerListItem';
import EmptyBookedList from '../../components/artichoke/screensComponents/booked/EmptyBookedList';
import FormGroup from '../../components/artichoke/common/FormGroup';
import FormBooking from '../../components/artichoke/common/FormBooking';
import FormSubmitButton from '../../components/artichoke/common/FormSubmitButton';
import { CalendarWithToggle, HeaderSearchBar } from '../../components';
import {
  getAppointmentsByDateAction,
  setFilteredAppointmentsAction,
  setSelectedAppointmentAction,
  setSelectedSlotBlockerAction,
  getAppointmentAction,
  clearSelectedAppointment,
  clearSelectedSlotBlocker,
  clearSelectedClassAction,
  getClassAction,
  getSlotBlockerAction,
  setAppointmentsByDateAction,
} from '../../actions/artichoke/Appointments.actions';
import {
  APPLICATION_ROUTES,
  androidSafeLayoutAnimation,
} from '../../constants';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import AddButton from '../../components/artichoke/common/AddButton';

import { generalStyles } from '../../styles/generalStyle';
import { selectDay } from '../../reducers/selectedDayReducer';
import LoadingSpinner from '../../components/LoadingSpinner';
import {
  getAvailableLocationsAction,
  getSelfBookingAction,
  getSelfBookingUsersAction,
} from '../../actions/artichoke/Locations.actions';
import artichoke from '../../api/artichoke/artichoke';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import HeaderRightButton from '../../components/HeaderRightButton';
import EmptyServiceList from '../../components/artichoke/screensComponents/services/EmptyServiceList';

const emptyBookedListImage = require('../../assets/imgAddChatFtue.png');

const bookedActionSheetRef = createRef();

class BookedScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Bookings',
    headerLeft: () => (
      <HeaderLeftButton
        title="Bookings"
        onPress={() => route.params.goBack()}
        titleStyle={{
          color: colors.white,
          margin: 15,
        }}
      />
    ),
    headerBackTitle: 'Bookings',
    headerRight: () => (
      <HeaderRightButton
        onPress={() => {}}
        title="Hours"
        titleStyle={{
          color: colors.white,
          margin: 15,
        }}
      />
    ),
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      weekView: true,
      calendarLoading: false,
      selectedDay: moment().format('YYYY-MM-DD'),
      pageIsLoading: true,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.getAvailableLocationsAction();
      this.props.getSelfBookingAction();
      this.props.getSelfBookingUsersAction();
      this.props.setFilteredAppointmentsAction([]);
      this.props.getAppointmentsByDateAction(this.state.selectedDay);
      this.props.clearSelectedAppointment();
      this.props.clearSelectedSlotBlocker();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onMonthChange = async (date) => {
    const startOfMonth = moment(date).startOf('month').format('YYYY-MM-DD');
    let selectedDay = startOfMonth;
    if (moment(startOfMonth).month() === moment().month()) {
      selectedDay = moment().format('YYYY-MM-DD');
    }
    this.setState({ calendarLoading: true, pageIsLoading: false, selectedDay });
    await artichoke.api
      .getAppointmentsByDate(this.props.user.id, startOfMonth, 31)
      .then((result) => {
        this.props.setAppointmentsByDateAction(result.data.appointments);
        this.setState({ calendarLoading: false, pageIsLoading: true });
      });
  };

  onPressDay = async (day) => {
    this.props.selectDay(day);
    this.setState({ calendarLoading: true, selectedDay: day });

    // Edge case: refresh fetching appointments if the first week or last week of the month
    // includes dates starting from the previous month or next month respectively
    const prevDate = moment(this.state.selectedDay);
    const currDate = moment(day);
    if (currDate.month() !== prevDate.month()) {
      const startOfMonth = moment(currDate)
        .startOf('month')
        .format('YYYY-MM-DD');
      await artichoke.api
        .getAppointmentsByDate(this.props.user.id, startOfMonth, 31)
        .then((result) => {
          this.props.setAppointmentsByDateAction(result.data.appointments);
          this.setState({ calendarLoading: false, pageIsLoading: true });
        });
    }
  };

  onSelectAppointment = (appointment) => {
    this.props.clearSelectedAppointment();
    this.props.getAppointmentAction(appointment.id);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.INDIVIDUAL_APPOINTMENT,
    });
  };

  onSelectClass = (objClass) => {
    this.props.clearSelectedAppointment();
    this.props.getAppointmentAction(objClass.id);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.INDIVIDUAL_APPOINTMENT,
    });
  };

  onSelectSlotBlocker = (slotblocker) => {
    this.props.clearSelectedSlotBlocker();
    this.props.getSlotBlockerAction(slotblocker.id);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.EDIT_SLOTBLOCKER,
    });
  };

  onToggleCalendar = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ weekView: !this.state.weekView });
  };

  getMarkedDates = () => {
    let markedDates = JSON.parse(
      JSON.stringify(this.props.filteredAppointmentsList),
    );

    const selectedDayBacklight = {
      selected: true,
      dark: true,
      selectedColor: 'black',
    };
    const checkedInDayBacklight = {
      selected: true,
      selectedColor: 'rgba(70, 154, 30, 0.2)',
    };
    const generalBooked = {
      selected: true,
      selectedColor: 'rgba(182, 189, 195, 0.2)',
    };

    const booked = {
      key: 'booked',
      color: 'rgb(36, 81, 134)',
      selectedDotColor: 'rgb(36, 81, 134)',
    };

    const dots = [];
    dots.push(booked);
    Object.keys(markedDates).forEach((key) => {
      if (moment(key).isSame(this.state.selectedDay)) {
        markedDates[key] = {
          dots,
          marked: true,
          dark: false,
          selected: true,
          selectedColor: 'rgba(182, 189, 195, 0.2)',
          ...selectedDayBacklight,
        };
      } else if (this.checkCompleted(key)) {
        markedDates[key] = {
          dots,
          marked: true,
          dark: false,
          selected: true,
          selectedColor: 'rgba(182, 189, 195, 0.2)',
          ...checkedInDayBacklight,
        };
      } else {
        markedDates[key] = {
          dots,
          marked: true,
          dark: false,
          selected: true,
          selectedColor: 'rgba(182, 189, 195, 0.2)',
          ...generalBooked,
        };
      }
    });

    if (!(this.state.selectedDay in markedDates)) {
      markedDates[this.state.selectedDay] = {
        ...selectedDayBacklight,
      };
    }

    if (typeof markedDates === 'object' && markedDates.length === 0) {
      markedDates = {};
    }
    return markedDates;
  };

  getWeeklyCustomDatesStyles = () => {};

  getWeeklyMarkedDates = () => {
    const markedDates = JSON.parse(
      JSON.stringify(this.props.filteredAppointmentsList),
    );
    const booked = { key: 'booked', color: colors.nasmBlue };

    const dots = [];
    let reducedMarked = [];
    dots.push(booked);
    Object.keys(markedDates).map((key, index) => {
      reducedMarked.push((markedDates[index] = { date: key, dots }));
      return key;
    });

    if (typeof reducedMarked === 'object' && reducedMarked.length === 0) {
      reducedMarked = [];
    }
    return reducedMarked;
  };

  addNewBooking = () => {
    bookedActionSheetRef.current?.setModalVisible(false);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT,
    });
  };

  addNewClass = () => {
    bookedActionSheetRef.current?.setModalVisible(false);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.CREATE_NEW_CLASS,
    });
  };

  addNewSlotblocker = () => {
    bookedActionSheetRef.current?.setModalVisible(false);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.CREATE_NEW_SLOTBLOCKER,
      params: {
        completed: 'false',
      },
    });
  };

  checkCompleted = (day) => {
    const appointmentsPerDay = this.props.filteredAppointmentsList[day];
    let completed = false;
    appointmentsPerDay.forEach((item) => {
      if (item.Invitee) {
        item.Invitee.forEach((inv) => {
          if (inv.checkedIn === 'true') {
            completed = true;
          }
        });
      }
    });
    return completed;
  };

  goToDay = (day) => {
    this.setState({ pageIsLoading: true }, () => {
      this.onPressDay(day);
    });
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = {};
    Object.keys(this.props.appointmentsByDate).map((date) => {
      const filteredAppointments = this.props.appointmentsByDate[date].filter(
        (item) => {
          const textData = text.toUpperCase();
          const itemDataByClientName = `${item.name.toUpperCase()}`;
          if (item.description) {
            const itemDataByServiceName = `${item.description.toUpperCase()}`;
            if (itemDataByServiceName.indexOf(textData) > -1) {
              return itemDataByServiceName;
            }
          }
          if (itemDataByClientName.indexOf(textData) > -1) {
            return itemDataByClientName;
          }
          return false;
        },
      );
      if (filteredAppointments.length) {
        newData[date] = filteredAppointments;
      }
      return date;
    });
    this.props.setFilteredAppointmentsAction(newData);
  };

  renderAddAppointmentButton = () => (
    <AddButton
      onPressAction={() => bookedActionSheetRef.current?.setModalVisible(true)}
    />
  );

  renderBookedItem = (item) => {
    if (item.name === 'SLOT_BLOCKER') {
      return (
        <SlotBlockerListItem
          item={item}
          onPressItem={this.onSelectSlotBlocker}
        />
      );
    }
    if (item.isScheduled) {
      return <BookedListItem item={item} onPressItem={this.onSelectClass} />;
    }
    return (
      <BookedListItem item={item} onPressItem={this.onSelectAppointment} />
    );
  };

  renderHeaderComponent = () => (
    <View>
      <HeaderSearchBar
        searchText={this.state.value}
        onChangeText={(text) => this.searchFilterFunction(text)}
        clearable
        paddingTop={10}
        light
        shadow={false}
        placeholder="Client or Service"
      />
      <CalendarWithToggle
        weekView={this.state.weekView}
        isLoading={!this.state.pageIsLoading && this.state.calendarLoading}
        selectedDay={this.state.selectedDay}
        markedDates={this.getMarkedDates()}
        weeklyMarkedDates={this.getWeeklyMarkedDates()}
        customDatesStyles={this.getWeeklyCustomDatesStyles()}
        onMonthChange={this.onMonthChange}
        onDateSelected={this.onPressDay}
        onToggle={this.onToggleCalendar}
      />
      <View style={styles.headerList}>
        <Text style={styles.label}>Upcoming Bookings</Text>
      </View>
      <View style={styles.formday}>
        <Text style={styles.labelday}>
          {moment(this.state.selectedDay, 'YYYY-MM-DD').format('dddd, MMMM Do')}
        </Text>
      </View>
    </View>
  );

  renderFooterComponent = () => {
    const previousDates = JSON.parse(
      JSON.stringify(this.props.filteredAppointmentsList),
    );
    Object.keys(previousDates).forEach((key) => {
      if (moment(key) >= moment(this.state.selectedDay)) {
        delete previousDates[key];
      }
      return previousDates;
    });
    return (
      <View>
        <View style={styles.headerList}>
          <Text style={styles.label}>Previous Bookings</Text>
        </View>
        <FlatList
          data={Object.keys(previousDates).sort().reverse()}
          renderItem={({ item }) => (
            <FormBooking
              date={item}
              refreshing={this.props.appointmentsListLoading}
              appointments={previousDates[item]}
              onPressAppointment={this.onSelectAppointment}
              onPressSlotBlocker={this.onSelectSlotBlocker}
            />
          )}
          keyExtractor={(item) => `booked-list-past-item-${item}`}
          refreshing={this.props.appointmentsListLoading}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Previous Bookings" />
          )}
        />
      </View>
    );
  };

  render() {
    return this.props.appointmentsByDate.length === 0
      && this.props.appointmentsListLoading ? (
        <LoadingSpinner
          visible
          size="large"
          backgroundColor="rgba(0, 0, 0, 0.25)"
        />
      ) : (
        <SafeAreaView style={styles.container}>
          <View style={styles.scrollViewContainer}>
            <FlatList
              showsVerticalScrollIndicator={false}
              data={this.props.filteredAppointmentsList[this.state.selectedDay]}
              renderItem={({ item }) => this.renderBookedItem(item)}
              keyExtractor={(item) => `booked-list-item-${item.id}`}
              ListEmptyComponent={() => (
                <EmptyBookedList
                  message="No Bookings Scheduled"
                  hasIcon={emptyBookedListImage}
                />
              )}
              ListHeaderComponent={this.renderHeaderComponent}
              ListFooterComponent={this.renderFooterComponent}
            />
          </View>
          <View style={styles.footerList}>
            {this.renderAddAppointmentButton()}
          </View>
          <CustomActionSheet
            actionSheetRef={bookedActionSheetRef}
            title="Select Type"
          >
            <FormGroup formStyle={styles.bottomSubmitButton}>
              <FormSubmitButton
                title="Session"
                onPressButton={() => this.addNewBooking()}
                buttonStyle={styles.actionSheetButtonStyle}
                buttonLabelStyle={{ color: colors.azure }}
              />
              <FormSubmitButton
                title="Class"
                onPressButton={() => this.addNewClass()}
                buttonStyle={styles.actionSheetButtonStyle}
                buttonLabelStyle={{ color: colors.azure }}
              />
              <FormSubmitButton
                title="Slot Blocker"
                onPressButton={() => this.addNewSlotblocker()}
                buttonStyle={styles.actionSheetButtonStyle}
                buttonLabelStyle={{ color: colors.azure }}
              />
            </FormGroup>
          </CustomActionSheet>
        </SafeAreaView>
      );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollViewContainer: {
    flex: 1,
  },
  label: {
    fontWeight: 'bold',
    ...generalStyles.avenirBlack17,
    width: '100%',
    color: colors.black,
  },
  labelday: {
    ...generalStyles.avenirMedium17,
    width: '100%',
    color: colors.black,
  },
  formday: {
    backgroundColor: colors.veryLightBlue,
    height: 58,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingHorizontal: 21,
  },
  footerList: {
    flex: 1,
    position: 'absolute', // Here is the trick
    bottom: 0, // Here is the trick
    right: 0,
    height: 70,
    flexGrow: 1,
  },
  headerList: {
    height: 64,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 21,
    borderBottomWidth: 1,
    borderColor: colors.veryLightBlue,
  },
  actionSheetButtonStyle: {
    backgroundColor: colors.white,
    borderColor: colors.white,
    marginTop: 15,
  },
});

const mapStateToProps = (state) => ({
  service: state.services.selectedService,
  appointmentsList: state.appointments.appointmentsList,
  filteredAppointmentsList: state.appointments.filteredAppointments,
  appointmentsByDate: state.appointments.appointmentsListByDate,
  appointmentsListLoading: state.loadingComponents.appointmentsListLoading,
  user: state.user.details,
});

const mapDispatchToProps = {
  getAppointmentsByDateAction,
  setFilteredAppointmentsAction,
  setSelectedAppointmentAction,
  setSelectedSlotBlockerAction,
  clearSelectedAppointment,
  clearSelectedSlotBlocker,
  getAppointmentAction,
  getSlotBlockerAction,
  clearSelectedClassAction,
  getClassAction,
  selectDay,
  getSelfBookingAction,
  getSelfBookingUsersAction,
  getAvailableLocationsAction,
  setAppointmentsByDateAction,
};
export default connect(mapStateToProps, mapDispatchToProps)(BookedScreen);
