import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
} from 'react-native';
import { colors } from '../../styles';
import { getAppointmentAction } from '../../actions/artichoke/Appointments.actions';
import { generalStyles } from '../../styles/generalStyle';
import { Spinner } from '../../components/artichoke/common';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const bgImage = require('../../assets/imgServiceBackground.png');

class CheckInDetails extends Component {
  static navigationOptions = ({ route }) => ({
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
    this.props.getAppointmentAction(
      this.props.selectedTransaction.checkIn.appointment.id,
    );
  }

  showSelectedAddress = (item) => {
    let addressLine = '';
    if (item.onsiteAddress) {
      if (item.onsiteAddress.id === 1) {
        addressLine = 'Offered Remotely';
      } else if (item.onsiteAddress.id === 0) {
        addressLine = "Client's location";
      } else if (item.inapp.id === 2) {
        addressLine = 'Video Call';
      } else {
        addressLine = item.onsiteAddress.address2 !== ''
          ? `${item.onsiteAddress.address1}, ${item.onsiteAddress.address2}, ${item.onsiteAddress.city}, ${item.onsiteAddress.state}`
          : `${item.onsiteAddress.address1}, ${item.onsiteAddress.city}, ${item.onsiteAddress.state}`;
      }
    } else if (item.remotely.toString() === 'true') {
      addressLine = 'Offered Remotely';
    } else if (item.clientAddressEnable.toString() === 'true') {
      addressLine = "Client's location";
    } else if (item.inapp.toString() === 'true') {
      addressLine = 'Video Call';
    }

    return addressLine;
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  render() {
    return !this.props.selectedAppointment ? (
      <Spinner />
    ) : (
      <SafeAreaView style={styles.container}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <View style={styles.contentUser}>
              <Image
                source={
                  this.props.selectedClient?.user.avatarUrl
                    ? { uri: this.props.selectedClient?.user.avatarUrl }
                    : bgImage
                }
                style={styles.userImage}
              />
              <View style={styles.userDetails}>
                <Text style={styles.userName}>
                  {this.props.selectedAppointment.ProductDuration.name}
                </Text>
                <Text style={styles.userJob}>
                  {this.props.selectedClient?.user.firstName}
                  {' '}
                  {this.props.selectedClient?.user.lastName}
                  {' '}
                </Text>
              </View>
            </View>
            <View>
              <View>
                <Text style={styles.label}>Location</Text>
                <Text style={styles.description}>
                  {this.showSelectedAddress(this.props.selectedAppointment)}
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    marginTop: 20,
    marginBottom: 120,
    paddingHorizontal: 20,
    width: '100%',
  },
  userImage: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userDetails: {
    flex: 6,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    lineHeight: 22,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  label: {
    ...generalStyles.avenirHeavy17,
    paddingTop: 24,
  },
  description: {
    marginTop: 8,
    ...generalStyles.avenirRoman14,
    color: colors.fillDarkGrey,
  },
});

const mapStateToProps = (state) => ({
  selectedTransaction: state.clients.selectedClientTransaction,
  selectedClient: state.clients.selectedClient,
  selectedAppointment: state.appointments.selectedAppointment,
});

export default connect(mapStateToProps, {
  getAppointmentAction,
})(CheckInDetails);
