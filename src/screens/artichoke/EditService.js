import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  updateServiceAction,
  getArchivedServicesAction,
  uploadServiceImageAction,
  setUploadedServiceImageAction,
} from '../../actions/artichoke/Services.actions';
import EditServiceForm from '../../components/artichoke/screensComponents/services/EditServiceForm';
import { APPLICATION_ROUTES } from '../../constants';
import { colors } from '../../styles';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { track } from '../../util/Analytics';
import { removeEmojis } from '../../util/utils';
import HeaderRightButton from '../../components/HeaderRightButton';
import { Platform } from 'react-native';

const formRef = createRef();

class EditServiceScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: APPLICATION_ROUTES.EDIT_SERVICES,
    headerRight: route.params?.saveService ?? (() => <View />),
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Cancel',
  });

  constructor(props) {
    super(props);
    this.state = {
      selectedService: null,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
      saveService: this.renderHeaderRight,
    });
  }

  componentDidUpdate() {
    if (this.props.service) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  onPressSave = () => {
    formRef.current.handleSubmit();
    track('service_edited');
  };

  onUpdateService = (service) => {
    const isClientLocations = !!this.props.selectedExtraLocations.filter(
      (item) => item.id === 0,
    ).length;
    const isRemoteLocations = !!this.props.selectedExtraLocations.filter(
      (item) => item.id === 1,
    ).length;
    const isInAppLocations = !!this.props.selectedExtraLocations.filter(
      (item) => item.id === 2,
    ).length;
    const locations = this.props.selectedLocations.filter(
      (item) => item.id !== 0,
    );
    const newService = {
      ...this.props.service,
      ...service,
      serviceImage: this.props.serviceImage,
      accountId: this.props.accountId,
      ProductDuration: [
        ...this.props.service.ProductDuration.slice(0, 0),
        {
          ...this.props.service.ProductDuration[0],
          duration: {
            ...this.props.service.ProductDuration[0].duration,
            duration: service.duration,
          },
          price: service.price,
        },
      ],
      clientLocationEnabled: isClientLocations,
      offeredOnline: isRemoteLocations,
      inappEnabled: isInAppLocations,
      selfBookingAddresses: locations,
      name: removeEmojis(service.name),
      description: removeEmojis(service.description),
    };
    delete newService.price;
    delete newService.duration;
    delete newService.booked;
    delete newService.commission;
    delete newService.demo;
    delete newService.productAddons;
    delete newService.productUserSettings;
    this.props.updateServiceAction(newService);
  };

  onUploadServiceImage = (data) => {
    this.props.uploadServiceImageAction(data);
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  goToLocations = () => {
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.LOCATIONS });
  };

  renderHeaderRight = () => {
    if (this.props.saveServiceLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.onPressSave()}
        title="Save"
        titleStyle={{
          color: colors.white,
        }}
      />
    );
  };

  render() {
    const service = {
      ...this.props.service,
      serviceImage: this.props.serviceImage,
      duration: this.props.service.ProductDuration[0].duration.duration,
      price: this.props.service.ProductDuration[0].price,
    };
    return (
      <KeyboardAwareScrollView
        resetScrollToCoords={{ x: 0, y: 0 }}
        contentContainerStyle={styles.container}
        scrollEnabled={false}
        extraHeight={100}
        extraScrollHeight={Platform.OS === 'ios' ? 0 : 40}
        enableOnAndroid
        enableAutomaticScroll
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.container}>
          <EditServiceForm
            refForm={formRef}
            initialValues={service}
            onSubmitForm={this.onUpdateService}
            navigation={this.props.navigation}
            selectedLocations={this.props.selectedLocations}
            selectedExtraLocations={this.props.selectedExtraLocations}
            onUploadServiceImage={this.onUploadServiceImage}
            setUploadedServiceImageAction={
              this.props.setUploadedServiceImageAction
            }
            saveServiceLoading={this.props.saveServiceLoading}
            symbol={this.props.symbol}
          />
        </View>
      </KeyboardAwareScrollView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  service: state.services.selectedService,
  serviceImage: state.services.serviceImage,
  selectedLocations: state.locations.selectedLocations,
  selectedExtraLocations: state.locations.selectedExtraLocations,
  saveServiceLoading: state.loadingComponents.saveServiceLoading,
  accountId: state.user.details.account.id,
  symbol: state.user.accountSettings?.symbol,
});
export default connect(mapStateToProps, {
  updateServiceAction,
  getArchivedServicesAction,
  uploadServiceImageAction,
  setUploadedServiceImageAction,
})(EditServiceScreen);
