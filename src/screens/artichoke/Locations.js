import React, { createRef, Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  FlatList,
  StyleSheet,
  View,
  ActivityIndicator,
} from 'react-native';
import AwesomeAlert from 'react-native-awesome-alerts';
import { colors } from '../../styles';
import { setServiceLocationsAction } from '../../actions/artichoke/Services.actions';
import {
  getAvailableLocationsAction,
  selectedLocationAction,
  clearSelectedLocationAction,
  clearSelectedExtraLocationAction,
  unselectedLocationAction,
  setSelectedLocationAction,
  setSelectedExtraLocationAction,
  getSelfBookingAction,
  setActiveServiceCreation,
} from '../../actions/artichoke/Locations.actions';
import EmptyLocationList from '../../components/artichoke/screensComponents/locations/EmptyLocationList';
import LocationListItem from '../../components/artichoke/screensComponents/locations/LocationListItem';
import { APPLICATION_ROUTES } from '../../constants';
import LoadingSpinner from '../../components/LoadingSpinner';
import AddButton from '../../components/artichoke/common/AddButton';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { setLoaderStateAction } from '../../actions/artichoke/Loaders.actions';
import { readFromStorage, saveToStorage } from '../../util/Storage.utils';
import { deleteAlertStyles } from '../../styles/alertStyle';
import FormGroup from '../../components/artichoke/common/FormGroup';
import FormSubmitButton from '../../components/artichoke/common/FormSubmitButton';
import HeaderRightButton from '../../components/HeaderRightButton';

const noLocationImage = require('../../assets/iconsGlobalAddLocation.png');

const locationActionSheetRef = createRef();

class LocationsScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Select Location(s)',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
    headerRight: route.params?.setProductLocations ?? (() => <View />),
  });

  constructor(props) {
    super(props);
    this.props.getAvailableLocationsAction();
    this.state = {
      loading: false,
      initialExtraLocations: this.props.selectedExtraLocations,
      selectedLocations: [],
      showAlert: false,
    };
    this.props.getSelfBookingAction();
    // this.props.clearSelectedLocationAction();
  }

  componentDidMount() {
    this.unsubscribeLocationFocus = this.props.navigation.addListener(
      'focus',
      () => {
        this.props.navigation.setParams({
          setProductLocations: this.renderHeaderRight,
          undoProductLocations: this.undoProductLocations,
          goBack: this.undoProductLocations,
        });
      },
    );
    const isClientLocations = !!this.props.selectedLocations.filter(
      (item) => item.id === 0,
    ).length;
    if (
      this.props.service
      && this.props.service.clientLocationEnabled
      && !isClientLocations
    ) {
      this.onSelectItem({ id: 0 });
    }
    this.setState({ selectedLocations: this.props.selectedLocations || [] });

    this.checkVideoCallAlert();
  }

  componentDidUpdate() {
    this.props.navigation.setOptions({
      headerRight:
        this.props.route?.params?.setProductLocations ?? (() => <View />),
    });
  }

  componentWillUnmount() {
    this.unsubscribeLocationFocus();
  }

  renderHeaderRight = () => {
    if (this.props.saveServiceLocationsLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.setProductLocations()}
        title="Done"
        titleStyle={{
          color: colors.white,
        }}
      />
    );
  };

  checkVideoCallAlert = async () => {
    const isVideoCallAlertShowed = await readFromStorage('showVideoCallAlert');
    this.setState({ showAlert: !isVideoCallAlertShowed });
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  undoProductLocations = () => {
    if (this.props.service) {
      const productAddresses = this.props.service?.selfBookingAddresses || [];
      const locations = productAddresses.map((location) => ({ id: location.id }));
      const extraLocations = [];
      if (this.props.service?.clientLocationEnabled) {
        extraLocations.push({ id: 0 });
      }
      if (this.props.service?.offeredOnline) {
        extraLocations.push({ id: 1 });
      }
      this.props.setSelectedExtraLocationAction(extraLocations);
      this.props.setSelectedLocationAction(locations);
    } else {
      this.props.setSelectedExtraLocationAction(
        this.state.initialExtraLocations,
      );
      this.props.setSelectedLocationAction(this.state.selectedLocations);
    }
    this.goBack();
  };

  setProductLocations = () => {
    this.props.setLoaderStateAction({
      key: 'saveServiceLocationsLoading',
      value: true,
    });
    const isClientLocations = !!this.props.selectedExtraLocations.filter(
      (item) => item.id === 0,
    ).length;
    const isRemoteLocations = !!this.props.selectedExtraLocations.filter(
      (item) => item.id === 1,
    ).length;
    const locations = this.props.selectedLocations.filter(
      (item) => item.id !== 0,
    );
    const obj = {
      clientLocationEnabled: isClientLocations,
      offeredOnline: isRemoteLocations,
      locationsList: locations,
    };
    this.props.setServiceLocationsAction(obj);
    if (this.props.service) {
      this.goBack();
    } else {
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.CREATE_SERVICE,
      });
    }
    this.props.setLoaderStateAction({
      key: 'saveServiceLocationsLoading',
      value: false,
    });
  };

  onSelectItem = (item) => {
    this.props.selectedLocationAction(item);
  };

  onUnselectItem = (item) => {
    this.props.unselectedLocationAction(item);
  };

  checkIsSelected = (item) => {
    if (item.id > 2) {
      return this.props.selectedLocations.filter(
        (objItem) => objItem.id === item.id,
      ).length;
    }
    return this.props.selectedExtraLocations.filter(
      (objItem) => objItem.id === item.id,
    ).length;
  };

  listAvailableLocations = () => {
    const items = [
      ...this.props.locationsList.filter((loc) => loc.checked === true),
    ];
    items.push({ id: 0, addressName: "Client's location" });
    items.push({ id: 1, addressName: 'Remotely' });
    // add video call as location
    items.push({ id: 2, addressName: 'Video Call' });

    return items;
  };

  createNewLocation = () => {
    this.props.setActiveServiceCreation(true);
    this.props.navigation.navigate({
      name: APPLICATION_ROUTES.CREATE_NEW_LOCATION,
      params: {
        previousScreen: this.props.route?.params?.previousScreen
          ? this.props.route?.params?.previousScreen
          : '',
      },
    });
    locationActionSheetRef.current?.setModalVisible(false);
  };

  enableLocation = () => {
    this.props.setActiveServiceCreation(true);
    this.props.navigation.navigate(APPLICATION_ROUTES.QUICK_ENABLE_LOCATION);
    locationActionSheetRef.current?.setModalVisible(false);
  };

  getSelectedLocations = (item) => this.props.selectedLocations.filter((objItem) => objItem.id === item.id)
    .length;

  renderAddLocationButton = () => (
    <AddButton
      onPressAction={() => {
        locationActionSheetRef.current?.setModalVisible(true);
      }}
    />
  );

  hideAlert = async () => {
    this.setState({ showAlert: false });
    await saveToStorage('showVideoCallAlert', true);
  };

  redirectToHoursTab = async () => {
    this.setState({ showAlert: false });
    await saveToStorage('showVideoCallAlert', true);
    this.props.navigation.navigate(APPLICATION_ROUTES.VIDEO_CALL_LOCATION, {
      returnRoute: APPLICATION_ROUTES.LOCATIONS,
    });
  };

  render() {
    const items = this.listAvailableLocations().reverse();
    const { showAlert } = this.state;

    return this.props.locationsLoading ? (
      <LoadingSpinner
        visible
        size="large"
        title="LOADING LOCATIONS..."
        // backgroundColor={'rgba(0, 0, 0, 0.25)'}
        backgroundColor={colors.white}
      />
    ) : (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={items}
          renderItem={({ item }) => (
            <LocationListItem
              item={item}
              selectItem={this.onSelectItem}
              unselectItem={this.onUnselectItem}
              isSelected={this.checkIsSelected(item)}
            />
          )}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={() => (
            <EmptyLocationList
              message="No Location Enabled"
              subTitle="Customize your list of locations by using the Hours tab, or add a new location tapping the “+” button below."
              hasIcon={noLocationImage}
            />
          )}
        />
        <View style={styles.footerList}>{this.renderAddLocationButton()}</View>
        <AwesomeAlert
          show={showAlert}
          showProgress={false}
          useNativeDriver
          title="Video Call Available"
          message="Sessions can now be hosted via video call. This feature can be enabled/disabled and work hours can be edited at any time in the Hours tab."
          closeOnTouchOutside
          closeOnHardwareBackPress
          showConfirmButton
          showCancelButton
          titleStyle={deleteAlertStyles.alertTitle}
          messageStyle={deleteAlertStyles.alertText}
          confirmText="Cancel"
          cancelText="Enable Video Call"
          confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
          confirmButtonStyle={deleteAlertStyles.alertButton}
          cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
          cancelButtonStyle={deleteAlertStyles.alertButton}
          actionContainerStyle={{
            flexDirection: 'column',
            backgroundColor: colors.white,
          }}
          contentContainerStyle={deleteAlertStyles.alertContainer}
          onConfirmPressed={() => {
            this.hideAlert();
          }}
          onCancelPressed={() => {
            this.hideAlert();
            this.redirectToHoursTab();
          }}
        />
        <CustomActionSheet actionSheetRef={locationActionSheetRef}>
          <FormGroup formStyle={styles.bottomSubmitButton}>
            <FormSubmitButton
              title="Enable Existing Location"
              onPressButton={() => this.enableLocation()}
              buttonStyle={{
                backgroundColor: colors.white,
                borderColor: colors.white,
                marginVertical: 15,
              }}
              buttonLabelStyle={{ color: colors.azure }}
            />
            <FormSubmitButton
              title="Create New Location"
              onPressButton={() => this.createNewLocation()}
              buttonStyle={{
                backgroundColor: colors.white,
                borderColor: colors.white,
              }}
              buttonLabelStyle={{ color: colors.azure }}
            />
          </FormGroup>
        </CustomActionSheet>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  footerList: {
    flex: 1,
    position: 'absolute', // Here is the trick
    bottom: 20, // Here is the trick
    right: 10,
    height: 70,
    flexGrow: 1,
  },
});

const mapStateToProps = (state) => ({
  locationsList: state.locations.locationsList,
  selectedLocations: state.locations.selectedLocations,
  selectedExtraLocations: state.locations.selectedExtraLocations,
  service: state.services.selectedService,
  selfBookings: state.locations.selfBookings,
  locationsLoading: state.loadingComponents.locationsLoading,
  saveServiceLocationsLoading:
    state.loadingComponents.saveServiceLocationsLoading,
});

const mapDispatchToProps = {
  getAvailableLocationsAction,
  selectedLocationAction,
  clearSelectedLocationAction,
  clearSelectedExtraLocationAction,
  unselectedLocationAction,
  setServiceLocationsAction,
  setSelectedLocationAction,
  setSelectedExtraLocationAction,
  getSelfBookingAction,
  setLoaderStateAction,
  setActiveServiceCreation,
};

export default connect(mapStateToProps, mapDispatchToProps)(LocationsScreen);
