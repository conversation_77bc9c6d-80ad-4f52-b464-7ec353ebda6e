import React, { Component } from 'react';
import {
  View, Text, TouchableOpacity, Image, Linking,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import DeviceInfo from 'react-native-device-info';
import { connect } from 'react-redux';
import { APPLICATION_ROUTES } from '../../constants';
import { colors } from '../../styles';
import { generalStyles } from '../../styles/generalStyle';
import { createLogoutAction } from '../../actions/artichoke/Login.actions';

const stripeNotConnected = require('../../assets/stripeGrey.png');
const stripeConnected = require('../../assets/stripeGreen.png');

class LearnMoreScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasNotch: false,
    };
  }

  componentDidMount() {
    this.hasNotch();
  }

  handleClick = () => {
    Linking.canOpenURL('https://dashboard.stripe.com/').then((supported) => {
      if (supported) {
        Linking.openURL('https://dashboard.stripe.com/');
      }
    });
  };

  handleLogout = () => {
    this.props.createLogoutAction();
  };

  hasNotch = () => {
    const hasNotch = DeviceInfo.hasNotch();
    hasNotch ? this.setState({ hasNotch }) : null;
  };

  renderAlreadyConnectedToStripeItems = () => (
    <TouchableOpacity
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: colors.lightgrey,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightgrey,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onPress={this.handleClick}
    >
      <Text style={{ flex: 1 }}>Payments</Text>
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          justifyContent: 'flex-end',
          alignItems: 'center',
        }}
      >
        <Image
          source={stripeConnected}
          style={{
            width: 18,
            height: 19,
            alignSelf: 'center',
            marginVertical: 0,
          }}
        />
        <Text
          style={{ color: colors.goodGreen, ...generalStyles.avenirRoman14 }}
        >
          {' '}
          Connected
        </Text>
        <IconFeader
          style={{ textAlign: 'right', marginLeft: 10 }}
          name="chevron-right"
          size={25}
          color={colors.bordergrey}
        />
      </View>
    </TouchableOpacity>
  );

  renderLogout = () => (
    <TouchableOpacity
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: colors.lightgrey,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightgrey,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onPress={() => this.handleLogout()}
    >
      <Text style={{ flex: 1 }}>Logout</Text>
      <IconFeader
        style={{ flex: 0.5, textAlign: 'right' }}
        name="chevron-right"
        size={25}
        color={colors.bordergrey}
      />
    </TouchableOpacity>
  );

  renderMenuItems = (item) => (
    <TouchableOpacity
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: colors.lightgrey,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightgrey,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onPress={() => this.props.navigation.navigate(item)}
    >
      <Text style={{ flex: 1 }}>{item}</Text>
      <IconFeader
        style={{ flex: 0.5, textAlign: 'right' }}
        name="chevron-right"
        size={25}
        color={colors.bordergrey}
      />
    </TouchableOpacity>
  );

  renderStripeItems = () => (
    <TouchableOpacity
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: colors.lightgrey,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightgrey,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onPress={() => this.props.navigation.navigate(APPLICATION_ROUTES.CONNECT_WITH_STRIPE)}
    >
      <Text style={{ flex: 0.5 }}>Payments</Text>
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          justifyContent: 'flex-end',
          alignItems: 'center',
        }}
      >
        <Image
          source={stripeNotConnected}
          style={{
            width: 18,
            height: 19,
            alignSelf: 'center',
            marginVertical: 0,
          }}
        />
        <Text
          style={{ color: colors.subGrey, ...generalStyles.avenirRoman14 }}
        >
          {' '}
          Connect with Stripe
        </Text>
        <IconFeader
          style={{ textAlign: 'right', marginLeft: 10 }}
          name="chevron-right"
          size={25}
          color={colors.bordergrey}
        />
      </View>
    </TouchableOpacity>
  );

  render() {
    const arrMenuItems = [APPLICATION_ROUTES.ARCHIVED_SERVICES];
    return (
      <View
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'flex-start',
          marginTop: this.state.hasNotch ? 100 : 70,
        }}
      >
        {this.props.user.account.StripeAccount
        && this.props.user.account.StripeAccount.stripeSecretKey
          ? this.renderAlreadyConnectedToStripeItems()
          : this.renderStripeItems()}
        {arrMenuItems.map((item) => this.renderMenuItems(item))}
        {this.renderLogout()}
      </View>
    );
  }
}

const mapStateToProps = (state) => ({
  user: state.user.details,
});

export default connect(mapStateToProps, { createLogoutAction })(
  LearnMoreScreen,
);
