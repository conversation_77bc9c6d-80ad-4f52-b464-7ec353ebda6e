import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, StyleSheet, TouchableOpacity } from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import _cloneDeep from 'lodash.clonedeep';
import { colors } from '../../styles';
import CreateBookingForm from '../../components/artichoke/screensComponents/booked/CreateBookingForm';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import {
  setNewAppointmentValuesAction,
  saveAppointmentAction,
  clearNewAppointment,
  clearSelectedAppointment,
} from '../../actions/artichoke/Appointments.actions';
import { getAvailableLocationsAction } from '../../actions/artichoke/Locations.actions';
import { APPLICATION_ROUTES, FREQUENCY_SHORT } from '../../constants';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const repeatActionSheetRef = createRef();

class CreateAppointment extends Component {
  constructor(props) {
    super(props);
    this.props.getAvailableLocationsAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
    this.props.clearNewAppointment();
    this.props.clearSelectedAppointment();
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.newAppointment.service
      && this.props.newAppointment.service?.id
      && prevProps.newAppointment.service?.id
        !== this.props.newAppointment.service.id
    ) {
      this.mergeWorkHours(this.props.newAppointment.service);
      this.preselectOneLocation(this.props.newAppointment.service);
    }
  }

  onChangeRepeatType = (repeat) => {
    // eslint-disable-next-line @babel/no-unused-expressions
    repeat
      ? this.props.navigation.navigate({
        name: APPLICATION_ROUTES.RECURRENCE_SCREEN,
        params: {
          returnRoute: APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT,
        },
      })
      : this.onDoesNotRepeat();
  };

  onDoesNotRepeat = () => {
    this.props.setNewAppointmentValuesAction({
      key: 'repeatIntervalType',
      value: null,
    });
    this.props.setNewAppointmentValuesAction({ key: 'untilDay', value: null });
    this.props.setNewAppointmentValuesAction({ key: 'count', value: 0 });
    this.props.setNewAppointmentValuesAction({
      key: 'repeatUntilType',
      value: null,
    });
  };

  static navigationOptions = ({ navigation }) => ({
    title: 'Session',
    headerLeft: () => <HeaderLeftButton onPress={() => navigation.goBack()} />,
    headerBackTitle: 'Back',
  });

  mergeWorkHours = (service) => {
    const items = service.selfBookingAddresses;
    const savedSelfBookingAddresses = this.props.selfBookings?.selfBookingAddresses;
    if (items && items.length > 0) {
      items.forEach((obj1) => {
        if (savedSelfBookingAddresses && savedSelfBookingAddresses.length > 0) {
          savedSelfBookingAddresses.forEach((obj2) => {
            if (obj1.id === obj2.id) {
              obj1.workHours = obj2.workHours;
            }
          });
        }
      });
    }
  };

  preselectOneLocation = (service) => {
    const items = service.selfBookingAddresses
      ? _cloneDeep(service.selfBookingAddresses)
      : [];
    if (service.clientLocationEnabled) {
      items.push({
        id: 0,
        addressName: "Client's location",
        checked: true,
        workHours: JSON.stringify(
          JSON.parse(this.props.selfBookings?.workHours).offsite,
        ),
      });
    }
    if (service.offeredOnline) {
      items.push({
        id: 1,
        addressName: 'Remotely',
        checked: true,
        workHours: JSON.stringify(
          JSON.parse(this.props.selfBookings?.workHours).remote,
        ),
      });
    }
    if (service.inappEnabled) {
      items.push({
        id: 2,
        addressName: 'Video Call',
        checked: true,
      });
    }
    if (items.length === 1) {
      this.props.setNewAppointmentValuesAction({
        key: 'appointmentLocation',
        value: items[0],
        workHours: JSON.stringify(
          JSON.parse(this.props.selfBookings?.workHours).inapp,
        ),
      });
    }
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  saveAppointment = () => {
    this.props.saveAppointmentAction();
  };

  render() {
    const repeatTypeShort = this.props.newAppointment.repeatIntervalType
      ? 'Custom'
      : null;
    return (
      <SafeAreaView style={styles.container}>
        <CreateBookingForm
          repeatActionSheetRef={repeatActionSheetRef}
          initialValues={this.props.newAppointment}
          setNewAppointmentValuesAction={
            this.props.setNewAppointmentValuesAction
          }
          onSaveAppointment={this.saveAppointment}
          navigation={this.props.navigation}
          symbol={this.props.symbol}
          isLoading={this.props.saveSessionLoading}
        />
        <CustomActionSheet actionSheetRef={repeatActionSheetRef} title="Repeat">
          <RadioForm>
            {FREQUENCY_SHORT.map((obj, i) => (
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  this.onChangeRepeatType(obj.value);
                  repeatActionSheetRef.current?.setModalVisible(false);
                }}
              >
                <RadioButton
                  labelHorizontal
                  style={{
                    paddingHorizontal: 30,
                    paddingVertical: 20,
                    flex: 1,
                    borderBottomWidth: 1,
                    borderBottomColor: colors.lightgrey,
                  }}
                >
                  {/*  You can set RadioButtonLabel before RadioButtonInput */}
                  <RadioButtonInput
                    obj={obj}
                    index={i}
                    isSelected={repeatTypeShort === obj.value}
                    onPress={() => {
                      this.onChangeRepeatType(obj.value);
                      repeatActionSheetRef.current?.setModalVisible(false);
                    }}
                    borderWidth={1}
                    buttonSize={10}
                    buttonOuterSize={20}
                    buttonStyle={{ borderColor: colors.subGrey }}
                    buttonWrapStyle={{ marginLeft: 73 }}
                  />
                  <RadioButtonLabel
                    obj={obj}
                    index={i}
                    labelHorizontal
                    onPress={() => {
                      this.onChangeRepeatType(obj.value);
                      repeatActionSheetRef.current?.setModalVisible(false);
                    }}
                    labelStyle={{
                      ...generalStyles.fontBold,
                      color: colors.black,
                    }}
                    labelWrapStyle={{}}
                  />
                </RadioButton>
              </TouchableOpacity>
            ))}
          </RadioForm>
        </CustomActionSheet>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newAppointment: state.appointments.newAppointmentValues,
  selectedAppointment: state.appointments.selectedAppointment,
  selfBookings: state.locations.selfBookings,
  saveSessionLoading: state.loadingComponents.saveSessionLoading,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  setNewAppointmentValuesAction,
  saveAppointmentAction,
  clearNewAppointment,
  clearSelectedAppointment,
  getAvailableLocationsAction,
})(CreateAppointment);
