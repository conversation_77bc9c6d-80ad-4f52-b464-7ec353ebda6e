import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, FlatList, StyleSheet } from 'react-native';
import {
  setUpdatedAccountSettingsAction,
  saveAccountSettingsAction,
} from '../../actions/artichoke/User.actions';
import { HeaderSearchBar } from '../../components';
import { APPLICATION_ROUTES, CURRENCIES } from '../../constants';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import CurrencyCountryListItem from '../../components/artichoke/screensComponents/login/CurrencyCountryListItem';

class ListCurrencies extends Component {
  static navigationOptions = ({ route }) => ({
    title: APPLICATION_ROUTES.CURRENCY_SELECTION,
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      filteredCurrencyList: CURRENCIES,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  onSelectCurrency = (item) => {
    this.props.setUpdatedAccountSettingsAction({
      key: 'newCurrencyCountry',
      value: item.country,
    });
    this.props.setUpdatedAccountSettingsAction({
      key: 'newCurrency',
      value: item.value,
    });
    this.props.saveAccountSettingsAction();
    this.goBack();
  };

  checkSelectedCurrency = (item) => item.value === this.props.account.currency;

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = CURRENCIES.filter((item) => {
      const labelData = `${item.label.toUpperCase()}`;
      const textData = text.toUpperCase();
      return labelData.indexOf(textData) > -1;
    });
    this.setState({
      filteredCurrencyList: newData,
    });
  };

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Search"
    />
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.state.filteredCurrencyList}
          renderItem={({ item }) => (
            <CurrencyCountryListItem
              item={item}
              isSelected={this.checkSelectedCurrency(item)}
              onSelectItem={this.onSelectCurrency}
            />
          )}
          keyExtractor={(item) => item.label.toString()}
          ListHeaderComponent={this.renderHeader}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  account: state.user.accountSettings,
});

export default connect(mapStateToProps, {
  setUpdatedAccountSettingsAction,
  saveAccountSettingsAction,
})(ListCurrencies);
