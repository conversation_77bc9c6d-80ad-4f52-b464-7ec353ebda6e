import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
} from 'react-native';
import AwesomeAlert from 'react-native-awesome-alerts';
// eslint-disable-next-line import/no-unresolved
import * as StripeFunctions from '@stripe/stripe-react-native/src/functions';
import { colors } from '../../styles';
import { APPLICATION_ROUTES, PAY_METHODS } from '../../constants';
import ViewPaymentClientItem from '../../components/artichoke/screensComponents/services/ViewPaymentClientItem';
import CardCashOptionItem from '../../components/artichoke/screensComponents/booked/CardCashOptionItem';
import FormPayWith from '../../components/artichoke/common/FormPayWith';
import DiscountItem from '../../components/artichoke/common/DiscountItem';
import TotalDetailsItemPackage from '../../components/artichoke/common/TotalDetailsItemPackage';
import TotalDetailsItem from '../../components/artichoke/common/TotalDetailsItem';
import {
  buyPackageWithCashOrCheckAction,
  buyServiceWithCashOrCheckAction,
  clearSuccessAction,
  buyServiceWithCreditCardAction,
  buyPackageWithCreditCardAction,
} from '../../actions/artichoke/Appointments.actions';
import FormCreditCardInfo from '../../components/artichoke/common/FormCreditCardInfo';
import {
  clearPurchase,
  saveClientStripeCreditCard,
} from '../../actions/artichoke/Clients.actions';
import { generalStyles } from '../../styles/generalStyle';
import { successAlertStyles } from '../../styles/alertStyle';
import SubTotalDetailsItem from '../../components/artichoke/common/SubTotalDetailsItem';
import SubTotalDetailsItemPackage from '../../components/artichoke/common/SubTotalDetailsItemPackage';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { LoadingSpinner } from '../../components';
import FormSubmitButtonWithSpinner from '../../components/artichoke/common/FormSubmitButtonWithSpinner';
import { track } from '../../util/Analytics';

const successIcon = require('../../assets/imgAlertCheckmark.png');
const stripeNotConnected = require('../../assets/stripeGrey.png');
const poweredByStripeBadge = require('../../assets/stripeLogo.png');

class PackagePaymentsScreen extends Component {
  static navigationOptions = ({ route }) => ({
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      payMethod: PAY_METHODS.DEBIT_OR_CC,
      useBalance: true,
      valid: false,
      params: {
        number: '',
        expMonth: 0,
        expYear: 0,
        cvc: '',
      },
      showAlert: false,
      validatingCard: false,
      overDiscount: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  componentDidUpdate(prevProps) {
    if (prevProps.success !== this.props.success) {
      if (this.props.success) {
        this.showAlert();
      }
    }
    if (this.props.selectedPackage) {
      this.props.navigation.setOptions({
        title: 'Sell Package',
      });
    } else {
      this.props.navigation.setOptions({
        title: 'Sell Service',
      });
    }
  }

  onSelectPay = (value) => {
    this.setState({ payMethod: value });
  };

  onUnselectPay = () => {
    const selectedPaymentMethod = this.state.payMethod;
    if (selectedPaymentMethod === PAY_METHODS.DEBIT_OR_CC) {
      this.setState({ payMethod: PAY_METHODS.CASH_OR_CHECK });
    } else {
      this.setState({ payMethod: PAY_METHODS.DEBIT_OR_CC });
    }
  };

  goBack = () => {
    this.props.clearPurchase();
    this.props.navigation.goBack();
  };

  handleCreditCardParamsChange = (valid, params) => {
    this.setState({
      valid,
      params,
    });
  };

  hideAlert = () => {
    this.setState({
      showAlert: false,
    });
  };

  overDiscountAlert = (value) => {
    if (!this.state.overDiscount && !value) {
      this.setState({
        overDiscount: true,
      });
      Alert.alert(
        'Info',
        'You are offering a discount greater than the price. The final amount will be 0',
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: true },
      );
    } else if (this.state.overDiscount && value) {
      this.setState({
        overDiscount: false,
      });
    }
  };

  saveCCDetails = async () => {
    const params = {
      type: 'Card',
    };

    this.setState({ validatingCard: true });
    try {
      const { token, error } = await StripeFunctions.createToken(params);
      if (error) {
        throw error;
      }
      this.props.saveClientStripeCreditCard(token.id);
    } catch (e) {
      Alert.alert(
        'Error',
        e.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    this.setState({ validatingCard: false });
  };

  showAlert = () => {
    this.setState({
      showAlert: true,
    });
  };

  renderButtons = () => (
    <View style={styles.bottomSubmitButton}>
      {this.state.payMethod === PAY_METHODS.DEBIT_OR_CC ? (
        <FormSubmitButtonWithSpinner
          style={{ width: '100%', marginHorizontal: 0, marginTop: 15 }}
          title="Confirm Card Payment"
          onPressButton={() => {
            if (this.props.selectedPackage) {
              this.props.buyPackageWithCreditCardAction();
              track('package_purchase');
            } else {
              this.props.buyServiceWithCreditCardAction(async () => {});
              track('service_purchase');
            }
          }}
          isLoading={this.props.confirmPaymentLoading}
        />
      ) : (
        <FormSubmitButtonWithSpinner
          style={{ width: '100%', marginHorizontal: 0, marginTop: 15 }}
          title="Confirm Cash Payment"
          onPressButton={() => {
            if (this.props.selectedPackage) {
              this.props.buyPackageWithCashOrCheckAction();
              track('package_purchase');
            } else {
              this.props.buyServiceWithCashOrCheckAction();
              track('service_purchase');
            }
          }}
          isLoading={this.props.confirmPaymentLoading}
        />
      )}
    </View>
  );

  renderContent = () => {
    if (this.props.selectedPackage?.price === 0) {
      return this.renderFreePackage();
    }
    if (
      this.props.selectedService?.ProductDuration
      && this.props.selectedService?.ProductDuration.length
      && this.props.selectedService?.ProductDuration[0].price === '0.0'
    ) {
      return this.renderFreePackage();
    }
    if (this.state.payMethod === PAY_METHODS.DEBIT_OR_CC) {
      if (
        this.props.user.account.StripeAccount
        && this.props.user.account.StripeAccount.stripeSecretKey
      ) {
        if (
          this.props.clientCreditCard
          && this.props.clientCreditCard.creditCardType
          && this.props.clientCreditCard.lastFour
        ) {
          return this.renderPaymentContent();
        }
        return this.renderStripeConnectedWithoutCreditCard();
      }
      return this.renderStripeNotConnected();
    }
    return this.renderPaymentContent();
  };

  renderFreePackage = () => {
    const price = 0;
    const tax = 0;
    const priceWithTax = 0;
    const discount = 0;
    const product = this.props.selectedPackage
      ? this.props.selectedPackage
      : this.props.selectedService;
    return (
      <>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <ViewPaymentClientItem
              product={product}
              clientInfo={this.props.selectedClient}
            />
          </View>
        </ScrollView>
        <View style={styles.bottom}>
          <SubTotalDetailsItemPackage
            price={price}
            tax={tax}
            priceWithTax={priceWithTax}
            discount={discount}
            usageStyle={{ paddingHorizontal: 0 }}
            symbol={this.props.symbol}
          />
          <TotalDetailsItemPackage
            symbol={this.props.symbol}
            price={price}
            tax={tax}
            priceWithTax={priceWithTax}
            discount={discount}
            overDiscountAlert={this.overDiscountAlert}
          />
        </View>
        <FormSubmitButtonWithSpinner
          style={{ width: '95%', alignSelf: 'center' }}
          title="Confirm"
          onPressButton={() => {
            if (this.props.selectedPackage) {
              this.props.buyPackageWithCashOrCheckAction();
              track('package_purchase');
            } else {
              this.props.buyServiceWithCashOrCheckAction();
              track('service_purchase');
            }
          }}
          isLoading={this.props.confirmPaymentLoading}
        />
      </>
    );
  };

  renderPaymentContent() {
    return this.props.selectedPackage ? (
      <>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <ViewPaymentClientItem
              product={this.props.selectedPackage}
              clientInfo={this.props.selectedClient}
            />
            {this.renderPaymentLayout()}
          </View>
          <SubTotalDetailsItemPackage
            price={this.props.selectedPackage.price}
            tax={this.props.selectedPackage.tax}
            priceWithTax={this.props.selectedPackage.priceWithTax}
            discount={this.props.clientsPurchase.discount}
            symbol={this.props.symbol}
          />
        </ScrollView>
        <View style={styles.bottom}>
          <TotalDetailsItemPackage
            symbol={this.props.symbol}
            price={this.props.selectedPackage.price}
            tax={this.props.selectedPackage.tax}
            priceWithTax={this.props.selectedPackage.priceWithTax}
            discount={this.props.clientsPurchase.discount}
            overDiscountAlert={this.overDiscountAlert}
          />
          {this.state.payMethod === PAY_METHODS.DEBIT_OR_CC ? (
            <View
              style={{
                ...styles.bottom,
                alignItem: 'center',
                justifyContent: 'center',
                paddingTop: 0,
                marginBottom: 0,
              }}
            >
              <Image
                source={poweredByStripeBadge}
                style={{
                  width: 119,
                  height: 27,
                  alignSelf: 'center',
                  marginVertical: 0,
                }}
              />
            </View>
          ) : null}
          {this.renderButtons()}
        </View>
      </>
    ) : (
      <>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <ViewPaymentClientItem
              product={this.props.selectedService}
              clientInfo={this.props.selectedClient}
            />
            {this.renderPaymentLayout()}
          </View>
          <SubTotalDetailsItem
            price={this.props.selectedService.ProductDuration[0].price}
            tax={this.props.selectedService.tax}
            discount={this.props.clientsPurchase.discount}
            symbol={this.props.symbol}
          />
        </ScrollView>
        <View style={styles.bottom}>
          <TotalDetailsItem
            price={this.props.selectedService.ProductDuration[0].price}
            tax={this.props.selectedService.tax}
            discount={this.props.clientsPurchase.discount}
            symbol={this.props.symbol}
            overDiscountAlert={this.overDiscountAlert}
          />
          {this.state.payMethod === PAY_METHODS.DEBIT_OR_CC ? (
            <View
              style={{
                ...styles.bottom,
                alignItem: 'center',
                justifyContent: 'center',
                paddingTop: 0,
                paddingBottom: 0,
              }}
            >
              <Image
                source={poweredByStripeBadge}
                style={{
                  width: 119,
                  height: 27,
                  alignSelf: 'center',
                  marginTop: 0,
                  marginBottom: 15,
                }}
              />
            </View>
          ) : null}
          {this.renderButtons()}
        </View>
      </>
    );
  }

  renderPaymentLayout = () => (
    <View>
      <View>
        <Text style={styles.label}>Payment</Text>
        <CardCashOptionItem
          payMethod={this.state.payMethod}
          onSelect={this.onSelectPay}
          onUnselect={this.onUnselectPay}
        />
      </View>
      {this.state.payMethod !== '' ? (
        <View>
          {this.state.payMethod === PAY_METHODS.DEBIT_OR_CC ? (
            <View style={{ marginTop: 12 }}>
              <FormCreditCardInfo
                value={`${this.props.clientCreditCard.creditCardType} ...${this.props.clientCreditCard.lastFour}`}
                onPressAction={() => this.props.navigation.navigate(
                  APPLICATION_ROUTES.ADD_CREDIT_CARD,
                )}
              />
            </View>
          ) : null}
          <Text style={styles.label}>Adjustments</Text>
          <DiscountItem
            selectedDiscount={this.props.clientsPurchase}
            symbol={this.props.symbol}
            selectDiscount={() => this.props.navigation.navigate({
              name: APPLICATION_ROUTES.DISCOUNTS,
              params: {
                returnRoute: APPLICATION_ROUTES.PAYMENTS,
              },
            })}
          />
        </View>
      ) : null}
    </View>
  );

  renderStripeConnectedWithoutCreditCard() {
    return this.props.selectedPackage ? (
      <>
        <ScrollView>
          <View style={styles.form}>
            <ViewPaymentClientItem
              product={this.props.selectedPackage}
              clientInfo={this.props.selectedClient}
            />
            <View style={{ marginBottom: 12 }}>
              <Text style={styles.label}>Payment</Text>
              <CardCashOptionItem
                payMethod={this.state.payMethod}
                onSelect={this.onSelectPay}
                onUnselect={this.onUnselectPay}
              />
            </View>
            <FormPayWith
              onChangeCreditCardInfo={this.handleCreditCardParamsChange}
            />
          </View>
        </ScrollView>
        <View
          style={{
            ...styles.bottom,
            alignItem: 'center',
            justifyContent: 'center',
          }}
        >
          <Image
            source={poweredByStripeBadge}
            style={{ width: 119, height: 27, alignSelf: 'center' }}
          />
          <View style={styles.bottomSubmitButton}>
            <FormSubmitButtonWithSpinner
              buttonStyle={
                this.state.valid ? styles.payButton : styles.disabledButton
              }
              style={{ width: '100%', marginHorizontal: 0, marginTop: 48 }}
              title="Validate"
              disabled={
                !this.state.valid
                || this.state.validatingCard
                || this.props.savingClientCard
              }
              onPressButton={this.saveCCDetails}
              isLoading={this.props.validateCardLoader}
            />
          </View>
        </View>
      </>
    ) : (
      <>
        <ScrollView>
          <View style={styles.form}>
            <ViewPaymentClientItem
              product={this.props.selectedService}
              clientInfo={this.props.selectedClient}
            />
            <View style={{ marginBottom: 12 }}>
              <Text style={styles.label}>Payment</Text>
              <CardCashOptionItem
                payMethod={this.state.payMethod}
                onSelect={this.onSelectPay}
                onUnselect={this.onUnselectPay}
              />
            </View>
            <FormPayWith
              onChangeCreditCardInfo={this.handleCreditCardParamsChange}
            />
          </View>
        </ScrollView>
        <View
          style={{
            ...styles.bottom,
            alignItem: 'center',
            justifyContent: 'center',
          }}
        >
          <Image
            source={poweredByStripeBadge}
            style={{ width: 119, height: 27, alignSelf: 'center' }}
          />
          <View style={styles.bottomSubmitButton}>
            <FormSubmitButtonWithSpinner
              buttonStyle={
                this.state.valid ? styles.payButton : styles.disabledButton
              }
              style={{ width: '100%', marginHorizontal: 0, marginTop: 48 }}
              disabled={
                !this.state.valid
                || this.state.validatingCard
                || this.props.savingClientCard
              }
              title="Validate"
              onPressButton={this.saveCCDetails}
              isLoading={this.props.validateCardLoader}
            />
          </View>
        </View>
      </>
    );
  }

  renderStripeNotConnected() {
    return this.props.selectedPackage ? (
      <>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <ViewPaymentClientItem
              product={this.props.selectedPackage}
              clientInfo={this.props.selectedClient}
            />
            <View>
              <View>
                <Text style={styles.label}>Payment</Text>
                <CardCashOptionItem
                  payMethod={this.state.payMethod}
                  onSelect={this.onSelectPay}
                  onUnselect={this.onUnselectPay}
                />
              </View>
              <TouchableOpacity
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  width: '100%',
                  borderTopColor: colors.lightgrey,
                  borderBottomColor: colors.lightgrey,
                  paddingVertical: 20,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => this.props.navigation.navigate(
                  APPLICATION_ROUTES.CONNECT_WITH_STRIPE,
                )}
              >
                <Text style={{ flex: 0.5 }}>Payments</Text>
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                  }}
                >
                  <Image
                    source={stripeNotConnected}
                    style={{
                      width: 18,
                      height: 19,
                      alignSelf: 'center',
                      marginVertical: 0,
                    }}
                  />
                  <Text
                    style={{
                      color: colors.subGrey,
                      ...generalStyles.avenirRoman14,
                    }}
                  >
                    {' '}
                    Connect with Stripe
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <View
          style={{
            ...styles.bottom,
            alignItem: 'center',
            justifyContent: 'center',
          }}
        >
          <Image
            source={poweredByStripeBadge}
            style={{ width: 119, height: 27, alignSelf: 'center' }}
          />
        </View>
      </>
    ) : (
      <>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <ViewPaymentClientItem
              product={this.props.selectedService}
              clientInfo={this.props.selectedClient}
            />
            <View>
              <View>
                <Text style={styles.label}>Payment</Text>
                <CardCashOptionItem
                  payMethod={this.state.payMethod}
                  onSelect={this.onSelectPay}
                  onUnselect={this.onUnselectPay}
                />
              </View>
              <TouchableOpacity
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  width: '100%',
                  borderTopColor: colors.lightgrey,
                  borderBottomColor: colors.lightgrey,
                  paddingVertical: 20,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => this.props.navigation.navigate(
                  APPLICATION_ROUTES.CONNECT_WITH_STRIPE,
                )}
              >
                <Text style={{ flex: 0.5 }}>Payments</Text>
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                  }}
                >
                  <Image
                    source={stripeNotConnected}
                    style={{
                      width: 18,
                      height: 19,
                      alignSelf: 'center',
                      marginVertical: 0,
                    }}
                  />
                  <Text
                    style={{
                      color: colors.subGrey,
                      ...generalStyles.avenirRoman14,
                    }}
                  >
                    {' '}
                    Connect with Stripe
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <View
          style={{
            ...styles.bottom,
            alignItem: 'center',
            justifyContent: 'center',
          }}
        >
          <Image
            source={poweredByStripeBadge}
            style={{ width: 119, height: 27, alignSelf: 'center' }}
          />
        </View>
      </>
    );
  }

  render() {
    return !this.props.showBuyProductsLoading ? (
      <SafeAreaView style={styles.container}>
        {this.renderContent()}
        {this.props.success ? (
          <AwesomeAlert
            show={this.state.showAlert}
            showProgress={false}
            useNativeDriver
            closeOnTouchOutside={false}
            closeOnHardwareBackPress={false}
            showConfirmButton
            confirmText="Continue"
            confirmButtonTextStyle={successAlertStyles.alertButtonText}
            confirmButtonStyle={successAlertStyles.alertButton}
            actionContainerStyle={{
              flexDirection: 'column',
              backgroundColor: colors.white,
              borderRadius: 5,
            }}
            contentContainerStyle={successAlertStyles.alertContainer}
            customView={(
              <View style={successAlertStyles.alertContainer}>
                <Image source={successIcon} style={{ marginTop: 35 }} />
                <Text style={successAlertStyles.alertTitle}>
                  {this.props.success.title}
                </Text>
                <Text style={successAlertStyles.alertText}>
                  {this.props.success.message}
                </Text>
              </View>
            )}
            onConfirmPressed={() => {
              this.hideAlert();
              this.props.clearSuccessAction();
              this.props.navigation.navigate(APPLICATION_ROUTES.DASHBOARD);
            }}
          />
        ) : null}
      </SafeAreaView>
    ) : (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    marginTop: 48,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 38,
  },
  bottom: {
    width: '100%',
    display: 'flex',
    alignSelf: 'flex-end',
    flexDirection: 'column',
    paddingBottom: 38,
    paddingHorizontal: 20,
  },
  form: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  label: {
    ...generalStyles.avenirHeavy17,
    paddingTop: 24,
    color: colors.black,
  },
  payButton: {
    display: 'flex',
    width: '100%',
    borderColor: colors.duskBlue,
    backgroundColor: colors.duskBlue,
    textAlign: 'center',
    justifyContent: 'center',
    borderRadius: 28,
    borderWidth: 1,
    height: 55,
    marginHorizontal: 0,
  },
  disabledButton: {
    display: 'flex',
    width: '100%',
    borderColor: colors.bordergrey,
    backgroundColor: colors.lightgrey,
    textAlign: 'center',
    justifyContent: 'center',
    borderRadius: 28,
    borderWidth: 1,
    height: 55,
    // marginTop: 48,
    marginHorizontal: 0,
  },
});

const mapStateToProps = (state) => ({
  selectedService: state.services.selectedService,
  selectedPackage: state.services.selectedPackage,
  selectedClient: state.clients.selectedClient,
  clientsPurchase: state.clients.clientsPurchase,
  clientCreditCard: state.clients.selectedClientCreditCard,
  user: state.user.details,
  success: state.appointments.success,
  purchasing: state.appointments.purchasing,
  savingClientCard: state.clients.savingClientCard,
  showBuyProductsLoading: state.loadingComponents.showBuyProductsLoading,
  confirmPaymentLoading: state.loadingComponents.confirmPaymentLoading,
  validateCardLoader: state.loadingComponents.validateCardLoader,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  clearPurchase,
  buyPackageWithCashOrCheckAction,
  buyServiceWithCashOrCheckAction,
  buyServiceWithCreditCardAction,
  buyPackageWithCreditCardAction,
  saveClientStripeCreditCard,
  clearSuccessAction,
})(PackagePaymentsScreen);
