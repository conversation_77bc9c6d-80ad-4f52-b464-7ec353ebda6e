import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, FlatList, StyleSheet } from 'react-native';
import { colors } from '../../styles';
import {
  setNewAppointmentValuesAction,
  setFilteredInviteeAction,
  checkInFreeAction,
  uncheckinAction,
} from '../../actions/artichoke/Appointments.actions';
import {
  getClientBalance,
  setSelectedClient,
  clearPurchase,
  getClientCreditCard,
} from '../../actions/artichoke/Clients.actions';
import ClientCheckInListItem from '../../components/artichoke/screensComponents/booked/ClientCheckInListItem';
import EmptyServiceList from '../../components/artichoke/screensComponents/booked/EmptyServiceList';
import { HeaderSearchBar } from '../../components';
import { APPLICATION_ROUTES } from '../../constants';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import LoadingSpinner from '../../components/LoadingSpinner';

class MultipleCheckIn extends Component {
  static navigationOptions = ({ navigation }) => ({
    title: 'Check In',
    headerLeft: () => <HeaderLeftButton onPress={() => navigation.goBack()} />,
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.checkInCashOrCheckLoading
      !== this.props.checkInCashOrCheckLoading
    ) {
      this.props.setFilteredInviteeAction(this.props.clientsList);
    }
  }

  onPressCheckInClient = (item) => {
    this.props.clearPurchase();
    this.props.setSelectedClient(item);
    if (parseInt(this.props.newAppointment.ProductDuration.price, 10) > 0) {
      this.props.getClientBalance(item.Client.id);
      this.props.getClientCreditCard(item.Client.id);
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.PAYMENTS,
      });
    } else {
      this.props.checkInFreeAction();
    }
  };

  onPressUnCheckInClient = (item) => {
    this.props.uncheckinAction({ id: item.Client.id });
  };

  checkIsCheckedIn = (e) => e.checkedIn === 'true';

  searchFilterFunction = (text) => {
    this.setState({
      value: text,
    });
    const newData = this.props.clientsList.filter((item) => {
      const itemData = `${item.Client.user.firstName.toUpperCase()} ${item.Client.user.lastName.toUpperCase()}`;
      const textData = text.toUpperCase();
      return itemData.indexOf(textData) > -1;
    });
    this.props.setFilteredInviteeAction(newData);
  };

  renderHeader = () => (
    <HeaderSearchBar
      searchText={this.state.value}
      onChangeText={(text) => this.searchFilterFunction(text)}
      clearable
      paddingTop={10}
      light
      shadow={false}
      placeholder="Client First Name"
    />
  );

  render() {
    return this.props.checkInCashOrCheckLoading ? (
      <LoadingSpinner
        visible
        size="large"
        title="CHECKING IN CLIENT..."
        backgroundColor={colors.white}
      />
    ) : (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={this.props.filteredClientsList}
          renderItem={({ item }) => (
            <ClientCheckInListItem
              item={item}
              isCheckedIn={this.checkIsCheckedIn(item)}
              onPressCheckInClient={() => this.onPressCheckInClient(item)}
              onPressUnCheckInClient={() => this.onPressUnCheckInClient(item)}
            />
          )}
          keyExtractor={(item) => item.id}
          refreshing={this.props.clientListLoading}
          ListEmptyComponent={() => (
            <EmptyServiceList message="No Client Found" />
          )}
          ListHeaderComponent={
            this.props.filteredClientsList.length > 1 ? this.renderHeader : null
          }
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  clientsList: state.appointments.selectedAppointment.Invitee,
  filteredClientsList: state.appointments.filteredInvitee,
  newAppointment: state.appointments.selectedAppointment,
  checkInCashOrCheckLoading: state.loadingComponents.checkInCashOrCheckLoading,
});

export default connect(mapStateToProps, {
  setFilteredInviteeAction,
  setNewAppointmentValuesAction,
  getClientBalance,
  setSelectedClient,
  clearPurchase,
  getClientCreditCard,
  checkInFreeAction,
  uncheckinAction,
})(MultipleCheckIn);
