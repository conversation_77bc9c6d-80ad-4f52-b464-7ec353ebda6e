import React, { Component } from 'react';
import { connect } from 'react-redux';
import { View, StyleSheet, Platform } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  createServiceAction,
  clearSelectedServiceAction,
  uploadServiceImageAction,
  setUploadedServiceImageAction,
} from '../../actions/artichoke/Services.actions';
import {
  clearSelectedLocationAction,
  clearSelectedExtraLocationAction,
} from '../../actions/artichoke/Locations.actions';
import CreateServiceForm from '../../components/artichoke/screensComponents/services/CreateServiceForm';
import { APPLICATION_ROUTES } from '../../constants';
import { track } from '../../util/Analytics';
import { removeEmojis } from '../../util/utils';

class CreateServiceScreen extends Component {
  constructor(props) {
    super(props);
    this.props.clearSelectedServiceAction();
    this.props.clearSelectedLocationAction();
    this.props.clearSelectedExtraLocationAction();
  }

  onSubmitService = (service) => {
    const isClientLocations = !!this.props.selectedExtraLocations.filter(
      (item) => item.id === 0,
    ).length;
    const isRemoteLocations = !!this.props.selectedExtraLocations.filter(
      (item) => item.id === 1,
    ).length;
    const isInAppLocations = !!this.props.selectedExtraLocations.filter(
      (item) => item.id === 2,
    ).length;
    const locations = this.props.selectedLocations.filter(
      (item) => item.id !== 0,
    );
    const newService = {
      id: '',
      name: removeEmojis(service.name),
      accountId: '',
      productType: {
        id: '0',
        name: null,
      },
      accountingReferenceNumber: '',
      tax: service.tax,
      active: true,
      description: removeEmojis(service.description),
      retailPrice: null,
      wholesalePrice: null,
      isService: true,
      bookOnlineEnabled: service.bookOnlineEnabled,
      onlyAsSchedule: false,
      offeredOnline: isRemoteLocations,
      inappEnabled: isInAppLocations,
      maxParticipants: service.maxParticipants,
      ProductDuration: [
        {
          productId: '',
          active: true,
          name: service.name,
          price: service.price,
          duration: {
            duration: service.duration,
            granularity: {
              granularity: 'minutes',
              abbreviation: 'min',
            },
          },
        },
      ],
      clientLocationEnabled: isClientLocations,
      selfBookingAddresses: locations,
      serviceImage: this.props.serviceImage,
    };
    let navScreen = '';
    if (this.props.route?.params?.previousScreen) {
      navScreen = this.props.route?.params?.previousScreen;
    } else {
      navScreen = APPLICATION_ROUTES.SERVICES;
    }
    this.props.createServiceAction({
      service: newService,
      routeName: navScreen,
    });
    track('service_created');
  };

  onUploadServiceImage = (data) => {
    this.props.uploadServiceImageAction(data);
  };

  goToLocations = () => {
    this.props.navigation.navigate({ name: APPLICATION_ROUTES.LOCATIONS });
  };

  render() {
    return (
      <KeyboardAwareScrollView
        resetScrollToCoords={{ x: 0, y: 0 }}
        contentContainerStyle={styles.container}
        scrollEnabled={false}
        extraHeight={100}
        extraScrollHeight={Platform.OS === 'ios' ? 0 : 40}
        enableOnAndroid
        enableAutomaticScroll
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.container}>
          <CreateServiceForm
            onSubmitForm={this.onSubmitService}
            navigation={this.props.navigation}
            selectedLocations={this.props.selectedLocations}
            selectedExtraLocations={this.props.selectedExtraLocations}
            saveServiceLoading={this.props.saveServiceLoading}
            symbol={this.props.symbol}
            initialValues={this.props.service}
            onUploadServiceImage={this.onUploadServiceImage}
            setUploadedServiceImageAction={
              this.props.setUploadedServiceImageAction
            }
          />
        </View>
      </KeyboardAwareScrollView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  service: state.services.selectedService,
  serviceImage: state.services.serviceImage,
  selectedLocations: state.locations.selectedLocations,
  selectedExtraLocations: state.locations.selectedExtraLocations,
  saveServiceLoading: state.loadingComponents.saveServiceLoading,
  symbol: state.user.accountSettings?.symbol,
});

const mapDispatchToProps = {
  createServiceAction,
  clearSelectedServiceAction,
  clearSelectedLocationAction,
  clearSelectedExtraLocationAction,
  uploadServiceImageAction,
  setUploadedServiceImageAction,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(CreateServiceScreen);
