import React, { Component, createRef } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { connect } from 'react-redux';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import { colors } from '../../styles';
import {
  setSelectedDiscountForEditValuesAction,
  saveSelectedDiscountForEditAction,
  saveDiscountAction,
  deleteSelectedDiscountForEditAction,
} from '../../actions/artichoke/Clients.actions';
import EditDiscountForm from '../../components/artichoke/screensComponents/payments/EditDiscountForm';
import { discount_type_props } from '../../constants';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { isNumber } from '../../util/validate';
import { track } from '../../util/Analytics';
import HeaderRightButton from '../../components/HeaderRightButton';

const discountTypeActionSheetRef = createRef();

class EditDiscountScreen extends Component {
  static navigationOptions = ({ route, navigation }) => ({
    title: route.params.discountId ? 'Edit discount' : 'New Discount',
    headerLeft: () => <HeaderLeftButton onPress={() => navigation?.goBack()} />,
    headerBackTitle: 'Back',
  });

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.navigation.setParams({
        onSave: this.onSave,
      });
    });
  }

  componentDidUpdate() {
    if (this.props.discount) {
      this.props.navigation.setOptions({
        title: this.changePageTitle(this.props.discount),
        headerRight: () => this.changeRight(this.props.discount),
      });
    }
  }

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
  }

  onDelete = () => {
    this.props.deleteSelectedDiscountForEditAction();
    this.goBack();
  };

  onSave = () => {
    this.props.saveSelectedDiscountForEditAction();
    this.goBack();
    track('discount_edited');
  };

  onSaveNewDiscount = () => {
    this.props.saveDiscountAction();
    this.goBack();
  };

  changePageTitle = (value) => {
    let title = 'New Discount';
    if (
      this.props.route.params
      && this.props.route.params.discountId
      && value.name
      && value.id
      && value.id !== 0
      && value.type
    ) {
      title = `${value.type === 'PERCENTAGE' ? '' : this.props.symbol}${
        value.amount
      }${value.type === 'PERCENTAGE' ? '%' : ''} - ${value.name}`;
    }
    return title.length > 25 ? `${title.slice(0, 25)}...` : title;
  };

  checkValidAmount = (value) => {
    let isValid;
    if (
      this.props.route?.params?.discountId
      && value?.name
      && value?.id
      && value?.type
      && value?.amount
      && isNumber(value.amount)
    ) {
      isValid = true;
    }
    return isValid;
  };

  changeRight = (value) => {
    if (!this.checkValidAmount(value)) {
      return null;
    }
    return this.renderHeaderRight();
  };

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={this.onSave}
      title="Save"
      titleStyle={styles.headerText}
    />
  );

  goBack = () => {
    this.props.navigation.goBack();
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <EditDiscountForm
          initialValues={this.props.discount}
          onSubmitForm={this.onSaveNewDiscount}
          onDelete={this.onDelete}
          symbol={this.props.symbol}
          setSelectedDiscountForEditValuesAction={
            this.props.setSelectedDiscountForEditValuesAction
          }
          onOpenDiscountType={() => discountTypeActionSheetRef.current?.setModalVisible(true)}
          isInEdit={
            this.props.route.params
            && this.props.route.params.discountId
            && this.props.discount.name
            && this.props.discount.id
            && this.props.discount.id !== 0
            && this.props.discount.type
          }
        />
        <CustomActionSheet
          actionSheetRef={discountTypeActionSheetRef}
          title="Type"
        >
          <RadioForm>
            {discount_type_props.map((obj, i) => (
              <RadioButton
                labelHorizontal
                key={i}
                style={{
                  paddingHorizontal: 30,
                  paddingVertical: 20,
                  flex: 1,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.lightgrey,
                }}
              >
                {/*  You can set RadioButtonLabel before RadioButtonInput */}
                <RadioButtonInput
                  obj={obj}
                  index={i}
                  isSelected={this.props.discount.type === obj.value}
                  onPress={() => {
                    this.props.setSelectedDiscountForEditValuesAction({
                      key: 'type',
                      value: obj.value,
                    });
                    discountTypeActionSheetRef.current?.setModalVisible(false);
                  }}
                  buttonStyle={{}}
                  buttonWrapStyle={{ justifyContent: 'center' }}
                  borderWidth={1}
                  buttonSize={10}
                  buttonOuterSize={20}
                />
                <RadioButtonLabel
                  obj={obj}
                  index={i}
                  labelHorizontal
                  onPress={() => {
                    this.props.setSelectedDiscountForEditValuesAction({
                      key: 'type',
                      value: obj.value,
                    });
                    discountTypeActionSheetRef.current?.setModalVisible(false);
                  }}
                  labelStyle={generalStyles.fontBold}
                  labelWrapStyle={{}}
                />
              </RadioButton>
            ))}
          </RadioForm>
        </CustomActionSheet>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
});

const mapStateToProps = (state) => ({
  discount: state.clients.selectedDiscount,
  symbol: state.user.accountSettings?.symbol,
});

const mapDispatchToProps = {
  setSelectedDiscountForEditValuesAction,
  saveSelectedDiscountForEditAction,
  saveDiscountAction,
  deleteSelectedDiscountForEditAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(EditDiscountScreen);
