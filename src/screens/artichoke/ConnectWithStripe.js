import React, { Component } from 'react';
import { WebView } from 'react-native-webview';
import { connect } from 'react-redux';
import { BASE_URL, STRIPE_CALLBACK_URLS, STRIPE_KEY } from '../../apiConstants';
import { getUserDataAction } from '../../actions/artichoke/User.actions';

class ConnectWithStripeScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      oldUrl: '',
    };
  }

  onNavigationStateChange = (e) => {
    if (e.url !== this.state.oldUrl) {
      if (e.url.includes(BASE_URL) || e.url.includes(STRIPE_CALLBACK_URLS)) {
        // we have to wait a bit until api update user data
        setTimeout(() => {
          this.props.getUserDataAction();
          this.props.navigation.navigate('BottomTab', { screen: 'Account' });
        }, 1500);
      }
      this.setState({ oldUrl: e.url });
    }
  };

  render() {
    const isStripeConnected = Boolean(
      this.props.user?.account?.StripeAccount?.stripeSecretKey,
    );
    const uri = isStripeConnected
      ? 'https://dashboard.stripe.com/'
      : `https://connect.stripe.com/oauth/authorize?response_type=code&client_id=${STRIPE_KEY}&scope=read_write&state=${this.props.user.id}`;
    return (
      <WebView
        source={{
          uri,
        }}
        androidHardwareAccelerationDisabled
        useWebKit
        style={{ marginTop: 20 }}
        onNavigationStateChange={this.onNavigationStateChange}
        cacheEnabled={false}
      />
    );
  }
}

const mapStateToProps = (state) => ({
  user: state.user.details,
});

export default connect(mapStateToProps, { getUserDataAction })(
  ConnectWithStripeScreen,
);
