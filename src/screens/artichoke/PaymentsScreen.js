/* eslint-disable no-nested-ternary */
import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
} from 'react-native';
import AwesomeAlert from 'react-native-awesome-alerts';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
// eslint-disable-next-line import/no-unresolved
import * as StripeFunctions from '@stripe/stripe-react-native/src/functions';
import { colors } from '../../styles';
import { APPLICATION_ROUTES, PAY_METHODS } from '../../constants';
import ViewClientPayItem from '../../components/artichoke/screensComponents/booked/ViewClientPayItem';
import CardCashOptionItem from '../../components/artichoke/screensComponents/booked/CardCashOptionItem';
import FormPayWith from '../../components/artichoke/common/FormPayWith';
import DiscountItem from '../../components/artichoke/common/DiscountItem';
import TotalDetailsItem from '../../components/artichoke/common/TotalDetailsItem';
import {
  getAppointmentAction,
  checkInWithBalanceAction,
  checkInWithCashOrCheckAction,
  checkInWithCreditCardAction,
  clearSuccessAction,
} from '../../actions/artichoke/Appointments.actions';
import {
  clearPurchase,
  saveClientStripeCreditCard,
} from '../../actions/artichoke/Clients.actions';
import FormBalance from '../../components/artichoke/common/FormBalance';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import { generalStyles } from '../../styles/generalStyle';
import { successAlertStyles } from '../../styles/alertStyle';
import SubTotalDetailsItem from '../../components/artichoke/common/SubTotalDetailsItem';
import FormCreditCardInfo from '../../components/artichoke/common/FormCreditCardInfo';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import FormSubmitButtonWithSpinner from '../../components/artichoke/common/FormSubmitButtonWithSpinner';

const successIcon = require('../../assets/imgAlertCheckmark.png');
const stripeNotConnected = require('../../assets/stripeGrey.png');
const poweredByStripeBadge = require('../../assets/stripeLogo.png');

const selectBalanceActionSheetRef = createRef();

class PaymentScreen extends Component {
  static navigationOptions = ({ route }) => ({
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      payMethod: PAY_METHODS.DEBIT_OR_CC,
      useBalance: true,
      valid: false,
      params: {
        number: '',
        expMonth: 0,
        expYear: 0,
        cvc: '',
      },
      showAlert: false,
      validatingCard: false,
      overDiscount: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  componentDidUpdate(prevProps) {
    if (this.props.newAppointment) {
      this.props.navigation.setOptions({
        title: 'Payments',
      });
    }
    console.log('I got here', prevProps);
    if (
      prevProps.newAppointment.ProductDuration.price
        < prevProps.clientsPurchase.discount
      && this.props.newAppointment.ProductDuration.price
        > this.props.clientsPurchase.discount
    ) {
      Alert.alert(
        'Info',
        'You are offering a discount greater than the price. The final amount will be 0',
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: true },
      );
    }
    if (prevProps.success !== this.props.success) {
      if (this.props.success) {
        this.showAlert();
      }
    }
  }

  handleCreditCardParamsChange = (valid, params) => {
    this.setState({
      valid,
      params,
    });
  };

  overDiscountAlert = (value) => {
    if (!this.state.overDiscount && !value) {
      this.setState({
        overDiscount: true,
      });
      Alert.alert(
        'Info',
        'You are offering a discount greater than the price. The final amount will be 0',
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: true },
      );
    } else if (this.state.overDiscount && value) {
      this.setState({
        overDiscount: false,
      });
    }
  };

  saveCCDetails = async () => {
    const params = {
      type: 'Card',
    };

    this.setState({ validatingCard: true });
    try {
      const { token, error } = await StripeFunctions.createToken(params);
      if (error) {
        throw error;
      }
      this.props.saveClientStripeCreditCard(token.id);
    } catch (e) {
      Alert.alert(
        'Error',
        e.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    this.setState({ validatingCard: false });
  };

  onSelectPay = (value) => {
    this.setState({ payMethod: value });
  };

  showAlert = () => {
    this.setState({
      showAlert: true,
    });
  };

  hideAlert = () => {
    this.setState({
      showAlert: false,
    });
  };

  onUnselectPay = () => {
    const selectedPaymentMethod = this.state.payMethod;
    if (selectedPaymentMethod === PAY_METHODS.DEBIT_OR_CC) {
      this.setState({ payMethod: PAY_METHODS.CASH_OR_CHECK });
    } else {
      this.setState({ payMethod: PAY_METHODS.DEBIT_OR_CC });
    }
  };

  goBack = () => {
    this.props.clearPurchase();
    this.props.navigation.goBack();
  };

  renderPaymentLayout = () => (this.checkAvailableBalance() === null || !this.state.useBalance ? (
    <View>
      <View>
        <Text style={styles.label}>Payment</Text>
        <CardCashOptionItem
          payMethod={this.state.payMethod}
          onSelect={this.onSelectPay}
          onUnselect={this.onUnselectPay}
        />
      </View>
      {this.state.payMethod !== '' ? (
        <View>
          {this.state.payMethod === 'DEBIT_OR_CC' ? (
            <View style={{ marginTop: 12 }}>
              <FormCreditCardInfo
                value={`${this.props.clientCreditCard.creditCardType} ...${this.props.clientCreditCard.lastFour}`}
              />
            </View>
          ) : null}
          <Text style={styles.label}>Adjustments</Text>
          <DiscountItem
            selectedDiscount={this.props.clientsPurchase}
            symbol={this.props.symbol}
            selectDiscount={() => this.props.navigation.navigate({
              name: APPLICATION_ROUTES.DISCOUNTS,
              params: {
                returnRoute: APPLICATION_ROUTES.PAYMENTS,
              },
            })}
          />
        </View>
      ) : null}
    </View>
  ) : null);

  onChangeBalanceOption = (value) => {
    this.setState({ useBalance: value });
    selectBalanceActionSheetRef.current?.setModalVisible(false);
  };

  renderBalance = () => {
    const balance = this.checkAvailableBalance();
    if (balance !== null) {
      const balanceValue = this.state.useBalance
        ? balance.quantity > 100
          ? 'Unlimited'
          : `${balance.quantity} left`
        : 'Do not use balance';
      const radio_props = [];
      radio_props.push({
        label: `${balance.quantity} left - ${balance.ProductDuration.name}`,
        value: balance.quantity,
      });
      radio_props.push({ label: 'Do not use balance', value: false });
      return (
        <View>
          <Text style={styles.label}>Available Balance</Text>
          <FormBalance
            label="Balance"
            onPressFunc={() => {
              selectBalanceActionSheetRef.current?.setModalVisible();
            }}
            value={balanceValue}
          />
          <CustomActionSheet
            actionSheetRef={selectBalanceActionSheetRef}
            title="Select a Balance"
          >
            <RadioForm>
              {radio_props.map((obj, i) => (
                <RadioButton
                  labelHorizontal
                  key={i}
                  style={{
                    paddingHorizontal: 30,
                    paddingVertical: 20,
                    flex: 1,
                    borderBottomWidth: 1,
                    borderBottomColor: colors.lightgrey,
                  }}
                >
                  {/*  You can set RadioButtonLabel before RadioButtonInput */}
                  <RadioButtonInput
                    obj={obj}
                    index={i}
                    isSelected={
                      obj.value === this.state.useBalance
                      || (obj.value && this.state.useBalance)
                    }
                    onPress={() => this.onChangeBalanceOption(obj.value)}
                    buttonStyle={{}}
                    buttonWrapStyle={{ justifyContent: 'center' }}
                    borderWidth={1}
                    buttonSize={10}
                    buttonOuterSize={20}
                  />
                  <RadioButtonLabel
                    obj={obj}
                    index={i}
                    labelHorizontal
                    onPress={() => this.onChangeBalanceOption(obj.value)}
                    labelStyle={generalStyles.fontBold}
                    labelWrapStyle={{}}
                  />
                </RadioButton>
              ))}
            </RadioForm>
          </CustomActionSheet>
        </View>
      );
    }
    return null;
  };

  checkAvailableBalance = () => {
    let availableBalance = null;
    if (
      this.props.clientBalance !== 'undefined'
      && this.props.clientBalance.length !== 0
    ) {
      this.props.clientBalance.map((item) => {
        item.clientPurchaseProductDurations.map(
          (clientPurchaseProductDuration) => {
            if (
              clientPurchaseProductDuration.quantity !== '0'
              && clientPurchaseProductDuration.ProductDuration.id
                === this.props.selectedAppointment.ProductDuration.id
            ) {
              availableBalance = clientPurchaseProductDuration;
            }
            return true;
          },
        );
        return true;
      });
    }
    return availableBalance;
  };

  renderButtons = () => {
    if (this.checkAvailableBalance() === null || !this.state.useBalance) {
      return (
        <View style={styles.bottomSubmitButton}>
          {this.state.payMethod === PAY_METHODS.DEBIT_OR_CC ? (
            <FormSubmitButtonWithSpinner
              buttonStyle={{ width: '100%', marginHorizontal: 0 }}
              disabled={this.props.purchasing}
              title="Confirm Card Payment"
              onPressButton={() => this.props.checkInWithCreditCardAction()}
              isLoading={this.props.checkInCreditCardLoading}
              testID="ConfirmCardPayment"
            />
          ) : (
            <FormSubmitButtonWithSpinner
              buttonStyle={{ width: '100%', marginHorizontal: 0 }}
              disabled={this.props.purchasing}
              title="Confirm Cash Payment"
              onPressButton={() => this.props.checkInWithCashOrCheckAction()}
              isLoading={this.props.checkInCashOrCheckLoading}
              testID="ConfirmCardPayment"
            />
          )}
        </View>
      );
    }
    return (
      <FormSubmitButtonWithSpinner
        buttonStyle={{ width: '100%', marginHorizontal: 0 }}
        disabled={this.props.purchasing}
        title="Check-in Client"
        onPressButton={() => this.props.checkInWithBalanceAction()}
        isLoading={this.props.checkInBalancesLoading}
      />
    );
  };

  renderPaymentContent() {
    return this.props.selectedAppointment ? (
      <SafeAreaView style={styles.container}>
        {this.props.success ? (
          <AwesomeAlert
            show={this.state.showAlert}
            showProgress={false}
            useNativeDriver
            closeOnTouchOutside={false}
            closeOnHardwareBackPress={false}
            showConfirmButton
            confirmText="Continue"
            confirmButtonTextStyle={successAlertStyles.alertButtonText}
            confirmButtonStyle={successAlertStyles.alertButton}
            actionContainerStyle={{
              flexDirection: 'column',
              backgroundColor: colors.white,
              borderRadius: 5,
            }}
            contentContainerStyle={successAlertStyles.alertContainer}
            customView={(
              <View style={successAlertStyles.alertContainer}>
                <Image source={successIcon} style={{ marginTop: 35 }} />
                <Text style={successAlertStyles.alertTitle}>
                  {this.props.success.title}
                </Text>
                <Text style={successAlertStyles.alertText}>
                  {this.props.success.message}
                </Text>
              </View>
            )}
            onConfirmPressed={() => {
              this.hideAlert();
              this.props.clearSuccessAction();
              if (this.props.newAppointment.Invitee.length > 1) {
                this.props.navigation.navigate(
                  APPLICATION_ROUTES.MULTIPLE_CHECK_IN_CLIENT,
                );
              } else {
                this.props.navigation.navigate(
                  APPLICATION_ROUTES.INDIVIDUAL_APPOINTMENT,
                );
              }
            }}
          />
        ) : null}
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <ViewClientPayItem
              appointment={this.props.selectedAppointment}
              clientInfo={
                this.props.selectedAppointment.Invitee.length === 1
                  ? this.props.selectedAppointment.Invitee[0]
                  : this.props.selectedClient
              }
            />
            {this.renderBalance()}
            {this.renderPaymentLayout()}
          </View>
        </ScrollView>
        <SubTotalDetailsItem
          price={this.props.newAppointment.ProductDuration.price}
          tax={this.props.newAppointment.ProductDuration.productTax}
          priceWithTax={this.props.newAppointment.ProductDuration.priceWithTax}
          discount={this.props.clientsPurchase.discount}
          balance={this.checkAvailableBalance()}
          useBalance={this.state.useBalance}
          symbol={this.props.symbol}
        />
        <View style={styles.bottom}>
          <TotalDetailsItem
            price={this.props.newAppointment.ProductDuration.price}
            tax={this.props.newAppointment.ProductDuration.productTax}
            priceWithTax={
              this.props.newAppointment.ProductDuration.priceWithTax
            }
            discount={this.props.clientsPurchase.discount}
            balance={this.checkAvailableBalance()}
            useBalance={this.state.useBalance}
            symbol={this.props.symbol}
            overDiscountAlert={this.overDiscountAlert}
          />
          {this.state.payMethod === PAY_METHODS.DEBIT_OR_CC
          && !this.state.useBalance ? (
            <View
              style={{
                ...styles.bottom,
                alignItem: 'center',
                justifyContent: 'center',
                paddingTop: 0,
                marginBottom: 0,
                paddingBottom: 0,
              }}
            >
              <Image
                source={poweredByStripeBadge}
                style={{
                  width: 119,
                  height: 27,
                  alignSelf: 'center',
                  marginVertical: 0,
                  marginBottom: 24,
                }}
              />
            </View>
            ) : null}
          {this.renderButtons()}
        </View>
      </SafeAreaView>
    ) : null;
  }

  renderStripeNotConnected() {
    return this.props.selectedAppointment ? (
      <SafeAreaView style={styles.container}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <ViewClientPayItem
              appointment={this.props.selectedAppointment}
              clientInfo={
                this.props.selectedAppointment.Invitee.length === 1
                  ? this.props.selectedAppointment.Invitee[0]
                  : this.props.selectedClient
              }
            />
            {this.renderBalance()}
            <View>
              <View>
                <Text style={styles.label}>Payment</Text>
                <CardCashOptionItem
                  payMethod={this.state.payMethod}
                  onSelect={this.onSelectPay}
                  onUnselect={this.onUnselectPay}
                />
              </View>
              <TouchableOpacity
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  width: '100%',
                  borderTopColor: colors.lightgrey,
                  borderBottomColor: colors.lightgrey,
                  paddingVertical: 20,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => this.props.navigation.navigate(
                  APPLICATION_ROUTES.CONNECT_WITH_STRIPE,
                )}
              >
                <Text style={{ flex: 0.5 }}>Payments</Text>
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                  }}
                >
                  <Image
                    source={stripeNotConnected}
                    style={{
                      width: 18,
                      height: 19,
                      alignSelf: 'center',
                      marginVertical: 0,
                    }}
                  />
                  <Text
                    style={{
                      color: colors.subGrey,
                      ...generalStyles.avenirRoman14,
                    }}
                  >
                    {' '}
                    Connect with Stripe
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <View
          style={{
            ...styles.bottom,
            alignItem: 'center',
            justifyContent: 'center',
          }}
        >
          <Image
            source={poweredByStripeBadge}
            style={{ width: 120, height: 25, alignSelf: 'center' }}
          />
        </View>
      </SafeAreaView>
    ) : null;
  }

  renderStripeConnectedWithoutCreditCard() {
    return this.props.selectedAppointment ? (
      <SafeAreaView style={styles.container}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <ViewClientPayItem
              appointment={this.props.selectedAppointment}
              clientInfo={
                this.props.selectedAppointment.Invitee.length === 1
                  ? this.props.selectedAppointment.Invitee[0]
                  : this.props.selectedClient
              }
            />
            {this.renderBalance()}
            <View>
              <View style={{ marginBottom: 12 }}>
                <Text style={styles.label}>Payment</Text>
                <CardCashOptionItem
                  payMethod={this.state.payMethod}
                  onSelect={this.onSelectPay}
                  onUnselect={this.onUnselectPay}
                />
              </View>
              <FormPayWith
                onChangeCreditCardInfo={this.handleCreditCardParamsChange}
              />
            </View>
          </View>
        </ScrollView>
        <View
          style={{
            ...styles.bottom,
            alignItem: 'center',
            justifyContent: 'center',
          }}
        >
          <Image
            source={poweredByStripeBadge}
            style={{ width: 120, height: 25, alignSelf: 'center' }}
          />
          <View style={styles.bottomSubmitButton}>
            <FormSubmitButtonWithSpinner
              disabled={
                !this.state.valid
                || this.state.validatingCard
                || this.props.savingClientCard
              }
              buttonStyle={
                this.state.valid ? styles.payButton : styles.disabledButton
              }
              style={{ width: '100%', marginHorizontal: 0, marginTop: 48 }}
              title="Validate"
              onPressButton={this.saveCCDetails}
              isLoading={this.props.validateCardLoader}
            />
          </View>
        </View>
      </SafeAreaView>
    ) : null;
  }

  render() {
    if (this.state.payMethod === PAY_METHODS.DEBIT_OR_CC) {
      if (
        this.props.user.account.StripeAccount
        && this.props.user.account.StripeAccount.stripeSecretKey
      ) {
        if (
          this.props.clientCreditCard
          && this.props.clientCreditCard.creditCardType
          && this.props.clientCreditCard.lastFour
        ) {
          return this.renderPaymentContent();
        }
        if (this.checkAvailableBalance() !== null && this.state.useBalance) {
          return this.renderPaymentContent();
        }
        return this.renderStripeConnectedWithoutCreditCard();
      }
      return this.renderStripeNotConnected();
    }
    return this.renderPaymentContent();
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 48,
    marginBottom: 38,
  },
  bottom: {
    width: '100%',
    display: 'flex',
    alignSelf: 'flex-end',
    flexDirection: 'column',
    paddingBottom: 38,
    paddingHorizontal: 20,
  },
  form: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  label: {
    ...generalStyles.avenirHeavy17,
    paddingTop: 24,
    color: colors.black,
  },
  payButton: {
    display: 'flex',
    width: '100%',
    borderColor: colors.duskBlue,
    backgroundColor: colors.duskBlue,
    textAlign: 'center',
    justifyContent: 'center',
    borderRadius: 28,
    borderWidth: 1,
    height: 55,
    // marginTop: 48,
    marginHorizontal: 0,
  },
  disabledButton: {
    display: 'flex',
    width: '100%',
    borderColor: colors.bordergrey,
    backgroundColor: colors.lightgrey,
    textAlign: 'center',
    justifyContent: 'center',
    borderRadius: 28,
    borderWidth: 1,
    height: 55,
    // marginTop: 48,
    marginHorizontal: 0,
  },
});

const mapStateToProps = (state) => ({
  selectedAppointment: state.appointments.selectedAppointment,
  selectedClient: state.clients.selectedClient,
  clientBalance: state.clients.clientBalance,
  newAppointment: state.appointments.selectedAppointment,
  clientsPurchase: state.clients.clientsPurchase,
  user: state.user.details,
  clientCreditCard: state.clients.selectedClientCreditCard,
  success: state.appointments.success,
  purchasing: state.appointments.purchasing,
  savingClientCard: state.clients.savingClientCard,
  checkInCreditCardLoading: state.loadingComponents.checkInCreditCardLoading,
  checkInCashOrCheckLoading: state.loadingComponents.checkInCashOrCheckLoading,
  checkInBalancesLoading: state.loadingComponents.checkInBalances,
  validateCardLoader: state.loadingComponents.validateCardLoader,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  getAppointmentAction,
  clearPurchase,
  checkInWithBalanceAction,
  checkInWithCashOrCheckAction,
  saveClientStripeCreditCard,
  checkInWithCreditCardAction,
  clearSuccessAction,
})(PaymentScreen);
