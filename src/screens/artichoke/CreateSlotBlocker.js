import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import {
  Keyboard,
  SafeAreaView,
  StyleSheet,
  ActivityIndicator,
  View,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import { colors } from '../../styles';
import CreateSlotBlockerForm from '../../components/artichoke/screensComponents/slotblockers/CreateSlotBlockerForm';
import CustomActionSheet from '../../components/artichoke/common/CustomActionSheet';
import {
  setNewSlotBlockerValuesAction,
  saveSlotBlockerAction,
  clearSelectedSlotBlocker,
} from '../../actions/artichoke/Appointments.actions';
import { APPLICATION_ROUTES, FREQUENCY_SHORT } from '../../constants';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { track } from '../../util/Analytics';
import HeaderRightButton from '../../components/HeaderRightButton';

const repeatActionSheetRef = createRef();

class CreateSlotBlocker extends Component {
  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Add New Slot Blocker',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      keyboardOpen: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
      renderHeaderRight: this.renderHeaderRight,
    });
    this.props.clearSelectedSlotBlocker();
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      this.keyboardDidShow,
    );
    this.keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      this.keyboardDidHide,
    );
    this.props.navigation.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
  }

  componentDidUpdate() {
    if (this.props.newSlotBlocker) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
  }

  onChangeRepeatType = (repeat) => {
    if (repeat) {
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.RECURRENCE_SCREEN_SLOT_BLOCKER,
        params: {
          returnRoute: APPLICATION_ROUTES.CREATE_NEW_SLOTBLOCKER,
        },
      });
    } else {
      this.onDoesNotRepeat();
    }
  };

  onDoesNotRepeat = () => {
    this.props.setNewSlotBlockerValuesAction({
      key: 'repeatIntervalType',
      value: null,
    });
    this.props.setNewSlotBlockerValuesAction({ key: 'untilDay', value: '' });
    this.props.setNewSlotBlockerValuesAction({ key: 'count', value: 0 });
    this.props.setNewSlotBlockerValuesAction({
      key: 'repeatUntilType',
      value: null,
    });
  };

  keyboardDidHide = () => {
    this.setState({
      keyboardOpen: false,
    });
  };

  keyboardDidShow = () => {
    this.setState({
      keyboardOpen: true,
    });
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  saveSlotBlocker = () => {
    this.props.saveSlotBlockerAction();
    track('slotblocker_created');
  };

  renderHeaderLeft = () => <HeaderLeftButton onPress={() => this.goBack()} />;

  renderHeaderRight = () => {
    if (this.props.saveSlotBlockerLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    if (this.props.route.params.completed === 'true') {
      return (
        <HeaderRightButton
          onPress={() => this.saveSlotBlocker()}
          title="Save"
          titleStyle={{
            color: colors.white,
            margin: 15,
          }}
        />
      );
    }
    return <View />;
  };

  render() {
    const repeatTypeShort = this.props.newSlotBlocker.repeatIntervalType
      ? 'Custom'
      : null;
    return (
      <SafeAreaView style={styles.container}>
        <CreateSlotBlockerForm
          initialValues={this.props.newSlotBlocker}
          setNewSlotBlockerValuesAction={
            this.props.setNewSlotBlockerValuesAction
          }
          repeatActionSheetRef={repeatActionSheetRef}
          // onSaveAppointment={this.saveAppointment}
          navigation={this.props.navigation}
          keyboardOpen={this.state.keyboardOpen}
        />
        <CustomActionSheet actionSheetRef={repeatActionSheetRef} title="Repeat">
          <RadioForm>
            {FREQUENCY_SHORT.map((obj, i) => (
              <RadioButton
                labelHorizontal
                key={i}
                style={{
                  paddingHorizontal: 30,
                  paddingVertical: 20,
                  flex: 1,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.lightgrey,
                }}
              >
                {/*  You can set RadioButtonLabel before RadioButtonInput */}
                <RadioButtonInput
                  obj={obj}
                  index={i}
                  isSelected={repeatTypeShort === obj.value}
                  onPress={() => {
                    this.onChangeRepeatType(obj.value);
                    repeatActionSheetRef.current?.setModalVisible(false);
                  }}
                  borderWidth={1}
                  buttonSize={10}
                  buttonOuterSize={20}
                  buttonStyle={{ borderColor: colors.subGrey }}
                  buttonWrapStyle={{ marginLeft: 73 }}
                />
                <RadioButtonLabel
                  obj={obj}
                  index={i}
                  labelHorizontal
                  onPress={() => {
                    this.onChangeRepeatType(obj.value);
                  }}
                  labelStyle={{
                    ...generalStyles.fontBold,
                    color: colors.black,
                  }}
                  labelWrapStyle={{}}
                />
              </RadioButton>
            ))}
          </RadioForm>
        </CustomActionSheet>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newSlotBlocker: state.appointments.newSlotBlocker,
  saveSlotBlockerLoading: state.loadingComponents.saveSlotBlockerLoading,
});

export default connect(mapStateToProps, {
  setNewSlotBlockerValuesAction,
  saveSlotBlockerAction,
  clearSelectedSlotBlocker,
})(CreateSlotBlocker);
