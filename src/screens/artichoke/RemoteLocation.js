import React, { Component, createRef } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Alert,
  ActivityIndicator,
  View,
} from 'react-native';
import { connect } from 'react-redux';
import LoadingSpinner from '../../components/LoadingSpinner';
import { colors } from '../../styles';
import RemoteLocationForm from '../../components/artichoke/screensComponents/hoursLocations/RemoteLocationForm';
import {
  getAvailableLocationsAction,
  getSelfBookingAction,
  setRemoteLocationValuesAction,
  saveRemoteLocationAction,
} from '../../actions/artichoke/Locations.actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import { track } from '../../util/Analytics';
import { generalStyles } from '../../styles/generalStyle';
import HeaderRightButton from '../../components/HeaderRightButton';

const travelTimeActionSheetRef = createRef();

const travelRadiusActionSheetRef = createRef();

class RemoteLocationScreen extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Remote',
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
    this.props.navigation.setParams({
      onSaveClientLocation: this.onSaveClientLocation,
      goBack: this.goBack,
    });
    this.props.getAvailableLocationsAction();
    this.props.getSelfBookingAction();
  }

  componentDidMount() {
    if (this.props.remoteLocation) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.locationsLoading !== this.props.locationsLoading) {
      this.props.navigation.setOptions({
        headerRight: this.renderHeaderRight,
      });
    }
  }

  renderHeaderRight = () => {
    if (this.props.locationsLoading) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.onSaveClientLocation()}
        title="Save"
        titleStyle={{ ...generalStyles.navigationButtonText }}
      />
    );
  };

  goBack = () => {
    this.props.navigation.goBack(null);
  };

  onSaveClientLocation = () => {
    if (this.props.remoteLocation.remoteStartTimes.length === 0) {
      Alert.alert(
        'No start slot selected',
        'Please select at least one start slot for current location',
      );
    } else if (this.props.remoteLocation.workHours.indexOf('null') !== -1) {
      Alert.alert('Invalid Workhours', 'Please check the workdays');
    } else {
      this.props.saveRemoteLocationAction();
      track('remote_location_enabled');
    }
  };

  render() {
    return this.props.locationDataLoading ? (
      <LoadingSpinner visible size="large" backgroundColor={colors.white} />
    ) : (
      <SafeAreaView style={styles.container}>
        <RemoteLocationForm
          navigation={this.props.navigation}
          selfBookings={this.props.remoteLocation}
          initialValues={this.props.remoteLocation}
          onChangeRemoteLocationsProps={
            this.props.setRemoteLocationValuesAction
          }
          travelTimeActionSheetRef={travelTimeActionSheetRef}
          travelRadiusActionSheetRef={travelRadiusActionSheetRef}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  locations: state.locations.locationsList,
  remoteLocation: state.locations.remoteLocation,
  locationsLoading: state.loadingComponents.remoteLocationsLoading,
  locationDataLoading: state.loadingComponents.locationsLoading,
});

export default connect(mapStateToProps, {
  getAvailableLocationsAction,
  getSelfBookingAction,
  setRemoteLocationValuesAction,
  saveRemoteLocationAction,
})(RemoteLocationScreen);
