import React, { Component } from 'react';
import { connect } from 'react-redux';
import { SafeAreaView, StyleSheet } from 'react-native';
import moment from 'moment';
import _cloneDeep from 'lodash.clonedeep';
import CreateClassForm from '../../components/artichoke/screensComponents/booked/CreateClassForm';
import {
  setNewClassValuesAction,
  saveClassAction,
  clearSelectedClassAction,
  clearNewClassAction,
} from '../../actions/artichoke/Appointments.actions';
import { getAvailableLocationsAction } from '../../actions/artichoke/Locations.actions';
import HeaderLeftButton from '../../components/HeaderLeftButton';

class CreateClass extends Component {
  static navigationOptions = ({ navigation }) => ({
    title: 'Class',
    headerLeft: () => <HeaderLeftButton onPress={() => navigation.goBack()} />,
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.props.getAvailableLocationsAction();
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
    this.props.clearNewClassAction();
    this.props.clearSelectedClassAction();
    this.props.setNewClassValuesAction({
      key: 'startDate',
      value: moment().toString(),
    });
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.newClassValues.service
      && prevProps.newClassValues.service?.id
        !== this.props.newClassValues.service.id
    ) {
      this.preselectOneLocation(this.props.newClassValues.service);
    }
  }

  preselectOneLocation = (service) => {
    const items = service.selfBookingAddresses
      ? _cloneDeep(service.selfBookingAddresses)
      : [];
    if (service.clientLocationEnabled) {
      items.push({
        id: 0,
        addressName: "Client's location",
        checked: true,
      });
    }
    if (service.offeredOnline) {
      items.push({
        id: 1,
        addressName: 'Remotely',
        checked: true,
      });
    }
    if (service.inappEnabled) {
      items.push({
        id: 2,
        addressName: 'Video Call',
        checked: true,
      });
    }
    if (items.length === 1) {
      this.props.setNewClassValuesAction({
        key: 'selfBookingAddressId',
        value: items[0],
      });
    }
  };

  goBack = () => {
    this.props.navigation.goBack();
  };

  saveClass = () => {
    this.props.saveClassAction();
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <CreateClassForm
          initialValues={this.props.newClassValues}
          setNewClassValuesAction={this.props.setNewClassValuesAction}
          onSaveClass={this.saveClass}
          navigation={this.props.navigation}
          isLoading={this.props.scheduleClassLoading}
          symbol={this.props.symbol}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

const mapStateToProps = (state) => ({
  newClassValues: state.appointments.newClassValues,
  selfBookings: state.locations.selfBookings,
  scheduleClassLoading: state.loadingComponents.scheduleClassLoading,
  symbol: state.user.accountSettings?.symbol,
});

export default connect(mapStateToProps, {
  setNewClassValuesAction,
  saveClassAction,
  clearSelectedClassAction,
  clearNewClassAction,
  getAvailableLocationsAction,
})(CreateClass);
