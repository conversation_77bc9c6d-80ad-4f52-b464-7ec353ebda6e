import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
} from 'react-native';
import { colors } from '../../styles';
import { APPLICATION_ROUTES } from '../../constants';
import { refundClientTransactionAction } from '../../actions/artichoke/Clients.actions';
import { generalStyles } from '../../styles/generalStyle';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const bgImage = require('../../assets/imgServiceBackground.png');

class RefundTransactionDetailsScreen extends Component {
  static navigationOptions = ({ route }) => ({
    headerLeft: () => (
      <HeaderLeftButton onPress={() => route.params.goBack()} />
    ),
    headerBackTitle: 'Back',
  });

  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
    };
    this.props.navigation.setParams({
      goBack: this.goBack,
    });
  }

  goBack = () => {
    this.props.navigation.goBack();
  };

  goToRefund = () => {
    this.props.navigation.navigate(APPLICATION_ROUTES.REFUND_TRANSACTION);
  };

  showSelectedAddress = (item) => {
    let addressLine = '';
    if (item) {
      addressLine = item.address2 !== ''
        ? `${item.address1}, ${item.address2}, ${item.city}, ${item.state}`
        : `${item.address1}, ${item.city}, ${item.state}`;
    }
    return addressLine;
  };

  render() {
    let productName = '';
    if (this.props.selectedTransaction.purchase.pack) {
      productName = this.props.selectedTransaction.purchase.pack.name;
    } else if (
      this.props.selectedTransaction.purchase.clientPurchaseProductDurations
      && this.props.selectedTransaction.purchase.clientPurchaseProductDurations
        .length
    ) {
      productName = this.props.selectedTransaction.purchase
        .clientPurchaseProductDurations[0].productName;
    }

    let purchaseType = '';

    if (
      this.props.selectedTransaction.purchase.purchaseType === 'CREDIT_CARD'
      && this.props.selectedClientCreditCart
    ) {
      purchaseType = `${this.props.selectedClientCreditCart.creditCardType} ...${this.props.selectedClientCreditCart.lastFour}`;
    } else if (
      this.props.selectedTransaction.purchase.purchaseType
        === 'CASH_OR_CHECK'
      || !this.props.selectedClientCreditCart
    ) {
      purchaseType = 'Cash Or Check';
    }
    return (
      <SafeAreaView style={styles.container}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.form}>
            <View style={styles.contentUser}>
              <Image
                source={
                  this.props.selectedClient?.user.avatarUrl
                    ? { uri: this.props.selectedClient?.user.avatarUrl }
                    : bgImage
                }
                style={styles.userImage}
              />
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{productName}</Text>
                <Text style={styles.userJob}>
                  {this.props.selectedClient?.user.firstName}
                  {' '}
                  {this.props.selectedClient?.user.lastName}
                  {' '}
                </Text>
              </View>
            </View>
            <View>
              <View>
                <Text style={styles.label}>Amount Refunded</Text>
                <Text style={styles.description}>
                  {parseFloat(
                    this.props.selectedTransaction.purchase.amountPaid,
                  ).toFixed(2)}
                </Text>
              </View>
              <View>
                <Text style={styles.label}>Refund Method</Text>
                <Text style={styles.description}>{purchaseType}</Text>
              </View>
              <View>
                <Text style={styles.label}>Date of Refund</Text>
                <Text style={styles.description}>
                  {this.props.selectedTransaction.purchase.refundDateTime}
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    marginTop: 20,
    marginBottom: 120,
    paddingHorizontal: 20,
    width: '100%',
  },
  userImage: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userDetails: {
    flex: 6,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    lineHeight: 22,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  label: {
    ...generalStyles.avenirHeavy17,
    paddingTop: 24,
  },
  description: {
    marginTop: 8,
    ...generalStyles.avenirRoman14,
    color: colors.fillDarkGrey,
  },
});

const mapStateToProps = (state) => ({
  selectedTransaction: state.clients.selectedClientTransaction,
  selectedClient: state.clients.selectedClient,
  selectedClientCreditCart: state.clients.selectedClient?.creditCard,
});

export default connect(mapStateToProps, {
  refundClientTransactionAction,
})(RefundTransactionDetailsScreen);
