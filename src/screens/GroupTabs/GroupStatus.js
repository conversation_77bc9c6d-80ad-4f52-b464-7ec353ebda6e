import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  Text,
  View,
  Alert,
  Image,
  FlatList,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import { HeaderSearchBar } from '../../components';
import nasm from '../../dataManager/apiConfig';
import { colors } from '../../styles';

const defaultProfile = require('../../resources/defaultProfile.png');

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.any,
  }).isRequired,
};

const stateSelector = (state) => state;

const GroupStatus = ({ navigation, route }) => {
  const { selectedGroup } = useSelector(stateSelector);
  const [groupClients, setGroupClients] = useState(
    selectedGroup?.client_group_clients ?? [],
  );
  const [updateScreen, setUpdateScreen] = useState(false);
  const { workoutData, clientUsersWorkoutsExercises, isVideoWorkout } = route?.params;
  const { workout, program_start_date, program_end_date } = workoutData;
  const completedByIds = workout?.completed_by ?? [];
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    setNavigationParams();
  });

  useEffect(() => {
    const client_group_clients = selectedGroup?.client_group_clients.map(
      (user) => ({
        ...user,
        is_complete: completedByIds.includes(user.client_user?.id),
      }),
    );
    setGroupClients(client_group_clients);
  }, [completedByIds]);

  const setNavigationParams = () => {
    navigation.setOptions({
      title: 'Group Status',
    });
  };

  const getExerciseIdsForSelectedClient = (selectedClientId) => {
    const workouts = clientUsersWorkoutsExercises[selectedClientId];
    const exerciseIds = workouts[workout.id];
    return exerciseIds.scheduled_exercise_ids;
  };

  const getWorkoutIdsForSelectedClient = (selectedClientId) => {
    const workouts = clientUsersWorkoutsExercises[selectedClientId];
    const workoutId = workouts[workout.id];
    return workoutId.user_schedule_workout_id;
  };

  const toggleCompleteWorkout = async (client) => {
    const selectedClientId = client.client_user?.user?.id;
    const clientUserId = client.client_user?.id;
    const exerciseIds = getExerciseIdsForSelectedClient(clientUserId);
    if (client.is_complete) {
      await nasm.api.incompleteClientExercises({
        clientId: selectedClientId,
        exerciseIds,
        program_start_date,
        program_end_date,
      });
    } else {
      await nasm.api.completeClientExercises({
        clientId: selectedClientId,
        exerciseIds,
        program_start_date,
        program_end_date,
      });
    }
  };

  const toggleCompleteVideoWorkout = async (client) => {
    const clientUserId = client.client_user?.id;
    if (client.is_complete) {
      await nasm.api.incompleteWorkout({
        scheduledWorkoutId: getWorkoutIdsForSelectedClient(clientUserId),
        program_start_date,
        program_end_date,
      });
    } else {
      await nasm.api.completeWorkout({
        scheduledWorkoutId: getWorkoutIdsForSelectedClient(clientUserId),
        program_start_date,
        program_end_date,
      });
    }
  };

  const markCompleteIncomplete = (client) => {
    try {
      if (isVideoWorkout) {
        toggleCompleteVideoWorkout(client);
      } else {
        toggleCompleteWorkout(client);
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      client.is_complete = !client.is_complete;
      setUpdateScreen(!updateScreen);
      route.params?.onUpdateCompleteStatus(client.client_user?.id);
    }
  };

  const renderItem = ({ item }) => (
    <View style={styles.itemContainer}>
      <View style={styles.avatarContainer}>
        <Image
          style={styles.profileImageStyle}
          source={
            item.client_user?.user?.avatar_url
              ? { uri: item.client_user.user.avatar_url }
              : defaultProfile
          }
        />
      </View>
      <View style={styles.nameView}>
        <Text
          style={styles.nameTextStyle}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {item.client_user?.user?.full_name}
        </Text>
      </View>
      <TouchableOpacity
        style={item.is_complete ? styles.selectedStyle : styles.unselectedStyle}
        onPress={() => markCompleteIncomplete(item)}
      >
        <Text
          style={item.is_complete ? styles.selectedTextSyle : styles.textStyle}
        >
          {item.is_complete ? 'Completed' : 'Incomplete'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const clients = groupClients.filter((item) => item.client_user?.user?.full_name
    ?.toLowerCase()
    .includes(searchText.toLowerCase()));

  return (
    <SafeAreaView style={styles.container}>
      {groupClients.length > 0 && (
        <>
          <HeaderSearchBar
            searchText={searchText}
            onChangeText={(text) => setSearchText(text)}
            clearable
            paddingTop={10}
            light
            shadow={false}
            placeholder="Search"
          />
          <View style={styles.headerTopContainer}>
            <Text style={styles.headerDescription}>
              You may choose to update a group member&apos;s status manually
              from this screen.
            </Text>
          </View>
        </>
      )}
      <FlatList
        testID="GroupStatusList"
        keyExtractor={(item) => item.client_user?.id}
        data={clients}
        renderItem={renderItem}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTopContainer: {
    backgroundColor: colors.subGreyTwo,
    paddingVertical: 13,
    paddingHorizontal: 20,
  },
  headerDescription: {
    fontFamily: 'Avenir-Medium',
    fontSize: 13,
    color: colors.white,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    borderBottomWidth: 1,
    borderColor: colors.subGreyLight,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  profileImageStyle: {
    width: 56,
    height: 56,
    borderRadius: 56 / 2,
  },
  avatarContainer: {
    width: '20%',
    alignItems: 'center',
  },
  nameView: {
    paddingHorizontal: 15,
    flex: 8,
  },
  nameTextStyle: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    lineHeight: 24,
    color: colors.black,
  },
  selectedStyle: {
    width: 132,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    backgroundColor: colors.goodGreen,
  },
  unselectedStyle: {
    width: 132,
    height: 36,
    borderRadius: 18,
    borderStyle: 'solid',
    borderWidth: 1,
    justifyContent: 'center',
    borderColor: colors.chatSelected,
    backgroundColor: colors.white,
  },
  textStyle: {
    fontSize: 17,
    fontFamily: 'Avenir-Heavy',
    textAlign: 'center',
    color: colors.chatSelected,
  },
  selectedTextSyle: {
    fontSize: 17,
    fontFamily: 'Avenir-Heavy',
    textAlign: 'center',
    color: colors.white,
  },
});

// Export
GroupStatus.propTypes = propTypes;

export default GroupStatus;
