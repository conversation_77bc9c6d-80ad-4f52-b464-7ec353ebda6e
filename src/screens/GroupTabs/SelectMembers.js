/* eslint-disable react-redux/useSelector-prefer-selectors */
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import HeaderRightButton from '../../components/HeaderRightButton';
import nasm from '../../dataManager/apiConfig';
import { generalStyles } from '../../styles/generalStyle';
import { colors } from '../../styles';
import HeaderSearchBar from '../../components/HeaderSearchBar';

const defaultProfile = require('../../resources/defaultProfile.png');
const selectedIcon = require('../../resources/completed.png');
const unselectedIcon = require('../../resources/checkmarkIncomplete.png');

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      selectedUsers: PropTypes.array,
      onUpdateSelectedUsers: PropTypes.func,
    }),
  }).isRequired,
};
const defaultProps = {};

const SelectMembers = ({ navigation, route }) => {
  const [clientsList, setClientsList] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState(
    route?.params?.selectedUsers ?? [],
  );
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const trainerActiveProfile = useSelector((state) => state.trainerActiveProfile);

  const filteredClients = clientsList?.filter((client) => client?.full_name?.toLowerCase().includes(searchText.toLowerCase()));

  useEffect(() => {
    navigation.setOptions({
      headerTitle: 'Select Members',
      headerTitleStyle: styles.headerTitleStyle,
      headerRight: renderHeaderRight,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedUsers]);

  useEffect(() => {
    getClientsList();
  }, []);

  const goBack = () => {
    navigation.goBack();
  };

  const getClientsList = async () => {
    const club_id = trainerActiveProfile?.ClubId;
    const location_id = trainerActiveProfile?.Locations?.Id;
    const clients = await nasm.api.getTrainerClients(club_id, location_id);
    if (clients?.length) {
      setClientsList(clients);
    }
    setLoading(false);
  };

  const renderHeaderRight = () => (
    <HeaderRightButton
      title="Save"
      titleStyle={{ ...generalStyles.navigationButtonText }}
      onPress={() => {
        route?.params?.onUpdateSelectedUsers(selectedUsers);
        goBack();
      }}
    />
  );

  const onSelectUser = (user) => {
    const users = [...selectedUsers];
    let isNewUser = true;
    let userIndex = 0;
    if (users.length) {
      userIndex = users.findIndex(
        (client_user) => client_user.id === user.client_user.id,
      );
      if (userIndex > -1) {
        isNewUser = false;
      }
    }
    if (isNewUser) {
      const client_user = {
        id: user.client_user.id,
        user: {
          id: user.id,
          avatar_url: user.avatar_url,
          full_name: user.full_name,
        },
      };
      users.push(client_user);
    } else {
      users.splice(userIndex, 1);
    }
    setSelectedUsers(users);
  };

  const renderUser = ({ item }) => (
    <View style={styles.userRow}>
      <View style={styles.nameRow}>
        <Image
          source={
            item && item?.avatar_url
              ? { url: item?.avatar_url }
              : defaultProfile
          }
          style={styles.profileImageStyle}
        />
        <Text style={styles.name}>{item.full_name}</Text>
      </View>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => {
          onSelectUser(item);
        }}
      >
        <Image
          source={
            selectedUsers.findIndex(
              (client_user) => client_user.id === item.client_user.id,
            ) > -1
              ? selectedIcon
              : unselectedIcon
          }
          style={styles.checkBox}
        />
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.activityContainer}>
        <ActivityIndicator size="large" color={colors.nasmBlue} />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <HeaderSearchBar
        searchText={searchText}
        onChangeText={(searchTxt) => setSearchText(searchTxt)}
        clearable
        paddingTop={10}
        light
        shadow={false}
        placeholder="Search Clients"
      />
      <FlatList
        data={filteredClients}
        keyExtractor={(item) => item.id}
        renderItem={renderUser}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  activityContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderColor: colors.subGreyLight,
    paddingHorizontal: 12,
    paddingVertical: 20,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    fontSize: 16,
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    marginLeft: 15,
  },
  profileImageStyle: {
    width: 50,
    height: 50,
    borderRadius: 50 / 2,
  },
  checkBox: {
    width: 20,
    height: 20,
  },
  headerTitleStyle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

SelectMembers.propTypes = propTypes;
SelectMembers.defaultProps = defaultProps;

export default SelectMembers;
