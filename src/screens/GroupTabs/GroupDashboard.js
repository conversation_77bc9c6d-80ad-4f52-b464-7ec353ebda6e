import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import {
  Alert,
  LayoutAnimation,
  Text,
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import { connect } from 'react-redux';
import { selectDay } from '../../reducers/selectedDayReducer';
import { scheduleProgram } from '../../reducers/programContextReducer';
import {
  addWorkouts,
  removeWorkout,
  clearProgram,
} from '../../reducers/selectedProgramReducer';
import { getWorkoutSections } from '../../reducers/workoutsReducer';

import { CalendarWithToggle, FloatingButton } from '../../components';
import nasm from '../../dataManager/apiConfig';
import { ROLES, androidSafeLayoutAnimation } from '../../constants';

// Helpers
import { curvedScale, scaleWidth } from '../../util/responsive';

// Styles
import { colors } from '../../styles';
import ProgramCard from '../../components/ProgramCard';

const noProgramScheduled = require('../../resources/imgNoProgram.png');
// Props
const propTypes = {
  currentUser: PropTypes.shape({
    full_name: PropTypes.string,
  }).isRequired,
  selectedGroup: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    route: PropTypes.object.isRequired,
  }).isRequired,
};
const defaultProps = {};

class GroupDashboard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      weekView: true,
      scheduleList: [],
      markedDatesData: [],
      clientUsersWorkoutsExercises: {},
      selectedDay: moment().format('YYYY-MM-DD'),
      club_id: this.props.trainerActiveProfile?.ClubId,
      location_id: this.props.trainerActiveProfile?.Locations?.Id,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.setInitialDate();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressDay = (day) => {
    if (day !== this.state.selectedDay) {
      this.props.selectDay(day);
      this.setScheduleDate(day);
    }
  };

  onMonthChange = (date) => {
    const month = moment(date).startOf('month').format();
    this.setState({ selectedDay: moment(date).format('YYYY-MM-DD') });
    this.getCalendarMonthSchedule(month);
  };

  onPressAddProgram = () => {
    this.props.scheduleProgram();
    this.props.getWorkoutSections();
    this.props.navigation.navigate('ActionSheet', {
      title: 'Assign a workout from:',
      actions: [
        {
          text: 'Exercises',
          onPress: () => {
            this.props.navigation.navigate('AddExercises', {
              quickAdd: true,
            });
          },
        },
        {
          text: 'Workouts',
          onPress: () => {
            this.props.navigation.navigate('QuickAddWorkouts', {
              quickAdd: true,
              addWorkouts: this.props.addWorkouts,
              removeWorkout: this.props.removeWorkout,
            });
          },
        },
        {
          text: 'Programs',
          onPress: this.assignNewProgram,
        },
      ],
      onDismiss: () => this.props.clearProgram(),
    });
  };

  onToggleCalendar = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ weekView: !this.state.weekView });
  };

  onSelectWorkout = (workoutData) => {
    let screenName = 'WorkoutOverview';
    if (workoutData.workout?.uploaded_media_id) {
      screenName = 'VideoWorkoutOverview';
    }
    this.props.navigation.navigate(screenName, {
      workoutData,
      clientUsersWorkoutsExercises: this.state.clientUsersWorkoutsExercises,
      isGroup: true,
      trainerName: this.props.currentUser.full_name,
    });
  };

  onPressDeleteSingleWorkout = async ({
    client_group_id,
    schedule_group_id,
    workout_id,
    workout_name,
    type,
  }) => {
    Alert.alert(
      'Remove workout?',
      `This will remove the selected instance of ${workout_name} from your client's calendar`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          onPress: async () => {
            const { club_id, location_id } = this.state;
            const response = await nasm.api
              .deleteGroupScheduleWorkout({
                client_group_id,
                schedule_group_id,
                workout_id,
                type,
                date: this.state.selectedDay,
                club_id,
                location_id,
              })
              .catch((error) => {
                Alert.alert(
                  'Error',
                  error?.message
                    || 'Something went wrong! Please try again later.',
                );
              });
            if (response) {
              this.refreshSchedules();
            }
          },
          style: 'destructive',
        },
      ],
    );
  };

  onPressDeleteAllInstanceWorkout = async ({
    client_group_id,
    schedule_group_id,
    workout_id,
    workout_name,
    type,
  }) => {
    Alert.alert(
      'Remove workout?',
      `This will remove all instances of ${workout_name} from your client's calendar`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          onPress: async () => {
            const { club_id, location_id } = this.state;
            const response = await nasm.api
              .deleteGroupScheduleWorkout({
                client_group_id,
                schedule_group_id,
                workout_id,
                type,
                date: this.state.selectedDay,
                club_id,
                location_id,
              })
              .catch((error) => {
                Alert.alert(
                  'Error',
                  error?.message
                    || 'Something went wrong! Please try again later.',
                );
              });
            if (response) {
              this.refreshSchedules();
            }
          },
          style: 'destructive',
        },
      ],
    );
  };

  onPressDeleteEntireProgram = async ({
    client_group_id,
    schedule_group_id,
    workout_id,
    program_name,
    type,
  }) => {
    Alert.alert(
      'Remove program?',
      `This will remove all future workouts in ${program_name} from your client's calendar`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          onPress: async () => {
            const { club_id, location_id } = this.state;
            const response = await nasm.api
              .deleteGroupScheduleWorkout({
                client_group_id,
                schedule_group_id,
                workout_id,
                type,
                date: this.state.selectedDay,
                club_id,
                location_id,
              })
              .catch((error) => {
                Alert.alert(
                  'Error',
                  error?.message
                    || 'Something went wrong! Please try again later.',
                );
              });
            if (response) {
              this.refreshSchedules();
            }
          },
          style: 'destructive',
        },
      ],
    );
  };

  onPressDeleteProgram = async (programDay) => {
    this.props.navigation.navigate('ActionSheet', {
      title: 'Remove',
      actions: this.getDeleteActions(programDay),
      onDismiss: null,
    });
  };

  setScheduleDate = (day) => {
    this.setState({ selectedDay: day }, () => {
      this.getScheduleListByDate(moment(day).format());
    });
  };

  getDeleteActions = (programDay) => {
    const {
      program_name,
      workout_name,
      workout_id,
      client_group_id,
      schedule_group_id,
    } = programDay;
    const actions = [
      {
        text: 'Just this workout',
        onPress: () => this.onPressDeleteSingleWorkout({
          client_group_id,
          schedule_group_id,
          workout_id,
          workout_name,
          type: 'THIS_WORKOUT',
        }),
      },
      {
        text: 'All instances of this workout',
        onPress: () => this.onPressDeleteAllInstanceWorkout({
          client_group_id,
          schedule_group_id,
          workout_id,
          workout_name,
          type: 'ALL_INSTANCE_OF_THIS_WORKOUT',
        }),
      },
    ];
    if (program_name) {
      actions.push({
        text: 'Entire program',
        onPress: () => this.onPressDeleteEntireProgram({
          client_group_id,
          schedule_group_id,
          workout_id,
          program_name,
          type: 'ENTIRE_PROGRAM',
        }),
      });
    }
    return actions;
  };

  setInitialDate = () => {
    const month = moment(this.state.selectedDay).startOf('month').format();
    const today = moment(this.state.selectedDay).format();
    this.getScheduleListByDate(today);
    this.getCalendarMonthSchedule(month);
  };

  getScheduleListByDate = async (day) => {
    const res = await this.getProgramDay(day);

    this.setState({
      scheduleList: res.workouts,
      clientUsersWorkoutsExercises: res.clientUsersWorkoutsExercises,
    });
  };

  getCalendarMonthSchedule = async (month) => {
    const res = await this.getProgramMonth(month);
    this.setState({ markedDatesData: res });
  };

  getProgramDay = async (day) => {
    const { id: groupId } = this.props.selectedGroup;
    const { club_id, location_id } = this.state;
    const response = await nasm.api
      .getGroupCalendarDaySchedule(groupId, day, club_id, location_id)
      .catch((error) => {
        Alert.alert('Error retrieving program day', error.message);
        return [];
      });
    return response;
  };

  getProgramMonth = async (month) => {
    const { id: groupId } = this.props.selectedGroup;
    const { club_id, location_id } = this.state;
    return nasm.api
      .getGroupCalendarScheduleDates(groupId, month, club_id, location_id)
      .catch((error) => {
        Alert.alert('Error retrieving schedule', `${error}`);
        return [];
      });
  };

  getMarkedDates = () => {
    const completedWorkout = {
      selected: true,
      selectedColor: 'rgba(70, 154, 30, 0.2)',
    };
    const missedWorkout = {
      selected: true,
      selectedColor: 'rgba(246, 170, 44, 0.2)',
    };
    const scheduledFutureWorkout = {
      selected: true,
      selectedColor: 'rgba(182, 189, 195, 0.2)',
    };
    const selectedDayBacklight = { selected: true, selectedColor: 'black' };
    let markObj = {};
    this.state.markedDatesData.forEach((item) => {
      const workout = item.scheduled_workout;
      let backlight = null;
      const currentDate = moment();
      if (!moment(item.date).isSame(this.state.selectedDay, 'day')) {
        if (workout) {
          if (currentDate.isSameOrBefore(moment(workout.workout_date), 'day')) {
            backlight = scheduledFutureWorkout;
          } else if (currentDate.isAfter(moment(workout.workout_date), 'day')) {
            backlight = missedWorkout;
          }
          if (workout.is_complete) {
            backlight = completedWorkout;
          }
        }
      } else {
        backlight = selectedDayBacklight;
      }
      markObj = {
        ...markObj,
        [moment(item.date).format('YYYY-MM-DD')]: {
          ...backlight,
          dark: moment(item.date).isSame(this.state.selectedDay, 'day'),
          marked: true,
        },
      };
    });
    markObj = {
      ...markObj,
      [this.state.selectedDay]: {
        selected: true,
        marked: true,
        dark: true,
        ...selectedDayBacklight,
      },
    };
    return markObj;
  };

  getWeeklyMarkedDates = () => {
    const marked = this.state.markedDatesData.map((item) => {
      let backgroundColor = 'transparent';
      if (item.is_complete) {
        backgroundColor = 'rgba(70, 154, 30, 0.2)';
      } else if (moment(item.date).isBefore(moment(), 'day')) {
        backgroundColor = 'rgba(246, 170, 44, 0.2)';
      } else {
        backgroundColor = 'rgba(182, 189, 195, 0.2)';
      }
      return {
        startDate: moment(item.date),
        dateContainerStyle: { backgroundColor },
      };
    });
    return marked;
  };

  getDayText = () => {
    const selected = moment(this.state.selectedDay);
    let dayText = '';

    if (selected.isSame(moment(), 'day')) {
      dayText += 'Today, ';
    } else {
      dayText += `${selected.format('dddd')}, `;
    }

    dayText += selected.format('MMMM D');

    return dayText;
  };

  refreshSchedules = () => {
    const { selectedDay } = this.state;
    this.setScheduleDate(selectedDay);
    const month = moment(selectedDay).startOf('month').format();
    this.getCalendarMonthSchedule(month);
  };

  assignNewProgram = () => {
    try {
      nasm.api.getAllGoals().then((goals) => {
        this.props.navigation.navigate('ProgramCatalog', { goals });
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to load client goals');
    }
  };

  renderScheduleProgram = ({ item }) => {
    const isExpired = new Date(item.program_end_date) < new Date();
    return (
      <TouchableOpacity
        key={item.id}
        onPress={() => this.onSelectWorkout(item)}
      >
        <ProgramCard
          key={item.user_schedule_id}
          workout={item.workout}
          role={ROLES.TRAINER}
          programName={item.program_name}
          programWorkoutName={item.workout_name}
          programCategory={item.program_category_name}
          totalSeconds={item.total_dur_seconds}
          durationPlus={item.duration_plus}
          isExpired={isExpired}
          completed={item.is_complete}
          updated_at={item.updated_at}
          onSelectWorkout={(workoutData) => this.onSelectWorkout(workoutData)}
          onPressDelete={() => this.onPressDeleteProgram(item)}
          scheduleGroupId={item.schedule_group_id}
          isGroup
        />
      </TouchableOpacity>
    );
  };

  renderHeader = () => {
    const { weekView, selectedDay } = this.state;
    return (
      <>
        <View style={{ backgroundColor: colors.white }}>
          <CalendarWithToggle
            weekView={weekView}
            isLoading={false}
            isWalkin={false}
            selectedDay={selectedDay}
            markedDates={this.getMarkedDates()}
            customDatesStyles={this.getWeeklyMarkedDates()}
            onDateSelected={this.onPressDay}
            onMonthChange={this.onMonthChange}
            onToggle={this.onToggleCalendar}
          />
        </View>
        <View style={styles.topView}>
          <Text style={styles.dayText}>{this.getDayText()}</Text>
        </View>
      </>
    );
  };

  renderEmptyComponent = () => (
    <View style={styles.container2}>
      <Image source={noProgramScheduled} />
      <Text style={styles.headerText}>No Workout Scheduled</Text>
    </View>
  );

  render() {
    return (
      <SafeAreaView style={styles.containerParentView}>
        <FlatList
          data={this.state.scheduleList}
          keyExtractor={(item) => item.id}
          ListHeaderComponent={this.renderHeader}
          renderItem={this.renderScheduleProgram}
          ListEmptyComponent={this.renderEmptyComponent}
          contentContainerStyle={styles.listContainer}
        />
        <FloatingButton
          testID="AddGroupSchedule"
          onPress={this.onPressAddProgram}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  containerParentView: {
    flex: 1,
    backgroundColor: colors.white,
  },
  topView: {
    flexDirection: 'row',
    paddingTop: 10,
    marginHorizontal: scaleWidth(5),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    lineHeight: 29,
    color: colors.black,
  },
  container2: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 100,
  },
  headerText: {
    fontFamily: 'Avenir-Roman',
    color: colors.black,
    position: 'relative',
    fontSize: 22,
    marginTop: 24,
  },
  listContainer: {
    paddingBottom: curvedScale(30),
  },
});

// Export
GroupDashboard.propTypes = propTypes;
GroupDashboard.defaultProps = defaultProps;

const mapStateToProps = ({
  currentUser,
  selectedGroup,
  trainerActiveProfile,
}) => ({
  currentUser,
  selectedGroup,
  trainerActiveProfile,
});

const mapDispatchToProps = {
  scheduleProgram,
  getWorkoutSections,
  addWorkouts,
  selectDay,
  removeWorkout,
  clearProgram,
};

export default connect(mapStateToProps, mapDispatchToProps)(GroupDashboard);
