import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Dimensions,
  StyleSheet,
  ActivityIndicator,
  DeviceEventEmitter,
} from 'react-native';
import AwesomeAlert from 'react-native-awesome-alerts';
import { connect } from 'react-redux';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import GroupDashboard from './GroupDashboard';
import GroupProfile from './GroupProfile';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import HeaderRightButton from '../../components/HeaderRightButton';
import { deleteAlertStyles } from '../../styles/alertStyle';
import { colors, materialTabBarOptions } from '../../styles';

const { width } = Dimensions.get('window');

const TabNav = createMaterialTopTabNavigator();

const propTypes = {
  currentUser: PropTypes.shape({
    role: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.shape({
    pop: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.any,
  }).isRequired,
  selectedGroup: PropTypes.any.isRequired,
};

let unsubscribeGroupUpdateListener;

class GroupTabView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isWalkIn: false,
      connectedMode: false,
      isLoading: false,
      hasUnsavedChanges: false,
      showAlert: false,
    };
  }

  componentDidMount() {
    this.setNavigationParams();
    this.registerListener();
  }

  componentWillUnmount() {
    if (unsubscribeGroupUpdateListener) {
      unsubscribeGroupUpdateListener.remove();
    }
  }

  onGroupEdited = () => {
    this.setState(
      {
        hasUnsavedChanges: true,
      },
      () => {
        this.setNavigationParams();
      },
    );
  };

  onUpdateGroup = () => {
    this.setState(
      {
        isLoading: true,
      },
      () => {
        this.setNavigationParams();
        DeviceEventEmitter.emit('onUpdateGroup');
      },
    );
  };

  onGroupUpdated = (isError) => {
    const updatedState = {
      isLoading: false,
      hasUnsavedChanges: false,
    };
    if (isError) {
      delete updatedState.hasUnsavedChanges;
    }
    this.setState(updatedState, () => {
      this.setNavigationParams();
    });
  };

  setNavigationParams = () => {
    this.props.navigation.setOptions({
      title: this.props.selectedGroup?.title ?? '',
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
  };

  registerListener = () => {
    unsubscribeGroupUpdateListener = DeviceEventEmitter.addListener(
      'onGroupEdited',
      () => {
        this.onGroupEdited();
      },
    );
  };

  goBack = (popCount = 1) => {
    this.props.navigation.pop(popCount);
  };

  toggleAlert = () => {
    const { showAlert } = this.state;
    this.setState({
      showAlert: !showAlert,
    });
  };

  renderUnsavedChangesPopup = () => (
    <AwesomeAlert
      show={this.state.showAlert}
      showProgress={false}
      useNativeDriver
      title="Unsaved Changes"
      message="Are you sure you want to discard your changes?"
      closeOnTouchOutside={false}
      closeOnHardwareBackPress={false}
      showConfirmButton
      showCancelButton
      confirmText="Cancel"
      cancelText="Yes, cancel changes"
      titleStyle={styles.alertTitle}
      messageStyle={styles.alertText}
      confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
      confirmButtonStyle={deleteAlertStyles.alertButton}
      cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
      cancelButtonStyle={deleteAlertStyles.alertButton}
      actionContainerStyle={styles.alertContainer}
      contentContainerStyle={deleteAlertStyles.alertContainer}
      overlayStyle={styles.alertOverlayStyle}
      onCancelPressed={() => {
        this.toggleAlert();
        this.goBack(2);
      }}
      onConfirmPressed={() => {
        this.toggleAlert();
      }}
    />
  );

  renderHeaderLeft = () => {
    if (this.state.hasUnsavedChanges) {
      return (
        <HeaderLeftButton
          onPress={() => {
            this.setState({
              showAlert: true,
            });
          }}
          title="Cancel"
        />
      );
    }
    return <View />;
  };

  renderHeaderRight = () => {
    if (this.state.isLoading) {
      return this.renderLoader();
    }
    if (this.state.hasUnsavedChanges) {
      return (
        <HeaderRightButton
          onPress={this.onUpdateGroup}
          title="Save"
          titleStyle={styles.headerButtonText}
        />
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.goBack(2)}
        title="Done"
        titleStyle={styles.headerButtonText}
      />
    );
  };

  renderTabBarLabel = (label, focused) => (
    <View style={styles.tabBarLabel}>
      <Text
        allowFontScaling={false}
        style={{
          ...materialTabBarOptions.tabBarOptions.tabBarLabelStyle,
          color: focused ? colors.white : colors.inactiveWhite,
        }}
      >
        {label}
      </Text>
    </View>
  );

  renderLoader = () => (
    <View style={styles.loaderView}>
      <ActivityIndicator animating size="small" color={colors.white} />
    </View>
  );

  renderTabs = () => {
    const tabWidth = width / 2;
    const indicatorWidth = tabWidth / 1.2;
    const backgroundColor = this.props.trainerActiveProfile?.ClubId
      ? colors.black
      : colors.duskBlue;
    return (
      <TabNav.Navigator
        tabBar={
          this.state.isWalkIn || this.state.connectedMode
            ? () => null
            : undefined
        }
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarStyle,
            backgroundColor,
          },
          tabBarActiveTintColor: colors.white,
          tabBarInactiveTintColor: '#8aa1bd',
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            backgroundColor: colors.medYellow,
            width: indicatorWidth,
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
          lazy: false,
        }}
      >
        <TabNav.Screen
          name="Dashboard"
          component={GroupDashboard}
          options={{
            tabBarLabel: ({ focused }) => this.renderTabBarLabel('Dashboard', focused),
            tabBarTestID: 'ClientTabSchedule',
          }}
        />
        <TabNav.Screen
          name="GroupProfile"
          component={GroupProfile}
          options={{
            tabBarLabel: ({ focused }) => this.renderTabBarLabel('Group Profile', focused),
            tabBarTestID: 'ClientTabDashboard',
          }}
          initialParams={{
            onGroupUpdated: this.onGroupUpdated,
            onError: this.onGroupUpdated,
          }}
        />
      </TabNav.Navigator>
    );
  };

  render() {
    return (
      <View style={styles.container}>
        {this.renderUnsavedChangesPopup()}
        {this.renderTabs()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  tabBarLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  loaderView: {
    marginRight: 10,
  },
  alertTitle: {
    fontSize: 15,
    width: 270,
    textAlign: 'center',
    color: colors.black,
    marginVertical: 30,
  },
  alertText: {
    fontSize: 12,
    color: colors.subGrey,
    textAlign: 'center',
    marginBottom: 25,
    padding: 4,
    paddingHorizontal: 10,
  },
  alertContainer: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    borderRadius: 5,
  },
  alertOverlayStyle: {
    backgroundColor: colors.pickerOverlayBg,
  },
});

GroupTabView.propTypes = propTypes;

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  selectedGroup: state.selectedGroup,
  trainerActiveProfile: state.trainerActiveProfile,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(GroupTabView);
