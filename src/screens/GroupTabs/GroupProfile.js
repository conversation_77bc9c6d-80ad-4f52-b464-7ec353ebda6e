import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON>,
  StatusBar,
  StyleSheet,
  SafeAreaView,
  DeviceEventEmitter,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import AwesomeAlert from 'react-native-awesome-alerts';
import Button from '../../components/Button';
import GroupDetails from '../../components/GroupDetails';
import nasm from '../../dataManager/apiConfig';
import { selectGroup, deselectClient } from '../../actions';
import { curvedScale } from '../../util/responsive';
import { colors } from '../../styles';
import { deleteAlertStyles } from '../../styles/alertStyle';
import { FEATURE_FLAGS, chatClient } from '../../constants';

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.any,
  }).isRequired,
  selectedGroup: PropTypes.any.isRequired,
  selectGroup: PropTypes.func,
  deselectClient: PropTypes.func,
};

const defaultProps = {
  selectGroup: null,
  deselectClient: null,
};

let unsubscribeGroupUpdateListener;

// Class
class GroupProfile extends Component {
  constructor(props) {
    super(props);
    const members = this.props.selectedGroup?.client_group_clients?.map(
      (user) => user?.client_user,
    ) ?? [];
    this.state = {
      groupInfo: {
        groupId: this.props.selectedGroup?.id ?? '',
        groupName: this.props.selectedGroup?.title ?? '',
        members,
      },
      showAlert: false,
      club_id: this.props.trainerActiveProfile?.ClubId,
      location_id: this.props.trainerActiveProfile?.Locations?.Id,
    };
  }

  componentDidMount() {
    this.registerListener();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.deselectClient();
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'group_profile' });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
    if (unsubscribeGroupUpdateListener) {
      unsubscribeGroupUpdateListener.remove();
    }
  }

  onGroupUpdated = (isError) => {
    this.props.route?.params?.onGroupUpdated(isError);
  };

  setValues = (key, value) => {
    const { groupInfo } = this.state;
    this.setState(
      {
        groupInfo: {
          ...groupInfo,
          [key]: value,
        },
      },
      () => {
        DeviceEventEmitter.emit('onGroupEdited');
      },
    );
  };

  getUserIdFromClientUserId = (clientUserId) => {
    let userId;
    const combinedUsers = [
      ...this.state.groupInfo.members,
      ...this.props.selectedGroup.client_group_clients,
    ];
    combinedUsers?.forEach((client) => {
      const client_user = client.id ? client : client.client_user;
      if (client_user.id === clientUserId) {
        userId = client_user.user.id;
      }
    });
    return userId;
  };

  isValid = () => {
    const { groupName, members } = this.state.groupInfo;
    if (!groupName.length) {
      Alert.alert('Error', 'Please enter a group name.');
      return false;
    }
    if (!members.length) {
      Alert.alert('Error', 'Please select your clients to create a group.');
      return false;
    }
    return true;
  };

  registerListener = () => {
    unsubscribeGroupUpdateListener = DeviceEventEmitter.addListener(
      'onUpdateGroup',
      () => {
        if (this.isValid()) {
          this.updateProfileInfo();
        } else {
          this.onGroupUpdated(true);
        }
      },
    );
  };

  updateProfileInfo = async () => {
    try {
      const { club_id, location_id } = this.state;
      const { groupId, members, groupName } = this.state.groupInfo;
      const previousUsers = this.props.selectedGroup?.client_group_clients.map(
        (i) => i.client_user.id,
      );
      const updatedUsers = members.map((i) => i.id);
      const combinedUsers = [...previousUsers, ...updatedUsers];
      const new_client_user_ids = [];
      const removed_client_user_ids = [];
      combinedUsers.forEach((id) => {
        if (previousUsers.indexOf(id) === -1) {
          new_client_user_ids.push(id);
        }
        if (updatedUsers.indexOf(id) === -1) {
          removed_client_user_ids.push(id);
        }
      });
      const groupData = {
        id: groupId,
        title: groupName,
        new_client_user_ids,
        removed_client_user_ids,
      };
      if (club_id) {
        groupData.club_id = club_id;
        if (FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED && location_id) {
          groupData.location_id = location_id;
        }
      }
      const response = await nasm.api.updateGroup(groupData);
      if (response) {
        const previousUserIDs = removed_client_user_ids.map((client_user_id) => this.getUserIdFromClientUserId(client_user_id));
        const updatedUserIDs = new_client_user_ids.map((client_user_id) => this.getUserIdFromClientUserId(client_user_id));
        await this.updateGroupChat({
          groupId,
          groupName,
          new_client_user_ids: [...updatedUserIDs],
          removed_client_user_ids: [...previousUserIDs],
        });
        Alert.alert('Success', 'The group has been successfully updated.');
        const updatedGroup = {
          ...this.props.selectedGroup,
          title: response?.title,
          client_group_clients: response?.client_group_clients,
        };
        this.props.selectGroup(updatedGroup);
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error?.message || 'Something went wrong! Please try again later.',
      );
    } finally {
      this.onGroupUpdated();
    }
  };

  updateGroupChat = async ({
    groupId,
    groupName,
    new_client_user_ids,
    removed_client_user_ids,
  }) => {
    const { club_id, location_id } = this.state;
    const chatGroupResponse = await nasm.api.createChatGroup(
      groupId,
      club_id,
      location_id,
    );
    if (chatGroupResponse) {
      const channels = await chatClient.queryChannels({ type: 'messaging' });
      if (channels?.length) {
        const groupChat = channels.filter((ch) => ch.id === groupId);
        if (groupChat?.length) {
          const channel = groupChat[0];
          if (removed_client_user_ids.length) {
            await channel?.removeMembers([...removed_client_user_ids]);
          }
          if (new_client_user_ids.length) {
            await channel?.addMembers([...new_client_user_ids]);
          }
          await channel?.update({
            name: groupName,
          });
        }
      }
    }
  };

  selectMembers = () => {
    this.props.navigation.navigate({
      name: 'SelectMembers',
      params: {
        selectedUsers: this.state.groupInfo?.members,
        onUpdateSelectedUsers: (users) => {
          this.setValues('members', users);
        },
      },
    });
  };

  toggleAlert = () => {
    const { showAlert } = this.state;
    this.setState({
      showAlert: !showAlert,
    });
  };

  removeGroup = async () => {
    try {
      const { club_id, location_id } = this.state;
      const groupData = {
        id: this.state.groupInfo?.groupId,
        isDelete: true,
      };
      if (club_id) {
        groupData.club_id = club_id;
        if (FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED && location_id) {
          groupData.location_id = location_id;
        }
      }
      const response = await nasm.api.updateGroup(groupData);
      if (response) {
        this.removeGroupChat();
        this.props.navigation.pop(2);
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error?.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  removeGroupChat = async () => {
    try {
      const channels = await chatClient.queryChannels({ type: 'messaging' });
      if (channels?.length) {
        const groupChat = channels.filter(
          (ch) => ch.id === this.state.groupInfo.groupId,
        );
        if (groupChat?.length) {
          const channel = groupChat[0];
          await channel?.delete();
        }
      }
    } catch (error) {
      Alert.alert('error', error.message);
    }
  };

  renderRemoveGroupPopup = () => (
    <AwesomeAlert
      show={this.state.showAlert}
      showProgress={false}
      useNativeDriver
      title="Remove Group"
      message="Are you sure you want to remove this group? All workouts that are associated with this group will be removed from the clients that are a part of this group."
      closeOnTouchOutside={false}
      closeOnHardwareBackPress={false}
      showConfirmButton
      showCancelButton
      confirmText="Cancel"
      cancelText="Yes, Remove Group"
      titleStyle={styles.alertTitle}
      messageStyle={styles.alertText}
      confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
      confirmButtonStyle={deleteAlertStyles.alertButton}
      cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
      cancelButtonStyle={deleteAlertStyles.alertButton}
      actionContainerStyle={styles.alertContainer}
      contentContainerStyle={deleteAlertStyles.alertContainer}
      overlayStyle={styles.alertOverlayStyle}
      onCancelPressed={() => {
        this.toggleAlert();
        this.removeGroup();
      }}
      onConfirmPressed={() => {
        this.toggleAlert();
      }}
    />
  );

  render() {
    const { groupInfo } = this.state;
    return (
      <SafeAreaView style={styles.container}>
        {this.renderRemoveGroupPopup()}
        <GroupDetails
          groupInfo={groupInfo}
          setValues={this.setValues}
          navigation={this.props.navigation}
          onPressSelectMembers={this.selectMembers}
        />
        <Button
          title="Remove Group"
          testID="RemoveGroup"
          containerStyle={styles.removeGrpBtn}
          textStyles={styles.textRemoveGroup}
          onPress={this.toggleAlert}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  removeGrpBtn: {
    alignSelf: 'center',
    borderRadius: 30,
    borderWidth: 1,
    width: '80%',
    height: curvedScale(50),
    backgroundColor: colors.white,
    margin: curvedScale(30),
    borderColor: colors.bordergrey,
  },
  textRemoveGroup: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
  },
  alertTitle: {
    fontSize: 15,
    width: 270,
    textAlign: 'center',
    color: colors.black,
    marginVertical: 30,
  },
  alertText: {
    fontSize: 12,
    color: colors.subGrey,
    textAlign: 'center',
    marginBottom: 25,
    padding: 4,
    paddingHorizontal: 10,
  },
  alertContainer: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    borderRadius: 5,
  },
  alertOverlayStyle: {
    backgroundColor: colors.pickerOverlayBg,
  },
});

GroupProfile.propTypes = propTypes;
GroupProfile.defaultProps = defaultProps;

const mapStateToProps = ({ selectedGroup, trainerActiveProfile }) => ({
  selectedGroup,
  trainerActiveProfile,
});

const mapDispatchToProps = {
  selectGroup,
  deselectClient,
};

export default connect(mapStateToProps, mapDispatchToProps)(GroupProfile);
