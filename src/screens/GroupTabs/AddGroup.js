/* eslint-disable react-redux/useSelector-prefer-selectors */
import React, { useEffect, useState } from 'react';
import {
  View,
  Alert,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import <PERSON>wesomeAlert from 'react-native-awesome-alerts';
import analytics from '@react-native-firebase/analytics';
import HeaderRightButton from '../../components/HeaderRightButton';
import HeaderLeftButton from '../../components/HeaderLeftButton';
import InformationBox from '../../components/InformationBox';
import GroupDetails from '../../components/GroupDetails';
import nasm from '../../dataManager/apiConfig';
import { generalStyles } from '../../styles/generalStyle';
import { colors } from '../../styles';
import { deleteAlertStyles } from '../../styles/alertStyle';
import { FEATURE_FLAGS } from '../../constants';
import { logClubConnectClientGroupCreated } from '../../util/Analytics';

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};
const defaultProps = {};

const AddGroup = ({ navigation }) => {
  const [groupInfo, setGroupInfo] = useState({
    groupName: '',
    members: [],
  });
  const [loading, setLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const trainerActiveProfile = useSelector((state) => state.trainerActiveProfile);
  const currentUser = useSelector((state) => state.currentUser);

  useEffect(() => {
    setNavigationParams();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupInfo, loading]);

  const setNavigationParams = () => {
    navigation.setOptions({
      headerTitle: 'Group Builder',
      headerTitleStyle: styles.headerTitleStyle,
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    });
  };

  const goBack = () => {
    navigation.goBack();
  };

  const setValues = (key, value) => {
    setGroupInfo((prevState) => ({
      ...prevState,
      [key]: value,
    }));
  };

  const isValid = () => {
    const { groupName, members } = groupInfo;
    if (!groupName.length) {
      Alert.alert('Error', 'Please enter a group name.');
      return false;
    }
    if (!members.length) {
      Alert.alert('Error', 'Please select your clients to create a group.');
      return false;
    }
    return true;
  };

  const onPressSave = () => {
    if (isValid()) {
      updateProfileInfo();
    }
  };

  const updateProfileInfo = async () => {
    try {
      const club_id = trainerActiveProfile?.ClubId;
      const location_id = trainerActiveProfile?.Locations?.Id;
      setLoading(true);
      const client_user_ids = groupInfo.members.map((user) => user.id);
      const groupData = {
        title: groupInfo.groupName,
        client_user_ids,
      };
      if (club_id) {
        groupData.club_id = club_id;
        if (FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED && location_id) {
          groupData.location_id = location_id;
        }
      }
      const response = await nasm.api.createGroup(groupData);
      if (response) {
        // Track ClubConnect client group creation analytics
        if (club_id) {
          logClubConnectClientGroupCreated(
            currentUser.id,
            trainerActiveProfile,
            response.id,
            groupInfo.groupName,
            client_user_ids.length,
          );
        }

        Alert.alert('Success', 'The group has been created.', [
          {
            text: 'Okay!',
            onPress: goBack,
          },
        ]);
      }
      let eventName = 'screen_view';
      const eventProps = {
        screen_name: 'create_group',
        user_id: currentUser?.id,
        full_name: currentUser?.full_name,
      };
      if (club_id) {
        eventName = `club_connect_${eventName}`;
        eventProps.screen_name = `club_connect_${eventProps.screen_name}`;
        eventProps.club_name = trainerActiveProfile?.ClubName;
        eventProps.club_id = club_id;
      }
      analytics().logEvent(eventName, eventProps);
    } catch (error) {
      Alert.alert(
        'Error',
        error?.message || 'Something went wrong! Please try again later.',
      );
    } finally {
      setLoading(false);
    }
  };

  const selectMembers = () => {
    navigation.navigate({
      name: 'SelectMembers',
      params: {
        selectedUsers: groupInfo.members,
        onUpdateSelectedUsers: (users) => {
          setValues('members', users);
        },
      },
    });
  };

  const showAlertOrGoBack = () => {
    const { groupName, members } = groupInfo;
    if (groupName.length || members.length) {
      toggleAlert();
    } else {
      goBack();
    }
  };

  const toggleAlert = () => {
    setShowAlert(!showAlert);
  };

  const renderUnsavedChangesPopup = () => (
    <AwesomeAlert
      show={showAlert}
      showProgress={false}
      useNativeDriver
      title="Unsaved Changes"
      message="Are you sure you want to discard your changes?"
      closeOnTouchOutside={false}
      closeOnHardwareBackPress={false}
      showConfirmButton
      showCancelButton
      confirmText="Cancel"
      cancelText="Yes, cancel changes"
      titleStyle={styles.alertTitle}
      messageStyle={styles.alertText}
      confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
      confirmButtonStyle={deleteAlertStyles.alertButton}
      cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
      cancelButtonStyle={deleteAlertStyles.alertButton}
      actionContainerStyle={styles.alertContainer}
      contentContainerStyle={deleteAlertStyles.alertContainer}
      overlayStyle={styles.alertOverlayStyle}
      onCancelPressed={() => {
        toggleAlert();
        goBack();
      }}
      onConfirmPressed={() => {
        toggleAlert();
      }}
    />
  );

  const renderHeaderLeft = () => (
    <HeaderLeftButton
      title="Cancel"
      titleStyle={{ ...generalStyles.navigationButtonText }}
      onPress={showAlertOrGoBack}
    />
  );

  const renderHeaderRight = () => {
    if (loading) {
      return renderLoader();
    }
    return (
      <HeaderRightButton
        title="Save"
        titleStyle={{ ...generalStyles.navigationButtonText }}
        onPress={onPressSave}
      />
    );
  };

  const renderLoader = () => (
    <View style={styles.loaderView}>
      <ActivityIndicator animating size="small" color={colors.white} />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderUnsavedChangesPopup()}
      <InformationBox titleComponent="You are creating a group. Once saved, you will be able to access the group Dashboard." />
      <GroupDetails
        groupInfo={groupInfo}
        setValues={setValues}
        navigation={navigation}
        onPressSelectMembers={selectMembers}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loaderView: {
    marginRight: 10,
  },
  headerTitleStyle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  alertTitle: {
    fontSize: 15,
    width: 270,
    textAlign: 'center',
    color: colors.black,
    marginVertical: 30,
  },
  alertText: {
    fontSize: 12,
    color: colors.subGrey,
    textAlign: 'center',
    marginBottom: 25,
    padding: 4,
    paddingHorizontal: 10,
  },
  alertContainer: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    borderRadius: 5,
  },
  alertOverlayStyle: {
    backgroundColor: colors.overlayBackground,
  },
});

AddGroup.propTypes = propTypes;
AddGroup.defaultProps = defaultProps;

export default AddGroup;
