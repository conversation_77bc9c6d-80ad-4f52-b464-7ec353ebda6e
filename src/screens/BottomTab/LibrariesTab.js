import React, { Component } from 'react';
import { StyleSheet } from 'react-native';
import AwesomeAlert from 'react-native-awesome-alerts';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { connect } from 'react-redux'; // Import connect from react-redux
import { colors, materialTabBarOptions } from '../../styles';
import ProgramCatalog from '../ProgramLibrary/ProgramCatalog';
import WorkoutCatalog from '../ProgramLibrary/WorkoutCatalog';
import ExerciseCatalog from '../ProgramLibrary/ExerciseCatalog';
import { readFromStorage, saveToStorage } from '../../util/Storage.utils';

const Tab = createMaterialTopTabNavigator();
const isFirstInstall = 'isFirstInstall';

class LibrariesTab extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
    };
  }

  async componentDidMount() {
    this.props.navigation.setParams({ header: null });
    const isData = await readFromStorage(isFirstInstall);
    if (!isData) {
      this.setState({ showAlert: true });
    }
  }

  showCustomNameAlert() {
    return (
      <AwesomeAlert
        show={this.state.showAlert}
        showProgress={false}
        title="Custom Exercise Names"
        message={
          'You can now customize EDGE Exercises with your own name.\n \n Your clients will see these names when they view the exercise.'
        }
        titleStyle={styles.titleStyle}
        closeOnHardwareBackPress={false}
        closeOnTouchOutside={false}
        useNativeDriver
        showConfirmButton
        confirmText="Continue"
        confirmButtonColor="transparent"
        confirmButtonTextStyle={styles.confirmButtonTextStyle}
        messageStyle={styles.messageStyle}
        alertContainerStyle={styles.alertContainerStyle}
        contentContainerStyle={styles.contentContainerStyle}
        contentStyle={[styles.innerContainerStyle, styles.contentStyle]}
        onConfirmPressed={() => {
          this.setState({ showAlert: false });
          saveToStorage(isFirstInstall, true);
        }}
      />
    );
  }

  render() {
    const backgroundColor = this.props.trainerActiveProfile?.ClubId
      ? colors.black
      : colors.duskBlue;
    return (
      <>
        {this.showCustomNameAlert()}
        <Tab.Navigator
          screenOptions={{
            ...materialTabBarOptions.defaultNavigationOptions,
            ...materialTabBarOptions.tabBarOptions,
            tabBarStyle: {
              backgroundColor,
            },
          }}
          style={{
            ...materialTabBarOptions.tabBarOptions.tabBarStyle,
            ...styles.topBar,
            backgroundColor,
          }}
        >
          <Tab.Screen
            name="Exercises"
            options={ExerciseCatalog.navigationOptions}
            component={ExerciseCatalog}
          />
          <Tab.Screen
            name="Workouts"
            options={WorkoutCatalog.navigationOptions}
            component={WorkoutCatalog}
          />
          <Tab.Screen
            name="Programs"
            options={ProgramCatalog.navigationOptions}
            component={ProgramCatalog}
          />
        </Tab.Navigator>
      </>
    );
  }
}

const styles = StyleSheet.create({
  topBar: {
    paddingTop: 34,
  },
  titleStyle: {
    fontSize: 20,
    fontFamily: 'SFProText-Medium',
    color: colors.black,
    textAlign: 'center',
  },
  messageStyle: {
    color: colors.fillDarkGrey,
    fontSize: 16,
    fontFamily: 'Avenir-Roman',
    textAlign: 'center',
    marginVertical: 10,
  },
  confirmButtonTextStyle: {
    color: colors.fillDarkGrey,
    fontSize: 20,
    fontFamily: 'Avenir-Heavy',
  },
  innerContainerStyle: {
    paddingHorizontal: 25,
  },
  alertContainerStyle: {
    paddingHorizontal: 0,
  },
  contentStyle: {
    borderBottomWidth: 1,
    borderColor: colors.actionSheetDivider,
  },
  contentContainerStyle: {
    paddingHorizontal: 0,
  },
});

const mapStateToProps = (state) => ({
  trainerActiveProfile: state.trainerActiveProfile,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(LibrariesTab); // Connect the component to the Redux store
