/* eslint-disable react-redux/prefer-separate-component-file */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/prop-types */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { CommonActions } from '@react-navigation/native';
import moment from 'moment';
import analytics from '@react-native-firebase/analytics';
import * as RNLocalize from 'react-native-localize';
// Components
import {
  Alert,
  Text,
  ScrollView,
  TouchableOpacity,
  View,
  LayoutAnimation,
  Dimensions,
} from 'react-native';
import { setUserProp, setUser } from '../../../util/Analytics';
import CountryPicker from '../../../components/CountryPicker/CountryPicker';
import subscriptionLevels from '../../../util/subscriptionLevels';
import nasm from '../../../dataManager/apiConfig';
import { logout, updateCurrentUser } from '../../../actions';
import {
  <PERSON><PERSON><PERSON>icker,
  LoadingS<PERSON>ner,
  DatePicker,
  TextInput,
  PageContainer,
  DropDownPicker,
} from '../../../components';

import ProfileTrainer from './ProfileTrainer';

// Helpers
import * as validate from '../../../util/validate';
import {
  ROLES,
  passwordRecoveryUAURL,
  androidSafeLayoutAnimation,
} from '../../../constants';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';
import { getCountryCode } from '../../../util/utils';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

// Images
const plusIcon = require('../../../resources/plusIcon.png');

// PropTypes
const propTypes = {
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    account_removal_requested: PropTypes.bool,
  }),
  logout: PropTypes.func.isRequired,
  updateCurrentUser: PropTypes.func.isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    setParams: PropTypes.func,
    dispatch: PropTypes.func,
    state: PropTypes.shape({
      params: PropTypes.shape({
        user: PropTypes.any,
        editEnabled: PropTypes.bool,
      }),
    }),
  }).isRequired,
};

const defaultProps = {
  currentUser: null,
};
const currentCountry = RNLocalize.getCountry();
// Class
class ProfileContainer extends Component {
  constructor(props) {
    super(props);
    const { params = {} } = props.route;
    const { isOwner = true, user } = params;
    this.state = {
      isOwner,
      user,
      userInputs: null,
      isLoading: true,
      prevPhone: '',
      countryCode: getCountryCode(currentCountry),
      countryISO: currentCountry,
    };
  }

  componentDidMount() {
    analytics().logEvent('screen_view', { screen_name: 'profile_trainer' });
    this.getUser();
    this.props.navigation.setParams({
      editEnabled: false,
    });
    const hasClubProfile = this.props.trainerActiveProfile?.ClubId;
    const backgroundColor = hasClubProfile ? colors.black : colors.duskBlue;
    this.props.navigation.setOptions({
      title: 'My Account',
      headerStyle: { backgroundColor },
      headerTitleStyle: {
        color: colors.white,
        fontFamily: 'Avenir',
      },
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
  }

  onEditCerts = (route) => {
    this.props.navigation.navigate(route, { isEditing: true });
  };

  onPressCancel = () => {
    this.setState({ countryCode: '' });
    // LayoutAnimation.easeInEaseOut();
    this.props.navigation.setParams({
      editEnabled: false,
    });
    this.props.navigation.setOptions({
      title: 'My Account',
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });
  };

  onPressCredential = (url) => {
    this.props.navigation.navigate('WebView', {
      title: 'NASM.ORG',
      uri: url,
    });
  };

  onPressEditProfile = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.props.navigation.setParams({
      editEnabled: true,
    });
    this.props.navigation.setOptions({
      title: 'Edit Account',
      headerLeft: this.renderHeaderLeft,
      headerRight: this.renderHeaderRight,
    });

    const user = this.state.isOwner ? this.props.currentUser : this.state.user;
    this.setState({ userInputs: { ...user } });
  };

  onPressSave = () => {
    const { first_name, last_name } = this.state.userInputs;
    if (
      !validate.name(first_name)
      || validate.containsAccentedChars(first_name)
    ) {
      Alert.alert(
        'First name invalid',
        'Please enter a valid first name to continue.',
      );
    } else if (
      !validate.name(last_name)
      || validate.containsAccentedChars(last_name)
    ) {
      Alert.alert(
        'Last name invalid',
        'Please enter a valid last name to continue.',
      );
    } else if (
      this.state.userInputs.phone_number
      && this.state.userInputs.phone_number.length !== 0
      && !validate.phone(
        this.state.userInputs.phone_number,
        this.state.countryCode,
      )
    ) {
      Alert.alert(
        'Phone number invalid',
        'Please enter a valid phone number to continue.',
      );
    } else {
      this.updateUser();
    }
  };

  getUser = async () => {
    this.setState({ countryCode: getCountryCode(currentCountry) });
    const userId = this.state.isOwner
      ? this.props.currentUser.id
      : this.state.user.id;
    const { isOwner } = this.state;

    // Make sure we have the latest user data
    this.setState({ isLoading: true });
    const user = await nasm.api.getMyUser(userId);
    await setUser(user.id);
    await setUserProp('Email', user.email);
    await setUserProp('Role', user.nasm_role);
    if (user.phone_number) {
      const countryObj = validate.getCountryISO(user.phone_number);
      if (countryObj?.country) {
        const countryISO = countryObj.country;
        this.setState({
          countryCode: getCountryCode(countryISO),
          countryISO,
        });
      }
    }
    if (user.role === ROLES.TRAINER) {
      await setUserProp(
        'active_subscriptions',
        moment().isSameOrBefore(user.subscription_expiration_date),
      );
      await setUserProp(
        'Purchases',
        (!!user.purchased_product_ids?.length).toString(),
      );
    }
    setUserProp('LevelOfSubscription', subscriptionLevels(user));

    if (isOwner) {
      this.props.updateCurrentUser(user);
    }

    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ isLoading: false, user, userInputs: { ...user } });
  };

  formatPhoneNumber = (phone, isCountrySelected) => {
    const currentDigits = validate.getDigits(phone);

    if (phone) {
      const number = phone.substr(phone.indexOf(' ') + 1).replace(/\s/g, '');
      if (this.state.prevPhone) {
        if (
          phone.length > this.state.prevPhone.length
          && currentDigits.length === 10
        ) {
          return validate.formatPhoneNumber(number, this.state.countryCode);
        }
        return phone;
      }
      if (
        phone.length > 0
        && currentDigits.length >= 10
        && isCountrySelected === undefined
      ) {
        const formattedNumber = validate.formatPhoneNumber(
          number,
          this.state.countryCode,
        );
        this.state.userInputs.phone_number = formattedNumber;
        return validate.formatPhoneNumber(number, this.state.countryCode);
      }

      return phone;
    }
    return '';
  };

  logout = () => {
    this.props.logout();
    const resetAction = CommonActions.reset({
      index: 0,
      key: null,
      routes: [{ name: 'Welcome' }],
    });
    this.props.navigation.dispatch(resetAction);
  };

  scrollToBottom = () => {
    if (this.editPageContainer && this.editPageContainer.keyboardHandler) {
      this.editPageContainer.keyboardHandler.scrollToEnd();
    }
  };

  selectProfilePicture = (image) => {
    if (image && image.uri) {
      this.updateUserInput({ avatar_url: image.uri });
      this.setState({ newAvatarImage: image });
    }
  };

  updatePhoneNumber = (phoneNumber, isCountrySelected) => {
    let phone_number = phoneNumber;
    const number = phone_number
      .substr(phone_number.indexOf(' ') + 1)
      .replace(/\s/g, '');
    if (number.length === 10) {
      phone_number = validate.formatPhoneNumber(number, this.state.countryCode);
    } else if (isCountrySelected) {
      phone_number = validate.formatPhoneNumber(number, this.state.countryCode);
    }
    this.setState({ prevPhone: phone_number });
    this.updateUserInput({ phone_number });
  };

  updateUser = async () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ isLoading: true });
    // TODO: this needs to handle update for both client and trainers
    try {
      // We can update the user and avatar simultaneously, so create a promise array
      const requests = [];
      requests.push(
        nasm.api.updateTrainer(
          this.state.userInputs,
          this.props.currentUser.id,
        ),
      );
      if (this.state.newAvatarImage) {
        requests.push(
          nasm.api.updateUserAvatar(
            this.props.currentUser.id,
            this.state.newAvatarImage,
          ),
        );
      }
      // Wait for both requests to resolve before proceeding
      await Promise.all(requests);
      this.props.navigation.setParams({
        editEnabled: false,
      });
      this.props.navigation.setOptions({
        title: 'My Account',
        headerLeft: this.renderHeaderLeft,
        headerRight: this.renderHeaderRight,
      });
      analytics().logEvent('edit_profile_trainer');
      await this.getUser();
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      LayoutAnimation.configureNext(androidSafeLayoutAnimation);
      this.setState({ isLoading: false });
    }
  };

  updateUserInput = (updateObject) => {
    this.setState((prevState) => ({
      userInputs: {
        ...prevState.userInputs,
        ...updateObject,
      },
    }));
  };

  selectedValue = (index) => {
    this.setState({ countryCode: index }, () => (this.state.prevPhone
      ? this.updatePhoneNumber(this.state.prevPhone, true)
      : this.formatPhoneNumber(this.state.userInputs.phone_number, true)));
  };

  checkDeletionRequest = () => {
    if (this.props.currentUser.account_removal_requested) {
      Alert.alert(
        'Error',
        'The process to delete your NASM Edge account has been initiated. Account deletion may take 7-10 business days, until then you can still access your account.',
      );
      return;
    }
    this.props.navigation.navigate('DeleteAccount');
  };

  renderEditProfile() {
    const userInputs = this.state.userInputs || {};
    return (
      <PageContainer
        scrollEnabled
        ref={(ref) => {
          this.editPageContainer = ref;
        }}
        containerStyle={{
          padding: 27,
          backgroundColor: colors.white,
          marginHorizontal: '2%',
        }}
      >
        <View style={styles.profilePicContainer}>
          <AvatarPicker
            onSelectImage={(image) => this.selectProfilePicture(image)}
            iconSize={30}
            disabled={false}
            imageUri={this.state.userInputs.avatar_url}
            containerStyle={{
              borderWidth: 0,
              width: 80,
              height: 80,
              borderRadius: 40,
            }}
            icon={plusIcon}
            iconContainerStyle={{
              backgroundColor: colors.macaroniAndCheese,
              width: 33,
              height: 33,
              borderRadius: 16.5,
            }}
            shadow={false}
            navigation={this.props.navigation}
          />
        </View>
        {this.state.user.ua_guid && (
          <View>
            <Text style={styles.editHeaderTextStyle}>Linked NASM Account</Text>
            <View
              style={{
                height: 46,
                borderColor: colors.subGreyLight,
                borderBottomWidth: 1,
                justifyContent: 'center',
              }}
            >
              <Text style={styles.emailText}>{this.state.user.ua_email}</Text>
            </View>
          </View>
        )}
        <Text style={styles.editHeaderTextStyle}>Email</Text>
        <View
          style={{
            height: 46,
            borderColor: colors.subGreyLight,
            borderBottomWidth: 1,
            justifyContent: 'center',
          }}
        >
          <Text style={styles.emailText}>{userInputs.email}</Text>
        </View>
        <Text style={styles.editHeaderTextStyle}>First Name</Text>
        <TextInput
          value={userInputs.first_name}
          onChangeText={(value) => {
            const first_name = validate.removeAllSpecialCharacters(value);
            this.updateUserInput({ first_name: first_name.trimLeft() });
          }}
          returnKeyType="done"
          validation={validate.name}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(userInputs.first_name)
              ? validate.accentValidationErrorMsg('first name')
              : ''
          }
          maxLength={64}
        />
        <Text style={styles.editHeaderTextStyle}>Last Name</Text>
        <TextInput
          value={userInputs.last_name}
          onChangeText={(value) => {
            const last_name = validate.removeAllSpecialCharacters(value);
            this.updateUserInput({ last_name: last_name.trimLeft() });
          }}
          returnKeyType="done"
          validation={validate.name}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(userInputs.last_name)
              ? validate.accentValidationErrorMsg('last name')
              : ''
          }
          maxLength={64}
        />
        <Text style={styles.editHeaderTextStyle}>Country</Text>
        <TouchableOpacity>
          <CountryPicker
            disable={false}
            animationType="slide"
            containerStyle={styles.pickerContainerStyle}
            hideCountryFlag
            hideCountryCode
            searchBarStyle={styles.searchBarStyle}
            selectedValue={this.selectedValue}
            country={this.state.countryISO}
          />
        </TouchableOpacity>

        <Text style={styles.editHeaderTextStyle}>Mobile Number</Text>
        <TextInput
          placeholder="### ### ####"
          placeholderTextColor={colors.subGreyTwo}
          value={this.formatPhoneNumber(userInputs.phone_number)}
          onChangeText={(phone_number) => this.updatePhoneNumber(phone_number)}
          keyboardType="phone-pad"
          maxLength={20}
          validation={validate.phone}
          returnKeyType="done"
        />

        <Text style={styles.editHeaderTextStyle}>Assigned Sex</Text>
        <DropDownPicker
          containerStyle={styles.inputUnderline}
          useNativeAndroidPickerStyle
          data={gender_types}
          placeholder="Assigned Sex"
          selected={this.state.userInputs.gender_type}
          onValueChange={(gender) => {
            this.updateUserInput({ gender_type: gender.id });
          }}
          onIosPickerVisible={(visible) => {
            if (visible) this.scrollToBottom();
          }}
          textStyle={{
            textAlign: 'left',
          }}
        />
        <Text style={styles.editHeaderTextStyle}>Birthday</Text>
        <DatePicker
          value={
            this.state.userInputs.birth_date
              ? moment(this.state.userInputs.birth_date).toDate()
              : undefined
          }
          placeholder="Birthday"
          maxValue={new Date()}
          onValueChange={(birth_date) => this.updateUserInput({ birth_date })}
          onIosPickerVisible={(visible) => {
            if (visible) this.scrollToBottom();
          }}
          defaultDate={moment().subtract(18, 'years').toDate()}
        />
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <TouchableOpacity
            style={[styles.profileActionButton, { width: 157 }]}
            onPress={
              this.props.currentUser.ua_guid != null
                ? this.renderUAForgotPassword
                : this.renderForgotPassword
            }
          >
            <Text style={styles.profileActionButtonText}>Reset Password</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.profileActionButton}
            onPress={this.logout}
          >
            <Text style={styles.profileActionButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={styles.deleteContainer}
          onPress={this.checkDeletionRequest}
        >
          <Text style={styles.deleteText}>Delete Account</Text>
        </TouchableOpacity>
      </PageContainer>
    );
  }

  renderForgotPassword = () => {
    this.props.navigation.navigate('ForgotPassword');
  };

  renderHeaderLeft = () => {
    // We have to pass navigation down to these methods or they will be one render cycle behind
    const editEnabled = this.props.route?.params?.editEnabled;
    if (!this.state.isOwner) {
      return (
        <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
      );
    }
    if (!editEnabled) {
      return <View />;
    }
    if (editEnabled) {
      return <HeaderLeftButton onPress={this.onPressCancel} title="Cancel" />;
    }
    return null;
  };

  renderHeaderRight = () => {
    const editEnabled = this.props.route?.params?.editEnabled;
    // We have to pass navigation down to these methods or they will be one render cycle behind
    if (editEnabled) {
      return (
        <HeaderRightButton
          onPress={() => this.onPressSave()}
          disabled={this.state.isLoading}
          title="Save"
          titleStyle={styles.headerLeftText}
        />
      );
    }
    return <View />;
  };

  renderUAForgotPassword = () => {
    passwordRecoveryUAURL().then((UAURL) => {
      this.props.navigation.navigate('WebView', {
        title: 'NASM.ORG',
        uri: UAURL,
      });
    });
  };

  render() {
    if (this.state.isLoading) {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <LoadingSpinner visible />
        </View>
      );
    }
    const user = this.state.isOwner ? this.props.currentUser : this.state.user;
    if (this.props.route.params?.editEnabled) {
      return this.renderEditProfile();
    }
    return (
      <ScrollView
        testID="ProfileScrollView"
        style={{ backgroundColor: colors.background }}
      >
        {user.role === ROLES.TRAINER && (
          <ProfileTrainer
            {...this.props}
            trainer={user}
            isOwner={this.state.isOwner}
            onPressCredential={this.onPressCredential}
            onEditCerts={this.onEditCerts}
            onPressEditProfile={this.onPressEditProfile}
            onPressLogout={this.logout}
            navigation={this.props.navigation}
          />
        )}
      </ScrollView>
    );
  }
}

const { width } = Dimensions.get('window');
const scale = width / 400;

const styles = {
  container: {
    flex: 1,
  },
  headerLeftText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  editHeaderTextStyle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: colors.subGrey,
    marginTop: 20,
  },
  profilePicContainer: {
    flex: 0,
    borderRadius: 66,
    alignSelf: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  emailText: {
    fontFamily: 'Avenir',
    fontSize: curvedScale(14),
    fontWeight: '500',
    color: colors.subGrey,
  },
  profileActionButton: {
    width: 125,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.subGrey,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginBottom: 28,
    marginTop: 28,
  },
  profileActionButtonText: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    color: colors.subGrey,
  },
  linkButton: {
    width: 245,
    borderWidth: 1,
    borderRadius: 17.5,
    borderColor: colors.subGrey,
    padding: 8 * scale,
    textAlign: 'center',
    alignSelf: 'center',
    marginTop: 20,
    color: colors.subGrey,
    fontWeight: 'bold',
  },
  inputUnderline: {
    borderBottomWidth: 1,
    borderBottomColor: colors.silver,
  },
  searchBarStyle: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'row',
    marginRight: 10,
    marginLeft: 20,
  },
  pickerContainerStyle: {
    borderBottomWidth: 1,
    borderBottomColor: colors.silver,
    flex: 1,
    paddingTop: 20,
    paddingBottom: 12,
    marginBottom: 10,
  },
  deleteContainer: {
    alignItems: 'center',
  },
  deleteText: {
    textDecorationLine: 'underline',
    color: colors.subGrey,
  },
};

// Picker Data
const gender_types = [
  { label: 'Male', id: 1 },
  { label: 'Female', id: 2 },
];
ProfileContainer.propTypes = propTypes;
ProfileContainer.defaultProps = defaultProps;
const mapStateToProps = ({ currentUser, trainerActiveProfile }) => ({
  currentUser,
  trainerActiveProfile,
});
const mapDispatchToProps = {
  logout,
  updateCurrentUser,
};
export default connect(mapStateToProps, mapDispatchToProps)(ProfileContainer);
