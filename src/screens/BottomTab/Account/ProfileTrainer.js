import React, { Component } from 'react';
import PropTypes from 'prop-types';
import IconFeader from 'react-native-vector-icons/Feather';
import IconAntDesign from 'react-native-vector-icons/AntDesign';

// Components
import {
  Text,
  TouchableOpacity,
  Image,
  View,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { connect } from 'react-redux';
import moment from 'moment';
import { PracticeSectionCard } from '../../../components';
import CPTExamPackageCard from '../../../components/CPTExamPackageCard';
import { APPLICATION_ROUTES, FEATURE_FLAGS } from '../../../constants';

// Styles
import { colors, shadow } from '../../../styles';
import { getUserDataAction } from '../../../actions/artichoke/User.actions';

import { track } from '../../../util/Analytics';
import { curvedScale } from '../../../util/responsive';

import {
  CALCULATOR_CONTEXTS,
  setCalculatorContext,
} from '../../../reducers/calculatorContextReducer';

// Images
const flashCardImg = require('../../../../assets/imgPackageFlashCard.png');
const examImg = require('../../../resources/imgPackageExam.png');
const stripeNotConnected = require('../../../assets/stripeGrey.png');
const stripeConnected = require('../../../assets/stripeGreen.png');
const tempProfilePic = require('../../../resources/defaultProfile.png');
const btnAddGray = require('../../../assets/btnAddGray.png');

// PropTypes
const propTypes = {
  trainer: PropTypes.shape({
    phone_number: PropTypes.string,
    email: PropTypes.string,
    trainer_user: PropTypes.shape({
      certificates: PropTypes.array,
    }),
  }).isRequired,
  isOwner: PropTypes.bool.isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
  }).isRequired,
  onPressEditProfile: PropTypes.func.isRequired,
  onPressLogout: PropTypes.func.isRequired,
};

const defaultProps = {
  isLoading: false,
};

IconAntDesign.loadFont();
// Class
class ProfileTrainer extends Component {
  constructor(props) {
    super(props);
    this.renderRow = this.renderRow.bind(this);
    this.renderStripeStatus = this.renderStripeStatus.bind(this);
    this.renderCertificateCount = this.renderCertificateCount.bind(this);
    this.defaultSections = [
      {
        title: 'Income & Sales Tax',
        destination: APPLICATION_ROUTES.INCOME,
        isPro: true,
      },
      {
        title:
          this.props.trainer.trainer_user.certificates.length === 0
            ? 'Add Your Certificates'
            : 'Certificates',
        destination: 'Certifications',
        decorator: this.renderCertificateCount,
      },
      {
        title: 'Payments',
        decorator: this.renderStripeStatus,
        destination: APPLICATION_ROUTES.CONNECT_WITH_STRIPE,
        isPro: true,
      },
      {
        title: 'Measurement Units',
        destination: 'MeasurementSystem',
      },
      {
        title: 'Subscription',
        destination: 'SubscriptionManagement',
        hide: () => !this.canShowSubsriptions(),
      },
      {
        title: 'Calculators',
        destination: 'Calculators',
      },
      {
        title: 'Timezone & Currency',
        destination: APPLICATION_ROUTES.TIMEZONE_CURRENCY,
        isPro: true,
      },
      {
        title: 'Archived Services',
        destination: APPLICATION_ROUTES.ARCHIVED_SERVICES,
        isHidden: !!props.trainerActiveProfile?.ClubId,
        isPro: true,
      },
      {
        title: 'Terms of Use',
        destination: 'TermsAndConditions',
        params: { readOnly: true },
      },
      {
        title: 'Privacy Policy',
        destination: 'PrivacyPolicy',
        params: { readOnly: true },
      },
      { title: 'Help', destination: 'Resources' },
    ];
    this.props.getUserDataAction();
  }

  canShowSubsriptions = () => {
    if (
      !FEATURE_FLAGS.TRAINER_PRO_ENABLED
      || this.props.trainerActiveProfile?.ClubId
    ) return false;
    const purchases = this.props.trainer.in_app_purchases;
    return (
      purchases
      && purchases.length > 0
      && moment().isSameOrBefore(this.props.trainer.subscription_expiration_date)
    );
  };

  renderCertificateCount() {
    const certificates = this.props.trainer.trainer_user.certificates.length || 0;
    if (certificates === 0) {
      return (
        <View style={styles.btnView}>
          <Image source={btnAddGray} style={styles.plusIcon} />
        </View>
      );
    }

    return (
      <Text style={styles.certificatesText}>
        {`${certificates} Certification${certificates !== 1 ? 's' : ''}`}
      </Text>
    );
  }

  renderRow(item, idx) {
    return (
      <TouchableOpacity
        key={idx}
        style={styles.rowContainer}
        onPress={() => {
          if (!item.destination) return;
          if (item.destination === 'Calculators') {
            this.props.setCalculatorContext(
              CALCULATOR_CONTEXTS.TRAINER_ACCOUNT,
            );
          }
          this.props.navigation.navigate(item.destination, item.params);
          if (item.destination === APPLICATION_ROUTES.INCOME) {
            track('view_income');
          }
        }}
      >
        <Text style={styles.rowTitle}>{item.title}</Text>
        {item.decorator && (
          <View style={styles.itemDecorator}>{item.decorator()}</View>
        )}
        {item.title !== 'Add Your Certificates' && (
          <IconFeader
            style={styles.chevron}
            name="chevron-right"
            size={27}
            color={colors.fillDarkGrey}
          />
        )}
      </TouchableOpacity>
    );
  }

  renderSections() {
    const isPro = this.props.currentUser.bypass_subscription
      || this.props.currentUser.permissions?.some(
        (p) => p.name === 'edge_trainer_pro',
      );
    if (!FEATURE_FLAGS.TRAINER_PRO_ENABLED || !isPro) {
      return this.defaultSections
        .filter((s) => !s.isPro && !(s.hide && s.hide()) && !s.isHidden)
        .map(this.renderRow);
    }
    return this.defaultSections
      .filter((s) => !(s.hide && s.hide()) && !s.isHidden)
      .map(this.renderRow);
  }

  renderStripeStatus() {
    const isStripeConnected = Boolean(
      this.props.user?.account?.StripeAccount?.stripeSecretKey,
    );
    const logo = isStripeConnected ? stripeConnected : stripeNotConnected;
    const text = isStripeConnected ? ' Connected' : ' Connect with Stripe';
    const textColor = isStripeConnected
      ? colors.goodGreen
      : colors.fillDarkGrey;
    return (
      <View style={styles.stripeConnectedContainer}>
        {this.props.userDataLoading ? (
          <ActivityIndicator color={colors.subGrey} />
        ) : (
          <>
            <Image source={logo} style={styles.logo} />
            <Text style={{ ...styles.stripeConnectedText, color: textColor }}>
              {text}
            </Text>
          </>
        )}
      </View>
    );
  }

  renderTrainerInfo(trainer) {
    return (
      <View style={styles.trainerInfoContainer}>
        <Image
          style={styles.profilePic}
          source={
            trainer.avatar_url ? { uri: trainer.avatar_url } : tempProfilePic
          }
        />
        <Text style={styles.trainerFullName}>{trainer.full_name}</Text>
        <Text style={styles.trainerEmail}>{`email: ${trainer.email}`}</Text>
        <View style={styles.editProfileContainer}>
          <TouchableOpacity
            style={styles.editProfileBtn}
            textStyles={{
              color: colors.subGrey,
            }}
            onPress={this.props.onPressEditProfile}
          >
            <Text style={styles.editBtnText}>Edit</Text>
          </TouchableOpacity>
          <View style={styles.logoutBtn}>
            <TouchableOpacity
              textStyles={{
                color: colors.subGrey,
              }}
              onPress={this.props.onPressLogout}
            >
              <Text style={styles.logoutBtnText}>Logout</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  render() {
    const { trainer } = this.props;
    // Initialize a single variable to check if the user has purchased cpt_exam_prep
    // Change this to true if testing
    // TODO: use an environment variable to override for testing
    let userPurchasedExamPrep = false;
    if (
      trainer.bypass_subscription
      || (this.props.currentUser.purchased_product_ids
        && this.props.currentUser.purchased_product_ids.includes('cpt_exam_prep'))
    ) {
      userPurchasedExamPrep = true;
    }
    const validSubscription = this.props.trainerActiveProfile?.ClubId
      || (this.props.isOwner && userPurchasedExamPrep);
    return (
      <View style={styles.container}>
        {this.renderTrainerInfo(trainer)}
        <View style={styles.examCardCont}>
          <PracticeSectionCard
            navigation={this.props.navigation}
            visible={validSubscription}
            cardTitle={'Flash\nCards'}
            image={flashCardImg}
            goTo="FlashCardModal"
          />
          <PracticeSectionCard
            navigation={this.props.navigation}
            visible={validSubscription}
            cardTitle={'Exam\nQuestions'}
            image={examImg}
            goTo="ExamPrepModal"
            end
          />
          <CPTExamPackageCard
            navigation={this.props.navigation}
            visible={!validSubscription}
          />
        </View>
        <View style={styles.settingsCont}>
          <View style={styles.settingsList} />
          {this.renderSections()}
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 5,
    backgroundColor: colors.white,
  },
  certificatesText: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    marginTop: 4,
    marginLeft: 2,
  },
  rowContainer: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    borderColor: colors.bordergrey,
    borderBottomWidth: 1,
    padding: 20,
    alignItems: 'center',
  },
  rowTitle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    color: colors.black,
    flex: 1,
  },
  itemDecorator: {
    alignSelf: 'flex-end',
  },
  chevron: {
    alignSelf: 'flex-end',
  },
  logo: {
    width: 18,
    height: 19,
    alignSelf: 'center',
  },
  stripeConnectedContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  stripeConnectedText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    alignSelf: 'center',
    marginRight: '3%',
  },
  trainerInfoContainer: {
    flex: 1,
    alignItems: 'center',
    marginTop: 27,
    marginBottom: 35,
  },
  profilePic: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  trainerFullName: {
    fontFamily: 'Avenir',
    fontSize: 17,
    marginTop: 10,
  },
  trainerEmail: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.subGrey,
  },
  editProfileContainer: {
    flex: 1,
    flexDirection: 'row',
    marginTop: 27,
  },
  editProfileBtn: {
    backgroundColor: colors.white,
    borderColor: colors.subGrey,
    width: 94,
    height: 35,
    borderStyle: 'solid',
    borderWidth: 2,
    borderRadius: 17.5,
    justifyContent: 'center',
    marginRight: 24,
  },
  editBtnText: {
    alignSelf: 'center',
    fontFamily: 'Avenir',
    fontSize: 14,
    color: colors.subGrey,
  },
  logoutBtn: {
    backgroundColor: colors.white,
    borderColor: colors.subGrey,
    width: 94,
    height: 35,
    borderStyle: 'solid',
    borderWidth: 2,
    borderRadius: 17.5,
    justifyContent: 'center',
  },
  logoutBtnText: {
    alignSelf: 'center',
    fontFamily: 'Avenir',
    fontSize: 14,
    color: colors.subGrey,
  },
  examCardCont: {
    flex: 1,
    marginLeft: 21,
    marginRight: 21,
    flexDirection: 'row',
  },
  settingsCont: {
    flex: 1,
    marginTop: 27,
    flexDirection: 'column',
  },
  settingsList: {
    height: 1,
    width: '100%',
    backgroundColor: colors.bordergrey,
  },
  plusIcon: {
    tintColor: colors.white,
    height: curvedScale(20),
    width: curvedScale(20),
    resizeMode: 'contain',
  },
  btnView: {
    width: curvedScale(28),
    height: curvedScale(28),
    borderRadius: curvedScale(28) / 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.macaroniAndCheese,
    marginRight: curvedScale(10),
    marginLeft: curvedScale(13),
    ...shadow,
  },
});

// Export
ProfileTrainer.propTypes = propTypes;
ProfileTrainer.defaultProps = defaultProps;

const mapStateToProps = (state) => ({
  user: state.user.details,
  userDataLoading: state.loadingComponents.userDataLoading,
  trainerActiveProfile: state.trainerActiveProfile,
});

const mapDispatchToProps = {
  getUserDataAction,
  setCalculatorContext,
};

export default connect(mapStateToProps, mapDispatchToProps)(ProfileTrainer);
