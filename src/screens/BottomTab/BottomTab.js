/* eslint-disable react-redux/prefer-separate-component-file */
/* eslint-disable react/prop-types */
import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Image, Platform } from 'react-native';
import { connect } from 'react-redux';
import moment from 'moment';
import { colors } from '../../styles';
import { ROLES, FEATURE_FLAGS } from '../../constants';

// Trainer Tabs
import TrainerHome from './TrainerHome';
import TrainerAccount from './Account/ProfileContainer';
import LibrariesTab from './LibrariesTab';

// Client Tabs
import ClientAccountTab from '../ClientTabs/ClientAccountTab';
import ClientHome from '../ClientTabs/ClientTabView';
import { ClientDashboard as Bookings } from '../artichoke/client';
import ExerciseCatalog from '../ProgramLibrary/ExerciseCatalog';
import { Dashboard } from '../artichoke';

const homeIcon = require('../../assets/imgTabBarHomeOff.png');
const exercisesIcon = require('../../resources/libraries.png');
const profileIcon = require('../../resources/account.png');
const servicesIcon = require('../../resources/imgTabBarServices.png');

const Tab = createBottomTabNavigator();

function BottomTab(props) {
  if (!props.currentUser.id) return null;
  /* Trainer Tabs */
  if (props.currentUser.role === ROLES.TRAINER) {
    return (
      <Tab.Navigator
        screenOptions={{
          tabBarActiveTintColor: colors.nasmBlue,
          tabBarInactiveTintColor: colors.cloudyBlue,
          tabBarAllowFontScaling: false,
          tabBarStyle: {
            elevation: 0,
          },
          headerTitleAlign: 'center',
          tabBarHideOnKeyboard: Platform.OS === 'android',
        }}
      >
        <Tab.Screen
          name="Home"
          options={{
            tabBarIcon: ({ color }) => (
              <Image style={{ tintColor: color }} source={homeIcon} />
            ),
            headerShown: false,
          }}
          component={TrainerHome}
        />
        <Tab.Screen
          name="Libraries"
          options={{
            tabBarIcon: ({ color }) => (
              <Image style={{ tintColor: color }} source={exercisesIcon} />
            ),
            headerShown: false,
          }}
          component={LibrariesTab}
        />
        {FEATURE_FLAGS.TRAINER_PRO_ENABLED && !props?.trainerActiveProfile && (
          <Tab.Screen
            name="ServicesTab"
            options={{
              title: 'Services',
              tabBarIcon: ({ color }) => (
                <Image style={{ tintColor: color }} source={servicesIcon} />
              ),
              headerShown: false,
            }}
            listeners={({ navigation }) => ({
              tabPress: (event) => {
                let hasSubscription = false;
                if (props.currentUser.bypass_subscription) {
                  hasSubscription = true;
                } else if (
                  props.currentUser.subscription === 'edge_trainer_pro'
                ) {
                  hasSubscription = true;
                  const expirationDate = props.currentUser.subscription_expiration_date;
                  if (
                    !expirationDate
                    || !moment().isValid(expirationDate)
                    || moment().utc().isAfter(moment.utc(expirationDate))
                  ) {
                    hasSubscription = false;
                  }
                }
                if (!hasSubscription) {
                  event.preventDefault();
                  navigation.navigate('ProSubscriptions');
                }
              },
            })}
            component={Dashboard}
          />
        )}
        <Tab.Screen
          name="Account"
          options={() => ({
            tabBarTestID: 'TrainerAccountTab',
            tabBarIcon: ({ color }) => (
              <Image style={{ tintColor: color }} source={profileIcon} />
            ),
          })}
          component={TrainerAccount}
        />
      </Tab.Navigator>
    );
  }
  /* Client Tabs */
  if (props.currentUser.role === ROLES.CLIENT) {
    const isTrainerAvailable = FEATURE_FLAGS.TRAINER_PRO_ENABLED
      && props.currentUser.client_user.trainer;
    return (
      <Tab.Navigator
        screenOptions={{
          tabBarActiveTintColor: colors.nasmBlue,
          tabBarInactiveTintColor: colors.cloudyBlue,
          headerTitleAlign: 'center',
        }}
      >
        <Tab.Screen
          name="Home"
          options={{
            tabBarIcon: ({ color }) => (
              <Image style={{ tintColor: color }} source={homeIcon} />
            ),
            headerStyle: { backgroundColor: colors.duskBlue },
            headerTitle: "Today's Workout",
            headerTitleStyle: {
              color: colors.white,
              fontFamily: 'Avenir-heavy',
            },
            headerShown: !isTrainerAvailable,
          }}
          component={ClientHome}
        />
        <Tab.Screen
          name="Exercises"
          options={{
            tabBarIcon: ({ color }) => (
              <Image style={{ tintColor: color }} source={exercisesIcon} />
            ),
            headerStyle: { backgroundColor: colors.duskBlue },
            headerTitleStyle: {
              color: colors.white,
              fontFamily: 'Avenir',
            },
          }}
          component={ExerciseCatalog}
        />
        {FEATURE_FLAGS.TRAINER_PRO_ENABLED
        && (props.currentUser.client_user.trainer?.subscription
          === 'edge_trainer_pro'
          || props.currentUser.client_user.trainer?.bypass_subscription) ? (
            <Tab.Screen
              name="Bookings"
              options={{
                tabBarIcon: ({ color }) => (
                  <Image style={{ tintColor: color }} source={servicesIcon} />
                ),
                headerShown: false,
              }}
              component={Bookings}
            />
          ) : null}
        <Tab.Screen
          name="Account"
          options={{
            tabBarIcon: ({ color }) => (
              <Image style={{ tintColor: color }} source={profileIcon} />
            ),
            headerStyle: { backgroundColor: colors.duskBlue },
            headerTitleStyle: {
              color: colors.white,
              fontFamily: 'Avenir',
            },
          }}
          component={ClientAccountTab}
        />
      </Tab.Navigator>
    );
  }

  return null;
}

const mapStateToProps = ({ currentUser, trainerActiveProfile }) => ({
  currentUser,
  trainerActiveProfile,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(BottomTab);
