/* eslint-disable max-classes-per-file */
import React, { Component, PureComponent } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import {
  View, Text, Dimensions, Image, StyleSheet,
} from 'react-native';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Screens
import { TouchableOpacity } from 'react-native-gesture-handler';
import DashboardTrainer from '../TrainerTabs/DashboardTrainer';
import ClientGroups from '../TrainerTabs/ClientGroups';
import ChatStack from '../Chat/ChatStack';

// Styles
import { materialTabBarOptions, colors } from '../../styles';
import { FEATURE_FLAGS, chatClient } from '../../constants';
import ChannelScreen from '../Chat/ChannelScreen';
import NewConversation from '../Chat/NewConversation';
import ChannelListScreen from '../Chat/ChannelListScreen';
import HeaderLeftButton from '../../components/HeaderLeftButton';

const tempProfilePic = require('../../resources/defaultProfile.png');
const arrowDownIcon = require('../../resources/btnArrowDown.png');

const { width } = Dimensions.get('window');

class Chat extends Component {
  static navigationOptions = ({ route }) => {
    const activeScreen = route.state?.routes[route.state.index];
    let tabBarVisible = true;
    if (activeScreen && activeScreen.params) {
      if (typeof activeScreen.params.tabBarVisible === 'boolean') {
        tabBarVisible = activeScreen.params.tabBarVisible;
      }
    }

    return {
      tabBarVisible,
      tabBarLabel: ({ tintColor }) => {
        const isBadged = route.params?.isBadged;
        return (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text
              allowFontScaling={false}
              style={{
                ...materialTabBarOptions.tabBarOptions.labelStyle,
                color: tintColor,
              }}
            >
              Chat
            </Text>
            {isBadged && (
              <View
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: colors.medYellow,
                  marginLeft: 5,
                }}
              />
            )}
          </View>
        );
      },
    };
  };

  render() {
    return <ChatStack navigation={this.props.navigation} />;
  }
}
Chat.router = ChatStack.router;
const tabWidth = width / 3;
const indicatorWidth = tabWidth / 1.2;

const Tab = createMaterialTopTabNavigator();
const Stack = createStackNavigator();

class TrainerHomeTabs extends Component {
  componentDidMount() {
    this.props.navigation.setParams({
      headerShown: !FEATURE_FLAGS.CHAT_ENABLED,
      isBadged: chatClient.user.total_unread_count > 0,
    });
    chatClient.on((event) => {
      if (event.total_unread_count > 0) {
        this.props.navigation.setParams({ isBadged: true });
      } else if (event.total_unread_count === 0) {
        this.props.navigation.setParams({ isBadged: false });
      }
    });
  }

  renderTabBarLabel = (label, focused, isBadged) => (
    <View style={styles.tabBarLabel}>
      <Text
        allowFontScaling={false}
        style={{
          ...materialTabBarOptions.tabBarOptions.tabBarLabelStyle,
          color: focused ? colors.white : colors.inactiveWhite,
        }}
      >
        {label}
      </Text>
      {isBadged && <View style={styles.tabIndicator} />}
    </View>
  );

  render() {
    const hasClubProfile = this.props.route?.params?.trainerActiveProfile?.ClubId;
    const backgroundColor = hasClubProfile ? colors.black : colors.duskBlue;
    const paddingTop = hasClubProfile ? 0 : 17;
    return (
      <Tab.Navigator
        screenOptions={() => ({
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarStyle,
            backgroundColor,
            paddingTop: 17,
            elevation: 0,
          },
          tabBarTestID: 'TrainerTabHome',
          tabBarActiveTintColor: colors.white,
          tabBarInactiveTintColor: '#8aa1bd',
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            backgroundColor: colors.medYellow,
            width: indicatorWidth,
            // Based of https://github.com/satya164/react-native-tab-view/issues/944#issuecomment-599224375
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
          lazy: false,
        })}
        style={{
          ...materialTabBarOptions.tabBarOptions.tabBarStyle,
          backgroundColor,
          paddingTop,
        }}
      >
        <Tab.Screen
          name="Clients"
          component={DashboardTrainer}
          options={{
            tabBarLabel: ({ focused }) => this.renderTabBarLabel('Clients', focused),
            tabBarTestID: 'TrainerTabClient',
          }}
        />
        <Tab.Screen
          name="Groups"
          component={ClientGroups}
          options={{
            tabBarLabel: ({ focused }) => this.renderTabBarLabel('Groups', focused),
            tabBarTestID: 'TrainerTabClientGroups',
          }}
        />
        <Tab.Screen
          name="ChannelList"
          component={ChannelListScreen}
          initialParams={{ isBadged: this.props.route.params?.isBadged }}
          options={{
            tabBarLabel: ({ focused }) => {
              const isBadged = this.props.route.params?.isBadged;
              return this.renderTabBarLabel('Chat', focused, isBadged);
            },
            tabBarTestID: 'TrainerTabChat',
          }}
        />
      </Tab.Navigator>
    );
  }
}

const styles = StyleSheet.create({
  tabBarLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tabIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.medYellow,
    marginLeft: 5,
  },
  parentContainer: {
    width,
    height: 100,
    padding: 10,
    paddingTop: 17,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  profileImageStyle: (imageSize) => ({
    width: imageSize,
    height: imageSize,
    borderRadius: imageSize / 2,
    borderWidth: 0,
    marginRight: 5,
  }),
  infoImageStyle: (imageSize) => ({
    width: imageSize,
    height: imageSize,
    borderRadius: imageSize / 2,
    marginRight: 10,
    tintColor: colors.white,
  }),
  nameView: {
    flex: 8,
    justifyContent: 'center',
  },
  nameTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 17,
    fontWeight: '900',
    color: colors.white,
  },
  mailTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 11,
    fontWeight: '400',
    color: colors.white,
  },
});

// PropTypes
const propTypes = {
  currentUser: PropTypes.object,
  trainerActiveProfile: PropTypes.object,
};

class TrainerHome extends PureComponent {
  onSwitchProfile(navigation) {
    navigation.navigate('ProfileList');
  }

  getLocationNameOrEmail = () => {
    let locationNameOrEmail = '';
    if (this.props.trainerActiveProfile?.ClubId) {
      if (
        FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED
        && this.props.trainerActiveProfile?.Locations?.Name
      ) {
        locationNameOrEmail = this.props.trainerActiveProfile?.Locations?.Name;
      }
    } else {
      locationNameOrEmail = this.props.currentUser.email;
    }
    return locationNameOrEmail;
  };

  showSwitchOption = () => {
    if (this.props.currentUser?.club_connect_user) {
      const clubs = Array.isArray(this.props.currentUser?.club_connect_user)
        ? this.props.currentUser?.club_connect_user
        : Object.values(this.props.currentUser?.club_connect_user);
      if (clubs.length) {
        return true;
      }
    }
    return false;
  };

  renderHeader = (navigation) => {
    let profileUri = tempProfilePic;
    if (this.props.trainerActiveProfile?.ClubId) {
      profileUri = { uri: this.props.trainerActiveProfile?.ClubLogoUrl };
    } else if (this.props.currentUser.avatar_url) {
      profileUri = { uri: this.props.currentUser.avatar_url };
    }
    return (
      <TouchableOpacity
        style={styles.parentContainer}
        activeOpacity={0.6}
        disabled={!this.showSwitchOption()}
        onPress={() => this.onSwitchProfile(navigation)}
      >
        <View style={styles.avatarContainer}>
          <Image style={styles.profileImageStyle(60)} source={profileUri} />
          {this.showSwitchOption() ? (
            <Image style={styles.infoImageStyle(25)} source={arrowDownIcon} />
          ) : null}
        </View>
        <View style={styles.nameView}>
          <Text
            style={styles.nameTextStyle}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {this.props.trainerActiveProfile?.ClubName
              || this.props.currentUser.full_name}
          </Text>
          {this.getLocationNameOrEmail() ? (
            <Text
              style={styles.mailTextStyle}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {this.getLocationNameOrEmail()}
            </Text>
          ) : null}
        </View>
      </TouchableOpacity>
    );
  };

  render() {
    const backgroundColor = this.props.trainerActiveProfile?.ClubId
      ? colors.black
      : colors.duskBlue;
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen
          name="Dashboard"
          component={TrainerHomeTabs}
          options={({ route, navigation }) => ({
            headerShown: true,
            headerLeft: () => this.renderHeader(navigation),
            headerStyle: {
              backgroundColor,
              shadowOffset: { height: 0, width: 0 },
              elevation: 0,
            },
            headerTitle: route.params?.title ?? '',
          })}
          initialParams={{
            trainerActiveProfile: this.props.trainerActiveProfile,
          }}
        />
        <Stack.Screen
          name="Channel"
          component={ChannelScreen}
          options={({ route, navigation }) => ({
            headerShown: true,
            headerLeft: () => (
              <HeaderLeftButton onPress={() => navigation.goBack()} />
            ),
            headerStyle: {
              backgroundColor,
            },
            headerTitleStyle: {
              color: colors.white,
              fontSize: 14,
            },
            headerTitleAlign: 'center',
            headerTitle: route.params?.title ?? '',
          })}
        />
        <Stack.Screen
          name="NewConversation"
          component={NewConversation}
          options={({ navigation }) => ({
            headerShown: true,
            headerLeft: () => (
              <HeaderLeftButton onPress={() => navigation.goBack()} />
            ),
            headerStyle: {
              backgroundColor,
            },
            headerTitleStyle: {
              color: colors.white,
              fontSize: 14,
            },
            headerTitleAlign: 'center',
            headerTitle: 'New Conversation',
          })}
        />
      </Stack.Navigator>
    );
  }
}

// Export
TrainerHome.propTypes = propTypes;
const mapStateToProps = ({ currentUser, trainerActiveProfile }) => ({
  currentUser,
  trainerActiveProfile,
});

export default connect(mapStateToProps)(TrainerHome);
