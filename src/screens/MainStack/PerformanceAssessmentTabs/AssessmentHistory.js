import React, { Component } from 'react';
import { connect } from 'react-redux';
import Moment from 'moment';

// Components
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  VictoryLine,
  VictoryScatter,
  VictoryAxis,
  VictoryChart,
} from 'victory-native';
import {
  BottomBannerActionView,
  ScaledText,
  Swipeable,
} from '../../../components';
import { curvedScale, scaleHeight, scaleWidth } from '../../../util/responsive';
import nasm from '../../../dataManager/apiConfig';

// Styles
import { colors } from '../../../styles';
import HeaderRightButton from '../../../components/HeaderRightButton';

// Images
const completedImage = require('../../../resources/completed.png');
const incompleteImage = require('../../../resources/checkmarkIncomplete.png');
const rightArrow = require('../../../resources/rightArrow.png');

const completedImageHeight = Image.resolveAssetSource(completedImage).height;
const completedImageWidth = Image.resolveAssetSource(completedImage).width;

const periods = {
  WEEK: 'week',
  MONTH: 'month',
  YEAR: 'year',
};

const types = {
  CARDIO: 'cardio',
  ENDURANCE: 'endurance',
  STRENGTH: 'strength',
};

const assessmentNames = {
  three_minute_step: 'Step',
  one_minute_jumping_jacks: 'JJ',
  one_mile_run: 'Mile',
  five_hundred_meter_row: 'Row',
  one_minute_pushups: 'Pushups',
  one_minute_squats: 'Squats',
  squat: 'Squat',
  bench: 'Bench',
  seated_row: 'Row',
};

const scale = Dimensions.get('window').width / 400;

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  weekMonthYearPickerContainer: {
    height: scaleHeight(12),
    width: '100%',
    alignItems: 'center',
  },
  weekMonthYearPicker: {
    height: '35%',
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: scaleHeight(3),
    marginHorizontal: '7%',
    borderColor: colors.subGrey,
    borderWidth: 1,
    borderRadius: curvedScale(4),
  },
  selectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGrey,
  },
  unselectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: null,
  },
  selectedPeriod: {
    color: colors.white,
    alignSelf: 'center',
    fontFamily: 'Avenir-Roman',
    fontSize: 13 * scale,
  },
  unselectedPeriod: {
    fontFamily: 'Avenir-Roman',
    alignSelf: 'center',
    color: colors.subGrey,
    fontSize: 13 * scale,
  },
  graphHeaderSection: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    height: scaleHeight(5),
    borderWidth: 1,
    justifyContent: 'space-between',
    borderColor: colors.silver51,
  },
  graphHeaderText: {
    fontWeight: 'bold',
    color: colors.black,
    alignSelf: 'center',
  },
  graphSectionContainer: {
    flexDirection: 'column',
    width: '100%',
    backgroundColor: '#FCFDFD',
  },
  listContainer: {
    flex: 1,
    width: '100%',
  },
  assessmentCell: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: 'rgb(242,243,243)',
  },
  dotAndLabel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  textSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scaleHeight(4),
    height: scaleHeight(10),
  },
  underText: {
    fontFamily: 'Avenir-Roman',
    color: 'rgb(124, 128, 132)',
    paddingRight: 10 * scale,
  },
  triangle: {
    width: curvedScale(3),
    height: curvedScale(3),
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
  },
  legendSquareStyle: {
    width: curvedScale(9),
    height: curvedScale(9),
  },
  cellSquareStyle: {
    width: curvedScale(5),
    height: curvedScale(5),
  },
  legendDotStyle: {
    width: curvedScale(9),
    height: curvedScale(9),
    borderRadius: scaleWidth(1),
  },
  cellDotStyle: {
    width: curvedScale(5),
    height: curvedScale(5),
    borderRadius: curvedScale(2.5),
  },
  deleteButtonContainer: {
    backgroundColor: 'red',
    flex: 1,
    justifyContent: 'center',
  },
  deleteText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.white,
    paddingLeft: 30 * scale,
  },
};

class AssessmentHistory extends Component {
  constructor(props) {
    super(props);
    this.state = {
      period: periods.WEEK,
      assessmentType: types.CARDIO,
      endDate: new Moment(),
      graphData: [],
      listData: [],
      selectedAssessments: [],
      refreshing: false,
      isDisplayDeleteAlert: false,
    };
  }

  componentDidMount() {
    this.fetchData();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.fetchData();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  getAxesData = () => {
    let axesData = [];
    let startDate;
    switch (this.state.period) {
      case periods.YEAR:
        for (let i = 1; i < 12 + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      case periods.MONTH:
        for (let i = 1; i < this.state.endDate.daysInMonth() + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      default:
        axesData = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
        startDate = this.getStartDateForCurrentRange();
        for (let i = 0; i < axesData.length; i += 1) {
          const num = startDate.date();
          axesData[i] = `${axesData[i]}\n${num}`;
          startDate.add(1, 'days');
        }
        break;
    }
    return axesData;
  };

  getCalendarHeaderText = () => {
    const end = this.getEndDateForCurrentRange();
    if (this.state.period === periods.YEAR) {
      return end.format('YYYY');
    }
    if (this.state.period === periods.MONTH) {
      return end.format('MMMM - YYYY');
    }
    const start = this.getStartDateForCurrentRange();
    if (start.year() === end.year()) {
      if (start.month() === end.month()) {
        return `${start.format('MMMM D')} - ${end.format('D, YYYY')}`;
      }
      return `${start.format('MMMM D')} - ${end.format('MMM D, YYYY')}`;
    }
    return `${start.format('MMMM D, YYYY')} - ${end.format('MMMM D, YYYY')}`;
  };

  getColorAndSymbol = (assessment) => {
    if (assessment === 'Pushups') {
      return { color: colors.azure, symbol: 'circle' };
    }
    if (assessment === 'Squats') {
      return { color: colors.nasmBlue, symbol: 'square' };
    }
    if (assessment === 'Squat') {
      return { color: colors.medYellow, symbol: 'triangleUp' };
    }
    if (assessment === 'Bench') {
      return { color: colors.nasmBlue, symbol: 'square' };
    }
    if (assessment === 'Row') {
      if (this.state.assessmentType === types.STRENGTH) {
        return { color: colors.azure, symbol: 'circle' };
      }
      return { color: colors.peaGreen, symbol: 'diamond' };
    }
    if (assessment === 'Step') {
      return { color: colors.azure, symbol: 'circle' };
    }
    if (assessment === 'JJ') {
      return { color: colors.nasmBlue, symbol: 'square' };
    }
    if (assessment === 'Mile') {
      return { color: colors.medYellow, symbol: 'triangleUp' };
    }
    return { color: 'rgba(0, 0, 0, 0)', symbol: null };
  };

  getDataForGraph = () => {
    const data = {};
    const expectedCategories = this.getExpectedAssessmentCategories();
    expectedCategories.forEach((element) => {
      data[element] = {};
    });

    const axesData = this.getAxesData();
    const date = this.getStartDateForCurrentRange();
    axesData.forEach((element) => {
      for (let i = 0; i < this.state.graphData.length; i += 1) {
        const item = this.state.graphData[i];
        if (this.checkIfDateMatches(date, item)) {
          for (let x = 0; x < item.items.length; x += 1) {
            const assessment = item.items[x];
            const category = assessment.performance_assessment.category.toLowerCase();
            if (this.state.assessmentType === category) {
              const assessmentName = assessment.performance_assessment.name;
              const mappedName = assessmentNames[assessmentName];
              data[mappedName][element] = assessment.avg_value;
            }
          }
        }
      }
      if (this.state.period === periods.YEAR) {
        date.add(1, 'months');
      } else {
        date.add(1, 'days');
      }
    });
    return data;
  };

  getEndDateForCurrentRange = () => {
    const newDate = this.state.endDate.clone();
    if (this.state.period === periods.YEAR) {
      return newDate.month(11).date(31);
    }
    if (this.state.period === periods.MONTH) {
      const daysInMonth = newDate.daysInMonth();
      return newDate.date(daysInMonth);
    }
    const dayOfWeekIndex = newDate.day();
    if (dayOfWeekIndex === 6) {
      // if newDate is a Saturday
      return newDate;
    }
    return newDate.add(6 - dayOfWeekIndex, 'days');
  };

  getExpectedAssessmentCategories = () => {
    if (this.state.assessmentType === types.CARDIO) {
      return ['Step', 'JJ', 'Mile', 'Row'];
    }
    if (this.state.assessmentType === types.ENDURANCE) {
      return ['Pushups', 'Squats'];
    }
    return ['Squat', 'Bench', 'Row'];
  };

  getLegendData = () => {
    const data = [];
    if (this.state.assessmentType === types.CARDIO) {
      data.push({
        text: 'Step',
        color: colors.azure,
        iconStyle: [styles.legendDotStyle, { backgroundColor: colors.azure }],
      });
      data.push({
        text: 'JJ',
        color: colors.nasmBlue,
        iconStyle: [
          styles.legendSquareStyle,
          { backgroundColor: colors.nasmBlue },
        ],
      });
      data.push({
        text: 'Mile',
        color: colors.medYellow,
        iconStyle: [
          styles.triangle,
          {
            borderBottomColor: colors.medYellow,
            borderLeftWidth: curvedScale(5),
            borderRightWidth: curvedScale(5),
            borderBottomWidth: curvedScale(10),
          },
        ],
      });
      data.push({
        text: 'Row',
        color: colors.peaGreen,
        iconStyle: [
          styles.legendSquareStyle,
          {
            transform: [{ rotate: '45deg' }],
            backgroundColor: colors.peaGreen,
          },
        ],
      });
    } else if (this.state.assessmentType === types.ENDURANCE) {
      data.push({
        text: 'Pushups',
        color: colors.azure,
        iconStyle: [styles.legendDotStyle, { backgroundColor: colors.azure }],
      });
      data.push({
        text: 'Squats',
        color: colors.nasmBlue,
        iconStyle: [
          styles.legendSquareStyle,
          { backgroundColor: colors.nasmBlue },
        ],
      });
    } else {
      data.push({
        text: 'Squat',
        color: colors.medYellow,
        iconStyle: [
          styles.triangle,
          {
            borderBottomColor: colors.medYellow,
            borderLeftWidth: curvedScale(5),
            borderRightWidth: curvedScale(5),
            borderBottomWidth: curvedScale(10),
          },
        ],
      });
      data.push({
        text: 'Bench',
        color: colors.nasmBlue,
        iconStyle: [
          styles.legendSquareStyle,
          { backgroundColor: colors.nasmBlue },
        ],
      });
      data.push({
        text: 'Row',
        color: colors.azure,
        iconStyle: [styles.legendDotStyle, { backgroundColor: colors.azure }],
      });
    }
    return data;
  };

  getStartDateForCurrentRange = () => {
    const newDate = this.state.endDate.clone();
    if (this.state.period === periods.YEAR) {
      return newDate.month(0).date(1);
    }
    if (this.state.period === periods.MONTH) {
      return newDate.date(1);
    }
    const endDate = this.getEndDateForCurrentRange();
    return endDate.subtract(6, 'days');
  };

  assessmentClicked = (assessment) => {
    this.props.navigation.navigate('AssessmentDetails', {
      date: assessment.date,
      assessments: assessment.items.map((item) => ({ ...item })),
    });
  };

  assessmentSelected = (assessment) => {
    const { selectedAssessments } = this.state;
    if (this.isAssessmentSelected(assessment)) {
      this.removeAssessmentFromSelected(assessment);
    } else {
      if (selectedAssessments.length > 1) return;
      selectedAssessments.push(assessment);
      this.setState({ selectedAssessments }, () => {
        setTimeout(() => {
          this.flatList.scrollToItem({ item: assessment });
        }, 100);
      });
    }
    this.props.route.params.updateHeaderRight(
      selectedAssessments.length > 0
        ? this.renderHeaderRight
        : this.props.route.params.originalHeaderRight,
    );
  };

  checkIfDateMatches = (date, assessment) => {
    const assessmentDateString = assessment.date;
    if (assessmentDateString) {
      if (this.state.period === periods.YEAR) {
        const assessmentDate2 = new Moment(assessmentDateString, 'YYYY-MM-DD');
        return date.format('YYYY-MM') === assessmentDate2.format('YYYY-MM');
      }
      return date.format('YYYY-MM-DD') === assessmentDateString;
    }
    return false;
  };

  clearSelectedAssessments = () => {
    this.setState({ selectedAssessments: [] });
    this.props.route.params.updateHeaderRight(
      this.props.route.params.originalHeaderRight,
    );
  };

  deleteAssessment = (assessment) => {
    if (!this.state.isDisplayDeleteAlert) {
      this.setState({ isDisplayDeleteAlert: true });
      Alert.alert(
        'Delete Assessment',
        'Are you sure you would like to delete this assessment?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              this.setState({ isDisplayDeleteAlert: false });
            },
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: async () => {
              try {
                this.setState({ isDisplayDeleteAlert: false });
                await nasm.api.deleteAssessmentsForDate(
                  this.props.selectedClient?.id,
                  assessment.date,
                );
              } catch (error) {
                Alert.alert(
                  'Error',
                  error.message || 'could not delete assessment',
                );
              } finally {
                this.refreshGraphAndList();
              }
            },
          },
        ],
        { cancelable: true },
      );
    }
  };

  fetchData = async () => {
    let graphData = [];
    let listData = [];
    try {
      const userId = this.props.selectedClient?.id;
      const start = this.getStartDateForCurrentRange().format('YYYY-MM-DD');
      const end = this.getEndDateForCurrentRange().format('YYYY-MM-DD');
      const period = this.state.period === periods.YEAR ? 'monthly' : 'daily';
      const response = await nasm.api.getAssessmentResults(
        userId,
        start,
        end,
        period,
      );
      graphData = response.graphData;
      listData = response.listData;
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Unable to get graph data. Please try again later.',
      );
    } finally {
      this.setState({ graphData, listData });
    }
  };

  isAssessmentSelected = (assessment) => {
    const { selectedAssessments } = this.state;
    for (let i = 0; i < selectedAssessments.length; i += 1) {
      const selectedAssessment = selectedAssessments[i];
      if (selectedAssessment.date === assessment.date) {
        return true;
      }
    }
    return false;
  };

  leftArrowClicked = () => {
    const newDate = this.state.endDate.clone();
    if (this.state.period === periods.YEAR) {
      newDate.subtract(1, 'years');
    } else if (this.state.period === periods.MONTH) {
      newDate.subtract(1, 'months');
    } else {
      newDate.subtract(7, 'days').format('MMMM DD, YYYY');
    }
    this.setState({ graphData: [], listData: [], endDate: newDate }, () => {
      this.fetchData();
    });
  };

  normalizeData = (data) => {
    const normalizedData = [];
    let max = -1;
    for (const key in data) {
      if (max === -1) {
        max = data[key].y;
      } else if (data[key].y > max) {
        max = data[key].y;
      }
    }
    for (const key2 in data) {
      if (max !== 0) {
        normalizedData.push({ x: data[key2].x, y: data[key2].y / max + 0.1 });
      }
    }
    return normalizedData;
  };

  refreshGraphAndList = () => {
    let { graphData, listData } = this.state;
    this.setState({ refreshing: true }, async () => {
      try {
        const userId = this.props.selectedClient?.id;
        const start = this.getStartDateForCurrentRange().format('YYYY-MM-DD');
        const end = this.getEndDateForCurrentRange().format('YYYY-MM-DD');
        const period = this.state.period === periods.YEAR ? 'monthly' : 'daily';
        const response = await nasm.api.getAssessmentResults(
          userId,
          start,
          end,
          period,
        );
        graphData = response.graphData;
        listData = response.listData;
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || 'Unable to refresh assessment history. Please try again later.',
        );
      } finally {
        this.setState({ refreshing: false, graphData, listData });
      }
    });
  };

  removeAssessmentFromSelected = (assessment) => {
    const { selectedAssessments } = this.state;
    let index = -1;
    for (let i = 0; i < selectedAssessments.length; i += 1) {
      const selectedAssessment = selectedAssessments[i];
      if (selectedAssessment.date === assessment.date) {
        index = i;
        break;
      }
    }
    if (index !== -1) {
      selectedAssessments.splice(index, 1);
      this.setState({ selectedAssessments });
    }
  };

  rightArrowClicked = () => {
    const newDate = this.state.endDate.clone();
    if (this.state.period === periods.YEAR) {
      newDate.add(1, 'years');
    } else if (this.state.period === periods.MONTH) {
      newDate.add(1, 'months');
    } else {
      newDate.add(7, 'days').format('MMMM DD, YYYY');
    }
    this.setState({ graphData: [], listData: [], endDate: newDate }, () => {
      this.fetchData();
    });
  };

  secondsToMinutes = (seconds) => {
    if (!seconds) return 'n/a';
    const mins = Math.floor(seconds / 60);
    const secs = seconds - mins * 60;
    if (secs < 10) {
      return `${mins}:0${secs}`;
    }
    return `${mins}:${secs}`;
  };

  renderAssessmentCell = ({ item, index }) => {
    const type = this.state.assessmentType;
    let cell = null;
    switch (type) {
      case types.CARDIO:
        cell = this.renderCardioCell(item);
        break;
      case types.ENDURANCE:
        cell = this.renderEnduranceCell(item);
        break;
      case types.STRENGTH:
        cell = this.renderStrengthCell(item);
        break;
      default:
        cell = this.renderCardioCell(item);
        break;
    }
    return (
      <Swipeable
        key={index}
        onRightActionRelease={() => this.deleteAssessment(item)}
        rightContent={(
          <View style={styles.deleteButtonContainer}>
            <ScaledText style={styles.deleteText}>Delete</ScaledText>
          </View>
        )}
      >
        {cell}
      </Swipeable>
    );
  };

  renderAssessmentTypePicker = () => (
    <View style={styles.weekMonthYearPickerContainer}>
      <View style={styles.weekMonthYearPicker}>
        <TouchableOpacity
          style={
            this.state.assessmentType === types.CARDIO
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState({ assessmentType: types.CARDIO });
          }}
        >
          <Text
            style={
              this.state.assessmentType === types.CARDIO
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Cardio
          </Text>
        </TouchableOpacity>

        <View
          style={{
            width: 1,
            height: '100%',
            backgroundColor: colors.subGrey,
          }}
        />

        <TouchableOpacity
          style={
            this.state.assessmentType === types.ENDURANCE
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState({ assessmentType: types.ENDURANCE });
          }}
        >
          <Text
            style={
              this.state.assessmentType === types.ENDURANCE
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Endurance
          </Text>
        </TouchableOpacity>

        <View
          style={{
            width: 1,
            height: '100%',
            backgroundColor: colors.subGrey,
          }}
        />

        <TouchableOpacity
          style={
            this.state.assessmentType === types.STRENGTH
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState({ assessmentType: types.STRENGTH });
          }}
        >
          <Text
            style={
              this.state.assessmentType === types.STRENGTH
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Strength
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  renderCardioCell = (item) => {
    const textStyle = [
      styles.underText,
      { fontSize: 12, paddingLeft: 3 * scale },
    ];
    const selected = this.isAssessmentSelected(item)
      ? completedImage
      : incompleteImage;
    const values = {
      one_minute_jumping_jacks: 'n/a',
      three_minute_step: 'n/a',
    };
    item.items.forEach((assessment) => {
      if (
        this.state.assessmentType.toUpperCase()
        === assessment.performance_assessment.category.toUpperCase()
      ) {
        let { value } = assessment;
        if (assessment.performance_assessment.name === 'three_minute_step') {
          value = `${value} bpm`;
        }
        values[assessment.performance_assessment.name] = assessment.value
          ? value
          : 'n/a';
      }
    });
    const mileVal = this.secondsToMinutes(values.one_mile_run);
    const rowVal = this.secondsToMinutes(values.five_hundred_meter_row);
    return (
      <TouchableOpacity
        style={styles.assessmentCell}
        onPress={() => this.assessmentClicked(item)}
      >
        <View style={styles.textSection}>
          {item.items[0].date && (
            <ScaledText style={styles.date}>
              {new Moment(item.items[0].date).format('M/D/YY')}
            </ScaledText>
          )}
          <View style={styles.dotAndLabel}>
            <View
              style={[styles.cellDotStyle, { backgroundColor: colors.azure }]}
            />
            <ScaledText style={textStyle}>
              {values.three_minute_step}
            </ScaledText>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.cellSquareStyle,
                { backgroundColor: colors.nasmBlue },
              ]}
            />
            <ScaledText style={textStyle}>
              {values.one_minute_jumping_jacks}
            </ScaledText>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.triangle,
                {
                  borderBottomColor: colors.medYellow,
                  borderLeftWidth: curvedScale(4),
                  borderRightWidth: curvedScale(4),
                  borderBottomWidth: curvedScale(8),
                },
              ]}
            />
            <ScaledText style={textStyle}>{mileVal}</ScaledText>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.cellSquareStyle,
                {
                  transform: [{ rotate: '45deg' }],
                  backgroundColor: colors.peaGreen,
                },
              ]}
            />
            <ScaledText style={textStyle}>{rowVal}</ScaledText>
          </View>
          <TouchableOpacity onPress={() => this.assessmentSelected(item)}>
            <Image
              key={`${item.status}`}
              source={selected}
              style={{
                height: curvedScale(completedImageHeight),
                width: curvedScale(completedImageWidth),
              }}
            />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  renderEnduranceCell = (item) => {
    const textStyle = [
      styles.underText,
      { fontSize: 12 * scale, paddingLeft: 3 * scale },
    ];
    const selected = this.isAssessmentSelected(item)
      ? completedImage
      : incompleteImage;
    const values = {
      one_minute_squats: 'n/a',
      one_minute_pushups: 'n/a',
    };
    item.items.forEach((assessment) => {
      if (
        this.state.assessmentType.toUpperCase()
        === assessment.performance_assessment.category.toUpperCase()
      ) {
        values[assessment.performance_assessment.name] = assessment.value
          ? assessment.value
          : 'n/a';
      }
    });
    return (
      <TouchableOpacity
        style={styles.assessmentCell}
        onPress={() => this.assessmentClicked(item)}
      >
        <View style={styles.textSection}>
          {item.items[0].date && (
            <ScaledText style={styles.date}>
              {new Moment(item.items[0].date).format('M/D/YY')}
            </ScaledText>
          )}
          <View style={styles.dotAndLabel}>
            <View
              style={[styles.cellDotStyle, { backgroundColor: colors.azure }]}
            />
            <ScaledText style={textStyle}>
              {values.one_minute_pushups}
            </ScaledText>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.cellSquareStyle,
                { backgroundColor: colors.nasmBlue },
              ]}
            />
            <ScaledText style={textStyle}>
              {values.one_minute_squats}
            </ScaledText>
          </View>
          <TouchableOpacity onPress={() => this.assessmentSelected(item)}>
            <Image
              key={`${item.status}`}
              source={selected}
              style={{
                height: curvedScale(completedImageHeight),
                width: curvedScale(completedImageWidth),
              }}
            />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  renderGraph = () => {
    const data = this.getDataForGraph();
    const axesData = this.getAxesData();
    const emptyPoints = [];
    axesData.forEach((element) => {
      emptyPoints.push({ x: element, y: 0 });
    });

    const keys = Object.keys(data);
    const maps = [];
    for (let i = 0; i < keys.length; i += 1) {
      const key = keys[i];
      const dictionaryForKey = data[key];
      const arrayForKey = [];
      for (const item in dictionaryForKey) {
        if (Object.prototype.hasOwnProperty.call(dictionaryForKey, item)) {
          arrayForKey.push({ x: item, y: dictionaryForKey[item] });
        }
      }
      maps.push({ [key]: arrayForKey });
    }
    return (
      <VictoryChart
        height={curvedScale(200)}
        domainPadding={12}
        padding={{
          top: 30,
          bottom: 35,
          left: 30,
          right: 30,
        }}
      >
        <VictoryAxis
          key={0}
          style={{
            axis: { stroke: '' },
            grid: { stroke: colors.silver },
            tickLabels: {
              fontSize: curvedScale(9),
              padding: 0,
              fill: colors.subGrey,
              marginTop: 20,
            },
          }}
          orientation="top"
          tickValues={axesData}
        />
        {this.renderGraphPointsWithArray(emptyPoints)}

        {this.renderGraphLine(maps[0])}
        {this.renderGraphPointsWithMap(maps[0])}

        {this.renderGraphLine(maps[1])}
        {this.renderGraphPointsWithMap(maps[1])}

        {this.renderGraphLine(maps[2])}
        {this.renderGraphPointsWithMap(maps[2])}

        {this.renderGraphLine(maps[3])}
        {this.renderGraphPointsWithMap(maps[3])}
      </VictoryChart>
    );
  };

  renderGraphHeader = () => {
    const headerText = this.getCalendarHeaderText();
    return (
      <View style={styles.graphHeaderSection}>
        <TouchableOpacity
          style={{ alignSelf: 'center' }}
          onPress={() => this.leftArrowClicked()}
        >
          <Image
            style={{
              marginStart: 23 * scale,
              tintColor: colors.cloudyBlue,
              transform: [{ rotate: '180deg' }],
            }}
            source={rightArrow}
          />
        </TouchableOpacity>
        <ScaledText style={styles.graphHeaderText}>{headerText}</ScaledText>
        <TouchableOpacity
          style={{ alignSelf: 'center' }}
          onPress={() => this.rightArrowClicked()}
        >
          <Image
            style={{ marginEnd: 23 * scale, tintColor: colors.cloudyBlue }}
            source={rightArrow}
          />
        </TouchableOpacity>
      </View>
    );
  };

  renderGraphLegend = () => {
    const legendData = this.getLegendData();
    return (
      <View
        style={{
          height: scaleHeight(6),
          display: 'flex',
          marginStart: 16 * scale,
          marginEnd: 16 * scale,
          flexDirection: 'row',
          justifyContent: 'space-evenly',
        }}
      >
        <View style={styles.textSection}>
          {legendData.map((data) => (
            <View style={styles.dotAndLabel} key={data.id}>
              <View style={data.iconStyle} />
              <Text
                style={[
                  styles.underText,
                  {
                    fontSize: 16 * scale,
                    paddingLeft: 9 * scale,
                    color: data.color,
                  },
                ]}
              >
                {data.text}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  renderGraphLine = (map) => {
    if (!map) return undefined;
    const key = Object.keys(map)[0];
    const data = map[key];
    if (data && data.length > 1) {
      // need at least 2 points for a line
      const { color } = this.getColorAndSymbol(key);
      const normalizedData = this.normalizeData(data);
      return (
        <VictoryLine
          style={{
            data: { stroke: color, strokeWidth: 2 },
          }}
          data={normalizedData}
          interpolation="catmullRom"
        />
      );
    }
    return undefined;
  };

  renderGraphPointsWithArray = (array) => {
    if (array) {
      return (
        <VictoryScatter
          style={{ data: { fill: 'rgba(0,0,0,0)' } }}
          size={curvedScale(4)}
          symbol={null}
          data={array}
        />
      );
    }
    return undefined;
  };

  renderGraphPointsWithMap = (map) => {
    if (!map) return undefined;
    const key = Object.keys(map)[0];
    const data = map[key];
    if (data) {
      const x = this.getColorAndSymbol(key);
      const normalizedData = data.length === 1 ? data : this.normalizeData(data);
      return (
        <VictoryScatter
          style={{ data: { fill: x.color } }}
          size={curvedScale(4)}
          symbol={x.symbol}
          data={normalizedData}
        />
      );
    }
    return undefined;
  };

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={this.clearSelectedAssessments}
      title="Clear All"
      titleStyle={styles.headerButtonText}
    />
  );

  renderPeriodPicker = () => (
    <View style={styles.weekMonthYearPickerContainer}>
      <View style={styles.weekMonthYearPicker}>
        <TouchableOpacity
          style={
            this.state.period === periods.WEEK
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState(
              {
                graphData: [],
                listData: [],
                endDate: new Moment(),
                period: periods.WEEK,
              },
              () => {
                this.fetchData();
              },
            );
          }}
        >
          <Text
            style={
              this.state.period === periods.WEEK
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Week
          </Text>
        </TouchableOpacity>

        <View
          style={{
            width: 1,
            height: '100%',
            backgroundColor: colors.subGrey,
          }}
        />

        <TouchableOpacity
          style={
            this.state.period === periods.MONTH
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState(
              {
                graphData: [],
                listData: [],
                endDate: new Moment(),
                period: periods.MONTH,
              },
              () => {
                this.fetchData();
              },
            );
          }}
        >
          <Text
            style={
              this.state.period === periods.MONTH
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Month
          </Text>
        </TouchableOpacity>

        <View
          style={{
            width: 1,
            height: '100%',
            backgroundColor: colors.subGrey,
          }}
        />

        <TouchableOpacity
          style={
            this.state.period === periods.YEAR
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState(
              {
                graphData: [],
                listData: [],
                endDate: new Moment(),
                period: periods.YEAR,
              },
              () => {
                this.fetchData();
              },
            );
          }}
        >
          <Text
            style={
              this.state.period === periods.YEAR
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Year
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  renderStrengthCell = (item) => {
    const textStyle = [
      styles.underText,
      { fontSize: 12 * scale, paddingLeft: 3 * scale },
    ];
    const selected = this.isAssessmentSelected(item)
      ? completedImage
      : incompleteImage;
    const values = {
      seated_row: 'n/a',
      bench: 'n/a',
      squat: 'n/a',
    };
    item.items.forEach((assessment) => {
      if (
        this.state.assessmentType.toUpperCase()
        === assessment.performance_assessment.category.toUpperCase()
      ) {
        values[assessment.performance_assessment.name] = assessment.value
          ? assessment.value
          : 'n/a';
      }
    });
    const weightUnit = this.props.currentUser.unit_weight === 'lb'
      ? 'lbs'
      : this.props.currentUser.unit_weight;
    const seatedRow = values.seated_row === 'n/a'
      ? values.seated_row
      : `${values.seated_row} ${weightUnit}`;
    const bench = values.bench === 'n/a' ? values.bench : `${values.bench} ${weightUnit}`;
    const squat = values.squat === 'n/a' ? values.squat : `${values.squat} ${weightUnit}`;
    return (
      <TouchableOpacity
        style={styles.assessmentCell}
        onPress={() => this.assessmentClicked(item)}
      >
        <View style={styles.textSection}>
          {item.items[0].date && (
            <ScaledText style={styles.date}>
              {new Moment(item.items[0].date).format('M/D/YY')}
            </ScaledText>
          )}
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.triangle,
                {
                  borderBottomColor: colors.medYellow,
                  borderLeftWidth: curvedScale(4),
                  borderRightWidth: curvedScale(4),
                  borderBottomWidth: curvedScale(8),
                },
              ]}
            />
            <ScaledText style={textStyle}>{squat}</ScaledText>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.cellSquareStyle,
                { backgroundColor: colors.nasmBlue },
              ]}
            />
            <ScaledText style={textStyle}>{bench}</ScaledText>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[styles.cellDotStyle, { backgroundColor: colors.azure }]}
            />
            <ScaledText style={textStyle}>{seatedRow}</ScaledText>
          </View>
          <TouchableOpacity onPress={() => this.assessmentSelected(item)}>
            <Image
              key={`${item.status}`}
              source={selected}
              style={{
                height: curvedScale(completedImageHeight),
                width: curvedScale(completedImageWidth),
              }}
            />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  render() {
    const selected = this.state.selectedAssessments.sort(
      (a, b) => -Moment(a.date).diff(b.date),
    );
    const bottomTitle = selected.length === 2
      ? '2 Assessments Selected'
      : `${selected.length}/2 Assessments Selected`;
    return (
      <View style={{ flex: 1 }}>
        <View style={styles.listContainer}>
          <FlatList
            ref={(ref) => {
              this.flatList = ref;
            }}
            contentContainerStyle={{ flexGrow: 1 }}
            ListHeaderComponent={(
              <View>
                {this.renderPeriodPicker()}
                {this.renderGraphHeader()}
                <View style={styles.graphSectionContainer}>
                  {this.renderGraph()}
                  {this.renderGraphLegend()}
                  {this.renderAssessmentTypePicker()}
                </View>
              </View>
            )}
            data={this.state.listData}
            renderItem={this.renderAssessmentCell}
            keyExtractor={(item) => item.date}
            refreshControl={(
              <RefreshControl
                refreshing={this.state.refreshing}
                onRefresh={this.refreshGraphAndList}
              />
            )}
          />
          {selected.length > 0 && (
            <BottomBannerActionView
              title={bottomTitle}
              buttonTitle="Compare"
              buttonDisabled={selected.length !== 2}
              onPress={() => {
                this.props.navigation.navigate('AssessmentComparison', {
                  assessments: selected,
                });
                this.clearSelectedAssessments();
              }}
            />
          )}
        </View>
      </View>
    );
  }
}

const mapStateToProps = ({ selectedClient, currentUser }) => ({
  selectedClient,
  currentUser,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(AssessmentHistory);
