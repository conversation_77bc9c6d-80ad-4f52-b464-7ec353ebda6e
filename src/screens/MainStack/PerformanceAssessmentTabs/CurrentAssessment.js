import React, { Component } from 'react';
import moment from 'moment';
import { connect } from 'react-redux';

// Components
import {
  <PERSON>ert, View, StatusBar, SafeAreaView,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import {
  PerformanceAssessmentView,
  FloatingButton,
  ScaledText,
} from '../../../components';
import nasm from '../../../dataManager/apiConfig';

// Styles
import { colors } from '../../../styles';

// Analytics

class CurrentAssessment extends Component {
  constructor(props) {
    super(props);
    this.state = {
      assessments: [],
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', {
        screen_name: 'current_performance_assessments',
      });
      this.getAssessments();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  getAssessments = async () => {
    try {
      const assessments = await nasm.api.getCurrentAssessmentResults(
        this.props.selectedClient?.id,
      );
      const sortedAssessments = assessments.sort((a, b) => moment(b.updated_at).diff(a.updated_at));
      this.setState({ assessments: sortedAssessments });
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  render() {
    const { assessments } = this.state;
    let updated_at = null;
    if (assessments && assessments.length > 0) {
      updated_at = moment(assessments[0].date).format('M/D/YY');
    }
    return (
      <View style={styles.container}>
        <View style={styles.assessmentView}>
          <PerformanceAssessmentView assessments={this.state.assessments} />
        </View>
        <View style={styles.bottomView}>
          {updated_at !== null && (
            <ScaledText style={styles.dateText}>
              Updated on
              {' '}
              {updated_at}
            </ScaledText>
          )}
        </View>
        <FloatingButton
          onPress={() => this.props.navigation.navigate('AddAssessment')}
        />
        <SafeAreaView />
      </View>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  assessmentView: {
    height: '90%',
  },
  bottomView: {
    height: '10%',
    paddingHorizontal: '5%',
    justifyContent: 'center',
  },
  dateText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.subGrey,
  },
};

const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(CurrentAssessment);
