import React, { Component } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';

// Components
import {
  View, Image, Dimensions, StyleSheet,
} from 'react-native';
import { connect } from 'react-redux';
import Current from './CurrentAssessment';
import History from './AssessmentHistory';

// Styles
import { colors, materialTabBarOptions } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

// Images
const infoIcon = require('../../../resources/info.png');

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

class PerformanceAssessmentTabView extends Component {
  componentDidMount() {
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
      renderHeaderRight: this.renderHeaderRight,
    });
  }

  static router = TabNav.router;

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Performance Assessments',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  updateHeaderRight = (headerRight) => {
    this.props.navigation.setParams({
      renderHeaderRight: headerRight,
    });
  };

  updateHeaderLeft = (headerLeft) => {
    this.props.navigation.setParams({
      renderHeaderLeft: headerLeft,
    });
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.props.navigation.navigate('WebView', {
        title: 'Help',
        uri: 'https://nasm.org/edge/info/performance-assessments',
      })}
      buttonImage={<Image source={infoIcon} />}
    />
  );

  render() {
    const backgroundColor = this.props.trainerActiveProfile?.ClubId
      ? colors.black
      : colors.duskBlue;
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarStyle,
            backgroundColor,
          },
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            // Based of https://github.com/satya164/react-native-tab-view/issues/944#issuecomment-599224375
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
        style={materialTabBarOptions.tabBarOptions.tabBarStyle}
      >
        <TabNav.Screen name="Current" component={Current} />
        <TabNav.Screen
          name="History"
          component={History}
          initialParams={{
            updateHeaderRight: this.updateHeaderRight,
            originalHeaderRight: this.renderHeaderRight,
          }}
        />
      </TabNav.Navigator>
    );
  }
}

const styles = StyleSheet.create({
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
});

const mapStateToProps = ({ trainerActiveProfile }) => ({
  trainerActiveProfile,
});
const mapDispatchToProps = {};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(PerformanceAssessmentTabView);
