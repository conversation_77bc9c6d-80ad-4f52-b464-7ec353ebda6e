import React, { Component } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';

// Components
import {
  View,
  Image,
  Dimensions,
  Platform,
  NativeModules,
  StyleSheet,
} from 'react-native';
import { connect } from 'react-redux';

import Current from './CurrentMeasurement';
import History from './MeasurementHistory';

// Styles
import { colors, materialTabBarOptions } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

const { AppKeyboardHandlerMethods } = NativeModules;

// Images
const infoIcon = require('../../../resources/info.png');

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

class MeasurementTabView extends Component {
  componentDidMount() {
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
      renderHeaderRight: this.renderHeaderRight,
    });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustResize();
      }
    });
    this.unsubscribeBlur = this.props.navigation.addListener('blur', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustPan();
      }
    });
  }

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
    if (this.unsubscribeBlur) {
      this.unsubscribeBlur();
    }
  }

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Measurements',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  static router = TabNav.router;

  updateHeaderRight = (headerRight) => {
    this.props.navigation.setParams({
      renderHeaderRight: headerRight,
    });
  };

  updateHeaderLeft = (headerLeft) => {
    this.props.navigation.setParams({
      renderHeaderLeft: headerLeft,
    });
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.props.navigation.navigate('WebView', {
        title: 'Help',
        uri: 'https://nasm.org/edge/info/measurements',
      })}
      buttonImage={<Image source={infoIcon} />}
    />
  );

  render() {
    const backgroundColor = this.props.trainerActiveProfile?.ClubId
      ? colors.black
      : colors.duskBlue;
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarStyle,
            backgroundColor,
          },
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            // Based of https://github.com/satya164/react-native-tab-view/issues/944#issuecomment-599224375
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
      >
        <TabNav.Screen
          name="Current"
          component={Current}
          initialParams={{
            updateHeaderRight: this.updateHeaderRight,
            updateHeaderLeft: this.updateHeaderLeft,
          }}
        />
        <TabNav.Screen
          name="History"
          component={History}
          initialParams={{
            updateHeaderRight: this.updateHeaderRight,
            updateHeaderLeft: this.updateHeaderLeft,
            originalHeaderRight: this.renderHeaderRight,
          }}
        />
      </TabNav.Navigator>
    );
  }
}

const styles = StyleSheet.create({
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
});

const mapStateToProps = ({ trainerActiveProfile }) => ({
  trainerActiveProfile,
});
const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(MeasurementTabView);
