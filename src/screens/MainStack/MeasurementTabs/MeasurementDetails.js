import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

// Components
import {
  Text,
  StatusBar,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
  View,
  Image,
  Keyboard,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import { KeyboardHandler, MeasurementView } from '../../../components';
import nasm from '../../../dataManager/apiConfig';

// Styles
import { colors } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

// Analytics

// Images
const infoIcon = require('../../../resources/info.png');

class MeasurementDetails extends Component {
  static navigationOptions = ({ navigation, route }) => {
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    return {
      title: 'Measurements',
      headerRight: () => renderHeaderRight(navigation, route),
      headerLeft: () => renderHeaderLeft(navigation, route),
    };
  };

  constructor(props) {
    super(props);
    const data = props.route.params?.data;
    let frontPicture = null;
    let backPicture = null;
    let sidePicture = null;
    if (data.photo_urls) {
      if (data.photo_urls.front) {
        frontPicture = { path: data.photo_urls.front };
      }
      if (data.photo_urls.back) {
        backPicture = { path: data.photo_urls.back };
      }
      if (data.photo_urls.side) {
        sidePicture = { path: data.photo_urls.side };
      }
    }
    this.state = {
      data,
      frontPicture,
      backPicture,
      sidePicture,
    };
  }

  componentDidMount() {
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
      renderHeaderRight: this.renderHeaderRight,
    });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', {
        screen_name: 'measurements_details',
      });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onBackPictureChanged = (backPicture) => {
    this.setState({ backPicture }, this.setHeaderRight);
  };

  onBodyFatPressed = () => {
    this.props.navigation.navigate('BodyFatModal', {
      onBodyFatUpdated: (bodyFat) => this.onDataEdited('body_fat_percentage', bodyFat),
      bodyFat: this.state.data.body_fat_percentage,
      calculationData: {
        ...this.state.data,
        height: this.props.selectedClient?.client_user.height,
      },
      isFemale:
        this.props.selectedClient?.gender_type
        && this.props.selectedClient?.gender_type === 2,
    });
  };

  onDataEdited = (key, value) => {
    const { data } = this.state;
    data[key] = Number.isNaN(value) ? 0 : value;
    this.setState({ data }, this.setHeaderRight);
  };

  onFrontPictureChanged = (frontPicture) => {
    this.setState({ frontPicture }, this.setHeaderRight);
  };

  onSidePictureChanged = (sidePicture) => {
    this.setState({ sidePicture }, this.setHeaderRight);
  };

  setHeaderRight = () => {
    this.props.navigation.setParams({ changes: true });
  };

  delete = () => {
    Alert.alert(
      'Delete Measurements',
      'Are you sure you would like to delete these measurements?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await nasm.api.deleteMeasurement(
                this.props.selectedClient?.id,
                this.state.data.id,
              );
              this.props.navigation.goBack();
            } catch (error) {
              Alert.alert(
                'Error',
                error.message || 'could not delete measurements',
              );
            }
          },
        },
      ],
      { cancelable: true },
    );
  };

  save = async () => {
    Keyboard.dismiss();
    this.props.navigation.setParams({ saving: true });
    const measurement = this.state.data;
    try {
      const saveResult = await nasm.api.updateMeasurementById(
        this.props.selectedClient?.id,
        measurement,
      );
      if (
        this.state.frontPicture
        || this.state.backPicture
        || this.state.sidePicture
      ) {
        return this.uploadPhotos(saveResult.id);
      }
      Alert.alert('Measurements Saved');
      this.props.navigation.setParams({ changes: false, saving: false });
      return true;
    } catch (error) {
      Alert.alert('Error', error.message);
      this.props.navigation.setParams({ saving: false });
      return false;
    }
  };

  showSaveAlert = (next) => {
    Alert.alert(
      'Unsaved Changes',
      'All unsaved changes will be lost',
      [
        { text: "Don't Save", style: 'destructive', onPress: next },
        {
          text: 'Save',
          onPress: async () => {
            const success = await this.save();
            if (success && next) {
              next();
            }
          },
        },
      ],
      { cancelable: false },
    );
  };

  uploadPhotos = async (measurementId) => {
    if (!measurementId) {
      return false;
    }
    try {
      await nasm.api.submitMeasurementImages(
        this.props.selectedClient?.id,
        measurementId,
        this.state.frontPicture,
        this.state.backPicture,
        this.state.sidePicture,
      );
      Alert.alert('Measurements Saved');
      this.props.navigation.setParams({ changes: false, saving: false });
      return true;
    } catch (error) {
      Alert.alert('Error', error.message);
      this.props.navigation.setParams({ saving: false });
      return false;
    }
  };

  renderHeaderLeft = (navigation, route) => {
    if (route.params?.saving) {
      return (
        <HeaderLeftButton
          style={{ opacity: 0.5 }}
          onPress={() => null}
          titleStyle={styles.headerButtonText}
        />
      );
    }
    return (
      <HeaderLeftButton
        onPress={() => {
          if (route.params?.changes) {
            this.showSaveAlert(() => navigation.goBack());
          } else {
            navigation.goBack();
          }
        }}
        titleStyle={styles.headerButtonText}
      />
    );
  };

  renderHeaderRight = (navigation, route) => {
    if (route.params?.saving) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    if (route.params?.changes) {
      return (
        <HeaderRightButton
          onPress={() => this.save()}
          title="Save"
          titleStyle={styles.headerButtonText}
        />
      );
    }
    return (
      <TouchableOpacity
        style={{ paddingHorizontal: 16 }}
        onPress={() => navigation.navigate('WebView', {
          title: 'Help',
          uri: 'https://nasm.org/edge/info/measurements',
        })}
      >
        <Image source={infoIcon} />
      </TouchableOpacity>
    );
  };

  render() {
    return (
      <KeyboardHandler scrollEnabled style={styles.container}>
        <Text style={styles.date}>
          {moment(this.state.data.measurements_date).format('MMMM D, YYYY')}
        </Text>
        <MeasurementView
          data={this.state.data}
          onDataEdited={this.onDataEdited}
          frontPicture={this.state.frontPicture}
          backPicture={this.state.backPicture}
          sidePicture={this.state.sidePicture}
          onFrontPictureChanged={this.onFrontPictureChanged}
          onBackPictureChanged={this.onBackPictureChanged}
          onSidePictureChanged={this.onSidePictureChanged}
          onBodyFatPressed={this.onBodyFatPressed}
          navigation={this.props.navigation}
          isEditable={false}
        />
        <TouchableOpacity style={styles.deleteBtn} onPress={this.delete}>
          <Text style={styles.delete}>Delete</Text>
        </TouchableOpacity>
      </KeyboardHandler>
    );
  }
}

const styles = {
  container: {
    flex: 1,
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  date: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18,
    color: colors.subGrey,
    marginHorizontal: 33,
    marginTop: 27,
  },
  deleteBtn: {
    width: 125,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.subGrey,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginBottom: 28,
  },
  delete: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    color: colors.subGrey,
  },
};

const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(MeasurementDetails);
