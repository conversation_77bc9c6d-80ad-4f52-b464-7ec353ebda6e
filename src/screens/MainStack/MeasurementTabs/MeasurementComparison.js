import React, { Component } from 'react';
import moment from 'moment';

// Components
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Dimensions,
  PixelRatio,
  Platform,
} from 'react-native';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Styles
import { connect } from 'react-redux';
import { colors } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

// Constants
const { width } = Dimensions.get('window');
const pixelWidth = PixelRatio.getPixelSizeForLayoutSize(width);

// Images
const cameraIcon = require('../../../resources/cameraIcon.png');
const upArrow = require('../../../resources/arrowUpStatIcon.png');
const downArrow = require('../../../resources/arrowDownStatIcon.png');

const pictureType = {
  FRONT: 'front',
  BACK: 'back',
  SIDE: 'side',
};

class MeasurementComparison extends Component {
  static navigationOptions = ({ route }) => {
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    return {
      title: 'Compare',
      headerRight: renderHeaderRight,
      headerLeft: renderHeaderLeft,
    };
  };

  constructor(props) {
    super(props);
    const measurements = props.route.params?.measurements ?? [];
    const data1 = measurements[0] || {};
    const data2 = measurements[1] || {};
    this.state = {
      data1,
      data2,
      pictureSelection: pictureType.FRONT,
    };
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      // headerRight: this.renderHeaderRight,
      headerLeft: this.renderHeaderLeft,
    });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', {
        screen_name: 'comapre_measurements',
      });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressShare = () => {};

  roundToOneDecimal = (num) => +`${Math.round(`${num}e+1`)}e-1`;

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.onPressShare()}
      title="Share"
      titleStyle={styles.headerButtonText}
    />
  );

  renderPicturePicker = () => (
    <View style={styles.picturePickerContainer}>
      <View style={styles.picturePicker}>
        <TouchableOpacity
          style={
            this.state.pictureSelection === pictureType.FRONT
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState({ pictureSelection: pictureType.FRONT });
          }}
        >
          <Text
            style={
              this.state.pictureSelection === pictureType.FRONT
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Front
          </Text>
        </TouchableOpacity>

        <View
          style={{
            width: 1,
            height: '100%',
            backgroundColor: colors.subGrey,
          }}
        />

        <TouchableOpacity
          style={
            this.state.pictureSelection === pictureType.BACK
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState({ pictureSelection: pictureType.BACK });
          }}
        >
          <Text
            style={
              this.state.pictureSelection === pictureType.BACK
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Back
          </Text>
        </TouchableOpacity>

        <View
          style={{
            width: 1,
            height: '100%',
            backgroundColor: colors.subGrey,
          }}
        />

        <TouchableOpacity
          style={
            this.state.pictureSelection === pictureType.SIDE
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState({ pictureSelection: pictureType.SIDE });
          }}
        >
          <Text
            style={
              this.state.pictureSelection === pictureType.SIDE
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Side
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  renderRow = (label, value1, value2, unit) => {
    const delta = value1 - value2;
    let arrow;
    if (delta > 0) {
      arrow = upArrow;
    } else if (delta < 0) {
      arrow = downArrow;
    }
    const first = value1 ? `${this.roundToOneDecimal(value1)}${unit}` : 'n/a';
    const second = value2 ? `${this.roundToOneDecimal(value2)}${unit}` : 'n/a';
    return (
      <View style={styles.rowContainer}>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.valueContainer}>
          <Text style={styles.value}>{first}</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.valueContainer}>
          <Text style={styles.value}>{second}</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.deltaContainer}>
          <Image
            style={{ width: 12, marginRight: 3, alignSelf: 'center' }}
            source={arrow}
          />
          <Text style={styles.delta}>
            {`${this.roundToOneDecimal(delta)}${unit}`}
          </Text>
        </View>
      </View>
    );
  };

  renderSpacing = () => (
    <View style={styles.rowContainer}>
      <View style={styles.labelContainer} />
      <View style={styles.divider} />
      <View style={styles.valueContainer} />
      <View style={styles.divider} />
      <View style={styles.valueContainer} />
      <View style={styles.divider} />
      <View style={styles.deltaContainer} />
    </View>
  );

  render() {
    const current_user = this.props.currentUser;
    const heightUnit = current_user.unit_height === 'cm' ? 'cm' : '"';
    let weightUnit = current_user.unit_weight === 'kg' ? 'kg' : 'lbs';
    weightUnit = Platform.OS === 'ios' ? ` ${weightUnit}` : weightUnit;
    let image1;
    let image2;
    if (
      this.state.data1.photo_urls
      && this.state.data1.photo_urls[this.state.pictureSelection]
    ) {
      image1 = {
        uri: this.state.data1.photo_urls[this.state.pictureSelection],
      };
    }
    if (
      this.state.data2.photo_urls
      && this.state.data2.photo_urls[this.state.pictureSelection]
    ) {
      image2 = {
        uri: this.state.data2.photo_urls[this.state.pictureSelection],
      };
    }
    return (
      <ScrollView>
        {this.renderPicturePicker()}
        <View style={styles.pictureContainer}>
          <Image
            style={styles.picture}
            source={image1 || cameraIcon}
            resizeMode={image1 ? 'cover' : 'center'}
          />
          <Image
            style={styles.picture}
            source={image2 || cameraIcon}
            resizeMode={image2 ? 'cover' : 'center'}
          />
        </View>
        <View style={styles.dateContainer}>
          <View style={{ flex: 1 }} />
          <View
            style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
          >
            <Text style={styles.date}>
              {moment(this.state.data1.measurements_date).format('M/D/YY')}
            </Text>
          </View>
          <View
            style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
          >
            <Text style={styles.date}>
              {moment(this.state.data2.measurements_date).format('M/D/YY')}
            </Text>
          </View>
          <View style={{ flex: 1 }} />
        </View>
        <View style={styles.dataContainer}>
          {this.renderRow(
            'Weight',
            this.state.data1.weight,
            this.state.data2.weight,
            ` ${weightUnit}`,
          )}
          {this.renderRow(
            'Body Fat',
            this.state.data1.body_fat_percentage,
            this.state.data2.body_fat_percentage,
            '%',
          )}
          {this.renderRow(
            'Lean Muscle',
            this.state.data1.weight
              - this.state.data1.weight
                * (this.state.data1.body_fat_percentage / 100),
            this.state.data2.weight
              - this.state.data2.weight
                * (this.state.data2.body_fat_percentage / 100),
            ` ${weightUnit}`,
          )}
          {this.renderSpacing()}
          {this.renderRow(
            'Neck',
            this.state.data1.neck,
            this.state.data2.neck,
            heightUnit,
          )}
          {this.renderRow(
            'Chest',
            this.state.data1.chest,
            this.state.data2.chest,
            heightUnit,
          )}
          {this.renderSpacing()}
          {this.renderRow(
            'Bicep (R)',
            this.state.data1.bicep_right,
            this.state.data2.bicep_right,
            heightUnit,
          )}
          {this.renderRow(
            'Bicep (L)',
            this.state.data1.bicep_left,
            this.state.data2.bicep_left,
            heightUnit,
          )}
          {this.renderSpacing()}
          {this.renderRow(
            'Forearm (R)',
            this.state.data1.forearm_right,
            this.state.data2.forearm_right,
            heightUnit,
          )}
          {this.renderRow(
            'Forearm (L)',
            this.state.data1.forearm_left,
            this.state.data2.forearm_left,
            heightUnit,
          )}
          {this.renderSpacing()}
          {this.renderRow(
            'Waist',
            this.state.data1.waist,
            this.state.data2.waist,
            heightUnit,
          )}
          {this.renderRow(
            'Hips',
            this.state.data1.hips,
            this.state.data2.hips,
            heightUnit,
          )}
          {this.renderSpacing()}
          {this.renderRow(
            'Thigh (R)',
            this.state.data1.thigh_right,
            this.state.data2.thigh_right,
            heightUnit,
          )}
          {this.renderRow(
            'Thigh (L)',
            this.state.data1.thigh_left,
            this.state.data2.thigh_left,
            heightUnit,
          )}
          {this.renderSpacing()}
          {this.renderRow(
            'Calf (R)',
            this.state.data1.calf_right,
            this.state.data2.calf_right,
            heightUnit,
          )}
          {this.renderRow(
            'Calf (L)',
            this.state.data1.calf_left,
            this.state.data2.calf_left,
            heightUnit,
          )}
          {this.renderSpacing()}
        </View>
      </ScrollView>
    );
  }
}

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  rowContainer: {
    flexDirection: 'row',
  },
  labelContainer: {
    flex: 1,
    paddingLeft: pixelWidth > 640 ? 20 : 5,
    justifyContent: 'center',
    height: 30,
  },
  valueContainer: {
    flex: 1,
    justifyContent: 'center',
    height: 30,
    paddingLeft: pixelWidth > 640 ? 12 : 4,
    paddingRight: pixelWidth > 640 ? 12 : 4,
  },
  deltaContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    height: 30,
    flexDirection: 'row',
    paddingLeft: pixelWidth > 640 ? 12 : 4,
    paddingRight: pixelWidth > 640 ? 12 : 4,
  },
  label: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    color: colors.black,
  },
  value: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.subGrey,
    textAlign: 'right',
  },
  delta: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    alignSelf: 'center',
    color: colors.medYellow,
  },
  divider: {
    width: 1,
    backgroundColor: colors.subGreyLight,
  },
  dataContainer: {
    paddingTop: 20,
  },
  dateContainer: {
    backgroundColor: colors.medYellow,
    flexDirection: 'row',
    paddingVertical: 13,
  },
  date: {
    fontFamily: 'Avenir-Roman',
    fontSize: 16,
    color: colors.white,
  },
  picturePickerContainer: {
    height: 68,
    width: '100%',
    alignItems: 'center',
  },
  picturePicker: {
    height: 27,
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 23,
    marginEnd: 27,
    marginStart: 27,
    borderColor: colors.subGrey,
    borderWidth: 1,
    borderRadius: 4,
  },
  selectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGrey,
  },
  unselectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: null,
  },
  selectedPeriod: {
    color: colors.white,
    alignSelf: 'center',
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
  },
  unselectedPeriod: {
    fontFamily: 'Avenir-Roman',
    alignSelf: 'center',
    color: colors.subGrey,
    fontSize: 13,
  },
  pictureContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  picture: {
    marginHorizontal: 10,
    height: 159,
    width: 94,
    marginBottom: 22,
    backgroundColor: '#f2f3f3',
  },
};

const mapStateToProps = ({ currentUser, selectedClient }) => ({
  currentUser,
  selectedClient,
});
const mapDispatchToProps = {};
export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(MeasurementComparison);
