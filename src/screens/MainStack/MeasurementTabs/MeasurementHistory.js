import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Components
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StatusBar,
  TouchableOpacity,
  View,
} from 'react-native';

import Moment from 'moment';
import {
  VictoryLine,
  VictoryScatter,
  VictoryAxis,
  VictoryChart,
} from 'victory-native';
import nasm from '../../../dataManager/apiConfig';
import {
  BottomBannerActionView,
  ScaledText,
  Swipeable,
} from '../../../components';
import { curvedScale, scaleHeight } from '../../../util/responsive';

// Styles
import { colors } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

// Image Assets
const completedImage = require('../../../resources/completed.png');
const incompleteImage = require('../../../resources/checkmarkIncomplete.png');
const rightArrow = require('../../../resources/rightArrow.png');

const propTypes = {
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
    last_name: PropTypes.string,
    avatar_url: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
  }).isRequired,
};

const periods = {
  WEEK: 'week',
  MONTH: 'month',
  YEAR: 'year',
};

class MeasurementHistory extends Component {
  constructor(props) {
    super(props);
    this.state = {
      period: periods.WEEK,
      endDate: new Moment(),
      measurements: [],
      selectedMeasurements: [],
      graphData: [],
      refreshing: false,
      hasNextPage: true,
      page: -1,
      pageSize: 10,
      loadingStateText: null,
      swiping: false,
      scrolling: false,
      isDisplayDeleteAlert: false,
    };
  }

  componentDidMount() {
    this.getUserMeasurements();
    this.getGraphData();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.route.params.updateHeaderLeft(this.renderHeaderLeft);
      StatusBar.setBarStyle('light-content');
      this.refreshGraphAndList();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => {
        this.props.navigation.pop();
      }}
      titleStyle={styles.headerButtonText}
    />
  );

  getAxesData = () => {
    let axesData = [];
    let startDate;
    switch (this.state.period) {
      case periods.YEAR:
        for (let i = 1; i < 12 + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      case periods.MONTH:
        for (let i = 1; i < this.state.endDate.daysInMonth() + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      default:
        axesData = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
        startDate = this.getStartDateForCurrentRange();
        for (let i = 0; i < axesData.length; i += 1) {
          const num = startDate.date();
          axesData[i] = `${axesData[i]}\n${num}`;
          startDate.add(1, 'days');
        }
        break;
    }
    return axesData;
  };

  getBodyFatData = () => {
    const bodyFatData = [];
    const axesData = this.getAxesData();
    const date = this.getStartDateForCurrentRange();
    axesData.forEach((element) => {
      for (let i = 0; i < this.state.graphData.length; i += 1) {
        const measurement = this.state.graphData[i];
        if (this.checkIfDateMatches(date, measurement)) {
          const bodyFat = measurement.avg_body_fat_percentage * measurement.avg_weight * 0.01;
          const safeBodyFat = bodyFat || 0;
          bodyFatData.push({ x: element, y: safeBodyFat });
          break;
        }
      }
      if (this.state.period === periods.YEAR) {
        date.add(1, 'months');
      } else {
        date.add(1, 'days');
      }
    });
    return bodyFatData.length === 1
      ? bodyFatData
      : this.normalizeData(bodyFatData);
  };

  getCalendarHeaderText = () => {
    const end = this.getEndDateForCurrentRange();
    if (this.state.period === periods.YEAR) {
      return end.format('YYYY');
    }
    if (this.state.period === periods.MONTH) {
      return end.format('MMMM - YYYY');
    }
    const start = this.getStartDateForCurrentRange();
    if (start.year() === end.year()) {
      if (start.month() === end.month()) {
        return `${start.format('MMMM D')} - ${end.format('D, YYYY')}`;
      }
      return `${start.format('MMMM D')} - ${end.format('MMM D, YYYY')}`;
    }
    return `${start.format('MMMM D, YYYY')} - ${end.format('MMMM D, YYYY')}`;
  };

  getEndDateForCurrentRange = () => {
    const newDate = this.state.endDate.clone();
    if (this.state.period === periods.YEAR) {
      return newDate.month(11).date(31);
    }
    if (this.state.period === periods.MONTH) {
      const daysInMonth = newDate.daysInMonth();
      return newDate.date(daysInMonth);
    }
    const dayOfWeekIndex = newDate.day();
    if (dayOfWeekIndex === 6) {
      // if newDate is a Saturday
      return newDate;
    }
    return newDate.add(6 - dayOfWeekIndex, 'days');
  };

  getGraphData = async () => {
    let graphData = [];
    try {
      const userId = this.props.selectedClient?.id;
      const start = this.getStartDateForCurrentRange().format('YYYY-MM-DD');
      const end = this.getEndDateForCurrentRange().format('YYYY-MM-DD');
      const scale = this.state.period === periods.YEAR ? 'monthly' : 'daily';
      const response = await nasm.api.getUserMeasurementsGraphData(
        userId,
        start,
        end,
        scale,
      );
      graphData = response;
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Unable to get graph data. Please try again later.',
      );
    } finally {
      this.setState({ graphData });
    }
  };

  getLeanMuscleData = () => {
    const leanMuscleData = [];
    const axesData = this.getAxesData();
    const date = this.getStartDateForCurrentRange();
    axesData.forEach((element) => {
      for (let i = 0; i < this.state.graphData.length; i += 1) {
        const measurement = this.state.graphData[i];
        if (this.checkIfDateMatches(date, measurement)) {
          const leanMuscle = measurement.avg_lean_body_mass_percentage
            * measurement.avg_weight
            * 0.01;
          const safeLeanMuscle = leanMuscle || 0;
          leanMuscleData.push({ x: element, y: safeLeanMuscle });
          break;
        }
      }
      if (this.state.period === periods.YEAR) {
        date.add(1, 'months');
      } else {
        date.add(1, 'days');
      }
    });
    return leanMuscleData.length === 1
      ? leanMuscleData
      : this.normalizeData(leanMuscleData);
  };

  getStartDateForCurrentRange = () => {
    const newDate = this.state.endDate.clone();
    if (this.state.period === periods.YEAR) {
      return newDate.month(0).date(1);
    }
    if (this.state.period === periods.MONTH) {
      return newDate.date(1);
    }
    const endDate = this.getEndDateForCurrentRange();
    return endDate.subtract(6, 'days');
  };

  getUserMeasurements = async () => {
    if (this.state.hasNextPage) {
      let { measurements, page, hasNextPage } = this.state;
      page += 1;
      try {
        const userId = this.props.selectedClient?.id;
        const start = this.getStartDateForCurrentRange().format('YYYY-MM-DD');
        const end = this.getEndDateForCurrentRange().format('YYYY-MM-DD');
        const response = await nasm.api.getUserMeasurements(
          userId,
          page,
          this.state.pageSize,
          start,
          end,
        );
        measurements = measurements.concat(response.measurementsList);
        hasNextPage = response.nextPage.length > 0;
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || 'Unable to get measurement history. Please try again later.',
        );
      } finally {
        this.setState({ measurements, page, hasNextPage });
      }
    }
  };

  getWeightData = () => {
    const weightData = [];
    const axesData = this.getAxesData();
    const date = this.getStartDateForCurrentRange();
    axesData.forEach((element) => {
      for (let i = 0; i < this.state.graphData.length; i += 1) {
        const measurement = this.state.graphData[i];
        if (this.checkIfDateMatches(date, measurement)) {
          const weight = measurement.avg_weight;
          weightData.push({ x: element, y: weight });
          break;
        }
      }
      if (this.state.period === periods.YEAR) {
        date.add(1, 'months');
      } else {
        date.add(1, 'days');
      }
    });
    return weightData.length === 1
      ? weightData
      : this.normalizeData(weightData);
  };

  checkIfDateMatches = (date, measurement) => {
    const measurementDateString = measurement.date;
    if (measurementDateString) {
      if (this.state.period === periods.YEAR) {
        const measurementDate2 = new Moment(
          measurementDateString,
          'YYYY-MM-DD',
        );
        return date.format('YYYY-MM') === measurementDate2.format('YYYY-MM');
      }
      return date.format('YYYY-MM-DD') === measurementDateString;
    }
    return false;
  };

  clearSelectedMeasurements = () => {
    this.setState({ selectedMeasurements: [] });
    this.props.route.params.updateHeaderRight(
      this.props.route.params.originalHeaderRight,
    );
  };

  deleteMeasurement = (measurement) => {
    if (!this.state.isDisplayDeleteAlert) {
      this.setState({ isDisplayDeleteAlert: true });
      Alert.alert(
        'Delete Measurement',
        'Are you sure you would like to delete this measurement?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              this.setState({ isDisplayDeleteAlert: false });
            },
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: async () => {
              try {
                this.setState({ isDisplayDeleteAlert: false });
                await nasm.api.deleteMeasurement(
                  this.props.selectedClient?.id,
                  measurement.id,
                );
              } catch (error) {
                Alert.alert(
                  'Error',
                  error.message || 'could not delete measurement',
                );
              } finally {
                this.refreshGraphAndList();
              }
            },
          },
        ],
        { cancelable: true },
      );
    }
  };

  isMeasurementSelected = (measurement) => {
    const { selectedMeasurements } = this.state;
    for (let i = 0; i < selectedMeasurements.length; i += 1) {
      const selectedMeasurementId = selectedMeasurements[i].id;
      if (measurement.id === selectedMeasurementId) {
        return true;
      }
    }
    return false;
  };

  leftArrowClicked = () => {
    const newDate = this.state.endDate.clone();
    if (this.state.period === periods.YEAR) {
      newDate.subtract(1, 'years');
    } else if (this.state.period === periods.MONTH) {
      newDate.subtract(1, 'months');
    } else {
      newDate.subtract(7, 'days').format('MMMM DD, YYYY');
    }
    this.setState(
      {
        measurements: [],
        endDate: newDate,
        page: -1,
        hasNextPage: true,
      },
      () => {
        this.getUserMeasurements();
        this.getGraphData();
      },
    );
  };

  measurementSelected = (measurement) => {
    const { selectedMeasurements } = this.state;
    if (this.isMeasurementSelected(measurement)) {
      const index = selectedMeasurements.findIndex(
        (item) => item.id === measurement.id,
      );
      selectedMeasurements.splice(index, 1);
    } else {
      if (selectedMeasurements.length > 1) return;
      selectedMeasurements.push(measurement);
    }
    this.props.route.params.updateHeaderRight(
      selectedMeasurements.length > 0
        ? this.renderHeaderRight
        : this.props.route.params.originalHeaderRight,
    );
    this.setState({ selectedMeasurements }, () => {
      setTimeout(() => {
        this.flatList.scrollToItem({ item: measurement });
      }, 100);
    });
  };

  normalizeData = (data) => {
    const normalized = [];
    let max = -1;
    data.forEach((element) => {
      if (max === -1) {
        max = element.y;
      }
      if (element.y > max) {
        max = element.y;
      }
    });
    data.forEach((element) => {
      if (max !== 0) {
        normalized.push({ x: element.x, y: element.y / max + 0.1 });
      }
    });
    return normalized;
  };

  refreshGraphAndList = () => {
    let {
      measurements, graphData, page, hasNextPage,
    } = this.state;
    this.setState({ refreshing: true }, async () => {
      try {
        const userId = this.props.selectedClient?.id;
        const start = this.getStartDateForCurrentRange().format('YYYY-MM-DD');
        const end = this.getEndDateForCurrentRange().format('YYYY-MM-DD');
        const scale = this.state.period === periods.YEAR ? 'monthly' : 'daily';
        const [listResponse, graphResponse] = await Promise.all([
          nasm.api.getUserMeasurements(
            userId,
            0,
            this.state.pageSize,
            start,
            end,
            scale,
          ),
          nasm.api.getUserMeasurementsGraphData(userId, start, end, scale),
        ]);
        measurements = listResponse.measurementsList;
        graphData = graphResponse;
        page = 0;
        hasNextPage = listResponse.nextPage.length > 0;
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || 'Unable to refresh measurement history. Please try again later.',
        );
      } finally {
        this.setState({
          refreshing: false,
          measurements,
          graphData,
          page,
          hasNextPage,
        });
      }
    });
  };

  rightArrowClicked = () => {
    const newDate = this.state.endDate.clone();
    if (this.state.period === periods.YEAR) {
      newDate.add(1, 'years');
    } else if (this.state.period === periods.MONTH) {
      newDate.add(1, 'months');
    } else {
      newDate.add(7, 'days').format('MMMM DD, YYYY');
    }
    this.setState(
      {
        measurements: [],
        endDate: newDate,
        page: -1,
        hasNextPage: true,
      },
      () => {
        this.getUserMeasurements();
        this.getGraphData();
      },
    );
  };

  roundToTwo = (num) => +`${Math.round(`${num}e+2`)}e-2`;

  renderGraph = () => {
    const leanMuscleData = this.getLeanMuscleData();
    const bodyFatData = this.getBodyFatData();
    const weightData = this.getWeightData();
    const axesData = this.getAxesData();
    const emptyPoints = [];
    axesData.forEach((element) => {
      emptyPoints.push({ x: element, y: 0 });
    });
    return (
      <VictoryChart
        height={curvedScale(200)}
        domainPadding={10}
        padding={{
          top: 40,
          bottom: 45,
          left: 30,
          right: 30,
        }}
      >
        <VictoryAxis
          key={0}
          style={{
            axis: { stroke: '' },
            grid: { stroke: colors.silver },
            tickLabels: {
              fontSize: curvedScale(9),
              padding: 0,
              fill: colors.subGrey,
            },
          }}
          orientation="top"
          tickValues={axesData}
        />
        {this.renderGraphPoints(emptyPoints, 'rgba(0, 0, 0, 0)', null)}

        {/* Lean Muscle Line and Points */}
        {this.renderGraphLine(leanMuscleData, '#D8D8D8')}
        {this.renderGraphPoints(leanMuscleData, '#D8D8D8', 'triangleUp')}

        {/* Body Fat Line and Points */}
        {this.renderGraphLine(bodyFatData, colors.cloudyBlue)}
        {this.renderGraphPoints(bodyFatData, colors.cloudyBlue, 'square')}

        {/* Weight Line and Points */}
        {this.renderGraphLine(weightData, colors.nasmRed)}
        {this.renderGraphPoints(weightData, colors.nasmRed, 'circle')}
      </VictoryChart>
    );
  };

  renderGraphHeader = () => {
    const headerText = this.getCalendarHeaderText();
    return (
      <View style={styles.graphHeaderSection}>
        <TouchableOpacity
          style={{ alignSelf: 'center' }}
          onPress={() => this.leftArrowClicked()}
        >
          <Image
            style={{
              marginStart: 23 * scale,
              tintColor: colors.cloudyBlue,
              transform: [{ rotate: '180deg' }],
            }}
            source={rightArrow}
          />
        </TouchableOpacity>
        <ScaledText style={styles.graphHeaderText}>{headerText}</ScaledText>
        <TouchableOpacity
          style={{ alignSelf: 'center' }}
          onPress={() => this.rightArrowClicked()}
        >
          <Image
            style={{ marginEnd: 23 * scale, tintColor: colors.cloudyBlue }}
            source={rightArrow}
          />
        </TouchableOpacity>
      </View>
    );
  };

  renderGraphLegend = () => {
    const underTextStyle = [styles.underText, { fontSize: 16, paddingLeft: 9 }];
    const legendDotStyle = {
      width: curvedScale(9),
      height: curvedScale(9),
      borderRadius: curvedScale(4.5),
    };
    const legendSquareStyle = { width: curvedScale(9), height: curvedScale(9) };
    return (
      <View
        style={{
          height: scaleHeight(6),
          display: 'flex',
          marginStart: 16 * scale,
          marginEnd: 16 * scale,
          flexDirection: 'column',
          justifyContent: 'space-evenly',
        }}
      >
        <View style={styles.textSection}>
          <View style={styles.dotAndLabel}>
            <View
              style={[legendDotStyle, { backgroundColor: colors.nasmRed }]}
            />
            <ScaledText style={[underTextStyle, { color: colors.nasmRed }]}>
              weight
            </ScaledText>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                legendSquareStyle,
                { backgroundColor: colors.cloudyBlue },
              ]}
            />
            <ScaledText style={[underTextStyle, { color: colors.cloudyBlue }]}>
              body fat
            </ScaledText>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.triangle,
                {
                  borderLeftWidth: curvedScale(5),
                  borderRightWidth: curvedScale(5),
                  borderBottomWidth: curvedScale(10),
                },
              ]}
            />
            <ScaledText style={[underTextStyle, { color: '#D8D8D8' }]}>
              lean muscle
            </ScaledText>
          </View>
        </View>
      </View>
    );
  };

  renderGraphLine = (data, color) => {
    if (data && data.length > 1) {
      // need at least 2 points for a line
      return (
        <VictoryLine
          style={{
            data: { stroke: color, strokeWidth: 2 },
          }}
          data={data}
          interpolation="catmullRom"
        />
      );
    }
    return null;
  };

  renderGraphPoints = (data, color, symbol) => {
    if (data) {
      return (
        <VictoryScatter
          style={{ data: { fill: color } }}
          size={curvedScale(4)}
          symbol={symbol}
          data={data}
        />
      );
    }
    return null;
  };

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.clearSelectedMeasurements()}
      title="Clear All"
      titleStyle={styles.headerButtonText}
    />
  );

  renderMeasurementCell = ({ item }) => {
    const underTextStyle = [
      styles.underText,
      { fontSize: 12, paddingLeft: 3 * scale },
    ];
    const legendDotStyle = {
      width: curvedScale(5),
      height: curvedScale(5),
      borderRadius: curvedScale(2.5),
    };
    const legendSquareStyle = { width: curvedScale(5), height: curvedScale(5) };
    const selected = this.isMeasurementSelected(item)
      ? completedImage
      : incompleteImage;
    const leanMuscle = this.roundToTwo(
      item.weight - item.weight * item.body_fat_percentage * 0.01,
    );
    const bodyFat = item.body_fat_percentage
      ? `${this.roundToTwo(item.body_fat_percentage)}%`
      : 'n/a';
    return (
      <Swipeable
        key={item.id}
        onRightActionRelease={() => this.deleteMeasurement(item)}
        rightContent={
          this.state.scrolling ? null : (
            <View style={styles.deleteButtonContainer}>
              <ScaledText style={styles.deleteText}>Delete</ScaledText>
            </View>
          )
        }
        onSwipeStart={() => {
          this.setState({ swiping: true });
        }}
        onSwipeRelease={() => {
          this.setState({ swiping: false });
        }}
      >
        <TouchableOpacity
          onPress={() => this.props.navigation.navigate('MeasurementDetails', { data: item })}
          style={styles.measurementCell}
        >
          <View style={styles.textSection}>
            {item.measurements_date && (
              <ScaledText style={styles.date}>
                {new Moment(item.measurements_date).format('M/D/YY')}
              </ScaledText>
            )}
            <View style={styles.dotAndLabel}>
              <View
                style={[legendDotStyle, { backgroundColor: colors.nasmRed }]}
              />
              <ScaledText style={underTextStyle}>
                {`${item.weight} ${this.props.currentUser.unit_weight}s`}
              </ScaledText>
            </View>
            <View style={styles.dotAndLabel}>
              <View
                style={[
                  legendSquareStyle,
                  { backgroundColor: colors.cloudyBlue },
                ]}
              />
              <ScaledText style={underTextStyle}>{bodyFat}</ScaledText>
            </View>
            <View style={styles.dotAndLabel}>
              <View
                style={[
                  styles.triangle,
                  {
                    borderLeftWidth: curvedScale(4),
                    borderRightWidth: curvedScale(4),
                    borderBottomWidth: curvedScale(8),
                  },
                ]}
              />
              <ScaledText style={underTextStyle}>
                {`${leanMuscle} ${this.props.currentUser.unit_weight}s`}
              </ScaledText>
            </View>
            <TouchableOpacity onPress={() => this.measurementSelected(item)}>
              <Image key={`${item.status}`} source={selected} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };

  renderPeriodPicker = () => (
    <View style={styles.weekMonthYearPickerContainer}>
      <View style={styles.weekMonthYearPicker}>
        <TouchableOpacity
          style={
            this.state.period === periods.WEEK
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState(
              {
                measurements: [],
                endDate: new Moment(),
                period: periods.WEEK,
                page: -1,
                hasNextPage: true,
              },
              () => {
                this.getUserMeasurements();
                this.getGraphData();
              },
            );
          }}
        >
          <ScaledText
            style={
              this.state.period === periods.WEEK
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Week
          </ScaledText>
        </TouchableOpacity>

        <View
          style={{
            width: 1,
            height: '100%',
            backgroundColor: colors.subGrey,
          }}
        />

        <TouchableOpacity
          style={
            this.state.period === periods.MONTH
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState(
              {
                measurements: [],
                endDate: new Moment(),
                period: periods.MONTH,
                page: -1,
                hasNextPage: true,
              },
              () => {
                this.getUserMeasurements();
                this.getGraphData();
              },
            );
          }}
        >
          <ScaledText
            style={
              this.state.period === periods.MONTH
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Month
          </ScaledText>
        </TouchableOpacity>

        <View
          style={{
            width: 1,
            height: '100%',
            backgroundColor: colors.subGrey,
          }}
        />

        <TouchableOpacity
          style={
            this.state.period === periods.YEAR
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState(
              {
                measurements: [],
                endDate: new Moment(),
                period: periods.YEAR,
                page: -1,
                hasNextPage: true,
              },
              () => {
                this.getUserMeasurements();
                this.getGraphData();
              },
            );
          }}
        >
          <ScaledText
            style={
              this.state.period === periods.YEAR
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Year
          </ScaledText>
        </TouchableOpacity>
      </View>
    </View>
  );

  render() {
    const bottomTitle = this.state.selectedMeasurements.length === 2
      ? '2 Measurements Selected'
      : `${this.state.selectedMeasurements.length}/2 Measurements Selected`;
    return (
      <View style={{ flex: 1 }}>
        <View style={styles.listContainer}>
          <FlatList
            ref={(ref) => {
              this.flatList = ref;
            }}
            contentContainerStyle={{ flexGrow: 1 }}
            ListHeaderComponent={(
              <View>
                {this.renderPeriodPicker()}
                {this.renderGraphHeader()}
                <View style={styles.graphSectionContainer}>
                  {this.renderGraph()}
                  {this.renderGraphLegend()}
                </View>
              </View>
            )}
            data={this.state.measurements}
            renderItem={this.renderMeasurementCell}
            keyExtractor={(item) => item.id}
            onEndReached={this.getUserMeasurements}
            refreshControl={(
              <RefreshControl
                refreshing={this.state.refreshing}
                onRefresh={this.refreshGraphAndList}
              />
            )}
            onScrollBeginDrag={() => {
              this.setState({ scrolling: true });
            }}
            onScrollEndDrag={() => {
              this.setState({ scrolling: false });
            }}
            scrollEnabled={!this.state.swiping}
          />
          {this.state.selectedMeasurements.length > 0 && (
            <BottomBannerActionView
              title={bottomTitle}
              buttonTitle="Compare"
              buttonDisabled={this.state.selectedMeasurements.length !== 2}
              onPress={() => {
                this.props.navigation.navigate('MeasurementComparison', {
                  measurements: this.state.selectedMeasurements.sort(
                    (a, b) => -Moment(a.measurements_date).diff(b.measurements_date),
                  ),
                });
                this.clearSelectedMeasurements();
              }}
            />
          )}
        </View>
      </View>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  weekMonthYearPickerContainer: {
    height: 68 * scale,
    width: '100%',
    alignItems: 'center',
  },
  weekMonthYearPicker: {
    height: 27 * scale,
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 23 * scale,
    marginEnd: 27 * scale,
    marginStart: 27 * scale,
    borderColor: colors.subGrey,
    borderWidth: 1,
    borderRadius: 4 * scale,
  },
  selectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGrey,
  },
  unselectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: null,
  },
  selectedPeriod: {
    color: colors.white,
    alignSelf: 'center',
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
  },
  unselectedPeriod: {
    fontFamily: 'Avenir-Roman',
    alignSelf: 'center',
    color: colors.subGrey,
    fontSize: 13,
  },
  graphHeaderSection: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    height: 37 * scale,
    borderWidth: 1,
    justifyContent: 'space-between',
    borderColor: colors.silver51,
  },
  graphHeaderText: {
    fontWeight: 'bold',
    color: colors.black,
    alignSelf: 'center',
  },
  graphSectionContainer: {
    flexDirection: 'column',
    width: '100%',
    backgroundColor: '#FCFDFD',
  },
  listContainer: {
    flex: 1,
    width: '100%',
  },
  measurementCell: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: 'rgb(242,243,243)',
  },
  dotAndLabel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  textSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scaleHeight(6),
    height: scaleHeight(10),
  },
  underText: {
    fontFamily: 'Avenir-Roman',
    color: 'rgb(124, 128, 132)',
    paddingRight: 10 * scale,
  },
  triangle: {
    width: curvedScale(3),
    height: curvedScale(3),
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#D8D8D8',
  },
  deleteButtonContainer: {
    backgroundColor: 'red',
    flex: 1,
    justifyContent: 'center',
  },
  deleteText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.white,
    paddingLeft: 30 * scale,
  },
};

MeasurementHistory.propTypes = propTypes;
const mapStateToProps = ({ currentUser, selectedClient }) => ({
  currentUser,
  selectedClient,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(MeasurementHistory);
