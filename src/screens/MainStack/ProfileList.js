import React, { useEffect } from 'react';
import {
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
  View,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import analytics from '@react-native-firebase/analytics';
import { colors } from '../../styles';
import { logClubConnectProfileSwitch } from '../../util/Analytics';
import {
  resetTrainerActiveProfile,
  setTrainerActiveProfile,
} from '../../reducers/trainerActiveProfileReducer';
import { reset } from '../../actions';
import { FEATURE_FLAGS } from '../../constants';

const tempProfilePic = require('../../resources/defaultProfile.png');
const rightArrow = require('../../resources/imgRightArrowGray.png');

const propTypes = {
  navigation: PropTypes.any.isRequired,
};

const stateSelector = (state) => state;

const ProfileList = ({ navigation }) => {
  const dispatch = useDispatch();
  const { currentUser, trainerActiveProfile } = useSelector(stateSelector);

  const updatedClubs = [
    {
      ClubId: null,
      ClubName: 'Personal',
      LocationName: [],
      ClubLogoUrl: tempProfilePic,
    },
  ];

  const clubs = Array.isArray(currentUser?.club_connect_user)
    ? currentUser?.club_connect_user
    : Object.values(currentUser?.club_connect_user);

  if (clubs?.length) {
    clubs.forEach((club) => {
      if (
        club.ClubStatus?.toLowerCase() === 'active'
        && club.TrainerStatus?.toLowerCase() === 'active'
      ) {
        if (FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED) {
          club.Locations?.forEach((loc) => {
            const clubObj = {
              ...club,
              Locations: {
                ...loc,
              },
            };
            updatedClubs.push(clubObj);
          });
        } else {
          updatedClubs.push(club);
        }
      }
    });
  }

  useEffect(() => {
    setHeaderTitle();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  const setHeaderTitle = () => {
    const title = 'My Profiles';
    navigation.setOptions({ title });
  };

  const switchToClub = (item) => {
    // Determine current and target profile types
    const fromProfileType = trainerActiveProfile?.ClubId ? 'club' : 'personal';
    const toProfileType = item.ClubId ? 'club' : 'personal';

    // Track profile switching analytics
    logClubConnectProfileSwitch(
      currentUser.id,
      fromProfileType,
      toProfileType,
      item.ClubId ? item : null,
    );

    if (item.ClubId) {
      analytics().logEvent('club_connect_screen_view', {
        screen_name: 'club_profile',
        club_name: item.ClubName,
        club_id: item.ClubId,
      });
      dispatch(setTrainerActiveProfile(item));
    } else {
      dispatch(resetTrainerActiveProfile());
    }
    reset(navigation, 'ModalStack', null, null);
  };

  const renderProfileItem = ({ item }) => {
    let profileUri = tempProfilePic;
    if (item?.ClubId) {
      profileUri = { uri: item?.ClubLogoUrl };
    } else if (currentUser.avatar_url) {
      profileUri = { uri: currentUser.avatar_url };
    }
    return (
      <TouchableOpacity
        onPress={() => switchToClub(item)}
        style={styles.itemContainer}
      >
        <View style={styles.avatarContainer}>
          <Image
            style={styles.profileImageStyle(!!item.Locations?.Id)}
            source={profileUri}
          />
        </View>
        <View style={styles.nameView}>
          <Text
            style={styles.nameTextStyle}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {item.ClubName}
          </Text>
          {item.Locations?.Id ? (
            <Text
              style={styles.addressTextStyle}
              numberOfLines={2}
              ellipsizeMode="tail"
            >
              {item.Locations?.Name}
            </Text>
          ) : null}
        </View>
        <View style={styles.arrowView}>
          <Image source={rightArrow} />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={updatedClubs || []}
        renderItem={renderProfileItem}
        keyExtractor={(item) => item.ClubId}
        onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  profileImageStyle: (isBorder) => ({
    width: 60,
    height: 60,
    borderRadius: 60,
    borderWidth: isBorder ? 1 : 0,
    borderColor: colors.medYellow,
    backgroundColor: colors.black,
  }),
  avatarContainer: {
    width: '20%',
    alignItems: 'center',
  },
  nameView: {
    paddingHorizontal: 5,
    width: '60%',
  },
  arrowView: {
    alignItems: 'flex-end',
    paddingHorizontal: 5,
    width: '20%',
    justifyContent: 'center',
  },
  nameTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    color: colors.black,
  },
  addressTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 11,
    fontWeight: '400',
    color: colors.subGrey,
  },
  itemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    borderBottomWidth: 1,
    borderColor: colors.subGreyLight,
    paddingVertical: 15,
    paddingHorizontal: 15,
    alignItems: 'center',
  },
});

ProfileList.propTypes = propTypes;

export default ProfileList;
