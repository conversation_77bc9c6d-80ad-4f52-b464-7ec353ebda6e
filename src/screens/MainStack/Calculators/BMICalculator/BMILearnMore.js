import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
  FlatList,
  Platform,
  UIManager,
  StatusBar,
  ScrollView,
  StyleSheet,
  Dimensions,
  SafeAreaView,
  LayoutAnimation,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';

const numOfColumns = 3;
const screenWidth = Dimensions.get('screen').width;
const columnWidth = (screenWidth - curvedScale(44)) / numOfColumns;
const closeImg = require('../../../../assets/closeCircle.png');

const BMI_CATEGORIES = [
  {
    id: 1,
    category: 'Essential Fat',
    men: '2.5%',
    women: '10-13%',
  },
  {
    id: 2,
    category: 'Athletes',
    men: '6-13%',
    women: '14-20%',
  },
  {
    id: 3,
    category: 'Fitness',
    men: '14-17%',
    women: '21-24%',
  },
  {
    id: 4,
    category: 'Average',
    men: '18-24%',
    women: '25-31%',
  },
  {
    id: 5,
    category: 'Obese',
    men: '25% or higher',
    women: '32% or higher',
  },
];

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

const BMILearnMore = ({ navigation }) => {
  useEffect(() => {
    StatusBar.setBarStyle('dark-content');
    setTimeout(() => {
      setCustomNavigationHeader();
    }, 300);
    return () => {
      StatusBar.setBarStyle('light-content');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setAnimation = () => {
    if (Platform.OS === 'android') {
      if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
      }
    }
    LayoutAnimation.configureNext({
      duration: 500,
      create: { type: 'linear', property: 'opacity' },
      update: { type: 'spring', springDamping: 0.5 },
    });
  };

  const setCustomNavigationHeader = () => {
    setAnimation();
    navigation.setOptions({
      headerStyle: { backgroundColor: colors.white, height: 100 },
      headerLeft: renderCloseImg,
    });
  };

  const goBack = () => {
    navigation.goBack();
  };

  const renderCloseImg = () => (
    <TouchableOpacity
      activeOpacity={0.6}
      hitSlop={{
        top: 20,
        bottom: 20,
        left: 20,
        right: 20,
      }}
      onPress={goBack}
      style={styles.closeImgView}
    >
      <Image source={closeImg} />
    </TouchableOpacity>
  );

  const renderPageContent = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <Text style={styles.title(22)}>Body Mass Index (BMI)</Text>
      <Text style={styles.title(16, 30)}>What is BMI?</Text>
      <View style={styles.descriptionView}>
        <Text style={styles.description}>
          BMI, which is short for body mass index, is a tool that is used
          clinically to define bodyweight concerning health. Essentially it is a
          mathematical tool that utilizes two metrics of a person, their height
          and weight, to estimate and categorize their body weight.
          {'\n\n'}
          There are two ways to calculate BMI, depending on whether you use the
          metric system or the imperial system. The most common equation is the
          metric one, but imperial can be used.
          {'\n\n'}
          Once BMI is calculated, the number is binned into categories that aim
          to define the bodyweight of the individual. Additionally, BMI metrics
          apply differently based on age, wherein individuals over 20 years of
          age are considered adults and individuals aged 2-20 are considered
          children. Infants under the age of 2 have a different scale.
          {'\n\n'}
          <Text style={styles.title(16, 30)}>BMI Formula</Text>
          {'\n\n'}
          The formula for BMI simply utilizes height and weight to create an
          index measurement, which has no units.
          {'\n\n'}
          {'\t'}
          • Metric formula: BMI = weight (kg) = [height(m)]2
          {'\n'}
          {'\t'}
          • Imperial formula: BMI = 703 * weight (lb) = [height (in.)]2
          {'\n\n'}
          For example, you can calculate Mary&apos;s BMI score using either
          formula if she currently weighs 160 lb (72.57 kg) and stands 5 ft 7
          in. or 67 in. (1.70 m).
          {'\n\n'}
          Answer: BMI = 25.1
          {'\n\n'}
          <Text style={styles.title(16, 30)}>
            5 Health Risks Associated with a High BMI
          </Text>
          {'\n\n'}
          BMI is an incredibly useful tool for risk prediction. When we look at
          human health data, BMI is one of the most effective risk prediction
          tools we have in modern medicine. This is especially true when BMI is
          utilized to predict the risk of chronic diseases and many of the
          leading causes of death.
          {'\n\n'}
          Here is a list of 5 health risks associated with a high BMI.
          {'\n\n'}
          <Text style={styles.title(16, 30)}>• Cardiovascular Disease:</Text>
          {' '}
          As
          BMI increases the risk of cardiovascular disease also increases. Some
          studies show that individuals with the highest classification of BMI
          have a 3-fold higher risk of having a cardiovascular event than those
          in the normal BMI category. However, it doesn&apos;t require being in
          the highest category, even those in the &quot;overweight&quot;
          category carry a 20% higher risk of having a cardiovascular event
          (Khan et al., 2018).
          {'\n\n'}
          <Text style={styles.title(16, 30)}>• Diabetes:</Text>
          {' '}
          BMI is one of
          the most robust predictors of developing diabetes. In fact, in some
          studies, those who have a BMI in the &quot;overweight&quot; category
          carry a roughly 6-fold higher risk of developing diabetes than those
          with a normal BMI (Sanada et al., 2012).
          {'\n\n'}
          <Text style={styles.title(16, 30)}>• Cancer:</Text>
          {' '}
          Cancer is a
          complicated disease with many different factors leading to its cause.
          However, BMI is linked to some forms of cancer. For example, higher
          BMI is associated with higher rates of liver and colon cancer
          (Bhaskaran et al., 2014).
          {'\n\n'}
          <Text style={styles.title(16, 30)}>• Hypertension:</Text>
          {' '}
          Hypertension, or elevated blood pressure, is one of the leading
          modifiable risk factors for cardiovascular disease. Some evidence
          shows that for every unit increase in BMI, there is a 1-2 mmHg
          increase in blood pressure (Linderman et al., 2018).
          {'\n\n'}
          <Text style={styles.title(16, 30)}>• Stroke:</Text>
          {' '}
          Stroke is the
          fifth leading cause of disease among adults in the United States.
          Those with a BMI of 30 or higher have reported a roughly 2-fold
          increase in the risk of stroke (Kurth et al., 2002). .
          {'\n\n'}
          <Text style={styles.title(16, 30)}>Shortcomings of the BMI</Text>
          {'\n\n'}
          BMI is often debated as not being a useful tool. However, oftentimes
          these discussions fail to understand exactly what BMI is and how best
          to utilize it as a tool. It is important to understand that BMI does
          not discriminate between fat mass and lean mass and does not directly
          address adiposity (i.e., body fat percentage). However, it is a tool
          based on large sample sizes and is primarily used as a risk prediction
          tool over large numbers of people. As such, on a population level, BMI
          can approximate levels of adiposity to a useful degree. Furthermore,
          it is a good tool for estimating risk for specific outcomes (e.g.,
          mortality, cardiovascular disease, diabetes).
          {'\n\n'}
          However, on an individual level, BMI can be less accurate, and it is
          best used in conjunction with other data (e.g., body composition
          tests) in the full assessment of an individual&apos;s weight status.
          Individuals who carry a substantially high level of lean mass (e.g.,
          bodybuilders or powerlifters) may fall into the overweight or obese
          category based solely on BMI but may have body fat percentages in the
          single digits.
          {'\n\n'}
          Conversely, people who carry a minimal amount of lean mass (e.g.,
          highly sedentary people) may fall into the normal/healthy category
          based solely on BMI but may have levels of body fat that would be
          considered overweight or obese.
          {'\n\n'}
          <Text style={styles.title(16, 30)}>
            BMI Differences for Men, Women, and Children
          </Text>
          {'\n\n'}
          Currently, BMI among adults is used to define and classify weight the
          same across men and women. However, at a given BMI women tend to carry
          a slightly higher body fat, which is expected given the physiological
          differences between men and women. This is where the body fat scales
          can provide additional information when examining the anthropometrics
          of men and women. See the table below for categories of adiposity
          status by body fat percentage by biological sex.
        </Text>
      </View>
      {renderTable()}
      <View style={styles.descriptionView}>
        <Text style={styles.description}>
          While BMI tends to be fairly accurate for people with height that is
          close to the average or median height, it tends to become less
          accurate the further away one&apos;s height is from average or median.
          In general, BMI tends to overestimate body fatness among shorter
          people and underestimate it among taller people.
          {'\n\n'}
          In children, the BMI classification system is used much differently.
          In children, the BMI value is not &quot;binned&quot; into categories
          based solely on the BMI metric. While BMI is calculated the same, the
          classification is based on a percentile compared to all other children
          of that age. A BMI less than the 5th percentile is considered
          underweight while children with a BMI between the 85th and 95th
          percentile are considered overweight.
        </Text>
      </View>
    </ScrollView>
  );

  const renderTable = () => (
    <FlatList
      horizontal
      data={['1']}
      renderItem={renderTableContent}
      scrollEnabled={false}
    />
  );

  const renderTableContent = () => (
    <FlatList
      style={styles.tableContainer}
      data={BMI_CATEGORIES}
      renderItem={renderListItem}
      ListHeaderComponent={renderListHeaderComponent}
      scrollEnabled={false}
    />
  );

  const renderListHeaderComponent = () => (
    <View style={styles.listHeaderContainer}>
      {renderListHeaderItemView('Category')}
      {renderHorizontalSeparator()}
      {renderListHeaderItemView('Men')}
      {renderHorizontalSeparator()}
      {renderListHeaderItemView('Women')}
    </View>
  );

  const renderListHeaderItemView = (title) => (
    <View style={styles.listHeaderItemView}>
      <Text style={styles.listHeaderItemTitle}>{title}</Text>
    </View>
  );

  const renderHorizontalSeparator = () => (
    <View style={styles.horizontalSeparator} />
  );

  const renderListItem = ({ item, index }) => (
    <View
      style={{
        ...styles.listItemContainer,
        backgroundColor: index % 2 === 1 ? colors.white : colors.paleGray2,
      }}
    >
      {renderListItemView(`${item.category}`)}
      {renderHorizontalSeparator()}
      {renderListItemView(`${item.men}`)}
      {renderHorizontalSeparator()}
      {renderListItemView(`${item.women}`)}
    </View>
  );

  const renderListItemView = (label) => (
    <View style={styles.listItemView}>
      <View style={styles.listItemContainer}>
        <Text style={styles.lisItemValue}>{label}</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>{renderPageContent()}</SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  closeImgView: {
    marginHorizontal: curvedScale(20),
    width: 30,
    height: 30,
  },
  content: {
    marginVertical: curvedScale(20),
    paddingHorizontal: curvedScale(20),
    height: '70%',
    backgroundColor: colors.white,
  },
  title: (fontSize = 22, marginTop = 0) => ({
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(fontSize),
    fontWeight: '700',
    textAlign: 'left',
    marginTop: curvedScale(marginTop),
  }),
  descriptionView: {
    marginTop: curvedScale(20),
  },
  description: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(15),
    textAlign: 'left',
  },
  tableContainer: {
    marginTop: curvedScale(40),
    marginBottom: curvedScale(20),
    borderWidth: 1,
    borderRadius: curvedScale(10),
    borderColor: colors.textPlaceholder,
  },
  listHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  listHeaderItemView: {
    width: columnWidth,
    borderBottomWidth: 1,
    borderBottomColor: colors.textPlaceholder,
    backgroundColor: colors.white,
    paddingVertical: curvedScale(20),
  },
  listHeaderItemTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(14),
    fontWeight: '700',
    textAlign: 'center',
  },
  horizontalSeparator: {
    width: 1,
    height: '100%',
    backgroundColor: colors.textPlaceholder,
  },
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemView: {
    width: columnWidth,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.paleGray2,
    paddingVertical: curvedScale(20),
  },
  lisItemValue: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    fontWeight: '400',
    textAlign: 'center',
  },
});

BMILearnMore.propTypes = propTypes;
BMILearnMore.defaultProps = defaultProps;

export default BMILearnMore;
