import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  Text,
  Image,
  View,
  FlatList,
  Dimensions,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import PropTypes from 'prop-types';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../../components/HeaderRightButton';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';
import { CALCULATOR_CONTEXTS } from '../../../../reducers/calculatorContextReducer';
import nasm from '../../../../dataManager/apiConfig';
import LoadingSpinner from '../../../../components/LoadingSpinner';
import BMIConstants from './BMIConstants';

const numOfColumns = 2;
const screenWidth = Dimensions.get('screen').width;
const columnWidth = screenWidth / numOfColumns;
const imgBMIResult = require('../../../../assets/bmi-results.png');

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    pop: PropTypes.func,
    popToTop: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      bmiData: PropTypes.object,
      onReset: PropTypes.func,
      comingFrom: PropTypes.string,
    }),
  }).isRequired,
  calculatorContext: PropTypes.object,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  bmiData: PropTypes.object,
};

const defaultProps = {
  calculatorContext: null,
  bmiData: null,
};

class BMIResultTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      bmiData: props?.route?.params?.bmiData || props?.bmiData,
      BMI_CATEGORIES: [
        {
          id: 1,
          label: 'Very Severely \nUnderweight',
          description: '',
          rangeStart: 0,
          rangeEnd: 15.9,
        },
        {
          id: 2,
          label: 'Underweight',
          description: '',
          rangeStart: 16,
          rangeEnd: 18.4,
        },
        {
          id: 3,
          label: 'Normal',
          description: '(Healthy Weight)',
          rangeStart: 18.5,
          rangeEnd: 24.9,
        },
        {
          id: 4,
          label: 'Overweight',
          description: '',
          rangeStart: 25,
          rangeEnd: 29.9,
        },
        {
          id: 5,
          label: 'Obese Class I',
          description: '(Moderately Obese)',
          rangeStart: 30,
          rangeEnd: 34.9,
        },
        {
          id: 6,
          label: 'Obese Class II',
          description: '(Severely Obese)',
          rangeStart: 35,
          rangeEnd: 39.9,
        },
        {
          id: 7,
          label: 'Obese Class III',
          description: '(Very Severely Obese)',
          rangeStart: 40,
          rangeEnd: '+',
        },
      ],
      isLoading: false,
    };
  }

  componentDidMount() {
    if (this.props?.route?.params?.bmiData) {
      this.props.navigation.setOptions({
        headerLeft: this.renderHeaderLeft,
        headerRight: this.renderHeaderRight,
        headerTitle: 'Body Mass Index',
        headerTitleStyle: styles.headerTitle,
      });
    }
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      title={
        this.props?.route?.params?.comingFrom === BMIConstants.BMIHistory
          ? 'Back'
          : 'Cancel'
      }
      onPress={() => {
        if (this.props?.route?.params?.comingFrom === BMIConstants.BMIHistory) {
          this.props.navigation.pop();
        } else if (
          this.props.calculatorContext?.type
          === CALCULATOR_CONTEXTS.SELECTED_CLIENT
        ) {
          this.props.navigation.pop(3);
        } else {
          this.props.navigation.popToTop();
        }
      }}
      titleStyle={styles.headerButtonsText}
    />
  );

  renderHeaderRight = () => {
    if (this.props?.route?.params?.comingFrom === BMIConstants.BMIHistory) {
      return null;
    }

    if (
      this.props.calculatorContext?.type === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT
    ) {
      return (
        <HeaderRightButton
          title="Reset"
          onPress={this.props.route?.params?.onReset}
          titleStyle={styles.headerButtonsText}
        />
      );
    }

    const userId = this.props?.selectedClient?.id;
    const {
      unit_weight, weight, unit_height, height,
    } = this.state.bmiData;
    const bmi = this.state.bmiData.bmiScore;
    return (
      <HeaderRightButton
        title="Save"
        onPress={async () => {
          try {
            this.setState({ isLoading: true });
            const jsonData = {
              unit_weight,
              weight,
              unit_height,
              height,
              bmi,
            };
            const response = await nasm.api.saveBMIResults(userId, jsonData);
            if (response !== null) {
              this.setState({ isLoading: false });
              this.props.navigation.navigate('BMITabView');
            }
          } catch (error) {
            this.setState({ isLoading: false });
            Alert.alert(
              'Error',
              error.message || 'Something went wrong! Please try again later.',
            );
          }
        }}
        titleStyle={styles.headerButtonsText}
      />
    );
  };

  renderTopHeader = (bmi_category) => (
    <View style={styles.topHeader}>
      <View style={styles.headerBMIResultsView}>
        <Text style={styles.headerBMIResultsText(colors.black)}>BMI</Text>
        <Text style={styles.headerBMIResultsText(colors.fillDarkGrey)}>
          Results
        </Text>
      </View>
      <View style={styles.headerResultValueContainer}>
        <View style={styles.headerStatusContainer}>
          <View style={styles.headerStatusColorView(6)} />
          <Text> </Text>
          <View>
            <Text style={styles.headerBMIText}>{`${bmi_category.label}`}</Text>
          </View>
        </View>
        <View style={styles.headerBMIViewStyle}>
          <Image source={imgBMIResult} style={styles.headerIconStyle} />
          <Text style={styles.headerBMIValueText(colors.black, 18, 'bold')}>
            {this.getUnroundedDecimalNumber(this.state.bmiData.bmiScore)}
          </Text>
        </View>
      </View>
    </View>
  );

  renderListHeaderItemView = (title) => (
    <View style={styles.listHeaderItemView}>
      <Text style={styles.listHeaderItemTitle}>{title}</Text>
    </View>
  );

  renderHorizontalSeparator = () => <View style={styles.horizontalSeparator} />;

  renderListHeaderComponent = () => (
    <View style={styles.listHeaderContainer}>
      {this.renderListHeaderItemView('Category')}
      {this.renderHorizontalSeparator()}
      {this.renderListHeaderItemView('From')}
    </View>
  );

  renderListItemView = (label, description, isSelected, showStatusColor) => (
    <View style={styles.listItemView}>
      <View style={styles.listItemContainer}>
        {isSelected && showStatusColor ? (
          <View style={styles.headerStatusColorView(10)} />
        ) : null}
        <View style={{ marginLeft: curvedScale(10) }}>
          <Text style={styles.lisItemValue(isSelected)}>{label}</Text>
          {description ? (
            <Text style={styles.lisItemValue(isSelected)}>{description}</Text>
          ) : null}
        </View>
      </View>
    </View>
  );

  renderListItem = ({ item, index }) => {
    let separator = '';
    if (index < 6) {
      separator = '-';
    }
    return (
      <View
        style={{
          ...styles.listItemContainer,
          backgroundColor: index % 2 === 1 ? colors.white : colors.paleGray2,
        }}
      >
        {this.renderListItemView(
          `${item.label}`,
          `${item.description}`,
          item.isSelected,
          true,
        )}
        {this.renderHorizontalSeparator()}
        {this.renderListItemView(
          `${item.rangeStart}${separator}${item.rangeEnd}`,
          null,
          item.isSelected,
        )}
      </View>
    );
  };

  getUnroundedDecimalNumber = (number, decimalPoint = 1) => {
    // eslint-disable-next-line no-extend-native
    Number.prototype.toFixedNoRound = function (precision = 1) {
      const factor = 10 ** precision;
      return Math.floor(this * factor) / factor;
    };
    return number.toFixedNoRound(decimalPoint);
  };

  findBMICategory = () => {
    const { bmiData, BMI_CATEGORIES } = this.state;
    const { bmiScore } = bmiData;
    const bmi_cat = BMI_CATEGORIES.reduce((prev, curr) => (this.getUnroundedDecimalNumber(bmiScore) < curr.rangeStart ? prev : curr));
    return bmi_cat;
  };

  getTableData = () => {
    const bmi_category = this.findBMICategory();
    const { BMI_CATEGORIES } = this.state;
    const index = BMI_CATEGORIES.findIndex((cat) => cat.id === bmi_category.id);
    const scoreCategory = BMI_CATEGORIES[index];
    BMI_CATEGORIES[index] = {
      ...scoreCategory,
      isSelected: true,
    };
    return {
      bmi_category,
      BMI_CATEGORIES,
    };
  };

  render() {
    const data = this.getTableData();
    return (
      <SafeAreaView style={styles.container}>
        {this.renderTopHeader(data.bmi_category)}
        <FlatList
          data={data.BMI_CATEGORIES}
          renderItem={this.renderListItem}
          ListHeaderComponent={this.renderListHeaderComponent}
        />
        <LoadingSpinner
          visible={this.state.isLoading}
          size="large"
          backgroundColor="rgba(0, 0, 0, 0.25)"
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerButtonsText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.paleGray2,
    padding: curvedScale(20),
  },
  headerBMIResultsView: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '40%',
  },
  headerBMIResultsText: (color) => ({
    color,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(22),
    fontWeight: '700',
    textAlign: 'left',
    marginRight: curvedScale(5),
  }),
  headerResultValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: '60%',
  },
  headerStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '50%',
  },
  headerStatusColorView: (size) => ({
    width: curvedScale(size),
    height: curvedScale(size),
    borderRadius: curvedScale(size) / 2,
    backgroundColor: colors.greenselect,
  }),
  headerBMIText: {
    color: colors.black,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(15),
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerBMIViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: curvedScale(10),
  },
  headerIconStyle: {
    width: curvedScale(25),
    height: curvedScale(25),
    marginRight: curvedScale(5),
  },
  headerBMIValueText: (color, fontSize, fontWeight) => ({
    color,
    fontSize: curvedScale(fontSize),
    fontWeight,
    fontFamily: 'Avenir',
    textAlign: 'right',
  }),
  listHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listHeaderItemView: {
    width: columnWidth,
    borderBottomWidth: 1,
    borderBottomColor: colors.textPlaceholder,
    backgroundColor: colors.white,
    paddingVertical: curvedScale(15),
  },
  listHeaderItemTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(14),
    fontWeight: '700',
    textAlign: 'center',
  },
  horizontalSeparator: {
    width: 1,
    height: '100%',
    backgroundColor: colors.textPlaceholder,
  },
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemView: {
    width: columnWidth,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.paleGray2,
    height: curvedScale(60),
  },
  lisItemValue: (isSelected) => ({
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    fontWeight: isSelected ? 'bold' : '400',
    textAlign: 'center',
  }),
});

BMIResultTable.propTypes = propTypes;
BMIResultTable.defaultProps = defaultProps;

const mapStateToProps = ({ calculatorContext, selectedClient }) => ({
  calculatorContext,
  selectedClient,
});
const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(BMIResultTable);
