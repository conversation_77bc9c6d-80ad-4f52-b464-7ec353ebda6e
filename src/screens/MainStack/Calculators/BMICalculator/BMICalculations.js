import { LIFT_TYPE } from '../../../../types/RMtypes';

export function isValidData(weight, height) {
  if (weight <= 0) {
    throw new Error('Weight must be a number > 0');
  }

  if (height <= 0) {
    throw new Error('Height must be a number > 0');
  }

  return true;
}

export function calculateMaxLiftWeight(weight, reps) {
  return weight * (1 + reps / 30);
}

export function calculateLiftWeight(maxWeight, reps) {
  return maxWeight / (1 + reps / 30);
}

export function calculateTabularResults(maxWeight, percentages) {
  const weightTable = [{ percentage: percentages[0], weight: maxWeight }];

  for (let i = 1; i < 10; i += 1) {
    weightTable.push({
      percentage: percentages[i],
      weight: calculateLiftWeight(maxWeight, i + 1),
    });
  }

  return weightTable;
}

export function calculateTabularResultsNSCA(maxWeight, reps, coefficients) {
  const weightTable = [];
  const maxNSCAWeight = maxWeight * coefficients[reps - 1];
  const maxLiftWeight = reps > 1 ? calculateMaxLiftWeight(maxWeight, reps) : maxWeight;

  for (let i = 0; i < 10; i += 1) {
    const coefficient = coefficients[i];
    const weight = maxNSCAWeight / coefficient;
    const percentage = weight / maxLiftWeight;
    const weightRecord = { percentage, weight };
    weightTable.push(weightRecord);
  }

  return weightTable;
}

// convenience functions
export function calculateLiftsTable(maxWeight) {
  const percentages = [1, 0.94, 0.91, 0.88, 0.86, 0.83, 0.81, 0.79, 0.77, 0.75];
  return calculateTabularResults(maxWeight, percentages);
}

export function calculateNSCATable(maxWeight, reps, type) {
  const coefficients = {
    [LIFT_TYPE.DEADLIFT]: [
      1,
      1.065,
      1.13,
      1.147,
      1.164,
      1.181,
      1.198,
      1.22,
      1.232,
      1.24,
    ],
    [LIFT_TYPE.SQUAT]: [
      1,
      1.0475,
      1.13,
      1.1575,
      1.2,
      1.242,
      1.284,
      1.326,
      1.368,
      1.41,
    ],
    [LIFT_TYPE.BENCH_PRESS]: [
      1,
      1.035,
      1.08,
      1.115,
      1.15,
      1.18,
      1.22,
      1.255,
      1.29,
      1.325,
    ],
  };
  return calculateTabularResultsNSCA(maxWeight, reps, coefficients[type]);
}
