import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  View,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  StyleSheet,
  Alert,
  Image,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import moment from 'moment';
import {
  VictoryAxis,
  VictoryChart,
  VictoryLine,
  VictoryScatter,
} from 'victory-native';
import PropTypes from 'prop-types';
import ScaledText from '../../../../components/ScaledText';
import nasm from '../../../../dataManager/apiConfig';
import BMIResultTable from './BMIResultTable';
import LoadingSpinner from '../../../../components/LoadingSpinner';
import { colors } from '../../../../styles';
import { curvedScale, scaleHeight } from '../../../../util/responsive';
import BMIConstants from './BMIConstants';

const imgBMIResult = require('../../../../assets/bmi-results.png');

const periods = {
  MONTH: 'MONTHLY',
  YEAR: 'YEARLY',
};

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

const defaultProps = {};

const rightArrow = require('../../../../resources/rightArrow.png');

class BMIHistory extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      refreshing: false,
      page: 1,
      hasNextPage: false,
      currentDate: moment(),
      bmiGraphData: [],
      bmiListData: [],
      period: periods.MONTH,
      BMI_CATEGORIES: [
        {
          id: 1,
          label: 'Very Severely Underweight',
          description: '',
          weight: 0,
        },
        {
          id: 2,
          label: 'Underweight',
          description: '',
          weight: 16,
        },
        {
          id: 3,
          label: 'Normal',
          description: '(Healthy Weight)',
          weight: 18.5,
        },
        {
          id: 4,
          label: 'Overweight',
          description: '',
          weight: 25,
        },
        {
          id: 5,
          label: 'Obese Class I',
          description: '(Moderately Obese)',
          weight: 30,
        },
        {
          id: 6,
          label: 'Obese Class II',
          description: '(Severely Obese)',
          weight: 35,
        },
        {
          id: 7,
          label: 'Obese Class III',
          description: '(Very Severely Obese)',
          weight: 40,
        },
      ],
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.refreshGraphAndList();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  refreshGraphAndList = (
    currentDate = moment(),
    period = this.state.period,
  ) => {
    this.setState(
      {
        isLoading: true,
        page: 1,
        currentDate,
        bmiGraphData: [],
        bmiListData: [],
        period,
      },
      () => {
        this.getBMIHistoryData();
      },
    );
  };

  getBMIHistoryData = async () => {
    try {
      await this.getBMIGraphHistory();
      await this.getBMIHistory();
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Unable to fetch BMI history. Please try again later.',
      );
    } finally {
      this.setState({ isLoading: false, refreshing: false });
    }
  };

  getBMIGraphHistory = async () => {
    const { period, currentDate } = this.state;
    const userId = this.props?.selectedClient?.id;
    try {
      const response = await nasm.api.getBMIGraphAssessment(
        userId,
        currentDate.format(),
        period,
      );
      if (response.length) {
        this.setGraphData(response);
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  setGraphData = (response) => {
    const bmiGraphData = [];
    response.forEach((obj) => {
      const { bmi, created_at } = obj;

      const bmiObj = {
        x: created_at,
        y: Number(bmi),
      };

      if (this.state.period === periods.YEAR) {
        bmiGraphData.push(bmiObj);
      } else {
        const getIndex = bmiGraphData.findIndex((ele) => ele.x === created_at);
        if (getIndex === -1) {
          bmiGraphData.push(bmiObj);
        }
      }
    });

    this.setState({ bmiGraphData });
  };

  getBMIHistory = async () => {
    const {
      period, currentDate, page = 1, bmiListData,
    } = this.state;
    const userId = this.props?.selectedClient?.id;
    try {
      const response = await nasm.api.getBMIHistoryAssessment(
        userId,
        currentDate.format(),
        period,
        page,
        20,
      );
      this.setState({
        bmiListData: page === 1 ? response : [...bmiListData, ...response],
        hasNextPage: response && response.length > 0,
        page: page + 1,
      });
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  onScroll = () => {
    if (this.state.hasNextPage && !this.state.isLoading) {
      this.setState({ isLoading: true }, () => {
        this.getBMIHistoryData();
      });
    }
  };

  leftArrowClicked = () => {
    const { currentDate } = this.state;
    const newDate = currentDate.clone();
    if (this.state.period === periods.YEAR) {
      newDate.subtract(1, 'years');
    } else if (this.state.period === periods.MONTH) {
      newDate.subtract(1, 'months');
    }
    this.refreshGraphAndList(newDate);
  };

  rightArrowClicked = () => {
    const { currentDate } = this.state;
    const newDate = currentDate.clone();
    if (this.state.period === periods.YEAR) {
      newDate.add(1, 'years');
    } else if (this.state.period === periods.MONTH) {
      newDate.add(1, 'months');
    }
    this.refreshGraphAndList(newDate);
  };

  getCalendarHeaderText = () => {
    const { period, currentDate } = this.state;
    const endDate = currentDate.clone();
    const dateFormat = 'D MMMM YYYY';
    const yearDateFormat = 'YYYY';
    let headerDate = '';
    const start = endDate.startOf('month').format(dateFormat);
    const end = endDate.endOf('month').format(dateFormat);
    headerDate = `${start} - ${end}`;
    if (period === periods.YEAR) {
      headerDate = endDate.startOf('year').format(yearDateFormat);
    }
    return headerDate;
  };

  getXAxisData = () => {
    const axesData = [];
    switch (this.state.period) {
      case periods.YEAR:
        for (let i = 1; i < 12 + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      default:
        for (let i = 1; i < this.state.currentDate.daysInMonth() + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
    }
    return axesData;
  };

  renderPeriodPicker = () => (
    <View style={styles.periodPickerContainer}>
      <View style={styles.separatorView} />
      {this.renderPeriodView({ label: 'Month', period: periods.MONTH })}
      <View style={styles.separatorView} />
      {this.renderPeriodView({ label: 'Year', period: periods.YEAR })}
    </View>
  );

  renderPeriodView = ({ label, period }) => (
    <>
      <TouchableOpacity
        activeOpacity={0.6}
        style={
          this.state.period === period
            ? styles.selectedContainer
            : styles.unselectedContainer
        }
        onPress={() => {
          if (period !== this.state.period) {
            this.refreshGraphAndList(moment(), period);
          }
        }}
      >
        <ScaledText
          style={
            this.state.period === period
              ? styles.selectedPeriod
              : styles.unselectedPeriod
          }
        >
          {label}
        </ScaledText>
      </TouchableOpacity>
    </>
  );

  renderGraphHeader = () => {
    const headerText = this.getCalendarHeaderText();
    return (
      <View style={styles.graphHeaderSection}>
        <TouchableOpacity onPress={this.leftArrowClicked}>
          <Image
            style={[styles.imageStyle, { transform: [{ rotate: '180deg' }] }]}
            source={rightArrow}
          />
        </TouchableOpacity>
        <ScaledText style={styles.graphHeaderText}>{headerText}</ScaledText>
        <TouchableOpacity onPress={this.rightArrowClicked}>
          <Image style={styles.imageStyle} source={rightArrow} />
        </TouchableOpacity>
      </View>
    );
  };

  renderGraph = () => {
    const { bmiGraphData } = this.state;
    const xAxisData = this.getXAxisData();
    return (
      <VictoryChart
        height={curvedScale(200)}
        width={Dimensions.get('window').width / 1.1}
        domainPadding={10}
        padding={{
          top: curvedScale(30),
          left: curvedScale(5),
          right: curvedScale(5),
          bottom: curvedScale(5),
        }}
      >
        <VictoryAxis
          key={0}
          style={{
            axis: { stroke: '' },
            grid: { stroke: colors.silver },
            tickLabels: {
              fill: colors.subGrey,
              fontSize: curvedScale(
                this.state.period === periods.MONTH ? 8 : 11,
              ),
              fontFamily: 'Avenir-Medium',
              padding: curvedScale(5),
            },
          }}
          orientation="top"
          tickValues={xAxisData}
        />
        <VictoryAxis
          key={1}
          style={{
            grid: {
              stroke: colors.subGrey,
              strokeWidth: 0,
            },
            axis: {
              strokeWidth: 0,
            },
            tickLabels: {
              fill: colors.subGrey,
              fontSize: curvedScale(11),
              fontFamily: 'Avenir-Medium',
              padding: curvedScale(10),
            },
          }}
          tickValues={[1, 2, 3, 4, 5]}
        />
        {this.renderGraphLine(bmiGraphData, colors.rustyRed)}
        {this.renderGraphPoints(bmiGraphData, colors.rustyRed, 'circle')}
      </VictoryChart>
    );
  };

  renderGraphLegend = () => {
    const underTextStyle = [
      styles.underText,
      { fontSize: 16, paddingLeft: 10 },
    ];
    const legendDotStyle = {
      width: curvedScale(10),
      height: curvedScale(10),
      borderRadius: curvedScale(5),
    };
    return (
      <View style={styles.graphViewStyle}>
        <View style={styles.dotAndLabel}>
          <View
            style={[legendDotStyle, { backgroundColor: colors.rustyRed }]}
          />
          <ScaledText style={underTextStyle}>BMI</ScaledText>
        </View>
      </View>
    );
  };

  renderGraphLine = (data, color) => {
    if (data && data.length > 1) {
      return (
        <VictoryLine
          style={{
            data: { stroke: color, strokeWidth: 1 },
          }}
          data={data}
          interpolation="catmullRom"
        />
      );
    }
    return null;
  };

  renderGraphPoints = (data, color, symbol) => {
    if (data) {
      return (
        <VictoryScatter
          style={{ data: { fill: color } }}
          size={curvedScale(4)}
          symbol={symbol}
          data={data}
        />
      );
    }
    return null;
  };

  renderCategory = (bmiSore) => {
    const { BMI_CATEGORIES } = this.state;
    const bmi_cat = BMI_CATEGORIES.reduce((prev, curr) => (Number(bmiSore).toFixed(2) < curr.weight ? prev : curr));
    const index = BMI_CATEGORIES.findIndex((cat) => cat.id === bmi_cat.id);
    const scoreCategory = BMI_CATEGORIES[index];
    return scoreCategory;
  };

  renderResults = (item) => <BMIResultTable bmiData={item.bmi} />;

  renderItem = ({ item }) => {
    const bmiLevel = this.renderCategory(item.bmi);
    return (
      <TouchableOpacity
        style={styles.assessmentCell}
        onPress={() => {
          this.props.navigation.navigate({
            name: 'BMIResultTable',
            params: {
              bmiData: {
                bmiScore: item.bmi,
              },
              comingFrom: BMIConstants.BMIHistory,
            },
            merge: true,
          });
        }}
      >
        <View style={styles.rowContainer}>
          <ScaledText style={styles.date}>
            {moment(item.created_at).format('M/D/YYYY')}
          </ScaledText>
        </View>
        <View style={styles.rowContainer}>
          <View style={styles.bmiTitleView}>
            <View style={styles.centerStyle}>
              <View style={styles.headerStatusColorView} />
            </View>
            <View style={styles.centerStyle}>
              <ScaledText style={styles.BMItitle}>
                {`${bmiLevel.label}`}
              </ScaledText>
            </View>
          </View>
        </View>
        <View style={styles.rowContainer}>
          <View style={styles.centerStyle}>
            <Image source={imgBMIResult} style={styles.bmiIconStyle} />
          </View>
          <ScaledText>
            <ScaledText style={styles.BMIValue}>
              {`${Number(item.bmi).toFixed(1)}`}
            </ScaledText>
            <ScaledText style={styles.BMItext}> BMI</ScaledText>
          </ScaledText>
        </View>
      </TouchableOpacity>
    );
  };

  renderLoader = (isLoading, bmiListData) => (
    <View style={styles.loaderView}>
      <ActivityIndicator
        size="large"
        animating={isLoading && bmiListData.length > 0}
      />
    </View>
  );

  render() {
    const { bmiListData, refreshing, isLoading } = this.state;
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          ref={(ref) => {
            this.flatList = ref;
          }}
          data={bmiListData}
          keyExtractor={(item) => item.date}
          renderItem={this.renderItem}
          onEndReached={this.onScroll}
          onEndReachedThreshold={0.5}
          refreshControl={(
            <RefreshControl
              refreshing={refreshing}
              onRefresh={this.refreshGraphAndList}
            />
          )}
          ListHeaderComponent={(
            <View>
              {this.renderPeriodPicker()}
              {this.renderGraphHeader()}
              <View style={styles.graphSectionContainer}>
                {this.renderGraph()}
              </View>
              {this.renderGraphLegend()}
            </View>
          )}
          ListFooterComponent={() => this.renderLoader(isLoading, bmiListData)}
          ListEmptyComponent={() => (
            <View style={styles.loaderView}>
              {!isLoading && !refreshing ? (
                <View style={styles.emptyDataView}>
                  <ScaledText style={styles.emptyDataMessage}>
                    No history to show
                  </ScaledText>
                </View>
              ) : null}
            </View>
          )}
        />
        <LoadingSpinner
          visible={isLoading && !bmiListData.length}
          size="large"
          backgroundColor="rgba(0, 0, 0, 0.25)"
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  periodPickerContainer: {
    margin: curvedScale(20),
    flexDirection: 'row',
    alignSelf: 'center',
    borderColor: colors.subGrey,
    borderWidth: 1,
    borderRadius: curvedScale(4),
    alignItems: 'center',
  },
  selectedContainer: {
    flex: 1,
    padding: curvedScale(5),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGrey,
  },
  unselectedContainer: {
    flex: 1,
    padding: curvedScale(5),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  selectedPeriod: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    alignSelf: 'center',
    fontSize: curvedScale(13),
  },
  unselectedPeriod: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    alignSelf: 'center',
    fontSize: curvedScale(13),
  },
  graphHeaderSection: {
    width: '100%',
    padding: curvedScale(15),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.silver51,
  },
  imageStyle: {
    tintColor: colors.cloudyBlue,
  },
  graphHeaderText: {
    fontFamily: 'Avenir-Medium',
    color: colors.black,
    alignSelf: 'center',
    fontSize: curvedScale(15),
  },
  graphSectionContainer: {
    width: '100%',
    backgroundColor: colors.pickerBg,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: curvedScale(10),
  },
  separatorView: {
    width: 1,
    height: '100%',
    backgroundColor: colors.subGrey,
  },
  emptyDataView: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: curvedScale(20),
  },
  emptyDataMessage: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: DeviceInfo.isTablet() ? curvedScale(10) : curvedScale(14),
  },
  loaderView: {
    marginVertical: curvedScale(10),
  },
  assessmentCell: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.silver51,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scaleHeight(2),
    height: curvedScale(90),
  },
  date: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(17),
    color: colors.black,
  },
  bmiTitleView: {
    flexDirection: 'row',
  },
  rowContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerStatusColorView: {
    width: curvedScale(5),
    height: curvedScale(5),
    backgroundColor: colors.greenselect,
    borderRadius: curvedScale(2.5),
    alignSelf: 'center',
  },
  centerStyle: {
    alignSelf: 'center',
  },
  bmiIconStyle: {
    width: curvedScale(22),
    height: curvedScale(22),
    marginRight: curvedScale(8),
  },
  BMItitle: {
    color: colors.subGrey,
    fontSize: curvedScale(11),
    marginLeft: curvedScale(6),
    marginRight: curvedScale(6),
    fontWeight: 'bold',
    fontFamily: 'Avenir-Roman',
  },
  BMIValue: {
    color: colors.black,
    fontSize: curvedScale(16),
    fontWeight: 'bold',
    fontFamily: 'Avenir-Roman',
  },
  BMItext: {
    color: colors.black,
    fontSize: curvedScale(13),
    fontFamily: 'Avenir-Medium',
    alignSelf: 'center',
    justifyContent: 'center',
  },
  underText: {
    fontFamily: 'Avenir-Roman',
    color: colors.black,
    fontSize: curvedScale(14),
    paddingRight: curvedScale(4),
  },
  graphViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    width: '100%',
    backgroundColor: colors.pickerBg,
    paddingBottom: curvedScale(8),
  },
  dotAndLabel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: curvedScale(9),
  },
});

BMIHistory.propTypes = propTypes;
BMIHistory.defaultProps = defaultProps;

const mapStateToProps = ({ selectedClient }) => ({
  selectedClient,
});
const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(BMIHistory);
