import React, { Component } from 'react';
import {
  View, Alert, ActivityIndicator, SafeAreaView,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import nasm from '../../../../dataManager/apiConfig';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import BMIResultTable from './BMIResultTable';
import BMIFTU from './BMIFTU';
import FloatingButton from '../../../../components/FloatingButton';
import BMIConstants from './BMIConstants';
import { colors } from '../../../../styles';

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

const defaultProps = {};

class BMICurrent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
      bmiData: null,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', {
        screen_name: 'current_BMICalculator',
      });
      this.getCurrentBMIResults();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  getCurrentBMIResults = async () => {
    try {
      this.setState({ isLoading: true });
      const userId = this.props?.selectedClient?.id;
      const response = await nasm.api.getCurrentBMIResults(userId);
      if (response) {
        this.setState({
          bmiData: {
            bmiScore: response.bmi,
          },
          isLoading: false,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch (error) {
      this.setState({ isLoading: false });
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => {
        this.props.navigation.pop();
      }}
      titleStyle={styles.headerButtonText}
    />
  );

  onPressAdd = () => {
    this.props.navigation.navigate({
      name: BMIConstants.BMICalculator,
      merge: true,
    });
  };

  render() {
    const { isLoading, bmiData } = this.state;
    if (isLoading) {
      return (
        <View style={styles.loadingView}>
          <ActivityIndicator animating size="large" />
        </View>
      );
    }
    return (
      <SafeAreaView style={styles.container}>
        {bmiData ? <BMIResultTable bmiData={bmiData} /> : <BMIFTU />}
        <FloatingButton onPress={this.onPressAdd} />
      </SafeAreaView>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

BMICurrent.propTypes = propTypes;
BMICurrent.defaultProps = defaultProps;

const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(BMICurrent);
