import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Alert,
  Platform,
  Keyboard,
  TextInput,
  Dimensions,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  NativeModules,
} from 'react-native';
import PropTypes from 'prop-types';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import DropDownPicker from '../../../../components/DropDownPicker';
import Button from '../../../../components/Button';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import BMIConstants from './BMIConstants';
import { colors } from '../../../../styles';
import { curvedScale, scaleWidth } from '../../../../util/responsive';
import { removeAllSpecialCharacters } from '../../../../util/validate';
import { isValidData } from './BMICalculations';
import { BMIContextProvider } from '../../../../reducers/BMIContext';
import { useBMIDispatchContext, useBMIStateContext } from './useBMIContext';
import { BMI_ACTION_TYPES, UNIT_TYPE } from '../../../../types/BMItypes';
import { convert_height_to_centimeters_from_inches } from '../../../../util/CalculatorUtils';
import { onPressExternalLink } from '../../../../util/utils';

const { AppKeyboardHandlerMethods } = NativeModules;

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    navigate: PropTypes.func,
    addListener: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

const screenWidth = Dimensions.get('screen').width;
const useVerticalFlex = screenWidth <= 375;

const UNIT_OPTIONS = [
  {
    id: 1,
    label: UNIT_TYPE.US,
  },
  {
    id: 2,
    label: UNIT_TYPE.METRIC,
  },
];

const BMICalculator = ({ navigation }) => {
  const dispatch = useBMIDispatchContext();
  const state = useBMIStateContext();
  const isAndroid = Platform.OS === 'android';
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const onEditValue = (key, value) => {
    dispatch({ type: BMI_ACTION_TYPES.EDIT, payload: { [key]: value } });
  };

  const onChangeUnit = (unitType) => {
    dispatch({ type: BMI_ACTION_TYPES.CHANGE_UNITS, payload: { unitType } });
  };

  const onCalculate = () => {
    dispatch({ type: BMI_ACTION_TYPES.CALCULATE });
  };

  const onReset = () => {
    dispatch({ type: BMI_ACTION_TYPES.RESET });
    navigation.goBack();
  };

  const setAnimation = () => {
    Keyboard.scheduleLayoutAnimation({ duration: 100, easing: 'easeIn' });
  };

  useEffect(() => {
    navigation.setOptions({
      headerLeft: renderHeaderLeft,
      headerTitle: 'Body Mass Index',
      headerTitleStyle: styles.headerTitle,
    });
  });

  useEffect(() => {
    let showSubscription = '';
    let hideSubscription = '';

    const unsubscribeFocus = navigation.addListener('focus', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustResize();
      }
      showSubscription = Keyboard.addListener('keyboardDidShow', () => {
        setAnimation();
        setKeyboardVisible(true);
      });
      hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
        setAnimation();
        setKeyboardVisible(false);
      });
    });

    const unsubscribeBlur = navigation.addListener('blur', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustPan();
      }
    });
    return () => {
      if (unsubscribeFocus) {
        unsubscribeFocus();
        showSubscription.remove();
        hideSubscription.remove();
      }
      if (unsubscribeBlur) {
        unsubscribeBlur();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const unit_weight = state.unitType === UNIT_TYPE.METRIC ? 'kg' : 'lb';
    const unit_height = state.unitType === UNIT_TYPE.METRIC ? 'cm' : 'in';
    const height = state.unitType === UNIT_TYPE.METRIC
      ? state.height_cm
      : Number(state.height_ft) * 12 + Number(state.height_in);
    if (state.weight > 0) {
      navigate(BMIConstants.BMIResultTable, {
        bmiData: {
          unit_weight, // lb or kg
          weight: state.weight,
          unit_height, // in or cm
          height,
          bmiScore: state.bmiScore,
        },
        onReset,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.bmiScore]);

  const renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={onReset}
      title="Cancel"
      titleStyle={styles.headerButtonText}
    />
  );

  const navigate = (name, params) => {
    navigation.navigate({ name, params, merge: true });
  };

  const validateInputs = () => {
    try {
      const {
        weight, height_cm, height_ft, height_in, unitType,
      } = state;
      const height = unitType === UNIT_TYPE.METRIC
        ? height_cm
        : convert_height_to_centimeters_from_inches(
          Number(height_ft) * 12 + Number(height_in),
        );
      if (isValidData(weight, height)) {
        onCalculate();
      }
    } catch (error) {
      Alert.alert('Error', error?.message || error);
    }
  };

  const getAllowedMaxInputLength = (maxLength, value) => {
    let allowedMaxLength = maxLength;
    const stringValue = value?.toString();
    if (stringValue && stringValue.length > 0 && stringValue.includes('.')) {
      const lengthBeforeDecimal = stringValue.split('.')[0].length;
      allowedMaxLength = lengthBeforeDecimal + 3;
    }
    return allowedMaxLength;
  };

  const getTextInputProps = (maxLength, customStyle) => {
    const props = {
      maxLength,
    };
    if (customStyle) {
      props.style = customStyle;
    }
    return props;
  };

  const stripDecimals = (input) => {
    const filteredInput = input?.toString();
    const beforeDecimalStr = filteredInput.substring(
      0,
      filteredInput.indexOf('.'),
    );
    const afterDecimalStr = filteredInput.substring(
      filteredInput.indexOf('.') + 1,
      filteredInput.length,
    );
    const filteredStr = afterDecimalStr.replaceAll('.', '');
    const finalStr = beforeDecimalStr.concat('.').concat(filteredStr);
    return finalStr;
  };

  const stripNonNumericChars = (input, decimalAllowed) => {
    if (decimalAllowed && input?.includes('.')) {
      const finalStr = stripDecimals(input);
      return finalStr.replace(/[^0-9.]/g, '');
    }
    return input.replace(/[^0-9]/g, '');
  };

  const renderPageHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitleBold}>
        Body Mass Index
        <Text style={styles.headerTitleRegular}> (BMI)</Text>
      </Text>
      <Text style={styles.headerDescription}>
        BMI is used by personal trainers, nutrition coaches, physicians, and
        epidemiologists to categorize bodyweight based on a person&apos;s height
        and weight.
      </Text>
      {renderLearnMoreButton()}
    </View>
  );

  const renderLearnMoreButton = () => (
    <TouchableOpacity
      style={styles.learnMoreBtn}
      onPress={() => navigate(BMIConstants.BMILearnMore)}
    >
      <Text style={styles.learnMoreBtnText}>Learn More</Text>
    </TouchableOpacity>
  );

  const renderUnit = () => (
    <View style={styles.unitContainer}>
      <View style={styles.inputContainerView}>
        <DropDownPicker
          useNativeAndroidPickerStyle
          data={UNIT_OPTIONS}
          label="Units:"
          selected={
            UNIT_OPTIONS.find((option) => option.label === state.unitType).id
          }
          onValueChange={(selectedUnit) => {
            onChangeUnit(selectedUnit.label);
          }}
          labelStyle={styles.pickerItem}
          containerStyle={styles.picker}
        />
      </View>
      <View style={[styles.inputContainerView, styles.inputTypeContainerView]}>
        {renderWeight()}
      </View>
    </View>
  );

  const renderTextInput = (
    key,
    unit,
    textInputProps,
    customStyle,
    decimalAllowed,
  ) => {
    let fieldName = key;
    const fieldValue = state[key.toLowerCase()];
    if (key === 'weight') {
      fieldName = 'Weight';
    } else if (unit === 'ft') {
      fieldName = 'Feet';
    } else if (unit === 'in') {
      fieldName = 'Inches';
    } else if (unit === 'cm') {
      fieldName = 'Centimeters';
    }
    const leftMargin = {
      marginLeft: Platform.OS === 'ios' ? curvedScale(5) : 0,
    };
    return (
      <View
        style={[
          styles.inputContainer,
          styles.customInputContainer,
          customStyle,
        ]}
      >
        <View style={styles.inputLabel}>
          <Text style={styles.input}>
            {`${fieldName}:`}
            {' '}
          </Text>
        </View>
        <View style={styles.inputView}>
          <TextInput
            style={[styles.inputField, styles.customTextInput]}
            placeholder="0"
            placeholderTextColor={colors.fillDarkGrey}
            value={`${fieldValue}`}
            onChangeText={(newValue) => {
              const filteredValue = removeAllSpecialCharacters(newValue);
              if (unit === 'in' && filteredValue) {
                if (parseInt(filteredValue, 10) < 12) {
                  onEditValue(
                    `${[key.toLowerCase()]}`,
                    stripNonNumericChars(filteredValue, decimalAllowed),
                  );
                }
              } else {
                onEditValue(
                  `${[key.toLowerCase()]}`,
                  stripNonNumericChars(filteredValue, decimalAllowed),
                );
              }
            }}
            keyboardType="numeric"
            returnKeyType="done"
            contextMenuhidden
            {...textInputProps}
          />
          <Text style={[styles.inputField, leftMargin]}>
            {unit.toLowerCase()}
          </Text>
        </View>
      </View>
    );
  };

  const renderWeight = () => (
    <View style={styles.weightContainer}>
      {renderTextInput(
        'weight',
        state.unitType === UNIT_TYPE.METRIC ? 'kg' : 'lb',
        getTextInputProps(getAllowedMaxInputLength(4, state.weight)),
        { marginLeft: 0 },
        true,
      )}
    </View>
  );

  const renderHeight = () => (
    <View style={styles.heightContainer}>
      <Text style={styles.heightTitle}>Height</Text>
      {state.unitType === UNIT_TYPE.US ? (
        <View style={styles.heightView}>
          <View style={styles.inputContainerView}>
            {renderTextInput('height_ft', 'ft', getTextInputProps(1), {
              marginLeft: 0,
            })}
          </View>

          <View style={styles.inputContainerView}>
            {renderTextInput(
              'height_in',
              'in',
              getTextInputProps(getAllowedMaxInputLength(3, state.height_in)),
              null,
              true,
            )}
          </View>
        </View>
      ) : (
        <View style={styles.heightView}>
          {renderTextInput(
            'height_cm',
            'cm',
            getTextInputProps(getAllowedMaxInputLength(3, state.height_cm)),
            { marginLeft: 0, width: '100%' },
            true,
          )}
        </View>
      )}
    </View>
  );

  const renderCalculateButton = () => (
    <View style={styles.calculateBtnView}>
      <Button
        textStyles={styles.calculateBtnText}
        title="Calculate"
        buttonStyle={[
          styles.createAccountButton,
          { borderRadius: scaleWidth(7.2) },
        ]}
        onPress={validateInputs}
      />
    </View>
  );

  let buttonView = renderCalculateButton();
  if (isAndroid && keyboardVisible) {
    buttonView = null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {renderPageHeader()}
        <View style={styles.pageView}>
          {renderUnit()}
          {renderHeight()}
        </View>
      </KeyboardAwareScrollView>
      {buttonView}
      <View style={styles.bottomLinkContainer}>
        <TouchableOpacity
          onPress={() => onPressExternalLink(
            'https://www.nhlbi.nih.gov/health/educational/lose_wt/BMI/bmi-m.htm',
          )}
        >
          <Text style={styles.bottomLink}>
            Based on the NIH Body Mass Index Calculator
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => onPressExternalLink(
            'https://www.nhlbi.nih.gov/health/educational/lose_wt/bmitools.htm',
          )}
        >
          <Text style={styles.bottomLink}>
            Based on the NIH Body Mass Index Tools
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => onPressExternalLink('https://www.nasm.org/products/CPT7104001')}
        >
          <Text style={styles.bottomLink}>
            Based on the NASM CPT 7th Edition Textbook
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
  headerButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  scrollView: {
    height: '90%',
  },
  headerContainer: {
    paddingHorizontal: curvedScale(20),
    paddingTop: curvedScale(40),
    paddingBottom: curvedScale(20),
    backgroundColor: colors.offWhite,
  },
  headerTitleBold: {
    color: colors.black,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(24),
    fontWeight: '700',
  },
  headerTitleRegular: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(24),
    fontWeight: '400',
  },
  headerDescription: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
    marginTop: curvedScale(20),
  },
  learnMoreBtn: {
    width: '40%',
    alignItems: 'center',
    borderRadius: 18.5,
    borderWidth: 1,
    borderColor: colors.disclaimerGrey,
    paddingHorizontal: curvedScale(16),
    paddingVertical: curvedScale(8),
    marginTop: curvedScale(20),
  },
  learnMoreBtnText: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(12),
    fontWeight: '500',
  },
  pageView: {
    padding: curvedScale(20),
    backgroundColor: colors.white,
  },
  inputContainerView: {
    width: useVerticalFlex ? '100%' : '50%',
  },
  inputTypeContainerView: {
    marginLeft: useVerticalFlex ? 0 : curvedScale(10),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
    paddingRight: useVerticalFlex ? 0 : curvedScale(10),
  },
  pickerItem: {
    color: colors.black,
    fontSize: 14,
    fontFamily: 'Avenir-Light',
    fontWeight: '500',
    paddingHorizontal: curvedScale(8),
    textAlign: 'left',
  },
  picker: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    height: curvedScale(40),
    marginVertical: 0,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    padding: curvedScale(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(40),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
  },
  customInputContainer: {
    marginLeft: useVerticalFlex ? 0 : curvedScale(10),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
  },
  inputLabel: {
    width: '40%',
  },
  input: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
    fontWeight: '500',
  },
  inputView: {
    flexDirection: 'row',
    width: '60%',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  inputField: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
    fontWeight: '500',
  },
  customTextInput: {
    color: colors.black,
    width: '90%',
    textAlign: 'right',
    paddingVertical: 0,
  },
  unitContainer: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  weightContainer: {
    marginTop: 0,
  },
  heightContainer: {
    marginTop: curvedScale(10),
  },
  heightView: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    flex: 1,
    alignItems: 'center',
    marginTop: curvedScale(10),
  },
  heightTitle: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
    marginTop: curvedScale(10),
  },
  calculateBtnView: {
    margin: curvedScale(20),
    height: '10%',
  },
  calculateBtnText: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
    fontWeight: '700',
  },
  bottomLinkContainer: {
    marginBottom: curvedScale(20),
  },
  bottomLink: {
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Medium',
    color: colors.fillDarkGrey,
    alignSelf: 'center',
    textDecorationLine: 'underline',
    lineHeight: 20,
  },
});

BMICalculator.propTypes = propTypes;
BMICalculator.defaultProps = defaultProps;

const BMIRoot = ({ navigation }) => (
  <BMIContextProvider>
    <BMICalculator navigation={navigation} />
  </BMIContextProvider>
);

BMIRoot.propTypes = propTypes;

export default BMIRoot;
