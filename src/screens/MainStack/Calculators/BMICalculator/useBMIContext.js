import { useContext } from 'react';
import {
  BMIDispatchContext,
  BMIStateContext,
} from '../../../../reducers/BMIContext';

const useBMIStateContext = () => {
  const state = useContext(BMIStateContext);
  if (state === undefined) {
    throw new Error('must be used within a Provider Component');
  }

  return state;
};

const useBMIDispatchContext = () => {
  const dispatch = useContext(BMIDispatchContext);
  if (dispatch === undefined) {
    throw new Error('must be used within a Provider Component');
  }

  return dispatch;
};

export { useBMIStateContext, useBMIDispatchContext };
