/* eslint-disable react/sort-comp */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  Text,
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import IconFeader from 'react-native-vector-icons/Feather';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import OneRMConstants from './OneRMCalculator/OneRMConstants';
import BWPConstants from './BWPCalculator/BWPConstants';
import BMIConstants from './BMICalculator/BMIConstants';
import MHRConstants from './MHRCalculator/MHRConstants';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';
import { CALCULATOR_CONTEXTS } from '../../../reducers/calculatorContextReducer';
import { BodyFatConstants } from './BodyFatCalculator/BodyFatConstants';

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      isBWPEnabled: PropTypes.bool,
    }),
  }).isRequired,
  calculatorContext: PropTypes.shape({
    type: PropTypes.string,
  }),
};

const defaultProps = {
  calculatorContext: {
    type: CALCULATOR_CONTEXTS.TRAINER_ACCOUNT,
  },
};

class Calculators extends Component {
  constructor(props) {
    super(props);
    this.state = {
      calculatorContext: props.calculatorContext?.type,
    };
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerTitle: 'Calculators',
      headerTitleStyle: styles.headerTitle,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => {
        this.props.navigation.goBack();
      }}
      titleStyle={styles.headerLeftButtonText}
    />
  );

  navigate = (screen, params) => {
    this.props.navigation.navigate({
      name: screen,
      params,
      merge: true,
    });
  };

  open1RMCalculator = () => {
    const { calculatorContext } = this.state;
    let screenName = OneRMConstants.OneRMTabView;
    if (calculatorContext === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      screenName = OneRMConstants.OneRMCalculator;
    }
    this.navigate(screenName);
  };

  openBWPCalculator = () => {
    const { calculatorContext } = this.state;
    let screenName = BWPConstants.BWPTabView;
    if (calculatorContext === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      screenName = BWPConstants.BMR;
    }
    this.navigate(screenName);
  };

  openBMICalculator = () => {
    const { calculatorContext } = this.state;
    let screenName = BMIConstants.BMICalculator;
    if (calculatorContext !== CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      screenName = BMIConstants.BMITabView;
    }
    this.navigate(screenName);
  };

  openMHRCalculator = () => {
    const { calculatorContext } = this.state;
    let screenName = MHRConstants.MHRCalculator;
    if (calculatorContext !== CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      screenName = MHRConstants.MHRTabView;
    }
    this.navigate(screenName);
  };

  openBodyFatCalculator = () => {
    const { calculatorContext } = this.state;
    let screenName = BodyFatConstants.BodyFatTabView;
    if (calculatorContext === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      screenName = BodyFatConstants.BodyFatCalculator;
    }
    this.navigate(screenName);
  };

  renderItem = (title, subTitle, onPress) => (
    <TouchableOpacity style={styles.item} onPress={onPress}>
      <View style={styles.leftBox}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subTitle}>{subTitle}</Text>
      </View>
      <View>
        <IconFeader name="chevron-right" size={30} color={colors.subGrey} />
      </View>
    </TouchableOpacity>
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        {this.renderItem(
          'One Rep Max (1RM)',
          'Max lift for a single repetition',
          this.open1RMCalculator,
        )}
        {!this.props.trainerActiveProfile?.ClubId
          ? this.renderItem(
            'Body Weight Planner (BWP)',
            'Calculates macronutrient intake',
            this.openBWPCalculator,
          )
          : null}
        {this.renderItem(
          'Body Mass Index (BMI)',
          'Calculate body fat based on height and weight',
          this.openBMICalculator,
        )}
        {this.renderItem(
          'Max Heart Rate (MHR)',
          'Estimates the intensity of your training',
          this.openMHRCalculator,
        )}
        {this.renderItem(
          'Body Fat',
          'Estimate overall body fatness',
          this.openBodyFatCalculator,
        )}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    textAlign: 'center',
  },
  headerLeftButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    padding: curvedScale(20),
  },
  title: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
    fontWeight: '700',
  },
  subTitle: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(13),
  },
  leftBox: {
    width: '95%',
  },
});

Calculators.propTypes = propTypes;
Calculators.defaultProps = defaultProps;

const mapStateToProps = ({ calculatorContext, trainerActiveProfile }) => ({
  calculatorContext,
  trainerActiveProfile,
});
const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(Calculators);
