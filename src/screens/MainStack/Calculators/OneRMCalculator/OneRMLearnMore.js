import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
  Platform,
  UIManager,
  StatusBar,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  LayoutAnimation,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';

const closeImg = require('../../../../assets/closeCircle.png');

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

const OneRMLearnMore = ({ navigation }) => {
  useEffect(() => {
    StatusBar.setBarStyle('dark-content');
    setTimeout(() => {
      setCustomNavigationHeader();
    }, 300);
    return () => {
      StatusBar.setBarStyle('light-content');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setAnimation = () => {
    if (Platform.OS === 'android') {
      if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
      }
    }
    LayoutAnimation.configureNext({
      duration: 500,
      create: { type: 'linear', property: 'opacity' },
      update: { type: 'spring', springDamping: 0.5 },
    });
  };

  const setCustomNavigationHeader = () => {
    setAnimation();
    navigation.setOptions({
      headerStyle: { backgroundColor: colors.white, height: 100 },
      headerLeft: renderCloseImg,
    });
  };

  const goBack = () => {
    navigation.goBack();
  };

  const renderCloseImg = () => (
    <TouchableOpacity
      activeOpacity={0.6}
      hitSlop={{
        top: 20,
        bottom: 20,
        left: 20,
        right: 20,
      }}
      onPress={goBack}
      style={styles.closeImgView}
    >
      <Image source={closeImg} />
    </TouchableOpacity>
  );

  const renderPageContent = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <Text style={styles.title}>One Rep Max Calculator (1RM)</Text>
      <View style={styles.descriptionView}>
        <Text style={styles.description}>
          Calculate your client&apos;s one rep max (1RM) for any lift. The
          one-rep max is the max weight your client can lift for a single
          repetition for a given exercise. To lift safely, a one rep max
          calculator is essential for determining what they can handle on the
          squat rack, bench, deadlift and more.
          {'\n\n'}
          A one rep max is also a huge factor when training in the
          Maximal Strength phase of the
          {' '}
          <Text style={[styles.description, styles.underlinedText]}>
            NASM OPT™ Model
          </Text>
          {' - '}
          as the weight ranges will sit in the 85-100% range of their 1RM. Being
          able to gauge the correct weight to lift will help keep their workouts
          intense enough to develop strength.
          {'\n\n'}
          Once you input the totals of your client&apos;s lifts, you will see a
          table of ranges that will determine where they are regarding their max
          lifts. Remember to calculate their 1RM for every specific lift. You
          don&apos;t want to assume that your client&apos;s bench press max will
          carry over to their back squat max.
        </Text>
      </View>
    </ScrollView>
  );

  const renderBottomDisclaimer = () => (
    <View style={styles.disclaimer}>
      <Text style={styles.disclaimerText}>
        Remember, the values are only estimates. Be sure to consult a personal
        trainer - or become one yourself - to ensure you are lifting safely.
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderPageContent()}
      {renderBottomDisclaimer()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  closeImgView: {
    marginHorizontal: curvedScale(20),
    width: 30,
    height: 30,
  },
  content: {
    margin: curvedScale(20),
    height: '70%',
    backgroundColor: colors.white,
  },
  title: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    fontWeight: '700',
    textAlign: 'left',
  },
  descriptionView: {
    marginTop: curvedScale(20),
  },
  description: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(15),
    textAlign: 'left',
  },
  underlinedText: {
    textDecorationLine: 'underline',
  },
  disclaimer: {
    maxHeight: '30%',
    padding: curvedScale(20),
    margin: curvedScale(20),
    borderRadius: 4,
    backgroundColor: colors.disclaimerGrey,
  },
  disclaimerText: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
  },
});

OneRMLearnMore.propTypes = propTypes;
OneRMLearnMore.defaultProps = defaultProps;

export default OneRMLearnMore;
