import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Alert,
  Platform,
  Keyboard,
  TextInput,
  Dimensions,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  NativeModules,
} from 'react-native';
import PropTypes from 'prop-types';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  LIFT_TYPE,
  ONE_RM_ACTION_TYPES,
  UNIT_TYPE,
} from '../../../../types/RMtypes';
import DropDownPicker from '../../../../components/DropDownPicker';
import Button from '../../../../components/Button';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import OneRMConstants from './OneRMConstants';
import { colors } from '../../../../styles';
import { curvedScale, scaleWidth } from '../../../../util/responsive';
import { removeAllSpecialCharacters } from '../../../../util/validate';
import { isValidData } from './OneRMCalculations';
import {
  useOneRmDispatchContext,
  useOneRmStateContext,
} from './useOneRMContext';
import { OneRmContextProvider } from '../../../../reducers/OneRMContext';
import { onPressExternalLink } from '../../../../util/utils';

const { AppKeyboardHandlerMethods } = NativeModules;
const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    navigate: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

const screenWidth = Dimensions.get('screen').width;
const useVerticalFlex = screenWidth <= 375;

const UNIT_OPTIONS = [
  {
    id: 1,
    label: UNIT_TYPE.US,
  },
  {
    id: 2,
    label: UNIT_TYPE.METRIC,
  },
];

const LIFT_TYPE_OPTIONS = [
  {
    id: 1,
    label: LIFT_TYPE.NOT_SPECIFIED,
  },
  {
    id: 2,
    label: LIFT_TYPE.DEADLIFT,
  },
  {
    id: 3,
    label: LIFT_TYPE.BENCH_PRESS,
  },
  {
    id: 4,
    label: LIFT_TYPE.SQUAT,
  },
];

const OneRMCalculator = ({ navigation }) => {
  const dispatch = useOneRmDispatchContext();
  const state = useOneRmStateContext();
  const isAndroid = Platform.OS === 'android';
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const onEditValue = (key, value) => {
    dispatch({ type: ONE_RM_ACTION_TYPES.EDIT, payload: { [key]: value } });
  };

  const onChangeUnit = (unitType) => {
    dispatch({ type: ONE_RM_ACTION_TYPES.CHANGE_UNITS, payload: { unitType } });
  };

  const onCalculate = () => {
    dispatch({ type: ONE_RM_ACTION_TYPES.CALCULATE });
  };

  const onReset = () => {
    dispatch({ type: ONE_RM_ACTION_TYPES.RESET });
    navigation.goBack();
  };

  const setAnimation = () => {
    Keyboard.scheduleLayoutAnimation({ duration: 100, easing: 'easeIn' });
  };

  useEffect(() => {
    navigation.setOptions({
      headerLeft: renderHeaderLeft,
      headerTitle: '1RM Calculator',
      headerTitleStyle: styles.headerTitle,
    });
  });

  useEffect(() => {
    let showSubscription = '';
    let hideSubscription = '';

    const unsubscribeFocus = navigation.addListener('focus', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustResize();
      }
      showSubscription = Keyboard.addListener('keyboardDidShow', () => {
        setAnimation();
        setKeyboardVisible(true);
      });
      hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
        setAnimation();
        setKeyboardVisible(false);
      });
    });

    const unsubscribeBlur = navigation.addListener('blur', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustPan();
      }
    });
    return () => {
      if (unsubscribeFocus) {
        unsubscribeFocus();
        showSubscription.remove();
        hideSubscription.remove();
      }
      if (unsubscribeBlur) {
        unsubscribeBlur();
      }
    };
  }, []);

  useEffect(() => {
    if (state.maxWeight > 0) {
      navigate(OneRMConstants.OneRMResultTable, {
        rmData: {
          maxLiftWeight: state.maxWeight,
          units: state.unitType === UNIT_TYPE.METRIC ? 'kg' : 'lb',
          type: state.liftType,
          reps: state.reps,
          weight: state.weight,
        },
        onReset,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.maxWeight]);

  const renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={onReset}
      title="Cancel"
      titleStyle={styles.headerButtonText}
    />
  );

  const navigate = (name, params) => {
    navigation.navigate({ name, params, merge: true });
  };

  const validateInputs = () => {
    try {
      if (isValidData(state.weight, state.reps)) {
        onCalculate();
      }
    } catch (error) {
      Alert.alert('Error', error?.message || error);
    }
  };

  const getAllowedMaxInputLength = (maxLength, value) => {
    let allowedMaxLength = maxLength;
    const stringValue = value?.toString();
    if (stringValue && stringValue.length > 0 && stringValue.includes('.')) {
      const lengthBeforeDecimal = stringValue.split('.')[0].length;
      allowedMaxLength = lengthBeforeDecimal + 3;
    }
    return allowedMaxLength;
  };

  const getTextInputProps = (maxLength, customStyle) => {
    const props = {
      maxLength,
    };
    if (customStyle) {
      props.style = customStyle;
    }
    return props;
  };

  const stripDecimals = (input) => {
    const filteredInput = input?.toString();
    const beforeDecimalStr = filteredInput.substring(
      0,
      filteredInput.indexOf('.'),
    );
    const afterDecimalStr = filteredInput.substring(
      filteredInput.indexOf('.') + 1,
      filteredInput.length,
    );
    const filteredStr = afterDecimalStr.replaceAll('.', '');
    const finalStr = beforeDecimalStr.concat('.').concat(filteredStr);
    return finalStr;
  };

  const stripNonNumericChars = (input, decimalAllowed) => {
    if (decimalAllowed && input?.includes('.')) {
      const finalStr = stripDecimals(input);
      return finalStr.replace(/[^0-9.]/g, '');
    }
    return input.replace(/[^0-9]/g, '');
  };

  const renderPageHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitleBold}>
        One Rep Max Calculator
        <Text style={styles.headerTitleRegular}> (1RM)</Text>
      </Text>
      <Text style={styles.headerDescription}>
        The lower the number of reps you enter in, the more accurate the 1RM
        will be. In other words, a three-rep max (3RM) will give a better
        estimate than a ten-rep max (10RM).
      </Text>
      {renderLearnMoreButton()}
    </View>
  );

  const renderLearnMoreButton = () => (
    <TouchableOpacity
      style={styles.learnMoreBtn}
      onPress={() => navigate(OneRMConstants.OneRMLearnMore)}
    >
      <Text style={styles.learnMoreBtnText}>Learn More</Text>
    </TouchableOpacity>
  );

  const renderUnit = () => (
    <View style={styles.unitContainer}>
      <View style={styles.inputContainerView}>
        <DropDownPicker
          testID="UnitPicker"
          useNativeAndroidPickerStyle
          data={UNIT_OPTIONS}
          label="Units:"
          selected={
            UNIT_OPTIONS.find((option) => option.label === state.unitType).id
          }
          onValueChange={(selectedUnit) => {
            onChangeUnit(selectedUnit.label);
          }}
          labelStyle={styles.pickerItem}
          containerStyle={styles.picker}
        />
      </View>
      <View style={[styles.inputContainerView, styles.inputTypeContainerView]}>
        <DropDownPicker
          testID="TypePicker"
          useNativeAndroidPickerStyle
          data={LIFT_TYPE_OPTIONS}
          label="Type"
          selected={
            LIFT_TYPE_OPTIONS.find((option) => option.label === state.liftType).id
          }
          onValueChange={(selectedType) => {
            onEditValue('liftType', selectedType.label);
          }}
          labelStyle={styles.pickerItem}
          containerStyle={styles.picker}
        />
      </View>
    </View>
  );

  const renderTextInput = (
    key,
    unit,
    textInputProps,
    customStyle,
    decimalAllowed,
    testID,
  ) => {
    let fieldName = key;
    const fieldValue = state[key.toLowerCase()];
    if (key === 'weight') {
      fieldName = 'Weight';
    } else if (key === 'reps') {
      fieldName = 'Repetitions';
    }
    const leftMargin = {
      marginLeft: Platform.OS === 'ios' ? curvedScale(5) : 0,
    };
    return (
      <View
        style={[
          styles.inputContainer,
          styles.customInputContainer,
          customStyle,
        ]}
      >
        <View style={styles.inputLabel}>
          <Text style={styles.input}>
            {`${fieldName}:`}
            {' '}
          </Text>
        </View>
        <View style={styles.inputView}>
          <TextInput
            testID={testID}
            style={[styles.inputField, styles.customTextInput]}
            placeholder="0"
            placeholderTextColor={colors.fillDarkGrey}
            value={`${fieldValue}`}
            onChangeText={(newValue) => {
              const filteredValue = removeAllSpecialCharacters(newValue);
              onEditValue(
                `${[key.toLowerCase()]}`,
                stripNonNumericChars(filteredValue, decimalAllowed),
              );
            }}
            keyboardType="numeric"
            returnKeyType="done"
            contextMenuhidden
            {...textInputProps}
          />
          <Text style={[styles.inputField, leftMargin]}>
            {unit.toLowerCase()}
          </Text>
        </View>
      </View>
    );
  };

  const renderWeight = () => (
    <View style={styles.weightContainer}>
      {renderTextInput(
        'weight',
        state.unitType === UNIT_TYPE.METRIC ? 'kg' : 'lb',
        getTextInputProps(getAllowedMaxInputLength(4, state.weight)),
        { marginLeft: 0 },
        true,
        'WeightInput',
      )}
    </View>
  );

  const renderRepetitions = () => (
    <View style={styles.repetitionsContainer}>
      <Text style={styles.repetitionsTitle}>
        Enter a maximum of 10 repetitions
      </Text>
      {renderTextInput(
        'reps',
        'rep(s)',
        getTextInputProps(2),
        {
          marginLeft: 0,
          marginTop: 10,
        },
        false,
        'RepetationInput',
      )}
    </View>
  );

  const renderCalculateButton = () => (
    <View style={styles.calculateBtnView}>
      <Button
        testID="CalculateButton"
        textStyles={styles.calculateBtnText}
        title="Calculate"
        buttonStyle={[
          styles.createAccountButton,
          { borderRadius: scaleWidth(7.2) },
        ]}
        onPress={validateInputs}
      />
    </View>
  );

  let buttonView = renderCalculateButton();
  if (isAndroid && keyboardVisible) {
    buttonView = null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {renderPageHeader()}
        <View style={styles.pageView}>
          {renderUnit()}
          {renderWeight()}
          {renderRepetitions()}
        </View>
      </KeyboardAwareScrollView>
      {buttonView}
      <View style={styles.bottomLinkContainer}>
        <TouchableOpacity
          onPress={() => onPressExternalLink(
            'https://www.ncbi.nlm.nih.gov/pmc/articles/PMC3525823/',
          )}
        >
          <Text style={styles.bottomLink}>
            Based on the NIH 1-Repetition Maximum Estimation
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
  headerButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  scrollView: {
    height: '90%',
  },
  headerContainer: {
    paddingHorizontal: curvedScale(20),
    paddingTop: curvedScale(40),
    paddingBottom: curvedScale(20),
    backgroundColor: colors.offWhite,
  },
  headerTitleBold: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    fontWeight: '700',
  },
  headerTitleRegular: {
    color: colors.black,
    fontFamily: 'Avenir',
    fontSize: curvedScale(22),
    fontWeight: '400',
  },
  headerDescription: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
    marginTop: curvedScale(20),
  },
  learnMoreBtn: {
    width: '40%',
    alignItems: 'center',
    borderRadius: 18.5,
    borderWidth: 1,
    borderColor: colors.disclaimerGrey,
    paddingHorizontal: curvedScale(16),
    paddingVertical: curvedScale(8),
    marginTop: curvedScale(20),
  },
  learnMoreBtnText: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(12),
    fontWeight: '500',
  },
  pageView: {
    padding: curvedScale(20),
    backgroundColor: colors.white,
  },
  inputContainerView: {
    width: useVerticalFlex ? '100%' : '50%',
  },
  inputTypeContainerView: {
    marginLeft: useVerticalFlex ? 0 : curvedScale(10),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
    paddingRight: useVerticalFlex ? 0 : curvedScale(10),
  },
  pickerItem: {
    color: colors.black,
    fontSize: 14,
    fontFamily: 'Avenir-Light',
    fontWeight: '500',
    paddingHorizontal: curvedScale(8),
    textAlign: 'left',
  },
  picker: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    height: curvedScale(40),
    marginVertical: 0,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    padding: curvedScale(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(40),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
  },
  customInputContainer: {
    marginLeft: useVerticalFlex ? 0 : curvedScale(10),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
  },
  inputLabel: {
    width: '40%',
  },
  input: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
    fontWeight: '500',
  },
  inputView: {
    flexDirection: 'row',
    width: '60%',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  inputField: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
    fontWeight: '500',
  },
  customTextInput: {
    color: colors.black,
    width: '90%',
    textAlign: 'right',
    paddingVertical: 0,
  },
  unitContainer: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  weightContainer: {
    marginTop: curvedScale(20),
  },
  repetitionsContainer: {
    marginTop: curvedScale(10),
  },
  repetitionsTitle: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
    marginTop: curvedScale(10),
  },
  calculateBtnView: {
    margin: curvedScale(20),
    height: '10%',
  },
  calculateBtnText: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
    fontWeight: '700',
  },
  bottomLinkContainer: {
    marginVertical: curvedScale(20),
  },
  bottomLink: {
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Medium',
    color: colors.fillDarkGrey,
    alignSelf: 'center',
    textDecorationLine: 'underline',
    lineHeight: 20,
  },
});

OneRMCalculator.propTypes = propTypes;
OneRMCalculator.defaultProps = defaultProps;

const OneRmRoot = ({ navigation }) => (
  <OneRmContextProvider>
    <OneRMCalculator navigation={navigation} />
  </OneRmContextProvider>
);

OneRmRoot.propTypes = propTypes;

export default OneRmRoot;
