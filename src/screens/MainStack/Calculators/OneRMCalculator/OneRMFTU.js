import React, { Component } from 'react';
import {
  Text, Image, View, StyleSheet, SafeAreaView,
} from 'react-native';
import PropTypes from 'prop-types';
import OneRMConstants from './OneRMConstants';
import { HeaderLeftButton } from '../../../../components';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';

const img1RMFTUE = require('../../../../assets/1rm.png');

const propTypes = {
  navigation: PropTypes.object.isRequired,
};

const defaultProps = {};

class OneRMFTU extends Component {
  componentDidMount() {
    this.props?.navigation?.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerTitle: '1RM Calculator',
      headerTitleStyle: styles.headerTitle,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  onPressAdd = () => {
    this.props.navigation.navigate({
      name: OneRMConstants.OneRMCalculator,
      merge: true,
    });
  };

  renderEmptyState = () => (
    <View style={styles.emptyStateContainer}>
      <View style={styles.emptyIconStyle}>
        <Image source={img1RMFTUE} />
      </View>
      <Text style={styles.emptyStateHeaderText}>
        {'Create your client’s first\n1RM calculation'}
      </Text>
      <Text style={styles.emptyStateBodyText}>
        {
          'One-rep max is the max weight your client can lift\nfor a single repetition for a given exercise.'
        }
      </Text>
    </View>
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        {this.renderEmptyState()}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
  headerButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyIconStyle: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateHeaderText: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    textAlign: 'center',
    marginTop: curvedScale(20),
  },
  emptyStateBodyText: {
    color: colors.darkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    textAlign: 'center',
    marginTop: curvedScale(10),
  },
});

OneRMFTU.propTypes = propTypes;
OneRMFTU.defaultProps = defaultProps;

export default OneRMFTU;
