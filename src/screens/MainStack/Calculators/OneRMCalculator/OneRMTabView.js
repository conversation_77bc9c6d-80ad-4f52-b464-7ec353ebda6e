import React, { Component } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { Dimensions } from 'react-native';
import PropTypes from 'prop-types';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import OneRMCurrent from './OneRMCurrent';
import { colors, materialTabBarOptions } from '../../../../styles';
import OneRMHistory from './OneRMHistory';

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
    setParams: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

class OneRMTabView extends Component {
  static router = TabNav.router;

  componentDidMount() {
    this.props.navigation.setOptions({
      headerTitle: 'One Rep Max',
      headerTitleStyle: styles.headerTitle,
      headerLeft: this.renderHeaderLeft,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  updateHeaderRight = (headerRight) => {
    this.props.navigation.setParams({
      renderHeaderRight: headerRight,
    });
  };

  updateHeaderLeft = (headerLeft) => {
    this.props.navigation.setParams({
      renderHeaderLeft: headerLeft,
    });
  };

  render() {
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
        style={materialTabBarOptions.tabBarOptions.tabBarStyle}
      >
        <TabNav.Screen
          name="Current"
          component={OneRMCurrent}
          initialParams={{
            updateHeaderLeft: this.updateHeaderLeft,
          }}
        />
        <TabNav.Screen
          name="History"
          component={OneRMHistory}
          initialParams={{
            updateHeaderLeft: this.updateHeaderLeft,
          }}
        />
      </TabNav.Navigator>
    );
  }
}

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 17,
    fontWeight: '500',
    color: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
};

OneRMTabView.propTypes = propTypes;
OneRMTabView.defaultProps = defaultProps;

export default OneRMTabView;
