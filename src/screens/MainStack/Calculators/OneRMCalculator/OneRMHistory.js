import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  View,
  FlatList,
  RefreshControl,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  Alert,
  Text,
  Image,
  TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import moment from 'moment';
import DeviceInfo from 'react-native-device-info';
import ScaledText from '../../../../components/ScaledText';
import { colors } from '../../../../styles';
import nasm from '../../../../dataManager/apiConfig';
import { curvedScale } from '../../../../util/responsive';

import { navigate } from '../../../../util/NavigatorService';
import OneRMConstants from './OneRMConstants';
import { LIFT_TYPE } from '../../../../types/RMtypes';

const img1RmResult = require('../../../../assets/1rm-result.png');

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

const defaultProps = {};

class OneRMHistory extends Component {
  constructor(props) {
    super(props);
    this.state = {
      oneRMListData: [],
      page: 1,
      isNextPage: true,
      isLoading: false,
      refreshing: true,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.onRefresh();
    });
  }

  onRefresh = () => {
    this.setState(
      {
        refreshing: true,
        page: 1,
      },
      () => {
        this.getOneRMHistory();
      },
    );
  };

  getOneRMHistory = async () => {
    const { page = 1, oneRMListData } = this.state;
    const userId = this.props?.selectedClient?.id;
    try {
      const response = await nasm.api.getOneRMHistory(userId, page, 20);
      this.setState({
        oneRMListData: page === 1 ? response : [...oneRMListData, ...response],
        isLoading: false,
        refreshing: false,
        isNextPage: response && response.length > 0,
      });
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  onScroll = () => {
    if (this.state.isNextPage && !this.state.isLoading) {
      this.setState(
        (prevState) => ({
          isLoading: true,
          page: prevState.page + 1,
        }),
        () => {
          this.getOneRMHistory();
        },
      );
    }
  };

  oneRMDetail = (item) => {
    navigate(OneRMConstants.OneRMResultTable, {
      rmData: {
        maxLiftWeight:
          item.lift_type === 'NOT_SPECIFIED'
            ? item.calculation_results[0]
            : item.weight,
        units: item.unit_weight,
        type: LIFT_TYPE[item.lift_type],
        reps: item.repetitions,
      },
      comingFrom: OneRMConstants.OneRMHistory,
    });
  };

  renderItem = ({ item }) => (
    <TouchableOpacity
      activeOpacity={0.5}
      onPress={() => this.oneRMDetail(item)}
      style={styles.historyItem}
    >
      <Text style={styles.date}>
        {moment(item.created_at).format('MM/DD/YYYY')}
      </Text>
      <View style={styles.historyView}>
        <Image source={img1RmResult} style={styles.headerIconStyle} />
        <View style={styles.weightView}>
          <Text style={styles.weight}>{item.calculation_results[0]}</Text>
          <Text style={styles.unit}>{item.unit_weight}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderLoader = (isLoading, onermListData) => (
    <View style={styles.loaderView}>
      <ActivityIndicator
        size="large"
        animating={isLoading && onermListData.length > 0}
      />
    </View>
  );

  render() {
    const { oneRMListData, refreshing, isLoading } = this.state;
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          ref={(ref) => {
            this.flatList = ref;
          }}
          data={oneRMListData}
          keyExtractor={(item) => item.date}
          renderItem={this.renderItem}
          onEndReached={this.onScroll}
          onEndReachedThreshold={0.5}
          refreshControl={(
            <RefreshControl
              refreshing={refreshing}
              onRefresh={this.onRefresh}
            />
          )}
          ListFooterComponent={() => this.renderLoader(isLoading, oneRMListData)}
          ListEmptyComponent={() => (
            <View style={styles.loaderView}>
              {!isLoading && !refreshing ? (
                <View style={styles.emptyDataView}>
                  <ScaledText style={styles.emptyDataMessage}>
                    No history to show
                  </ScaledText>
                </View>
              ) : null}
            </View>
          )}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyDataView: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: curvedScale(20),
  },
  emptyDataMessage: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: DeviceInfo.isTablet() ? curvedScale(10) : curvedScale(14),
  },
  loaderView: {
    marginVertical: curvedScale(10),
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderColor: colors.paleGray,
    borderBottomWidth: 2,
  },
  historyView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '24%',
  },
  weightView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIconStyle: {
    width: 18,
    height: 18,
    marginRight: curvedScale(5),
  },
  date: {
    fontSize: curvedScale(14),
    color: colors.black,
    fontFamily: 'Avenir-Medium',
  },
  weight: {
    fontSize: curvedScale(14),
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    marginRight: curvedScale(5),
  },
  unit: {
    fontSize: curvedScale(12),
    color: colors.black,
    fontFamily: 'Avenir-Medium',
  },
});

OneRMHistory.propTypes = propTypes;
OneRMHistory.defaultProps = defaultProps;

const mapStateToProps = ({ selectedClient }) => ({
  selectedClient,
});
const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(OneRMHistory);
