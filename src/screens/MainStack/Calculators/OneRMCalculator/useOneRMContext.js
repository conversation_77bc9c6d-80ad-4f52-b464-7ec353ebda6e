import { useContext } from 'react';
import {
  OneRmStateContext,
  OneRmDispatchContext,
} from '../../../../reducers/OneRMContext';

const useOneRmStateContext = () => {
  const state = useContext(OneRmStateContext);
  if (state === undefined) {
    throw new Error('must be used within a Provider Component');
  }

  return state;
};

const useOneRmDispatchContext = () => {
  const dispatch = useContext(OneRmDispatchContext);
  if (dispatch === undefined) {
    throw new Error('must be used within a Provider Component');
  }

  return dispatch;
};

export { useOneRmStateContext, useOneRmDispatchContext };
