import React, { Component } from 'react';
import {
  View, Alert, ActivityIndicator, SafeAreaView,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import nasm from '../../../../dataManager/apiConfig';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import OneRMResultTable from './OneRMResultTable';
import OneRMFTU from './OneRMFTU';
import FloatingButton from '../../../../components/FloatingButton';
import OneRMConstants from './OneRMConstants';
import { colors } from '../../../../styles';
import { LIFT_TYPE } from '../../../../types/RMtypes';

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

const defaultProps = {};

class OneRMCurrent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
      oneRMData: null,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', {
        screen_name: 'current_OneRMCalculator',
      });
      this.getCurrentOneRMResults();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  getCurrentOneRMResults = async () => {
    try {
      this.setState({ isLoading: true });
      const userId = this.props?.selectedClient?.id;
      const response = await nasm.api.getCurrentOneRMResults(userId);
      if (response) {
        this.setState({
          oneRMData: {
            type: LIFT_TYPE[response.lift_type],
            units: response.unit_weight,
            maxLiftWeight:
              response.lift_type === 'NOT_SPECIFIED'
                ? response.calculation_results[0]
                : response.weight,
            reps: response.repetitions,
          },
          isLoading: false,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch (error) {
      this.setState({ isLoading: false });
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => {
        this.props.navigation.pop();
      }}
      titleStyle={styles.headerButtonText}
    />
  );

  onPressAdd = () => {
    this.props.navigation.navigate({
      name: OneRMConstants.OneRMCalculator,
      merge: true,
    });
  };

  render() {
    const { isLoading, oneRMData } = this.state;
    if (isLoading) {
      return (
        <View style={styles.loadingView}>
          <ActivityIndicator animating size="large" />
        </View>
      );
    }
    return (
      <SafeAreaView style={styles.container}>
        <View style={[styles.container, { marginBottom: 90 }]}>
          {oneRMData ? (
            <OneRMResultTable
              navigation={this.props.navigation}
              oneRMData={oneRMData}
            />
          ) : (
            <OneRMFTU />
          )}
        </View>
        <FloatingButton testID="AddCalculator" onPress={this.onPressAdd} />
      </SafeAreaView>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

OneRMCurrent.propTypes = propTypes;
OneRMCurrent.defaultProps = defaultProps;

const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(OneRMCurrent);
