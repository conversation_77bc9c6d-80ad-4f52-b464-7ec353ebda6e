import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  Text,
  Image,
  View,
  FlatList,
  Dimensions,
  StyleSheet,
  SafeAreaView,
  BackHandler,
  Alert,
} from 'react-native';
import PropTypes from 'prop-types';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../../components/HeaderRightButton';
import { calculateLiftsTable, calculateNSCATable } from './OneRMCalculations';
import { LIFT_TYPE } from '../../../../types/RMtypes';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';
import { CALCULATOR_CONTEXTS } from '../../../../reducers/calculatorContextReducer';
import nasm from '../../../../dataManager/apiConfig';
import LoadingSpinner from '../../../../components/LoadingSpinner';
import OneRMConstants from './OneRMConstants';

const numOfColumns = 2;
const screenWidth = Dimensions.get('screen').width;
const columnWidth = screenWidth / numOfColumns;
const img1RmResult = require('../../../../assets/1rm-result.png');

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    pop: PropTypes.func,
    popToTop: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      rmData: PropTypes.object,
      onReset: PropTypes.func,
      comingFrom: PropTypes.string,
    }),
  }).isRequired,
  calculatorContext: PropTypes.object,
  oneRMData: PropTypes.object,
};

const defaultProps = {
  calculatorContext: null,
  oneRMData: null,
};

class OneRMResultTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      rmData: props?.route?.params?.rmData || props?.oneRMData,
      isLoading: false,
    };
  }

  componentDidMount() {
    if (this.props?.route?.params?.rmData) {
      this.props.navigation.setOptions({
        headerLeft: this.renderHeaderLeft,
        headerRight:
          this.props?.route?.params?.comingFrom
            !== OneRMConstants.OneRMHistory && this.renderHeaderRight,
        headerTitle: 'One Rep Max',
        headerTitleStyle: styles.headerTitle,
      });
      this.backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        this.handleBackPress,
      );
    }
  }

  componentWillUnmount() {
    if (this.backHandler) {
      this.backHandler.remove();
    }
  }

  handleBackPress = () => {
    if (
      this.props.route
      && this.props.route.params
      && this.props.route.params.onReset
    ) {
      this.props.route.params.onReset();
    }
    return true;
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      title={
        this.props.calculatorContext?.type
        === CALCULATOR_CONTEXTS.SELECTED_CLIENT
          ? 'Back'
          : 'Cancel'
      }
      onPress={() => {
        if (
          this.props?.route?.params?.comingFrom === OneRMConstants.OneRMHistory
        ) {
          this.props.navigation.goBack();
        } else if (
          this.props.calculatorContext?.type
          === CALCULATOR_CONTEXTS.SELECTED_CLIENT
        ) {
          this.props.navigation.pop(4);
        } else {
          this.props.navigation.popToTop();
        }
      }}
      titleStyle={styles.headerButtonsText}
    />
  );

  saveResult = async () => {
    const userId = this.props?.selectedClient?.id;
    const {
      type, units, reps, weight,
    } = this.state.rmData;
    const data = this.getTabledata();
    const calResults = data.map((item) => Number(item.weight).toFixed(2));
    const liftType = Object.keys(LIFT_TYPE).find(
      (key) => LIFT_TYPE[key] === type,
    );
    try {
      this.setState({ isLoading: true });
      const jsonData = {
        lift_type: liftType,
        unit_weight: units,
        weight,
        repetitions: reps,
        calculation_results: calResults,
      };
      const response = await nasm.api.saveOneRMResults(userId, jsonData);
      if (response !== null) {
        this.setState({ isLoading: false });
        this.props.navigation.navigate(OneRMConstants.OneRMTabView);
      }
    } catch (error) {
      this.setState({ isLoading: false });
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  renderHeaderRight = () => {
    if (
      this.props.calculatorContext?.type === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT
    ) {
      return (
        <HeaderRightButton
          title="Reset"
          onPress={this.props.route?.params?.onReset}
          titleStyle={styles.headerButtonsText}
        />
      );
    }
    return (
      <HeaderRightButton
        title="Save"
        onPress={this.saveResult}
        titleStyle={styles.headerButtonsText}
      />
    );
  };

  renderTopHeader = (tableData) => (
    <View style={styles.topHeader}>
      <Text style={styles.headerDeadliftResultsText(colors.black)}>
        {`${this.state.rmData.type}`}
        {' '}
        <Text style={styles.headerDeadliftResultsText(colors.fillDarkGrey)}>
          Results
        </Text>
      </Text>
      <Text style={styles.header1RMText}>1RM*</Text>
      <View style={styles.header1RMViewStyle}>
        <Image source={img1RmResult} style={styles.headerIconStyle} />
        <Text style={styles.headerIRMValueText(colors.black, 18, 'bold')}>
          {Number(tableData[0].weight).toFixed(1)}
          {' '}
          <Text style={styles.headerIRMValueText(colors.darkGrey, 12)}>
            {this.state.rmData.units}
          </Text>
        </Text>
      </View>
    </View>
  );

  renderListHeaderItemView = (title) => (
    <View style={styles.listHeaderItemView}>
      <Text style={styles.listHeaderItemTitle}>{title}</Text>
    </View>
  );

  renderHorizontalSeparator = () => <View style={styles.horizontalSeparator} />;

  renderListHeaderComponent = () => (
    <View style={styles.listHeaderContainer}>
      {this.renderListHeaderItemView('% of 1RM')}
      {this.renderHorizontalSeparator()}
      {this.renderListHeaderItemView('Weight')}
    </View>
  );

  renderListItemView = (value) => (
    <View style={styles.listItemView}>
      <Text style={styles.lisItemValue}>{value}</Text>
    </View>
  );

  renderListItem = ({ item, index }) => (
    <View
      style={{
        ...styles.listItemContainer,
        backgroundColor: index % 2 === 1 ? colors.white : colors.paleGray2,
      }}
    >
      {this.renderListItemView(`${Math.round(item.percentage * 100)}%`)}
      {this.renderHorizontalSeparator()}
      {this.renderListItemView(
        `${Number(item.weight).toFixed(2)} ${this.state.rmData.units}`,
      )}
    </View>
  );

  getTabledata = () => {
    const { rmData } = this.state;
    const { maxLiftWeight, type, reps } = rmData;
    let tableData;
    if (type === LIFT_TYPE.NOT_SPECIFIED) {
      tableData = calculateLiftsTable(maxLiftWeight);
    } else {
      tableData = calculateNSCATable(maxLiftWeight, reps, type);
    }
    return tableData;
  };

  render() {
    const data = this.getTabledata();
    return (
      <SafeAreaView style={styles.container}>
        {this.renderTopHeader(data)}
        <FlatList
          data={data}
          renderItem={this.renderListItem}
          ListHeaderComponent={this.renderListHeaderComponent}
        />
        <LoadingSpinner
          visible={this.state.isLoading}
          size="large"
          backgroundColor="rgba(0, 0, 0, 0.25)"
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerButtonsText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.paleGray2,
    padding: curvedScale(20),
  },
  headerDeadliftResultsText: (color) => ({
    color,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    fontWeight: '700',
    textAlign: 'left',
  }),
  header1RMText: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(15),
    fontWeight: 'bold',
    textAlign: 'center',
  },
  header1RMViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIconStyle: {
    width: 25,
    height: 25,
    marginRight: curvedScale(5),
  },
  headerIRMValueText: (color, fontSize, fontWeight) => ({
    color,
    fontSize,
    fontWeight,
    fontFamily: 'Avenir',
    textAlign: 'right',
    justifyContent: 'center',
  }),
  listHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listHeaderItemView: {
    width: columnWidth,
    borderBottomWidth: 1,
    borderBottomColor: colors.textPlaceholder,
    backgroundColor: colors.white,
    paddingVertical: curvedScale(15),
  },
  listHeaderItemTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(14),
    fontWeight: '700',
    textAlign: 'center',
  },
  horizontalSeparator: {
    width: 1,
    height: '100%',
    backgroundColor: colors.textPlaceholder,
  },
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemView: {
    width: columnWidth,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.paleGray2,
    paddingVertical: curvedScale(15),
  },
  lisItemValue: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    textAlign: 'center',
  },
});

OneRMResultTable.propTypes = propTypes;
OneRMResultTable.defaultProps = defaultProps;

const mapStateToProps = ({ calculatorContext, selectedClient }) => ({
  calculatorContext,
  selectedClient,
});
const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(OneRMResultTable);
