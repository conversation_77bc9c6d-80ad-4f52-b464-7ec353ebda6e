import React, { Component } from 'react';
import {
  Text, Image, View, StyleSheet, SafeAreaView,
} from 'react-native';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { HeaderLeftButton } from '../../../../components';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';
import { CALCULATOR_CONTEXTS } from '../../../../reducers/calculatorContextReducer';

const imgBodyFatFTU = require('../../../../assets/bodyFat-FTU.png');

const propTypes = {
  navigation: PropTypes.object.isRequired,
  calculatorContext: PropTypes.object,
};

const defaultProps = {
  calculatorContext: null,
};

class BodyFatFTU extends Component {
  componentDidMount() {
    this.props?.navigation?.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerTitle: 'Body Fat Calculator',
      headerTitleStyle: styles.headerTitle,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  renderEmptyState = () => (
    <View style={styles.emptyStateContainer}>
      <View style={styles.emptyIconStyle}>
        <Image source={imgBodyFatFTU} />
      </View>
      <Text style={styles.emptyStateHeaderText}>
        {this.props?.calculatorContext?.type
        === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT
          ? 'Create your client’s first\nBody Fat calculation'
          : 'Create your first\nBody Fat calculation'}
      </Text>
      <Text style={styles.emptyStateBodyText}>
        The Body Fat calculator estimate overall body
        {'\n'}
        fatness. You will need a skin-fold caliper and/or
        {'\n'}
        a tape measure.
      </Text>
    </View>
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        {this.renderEmptyState()}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
  headerButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyIconStyle: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateHeaderText: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    fontSize: curvedScale(22),
    textAlign: 'center',
    marginTop: curvedScale(20),
  },
  emptyStateBodyText: {
    color: colors.darkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    textAlign: 'center',
    marginTop: curvedScale(10),
    lineHeight: 24,
  },
});

BodyFatFTU.propTypes = propTypes;
BodyFatFTU.defaultProps = defaultProps;

const mapStateToProps = ({ calculatorContext }) => ({ calculatorContext });
const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(BodyFatFTU);
