import {
  GENDER_TYPE,
  UNIT_TYPE,
  CALCULATION_TYPE,
  MethodKeysAndLabel,
} from '../../../../types/BodyFatTypes';
import { convert_height_to_centimeters_from_inches } from '../../../../util/CalculatorUtils';

export const calculateBodyFatRange = (bodyfatScore, age, gender) => {
  const categories = Object.values(getBodyFatTableValues(age, gender).data);
  const selectedRange = categories.find(
    (category) => bodyfatScore >= category.rangeStart && bodyfatScore <= category.rangeEnd,
  );
  if (selectedRange) {
    return selectedRange;
  }
  return categories[categories.length - 1];
};

const calculateUsingUSNavyMethod = (data) => {
  let bodyfatScore = 0;
  const isFemale = data.sex === GENDER_TYPE.FEMALE;
  const waistCircumference = data[MethodKeysAndLabel.US_NAVY_METHOD.waistCircumference.key];
  const hipCircumference = data[MethodKeysAndLabel.US_NAVY_METHOD.hipCircumference.key];
  const neckCircumference = data[MethodKeysAndLabel.US_NAVY_METHOD.neckCircumference.key];
  switch (data.unitType) {
    case UNIT_TYPE.METRIC:
      if (isFemale) {
        const numMetricFemale = 495
            / (1.29579
              - 0.35004
                * Math.log10(
                  Number(waistCircumference)
                    + Number(hipCircumference)
                    - Number(neckCircumference),
                )
              + 0.221 * Math.log10(Number(data.height_cm)))
          - 450;
        bodyfatScore = parseFloat(numMetricFemale).toFixed(1);
      } else {
        const numMetricMale = 495
            / (1.0324
              - 0.19077
                * Math.log10(
                  Number(waistCircumference) - Number(neckCircumference),
                )
              + 0.15456 * Math.log10(Number(data.height_cm)))
          - 450;
        bodyfatScore = parseFloat(numMetricMale).toFixed(1);
      }
      break;
    case UNIT_TYPE.IMPERIAL:
    default:
      if (isFemale) {
        const numFemale = 163.205
            * Math.log10(
              Number(waistCircumference)
                + Number(hipCircumference)
                - Number(neckCircumference),
            )
          - 97.684
            * Math.log10(Number(data.height_ft) * 12 + Number(data.height_in))
          - 78.387;
        bodyfatScore = parseFloat(numFemale).toFixed(1);
      } else {
        const numMale = 86.01
            * Math.log10(Number(waistCircumference) - Number(neckCircumference))
          - 70.041
            * Math.log10(Number(data.height_ft) * 12 + Number(data.height_in))
          + 36.76;
        bodyfatScore = parseFloat(numMale).toFixed(1);
      }
      break;
  }
  return bodyfatScore;
};

const calculateUsingJP3SMethod = (data) => {
  let bodyfatScore = 0;
  let bodyDensity = 0;
  const isFemale = data.sex === GENDER_TYPE.FEMALE;
  const age = Number(data.age);
  const { unitType } = data;
  const triceps = data[MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.triceps.key];
  const chest = data[MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.chest.key];
  const suprailiac = data[MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.suprailiac.key];
  const abdomen = data[MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.abdomen.key];
  const thigh = data[MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.thigh.key];
  const firstSkinfold = isFemale ? Number(triceps) : Number(chest);
  const secondSkinfold = isFemale ? Number(suprailiac) : Number(abdomen);
  const thirdSkinfold = Number(thigh);

  const X1 = isFemale ? 1.0994291 : 1.10938;
  const X2 = isFemale ? 0.0009929 : 0.0008267;
  const X3 = isFemale ? 0.0000023 : 0.0000016;
  const X4 = isFemale ? 0.0001392 : 0.0002574;
  let sumOfAllSkinfolds = Number(firstSkinfold) + Number(secondSkinfold) + Number(thirdSkinfold);
  if (unitType === UNIT_TYPE.IMPERIAL) {
    // inches to mm
    sumOfAllSkinfolds = convert_height_to_centimeters_from_inches(sumOfAllSkinfolds) * 10;
  }
  const sumOfAllSkinfoldsSquared = sumOfAllSkinfolds ** 2;
  bodyDensity = X1 - X2 * sumOfAllSkinfolds + X3 * sumOfAllSkinfoldsSquared - X4 * age;
  bodyfatScore = 495 / bodyDensity - 450;
  return bodyfatScore;
};

const calculateUsingJP7SMethod = (data) => {
  let bodyfatScore = 0;
  let bodyDensity = 0;
  const isFemale = data.sex === GENDER_TYPE.FEMALE;
  const age = Number(data.age);
  const { unitType } = data;
  const chest = data[MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.chest.key];
  const midAxillary = data[MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.midAxillary.key];
  const subscapular = data[MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.subscapular.key];
  const triceps = data[MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.triceps.key];
  const abdomen = data[MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.abdomen.key];
  const suprailiac = data[MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.suprailiac.key];
  const thigh = data[MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.thigh.key];
  const X1 = isFemale ? 1.097 : 1.112;
  const X2 = isFemale ? 0.00046971 : 0.00043499;
  const X3 = isFemale ? 0.00000056 : 0.00000055;
  const X4 = isFemale ? 0.00012828 : 0.00028826;
  let sumOfAllSkinfolds = Number(chest)
    + Number(midAxillary)
    + Number(subscapular)
    + Number(triceps)
    + Number(abdomen)
    + Number(suprailiac)
    + Number(thigh);
  if (unitType === UNIT_TYPE.IMPERIAL) {
    // inches to mm
    sumOfAllSkinfolds = convert_height_to_centimeters_from_inches(sumOfAllSkinfolds) * 10;
  }
  const sumOfAllSkinfoldsSquared = sumOfAllSkinfolds ** 2;
  bodyDensity = X1 - X2 * sumOfAllSkinfolds + X3 * sumOfAllSkinfoldsSquared - X4 * age;
  bodyfatScore = 495 / bodyDensity - 450;
  return bodyfatScore;
};

const calculateUsingDW4SMethod = (data) => {
  const isFemale = data.sex === GENDER_TYPE.FEMALE;
  const age = Number(data.age);
  const { unitType } = data;
  const biceps = data[MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.biceps.key];
  const subscapular = data[MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.subscapular.key];
  const triceps = data[MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.triceps.key];
  const suprailiac = data[MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.suprailiac.key];
  let sumOfAllSkinfolds = Number(biceps) + Number(subscapular) + Number(triceps) + Number(suprailiac);
  if (unitType === UNIT_TYPE.IMPERIAL) {
    // inches to mm
    sumOfAllSkinfolds = convert_height_to_centimeters_from_inches(sumOfAllSkinfolds) * 10;
  }
  const bodyDensityCategory = getBodyFatScoreByDW4SMethod(age, isFemale);
  const bodyDensity = bodyDensityCategory.subtractionFactor
    - bodyDensityCategory.multiplicationFactor * sumOfAllSkinfolds;
  const bodyfatScore = 495 / bodyDensity - 450;
  return bodyfatScore;
};

const getBodyFatScoreByDW4SMethod = (age = 0, isFemale = false) => {
  const bodyDensityEquations = [
    {
      ageStart: 0,
      ageEnd: 16,
      subtractionFactor: isFemale ? 1.1369 : 1.1533,
      multiplicationFactor: isFemale ? 0.0598 : 0.0643,
    },
    {
      ageStart: 17,
      ageEnd: 19,
      subtractionFactor: isFemale ? 1.1549 : 1.162,
      multiplicationFactor: isFemale ? 0.0678 : 0.063,
    },
    {
      ageStart: 20,
      ageEnd: 29,
      subtractionFactor: isFemale ? 1.1599 : 1.1631,
      multiplicationFactor: isFemale ? 0.0717 : 0.0632,
    },
    {
      ageStart: 30,
      ageEnd: 39,
      subtractionFactor: isFemale ? 1.1423 : 1.1422,
      multiplicationFactor: isFemale ? 0.0632 : 0.0544,
    },
    {
      ageStart: 40,
      ageEnd: 49,
      subtractionFactor: isFemale ? 1.1333 : 1.162,
      multiplicationFactor: isFemale ? 0.0612 : 0.07,
    },
    {
      ageStart: 50,
      ageEnd: 100,
      subtractionFactor: isFemale ? 1.1339 : 1.1715,
      multiplicationFactor: isFemale ? 0.0645 : 0.0779,
    },
  ];
  if (
    Number(age) > bodyDensityEquations[0].ageStart
    && Number(age) < bodyDensityEquations[bodyDensityEquations.length - 1].ageEnd
  ) {
    return bodyDensityEquations.find(
      (i) => Number(age) >= i.ageStart && Number(age) <= i.ageEnd,
    );
  }
  return bodyDensityEquations[0];
};

export const calculateBodyFat = (data) => {
  const method = data.methodType;
  switch (method) {
    case CALCULATION_TYPE.US_NAVY_METHOD:
      return calculateUsingUSNavyMethod(data);
    case CALCULATION_TYPE.JACKSON_POLLOCK_3_SITE:
      return calculateUsingJP3SMethod(data);
    case CALCULATION_TYPE.JACKSON_POLLOCK_7_SITE:
      return calculateUsingJP7SMethod(data);
    case CALCULATION_TYPE.DURNIN_WOMERSLEY_4_SITE:
      return calculateUsingDW4SMethod(data);
    default:
      return calculateUsingUSNavyMethod(data);
  }
};

export const getBodyFatTableValues = (age = 18, gender = GENDER_TYPE.MALE) => {
  const isFemale = gender === GENDER_TYPE.FEMALE;
  const tableData = [
    {
      ageStart: 18,
      ageEnd: 29,
      data: {
        ESSENTIAL_FAT: {
          category: 'Essential Fat',
          rangeStart: isFemale ? 10 : 2,
          rangeEnd: isFemale ? 13 : 5,
        },
        EXCELLENT: {
          category: 'Excellent',
          rangeStart: isFemale ? 13.1 : 5.1,
          rangeEnd: isFemale ? 17 : 9.3,
        },
        GOOD: {
          category: 'Good',
          rangeStart: isFemale ? 17.1 : 9.4,
          rangeEnd: isFemale ? 20.5 : 14,
        },
        AVERAGE: {
          category: 'Average',
          rangeStart: isFemale ? 20.6 : 14.1,
          rangeEnd: isFemale ? 23.6 : 17.5,
        },
        BELOW_AVERAGE: {
          category: 'Below Average',
          rangeStart: isFemale ? 23.7 : 17.6,
          rangeEnd: isFemale ? 27.6 : 22.5,
        },
        POOR: {
          category: 'Poor',
          rangeStart: isFemale ? 27.7 : 22.6,
          rangeEnd: Number.MAX_SAFE_INTEGER,
        },
      },
    },
    {
      ageStart: 30,
      ageEnd: 39,
      data: {
        ESSENTIAL_FAT: {
          category: 'Essential Fat',
          rangeStart: isFemale ? 10 : 2,
          rangeEnd: isFemale ? 13 : 5,
        },
        EXCELLENT: {
          category: 'Excellent',
          rangeStart: isFemale ? 13.1 : 5.1,
          rangeEnd: isFemale ? 17.9 : 13.8,
        },
        GOOD: {
          category: 'Good',
          rangeStart: isFemale ? 18 : 13.9,
          rangeEnd: isFemale ? 21.5 : 17.4,
        },
        AVERAGE: {
          category: 'Average',
          rangeStart: isFemale ? 21.6 : 17.5,
          rangeEnd: isFemale ? 24.8 : 20.4,
        },
        BELOW_AVERAGE: {
          category: 'Below Average',
          rangeStart: isFemale ? 24.9 : 20.5,
          rangeEnd: isFemale ? 29.2 : 24.1,
        },
        POOR: {
          category: 'Poor',
          rangeStart: isFemale ? 29.3 : 24.2,
          rangeEnd: Number.MAX_SAFE_INTEGER,
        },
      },
    },
    {
      ageStart: 40,
      ageEnd: 49,
      data: {
        ESSENTIAL_FAT: {
          category: 'Essential Fat',
          rangeStart: isFemale ? 10 : 2,
          rangeEnd: isFemale ? 13 : 5,
        },
        EXCELLENT: {
          category: 'Excellent',
          rangeStart: isFemale ? 13.1 : 5.1,
          rangeEnd: isFemale ? 21.2 : 16.2,
        },
        GOOD: {
          category: 'Good',
          rangeStart: isFemale ? 21.3 : 16.3,
          rangeEnd: isFemale ? 24.8 : 19.5,
        },
        AVERAGE: {
          category: 'Average',
          rangeStart: isFemale ? 24.9 : 19.6,
          rangeEnd: isFemale ? 28 : 22.4,
        },
        BELOW_AVERAGE: {
          category: 'Below Average',
          rangeStart: isFemale ? 28.1 : 22.5,
          rangeEnd: isFemale ? 32 : 26,
        },
        POOR: {
          category: 'Poor',
          rangeStart: isFemale ? 32.1 : 26.1,
          rangeEnd: Number.MAX_SAFE_INTEGER,
        },
      },
    },
    {
      ageStart: 50,
      ageEnd: 59,
      data: {
        ESSENTIAL_FAT: {
          category: 'Essential Fat',
          rangeStart: isFemale ? 10 : 2,
          rangeEnd: isFemale ? 13 : 5,
        },
        EXCELLENT: {
          category: 'Excellent',
          rangeStart: isFemale ? 13.1 : 5.1,
          rangeEnd: isFemale ? 24.9 : 17.8,
        },
        GOOD: {
          category: 'Good',
          rangeStart: isFemale ? 25 : 17.9,
          rangeEnd: isFemale ? 28.4 : 21.2,
        },
        AVERAGE: {
          category: 'Average',
          rangeStart: isFemale ? 28.5 : 21.3,
          rangeEnd: isFemale ? 31.5 : 24,
        },
        BELOW_AVERAGE: {
          category: 'Below Average',
          rangeStart: isFemale ? 31.6 : 24.1,
          rangeEnd: isFemale ? 35.5 : 27.4,
        },
        POOR: {
          category: 'Poor',
          rangeStart: isFemale ? 35.6 : 27.5,
          rangeEnd: Number.MAX_SAFE_INTEGER,
        },
      },
    },
    {
      ageStart: 60,
      ageEnd: 100,
      data: {
        ESSENTIAL_FAT: {
          category: 'Essential Fat',
          rangeStart: isFemale ? 10 : 2,
          rangeEnd: isFemale ? 13 : 5,
        },
        EXCELLENT: {
          category: 'Excellent',
          rangeStart: isFemale ? 13.1 : 5.1,
          rangeEnd: isFemale ? 25 : 18.3,
        },
        GOOD: {
          category: 'Good',
          rangeStart: isFemale ? 25.1 : 18.4,
          rangeEnd: isFemale ? 29.2 : 21.9,
        },
        AVERAGE: {
          category: 'Average',
          rangeStart: isFemale ? 29.3 : 22,
          rangeEnd: isFemale ? 32.4 : 25,
        },
        BELOW_AVERAGE: {
          category: 'Below Average',
          rangeStart: isFemale ? 32.5 : 25.1,
          rangeEnd: isFemale ? 36.5 : 28.4,
        },
        POOR: {
          category: 'Poor',
          rangeStart: isFemale ? 36.6 : 28.5,
          rangeEnd: Number.MAX_SAFE_INTEGER,
        },
      },
    },
  ];

  if (
    Number(age) > tableData[0].ageStart
    && Number(age) < tableData[tableData.length - 1].ageEnd
  ) {
    return tableData.find(
      (i) => Number(age) >= i.ageStart && Number(age) <= i.ageEnd,
    );
  }
  return tableData[0];
};
