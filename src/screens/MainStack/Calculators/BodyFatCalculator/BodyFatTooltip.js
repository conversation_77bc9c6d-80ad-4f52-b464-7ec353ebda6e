import React, { useEffect, useState } from 'react';
import {
  Dimensions,
  Text,
  Image,
  View,
  Modal,
  TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import { colors } from '../../../../styles';
import { ToolTipData } from './BodyFatConstants';

const iconCloseParticipants = require('../../../../assets/iconCloseSmallGrey.png');

const propTypes = {
  toolTipKey: PropTypes.string,
  onModalClose: PropTypes.func,
};

const defaultProps = {
  toolTipKey: '',
  onModalClose: null,
};

const BodyFatTooltip = (props) => {
  const { toolTipKey, onModalClose } = props;
  const [modalVisible, setModelVisible] = useState(true);

  useEffect(() => {
    setModelVisible(true);
  }, [toolTipKey]);

  const closeModal = () => {
    setModelVisible(false);
    if (onModalClose) {
      onModalClose();
    }
  };

  if (!toolTipKey?.length) {
    return null;
  }

  return (
    <Modal
      // presentationStyle="fullScreen"
      animationType="none"
      transparent
      visible={toolTipKey?.length && modalVisible}
      onRequestClose={closeModal}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.titleView}>
            <View style={styles.titleViewStyle}>
              <Text style={styles.title}>{ToolTipData[toolTipKey].label}</Text>
            </View>
            <TouchableOpacity onPress={closeModal}>
              <Image source={iconCloseParticipants} style={styles.crossImg} />
            </TouchableOpacity>
          </View>
          <View style={styles.iconView}>
            <Image source={ToolTipData[toolTipKey].image} />
          </View>
        </View>
      </View>
    </Modal>
  );
};
const scale = Dimensions.get('window').width / 400;

const styles = {
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    backgroundColor: colors.white,
    padding: 10,
    borderRadius: 5,
    elevation: 6,
  },
  titleView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 5,
  },
  titleViewStyle: {
    flex: 1,
  },
  title: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 17 * scale,
    color: colors.black,
    textAlign: 'center',
  },
  crossImg: {
    width: 14,
    height: 14,
  },
  iconView: {
    marginTop: 10,
  },
};

BodyFatTooltip.propTypes = propTypes;
BodyFatTooltip.defaultProps = defaultProps;

export default BodyFatTooltip;
