import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
  Platform,
  UIManager,
  StatusBar,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  LayoutAnimation,
  TouchableOpacity,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';

const closeImg = require('../../../../assets/closeCircle.png');

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

const BodyFatLearnMore = ({ navigation }) => {
  useEffect(() => {
    analytics().logEvent('screen_view', {
      screen_name: 'body_fat_calculator_learn_more',
    });
    StatusBar.setBarStyle('dark-content');
    setTimeout(() => {
      setCustomNavigationHeader();
    }, 300);
    return () => {
      StatusBar.setBarStyle('light-content');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setAnimation = () => {
    if (Platform.OS === 'android') {
      if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
      }
    }
    LayoutAnimation.configureNext({
      duration: 500,
      create: { type: 'linear', property: 'opacity' },
      update: { type: 'spring', springDamping: 0.5 },
    });
  };

  const setCustomNavigationHeader = () => {
    setAnimation();
    navigation.setOptions({
      headerStyle: { backgroundColor: colors.white, height: 100 },
      headerLeft: renderCloseImg,
    });
  };

  const goBack = () => {
    navigation.goBack();
  };

  const renderCloseImg = () => (
    <TouchableOpacity
      activeOpacity={0.6}
      hitSlop={{
        top: 20,
        bottom: 20,
        left: 20,
        right: 20,
      }}
      onPress={goBack}
      style={styles.closeImgView}
    >
      <Image source={closeImg} />
    </TouchableOpacity>
  );

  const renderPageContent = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <Text style={styles.title(22)}>Body Fat Calculator</Text>
      <Text style={styles.title(16, 20)}>Caliper Skills and Techniques</Text>
      <View>
        <Text style={styles.description}>
          Skinfolds (SKF) are indirect measurements of the thickness of
          subcutaneous region of the body is proportional to overall body
          fatness, and most of the time, this is the case. Proper assessments of
          body composition using skinfolds include these recommendations:
        </Text>
        <View style={styles.descriptionView}>
          <View style={styles.descriptionBulletView}>
            <Text style={styles.bullet}>•</Text>
            <Text style={styles.bulletDescription}>
              Train with an individual skilled in SKF assessment and frequently
              compare your results against theirs.
            </Text>
          </View>
          <View style={styles.descriptionBulletView}>
            <Text style={styles.bullet}>•</Text>
            <Text style={styles.bulletDescription}>
              Take a minimum of two measurements at each site; each site must be
              within 1 to 2 mm to take an average.
            </Text>
          </View>
          <View style={styles.descriptionBulletView}>
            <Text style={styles.bullet}>•</Text>
            <Text style={styles.bulletDescription}>
              Completely open the jaw of the caliper before removing from the
              site.
            </Text>
          </View>
          <View style={styles.descriptionBulletView}>
            <Text style={styles.bullet}>•</Text>
            <Text style={styles.bulletDescription}>
              Be meticulous when locating anatomic landmarks.
            </Text>
          </View>
          <View style={styles.descriptionBulletView}>
            <Text style={styles.bullet}>•</Text>
            <Text style={styles.bulletDescription}>
              Do not measure SKF’s immediately after exercise.
            </Text>
          </View>
          <View style={styles.descriptionBulletView}>
            <Text style={styles.bullet}>•</Text>
            <Text style={styles.bulletDescription}>
              Instruct clients ahead of time regarding test protocol.
            </Text>
          </View>
          <View style={styles.descriptionBulletView}>
            <Text style={styles.bullet}>•</Text>
            <Text style={styles.bulletDescription}>
              Avoid performing SKF’s on obese clients.
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>{renderPageContent()}</SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  closeImgView: {
    marginHorizontal: curvedScale(20),
    width: 30,
    height: 30,
  },
  content: {
    marginVertical: curvedScale(20),
    paddingHorizontal: curvedScale(20),
    height: '70%',
    backgroundColor: colors.white,
  },
  title: (fontSize = 22, marginTop = 0) => ({
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(fontSize),
    fontWeight: '700',
    textAlign: 'left',
    marginTop: curvedScale(marginTop),
  }),
  description: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(15),
    textAlign: 'left',
    marginTop: curvedScale(20),
    marginBottom: curvedScale(20),
  },
  bullet: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
    textAlign: 'left',
    marginRight: 10,
  },
  bulletDescription: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(15),
    textAlign: 'left',
    marginRight: 10,
  },
  descriptionBulletView: {
    flexDirection: 'row',
  },
  descriptionView: {
    marginLeft: curvedScale(10),
    marginRight: curvedScale(10),
  },
});

BodyFatLearnMore.propTypes = propTypes;
BodyFatLearnMore.defaultProps = defaultProps;

export default BodyFatLearnMore;
