import { useContext } from 'react';
import {
  BodyFatStateContext,
  BodyFatDispatchContext,
} from '../../../../reducers/BodyFatContext';

const useBodyFatStateContext = () => {
  const state = useContext(BodyFatStateContext);
  if (state === undefined) {
    throw new Error('must be used within a Provider Component');
  }

  return state;
};

const useBodyFatDispatchContext = () => {
  const dispatch = useContext(BodyFatDispatchContext);
  if (dispatch === undefined) {
    throw new Error('must be used within a Provider Component');
  }

  return dispatch;
};

export { useBodyFatStateContext, useBodyFatDispatchContext };
