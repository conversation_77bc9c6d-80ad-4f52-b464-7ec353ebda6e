import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  View,
  FlatList,
  RefreshControl,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  Alert,
  Image,
  TouchableOpacity,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import PropTypes from 'prop-types';
import moment from 'moment';
import DeviceInfo from 'react-native-device-info';
import ScaledText from '../../../../components/ScaledText';
import nasm from '../../../../dataManager/apiConfig';
import { BodyFatConstants } from './BodyFatConstants';
import { curvedScale, scaleHeight } from '../../../../util/responsive';
import { colors } from '../../../../styles';
import {
  getKeyByValue,
  get_height_from_inches,
} from '../../../../util/CalculatorUtils';
import { UNIT_TYPE } from '../../../../types/BodyFatTypes';

const imgBodyFatResult = require('../../../../assets/bodyFat-results.png');
const rightArrow = require('../../../../resources/imgRightArrowGray.png');

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

const defaultProps = {};

class BodyFatHistory extends Component {
  constructor(props) {
    super(props);
    this.state = {
      bodyFatListData: [],
      page: 1,
      isNextPage: true,
      isLoading: false,
      refreshing: true,
    };
  }

  componentDidMount() {
    analytics().logEvent('screen_view', {
      screen_name: 'body_fat_calculator_history',
    });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.onRefresh();
    });
  }

  onRefresh = () => {
    this.setState(
      {
        refreshing: true,
        page: 1,
      },
      () => {
        this.getBodyFatHistory();
      },
    );
  };

  getBodyFatHistory = async () => {
    const { page = 1, bodyFatListData } = this.state;
    const userId = this.props?.selectedClient?.id;
    try {
      const response = await nasm.api.getBodyFatHistory(userId, page, 20);
      this.setState({
        bodyFatListData:
          page === 1 ? response : [...bodyFatListData, ...response],
        isLoading: false,
        refreshing: false,
        isNextPage: response && response.length > 0,
      });
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  onScroll = () => {
    if (this.state.isNextPage && !this.state.isLoading) {
      this.setState(
        (prevState) => ({
          isLoading: true,
          page: prevState.page + 1,
        }),
        () => {
          this.getBodyFatHistory();
        },
      );
    }
  };

  renderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.assessmentCell}
      onPress={() => {
        this.props.navigation.navigate({
          name: 'BodyFatResultTable',
          params: {
            bodyFatData: {
              ...item,
              calculationMethod: item.method_type,
              unitType: item.unit_type,
              height_cm:
                item.unit_type === getKeyByValue(UNIT_TYPE, UNIT_TYPE.METRIC)
                  ? item.height
                  : '',
              height_ft:
                item.unit_type === getKeyByValue(UNIT_TYPE, UNIT_TYPE.IMPERIAL)
                  ? get_height_from_inches(item.height).feet
                  : '',
              height_in:
                item.unit_type === getKeyByValue(UNIT_TYPE, UNIT_TYPE.IMPERIAL)
                  ? get_height_from_inches(item.height).inches
                  : '',
              bodyfatScore: item.bodyfat_score,
            },
            comingFrom: BodyFatConstants.BodyFatHistory,
          },
          merge: true,
        });
      }}
    >
      <View style={styles.rowContainer(7)}>
        <ScaledText style={styles.date}>
          {moment(item.created_at).format('M/D/YYYY')}
        </ScaledText>
      </View>
      <View style={styles.rowContainer(3)}>
        <View style={styles.centerStyle}>
          <Image source={imgBodyFatResult} style={styles.bodyFatIconStyle} />
        </View>
        <ScaledText style={styles.bodyFatValue}>
          {`${Number(item.bodyfat_score).toFixed(1)}`}
        </ScaledText>
        <View style={styles.centerStyle}>
          <Image source={rightArrow} style={styles.bodyFatIconStyle} />
        </View>
      </View>
    </TouchableOpacity>
  );

  renderLoader = (isLoading, bodyFatListData) => (
    <View style={styles.loaderView}>
      <ActivityIndicator
        size="large"
        animating={isLoading && bodyFatListData.length > 0}
      />
    </View>
  );

  render() {
    const { bodyFatListData, refreshing, isLoading } = this.state;
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          ref={(ref) => {
            this.flatList = ref;
          }}
          data={bodyFatListData}
          keyExtractor={(item) => item.created_at}
          renderItem={this.renderItem}
          onEndReached={this.onScroll}
          onEndReachedThreshold={0.5}
          refreshControl={(
            <RefreshControl
              refreshing={refreshing}
              onRefresh={this.onRefresh}
            />
          )}
          ListFooterComponent={() => this.renderLoader(isLoading, bodyFatListData)}
          ListEmptyComponent={() => (
            <View style={styles.loaderView}>
              {!isLoading && !refreshing ? (
                <View style={styles.emptyDataView}>
                  <ScaledText style={styles.emptyDataMessage}>
                    No history to show
                  </ScaledText>
                </View>
              ) : null}
            </View>
          )}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyDataView: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: curvedScale(20),
  },
  emptyDataMessage: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: DeviceInfo.isTablet() ? curvedScale(10) : curvedScale(14),
  },
  loaderView: {
    marginVertical: curvedScale(10),
  },
  assessmentCell: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.silver51,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scaleHeight(2),
    height: curvedScale(90),
  },
  date: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(17),
    color: colors.black,
  },
  rowContainer: (flex = 1) => ({
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex,
  }),
  centerStyle: {
    width: curvedScale(22),
    height: curvedScale(22),
  },
  bodyFatIconStyle: {
    width: '100%',
    height: '100%',
  },
  bodyFatValue: {
    color: colors.black,
    fontSize: curvedScale(16),
    fontWeight: 'bold',
    fontFamily: 'Avenir-Roman',
    marginLeft: curvedScale(5),
  },
});

BodyFatHistory.propTypes = propTypes;
BodyFatHistory.defaultProps = defaultProps;

const mapStateToProps = ({ selectedClient }) => ({
  selectedClient,
});
const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(BodyFatHistory);
