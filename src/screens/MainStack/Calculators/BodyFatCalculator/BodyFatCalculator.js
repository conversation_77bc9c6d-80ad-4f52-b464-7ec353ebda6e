import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import PropTypes from 'prop-types';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { NIL } from 'uuid';
import { CALCULATION_TYPE } from '../../../../types/BodyFatTypes';
import { BodyFatConstants } from './BodyFatConstants';
import { curvedScale, scaleWidth } from '../../../../util/responsive';
import { colors } from '../../../../styles';
import DropDownPicker from '../../../../components/DropDownPicker';
import Button from '../../../../components/Button';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import ProgressBar from '../../../../components/ProgressBar';
import { onPressExternalLink } from '../../../../util/utils';

const disclaimerIcon = require('../../../../assets/error.png');

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    addListener: PropTypes.func,
    navigate: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

const CALCULATION_TYPE_OPTIONS = [
  {
    id: 1,
    label: CALCULATION_TYPE.US_NAVY_METHOD,
  },
  {
    id: 2,
    label: CALCULATION_TYPE.JACKSON_POLLOCK_3_SITE,
  },
  {
    id: 3,
    label: CALCULATION_TYPE.JACKSON_POLLOCK_7_SITE,
  },
  {
    id: 4,
    label: CALCULATION_TYPE.DURNIN_WOMERSLEY_4_SITE,
  },
];

const BodyFatCalculator = ({ navigation }) => {
  const [methodType, setMethodType] = useState(CALCULATION_TYPE.US_NAVY_METHOD);

  const onCalculate = () => {
    navigate(BodyFatConstants.BodyFatCalculatorField, { methodType });
  };

  const onReset = () => {
    navigation.goBack();
  };

  useEffect(() => {
    analytics().logEvent('screen_view', {
      screen_name: 'body_fat_calculator_selection',
    });
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: renderHeaderLeft,
      headerTitle: 'Body Fat Calculator',
      headerTitleStyle: styles.headerTitle,
    });
  });

  const renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={onReset}
      title="Cancel"
      titleStyle={styles.headerButtonText}
    />
  );

  const navigate = (name, params) => {
    navigation.navigate({ name, params, merge: true });
  };

  const renderProgressBar = () => (
    <ProgressBar
      progress={50}
      progressColor={colors.medYellow}
      barStyle={styles.barStyle}
      style={styles.progressBackground}
    />
  );

  const renderPageHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitleBold}>Body Fat Calculator</Text>
      <Text style={styles.headerDescription}>
        Body fat calculators estimate total body fat percentages based on
        specific measurements taken on the body and will vary greatly. The
        methods provided will require a skinfold caliper and/or tape measure to
        complete the calculations.
      </Text>
      {renderLearnMoreButton()}
    </View>
  );

  const renderMethodDescription = () => {
    switch (methodType) {
      case CALCULATION_TYPE.JACKSON_POLLOCK_3_SITE:
        return 'While the Jackson and Pollock three-site protocol is perhaps not as accurate as the seven-site protocol, it is still considered valid. An advantage is that the three-site protocol is less invasive to conduct.';
      case CALCULATION_TYPE.JACKSON_POLLOCK_7_SITE:
        return 'Although the seven-site method is considered the most accurate skinfold method because it collects SKFs at more locations across the body, this protocol is certainly more invasive to the client. As such, it may be best used for clinical or athletic populations.';
      case CALCULATION_TYPE.DURNIN_WOMERSLEY_4_SITE:
        return 'The Durnin-Womersley formula is best used for individuals between the ages of 17 and 49 years of age and may not be as applicable for older adults. A potential advantage is that it only requires measurements for the upper body; it does not require an individual to wear shorts for a thigh measurement.';
      default:
        return "The U.S. Navy Method to estimated is the least accurate but requires minimal equipment and measurements to use. This method's accuracy is approximately 3-4%.";
    }
  };

  const getBottomLinkData = () => {
    switch (methodType) {
      case CALCULATION_TYPE.JACKSON_POLLOCK_3_SITE:
      case CALCULATION_TYPE.JACKSON_POLLOCK_7_SITE:
        return [
          {
            label: 'Based on the Cambridge Core British Journal of Nutrition',
            link:
              'https://www.cambridge.org/core/journals/british-journal-of-nutrition/article/generalized-equations-for-predicting-body-density-of-men/EAB21B1CF3A8360E5F5D43FDB8D4DD17',
          },
          {
            label:
              'Based on the Medicine & Science in Sports & Exercise Article',
            link:
              'https://journals.lww.com/acsm-msse/Abstract/1980/23000/Generalized_equations_for_predicting_body_density.9.aspx',
          },
        ];
      case CALCULATION_TYPE.DURNIN_WOMERSLEY_4_SITE:
        return [
          {
            label: 'Based on the NIH Durnin-Womersly Article',
            link: 'https://www.ncbi.nlm.nih.gov/pmc/articles/PMC7368772/',
          },
        ];
      default:
        return [
          {
            label: 'Based on the NIH U.S. Navy Body Composition Program',
            link: 'https://pubmed.ncbi.nlm.nih.gov/25562863/',
          },
        ];
    }
  };

  const renderLearnMoreButton = () => (
    <TouchableOpacity
      style={styles.learnMoreBtn}
      onPress={() => navigate(BodyFatConstants.BodyFatLearnMore)}
    >
      <Text style={styles.learnMoreBtnText}>Learn More</Text>
    </TouchableOpacity>
  );

  const renderMethodType = () => (
    <View style={styles.unitContainer}>
      <DropDownPicker
        useNativeAndroidPickerStyle
        data={CALCULATION_TYPE_OPTIONS}
        label={methodType}
        placeholder=""
        selected={NIL}
        onValueChange={(selectedType) => {
          setMethodType(selectedType.label);
        }}
        labelStyle={styles.pickerItem}
        containerStyle={styles.picker}
        contentContainerStyle={styles.contentContainerStyle}
      />
    </View>
  );

  const renderFooter = () => (
    <View style={styles.footerView}>
      <Image source={disclaimerIcon} style={styles.disclaimer} />
      <Text style={styles.disclaimerDescription}>
        For consistency, it&apos;s important to take measurements at the same
        time every day and use the same calculation method.
      </Text>
    </View>
  );

  const renderCalculateButton = () => (
    <View style={styles.calculateBtnView}>
      <Button
        textStyles={styles.calculateBtnText}
        title="Calculate"
        buttonStyle={[
          styles.createAccountButton,
          { borderRadius: scaleWidth(7.2) },
        ]}
        onPress={onCalculate}
      />
    </View>
  );

  const bottomLinkData = getBottomLinkData();

  return (
    <SafeAreaView style={styles.container}>
      {renderProgressBar()}
      <KeyboardAwareScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {renderPageHeader()}
        <View style={styles.pageView}>
          <Text style={styles.title}>
            What calculation method would you like to use?
          </Text>
          {renderMethodType()}
          <Text style={styles.description}>{renderMethodDescription()}</Text>
          {renderFooter()}
        </View>
      </KeyboardAwareScrollView>
      {renderCalculateButton()}
      <View style={styles.bottomLinkContainer}>
        {bottomLinkData.map((item, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => onPressExternalLink(item.link)}
          >
            <Text style={styles.bottomLink}>{item.label}</Text>
          </TouchableOpacity>
        ))}
        <TouchableOpacity
          onPress={() => onPressExternalLink('https://www.nasm.org/products/CPT7104001')}
        >
          <Text style={styles.bottomLink}>
            Based on the NASM CPT 7th Edition Textbook
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
  headerButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  scrollView: {
    height: '90%',
  },
  headerContainer: {
    paddingHorizontal: curvedScale(20),
    paddingTop: curvedScale(40),
    paddingBottom: curvedScale(20),
    backgroundColor: colors.offWhite,
  },
  headerTitleBold: {
    color: colors.eerieBlack,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    fontWeight: '700',
  },
  title: {
    color: colors.eerieBlack,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(17),
    marginTop: curvedScale(10),
    marginBottom: curvedScale(20),
  },
  description: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    marginTop: curvedScale(20),
  },
  headerDescription: {
    color: colors.eerieBlack,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
    marginTop: curvedScale(20),
  },
  learnMoreBtn: {
    width: '40%',
    alignItems: 'center',
    borderRadius: 18.5,
    borderWidth: 1,
    borderColor: colors.disclaimerGrey,
    paddingHorizontal: curvedScale(16),
    paddingVertical: curvedScale(8),
    marginTop: curvedScale(20),
  },
  learnMoreBtnText: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(12),
    fontWeight: '500',
  },
  pageView: {
    padding: curvedScale(20),
    backgroundColor: colors.white,
  },
  pickerItem: {
    color: colors.eerieBlack,
    fontSize: 14,
    fontFamily: 'Avenir-Light',
    fontWeight: '500',
    paddingHorizontal: curvedScale(8),
    justifyContent: 'space-between',
  },
  picker: {
    height: curvedScale(40),
    marginVertical: 0,
  },
  unitContainer: {
    justifyContent: 'space-between',
    width: '100%',
    borderWidth: 1,
    borderRadius: 5,
    borderColor: colors.actionSheetDivider,
  },
  contentContainerStyle: {
    paddingBottom: 0,
  },
  calculateBtnView: {
    margin: curvedScale(20),
    height: '10%',
  },
  calculateBtnText: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
    fontWeight: '700',
  },
  barStyle: {
    borderRadius: 0,
  },
  progressBackground: {
    borderRadius: 0,
    width: '100%',
    borderWidth: 0,
    backgroundColor: colors.nasmBlue,
    height: 12,
  },
  footerView: {
    marginTop: curvedScale(20),
    backgroundColor: colors.disclaimerGrey,
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    borderRadius: curvedScale(5),
  },
  disclaimer: {
    marginRight: curvedScale(5),
    marginLeft: curvedScale(10),
  },
  disclaimerDescription: {
    color: colors.eerieBlack,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(11),
    marginRight: curvedScale(40),
    marginVertical: curvedScale(7),
  },
  bottomLinkContainer: {
    marginVertical: curvedScale(20),
  },
  bottomLink: {
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Medium',
    color: colors.fillDarkGrey,
    alignSelf: 'center',
    textDecorationLine: 'underline',
    lineHeight: 20,
  },
});

BodyFatCalculator.propTypes = propTypes;
BodyFatCalculator.defaultProps = defaultProps;

export default BodyFatCalculator;
