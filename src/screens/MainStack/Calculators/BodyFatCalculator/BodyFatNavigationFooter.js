import React, { useEffect, useState } from 'react';
import {
  Image,
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  Alert,
  Platform,
  UIManager,
  Keyboard,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import PropTypes from 'prop-types';
import { colors } from '../../../../styles';
import { clearFormData } from '../../../../actions';
import { curvedScale } from '../../../../util/responsive';

const propTypes = {
  onPressPrevious: PropTypes.func,
  onPressNext: PropTypes.func,
};

const defaultProps = {
  onPressPrevious: null,
  onPressNext: null,
  isTrainer: false,
};

const imgArrow = require('../../../../resources/rightArrow.png');
const imgCheckmarkNav = require('../../../../resources/btnCheckmarkNav.png');

const BodyFatNavigationFooter = ({ onPressPrevious, onPressNext }) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const customStyle = {
    opacity: 1,
  };
  const isAndroid = Platform.OS === 'android';
  if (isAndroid) {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const setAnimation = () => {
    Keyboard.scheduleLayoutAnimation({ duration: 100, easing: 'easeIn' });
  };

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setAnimation();
      setKeyboardVisible(true);
    });
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setAnimation();
      setKeyboardVisible(false);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);
  const resetCalulation = () => {
    Alert.alert(
      'Are you sure you want to start over?',
      'Any information entered in will be lost.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Start Over',
          style: 'destructive',
          onPress: () => {
            dispatch(clearFormData());
            navigation.goBack();
          },
        },
      ],
      { cancelable: false },
    );
  };
  if (isAndroid && keyboardVisible) {
    return null;
  }
  return (
    <View style={styles.container}>
      <View style={styles.controller}>
        <TouchableOpacity
          style={[styles.arrowView, customStyle]}
          onPress={onPressPrevious}
        >
          <Image
            style={[styles.arrow, styles.previousArrow, customStyle]}
            source={imgArrow}
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={resetCalulation}>
          <Text style={styles.reset}>Reset</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.arrowView, styles.customArrowView]}
          onPress={onPressNext}
        >
          <Image
            style={[styles.arrow, styles.customArrow]}
            source={imgCheckmarkNav}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    justifyContent: 'center',
    padding: 20,
  },
  controller: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  previousArrow: {
    transform: [{ rotate: '180deg' }],
  },
  reset: {
    fontSize: curvedScale(15),
    fontFamily: 'Avenir-Medium',
    color: colors.fillDarkGrey,
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    borderRadius: 25,
    paddingHorizontal: curvedScale(40),
    paddingVertical: curvedScale(10),
  },
  arrowView: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    borderRadius: 100,
    padding: curvedScale(20),
  },
  customArrowView: {
    backgroundColor: colors.avatarGreen,
  },
  arrow: {
    tintColor: colors.fillDarkGrey,
    width: curvedScale(25),
    height: curvedScale(25),
  },
  customArrow: {
    tintColor: colors.white,
  },
});

BodyFatNavigationFooter.propTypes = propTypes;
BodyFatNavigationFooter.defaultProps = defaultProps;

export default BodyFatNavigationFooter;
