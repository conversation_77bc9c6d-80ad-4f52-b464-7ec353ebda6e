import Abdomen from '../../../../assets/abdomen.png';
import Chest from '../../../../assets/chest.png';
import MidAxillary from '../../../../assets/mid-axillary.png';
import NeckCircumference from '../../../../assets/neck-circumference.png';
import Subscapular from '../../../../assets/subscapular.png';
import Suprailiac from '../../../../assets/suprailiac.png';
import Thigh from '../../../../assets/thigh.png';
import Triceps from '../../../../assets/tricep.png';
import WaistCircumference from '../../../../assets/waist-circumference.png';

export const BodyFatConstants = {
  BodyFatTabView: 'BodyFatTabView',
  BodyFatFTU: 'BodyFatFTU',
  BodyFatCalculatorField: 'BodyFatCalculatorField',
  BodyFatHistory: 'BodyFatHistory',
  BodyFatCalculator: 'BodyFatCalculator',
  BodyFatLearnMore: 'BodyFatLearnMore',
  BodyFatResultTable: 'BodyFatResultTable',
};

export const ToolTipData = {
  abdomen: {
    label: 'Abdomen',
    image: Abdomen,
  },
  chest: {
    label: 'Chest',
    image: Chest,
  },
  mid_axillary: {
    label: 'Mid-axillary',
    image: MidAxillary,
  },
  neck_circumference: {
    label: 'Neck Circumference',
    image: NeckCircumference,
  },
  subscapular: {
    label: 'Subscapular',
    image: Subscapular,
  },
  suprailiac: {
    label: 'Suprailiac',
    image: Suprailiac,
  },
  thigh: {
    label: 'Thigh',
    image: Thigh,
  },
  triceps: {
    label: 'Triceps',
    image: Triceps,
  },
  triceps_subscapular: {
    label: 'Triceps',
    image: Triceps,
  },
  waist_circumference: {
    label: 'Waist Circumference',
    image: WaistCircumference,
  },
};
