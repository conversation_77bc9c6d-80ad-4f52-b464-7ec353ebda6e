import React, { Component } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { Dimensions } from 'react-native';
import PropTypes from 'prop-types';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import BodyFatCurrent from './BodyFatCurrent';
import BodyFatHistory from './BodyFatHistory';
import { colors, materialTabBarOptions } from '../../../../styles';

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
    setParams: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

class BodyFatTabView extends Component {
  static router = TabNav.router;

  componentDidMount() {
    this.props.navigation.setOptions({
      headerTitle: 'Body Fat Calculator',
      headerLeft: this.renderHeaderLeft,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  updateHeaderRight = (headerRight) => {
    this.props.navigation.setParams({
      renderHeaderRight: headerRight,
    });
  };

  updateHeaderLeft = (headerLeft) => {
    this.props.navigation.setParams({
      renderHeaderLeft: headerLeft,
    });
  };

  render() {
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
        style={materialTabBarOptions.tabBarOptions.tabBarStyle}
      >
        <TabNav.Screen
          name="Current"
          component={BodyFatCurrent}
          initialParams={{
            updateHeaderLeft: this.updateHeaderLeft,
          }}
        />
        <TabNav.Screen
          name="History"
          component={BodyFatHistory}
          initialParams={{
            updateHeaderLeft: this.updateHeaderLeft,
          }}
        />
      </TabNav.Navigator>
    );
  }
}

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

BodyFatTabView.propTypes = propTypes;
BodyFatTabView.defaultProps = defaultProps;

export default BodyFatTabView;
