import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Alert,
  Platform,
  Image,
  NativeModules,
  Keyboard,
  SafeAreaView,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import PropTypes from 'prop-types';
import { useNavigation } from '@react-navigation/native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { clearFormData } from '../../../../actions';
import {
  GENDER_TYPE,
  UNIT_TYPE,
  CALCULATION_TYPE,
  BODY_FAT_ACTION_TYPES,
  MethodKeysAndLabel,
} from '../../../../types/BodyFatTypes';
import BodyFatNavigationFooter from './BodyFatNavigationFooter';
import { BodyFatConstants } from './BodyFatConstants';
import ProgressBar from '../../../../components/ProgressBar';
import { DropDownPicker } from '../../../../components';
import { curvedScale } from '../../../../util/responsive';
import { colors } from '../../../../styles';
import { removeAllSpecialCharacters } from '../../../../util/validate';
import {
  feetAndInchesToInches,
  getKeyByValue,
} from '../../../../util/CalculatorUtils';
import {
  useBodyFatDispatchContext,
  useBodyFatStateContext,
} from './useBodyFatContext';
import { BodyFatContextProvider } from '../../../../reducers/BodyFatContext';
import BodyFatTooltip from './BodyFatTooltip';

const tooltipIcon = require('../../../../assets/tooltip.png');

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    navigate: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      methodType: PropTypes.string,
    }),
  }).isRequired,
};

const defaultProps = {};

const UNIT_OPTIONS = [
  {
    id: 1,
    label: UNIT_TYPE.IMPERIAL,
  },
  {
    id: 2,
    label: UNIT_TYPE.METRIC,
  },
];

const GENDER_OPTIONS = [
  {
    id: 1,
    label: GENDER_TYPE.MALE,
  },
  {
    id: 2,
    label: GENDER_TYPE.FEMALE,
  },
];

const screenWidth = Dimensions.get('screen').width;
const useVerticalFlex = screenWidth <= 375;

const { AppKeyboardHandlerMethods } = NativeModules;

const BodyFatCalculatorField = ({ route }) => {
  const navigation = useNavigation();
  const dispatch = useBodyFatDispatchContext();
  const state = useBodyFatStateContext();
  const [toolTipKey, setTooltipKey] = useState(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const isAndroid = Platform.OS === 'android';

  useEffect(() => {
    analytics().logEvent('screen_view', {
      screen_name: 'body_fat_calculator_input',
      method_type: route?.params?.methodType,
    });
    dispatch({
      type: BODY_FAT_ACTION_TYPES.EDIT,
      payload: { methodType: route?.params?.methodType },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    navigation.setOptions({
      title: 'Body Fat Calculator',
      headerTitleStyle: styles.headerTitle,
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    });
  });

  useEffect(() => {
    let showSubscription = '';
    let hideSubscription = '';

    const unsubscribeFocus = navigation.addListener('focus', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustResize();
      }
      showSubscription = Keyboard.addListener('keyboardDidShow', () => {
        setAnimation();
        setKeyboardVisible(true);
      });
      hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
        setAnimation();
        setKeyboardVisible(false);
      });
    });

    const unsubscribeBlur = navigation.addListener('blur', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustPan();
      }
    });
    return () => {
      if (unsubscribeFocus) {
        unsubscribeFocus();
      }
      if (unsubscribeBlur) {
        unsubscribeBlur();
      }
      if (showSubscription) {
        showSubscription.remove();
      }
      if (hideSubscription) {
        hideSubscription.remove();
      }
    };
  }, [navigation]);

  const setAnimation = () => {
    Keyboard.scheduleLayoutAnimation({ duration: 100, easing: 'easeIn' });
  };

  useEffect(() => {
    if (Number(state.bodyfat_score) > 0 && Number(state.bodyfat_score) <= 100) {
      let bodyFatData = {
        method_type: getKeyByValue(CALCULATION_TYPE, state.methodType),
        unit_type: getKeyByValue(UNIT_TYPE, state.unitType),
        age: Number(state.age),
        gender: getKeyByValue(GENDER_TYPE, state.sex),
        weight: state.weight,
        height:
          state.unitType === UNIT_TYPE.IMPERIAL
            ? feetAndInchesToInches(state.height_ft, state.height_in)
            : state.height_cm,
        bodyfat_score: state.bodyfat_score,
      };
      if (state.methodType !== CALCULATION_TYPE.US_NAVY_METHOD) {
        delete bodyFatData.height;
      }
      const skinfolds = Object.values(
        MethodKeysAndLabel[getKeyByValue(CALCULATION_TYPE, state.methodType)],
      );
      skinfolds.forEach((field) => {
        if (state[field.key]) {
          bodyFatData = {
            ...bodyFatData,
            [field.key]: state[field.key],
          };
        }
      });
      navigation.navigate(BodyFatConstants.BodyFatResultTable, {
        bodyFatData,
        onReset: returnToDashboard,
      });
    } else if (state.bodyfat_score) {
      Alert.alert(
        'Error!',
        'The inputs you have given generates an invalid bodyfat percentage. Please check your inputs again!',
      );
      onEditValue('bodyfat_score', null);
    }
  }, [navigation, state.bodyfat_score]);

  const onEditValue = (key, value) => {
    dispatch({ type: BODY_FAT_ACTION_TYPES.EDIT, payload: { [key]: value } });
  };

  const onChangeUnit = (unitType) => {
    dispatch({
      type: BODY_FAT_ACTION_TYPES.CHANGE_UNITS,
      payload: { unitType },
    });
  };

  const returnToDashboard = () => {
    dispatch(clearFormData());
    navigation.pop(2);
  };

  const onPressNext = () => {
    const { status, message } = isValidForm();
    if (!status) {
      Alert.alert('Error', message);
    } else {
      dispatch({ type: BODY_FAT_ACTION_TYPES.CALCULATE });
    }
  };

  const isValidForm = () => {
    let status = true;
    let message = '';
    if (!Number(state.age) || state.age <= 0) {
      status = false;
      message = 'Please enter a valid age.';
      return { status, message };
    }
    if (!Number(state.weight) || state.weight <= 0) {
      status = false;
      message = 'Please enter a valid weight.';
      return { status, message };
    }
    if (state.methodType === CALCULATION_TYPE.US_NAVY_METHOD) {
      const heightUnit = state.unitType === UNIT_TYPE.METRIC ? 'height_cm' : 'height_ft';
      if (!state[heightUnit] || state[heightUnit] <= 0) {
        status = false;
        message = 'Please enter a valid height.';
        return { status, message };
      }
    }

    const measurementFields = MethodKeysAndLabel[getKeyByValue(CALCULATION_TYPE, state.methodType)];
    const errors = {};

    const measurementTypes = [];

    Object.values(measurementFields).forEach((field) => {
      if (
        !Number(state[field.key])
        || state[field.key] <= 0
        || !state[field.key]?.toString().match(/^[+-]?(\d*\.)?\d+$/)
      ) {
        if (!field.gender || field.gender === state.sex) {
          measurementTypes.push(field.label);
          Object.assign(errors, { [field.key]: field.key });
        }
      }
    });
    if (Object.keys(errors).length) {
      status = false;
      message = `Please enter a valid ${measurementTypes[0]}`;
      return { status, message };
    }
    return { status, message };
  };

  const stripDecimals = (input) => {
    const filteredInput = input?.toString();
    const beforeDecimalStr = filteredInput.substring(
      0,
      filteredInput.indexOf('.'),
    );
    const afterDecimalStr = filteredInput.substring(
      filteredInput.indexOf('.') + 1,
      filteredInput.length,
    );
    const filteredStr = afterDecimalStr.replaceAll('.', '');
    const finalStr = beforeDecimalStr.concat('.').concat(filteredStr);
    return finalStr;
  };

  const stripNonNumericChars = (input, decimalAllowed) => {
    if (decimalAllowed && input?.includes('.')) {
      const finalStr = stripDecimals(input);
      return finalStr.replace(/[^0-9.]/g, '');
    }
    return input.replace(/[^0-9]/g, '');
  };

  const getAllowedMaxInputLength = (maxLength, value) => {
    let allowedMaxLength = maxLength;
    const stringValue = value?.toString();
    if (stringValue && stringValue.length > 0 && stringValue.includes('.')) {
      const lengthBeforeDecimal = stringValue.split('.')[0].length;
      allowedMaxLength = lengthBeforeDecimal + 3;
    }
    return allowedMaxLength;
  };

  const getTextInputProps = (maxLength, customStyle) => {
    const props = {
      maxLength,
    };
    if (customStyle) {
      props.style = customStyle;
    }
    return props;
  };

  const getSelectedGender = () => {
    let selectedGenderId = GENDER_OPTIONS[0].id;
    if (state.sex) {
      selectedGenderId = GENDER_OPTIONS.find(
        (gender) => gender.label.toLowerCase() === state.sex?.toLowerCase(),
      ).id;
    }
    return selectedGenderId;
  };

  const renderHeaderLeft = () => (
    <TouchableOpacity onPress={returnToDashboard}>
      <Text style={styles.headerLeft}>Cancel</Text>
    </TouchableOpacity>
  );

  const renderHeaderRight = () => <Text style={styles.headerRight}>2/2</Text>;

  const renderProgressBar = () => (
    <ProgressBar
      progress={100}
      progressColor={colors.medYellow}
      barStyle={styles.barStyle}
      style={styles.progressBackground}
    />
  );

  const renderPageHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitleBold}>{state.methodType}</Text>
    </View>
  );

  const renderTextInput = (
    key,
    unit,
    textInputProps,
    customStyle,
    decimalAllowed,
  ) => {
    let fieldName = key;
    const fieldValue = state[key.toLowerCase()];
    if (unit === 'ft') {
      fieldName = 'Feet';
    } else if (unit === 'in') {
      fieldName = 'Inches';
    } else if (unit === 'cm') {
      fieldName = 'Centimeters';
    }

    let keyboardType = 'number-pad';
    if (unit === 'cm' || key === 'Weight') {
      keyboardType = 'decimal-pad';
    }

    const leftMargin = {
      marginLeft: Platform.OS === 'ios' ? curvedScale(5) : 0,
    };
    return (
      <View
        style={[
          styles.inputContainer,
          styles.customInputContainer,
          customStyle,
        ]}
      >
        <View style={styles.inputLabel}>
          <Text style={styles.input}>
            {`${fieldName}`}
            {' '}
          </Text>
        </View>
        <View style={styles.inputView}>
          <TextInput
            style={[styles.input, styles.customTextInput]}
            placeholder="0"
            placeholderTextColor={colors.fillDarkGrey}
            value={`${fieldValue}`}
            onChangeText={(newValue) => {
              const filteredValue = removeAllSpecialCharacters(newValue);
              if (unit === 'in' && filteredValue) {
                if (parseInt(filteredValue, 10) < 12) {
                  onEditValue(
                    `${[key.toLowerCase()]}`,
                    stripNonNumericChars(filteredValue, decimalAllowed),
                  );
                }
              } else {
                onEditValue(
                  `${[key.toLowerCase()]}`,
                  stripNonNumericChars(filteredValue, decimalAllowed),
                );
              }
            }}
            keyboardType={keyboardType}
            returnKeyType="done"
            contextMenuhidden
            {...textInputProps}
          />
          <Text style={[styles.input, leftMargin]}>{unit.toLowerCase()}</Text>
        </View>
      </View>
    );
  };

  const showMeasurementUnit = () => {
    if (state.unitType === UNIT_TYPE.IMPERIAL) {
      return 'in';
    }
    if (state.methodType === CALCULATION_TYPE.US_NAVY_METHOD) {
      return 'cm';
    }
    return 'mm';
  };

  const renderMeasurementsTextInput = ({ key, label, textInputProps }) => {
    const unit = showMeasurementUnit();
    const fieldValue = state[key] || '';
    const leftMargin = {
      marginLeft: Platform.OS === 'ios' ? curvedScale(5) : 0,
    };
    return (
      <View style={styles.inputMeasurementsContainer}>
        <View style={styles.inputLabel}>
          <Text style={styles.input}>
            {`${label}`}
            {' '}
          </Text>
        </View>
        <View style={styles.inputView}>
          <TextInput
            style={[styles.input, styles.customTextInput]}
            placeholder="0"
            placeholderTextColor={colors.fillDarkGrey}
            value={`${fieldValue}`}
            onChangeText={(newValue) => {
              const filteredValue = removeAllSpecialCharacters(newValue);
              const allowedLength = filteredValue.includes('.') ? 5 : 4;
              if (
                +filteredValue > 1000
                || filteredValue.length > allowedLength
              ) {
                return;
              }
              onEditValue(
                `${[key]}`,
                stripNonNumericChars(filteredValue, true),
              );
            }}
            keyboardType="decimal-pad"
            returnKeyType="done"
            contextMenuhidden
            {...textInputProps}
          />
          <Text style={[styles.input, leftMargin]}>{unit}</Text>
        </View>
      </View>
    );
  };

  const renderBasicInfo = () => (
    <View style={styles.marginTop}>
      <Text style={styles.title}>Basic Information</Text>
      <View style={styles.weightContainer}>
        {renderUnit()}
        {renderAge()}
      </View>
    </View>
  );

  const renderUnit = () => (
    <View style={styles.inputContainerView}>
      <DropDownPicker
        useNativeAndroidPickerStyle
        data={UNIT_OPTIONS}
        label="Units"
        selected={
          UNIT_OPTIONS.find((option) => option.label === state.unitType).id
        }
        onValueChange={(selectedUnit) => {
          onChangeUnit(selectedUnit.label);
        }}
        labelStyle={styles.pickerItem}
        containerStyle={styles.picker}
      />
    </View>
  );

  const renderAge = () => (
    <View style={styles.inputContainerView}>
      {renderTextInput('Age', 'yr', getTextInputProps(2), { marginLeft: 0 })}
    </View>
  );

  const renderGenderAndWeight = () => (
    <View style={[styles.genderWeightView, styles.marginTop]}>
      {renderGender()}
      {renderWeight()}
    </View>
  );

  const renderGender = () => (
    <View>
      <DropDownPicker
        useNativeAndroidPickerStyle
        data={GENDER_OPTIONS}
        label="Assigned Sex"
        selected={getSelectedGender()}
        onValueChange={(selectedGender) => {
          onEditValue('sex', selectedGender.label);
        }}
        labelStyle={styles.pickerItem}
        containerStyle={styles.picker}
        numberOfLines={1}
      />
    </View>
  );

  const renderWeight = () => (
    <View style={{ marginTop: curvedScale(20) }}>
      {renderTextInput(
        'Weight',
        state.unitType === UNIT_TYPE.METRIC ? 'kg' : 'lb',
        getTextInputProps(getAllowedMaxInputLength(4, state.weight)),
        { marginLeft: 0 },
        true,
      )}
    </View>
  );

  const renderHeight = () => (
    <View style={styles.marginTop}>
      <Text style={styles.title}>Height</Text>
      {state.unitType === UNIT_TYPE.IMPERIAL ? (
        <View style={styles.heightView()}>
          <View style={styles.inputContainerView}>
            {renderTextInput('height_ft', 'ft', getTextInputProps(1), {
              marginLeft: 0,
            })}
          </View>

          <View style={styles.inputContainerView}>
            {renderTextInput(
              'height_in',
              'in',
              getTextInputProps(getAllowedMaxInputLength(3, state.height_in)),
              {
                marginLeft: 0,
              },
            )}
          </View>
        </View>
      ) : (
        <View style={styles.heightView()}>
          {renderTextInput(
            'height_cm',
            'cm',
            getTextInputProps(getAllowedMaxInputLength(4, state.height_cm)),
            { marginLeft: 0, width: '100%' },
            true,
          )}
        </View>
      )}
    </View>
  );

  const renderBodyMeasurements = () => (
    <View style={styles.marginTop}>
      <Text style={styles.title}>Body Measurements</Text>
      {renderMeasurementInputs()}
    </View>
  );

  const renderMeasurementInputs = () => {
    const isFemale = state.sex === GENDER_TYPE.FEMALE;
    switch (state.methodType) {
      case CALCULATION_TYPE.US_NAVY_METHOD:
        return (
          <>
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.US_NAVY_METHOD.neckCircumference.key,
              label: MethodKeysAndLabel.US_NAVY_METHOD.neckCircumference.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.US_NAVY_METHOD.waistCircumference.key,
              label: MethodKeysAndLabel.US_NAVY_METHOD.waistCircumference.label,
            })}
            {isFemale
              ? renderInputWithTooltip({
                key: MethodKeysAndLabel.US_NAVY_METHOD.hipCircumference.key,
                label:
                    MethodKeysAndLabel.US_NAVY_METHOD.hipCircumference.label,
                showToolTip: false,
              })
              : null}
          </>
        );
      case CALCULATION_TYPE.JACKSON_POLLOCK_3_SITE:
        return (
          <>
            {isFemale
              ? renderInputWithTooltip({
                key: MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.triceps.key,
                label:
                    MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.triceps.label,
              })
              : renderInputWithTooltip({
                key: MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.chest.key,
                label: MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.chest.label,
              })}
            {isFemale
              ? renderInputWithTooltip({
                key: MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.suprailiac.key,
                label:
                    MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.suprailiac.label,
              })
              : renderInputWithTooltip({
                key: MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.abdomen.key,
                label:
                    MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.abdomen.label,
              })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.thigh.key,
              label: MethodKeysAndLabel.JACKSON_POLLOCK_3_SITE.thigh.label,
            })}
          </>
        );
      case CALCULATION_TYPE.JACKSON_POLLOCK_7_SITE:
        return (
          <>
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.chest.key,
              label: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.chest.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.midAxillary.key,
              label:
                MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.midAxillary.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.subscapular.key,
              label:
                MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.subscapular.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.triceps.key,
              label: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.triceps.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.abdomen.key,
              label: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.abdomen.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.suprailiac.key,
              label: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.suprailiac.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.thigh.key,
              label: MethodKeysAndLabel.JACKSON_POLLOCK_7_SITE.thigh.label,
            })}
          </>
        );
      case CALCULATION_TYPE.DURNIN_WOMERSLEY_4_SITE:
        return (
          <>
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.biceps.key,
              label: MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.biceps.label,
              showToolTip: false,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.subscapular.key,
              label:
                MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.subscapular.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.triceps.key,
              label: MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.triceps.label,
            })}
            {renderInputWithTooltip({
              key: MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.suprailiac.key,
              label:
                MethodKeysAndLabel.DURNIN_WOMERSLEY_4_SITE.suprailiac.label,
            })}
          </>
        );
      default:
        return null;
    }
  };

  const renderInputWithTooltip = ({ key, label, showToolTip = true }) => (
    <View style={styles.heightView(true)}>
      {renderMeasurementsTextInput({
        key,
        label,
        textInputProps: getTextInputProps(
          getAllowedMaxInputLength(3, state[key]),
        ),
      })}
      {showToolTip ? (
        <TouchableOpacity
          style={styles.tooltipView}
          onPress={() => {
            setTooltipKey(key);
          }}
        >
          <Image source={tooltipIcon} style={styles.tooltip} />
        </TouchableOpacity>
      ) : null}
    </View>
  );

  const renderToolTipInfo = () => (
    <BodyFatTooltip
      toolTipKey={toolTipKey}
      onModalClose={() => setTooltipKey('')}
    />
  );

  const renderPageContent = () => (
    <KeyboardAwareScrollView
      showsVerticalScrollIndicator={false}
      keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
    >
      {renderPageHeader()}
      <View style={styles.pageView}>
        {renderBasicInfo()}
        {renderGenderAndWeight()}
        {state.methodType === CALCULATION_TYPE.US_NAVY_METHOD
          ? renderHeight()
          : null}
        {renderBodyMeasurements()}
      </View>
    </KeyboardAwareScrollView>
  );

  const renderPageFooter = () => (
    <BodyFatNavigationFooter
      onPressPrevious={() => navigation.goBack()}
      onPressNext={() => onPressNext()}
    />
  );

  let buttonView = renderPageFooter();
  if (isAndroid && keyboardVisible) {
    buttonView = null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
      >
        {renderProgressBar()}
        {renderPageContent()}
        {toolTipKey ? renderToolTipInfo() : null}
      </KeyboardAwareScrollView>
      {buttonView}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  headerLeft: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    marginLeft: 10,
    width: 100,
  },
  headerContainer: {
    paddingHorizontal: curvedScale(20),
    paddingTop: curvedScale(30),
    paddingBottom: curvedScale(30),
    backgroundColor: colors.offWhite,
  },
  headerTitleBold: {
    color: colors.eerieBlack,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    fontWeight: '700',
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
    alignSelf: 'center',
  },
  headerRight: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    marginRight: 10,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  barStyle: {
    borderRadius: 0,
  },
  progressBackground: {
    borderRadius: 0,
    width: '100%',
    borderWidth: 0,
    backgroundColor: colors.nasmBlue,
    height: 12,
  },
  pageView: {
    paddingHorizontal: curvedScale(20),
  },
  title: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
    marginTop: curvedScale(10),
  },
  picker: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    height: curvedScale(40),
    marginVertical: useVerticalFlex ? curvedScale(5) : 0,
  },
  pickerItem: {
    color: colors.black,
    fontSize: 14,
    fontFamily: 'Avenir-Light',
    fontWeight: '500',
    paddingHorizontal: curvedScale(8),
    textAlign: 'left',
  },
  weightContainer: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginTop: curvedScale(10),
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    padding: curvedScale(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(40),
    marginTop: useVerticalFlex ? 10 : 0,
  },
  inputMeasurementsContainer: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    padding: curvedScale(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(40),
    // marginTop: useVerticalFlex ? 10 : 0,
    width: '88%',
  },
  customInputContainer: {
    marginLeft: useVerticalFlex ? 0 : curvedScale(10),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
  },
  inputLabel: {
    width: '50%',
  },
  input: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
  },
  customTextInput: {
    width: '90%',
    textAlign: 'right',
    paddingVertical: 0,
  },
  heightView: (useRow = false) => ({
    flexDirection: useVerticalFlex && !useRow ? 'column' : 'row',
    flex: 1,
    alignItems: 'center',
    marginTop: curvedScale(10),
    justifyContent: 'space-between',
  }),
  tooltipView: {
    width: curvedScale(25),
    height: curvedScale(25),
  },
  tooltip: {
    width: '100%',
    height: '100%',
  },
  genderWeightView: {
    width: '100%',
    marginTop: curvedScale(10),
  },
  inputView: {
    flexDirection: 'row',
    width: '50%',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  marginTop: {
    marginTop: curvedScale(20),
  },
  inputContainerView: {
    width: useVerticalFlex ? '100%' : '48%',
  },
});

BodyFatCalculatorField.propTypes = propTypes;
BodyFatCalculatorField.defaultProps = defaultProps;

const BodyFatFieldRoot = (props) => (
  <BodyFatContextProvider>
    <BodyFatCalculatorField {...props} />
  </BodyFatContextProvider>
);

BodyFatFieldRoot.propTypes = propTypes;

export default BodyFatFieldRoot;
