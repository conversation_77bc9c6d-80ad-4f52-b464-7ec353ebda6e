import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  Text,
  Image,
  View,
  FlatList,
  Dimensions,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import PropTypes from 'prop-types';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../../components/HeaderRightButton';
import nasm from '../../../../dataManager/apiConfig';
import LoadingSpinner from '../../../../components/LoadingSpinner';
import { CALCULATOR_CONTEXTS } from '../../../../reducers/calculatorContextReducer';
import { BodyFatConstants } from './BodyFatConstants';
import { curvedScale } from '../../../../util/responsive';
import { colors } from '../../../../styles';
import {
  calculateBodyFatRange,
  getBodyFatTableValues,
} from './BodyFatCalculations';
import { GENDER_TYPE, CALCULATION_TYPE } from '../../../../types/BodyFatTypes';

const numOfColumns = 3;
const screenWidth = Dimensions.get('screen').width;
const columnWidth = screenWidth / numOfColumns;
const imgBodyFatResult = require('../../../../assets/bodyFat-results.png');
const disclaimerIcon = require('../../../../assets/error.png');

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    pop: PropTypes.func,
    popToTop: PropTypes.func,
    setOptions: PropTypes.func,
  }),
  route: PropTypes.shape({
    params: PropTypes.shape({
      bodyFatData: PropTypes.object,
      onReset: PropTypes.func,
      comingFrom: PropTypes.string,
    }),
  }),
  calculatorContext: PropTypes.object,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  bodyFatData: PropTypes.object,
};

const defaultProps = {
  navigation: {},
  route: {},
  calculatorContext: null,
  bodyFatData: null,
};

class BodyFatResultTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      bodyFatData: props?.route?.params?.bodyFatData || props?.bodyFatData,
      isLoading: false,
    };
  }

  componentDidMount() {
    analytics().logEvent('screen_view', {
      screen_name: 'body_fat_calculator_results',
    });
    if (this.props?.route?.params?.bodyFatData) {
      this.props.navigation.setOptions({
        headerLeft: this.renderHeaderLeft,
        headerRight: this.renderHeaderRight,
        headerTitle: 'Body Fat Calculator',
        headerTitleStyle: styles.headerTitle,
      });
    }
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      title={
        this.props?.route?.params?.comingFrom
        === BodyFatConstants.BodyFatHistory
          ? 'Back'
          : 'Cancel'
      }
      onPress={() => {
        if (
          this.props?.route?.params?.comingFrom
          === BodyFatConstants.BodyFatHistory
        ) {
          this.props.navigation.pop();
        } else if (
          this.props.calculatorContext?.type
          === CALCULATOR_CONTEXTS.SELECTED_CLIENT
        ) {
          this.props.navigation.pop(3);
        } else {
          this.props.navigation.popToTop();
        }
      }}
      titleStyle={styles.headerButtonsText}
    />
  );

  renderHeaderRight = () => {
    if (
      this.props?.route?.params?.comingFrom === BodyFatConstants.BodyFatHistory
    ) {
      return null;
    }

    if (
      this.props.calculatorContext?.type === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT
    ) {
      return (
        <HeaderRightButton
          title="Reset"
          onPress={this.props.route?.params?.onReset}
          titleStyle={styles.headerButtonsText}
        />
      );
    }

    const userId = this.props?.selectedClient?.id;
    const { bodyFatData } = this.state;
    return (
      <HeaderRightButton
        title="Save"
        onPress={async () => {
          try {
            this.setState({ isLoading: true });
            const response = await nasm.api.saveBodyFatResults(
              bodyFatData,
              userId,
            );
            if (response !== null) {
              this.setState({ isLoading: false });
              this.props.navigation.navigate(BodyFatConstants.BodyFatTabView);
              analytics().logEvent('body_fat_calculator_save');
            }
          } catch (error) {
            this.setState({ isLoading: false });
            Alert.alert(
              'Error',
              error.message || 'Something went wrong! Please try again later.',
            );
          }
        }}
        titleStyle={styles.headerButtonsText}
      />
    );
  };

  renderTopHeader = (bodyfat_category) => (
    <View>
      <View style={styles.topHeader}>
        <View style={styles.headerBodyFatResultsView}>
          <Text style={styles.headerBodyFatResultsText(colors.black)}>
            Body Fat
          </Text>
        </View>
        <View style={styles.headerResultValueContainer}>
          <View style={styles.headerStatusContainer}>
            <View style={styles.headerStatusColorView(6)} />
            <Text> </Text>
            <View>
              <Text style={styles.headerBodyFatText}>
                {`${bodyfat_category.category}`}
              </Text>
            </View>
          </View>
          <View style={styles.headerBodyFatViewStyle}>
            <Image source={imgBodyFatResult} style={styles.headerIconStyle} />
            <Text
              style={styles.headerBodyFatValueText(colors.black, 18, 'bold')}
            >
              {Number(this.state.bodyFatData.bodyfat_score).toFixed(1)}
              %
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.bottomHeader}>
        <Text style={styles.headerBodyFatResultsText(colors.fillDarkGrey)}>
          Results
        </Text>
      </View>
      {this.renderResultUserInfo()}
    </View>
  );

  renderResultUserInfo = () => {
    const { bodyFatData } = this.state;
    const { age, gender } = bodyFatData;
    const tableData = getBodyFatTableValues(age, GENDER_TYPE[gender]);
    return (
      <View style={styles.resultUserInfoView}>
        <Text style={styles.resultUserInfoLabel}>
          {`${gender}: ${tableData.ageStart}-${tableData.ageEnd} y/o`}
        </Text>
      </View>
    );
  };

  renderListHeaderItemView = (title) => (
    <View style={styles.listHeaderItemView}>
      <Text style={styles.listHeaderItemTitle}>{title}</Text>
    </View>
  );

  renderHorizontalSeparator = () => <View style={styles.horizontalSeparator} />;

  renderListHeaderComponent = () => (
    <View style={styles.listHeaderContainer}>
      {this.renderListHeaderItemView('Classification')}
      {this.renderHorizontalSeparator()}
      {this.renderListHeaderItemView('From (%)')}
      {this.renderHorizontalSeparator()}
      {this.renderListHeaderItemView('To (%)')}
    </View>
  );

  renderListItemView = ({ label, isSelected, showStatusColor }) => (
    <View style={styles.listItemView}>
      <View style={styles.listItemContainer}>
        {isSelected && showStatusColor ? (
          <View style={styles.headerStatusColorView(10)} />
        ) : null}
        <View style={{ marginLeft: curvedScale(10) }}>
          <Text style={styles.lisItemValue(isSelected)}>{label}</Text>
        </View>
      </View>
    </View>
  );

  renderListItem = ({ item, index }) => {
    let separator = '';
    if (index > 4) {
      separator = '>';
    }
    return (
      <View
        style={{
          ...styles.listItemContainer,
          backgroundColor: index % 2 === 1 ? colors.white : colors.paleGray2,
        }}
      >
        {this.renderListItemView({
          label: `${item.category}`,
          isSelected: item.isSelected,
          showStatusColor: true,
        })}
        {this.renderHorizontalSeparator()}
        {this.renderListItemView({
          label: `${item.rangeStart}`,
          isSelected: item.isSelected,
        })}
        {this.renderHorizontalSeparator()}
        {this.renderListItemView({
          label: `${separator}${
            item.rangeEnd === Number.MAX_SAFE_INTEGER
              ? item.rangeStart
              : item.rangeEnd
          }`,
          isSelected: item.isSelected,
        })}
      </View>
    );
  };

  getTableData = () => {
    const { bodyFatData } = this.state;
    const { bodyfat_score, age, gender } = bodyFatData;
    const bodyFat_category = calculateBodyFatRange(
      bodyfat_score,
      age,
      GENDER_TYPE[gender],
    );
    const BODY_FAT_CATEGORIES = Object.values(
      getBodyFatTableValues(age, GENDER_TYPE[gender]).data,
    );
    const index = BODY_FAT_CATEGORIES.findIndex(
      (cat) => cat.rangeStart === bodyFat_category.rangeStart,
    );
    const scoreCategory = BODY_FAT_CATEGORIES[index];
    BODY_FAT_CATEGORIES[index] = {
      ...scoreCategory,
      isSelected: true,
    };
    return {
      bodyFat_category,
      BODY_FAT_CATEGORIES,
    };
  };

  renderFooter = () => {
    const calculationMethod = CALCULATION_TYPE[this.state.bodyFatData?.method_type];
    if (calculationMethod?.length) {
      return (
        <View style={styles.footerView}>
          <Image source={disclaimerIcon} style={styles.disclaimer} />
          <Text style={styles.disclaimerDescription}>
            Body fat percentage calculated using the
            {' '}
            {calculationMethod}
          </Text>
        </View>
      );
    }
    return null;
  };

  render() {
    const data = this.getTableData();
    return (
      <SafeAreaView style={styles.container}>
        {this.renderTopHeader(data.bodyFat_category)}
        <FlatList
          data={data.BODY_FAT_CATEGORIES}
          renderItem={this.renderListItem}
          ListHeaderComponent={this.renderListHeaderComponent}
          ListFooterComponent={this.renderFooter}
        />
        <LoadingSpinner
          visible={this.state.isLoading}
          size="large"
          backgroundColor="rgba(0, 0, 0, 0.25)"
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerButtonsText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.paleGray2,
    paddingLeft: curvedScale(20),
    paddingRight: curvedScale(20),
    paddingTop: curvedScale(20),
  },
  bottomHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.paleGray2,
    paddingLeft: curvedScale(20),
    paddingRight: curvedScale(20),
    paddingBottom: curvedScale(20),
  },
  resultUserInfoView: {
    backgroundColor: colors.disclaimerGrey,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: curvedScale(45),
  },
  resultUserInfoLabel: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(17),
  },
  headerBodyFatResultsView: {
    width: '40%',
  },
  headerBodyFatResultsText: (color) => ({
    color,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(22),
    fontWeight: '700',
    textAlign: 'left',
    marginRight: curvedScale(5),
  }),
  headerResultValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: '60%',
  },
  headerStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '50%',
  },
  headerStatusColorView: (size) => ({
    width: curvedScale(size),
    height: curvedScale(size),
    borderRadius: curvedScale(size) / 2,
    backgroundColor: colors.greenselect,
  }),
  headerBodyFatText: {
    color: colors.black,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(15),
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerBodyFatViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: curvedScale(10),
  },
  headerIconStyle: {
    width: curvedScale(25),
    height: curvedScale(25),
    marginRight: curvedScale(5),
  },
  headerBodyFatValueText: (color, fontSize, fontWeight) => ({
    color,
    fontSize: curvedScale(fontSize),
    fontWeight,
    fontFamily: 'Avenir',
    textAlign: 'right',
  }),
  listHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listHeaderItemView: {
    width: columnWidth,
    borderBottomWidth: 1,
    borderBottomColor: colors.textPlaceholder,
    backgroundColor: colors.white,
    paddingVertical: curvedScale(15),
  },
  listHeaderItemTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(14),
    fontWeight: '700',
    textAlign: 'center',
  },
  horizontalSeparator: {
    width: 1,
    height: '100%',
    backgroundColor: colors.textPlaceholder,
  },
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemView: {
    width: columnWidth,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.paleGray2,
    height: curvedScale(60),
  },
  lisItemValue: (isSelected) => ({
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    fontWeight: isSelected ? 'bold' : '400',
    textAlign: 'center',
  }),
  footerView: {
    margin: curvedScale(20),
    marginBottom: curvedScale(100),
    height: curvedScale(46),
    backgroundColor: colors.disclaimerGrey,
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    borderRadius: curvedScale(5),
  },
  disclaimer: {
    marginRight: curvedScale(5),
    marginLeft: curvedScale(10),
  },
  disclaimerDescription: {
    color: colors.eerieBlack,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(11),
    marginRight: curvedScale(40),
    marginVertical: curvedScale(7),
  },
});

BodyFatResultTable.propTypes = propTypes;
BodyFatResultTable.defaultProps = defaultProps;

const mapStateToProps = ({ calculatorContext, selectedClient }) => ({
  calculatorContext,
  selectedClient,
});
const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(BodyFatResultTable);
