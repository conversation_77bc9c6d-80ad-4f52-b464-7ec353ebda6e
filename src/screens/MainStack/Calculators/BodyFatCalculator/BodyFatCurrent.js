import React, { Component } from 'react';
import {
  View, Alert, ActivityIndicator, SafeAreaView,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import nasm from '../../../../dataManager/apiConfig';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import FloatingButton from '../../../../components/FloatingButton';
import { colors } from '../../../../styles';
import { BodyFatConstants } from './BodyFatConstants';
import BodyFatResultTable from './BodyFatResultTable';
import BodyFatFTU from './BodyFatFTU';
import {
  getKeyByValue,
  get_height_from_inches,
} from '../../../../util/CalculatorUtils';
import { UNIT_TYPE } from '../../../../types/BodyFatTypes';

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

const defaultProps = {};

class BodyFatCurrent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
      bodyFatData: null,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', {
        screen_name: 'body_fat_calculator_current',
      });
      this.getCurrentBodyFatResults();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  getCurrentBodyFatResults = async () => {
    try {
      this.setState({ isLoading: true });
      const userId = this.props?.selectedClient?.id;
      const response = await nasm.api.getCurrentBodyFatResults(userId);
      if (
        response
        && Object.prototype.hasOwnProperty.call(response, 'bodyfat_score')
      ) {
        const bodyFatData = {
          ...response,
          methodType: response.method_type,
          unitType: response.unit_type,
          height_cm:
            response.unit_type === getKeyByValue(UNIT_TYPE, UNIT_TYPE.METRIC)
              ? response.height
              : '',
          height_ft:
            response.unit_type === getKeyByValue(UNIT_TYPE, UNIT_TYPE.IMPERIAL)
              ? get_height_from_inches(response.height).feet
              : '',
          height_in:
            response.unit_type === getKeyByValue(UNIT_TYPE, UNIT_TYPE.IMPERIAL)
              ? get_height_from_inches(response.height).inches
              : '',
          bodyfat_score: response.bodyfat_score,
        };
        this.setState({
          bodyFatData,
          isLoading: false,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch (error) {
      this.setState({ isLoading: false });
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => {
        this.props.navigation.pop();
      }}
      titleStyle={styles.headerButtonText}
    />
  );

  onPressAdd = () => {
    this.props.navigation.navigate({
      name: BodyFatConstants.BodyFatCalculator,
      merge: true,
    });
  };

  render() {
    const { isLoading, bodyFatData } = this.state;
    if (isLoading) {
      return (
        <View style={styles.loadingView}>
          <ActivityIndicator animating size="large" />
        </View>
      );
    }
    return (
      <SafeAreaView style={styles.container}>
        {bodyFatData ? (
          <BodyFatResultTable bodyFatData={bodyFatData} />
        ) : (
          <BodyFatFTU />
        )}
        <FloatingButton onPress={this.onPressAdd} />
      </SafeAreaView>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

BodyFatCurrent.propTypes = propTypes;
BodyFatCurrent.defaultProps = defaultProps;

const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(BodyFatCurrent);
