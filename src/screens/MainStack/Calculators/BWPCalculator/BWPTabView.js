import React, { Component } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';

// Components
import { View, Dimensions } from 'react-native';
import Current from './BWPCurrent';
import History from './BWPHistory';

// Styles
import { colors, materialTabBarOptions } from '../../../../styles';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

class BWPTabView extends Component {
  static router = TabNav.router;

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    return {
      title: 'Nutrition',
      headerLeft: renderHeaderLeft,
    };
  };

  componentDidMount() {
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  updateHeaderLeft = (headerLeft) => {
    this.props.navigation.setParams({
      renderHeaderLeft: headerLeft,
    });
  };

  render() {
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
        style={materialTabBarOptions.tabBarOptions.tabBarStyle}
      >
        <TabNav.Screen name="Current" component={Current} />
        <TabNav.Screen name="History" component={History} />
      </TabNav.Navigator>
    );
  }
}

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

export default BWPTabView;
