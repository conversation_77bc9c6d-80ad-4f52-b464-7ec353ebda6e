import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import PropTypes from 'prop-types';
import moment from 'moment';
// eslint-disable-next-line import/no-extraneous-dependencies
import _ from 'lodash';
import {
  VictoryAxis,
  VictoryChart,
  VictoryLine,
  VictoryScatter,
} from 'victory-native';
import { clearFormData } from '../../../../actions';
import { UnitType } from '../../../../types/BWPTypes';
import {
  format_thousands,
  calculate_BMR,
  calculate_macro_allocations,
  calculate_adjusted_caloric_requirement,
} from '../../../../util/CalculatorUtils';
import BWPConstants from './BWPConstants';
import ChartService from './GoalServices/ChartService';
import {
  curvedScale,
  scaleHeight,
  scaleWidth,
} from '../../../../util/responsive';
import AnimatedCircularProgress from '../../../../components/CircularAnimated/AnimatedCircularProgress';
import nasm from '../../../../dataManager/apiConfig';
import HeaderRightButton from '../../../../components/HeaderRightButton';
import { colors } from '../../../../styles';
import { logScreenException } from '../../../../util/logging';
import { CALCULATOR_CONTEXTS } from '../../../../reducers/calculatorContextReducer';

const imgHeart = require('../../../../resources/heart.png');
const imgFire = require('../../../../resources/fire.png');

const windowWidth = Dimensions.get('window').width;

const stateSelector = (state) => state;

const propTypes = {
  headerTitle: PropTypes.string,
};

const defaultProps = {
  headerTitle: 'Nutrition',
};

const Results = ({ headerTitle = 'Nutrition' }) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { bwpCalculator, selectedClient, calculatorContext } = useSelector(stateSelector);
  const [isSavingBWP, setSavingBWP] = useState(false);
  const [graphData, setGraphData] = useState(null);

  const formData = {
    ...bwpCalculator?.data,
    height:
      bwpCalculator?.data?.units === UnitType.US
        ? Number(bwpCalculator?.data?.height_ft) * 12
          + Number(bwpCalculator?.data?.height_in)
        : Number(bwpCalculator?.data?.height_cm),
  };

  const bmr = calculate_BMR(formData, false);
  const tdee = calculate_BMR(formData, true);
  const adjustedCaloricRequirement = calculate_adjusted_caloric_requirement(
    tdee,
    formData?.weight || 0,
    formData?.goalWeight || 0,
    formData?.numberOfDays || 180,
  );
  const todayDate = moment(new Date()).format('MM/DD/YYYY');
  const endDate = formData.goalDate || '';

  // Carbohydrates: 1 gram = 4 calories
  const carbAllocations = calculate_macro_allocations(
    formData?.carbohydratesPercent || 0,
    adjustedCaloricRequirement,
    4,
  );

  // Fat: 1 gram = 9 calories
  const fatAllocations = calculate_macro_allocations(
    formData?.fatsPercent || 0,
    adjustedCaloricRequirement,
    9,
  );

  // Protein: 1 gram = 4 calories
  const proteinAllocations = calculate_macro_allocations(
    formData?.proteinPercent || 0,
    adjustedCaloricRequirement,
    4,
  );

  const DAY_INTERVAL = 30;

  useEffect(() => {
    const chartData = lineChartData();
    setGraphData(chartData);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    navigation.setOptions({
      title: headerTitle,
      headerTitleStyle: styles.headerTitle,
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSavingBWP]);

  const cancelCalculation = () => {
    closeBwpCalculator();
  };

  const returnToDashboard = async () => {
    const clientUserId = selectedClient?.id;
    if (!clientUserId) {
      closeBwpCalculator();
      return;
    }
    await saveBWPRecord(clientUserId);
  };

  const saveBWPRecord = async (clientUserId) => {
    // if we have client user ID for this screen in redux, save their BWP results
    setSavingBWP(true);
    try {
      const requestData = {
        nutrition: {
          carbohydrates: Math.round(carbAllocations.grams),
          protein: Math.round(proteinAllocations.grams),
          fat: Math.round(fatAllocations.grams),
          calories: Math.round(adjustedCaloricRequirement),
        },
        activity_level_id: formData.currentActivityLevelId,
        unit_height: formData.units === UnitType.METRIC ? 'cm' : 'in',
        unit_weight: formData.units === UnitType.METRIC ? 'kg' : 'lb',
        age: formData.age,
        gender: formData.sex.toUpperCase(),
        height: formData.height,
        start_weight: formData.weight,
        goal_weight: formData.goalWeight,
        start_date: moment().format('YYYY-MM-DD'),
        end_date: moment(formData.goalDate, 'MM/DD/YYYY').format('YYYY-MM-DD'),
        number_of_days: formData.numberOfDays,
        basal_metabolic_rate: parseInt(
          format_thousands(bmr, false).replace(',', ''),
          10,
        ),
        total_daily_energy_expenditure: parseInt(
          format_thousands(tdee, false).replace(',', ''),
          10,
        ),
        daily_goal_intake: parseInt(
          format_thousands(adjustedCaloricRequirement, false).replace(',', ''),
          10,
        ),
      };
      await nasm.api.saveBWPResults(requestData, clientUserId);
    } catch (err) {
      Alert.alert(
        'Error',
        "We're having trouble saving the BWP results. Please try again later.",
      );
      logScreenException('BWP RESULTS', err);
    } finally {
      setSavingBWP(false);
      closeBwpCalculator();
    }
  };

  // removes all 5 screens relating to BWP calculator
  const closeBwpCalculator = (popCount = 5) => {
    dispatch(clearFormData());
    navigation.pop(popCount);
  };

  const getWeightUnit = () => (bwpCalculator?.data?.units === UnitType.METRIC ? 'kg' : 'lbs');

  const lineChartData = () => {
    const lineChartLabels = [];
    let dataArray = [];

    if (formData) {
      const chartService = new ChartService(formData);
      chartService.processGoal();
      chartService.generateChartData();
      const chartData = chartService.chartArray;

      let combineLastDataPoints = false;
      for (let i = 0; i < formData.numberOfDays; i += DAY_INTERVAL) {
        const momDateValue = moment(endDate, 'MM/DD/YYYY').isAfter(moment())
          ? moment(new Date()).add(i, 'days')
          : moment(new Date()).subtract(i, 'days');
        const dateValue = momDateValue.toDate();
        dataArray.push({
          x: moment(dateValue).format('MMM DD'),
          y: chartData[i][2],
        });

        // If there are 30 days or less left until the goal number of days, then get the chart data for
        // the last day and add it to the array along with its corresponding chart label
        const nextDayIndex = i + DAY_INTERVAL;
        if (nextDayIndex >= formData.numberOfDays) {
          const lastIndex = formData.numberOfDays - 1;
          const momDateValue2 = moment(endDate, 'MM/DD/YYYY').isAfter(moment())
            ? moment(new Date()).add(formData.numberOfDays, 'days')
            : moment(new Date()).subtract(formData.numberOfDays, 'days');

          const dateValue2 = momDateValue2.toDate();

          // Add extra chart label as it may not fall on a 30 day increment
          lineChartLabels.push(dateValue2);

          dataArray.push({
            x: moment(dateValue2).format('MMM DD'),
            y: chartData[lastIndex][2],
          });

          // If there is only a couple of days from the 30 day interval and the last day, remove the 30 day interval
          // data point or the last two labels will overlap when rendered on the page
          const dayDiff = momDateValue2.diff(momDateValue, 'days');
          if (dayDiff <= 7) {
            combineLastDataPoints = true;
            dataArray = _.reject(dataArray, { x: dateValue });
          }
        }

        // Add next chart label at 30 day increments, unless the last chart label was added above or we are at 180 days
        if (
          nextDayIndex !== formData.numberOfDays
          && nextDayIndex <= 180
          && !combineLastDataPoints
        ) {
          lineChartLabels.push(dateValue);
        }
      }
    }

    return dataArray;
  };

  const renderHeaderLeft = () => (
    <TouchableOpacity disabled={isSavingBWP} onPress={cancelCalculation}>
      <Text style={styles.headerText('left')}>Cancel</Text>
    </TouchableOpacity>
  );

  const renderHeaderRight = () => {
    if (isSavingBWP) {
      return renderLoader();
    }
    if (calculatorContext?.type === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      return (
        <HeaderRightButton
          title="Reset"
          onPress={() => closeBwpCalculator(4)}
          titleStyle={styles.headerText('right')}
        />
      );
    }
    return (
      <HeaderRightButton
        onPress={returnToDashboard}
        title="Save"
        titleStyle={styles.headerText('right')}
      />
    );
  };

  const renderLoader = () => (
    <View style={styles.loaderView}>
      <ActivityIndicator animating color={colors.white} size="small" />
    </View>
  );

  const renderClientInfo = (opacity) => (
    <View>
      <Text numberOfLines={1} style={styles.clientName}>
        {`${formData.clientName}'s`}
        {' '}
        <Text style={{ opacity }}>Results</Text>
      </Text>
      <View style={[styles.horizontalView, styles.clientInfoRow]}>
        <Text style={styles.clientInfo}>{`${formData.sex}`}</Text>
        <Text style={styles.clientInfo}>
          {`${Number(formData.weight).toFixed(1)} ${getWeightUnit()}`}
        </Text>
        <Text style={styles.clientInfo}>
          {`${
            formData.units === UnitType.US
              ? `${formData.height_ft}'${formData.height_in}" ht`
              : `${formData.height_cm} cm`
          }`}
        </Text>
        <Text style={styles.clientInfo}>{`${formData.age} age`}</Text>
        <Text> </Text>
      </View>
    </View>
  );

  const renderCalorieCard = (color, title, icon, result, description) => (
    <View style={styles.cardStyle}>
      <View style={styles.cardColor(color, true)} />
      <View style={styles.cardContent}>
        <View style={styles.horizontalView}>
          <Text style={styles.cardTitle}>
            {title}
            *
          </Text>
          <View style={styles.sectionResult}>
            <Image
              style={[styles.icon, icon === imgFire && styles.fireIcon]}
              source={icon}
            />
            <Text style={[styles.cardTitle, styles.cardResult]}>
              {result}
              {' '}
              <Text style={styles.unit}>Cal</Text>
            </Text>
          </View>
        </View>
        <Text style={styles.cardDescription}>{description}</Text>
      </View>
    </View>
  );

  const renderAxes = (key, dependentAxis, showGrid) => (
    <VictoryAxis
      key={key}
      dependentAxis={dependentAxis}
      style={styles.axesStyle(showGrid)}
    />
  );

  // eslint-disable-next-line consistent-return
  const renderGraphLine = (data, color) => {
    if (data && data.length > 1) {
      return (
        <VictoryLine
          style={{ data: { stroke: color, strokeWidth: 2 } }}
          data={data}
          animate={{
            duration: 300,
          }}
        />
      );
    }
  };

  // eslint-disable-next-line consistent-return
  const renderGraphPoints = (data, color, symbol) => {
    if (data) {
      return (
        <VictoryScatter
          style={{ data: { fill: color } }}
          size={curvedScale(4)}
          symbol={symbol}
          data={data}
        />
      );
    }
  };

  const renderGoalWeightCard = (color, title, result) => {
    const dataLength = graphData?.length || 0;
    const chartWidth = dataLength * curvedScale(45);
    return (
      <View style={styles.cardStyle}>
        <View style={styles.cardColor(color)}>
          <View style={styles.horizontalView}>
            <Text style={[styles.cardTitle, styles.colorWhite]}>{title}</Text>
          </View>
          <View style={styles.horizontalView}>
            <Text style={[styles.unit, styles.colorWhite]}>
              {`Start: ${todayDate}`}
            </Text>
            <Text style={[styles.unit, styles.colorWhite]}>
              {`End: ${endDate}`}
            </Text>
            <View style={styles.sectionResult}>
              <Text
                style={[
                  styles.cardTitle,
                  styles.cardResult,
                  styles.colorWhite,
                ]}
              >
                {Number(result).toFixed(1)}
                {' '}
                <Text style={[styles.unit, styles.colorWhite]}>
                  {getWeightUnit()}
                </Text>
              </Text>
            </View>
          </View>
        </View>
        <ScrollView horizontal>
          <VictoryChart
            height={scaleHeight(30)}
            domainPadding={curvedScale(15)}
            width={chartWidth < windowWidth ? windowWidth : chartWidth}
            padding={{
              left: curvedScale(40),
              right: curvedScale(50),
              top: curvedScale(10),
              bottom: curvedScale(40),
            }}
          >
            {renderAxes('x-axis')}
            {renderAxes('y-axis', true, true)}
            {renderGraphLine(graphData, colors.darkPurple)}
            {renderGraphPoints(graphData, colors.darkPurple, 'circle')}
          </VictoryChart>
        </ScrollView>
      </View>
    );
  };

  const renderDailyGoalCard = (color, title, result) => (
    <View style={[styles.cardStyle, { marginBottom: curvedScale(100) }]}>
      <View style={styles.cardColor(color, true)}>
        <View style={styles.horizontalView}>
          <Text style={[styles.cardTitle, styles.colorWhite]}>{title}</Text>
          <View style={styles.sectionResult}>
            <Text
              style={[styles.cardTitle, styles.cardResult, styles.colorWhite]}
            >
              {result}
              {' '}
              <Text style={[styles.unit, styles.colorWhite]}>Cal</Text>
            </Text>
          </View>
        </View>
      </View>
      <View>
        <View style={[styles.titleView, styles.macrosTitle]}>
          <Text style={styles.title}>Macros %</Text>
        </View>
        <View style={styles.chartParentView}>
          <View style={styles.chartView}>
            <AnimatedCircularProgress
              size={curvedScale(90)}
              width={5}
              fill={formData.carbohydratesPercent}
              lineCap="round"
              rotation={0}
              tintColor={colors.darkPurple}
              backgroundColor={colors.loadingStateGray}
            >
              {() => (
                <View style={styles.titleView}>
                  <Text style={styles.title}>Carbs</Text>
                  <Text style={styles.title}>
                    {formData.carbohydratesPercent}
                  </Text>
                </View>
              )}
            </AnimatedCircularProgress>
            <View style={styles.calView}>
              <Text style={styles.caltitle}>
                {Math.round(carbAllocations.calories)}
                {' '}
                <Text style={styles.unit}>cal</Text>
              </Text>
              <Text style={[styles.caltitle, styles.macros]}>
                {Math.round(carbAllocations.grams)}
                {' '}
                <Text style={styles.unit}>g</Text>
              </Text>
            </View>
          </View>
          <View style={styles.chartView}>
            <AnimatedCircularProgress
              size={curvedScale(90)}
              width={5}
              fill={formData.fatsPercent}
              lineCap="round"
              rotation={0}
              tintColor={colors.nasmBlue}
              backgroundColor={colors.loadingStateGray}
            >
              {() => (
                <View style={styles.titleView}>
                  <Text style={styles.title}>Fats</Text>
                  <Text style={styles.title}>{formData.fatsPercent}</Text>
                </View>
              )}
            </AnimatedCircularProgress>
            <View style={styles.calView}>
              <Text style={styles.caltitle}>
                {Math.round(fatAllocations.calories)}
                {' '}
                <Text style={styles.unit}>cal</Text>
              </Text>
              <Text style={[styles.caltitle, styles.macros]}>
                {Math.round(fatAllocations.grams)}
                {' '}
                <Text style={styles.unit}>g</Text>
              </Text>
            </View>
          </View>
          <View style={styles.chartView}>
            <AnimatedCircularProgress
              size={curvedScale(90)}
              width={5}
              fill={formData.proteinPercent}
              lineCap="round"
              rotation={0}
              tintColor={colors.avatarOrange}
              backgroundColor={colors.loadingStateGray}
            >
              {() => (
                <View style={styles.titleView}>
                  <Text style={styles.title}>Protein</Text>
                  <Text style={styles.title}>{formData.proteinPercent}</Text>
                </View>
              )}
            </AnimatedCircularProgress>
            <View style={styles.calView}>
              <Text style={styles.caltitle}>
                {Math.round(proteinAllocations.calories)}
                {' '}
                <Text style={styles.unit}>cal</Text>
              </Text>
              <Text style={[styles.caltitle, styles.macros]}>
                {Math.round(proteinAllocations.grams)}
                {' '}
                <Text style={styles.unit}>g</Text>
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );

  const renderPageContent = () => (
    <ScrollView showsVerticalScrollIndicator={false}>
      {renderClientInfo(0.5)}
      {renderCalorieCard(
        colors.duskBlue,
        BWPConstants.BMR,
        imgHeart,
        format_thousands(bmr, false),
        'Daily requirement to function without physical activity.',
      )}
      {renderCalorieCard(
        colors.darkGreen,
        BWPConstants.TDEE,
        imgFire,
        format_thousands(tdee, false),
        'Calories you burn based on daily activity level.',
      )}
      {renderGoalWeightCard(
        colors.darkPurple,
        'Goal Weight',
        formData.goalWeight,
      )}
      {renderDailyGoalCard(
        colors.darkOrange,
        'Daily Goal Intake',
        format_thousands(adjustedCaloricRequirement, false),
      )}
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      {graphData ? (
        renderPageContent()
      ) : (
        <ActivityIndicator
          animating={!graphData}
          size="large"
          color={colors.subGreyTwo}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
    alignSelf: 'center',
  },
  headerText: (textAlign) => ({
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    marginLeft: 10,
    textAlign,
    width: 100,
  }),
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: scaleWidth(4),
  },
  clientName: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    fontWeight: '700',
  },
  clientInfo: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
  },
  clientInfoRow: {
    marginVertical: 5,
  },
  cardStyle: {
    borderRadius: 7,
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    marginTop: 20,
  },
  cardColor: (color, isCalorieCard) => ({
    backgroundColor: color,
    borderTopLeftRadius: 7,
    borderTopRightRadius: 7,
    paddingHorizontal: 20,
    paddingTop: isCalorieCard ? 5 : 20,
    paddingBottom: isCalorieCard ? 5 : 5,
  }),
  cardContent: {
    padding: 15,
  },
  cardTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
    fontWeight: '700',
  },
  cardResult: {
    fontSize: curvedScale(25),
  },
  colorWhite: {
    color: colors.white,
  },
  cardDescription: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(13),
    marginTop: 10,
  },
  horizontalView: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sectionResult: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  unit: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(11),
  },
  icon: {
    width: curvedScale(25),
    height: curvedScale(25),
    marginRight: 5,
  },
  fireIcon: {
    tintColor: colors.medYellow,
    resizeMode: 'contain',
    width: curvedScale(17),
    height: curvedScale(17),
    marginRight: 7,
  },
  axesStyle: (isYAxis) => ({
    grid: {
      stroke: colors.subGreyLight,
      strokeWidth: isYAxis ? 1 : 0,
    },
    axis: {
      strokeWidth: 0,
    },
    tickLabels: {
      fontSize: curvedScale(11),
      padding: 10,
    },
  }),
  chartParentView: {
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  chartView: {
    alignItems: 'center',
    width: '33%',
  },
  calView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  titleView: {
    alignItems: 'center',
    marginTop: 5,
  },
  macrosTitle: {
    marginTop: 10,
  },
  title: {
    color: colors.black,
    fontSize: curvedScale(16),
    fontFamily: 'Avenir-Heavy',
  },
  caltitle: {
    color: colors.black,
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Heavy',
  },
  macros: {
    marginLeft: curvedScale(5),
  },
  loaderView: {
    marginHorizontal: 10,
  },
});

Results.propTypes = propTypes;
Results.defaultProps = defaultProps;

export default Results;
