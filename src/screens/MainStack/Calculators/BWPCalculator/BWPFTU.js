import React, { Component } from 'react';
import {
  Text, Image, View, StyleSheet, SafeAreaView,
} from 'react-native';
import PropTypes from 'prop-types';
import BWPConstants from './BWPConstants';
import { FloatingButton } from '../../../../components';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';

const imgBWPFTU = require('../../../../assets/bwpftu.png');

const propTypes = {
  navigation: PropTypes.object.isRequired,
};

const defaultProps = {};

class BWPFTU extends Component {
  onPressAdd = () => {
    this.props.navigation.navigate({
      name: BWPConstants.BMR,
      merge: true,
    });
  };

  renderEmptyState = () => (
    <View style={styles.emptyStateContainer}>
      <View style={styles.emptyIconStyle}>
        <Image source={imgBWPFTU} />
      </View>
      <Text style={styles.emptyStateHeaderText}>
        Set Up Nutrition with the Body Weight Planner
      </Text>
      <Text style={styles.emptyStateBodyText}>
        {
          'Calculate basic macronutrients and create advanced\nmeal plans based on specific needs.'
        }
      </Text>
    </View>
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        {this.renderEmptyState()}
        <FloatingButton onPress={this.onPressAdd} />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyIconStyle: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateHeaderText: {
    color: colors.black,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(22),
    textAlign: 'center',
    marginTop: curvedScale(20),
    marginHorizontal: curvedScale(50),
  },
  emptyStateBodyText: {
    color: colors.darkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    textAlign: 'center',
    marginTop: curvedScale(20),
    marginHorizontal: curvedScale(25),
    lineHeight: 20,
  },
});

BWPFTU.propTypes = propTypes;
BWPFTU.defaultProps = defaultProps;

export default BWPFTU;
