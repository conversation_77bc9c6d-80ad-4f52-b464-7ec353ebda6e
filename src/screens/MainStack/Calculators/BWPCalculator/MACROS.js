import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Text,
  TouchableOpacity,
  StyleSheet,
  View,
  TextInput,
  Alert,
} from 'react-native';
import PropTypes from 'prop-types';
import { VictoryPie } from 'victory-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { DietTypes, getDietPercents } from '../../../../types/BWPTypes';
import ColoredHeadingBox from './ColoredHeadingBox';
import BWPNavigationFooter from './BWPNavigationFooter';
import BWPConstants from './BWPConstants';
import DropDownPicker from '../../../../components/DropDownPicker';
import ProgressBar from '../../../../components/ProgressBar';
import { curvedScale } from '../../../../util/responsive';
import { colors } from '../../../../styles';
import { clearFormData, updateFormData } from '../../../../actions';
import { removeAllSpecialCharacters } from '../../../../util/validate';

const stateSelector = (state) => state;
const DIET_OPTIONS = [
  {
    id: 1,
    label: DietTypes.DEFAULT,
  },
  {
    id: 2,
    label: DietTypes.KETOGENIC,
  },
  {
    id: 3,
    label: DietTypes.LOW_FAT,
  },
  {
    id: 4,
    label: DietTypes.LOW_CARBOHYDRATE,
  },
  {
    id: 5,
    label: DietTypes.HIGH_PROTEIN,
  },
];

const colorScale = [colors.avatarOrange, colors.darkPurple, colors.nasmBlue];
const arraySum = (array) => array.reduce((a, b) => Number(a) + Number(b), 0);

const Macros = ({ navigation }) => {
  const { bwpCalculator } = useSelector(stateSelector);
  const dispatch = useDispatch();

  const proteinPercent = bwpCalculator?.data?.proteinPercent;
  const carbohydratesPercent = bwpCalculator?.data?.carbohydratesPercent;
  const fatsPercent = bwpCalculator?.data?.fatsPercent;

  const initialChartData = [
    { x: 'Proteins', y: proteinPercent / 10 },
    { x: 'Carbs', y: carbohydratesPercent / 10 },
    { x: 'Fats', y: fatsPercent / 10 },
  ];
  const [chartData, setChartData] = useState(initialChartData);
  const [selectedDiet, setSelectedDiet] = useState(bwpCalculator?.data?.diet);

  useEffect(() => {
    navigation.setOptions({
      title: 'Body Weight Planner',
      headerTitleStyle: styles.headerTitle,
      headerRight,
      headerLeft,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const dietData = getDietPercents(selectedDiet);
    updateChartData(dietData);
  }, [selectedDiet]);

  const updateChartData = (data) => {
    const [newProteinPercent, newCarbohydratesPercent, newFatsPercent] = data;
    const updatedChartData = [
      { x: 'Proteins', y: (newProteinPercent.y || newProteinPercent) / 10 },
      {
        x: 'Carbs',
        y: (newCarbohydratesPercent.y || newCarbohydratesPercent) / 10,
      },
      { x: 'Fats', y: (newFatsPercent.y || newFatsPercent) / 10 },
    ];

    setChartData(updatedChartData);
    const updatedFormData = {
      proteinPercent: newProteinPercent.y || newProteinPercent,
      carbohydratesPercent:
        newCarbohydratesPercent.y || newCarbohydratesPercent,
      fatsPercent: newFatsPercent.y || newFatsPercent,
      diet: selectedDiet,
    };
    dispatch(updateFormData(updatedFormData));
  };

  const handleMacrosChange = (percentArray) => {
    updateChartData(percentArray);
  };

  const headerRight = () => <Text style={styles.headerRight}>4/5</Text>;

  const headerLeft = () => (
    <TouchableOpacity
      activeOpacity={0.6}
      onPress={() => {
        dispatch(clearFormData());
        navigation.pop(4);
      }}
    >
      <Text style={styles.headerLeft}>Cancel</Text>
    </TouchableOpacity>
  );

  const renderPageFooter = () => {
    const totalPercent = parseInt(
      arraySum([proteinPercent, carbohydratesPercent, fatsPercent]),
      10,
    );
    const isError = totalPercent !== 100;
    return (
      <BWPNavigationFooter
        currentStep={4}
        onPressPrevious={() => navigation.goBack()}
        onPressNext={() => {
          if (isError) {
            Alert.alert('', 'Total ratio must equal 100%');
          } else {
            updateFormData('');
            navigation.navigate(BWPConstants.RESULTS);
          }
        }}
      />
    );
  };

  const renderPickerView = () => (
    <View style={styles.pickerView}>
      <DropDownPicker
        useNativeAndroidPickerStyle
        data={DIET_OPTIONS}
        label="Diet"
        selected={DIET_OPTIONS.find((option) => option.label === selectedDiet).id}
        onValueChange={(selectedUnit) => {
          setSelectedDiet(selectedUnit.label);
        }}
        labelStyle={styles.pickerItem}
        containerStyle={styles.picker}
      />
    </View>
  );

  const renderChartView = () => {
    const totalPercent = parseInt(
      arraySum([proteinPercent, carbohydratesPercent, fatsPercent]),
      10,
    );
    const isError = totalPercent !== 100;
    const bottomStyle = { bottom: isError ? curvedScale(20) : 0 };
    const customInputStyle = { fontWeight: 'bold', fontSize: curvedScale(20) };
    return (
      <View>
        <View>
          <VictoryPie
            colorScale={colorScale}
            padAngle={() => 1}
            labels={() => null}
            innerRadius={curvedScale(45)}
            height={curvedScale(225)}
            animate={{
              duration: 1000,
            }}
            data={chartData}
            style={{
              parent: {
                alignItems: 'center',
              },
            }}
            padding={{
              top: 25,
              bottom: 25,
            }}
          />
          {isError ? (
            <Text style={styles.totalError}>Total ratio must equal 100%</Text>
          ) : null}
        </View>
        <View style={[styles.total, bottomStyle]}>
          <Text style={[styles.input, customInputStyle]}>
            {`${totalPercent}%`}
          </Text>
        </View>
      </View>
    );
  };

  const chartView = useMemo(
    () => renderChartView(),
    [chartData[0].y, chartData[1].y, chartData[2].y],
  );

  const renderInputView = (field, fieldValue, backgroundColor) => (
    <View style={styles.inputView}>
      <View style={styles.dotView}>
        <View style={[styles.dot, { backgroundColor }]} />
        <Text style={styles.diet}>{field}</Text>
      </View>
      <View style={styles.inputStyle}>
        <TextInput
          placeholder="0"
          style={[styles.input, { width: '100%' }]}
          placeholderTextColor={colors.fillDarkGrey}
          value={`${fieldValue}`}
          onChangeText={(newValue) => {
            const filteredValue = removeAllSpecialCharacters(newValue);
            if (field === 'Carbs') {
              handleMacrosChange([proteinPercent, filteredValue, fatsPercent]);
            } else if (field === 'Fats') {
              handleMacrosChange([
                proteinPercent,
                carbohydratesPercent,
                filteredValue,
              ]);
            } else {
              handleMacrosChange([
                filteredValue,
                carbohydratesPercent,
                fatsPercent,
              ]);
            }
          }}
          maxLength={2}
          keyboardType="number-pad"
          returnKeyType="done"
          contextMenuhidden
        />
        <Text style={styles.input}>%</Text>
      </View>
    </View>
  );

  const renderPageHeader = () => (
    <ColoredHeadingBox
      title="Macronutrients Ratios"
      titleStyle={styles.title}
      viewStyle={{ paddingHorizontal: 0 }}
    >
      <Text style={styles.subTitle}>
        Plan what works for you. Adjust the macronutrient ratio recommendations
        to fit your needs.
      </Text>
    </ColoredHeadingBox>
  );

  const renderInputs = () => (
    <>
      <View style={styles.rowInputView}>
        <View style={styles.rowInput}>
          {renderInputView('Carbs', carbohydratesPercent, colors.darkPurple)}
        </View>
        <View style={styles.rowInput}>
          {renderInputView('Fats', fatsPercent, colors.nasmBlue)}
        </View>
      </View>
      {renderInputView('Proteins', proteinPercent, colors.avatarOrange)}
    </>
  );

  const renderPageContent = () => (
    <KeyboardAwareScrollView
      style={styles.mainView}
      showsVerticalScrollIndicator={false}
    >
      {renderPageHeader()}
      {renderPickerView()}
      {chartView}
      {renderInputs()}
    </KeyboardAwareScrollView>
  );

  return (
    <View style={styles.container}>
      <ProgressBar
        progress={80}
        barStyle={styles.barStyle}
        progressColor={colors.medYellow}
        style={styles.progressBackground}
      />
      {renderPageContent()}
      {renderPageFooter()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  progressBackground: {
    borderRadius: 0,
    width: '100%',
    borderWidth: 0,
    backgroundColor: colors.nasmBlue,
    height: 12,
  },
  barStyle: {
    borderRadius: 0,
  },
  headerRight: {
    color: colors.white,
    fontSize: 14,
    marginRight: curvedScale(10),
    fontFamily: 'Avenir-Medium',
  },
  headerLeft: {
    color: colors.white,
    fontSize: 14,
    marginLeft: curvedScale(10),
    fontFamily: 'Avenir-Medium',
    width: curvedScale(100),
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
    alignSelf: 'center',
  },
  title: {
    color: colors.black,
    fontSize: curvedScale(20),
    fontFamily: 'Avenir-Heavy',
  },
  mainView: {
    height: '80%',
    paddingHorizontal: curvedScale(20),
  },
  subTitle: {
    color: colors.black,
    fontSize: curvedScale(16),
    marginTop: curvedScale(20),
    fontFamily: 'Avenir-Medium',
  },
  pickerView: {
    marginTop: curvedScale(20),
  },
  picker: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    height: curvedScale(40),
    marginVertical: 0,
  },
  pickerItem: {
    color: colors.black,
    fontSize: 14,
    fontFamily: 'Avenir-Light',
    fontWeight: '500',
    paddingHorizontal: curvedScale(8),
    textAlign: 'left',
  },
  input: {
    color: colors.black,
    fontSize: curvedScale(16),
    fontFamily: 'Avenir-Heavy',
    textAlign: 'right',
    paddingVertical: curvedScale(10),
  },
  inputView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.bordergrey,
    paddingHorizontal: curvedScale(10),
  },
  dotView: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '40%',
  },
  inputStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '60%',
    justifyContent: 'flex-end',
  },
  dot: {
    height: 10,
    width: 10,
    borderRadius: 10 / 2,
  },
  diet: {
    fontSize: curvedScale(14),
    color: colors.black,
    marginLeft: curvedScale(10),
  },
  totalError: {
    color: colors.nasmRed,
    fontSize: curvedScale(12),
    alignSelf: 'center',
    marginBottom: curvedScale(10),
  },
  rowInputView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: curvedScale(10),
  },
  rowInput: {
    width: '49%',
  },
  total: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

Macros.propTypes = {
  navigation: PropTypes.object.isRequired,
};

export default Macros;
