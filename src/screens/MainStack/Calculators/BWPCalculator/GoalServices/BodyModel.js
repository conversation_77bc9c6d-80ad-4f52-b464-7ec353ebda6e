/* eslint-disable no-restricted-properties */
/* eslint-disable no-mixed-operators */
import BodyChange from './BodyChange';
import DailyParameters from './DailyParameters';

export default class BodyModel {
  static RK4wt = [1, 2, 2, 1];

  fat;

  lean;

  glycogen;

  dECW;

  therm;

  constructor(props) {
    this.fat = props.fat || 0;
    this.lean = props.lean || 0;
    this.glycogen = props.glycogen || 0;
    this.dECW = props.dECW || 0;
    this.therm = props.therm || 0;
  }

  static createFromBaseline = (baseline) => new BodyModel({
    fat: baseline.getFatWeight(),
    lean: baseline.getLeanWeight(),
    glycogen: baseline.glycogen,
    dECW: baseline.dECW,
    therm: baseline.getTherm(),
  });

  static projectFromBaseline = (baseline, dailyParams, simLength) => {
    // Creates a BodyModel projected from the given baseline, using the input DailyParameters for sim length days
    let loop = BodyModel.createFromBaseline(baseline);
    for (let i = 0; i < simLength; i += 1) {
      loop = BodyModel.RungeKatta(loop, baseline, dailyParams);
    }
    return loop;
  };

  static projectFromBaselineViaIntervention = (
    baseline,
    intervention,
    simLength,
  ) => {
    const dailyParams = DailyParameters.createFromIntervention(
      intervention,
      baseline,
    );
    return BodyModel.projectFromBaseline(baseline, dailyParams, simLength);
  };

  getWeight = (baseline) => this.fat + this.lean + baseline.getGlycogenH2O(this.glycogen) + this.dECW;

  getApproxWeight = () => this.fat + this.lean + this.dECW;

  getFatFree = (baseline) => this.getWeight(baseline) - this.fat;

  getFatPercent = (baseline) => (this.fat / this.getWeight(baseline)) * 100.0;

  getBMI = (baseline) => baseline.getNewBMI(this.getWeight(baseline));

  dt = (baseline, dailyParams) => {
    const deltaFat = this.deltaFatdt(baseline, dailyParams);
    const deltaLean = this.deltaLeandt(baseline, dailyParams);
    const deltaGlycogen = this.deltaGlycogendt(baseline, dailyParams);
    const deltaDecw = this.deltaDecwdt(baseline, dailyParams);
    const deltaTherm = this.deltaThermdt(baseline, dailyParams);

    return new BodyChange({
      deltaFat,
      deltaLean,
      deltaGlycogen,
      deltaDecw,
      deltaTherm,
    });
  };

  static RungeKattaAveraged = (
    bodyModel,
    baseline,
    dailyParams1,
    dailyParams2,
  ) => {
    const midailyParams = dailyParams2.ramped
      ? dailyParams2.avg(dailyParams1, dailyParams2)
      : null;
    const dt1 = bodyModel.dt(baseline, dailyParams1);
    const b2 = bodyModel.addchange(dt1, 0.5);
    const dt2 = b2.dt(baseline, midailyParams);
    const b3 = bodyModel.addchange(dt2, 0.5);
    const dt3 = b3.dt(baseline, midailyParams);
    const b4 = bodyModel.addchange(dt3, 1.0);
    const dt4 = b4.dt(baseline, dailyParams2);
    const finaldt = bodyModel.avgdt_weighted(BodyModel.RK4wt, [
      dt1,
      dt2,
      dt3,
      dt4,
    ]);
    const finalstate = bodyModel.addchange(finaldt, 1.0);

    return finalstate;
  };

  static RungeKatta = (bodyModel, baseline, dailyParams) => {
    const dt1 = bodyModel.dt(baseline, dailyParams);
    const b2 = bodyModel.addchange(dt1, 0.5);
    const dt2 = b2.dt(baseline, dailyParams);
    const b3 = bodyModel.addchange(dt2, 0.5);
    const dt3 = b3.dt(baseline, dailyParams);
    const b4 = bodyModel.addchange(dt3, 1.0);
    const dt4 = b4.dt(baseline, dailyParams);
    const finaldt = bodyModel.avgdt_weighted(BodyModel.RK4wt, [
      dt1,
      dt2,
      dt3,
      dt4,
    ]);
    const finalstate = bodyModel.addchange(finaldt, 1.0);

    return finalstate;
  };

  static Euler = (bodyModel, baseline, dailyParams) => {
    const dt1 = bodyModel.dt(baseline, dailyParams);
    return bodyModel.addchange(dt1, 1.0);
  };

  getTEE = (baseline, dailyParams) => {
    const p = this.getp();
    const calIn = dailyParams.calories;
    const carbFlux = this.carbflux(baseline, dailyParams);
    const Expend = this.getExpend(baseline, dailyParams);

    return (
      (Expend
        + (calIn - carbFlux)
          * (((1.0 - p) * 180.0) / 9440.0 + (p * 230.0) / 1807.0))
      / (1.0 + (p * 230.0) / 1807.0 + ((1.0 - p) * 180.0) / 9440.0)
    );
  };

  getExpend = (baseline, dailyParams) => {
    const TEF = 0.1 * dailyParams.calories;
    const weight = this.getWeight(baseline);

    return (
      baseline.getK()
      + 22.0 * this.lean
      + 3.2 * this.fat
      + dailyParams.activityParam * weight
      + this.therm
      + TEF
    );
  };

  getp = () => 1.990762711864407 / (1.990762711864407 + this.fat);

  carbflux = (baseline, dailyParams) => {
    const k_carb = baseline.getCarbsIn() / Math.pow(baseline.glycogen, 2.0);
    return dailyParams.getCarbIntake() - k_carb * Math.pow(this.glycogen, 2.0);
  };

  Na_imbal = (baseline, dailyParams) => dailyParams.sodium
    - baseline.sodium
    - 3000.0 * this.dECW
    - 4000.0 * (1.0 - dailyParams.getCarbIntake() / baseline.getCarbsIn());

  deltaFatdt = (baseline, dailyParams) => {
    const deltaFatdt = ((1.0 - this.getp())
        * (dailyParams.calories
          - this.getTEE(baseline, dailyParams)
          - this.carbflux(baseline, dailyParams)))
      / 9440.0;
    return deltaFatdt;
  };

  deltaLeandt = (baseline, dailyParams) => {
    const deltaLeandt = (this.getp()
        * (dailyParams.calories
          - this.getTEE(baseline, dailyParams)
          - this.carbflux(baseline, dailyParams)))
      / 1807.0;
    return deltaLeandt;
  };

  deltaGlycogendt = (baseline, dailyParams) => this.carbflux(baseline, dailyParams) / 4180.0;

  deltaDecwdt = (baseline, dailyParams) => this.Na_imbal(baseline, dailyParams) / 3220.0;

  deltaThermdt = (baseline, dailyParams) => (0.14 * dailyParams.calories - this.therm) / 14.0;

  addchange = (bChange, tStep) => new BodyModel({
    fat: this.fat + tStep * bChange.deltaFat,
    lean: this.lean + tStep * bChange.deltaLean,
    glycogen: this.glycogen + tStep * bChange.deltaGlycogen,
    dECW: this.dECW + tStep * bChange.deltaDecw,
    therm: this.therm + tStep * bChange.deltaTherm,
  });

  cals4balance = (baseline, act) => {
    const weight = this.getWeight(baseline);
    const Expend_no_food = baseline.getK() + 22.0 * this.lean + 3.2 * this.fat + act * weight;
    const p = this.getp();
    const p_d = 1.0 + (p * 230.0) / 1807.0 + ((1.0 - p) * 180.0) / 9440.0;
    const p_n = ((1.0 - p) * 180.0) / 9440.0 + (p * 230.0) / 1807.0;
    const maint_nocflux = Expend_no_food / (p_d - p_n - 0.24);

    return maint_nocflux;
  };

  Bodytraj = (baseline, paramTraj = []) => {
    const simLength = paramTraj.length;
    const bodyTraj = [];

    bodyTraj.push(new BodyModel(baseline));

    for (let i = 1; i < simLength; i += 1) {
      bodyTraj.push(
        BodyModel.RungeKattaAveraged(
          bodyTraj[i - 1],
          baseline,
          paramTraj[i - 1],
          paramTraj[i],
        ),
      );
    }

    return bodyTraj;
  };

  avgdt = (bChange = []) => {
    let sumf = 0.0;
    let suml = 0.0;
    let sumg = 0.0;
    let sumDecw = 0.0;
    let sumTherm = 0.0;

    for (let i = 0; i < bChange.length; i += 1) {
      sumf += bChange[i].deltaFat;
      suml += bChange[i].deltaLean;
      sumg += bChange[i].deltaGlycogen;
      sumDecw += bChange[i].deltaDecw;
      sumTherm += bChange[i].deltaTherm;
    }

    const nf = sumf / bChange.length;
    const nl = suml / bChange.length;
    const ng = sumg / bChange.length;
    const nDecw = sumDecw / bChange.length;
    const nTherm = sumTherm / bChange.length;

    return new BodyChange({
      deltaFat: nf,
      deltaLean: nl,
      deltaGlycogen: ng,
      deltaDecw: nDecw,
      deltaTherm: nTherm,
    });
  };

  avgdt_weighted = (wt = [], bChange = []) => {
    let sumf = 0.0;
    let suml = 0.0;
    let sumg = 0.0;
    let sumDecw = 0.0;
    let sumTherm = 0.0;
    let wti = 0;
    let wtSum = 0;

    for (let i = 0; i < bChange.length; i += 1) {
      try {
        wti = wt[i];
      } catch (e) {
        wti = 1;
      }

      wti = wti < 0 ? 1 : wti;
      wtSum += wti;
      sumf += wti * bChange[i].deltaFat;
      suml += wti * bChange[i].deltaLean;
      sumg += wti * bChange[i].deltaGlycogen;
      sumDecw += wti * bChange[i].deltaDecw;
      sumTherm += wti * bChange[i].deltaTherm;
    }

    const nf = sumf / wtSum;
    const nl = suml / wtSum;
    const ng = sumg / wtSum;
    const nDecw = sumDecw / wtSum;
    const nTherm = sumTherm / wtSum;

    return new BodyChange({
      deltaFat: nf,
      deltaLean: nl,
      deltaGlycogen: ng,
      deltaDecw: nDecw,
      deltaTherm: nTherm,
    });
  };
}
