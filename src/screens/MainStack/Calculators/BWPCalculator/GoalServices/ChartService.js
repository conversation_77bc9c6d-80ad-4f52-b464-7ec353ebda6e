import moment from 'moment';
import Baseline from './Baseline';
import BodyModel from './BodyModel';
import DailyParameters from './DailyParameters';
import Intervention from './Intervention';
import { MessageTypes, UnitType } from '../../../../../types/BWPTypes';

export default class ChartService {
  clientData;

  baseline;

  goalIntervention;

  goalMaintenanceIntervention;

  intervention1;

  intervention2;

  eps = 0.001;

  simulationLength = 365;

  calorieSpread = 256;

  minCal = 0;

  energyUnits;

  goalCalories;

  goalMaintenanceCalories;

  chartTableArray = [];

  chartArray;

  errorMessages = [];

  constructor(clientData) {
    this.clientData = clientData;

    this.baseline = new Baseline({
      isMale: clientData.sex === 'Male',
      age: Number(clientData.age),
      height:
        clientData.units === UnitType.US
          ? Number(clientData.height_ft) * 12 + Number(clientData.height_in)
          : Number(clientData.height_cm),
      weight: Number(clientData.weight),
      pal: Number(clientData.currentActivityLevel),
      carbIntakePercent: clientData.carbohydratesPercent,
    });

    this.intervention1 = new Intervention({
      day: 90,
      calories: 2000,
      carbInPercent: 50,
      activityChangePercent: 0,
      sodium: 4000,
    });
    this.intervention1.setProportionalSodium(this.baseline);

    this.intervention2 = new Intervention({
      day: 180,
      calories: 2500,
      carbInPercent: 50,
      activityChangePercent: 0,
      sodium: 4000,
    });
    this.intervention2.setProportionalSodium(this.baseline);

    this.goalIntervention = new Intervention({});
    this.goalMaintenanceIntervention = this.goalIntervention;

    if (clientData.units === UnitType.US) {
      this.energyUnits = 1; // Calories / Day
    } else {
      this.energyUnits = 4.184; // Kilojoules / Day
    }

    this.chartTableArray = [];
    this.chartArray = [];

    this.simulationLength = this.clientData.numberOfDays;
    this.goalCalories = 0;
    this.goalMaintenanceCalories = 0;

    this.errorMessages = [];
  }

  processGoal = () => {
    if (this.clientData.goalWeight === this.clientData.weight) {
      this.errorMessages.push(MessageTypes.NO_WEIGHT_CHANGE);
    }

    this.goalIntervention = new Intervention({});
    this.goalMaintenanceIntervention = this.goalIntervention;

    this.goalMaintenanceIntervention.activityChangePercent = 0;

    try {
      this.goalIntervention = Intervention.forGoal(
        this.baseline,
        this.clientData.goalWeight,
        this.clientData.numberOfDays,
        this.goalMaintenanceIntervention.activityChangePercent,
        this.minCal,
        this.eps,
      );
    } catch (error) {
      this.errorMessages.push(MessageTypes.UNREACHABLE_GOAL);
    }

    this.goalCalories = Math.round(
      this.goalIntervention.calories * this.energyUnits,
    );

    // We find out what the body composition will be at the goal time with the RKlast_ecw function
    const goalBC = BodyModel.projectFromBaselineViaIntervention(
      this.baseline,
      this.goalIntervention,
      this.clientData.numberOfDays + 1,
    );

    // The goal body comp is used to calculated the calories needed to keep tissue from changing. However, it does not account for
    // the sodium needed to stabilize the extracellular water.
    if (
      this.clientData.goalWeight === this.baseline.weight
      && this.goalMaintenanceIntervention.activityChangePercent === 0
    ) {
      this.goalMaintenanceCalories = this.baseline.getMaintCals();
    } else {
      this.goalMaintenanceCalories = goalBC.cals4balance(
        this.baseline,
        this.goalMaintenanceIntervention.getActivity(this.baseline),
      );
    }

    // If the user has changed the maintenance activity level, we may get a negative result, so we check and redo if needed
    if (this.goalMaintenanceCalories < 0) {
      this.errorMessages.push(MessageTypes.NEGATIVE_CALORIES);
    }

    if (
      this.clientData.goalWeight !== this.clientData.weight
      && (this.goalIntervention.calories < 1000
        || this.goalMaintenanceCalories < 1000)
    ) {
      this.errorMessages.push(MessageTypes.NEGATIVE_CALORIES);
    }

    this.goalMaintenanceIntervention.day = this.clientData.numberOfDays + 1;
    this.goalMaintenanceIntervention.calories = this.goalMaintenanceCalories;
    this.goalMaintenanceIntervention.carbInPercent = this.baseline.carbIntakePercent;
    this.goalMaintenanceIntervention.setProportionalSodium(this.baseline);
    this.goalMaintenanceCalories = Math.round(
      this.goalMaintenanceIntervention.calories * this.energyUnits,
    );
  };

  setChartRow = (day, bodyTraj, upperBodyTraj, lowerBodyTraj, paramTraj) => {
    const weight = Math.round(bodyTraj.getWeight(this.baseline));
    const upperWeight = Math.round(upperBodyTraj.getWeight(this.baseline));
    const lowerWeight = Math.round(lowerBodyTraj.getWeight(this.baseline));
    const BMI = Math.round(bodyTraj.getBMI(this.baseline));
    const fat = Math.round(bodyTraj.fat);
    const fatPercent = Math.round(bodyTraj.getFatPercent(this.baseline));
    const upperFatPercent = Math.round(
      upperBodyTraj.getFatPercent(this.baseline),
    );
    const lowerFatPercent = Math.round(
      lowerBodyTraj.getFatPercent(this.baseline),
    );
    const fatFree = Math.round(bodyTraj.getFatFree(this.baseline));
    const TEE = Math.round(
      bodyTraj.getTEE(this.baseline, paramTraj) * this.energyUnits,
    );
    const caloriesIn = Math.round(bodyTraj.getFatFree(this.baseline));

    const dataChartRow = {
      weight,
      fatPercent,
      BMI,
    };

    const date = moment(new Date()).add(day, 'days').format('M/D/YYYY');

    this.chartTableArray[day] = dataChartRow;
    this.chartArray[day] = [
      day,
      date,
      weight,
      upperWeight,
      lowerWeight,
      fatPercent,
      upperFatPercent,
      lowerFatPercent,
      BMI,
      fat,
      fatFree,
      caloriesIn,
      TEE,
    ];
  };

  generateChartData = () => {
    this.chartTableArray = [];
    this.chartArray = [];

    const upperBaseline = { ...this.baseline };
    upperBaseline.deltaE = this.calorieSpread;

    const lowerBaseline = { ...this.baseline };
    lowerBaseline.deltaE = -this.calorieSpread;

    const paramTraj = DailyParameters.makeParamTrajectory(
      this.baseline,
      this.goalIntervention,
      this.goalMaintenanceIntervention,
      this.simulationLength,
    );

    const bodyTraj = [];
    const upperBodyTraj = [];
    const lowerBodyTraj = [];

    bodyTraj[0] = BodyModel.createFromBaseline(this.baseline);
    upperBodyTraj[0] = BodyModel.createFromBaseline(upperBaseline);
    lowerBodyTraj[0] = BodyModel.createFromBaseline(lowerBaseline);
    this.setChartRow(
      0,
      bodyTraj[0],
      upperBodyTraj[0],
      lowerBodyTraj[0],
      paramTraj[0],
      this.baseline,
    );

    for (let i = 1; i < this.simulationLength; i += 1) {
      const dParams = paramTraj[i];

      bodyTraj[i] = BodyModel.RungeKatta(
        bodyTraj[i - 1],
        this.baseline,
        dParams,
      );
      upperBodyTraj[i] = BodyModel.RungeKatta(
        upperBodyTraj[i - 1],
        upperBaseline,
        dParams,
      );
      lowerBodyTraj[i] = BodyModel.RungeKatta(
        lowerBodyTraj[i - 1],
        lowerBaseline,
        dParams,
      );

      this.setChartRow(
        i,
        bodyTraj[i],
        upperBodyTraj[i],
        lowerBodyTraj[i],
        paramTraj[i],
        this.baseline,
      );
    }

    const finalWeight = this.chartTableArray[this.chartArray.length - 1].weight;
    const finalBMI = this.chartTableArray[this.chartArray.length - 1].BMI;

    // Check if the final BMI is too low or too high
    if (finalBMI < 18.5 && finalWeight < this.clientData.weight) {
      this.errorMessages.push(MessageTypes.LOW_BMI);
    } else if (finalBMI > 25 && finalWeight > this.clientData.weight) {
      this.errorMessages.push(MessageTypes.HIGH_BMI);
    }
  };
}
