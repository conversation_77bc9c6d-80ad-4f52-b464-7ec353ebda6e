/* eslint-disable no-mixed-operators */
export default class DailyParameters {
  calories;

  carbPercent;

  sodium;

  activityParam;

  flag;

  ramped;

  constructor(props) {
    this.calories = props.calories || 0;
    this.calories = this.calories < 0 ? 0 : this.calories;
    this.carbPercent = props.carbPercent || 0;
    this.carbPercent = this.carbPercent < 0 ? 0 : this.carbPercent;
    this.carbPercent = this.carbPercent > 100.0 ? 100.0 : this.carbPercent;
    this.sodium = props.sodium || 0;
    this.sodium = this.sodium < 0 ? 0 : this.sodium;
    this.activityParam = props.activityParam || 0;
    this.activityParam = this.activityParam < 0 ? 0 : this.activityParam;
    this.flag = false;
    this.ramped = false;
  }

  static createFromBaseline = (baseline) => new DailyParameters({
    calories: baseline.getMaintCals(),
    carbPercent: baseline.carbIntakePercent,
    sodium: baseline.sodium,
    activityParam: baseline.getActivityParam(),
  });

  static createFromIntervention = (intervention, baseline) => new DailyParameters({
    calories: intervention.calories,
    carbPercent: intervention.carbInPercent,
    sodium: intervention.sodium,
    activityParam: intervention.getActivity(baseline),
  });

  getCarbIntake = () => (this.carbPercent / 100.0) * this.calories;

  makeCaloricCopy = (calorie) => {
    let nCals = this.calories + calorie;
    nCals = nCals < 0.0 ? 0.0 : nCals;
    return new DailyParameters({
      calories: nCals,
      carbPercent: this.carbPercent,
      sodium: this.sodium,
      activityParam: this.activityParam,
    });
  };

  static avg = (dailyParams1, dailyParams2) => {
    const calories = (dailyParams1.calories + dailyParams2.calories) / 2.0;
    const sodium = (dailyParams1.sodium + dailyParams2.sodium) / 2.0;
    const carbPercent = (dailyParams1.carbPercent + dailyParams2.carbPercent) / 2.0;
    const activityParam = (dailyParams1.activityParam + dailyParams2.activityParam) / 2.0;

    return new DailyParameters({
      calories,
      carbPercent,
      sodium,
      activityParam,
    });
  };

  static makeParamTrajectory = (
    baseline,
    intervention1,
    intervention2,
    simLength,
  ) => {
    const maintCals = baseline.getMaintCals();
    const carbInPercent = baseline.carbIntakePercent;
    const activity = baseline.getActivityParam();
    const Na = baseline.sodium;
    const paramTraj = [];
    const noEffect1 = !intervention1.on
      || (intervention1.on
        && intervention1.day > simLength
        && !intervention1.rampOn);
    const noEffect2 = !intervention2.on
      || (intervention2.on
        && intervention2.day > simLength
        && !intervention2.rampOn);
    const noEffect = noEffect1 && noEffect2;
    const sameDay = intervention1.day === intervention2.day;
    const oneOn = (intervention1.on && !intervention2.on)
      || (!intervention1.on && intervention2.on);
    const bothOn = intervention1.on && intervention2.on;
    let dCal = 0.0;
    let dactivity = 0.0;
    let dCarb = 0.0;
    let dSodium = 0.0;
    let dailyParams = null;

    paramTraj.push(DailyParameters.createFromBaseline(baseline));

    if (noEffect) {
      for (let i = 1; i < simLength; i += 1) {
        paramTraj[i] = DailyParameters.createFromBaseline(baseline);
      }
    } else if (oneOn || (bothOn && sameDay && intervention2.rampOn)) {
      let intervention = null;

      if (oneOn) {
        intervention = intervention1.on ? intervention1 : intervention2;
      } else {
        intervention = intervention2;
      }

      if (intervention.rampOn) {
        for (let i = 1; i < intervention.day; i += 1) {
          dCal = maintCals
            + (i / intervention.day) * (intervention.calories - maintCals);
          dactivity = activity
            + (i / intervention.day)
              * (intervention.getActivity(baseline) - activity);
          dCarb = carbInPercent
            + (i / intervention.carbInPercent)
              * (intervention.carbInPercent - carbInPercent);
          dSodium = Na + (i / intervention.day) * (intervention.sodium - Na);
          dailyParams = new DailyParameters({
            calories: dCal,
            carbPercent: dCarb,
            sodium: dSodium,
            activityParam: dactivity,
          });
          dailyParams.ramped = true;

          paramTraj.push(dailyParams);
        }

        for (let i = intervention.day; i < simLength; i += 1) {
          paramTraj.push(
            DailyParameters.createFromIntervention(intervention, baseline),
          );
        }
      } else {
        for (let i = 1; i < intervention.day; i += 1) {
          paramTraj.push(DailyParameters.createFromBaseline(baseline));
        }

        for (let i = intervention.day; i < simLength; i += 1) {
          paramTraj.push(
            DailyParameters.createFromIntervention(intervention, baseline),
          );
        }
      }
    } else {
      const firstIntervention = intervention1.day < intervention2.day ? intervention1 : intervention2;
      const secondIntervention = intervention1.day < intervention2.day ? intervention2 : intervention1;

      if (firstIntervention.rampOn) {
        for (let i = 1; i < firstIntervention.day; i += 1) {
          dCal = maintCals
            + (i / firstIntervention.day)
              * (firstIntervention.calories - maintCals);
          dactivity = activity
            + (i / firstIntervention.day)
              * (firstIntervention.getActivity(baseline) - activity);
          dCarb = carbInPercent
            + (i / firstIntervention.carbInPercent)
              * (firstIntervention.carbInPercent - carbInPercent);
          dSodium = Na + (i / firstIntervention.day) * (firstIntervention.sodium - Na);
          dailyParams = new DailyParameters({
            calories: dCal,
            carbPercent: dCarb,
            sodium: dSodium,
            activityParam: dactivity,
          });
          dailyParams.ramped = true;

          paramTraj.push(dailyParams);
        }
      } else {
        for (let i = 1; i < firstIntervention.day; i += 1) {
          paramTraj.push(DailyParameters.createFromBaseline(baseline));
        }
      }

      if (secondIntervention.rampOn) {
        for (
          let i = firstIntervention.day;
          i < secondIntervention.day;
          i += 1
        ) {
          const firstCalories = firstIntervention.calories;
          const firstDay = firstIntervention.day;
          const firstSodium = firstIntervention.sodium;
          const firstactivity = firstIntervention.getActivity(baseline);
          const firstCarbIn = firstIntervention.carbInPercent;
          const secondCalories = secondIntervention.calories;
          const secondDay = secondIntervention.day;
          const secondSodium = secondIntervention.sodium;
          const secondactivity = secondIntervention.getActivity(baseline);
          const secondCarbIn = secondIntervention.carbInPercent;

          dCal = firstCalories
            + ((i - firstDay) / (secondDay - firstDay))
              * (secondCalories - firstCalories);
          dactivity = firstactivity
            + ((i - firstDay) / (secondDay - firstDay))
              * (secondactivity - firstactivity);
          dactivity = firstCarbIn
            + ((i - firstDay) / (secondDay - firstDay))
              * (secondCarbIn - firstCarbIn);
          dSodium = firstSodium
            + ((i - firstDay) / (secondDay - firstDay))
              * (secondSodium - firstSodium);
          dailyParams = new DailyParameters({
            calories: dCal,
            carbPercent: dCarb,
            sodium: dSodium,
            activityParam: dactivity,
          });
          dailyParams.ramped = true;

          paramTraj.push(dailyParams);
        }
      } else {
        const endFirst = Math.min(secondIntervention.day, simLength);

        for (let i = firstIntervention.day; i < endFirst; i += 1) {
          paramTraj.push(
            DailyParameters.createFromIntervention(firstIntervention, baseline),
          );
        }
      }

      if (simLength > secondIntervention.day) {
        for (let i = secondIntervention.day; i < simLength; i += 1) {
          paramTraj.push(
            DailyParameters.createFromIntervention(
              secondIntervention,
              baseline,
            ),
          );
        }
      }
    }

    return paramTraj;
  };
}
