import React, { Component } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { connect } from 'react-redux';
import { colors } from '../../../../../styles';
import { curvedScale } from '../../../../../util/responsive';
import ChartService from './ChartService';

const Alert = (props) => (
  <View>
    {props.alertMessage.messageType === 'Warning' ? (
      <Text style={styles.error}>
        Warning!
        {' '}
        {props.alertMessage.message}
        {' '}
        Please check your inputs before
        continuing.
      </Text>
    ) : (
      <Text style={styles.error}>
        Uh-oh!
        {' '}
        {props.alertMessage.message}
        {' '}
        Please check your inputs before
        continuing.
      </Text>
    )}
  </View>
);

class CheckReachableGoal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      errors: [],
    };
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.calculating
      && prevProps.calculating !== this.props.calculating
    ) {
      setTimeout(() => {
        this.checkError();
      }, 100);
    }
  }

  checkError = () => {
    const { bwpCalculator } = this.props;
    const errors = this.checkGoal(bwpCalculator);
    this.setState({ errors });
  };

  checkGoal = (bwpCalculator) => {
    const chartService = new ChartService(bwpCalculator?.data);
    // Process the current goal parameters with the chart service to determine if the goal is reachable or not
    chartService.processGoal();
    chartService.generateChartData();
    this.props.onCalculationComplete();
    return chartService.errorMessages;
  };

  render() {
    const { errors } = this.state;

    if (errors && errors.length > 0) {
      return (
        <>
          {errors.map((value, index) => (
            <Alert alertMessage={value} key={`message-${index}`} />
          ))}
        </>
      );
    }

    return null;
  }
}

const styles = StyleSheet.create({
  error: {
    color: colors.nasmRed,
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Roman',
    fontWeight: '600',
    marginVertical: curvedScale(10),
  },
});

const mapStateToProps = ({ bwpCalculator }) => ({
  bwpCalculator,
});

const mapDispatchToProps = null;

export default connect(mapStateToProps, mapDispatchToProps)(CheckReachableGoal);
