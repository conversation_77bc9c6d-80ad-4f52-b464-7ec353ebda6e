/* eslint-disable no-mixed-operators */
/* eslint-disable no-restricted-properties */
export const MAX_AGE = 250.0;
const INITIAL_AGE = 23.0;
const MIN_HEIGHT = 0.1;
const MAX_HEIGHT = 400.0;
const INITIAL_HEIGHT = 180.0;
const MIN_WEIGHT = 0.1;
const INITIAL_WEIGHT = 70.0;
const MIN_BFP = 0.0;
const MAX_BFP = 100.0;
const INITIAL_BFP = 18.0;
const INITIAL_RMR = 1708.0;
const MIN_PAL = 1.0;
const INITIAL_PAL = 1.6;
const INITIAL_CARB_INTAKE_PCT = 50.0;
const INITIAL_SODIUM = 4000.0;
const INITIAL_GLYCOGEN = 0.5;
const INITIAL_DELTA_E = 0;
const INITIAL_DECW = 0;

export default class Baseline {
  isMale;

  bfpCalc;

  rmrCalc;

  age;

  maximumAge;

  height;

  weight;

  bfp;

  rmr;

  pal;

  carbIntakePercent;

  sodium;

  deltaE;

  dECW;

  glycogen;

  constructor(props) {
    this.isMale = props.isMale ? props.isMale : true;
    this.bfpCalc = true;
    this.rmrCalc = true;

    this.age = props.age || INITIAL_AGE;
    this.maximumAge = MAX_AGE;

    this.height = props.height || INITIAL_HEIGHT;
    this.height = this.height < 0.0 ? MIN_HEIGHT : this.height;
    this.height = this.height > MAX_HEIGHT ? MAX_HEIGHT : this.height;

    this.weight = props.weight || INITIAL_WEIGHT;
    this.weight = this.weight < 0.0 ? MIN_WEIGHT : this.weight;

    this.bfp = props.bfp || INITIAL_BFP;
    this.bfp = this.bfp < MIN_BFP ? MIN_BFP : this.bfp;
    this.bfp = this.bfp > MAX_BFP ? MAX_BFP : this.bfp;

    this.rmr = props.rmr || INITIAL_RMR;

    this.pal = props.pal || INITIAL_PAL;
    this.pal = this.pal < MIN_PAL ? MIN_PAL : this.pal;

    this.carbIntakePercent = props.carbIntakePercent || INITIAL_CARB_INTAKE_PCT;
    this.sodium = INITIAL_SODIUM;
    this.deltaE = INITIAL_DELTA_E;
    this.dECW = INITIAL_DECW;
    this.glycogen = INITIAL_GLYCOGEN;
  }

  getBFP = () => {
    if (this.bfpCalc) {
      if (this.isMale) {
        this.bfp = 0.14 * this.age
          + 37.310000000000002 * Math.log(this.getBMI())
          - 103.94;
      } else {
        this.bfp = 0.14 * this.age
          + 39.960000000000001 * Math.log(this.getBMI())
          - 102.01000000000001;
      }
      this.bfp = this.bfp < 0.0 ? 0.0 : this.bfp;
      this.bfp = this.bfp > 60.0 ? 60.0 : this.bfp;
    }
    return this.bfp;
  };

  getHealthyWeightRange = () => {
    const healthyWeightRange = { low: 0, high: 0 };
    healthyWeightRange.low = Math.round(18.5 * Math.pow(this.height / 100, 2));
    healthyWeightRange.high = Math.round(25 * Math.pow(this.height / 100, 2));
    return healthyWeightRange;
  };

  getRMR = () => {
    if (!this.rmrCalc) {
      if (this.isMale) {
        this.rmr = 9.99 * this.weight
          + (625.0 * this.height) / 100.0
          - 4.92 * this.age
          + 5.0;
      } else {
        this.rmr = 9.99 * this.weight
          + (625.0 * this.height) / 100.0
          - 4.92 * this.age
          - 161.0;
      }
    }
    return this.rmr;
  };

  getNewRMR = (newWeight, day) => {
    let rmr = 0;

    if (this.isMale) {
      rmr = 9.99 * newWeight
        + (625.0 * this.height) / 100.0
        - 4.92 * (this.age + day / 365.0)
        + 5.0;
    } else {
      rmr = 9.99 * newWeight
        + (625.0 * this.height) / 100.0
        - 4.92 * (this.age + day / 365.0)
        - 161.0;
    }
    return rmr;
  };

  getMaintCals = () => this.pal * this.getRMR();

  getActivityParam = () => (0.9 * this.getRMR() * this.pal - this.getRMR()) / this.weight;

  getTEE = () => this.pal * this.getRMR();

  getActivityExpenditure = () => this.getTEE() - this.getRMR();

  getFatWeight = () => (this.weight * this.getBFP()) / 100.0;

  getLeanWeight = () => this.weight - this.getFatWeight();

  getK = () => 0.76 * this.getMaintCals()
    - this.deltaE
    - 22.0 * this.getLeanWeight()
    - 3.2 * this.getFatWeight()
    - this.getActivityParam() * this.weight;

  getBMI = () => this.weight / Math.pow(this.height / 100.0, 2.0);

  getNewBMI = (newWeight) => newWeight / Math.pow(this.height / 100.0, 2.0);

  getECW = () => {
    let ECW = 0;

    if (this.isMale) {
      ECW = 0.025 * this.age + 9.57 * this.height + 0.191 * this.weight - 12.4;
    } else {
      ECW = -4.0 + 5.98 * this.height + 0.167 * this.weight;
    }
    return ECW;
  };

  getNewECW = (days, newWeight) => {
    let ECW = 0;

    if (this.isMale) {
      ECW = 0.025 * (this.age + days / 365.0)
        + 9.57 * this.height
        + 0.191 * newWeight
        - 12.4;
    } else {
      ECW = -4.0 + 5.98 * this.height + 0.167 * newWeight;
    }
    return ECW;
  };

  proportionalSodium = (newCals) => (this.sodium * newCals) / this.getMaintCals();

  getCarbsIn = () => (this.carbIntakePercent / 100.0) * this.getMaintCals();

  setCalculatedBFP = (bfpcalc) => {
    this.bfp = this.getBFP();
    this.bfpCalc = bfpcalc;
  };

  setCalculatedRMR = (rmrcalc) => {
    this.rmr = this.getRMR();
    this.rmrCalc = rmrcalc;
  };

  getGlycogenH2O = (newGlycogen) => 3.7 * (newGlycogen - INITIAL_GLYCOGEN);

  getTherm = () => 0.14 * this.getTEE();

  getBodyComposition = () => [
    (this.weight * this.bfp) / 100.0,
    (this.weight * (100.0 - this.bfp)) / 100.0,
    this.dECW,
  ];

  getNewWeight = (fat, lean, glycogen, deltaECW) => fat + lean + this.getGlycogenH2O(glycogen) + deltaECW;

  glycogenEquation = (caloricIntake) => INITIAL_GLYCOGEN
    * Math.sqrt(
      ((this.carbIntakePercent / 100.0) * caloricIntake) / this.getCarbsIn(),
    );

  deltaECWEquation = (caloricIntake) => ((this.sodium / this.getMaintCals()
      + (4000.0 * this.carbIntakePercent) / (100.0 * this.getCarbsIn()))
      * caloricIntake
      - (this.sodium + 4000.0))
    / 3000.0;

  getStableWeight = (fat, lean, caloricIntake) => {
    const newGlycogen = this.glycogenEquation(caloricIntake);
    const glycogenH2O = this.getGlycogenH2O(newGlycogen);
    const deltaECW = this.deltaECWEquation(caloricIntake);

    return fat + lean + glycogenH2O + deltaECW;
  };
}
