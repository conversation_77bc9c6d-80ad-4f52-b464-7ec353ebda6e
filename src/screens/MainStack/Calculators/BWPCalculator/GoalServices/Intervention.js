/* eslint-disable no-mixed-operators */
import BodyModel from './BodyModel';

export const MIN_CALORIES = 0.0;
const INITIAL_CALORIES = 2200.0;
const MIN_CARB_INTAKE_PCT = 0.0;
const MAX_CARB_INTAKE_PCT = 100.0;
const INITIAL_CARB_INTAKE_PCT = 50.0;
const INITIAL_PAL = 1.6;
const MIN_SODIUM = 0.0;
const MAX_SODIUM = 50000.0;
const INITIAL_SODIUM = 4000.0;
const MIN_ACTIVITY_CHG_PCT = -100.0;
const INITIAL_ACTIVITY_CHG_PCT = 0.0;

export default class Intervention {
  day;

  calories;

  carbInPercent;

  activityChangePercent;

  sodium;

  PAL;

  on;

  rampOn;

  title;

  isDetailed;

  constructor(props) {
    this.calories = props.calories && props.calories >= MIN_CALORIES
      ? props.calories
      : INITIAL_CALORIES;
    this.carbInPercent = props.carbInPercent
      && props.carbInPercent >= MIN_CARB_INTAKE_PCT
      && props.carbInPercent <= MAX_CARB_INTAKE_PCT
      ? props.carbInPercent
      : INITIAL_CARB_INTAKE_PCT;
    this.PAL = INITIAL_PAL;
    this.sodium = props.sodium && props.sodium >= MIN_SODIUM && props.sodium <= MAX_SODIUM
      ? props.sodium
      : INITIAL_SODIUM;
    this.on = true;
    this.rampOn = false;
    this.activityChangePercent = props.activityChangePercent
      && props.activityChangePercent >= MIN_ACTIVITY_CHG_PCT
      ? props.activityChangePercent
      : INITIAL_ACTIVITY_CHG_PCT;
    this.day = props.day || 100;
    this.title = '';
    this.isDetailed = false;
  }

  static forGoal = (
    baseline,
    goalWeight,
    goalTime,
    activityChangePercent,
    minCals,
    eps,
  ) => {
    let holdCals = 0.0;

    // We create the Intervention
    const goalIntervention = new Intervention({});

    // We then set it's title and to start immediately
    goalIntervention.title = 'Goal Intervention';
    goalIntervention.day = 1;

    // We set the calories to their minimum
    goalIntervention.calories = minCals;

    // Enter in the ACTIVITY change level
    goalIntervention.activityChangePercent = activityChangePercent;

    // We use the baseline values for carbs and sodium)
    goalIntervention.carbInPercent = baseline.carbIntakePercent;
    goalIntervention.setProportionalSodium(baseline);

    if (baseline.weight === goalWeight && activityChangePercent === 0) {
      goalIntervention.calories = baseline.getMaintCals();
      goalIntervention.setProportionalSodium(baseline);
    } else {
      const starvtest = BodyModel.projectFromBaselineViaIntervention(
        baseline,
        goalIntervention,
        goalTime,
      );

      let startWeight = starvtest.getWeight(baseline);
      startWeight = startWeight < 0 ? 0 : startWeight;

      let error = Math.abs(startWeight - goalWeight);

      if (error < eps || goalWeight <= startWeight) {
        goalIntervention.calories = 0.0;
        throw new Error('Unachievable Goal');
      }

      let checkCals = minCals;
      let calStep = 200.0;

      let PCXerror = 0;
      do {
        holdCals = checkCals;
        checkCals += calStep;

        goalIntervention.calories = checkCals;
        goalIntervention.setProportionalSodium(baseline);

        const testBC = BodyModel.projectFromBaselineViaIntervention(
          baseline,
          goalIntervention,
          goalTime,
        );
        const testWeight = testBC.getWeight(baseline);

        if (testWeight < 0.0) {
          PCXerror += 1;

          if (PCXerror > 10) {
            throw new Error('Unachievable Goal');
          }
        }
        error = Math.abs(goalWeight - testWeight);

        if (error > eps && testWeight > goalWeight) {
          calStep /= 2.0;
          checkCals = holdCals;
        }
      } while (error > eps);
    }

    return goalIntervention;
  };

  getActivity = (baseline) => baseline.getActivityParam() * (1.0 + this.activityChangePercent / 100.0);

  setProportionalSodium = (baseline) => {
    this.sodium = (baseline.sodium * this.calories) / baseline.getMaintCals();
  };
}
