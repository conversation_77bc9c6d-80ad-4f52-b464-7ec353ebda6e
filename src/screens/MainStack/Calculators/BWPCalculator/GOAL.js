import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Image,
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import moment from 'moment';
import PropTypes from 'prop-types';
import DateTimePicker from 'react-native-modal-datetime-picker';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  updateFormData,
  changeUnits,
  syncUnits,
  clearFormData,
} from '../../../../actions';
import { UnitType } from '../../../../types/BWPTypes';
import ColoredHeadingBox from './ColoredHeadingBox';
import BWPNavigationFooter from './BWPNavigationFooter';
import BWPConstants from './BWPConstants';
import CheckReachableGoal from './GoalServices/CheckReachableGoal';
import ProgressBar from '../../../../components/ProgressBar';
import { DropDownPicker } from '../../../../components';
import { curvedScale } from '../../../../util/responsive';
import { colors } from '../../../../styles';
import { removeAllSpecialCharacters } from '../../../../util/validate';
import LoadingSpinner from '../../../../components/LoadingSpinner';

const imgArrow = require('../../../../resources/rightArrow.png');

const UNIT_OPTIONS = [
  {
    id: 1,
    label: UnitType.US,
  },
  {
    id: 2,
    label: UnitType.METRIC,
  },
];

const screenWidth = Dimensions.get('screen').width;
const useVerticalFlex = true;
const pickerWidth = useVerticalFlex
  ? screenWidth - curvedScale(40)
  : (screenWidth - curvedScale(50)) / 2;

const stateSelector = (state) => state;

const Goal = ({ navigation }) => {
  const dispatch = useDispatch();
  const { bwpCalculator } = useSelector(stateSelector);
  const initialState = {
    showDatePicker: false,
    goalWeight: bwpCalculator?.data?.goalWeight || 0,
    numberOfDays: bwpCalculator?.data?.numberOfDays || 0,
  };
  const [state, setState] = useState(initialState);
  const [calculating, setCalculating] = useState(false);

  useEffect(() => {
    const goalWeight = bwpCalculator?.data?.goalWeight;
    setState({
      ...state,
      goalWeight,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bwpCalculator?.data?.units]);

  useEffect(() => {
    navigation.setOptions({
      title: 'Body Weight Planner',
      headerTitleStyle: styles.headerTitle,
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setTimeout(() => {
      const numberOfDays = bwpCalculator?.data?.numberOfDays;
      const goalDate = moment(new Date(), 'MM/DD/YYYY')
        .add(Number(numberOfDays), 'days')
        .format('MM/DD/YYYY');
      updateGoalForm('goalDate', goalDate);
    }, 100);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bwpCalculator?.data?.numberOfDays]);

  const handlerBlur = (e, key) => {
    setCalculating(true);
    const fieldKey = key;
    const fieldValue = state[key];
    updateGoalForm(fieldKey, fieldValue);
  };

  const returnToDashboard = () => {
    dispatch(clearFormData());
    navigation.pop(3);
  };

  const goBack = () => {
    navigation.goBack();
  };

  const onPressNext = () => {
    saveStep3();
  };

  const saveStep3 = () => {
    const { status, message } = isValidForm();
    if (!status) {
      Alert.alert('Error', message);
    } else {
      navigation.navigate(BWPConstants.MACROS);
    }
  };

  const isValidForm = () => {
    let status = true;
    let message = '';
    const goalWeight = Math.round(Number(bwpCalculator?.data?.goalWeight));
    if (!goalWeight) {
      status = false;
      message = 'Please enter a valid goal weight.';
    } else if (!bwpCalculator?.data?.numberOfDays) {
      status = false;
      message = 'Please enter number of days.';
    } else if (bwpCalculator?.data?.numberOfDays > 180) {
      status = false;
      message = 'Number of days cannot be more than 6 months.';
    }
    return { status, message };
  };

  const stripNonNumericChars = (input, decimalAllowed) => {
    if (decimalAllowed) {
      return input.replace(/[^0-9.]/g, '');
    }
    return input.replace(/[^0-9]/g, '');
  };

  const getAllowedMaxInputLength = (maxLength, value) => {
    let allowedMaxLength = maxLength;
    const stringValue = value?.toString();
    if (stringValue && stringValue.length > 0 && stringValue.includes('.')) {
      const lengthBeforeDecimal = stringValue.split('.')[0].length;
      allowedMaxLength = lengthBeforeDecimal + 3;
    }
    return allowedMaxLength;
  };

  const getTextInputProps = (maxLength, customStyle, editable = true) => {
    const props = {
      maxLength,
      editable,
    };
    if (customStyle) {
      props.style = customStyle;
    }
    return props;
  };

  const updateGoalForm = (key, value) => {
    dispatch(updateFormData({ [key]: value }));
  };

  const getWeightUnit = () => (bwpCalculator?.data?.units === UnitType.METRIC ? 'kg' : 'lb');

  const onCalculationComplete = () => {
    setCalculating(false);
  };

  const renderHeaderLeft = () => (
    <TouchableOpacity onPress={returnToDashboard}>
      <Text style={styles.headerLeft}>Cancel</Text>
    </TouchableOpacity>
  );

  const renderHeaderRight = () => <Text style={styles.headerRight}>3/5</Text>;

  const renderProgressBar = () => (
    <ProgressBar
      progress={60}
      progressColor={colors.medYellow}
      barStyle={styles.barStyle}
      style={styles.progressBackground}
    />
  );

  const renderPageHeader = () => (
    <ColoredHeadingBox title="Goal Weight" backgroundColor={colors.darkPurple}>
      <Text style={styles.pageHeaderDescription}>
        <Text style={styles.username}>{bwpCalculator?.data?.clientName}</Text>
        {' '}
        wants to reach their goal weight of
        {' '}
        <Text style={styles.username}>{state.goalWeight}</Text>
        {' '}
        <Text style={styles.username}>{getWeightUnit()}</Text>
        {' '}
        in
        {' '}
        <Text style={styles.username}>{state.numberOfDays}</Text>
        {' '}
        days.
      </Text>
    </ColoredHeadingBox>
  );

  const renderTextInput = (
    key,
    unit,
    textInputProps,
    customStyle,
    decimalAllowed,
  ) => {
    let fieldName = key;
    let fieldValue = state[key];
    if (key === 'weight') {
      fieldName = 'Current Weight';
      fieldValue = bwpCalculator?.data?.[key];
    } else if (key === 'goalWeight') {
      fieldName = 'Goal Weight';
    } else if (key === 'numberOfDays') {
      fieldName = 'Number of Days';
    }
    const textInputCustomStyle = textInputProps?.style;
    delete textInputProps?.style;
    return (
      <View
        style={[
          styles.inputContainer,
          styles.customInputContainer,
          customStyle,
        ]}
      >
        <View style={styles.inputLabel}>
          <Text style={styles.input}>
            {`${fieldName}:`}
            {' '}
          </Text>
        </View>
        <View style={styles.inputView}>
          <TextInput
            style={[styles.input, styles.customTextInput, textInputCustomStyle]}
            placeholder="0"
            placeholderTextColor={colors.fillDarkGrey}
            value={`${fieldValue}`}
            onChangeText={(newValue) => {
              const filteredValue = removeAllSpecialCharacters(newValue);
              setState({
                ...state,
                [key]: stripNonNumericChars(filteredValue, decimalAllowed),
              });
            }}
            onBlur={(e) => {
              handlerBlur(e, key);
            }}
            keyboardType="number-pad"
            returnKeyType="done"
            contextMenuhidden
            {...textInputProps}
          />
          <Text style={styles.input}>
            {' '}
            {unit.toLowerCase()}
          </Text>
        </View>
      </View>
    );
  };

  const renderWeight = () => (
    <View style={styles.weightContainer}>
      <DropDownPicker
        useNativeAndroidPickerStyle
        data={UNIT_OPTIONS}
        label="Units:"
        selected={
          UNIT_OPTIONS.find(
            (option) => option.label === bwpCalculator?.data?.units,
          ).id
        }
        onValueChange={(selectedUnit) => {
          if (bwpCalculator?.prevUnits !== selectedUnit.label) {
            dispatch(changeUnits(selectedUnit.label));
            dispatch(syncUnits());
            setCalculating(true);
          }
        }}
        labelStyle={styles.pickerItem}
        containerStyle={styles.picker}
      />
      {renderTextInput(
        'weight',
        getWeightUnit(),
        getTextInputProps(
          getAllowedMaxInputLength(4, bwpCalculator?.data?.weight),
          { color: colors.subGreyTwo },
          false,
        ),
        null,
        true,
      )}
    </View>
  );

  const renderGoalWeightAndDays = () => (
    <View>
      <View style={styles.goalWeightView}>
        {renderTextInput(
          'goalWeight',
          getWeightUnit(),
          getTextInputProps(
            getAllowedMaxInputLength(4, bwpCalculator?.data?.goalWeight),
          ),
          null,
          true,
        )}
        {renderTextInput('numberOfDays', '', getTextInputProps(3))}
      </View>
    </View>
  );

  const renderLoader = () => (
    <LoadingSpinner
      backgroundColor="rgba(0, 0, 0, 0.25)"
      color={colors.subGrey}
      titleTextStyle={{ color: colors.subGrey }}
      visible={calculating}
    />
  );

  const renderGoalDate = () => (
    <View style={[styles.goalDateView, styles.marginTop]}>
      <Text style={styles.selectDateLabel}>or select a date</Text>
      <TouchableOpacity
        style={[styles.inputContainer, styles.customInputContainer]}
        onPress={() => {
          setState({
            ...state,
            showDatePicker: !state.showDatePicker,
          });
        }}
      >
        <Text style={[styles.pickerItem, styles.date]}>Goal Date:</Text>
        <View style={styles.inputView}>
          <Text style={[styles.pickerItem, styles.date]}>
            {bwpCalculator?.data?.goalDate}
          </Text>
          <Image style={styles.arrow} source={imgArrow} />
        </View>
      </TouchableOpacity>
      {renderDatePicker()}
    </View>
  );

  const renderDatePicker = () => (
    <DateTimePicker
      isVisible={state.showDatePicker}
      date={moment(bwpCalculator?.data?.goalDate, 'MM/DD/YYYY').toDate()}
      mode="date"
      display={Platform.OS === 'ios' ? 'spinner' : 'calendar'}
      minimumDate={new Date()}
      onConfirm={(newDate) => {
        const goalDays = moment(newDate).diff(moment(), 'days', true);
        updateGoalForm('numberOfDays', Math.ceil(goalDays));
        setState({
          ...state,
          showDatePicker: false,
          numberOfDays: Math.ceil(goalDays),
        });
        const goalDate = moment(newDate, 'MM/DD/YYYY').format('MM/DD/YYYY');
        updateGoalForm('goalDate', goalDate);
      }}
      onCancel={() => {
        setState({
          ...state,
          showDatePicker: false,
        });
      }}
    />
  );

  const renderPageContent = () => (
    <KeyboardAwareScrollView
      style={styles.pageContent}
      showsVerticalScrollIndicator={false}
    >
      {renderPageHeader()}
      <View style={styles.pageView}>
        {renderWeight()}
        {renderGoalWeightAndDays()}
        {renderGoalDate()}
        <CheckReachableGoal
          calculating={calculating}
          onCalculationComplete={onCalculationComplete}
        />
      </View>
    </KeyboardAwareScrollView>
  );

  const renderPageFooter = () => (
    <BWPNavigationFooter
      currentStep={3}
      onPressNext={onPressNext}
      onPressPrevious={goBack}
    />
  );

  return (
    <View style={styles.container}>
      {renderProgressBar()}
      {renderPageContent()}
      {renderPageFooter()}
      {renderLoader()}
    </View>
  );
};

const styles = StyleSheet.create({
  headerLeft: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    marginLeft: curvedScale(10),
    width: curvedScale(100),
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
    alignSelf: 'center',
  },
  headerRight: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    marginRight: curvedScale(10),
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  barStyle: {
    borderRadius: 0,
  },
  progressBackground: {
    borderRadius: 0,
    width: '100%',
    borderWidth: 0,
    backgroundColor: colors.nasmBlue,
    height: 12,
  },
  pageHeaderDescription: {
    color: colors.white,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
    marginTop: curvedScale(20),
  },
  username: {
    fontFamily: 'Avenir-Heavy',
    fontWeight: '800',
  },
  pageContent: {
    height: '60%',
  },
  pageView: {
    paddingHorizontal: curvedScale(20),
  },
  picker: {
    width: pickerWidth,
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    height: curvedScale(40),
    marginVertical: 0,
  },
  pickerItem: {
    color: colors.black,
    fontSize: 14,
    fontFamily: 'Avenir-Light',
    fontWeight: '500',
    paddingHorizontal: 8,
    textAlign: 'left',
  },
  weightContainer: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginTop: curvedScale(20),
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    padding: curvedScale(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(40),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
  },
  customInputContainer: {
    width: pickerWidth,
    marginLeft: useVerticalFlex ? 0 : curvedScale(10),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
  },
  inputLabel: {
    width: '40%',
  },
  input: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
  },
  customTextInput: {
    width: '100%',
    textAlign: 'right',
    paddingVertical: 0,
  },
  goalWeightView: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    flex: 1,
    alignItems: 'center',
    marginTop: curvedScale(10),
  },
  goalDateView: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    marginTop: curvedScale(10),
  },
  selectDateLabel: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    fontWeight: '500',
    marginVertical: curvedScale(5),
  },
  date: {
    fontSize: curvedScale(14),
  },
  arrow: {
    transform: [{ rotate: '90deg' }],
    tintColor: colors.black,
  },
  inputView: {
    flexDirection: 'row',
    width: '60%',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  marginTop: {
    marginTop: curvedScale(20),
  },
});

Goal.propTypes = {
  navigation: PropTypes.object.isRequired,
};

export default Goal;
