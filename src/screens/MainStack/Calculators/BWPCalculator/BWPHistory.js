import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  Alert,
  FlatList,
  Image,
  StatusBar,
  TouchableOpacity,
  View,
  RefreshControl,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import PropTypes from 'prop-types';
import moment from 'moment';
import DeviceInfo from 'react-native-device-info';
import nasm from '../../../../dataManager/apiConfig';
import { ScaledText } from '../../../../components';
import { curvedScale } from '../../../../util/responsive';
import { colors } from '../../../../styles';
import { ROLES } from '../../../../constants';
import { calculate_macro_percentage } from '../../../../util/CalculatorUtils';

const imgHeart = require('../../../../resources/heart.png');
const imgFire = require('../../../../resources/fire_bwp.png');

const propTypes = {
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    role: PropTypes.string,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.object.isRequired,
};

const defaultProps = {};

class HistoryNutrition extends Component {
  constructor(props) {
    super(props);
    this.state = {
      nutritionData: [],
      page: 1,
      isNextPage: false,
      isLoading: true,
      refreshing: false,
      resultSize: 20,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      this.setState({ page: 1 }, () => {
        this.getNutritionData();
      });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onRefresh = () => {
    this.setState({ refreshing: true, page: 1 }, () => {
      this.getNutritionData();
    });
  };

  onScroll = () => {
    if (
      this.state.isNextPage
      && !this.state.isLoading
      && !this.state.refreshing
    ) {
      const { page } = this.state;
      this.setState({ isLoading: true, page: page + 1 }, () => {
        this.getNutritionData();
      });
    }
  };

  getNutritionData = async () => {
    const { nutritionData, page, resultSize } = this.state;
    try {
      const userObject = this.props.currentUser.role === ROLES.TRAINER
        ? this.props.selectedClient
        : this.props.currentUser;
      const history = await nasm.api.getNutritionsHistory(
        userObject.id,
        page,
        resultSize,
      );
      let dataArr = [];
      dataArr = page === 1 ? history : [...nutritionData, ...history];
      this.setState({
        refreshing: false,
        isLoading: false,
        nutritionData: dataArr,
        isNextPage: history?.length > 0,
      });
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Failed to load BWP history. Try again later.',
      );
    }
  };

  renderHeader = () => (
    <View style={styles.headerContainer}>
      {this.renderHeaderItem(imgHeart, 'BMR', {
        ...styles.dotBigStyle,
        backgroundColor: colors.medYellow,
      })}
      {this.renderHeaderItem(imgFire, 'TDEE', {
        ...styles.dotBigStyle,
        backgroundColor: colors.pinkishPurple,
      })}
      {this.renderHeaderItem('', 'protein', {
        ...styles.dotBigStyle,
        backgroundColor: colors.medYellow,
      })}
      {this.renderHeaderItem('', 'carbs', {
        ...styles.dotBigStyle,
        backgroundColor: colors.pinkishPurple,
      })}
      {this.renderHeaderItem('', 'fat', {
        ...styles.dotBigStyle,
        backgroundColor: colors.nasmBlue,
      })}
    </View>
  );

  renderHeaderItem = (icon, title, dotStyle) => (
    <View style={styles.dotAndLabel}>
      {icon ? (
        <Image source={icon} style={styles.icon} />
      ) : (
        <View style={dotStyle} />
      )}
      <ScaledText style={styles.textHeaderStyle}>{title}</ScaledText>
    </View>
  );

  renderProgressView = (color, width) => (
    <View style={{ ...styles.progressView, backgroundColor: color, width }} />
  );

  renderCell = ({ item }) => (
    <View>
      <TouchableOpacity style={styles.itemCell}>
        <View style={styles.textSection}>
          <ScaledText style={styles.textDateStyle(20)}>
            {moment(item.created_at).format('MM/DD/YYYY')}
          </ScaledText>
          {this.renderHeaderItem(imgHeart, item.basal_metabolic_rate, {
            ...styles.dotBigStyle,
            backgroundColor: colors.pinkishPurple,
          })}
          {this.renderHeaderItem(imgFire, item.total_daily_energy_expenditure, {
            ...styles.dotBigStyle,
            backgroundColor: colors.nasmBlue,
            marginLeft: 10,
          })}
          {this.renderHeaderItem(
            '',
            `Weight: ${item.start_weight.toFixed(2)} ${item.unit_weight}`,
            { ...styles.dotSmallStyle, backgroundColor: '' },
          )}
        </View>
        <View style={styles.textSection}>
          <ScaledText style={styles.textDateStyle(16)}>
            {`Goal: ${item.goal_weight} ${item.unit_weight}`}
          </ScaledText>
          {this.renderHeaderItem(
            '',
            `${calculate_macro_percentage(
              item.nutrition.protein,
              item.daily_goal_intake,
              4,
            )}%`,
            {
              ...styles.dotSmallStyle,
              backgroundColor: colors.medYellow,
            },
          )}
          {this.renderHeaderItem(
            '',
            `${calculate_macro_percentage(
              item.nutrition.carbohydrates,
              item.daily_goal_intake,
              4,
            )}%`,
            {
              ...styles.dotSmallStyle,
              backgroundColor: colors.pinkishPurple,
            },
          )}
          {this.renderHeaderItem(
            '',
            `${calculate_macro_percentage(
              item.nutrition.fat,
              item.daily_goal_intake,
              9,
            )}%`,
            {
              ...styles.dotSmallStyle,
              backgroundColor: colors.nasmBlue,
            },
          )}
          {item.nutrition.calories
            ? this.renderHeaderItem('', `Cal: ${item.nutrition.calories}`, {
              ...styles.dotSmallStyle,
              backgroundColor: '',
            })
            : null}
        </View>
        <View style={styles.progressSection}>
          {this.renderProgressView(
            colors.medYellow,
            `${calculate_macro_percentage(
              item.nutrition.protein,
              item.daily_goal_intake,
              4,
            )}%`,
          )}
          {this.renderProgressView(
            colors.darkPurple,
            `${calculate_macro_percentage(
              item.nutrition.carbohydrates,
              item.daily_goal_intake,
              4,
            )}%`,
          )}
          {this.renderProgressView(
            colors.nasmBlue,
            `${calculate_macro_percentage(
              item.nutrition.fat,
              item.daily_goal_intake,
              9,
            )}%`,
          )}
        </View>
      </TouchableOpacity>
    </View>
  );

  render() {
    const {
      refreshing, nutritionData, page, isLoading,
    } = this.state;
    return (
      <SafeAreaView style={styles.container}>
        {nutritionData.length ? this.renderHeader() : null}
        {isLoading && !refreshing && page === 1 ? (
          <View style={styles.loaderView}>
            <ActivityIndicator size="large" visible={isLoading} />
          </View>
        ) : (
          <View style={styles.listContainer}>
            <FlatList
              ref={(ref) => {
                this.flatList = ref;
              }}
              refreshControl={(
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={this.onRefresh}
                />
              )}
              contentContainerStyle={
                nutritionData.length ? null : { flexGrow: 1 }
              }
              data={nutritionData}
              renderItem={(item) => this.renderCell(item)}
              ListFooterComponent={() => (
                <View style={styles.loaderView}>
                  {isLoading ? (
                    <ActivityIndicator size="large" visible={isLoading} />
                  ) : null}
                </View>
              )}
              ListEmptyComponent={() => (
                <View style={styles.emptyDataContainer}>
                  {!isLoading && !refreshing ? (
                    <View style={styles.emptyDataView}>
                      <ScaledText style={styles.emptyDataMessage}>
                        No history to show
                      </ScaledText>
                    </View>
                  ) : null}
                </View>
              )}
              onEndReached={this.onScroll}
              onEndReachedThreshold={0.5}
            />
          </View>
        )}
      </SafeAreaView>
    );
  }
}

const styles = {
  container: {
    flex: 1,
  },
  listContainer: {
    flex: 1,
    width: '100%',
  },
  itemCell: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: 'rgb(242,243,243)',
  },
  dotAndLabel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  dotBigStyle: {
    width: curvedScale(10),
    height: curvedScale(10),
    borderRadius: curvedScale(5),
  },
  dotSmallStyle: {
    width: curvedScale(5),
    height: curvedScale(5),
    borderRadius: curvedScale(2.5),
  },
  headerContainer: {
    height: curvedScale(60),
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    borderBottomWidth: 1,
    borderBottomColor: colors.subGreyLight,
  },
  textSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: curvedScale(20),
    paddingTop: curvedScale(15),
  },
  textHeaderStyle: {
    fontFamily: 'Avenir-Roman',
    color: colors.lightBlack,
    fontSize: curvedScale(14),
    paddingLeft: curvedScale(4),
  },
  textDateStyle: (fontSize) => ({
    fontFamily: 'Avenir-Medium',
    color: colors.lightBlack,
    fontSize,
    paddingRight: curvedScale(10),
  }),
  progressSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: curvedScale(20),
    paddingVertical: curvedScale(15),
    maxWidth: '100%',
  },
  progressView: {
    height: curvedScale(4),
  },
  icon: {
    width: curvedScale(14),
    height: curvedScale(14),
  },
  loaderView: {
    marginVertical: curvedScale(10),
  },
  emptyDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyDataView: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: curvedScale(20),
  },
  emptyDataMessage: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: DeviceInfo.isTablet() ? curvedScale(10) : curvedScale(14),
  },
};

HistoryNutrition.propTypes = propTypes;
HistoryNutrition.defaultProps = defaultProps;

const mapStateToProps = ({ currentUser, selectedClient }) => ({
  currentUser,
  selectedClient,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(HistoryNutrition);
