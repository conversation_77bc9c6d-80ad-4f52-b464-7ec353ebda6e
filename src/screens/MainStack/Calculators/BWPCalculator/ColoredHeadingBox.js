import React from 'react';
import { Text, StyleSheet, View } from 'react-native';
import PropTypes from 'prop-types';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';

const ColoredHeadingBoxProps = {
  backgroundColor: PropTypes.string,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  children: PropTypes.any,
  viewStyle: PropTypes.object,
  titleStyle: PropTypes.object,
};

const defaultProps = {
  backgroundColor: null,
  title: null,
  subtitle: null,
  children: null,
  viewStyle: null,
  titleStyle: null,
};

const ColoredHeadingBox = (props) => {
  const {
    backgroundColor,
    children,
    title,
    subtitle,
    viewStyle,
    titleStyle,
  } = props;
  return (
    <View style={[styles.headerView, { backgroundColor }, viewStyle]}>
      <Text style={[styles.title, titleStyle]}>
        {title}
        {' '}
        <Text style={styles.subTitle}>{subtitle}</Text>
      </Text>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  headerView: {
    padding: curvedScale(20),
  },
  title: {
    color: colors.white,
    fontSize: curvedScale(20),
    fontFamily: 'Avenir-Heavy',
  },
  subTitle: {
    fontFamily: 'Avenir-Roman',
  },
});

ColoredHeadingBox.propTypes = ColoredHeadingBoxProps;
ColoredHeadingBox.defaultProps = defaultProps;
export default ColoredHeadingBox;
