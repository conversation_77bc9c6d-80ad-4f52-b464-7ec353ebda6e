import React, { useEffect, useState } from 'react';
import {
  Image,
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  Alert,
  Platform,
  UIManager,
  Keyboard,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import PropTypes from 'prop-types';
import { colors } from '../../../../styles';
import { clearFormData } from '../../../../actions';
import BWPConstants from './BWPConstants';
import { curvedScale } from '../../../../util/responsive';
import { onPressExternalLink } from '../../../../util/utils';

const propTypes = {
  currentStep: PropTypes.number.isRequired,
  onPressStart: PropTypes.func,
  onPressPrevious: PropTypes.func,
  onPressNext: PropTypes.func,
  isTrainer: PropTypes.bool,
};

const defaultProps = {
  onPressStart: null,
  onPressPrevious: null,
  onPressNext: null,
  onPressFinish: null,
  isTrainer: false,
};

const imgArrow = require('../../../../resources/rightArrow.png');
const imgCheckmarkNav = require('../../../../resources/btnCheckmarkNav.png');

const BWPNavigationFooter = ({
  currentStep,
  onPressStart,
  onPressPrevious,
  onPressNext,
  isTrainer,
}) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const customStyle = {
    opacity: currentStep === 1 ? 0.2 : 1,
  };
  const isAndroid = Platform.OS === 'android';
  if (isAndroid) {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const setAnimation = () => {
    Keyboard.scheduleLayoutAnimation({ duration: 100, easing: 'easeIn' });
  };

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setAnimation();
      setKeyboardVisible(true);
    });
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setAnimation();
      setKeyboardVisible(false);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);
  const resetCalulation = () => {
    Alert.alert(
      'Are you sure you want to start over?',
      'Any information entered in will be lost.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Start Over',
          style: 'destructive',
          onPress: () => {
            dispatch(clearFormData());
            navigation.navigate({ name: BWPConstants.BMR, merge: true });
          },
        },
      ],
      { cancelable: false },
    );
  };

  if (isAndroid && keyboardVisible) {
    return null;
  }
  return (
    <View style={styles.container}>
      {isTrainer && currentStep === 1 ? (
        <TouchableOpacity style={styles.startView} onPress={onPressStart}>
          <Text style={styles.start}>Start</Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.controller}>
          <TouchableOpacity
            style={[styles.arrowView, customStyle]}
            onPress={onPressPrevious}
            disabled={currentStep === 1}
          >
            <Image
              style={[styles.arrow, styles.previousArrow, customStyle]}
              source={imgArrow}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={resetCalulation}>
            <Text style={styles.reset}>Reset</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.arrowView,
              currentStep === 4 ? styles.customArrowView : null,
            ]}
            onPress={onPressNext}
          >
            <Image
              style={[
                styles.arrow,
                currentStep === 4 ? styles.customArrow : null,
              ]}
              source={currentStep < 4 ? imgArrow : imgCheckmarkNav}
            />
          </TouchableOpacity>
        </View>
      )}
      <View style={styles.bottomLinkContainer}>
        <TouchableOpacity
          onPress={() => onPressExternalLink('https://www.niddk.nih.gov/bwp')}
        >
          <Text style={styles.bottomLink}>
            Based on the NIH Body Weight Planner
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => onPressExternalLink('https://pubmed.ncbi.nlm.nih.gov/2305711/')}
        >
          <Text style={styles.bottomLink}>
            Based on the NIH Resting Energy Article
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: curvedScale(20),
    justifyContent: 'center',
  },
  controller: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  startView: {
    backgroundColor: colors.darkOrange,
    borderRadius: 25,
    alignSelf: 'center',
  },
  start: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(15),
    paddingHorizontal: curvedScale(40),
    paddingVertical: curvedScale(10),
  },
  previousArrow: {
    transform: [{ rotate: '180deg' }],
  },
  reset: {
    fontSize: curvedScale(15),
    fontFamily: 'Avenir-Medium',
    color: colors.fillDarkGrey,
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    borderRadius: 25,
    paddingHorizontal: curvedScale(40),
    paddingVertical: curvedScale(10),
  },
  arrowView: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    borderRadius: 100,
    padding: curvedScale(20),
  },
  customArrowView: {
    backgroundColor: colors.avatarGreen,
  },
  arrow: {
    tintColor: colors.fillDarkGrey,
    width: curvedScale(25),
    height: curvedScale(25),
  },
  customArrow: {
    tintColor: colors.white,
  },
  bottomLinkContainer: {
    marginVertical: curvedScale(20),
  },
  bottomLink: {
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Medium',
    color: colors.fillDarkGrey,
    alignSelf: 'center',
    textDecorationLine: 'underline',
    lineHeight: 20,
  },
});

BWPNavigationFooter.propTypes = propTypes;
BWPNavigationFooter.defaultProps = defaultProps;

export default BWPNavigationFooter;
