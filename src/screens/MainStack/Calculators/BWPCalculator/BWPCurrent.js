import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  View,
  SafeAreaView,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import moment from 'moment';
import PropTypes from 'prop-types';
import Results from './RESULTS';
import nasm from '../../../../dataManager/apiConfig';
import { colors } from '../../../../styles';
import { logScreenException } from '../../../../util/logging';
import { ROLES } from '../../../../constants';
import { clearFormData, updateFormData } from '../../../../actions';
import { UnitType } from '../../../../types/BWPTypes';
import {
  calculate_macro_percentage,
  get_height_from_inches,
} from '../../../../util/CalculatorUtils';
import BWPFTU from './BWPFTU';
import FloatingButton from '../../../../components/FloatingButton';
import BWPConstants from './BWPConstants';

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    role: PropTypes.string,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  bwpCalculator: PropTypes.shape({
    id: PropTypes.string,
    data: PropTypes.object,
  }).isRequired,
  clearFormData: PropTypes.func,
  updateFormData: PropTypes.func,
};

const defaultProps = {
  clearFormData: null,
  updateFormData: null,
};

class BWPCurrent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: true,
    };
  }

  componentDidMount() {
    this.props.navigation.addListener('focus', () => {
      this.setState({ loading: true }, () => {
        this.getCurrentBWP();
      });
    });
  }

  onPressAdd = () => {
    this.props.clearFormData();
    this.props.navigation.navigate({ name: BWPConstants.BMR, merge: true });
  };

  getCurrentBWP = async () => {
    // if we have client user ID for this screen in redux, get their current body weight plan
    try {
      const userObject = this.props.currentUser.role === ROLES.TRAINER
        ? this.props.selectedClient
        : this.props.currentUser;
      const response = await nasm.api.getCurrentBWP(userObject.id);
      if (response) {
        const height_ft = response.unit_height === 'in'
          ? get_height_from_inches(response.height).feet
          : null;
        const height_in = response.unit_height === 'in'
          ? get_height_from_inches(response.height).inches
          : null;
        const height_cm = response.height;
        const numberOfDays = moment(response.end_date).isAfter(moment())
          ? Math.ceil(
            moment(response.end_date, 'YYYY-MM-DD').diff(
              moment(),
              'days',
              true,
            ),
          )
          : Math.ceil(moment().diff(response.end_date, 'days', true));

        const bwpCalculatorData = {
          units: response.unit_weight === 'lb' ? UnitType.US : UnitType.METRIC,
          clientName: userObject.full_name,
          sex: response.gender,
          weight: response.start_weight,
          height: response.height,
          height_ft,
          height_in,
          height_cm,
          age: response.age,
          goalWeight: response.goal_weight,
          numberOfDays,
          goalDate: moment(response.end_date, 'YYYY-MM-DD').format(
            'MM/DD/YYYY',
          ),
          proteinPercent: calculate_macro_percentage(
            response.nutrition.protein,
            response.daily_goal_intake,
            4,
          ),
          carbohydratesPercent: calculate_macro_percentage(
            response.nutrition.carbohydrates,
            response.daily_goal_intake,
            4,
          ),
          fatsPercent: calculate_macro_percentage(
            response.nutrition.fat,
            response.daily_goal_intake,
            9,
          ),
          currentActivityLevel: response.activity_level.coefficient,
          endDate: response.end_date,
        };
        this.props.updateFormData(bwpCalculatorData);
      } else {
        this.props.clearFormData();
      }
    } catch (err) {
      Alert.alert(
        'Error',
        'Unable to fetch current body weight plan. Please try again later.',
      );
      logScreenException('Current BWP', err);
    } finally {
      this.setState({ loading: false });
    }
  };

  renderPlusButton = () => <FloatingButton onPress={this.onPressAdd} />;

  renderLoader = () => (
    <View style={styles.loaderView}>
      <ActivityIndicator animating color={colors.subGrey} size="large" />
    </View>
  );

  renderFTUE = () => <BWPFTU navigation={this.props.navigation} />;

  renderData = () => (
    <View style={styles.container}>
      <Results isCurrent headerTitle="Current" />
      {this.renderPlusButton()}
      <SafeAreaView />
    </View>
  );

  render() {
    if (this.state.loading) {
      return this.renderLoader();
    }
    if (!this.props.bwpCalculator?.data?.weight) {
      return this.renderFTUE();
    }
    return this.renderData();
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loaderView: {
    flex: 1,
    justifyContent: 'center',
  },
});

const mapStateToProps = ({ currentUser, selectedClient, bwpCalculator }) => ({
  currentUser,
  selectedClient,
  bwpCalculator,
});

const mapDispatchToProps = {
  clearFormData,
  updateFormData,
};

BWPCurrent.propTypes = propTypes;
BWPCurrent.defaultProps = defaultProps;

export default connect(mapStateToProps, mapDispatchToProps)(BWPCurrent);
