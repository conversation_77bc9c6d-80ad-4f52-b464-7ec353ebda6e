import React, { useEffect } from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Alert,
  Platform,
  NativeModules,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  initializeFormData,
  updateFormData,
  changeUnits,
  syncUnits,
  clearFormData,
} from '../../../../actions';
import { GenderType, UnitType } from '../../../../types/BWPTypes';
import {
  convert_from_kilograms,
  convert_to_kilograms,
} from '../../../../util/CalculatorUtils';
import ColoredHeadingBox from './ColoredHeadingBox';
import BWPNavigationFooter from './BWPNavigationFooter';
import BWPConstants from './BWPConstants';
import nasm from '../../../../dataManager/apiConfig';
import ProgressBar from '../../../../components/ProgressBar';
import { DropDownPicker } from '../../../../components';
import { ROLES } from '../../../../constants';
import { curvedScale } from '../../../../util/responsive';
import { colors } from '../../../../styles';
import { removeAllSpecialCharacters } from '../../../../util/validate';
import { CALCULATOR_CONTEXTS } from '../../../../reducers/calculatorContextReducer';

const { AppKeyboardHandlerMethods } = NativeModules;

const UNIT_OPTIONS = [
  {
    id: 1,
    label: UnitType.US,
  },
  {
    id: 2,
    label: UnitType.METRIC,
  },
];

const GENDER_OPTIONS = [
  {
    id: 1,
    label: GenderType.male,
  },
  {
    id: 2,
    label: GenderType.female,
  },
];

const screenWidth = Dimensions.get('screen').width;
const useVerticalFlex = screenWidth <= 375;

const stateSelector = (state) => state;

const BMR = () => {
  const navigation = useNavigation();
  const {
    currentUser,
    selectedClient,
    bwpCalculator,
    calculatorContext,
  } = useSelector(stateSelector);
  const dispatch = useDispatch();

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustResize();
      }
    });

    const unsubscribeBlur = navigation.addListener('blur', () => {
      if (Platform.OS === 'android') {
        AppKeyboardHandlerMethods.setAdjustPan();
      }
    });
    return () => {
      if (unsubscribeFocus) {
        unsubscribeFocus();
      }
      if (unsubscribeBlur) {
        unsubscribeBlur();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    navigation.setOptions({
      title: 'Body Weight Planner',
      headerTitleStyle: styles.headerTitle,
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    });
  });

  useEffect(() => {
    if (calculatorContext?.type === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      dispatch(clearFormData());
      return;
    }
    const initialClientInfo = currentUser.role === ROLES.CLIENT ? currentUser : selectedClient;
    nasm.api.getActivityLevels().then((activityLevels) => {
      const filteredLevel = activityLevels.find(
        (activityLevel) => activityLevel.id
            === initialClientInfo.client_user.activity_level_id,
      ) || activityLevels[0];
      const clientInfo = {
        ...initialClientInfo,
      };
      clientInfo.activityLevel = filteredLevel;
      clientInfo.allActivityLevels = activityLevels;
      dispatch(initializeFormData(clientInfo));
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // convert weight when unit measurement changes
  useEffect(() => {
    if (calculatorContext?.type === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      dispatch(clearFormData());
      return;
    }
    if (bwpCalculator) {
      const { prevUnits, data } = bwpCalculator;
      if (prevUnits !== data?.units) {
        let convertedWeight = 0;
        if (data?.units === UnitType.US) {
          convertedWeight = convert_from_kilograms(data?.weight);
        } else if (data?.units === UnitType.METRIC) {
          convertedWeight = convert_to_kilograms(data?.weight);
        }
        updateForm1('weight', convertedWeight);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const returnToDashboard = () => {
    dispatch(clearFormData());
    navigation.pop(1);
  };

  const onPressNext = () => {
    saveStep1();
  };

  const saveStep1 = () => {
    const { status, message } = isValidForm();
    if (!status) {
      Alert.alert('Error', message);
    } else {
      navigation.navigate(BWPConstants.TDEE);
    }
  };

  const isValidForm = () => {
    let status = true;
    let message = '';
    if (
      currentUser.role === ROLES.TRAINER
      && !bwpCalculator?.data?.clientName
    ) {
      status = false;
      message = 'Please enter a client name.';
    } else if (!Number(bwpCalculator?.data?.weight)) {
      status = false;
      message = 'Please enter a valid weight.';
    } else if (
      bwpCalculator?.data?.units === UnitType.US
      && !Number(bwpCalculator?.data?.height_ft)
    ) {
      status = false;
      message = 'Please enter a valid height (ft).';
    } else if (
      bwpCalculator?.data?.units === UnitType.METRIC
      && !Number(bwpCalculator?.data?.height_cm)
    ) {
      status = false;
      message = 'Please enter a valid height (cm).';
    } else if (!Number(bwpCalculator?.data?.age)) {
      status = false;
      message = 'Please enter a valid age.';
    }
    return { status, message };
  };

  const stripNonNumericChars = (input, decimalAllowed) => {
    if (decimalAllowed) {
      return input.replace(/[^0-9.]/g, '');
    }
    return input.replace(/[^0-9]/g, '');
  };

  const getAllowedMaxInputLength = (maxLength, value) => {
    let allowedMaxLength = maxLength;
    const stringValue = value?.toString();
    if (stringValue && stringValue.length > 0 && stringValue.includes('.')) {
      const lengthBeforeDecimal = stringValue.split('.')[0].length;
      allowedMaxLength = lengthBeforeDecimal + 3;
    }
    return allowedMaxLength;
  };

  const getTextInputProps = (maxLength, customStyle) => {
    const props = {
      maxLength,
    };
    if (customStyle) {
      props.style = customStyle;
    }
    return props;
  };

  const updateForm1 = (key, value) => {
    dispatch(updateFormData({ [key]: value }));
  };

  const getSelectedGender = () => {
    let selectedGenderId = GENDER_OPTIONS[0].id;
    if (bwpCalculator?.data?.sex) {
      selectedGenderId = GENDER_OPTIONS.find(
        (gender) => gender.label.toLowerCase()
          === bwpCalculator?.data?.sex?.toLowerCase(),
      ).id;
    }
    return selectedGenderId;
  };

  const renderHeaderLeft = () => (
    <TouchableOpacity onPress={returnToDashboard}>
      <Text style={styles.headerLeft}>Cancel</Text>
    </TouchableOpacity>
  );

  const renderHeaderRight = () => <Text style={styles.headerRight}>1/5</Text>;

  const renderProgressBar = () => (
    <ProgressBar
      progress={20}
      progressColor={colors.medYellow}
      barStyle={styles.barStyle}
      style={styles.progressBackground}
    />
  );

  const renderPageHeader = () => (
    <ColoredHeadingBox
      title="Basal Metabolic Rate"
      subtitle="(BMR)"
      backgroundColor={colors.duskBlue}
    >
      <Text style={styles.pageHeaderDescription}>
        Enter the following information to calculate the client&apos;s Basal
        Metabolic Rate (BMR).
      </Text>
    </ColoredHeadingBox>
  );

  const renderTextInput = (
    key,
    unit,
    textInputProps,
    customStyle,
    decimalAllowed,
  ) => {
    let fieldName = key;
    const fieldValue = bwpCalculator?.data[key.toLowerCase()];
    if (unit === 'ft') {
      fieldName = 'Feet';
    } else if (unit === 'in') {
      fieldName = 'Inches';
    } else if (unit === 'cm') {
      fieldName = 'Centimeters';
    }
    const leftMargin = {
      marginLeft: Platform.OS === 'ios' ? curvedScale(5) : 0,
    };
    return (
      <View
        style={[
          styles.inputContainer,
          styles.customInputContainer,
          customStyle,
        ]}
      >
        <View style={styles.inputLabel}>
          <Text style={styles.input}>
            {`${fieldName}:`}
            {' '}
          </Text>
        </View>
        <View style={styles.inputView}>
          <TextInput
            style={[styles.input, styles.customTextInput]}
            placeholder="0"
            placeholderTextColor={colors.fillDarkGrey}
            value={`${fieldValue}`}
            onChangeText={(newValue) => {
              const filteredValue = removeAllSpecialCharacters(newValue);
              if (unit === 'in' && filteredValue) {
                if (parseInt(filteredValue, 10) < 12) {
                  updateForm1(
                    `${[key.toLowerCase()]}`,
                    stripNonNumericChars(filteredValue, decimalAllowed),
                  );
                }
              } else {
                updateForm1(
                  `${[key.toLowerCase()]}`,
                  stripNonNumericChars(filteredValue, decimalAllowed),
                );
              }
            }}
            keyboardType="number-pad"
            returnKeyType="done"
            contextMenuhidden
            {...textInputProps}
          />
          <Text style={[styles.input, leftMargin]}>{unit.toLowerCase()}</Text>
        </View>
      </View>
    );
  };

  const renderClientName = () => (
    <>
      {currentUser.role === ROLES.TRAINER && (
        <View style={styles.clientNameContainer}>
          <Text style={styles.clientName}>Client&apos;s Name</Text>
          <TextInput
            style={styles.clientNameInput}
            placeholder="Client Name"
            placeholderTextColor={colors.fillDarkGrey}
            value={bwpCalculator?.data?.clientName}
            onChangeText={(newName) => {
              const filteredValue = removeAllSpecialCharacters(newName);
              updateForm1('clientName', filteredValue);
            }}
            numberOfLines={1}
            editable={
              calculatorContext?.type === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT
              || false
            }
          />
        </View>
      )}
    </>
  );

  const renderWeight = () => (
    <View style={styles.weightContainer}>
      <View style={styles.inputContainerView}>
        <DropDownPicker
          useNativeAndroidPickerStyle
          data={UNIT_OPTIONS}
          label="Units:"
          selected={
            UNIT_OPTIONS.find(
              (option) => option.label === bwpCalculator?.data?.units,
            ).id
          }
          onValueChange={(selectedUnit) => {
            if (bwpCalculator?.prevUnits !== selectedUnit.label) {
              dispatch(changeUnits(selectedUnit.label));
              dispatch(syncUnits());
            }
          }}
          labelStyle={styles.pickerItem}
          containerStyle={styles.picker}
        />
      </View>
      <View style={styles.inputContainerView}>
        {renderTextInput(
          'Weight',
          bwpCalculator?.data?.units === UnitType.METRIC ? 'kg' : 'lb',
          getTextInputProps(
            getAllowedMaxInputLength(4, bwpCalculator?.data?.weight),
          ),
          null,
          true,
        )}
      </View>
    </View>
  );

  const renderHeight = () => (
    <View style={styles.marginTop}>
      <Text style={styles.clientName}>Height</Text>
      {bwpCalculator?.data?.units === UnitType.US ? (
        <View style={styles.heightView}>
          <View style={styles.inputContainerView}>
            {renderTextInput('height_ft', 'ft', getTextInputProps(1), {
              marginLeft: 0,
            })}
          </View>

          <View style={styles.inputContainerView}>
            {renderTextInput(
              'height_in',
              'in',
              getTextInputProps(
                getAllowedMaxInputLength(3, bwpCalculator?.data?.height_in),
              ),
              null,
              true,
            )}
          </View>
        </View>
      ) : (
        <View style={styles.heightView}>
          {renderTextInput(
            'height_cm',
            'cm',
            getTextInputProps(
              getAllowedMaxInputLength(3, bwpCalculator?.data?.height_cm),
            ),
            { marginLeft: 0, width: '100%' },
            true,
          )}
        </View>
      )}
    </View>
  );

  const renderGenderAndAge = () => (
    <View style={[styles.genderAgeView, styles.marginTop]}>
      <View style={styles.inputContainerView}>
        <DropDownPicker
          useNativeAndroidPickerStyle
          data={GENDER_OPTIONS}
          label="Assigned Sex"
          selected={getSelectedGender()}
          onValueChange={(selectedGender) => {
            updateForm1('sex', selectedGender.label);
          }}
          labelStyle={styles.pickerItem}
          containerStyle={styles.picker}
          numberOfLines={1}
        />
      </View>
      <View style={styles.inputContainerView}>
        {renderTextInput('Age', 'yr', getTextInputProps(2))}
      </View>
    </View>
  );

  const renderPageContent = () => (
    <KeyboardAwareScrollView
      style={styles.pageContent}
      showsVerticalScrollIndicator={false}
    >
      {renderPageHeader()}
      <View style={styles.pageView}>
        {renderClientName()}
        {renderWeight()}
        {renderHeight()}
        {renderGenderAndAge()}
      </View>
    </KeyboardAwareScrollView>
  );

  const renderPageFooter = () => (
    <BWPNavigationFooter
      currentStep={1}
      onPressNext={onPressNext}
      onPressStart={onPressNext}
      isTrainer={currentUser.role === ROLES.TRAINER}
    />
  );

  return (
    <View style={styles.container}>
      {renderProgressBar()}
      {renderPageContent()}
      {renderPageFooter()}
    </View>
  );
};

const styles = StyleSheet.create({
  headerLeft: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    marginLeft: 10,
    width: 100,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
    alignSelf: 'center',
  },
  headerRight: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    marginRight: 10,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  barStyle: {
    borderRadius: 0,
  },
  progressBackground: {
    borderRadius: 0,
    width: '100%',
    borderWidth: 0,
    backgroundColor: colors.nasmBlue,
    height: 12,
  },
  pageHeaderDescription: {
    color: colors.white,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
    marginTop: 20,
  },
  pageContent: {
    height: '60%',
  },
  pageView: {
    paddingHorizontal: curvedScale(20),
  },
  clientNameContainer: {
    marginVertical: curvedScale(20),
  },
  clientName: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
    marginTop: curvedScale(10),
  },
  clientNameInput: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
    marginTop: curvedScale(10),
    paddingBottom: curvedScale(7),
    borderBottomWidth: 1,
    borderColor: colors.actionSheetDivider,
  },
  picker: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    height: curvedScale(40),
    marginVertical: 0,
  },
  pickerItem: {
    color: colors.black,
    fontSize: 14,
    fontFamily: 'Avenir-Light',
    fontWeight: '500',
    paddingHorizontal: curvedScale(8),
    textAlign: 'left',
  },
  weightContainer: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginTop: curvedScale(20),
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    padding: curvedScale(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(40),
    marginTop: useVerticalFlex ? 10 : 0,
  },
  customInputContainer: {
    marginLeft: useVerticalFlex ? 0 : curvedScale(10),
    marginTop: useVerticalFlex ? curvedScale(10) : 0,
  },
  inputLabel: {
    width: '40%',
  },
  input: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
  },
  customTextInput: {
    width: '90%',
    textAlign: 'right',
    paddingVertical: 0,
  },
  heightView: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    flex: 1,
    alignItems: 'center',
    marginTop: curvedScale(10),
  },
  genderAgeView: {
    flexDirection: useVerticalFlex ? 'column' : 'row',
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    marginTop: curvedScale(10),
  },
  inputView: {
    flexDirection: 'row',
    width: '60%',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  marginTop: {
    marginTop: curvedScale(20),
  },
  inputContainerView: {
    width: useVerticalFlex ? '100%' : '50%',
  },
});

export default BMR;
