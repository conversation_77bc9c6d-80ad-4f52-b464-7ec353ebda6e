import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Text,
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import PropTypes from 'prop-types';
import { clearFormData, updateFormData } from '../../../../actions';
import { ActivityLevel, UnitType } from '../../../../types/BWPTypes';
import {
  calculate_BMR,
  format_thousands,
} from '../../../../util/CalculatorUtils';
import ColoredHeadingBox from './ColoredHeadingBox';
import BWPNavigationFooter from './BWPNavigationFooter';
import BWPConstants from './BWPConstants';
import ProgressBar from '../../../../components/ProgressBar';
import { curvedScale } from '../../../../util/responsive';
import { colors } from '../../../../styles';

const stateSelector = (state) => state;
const options = [
  {
    label: 'Sedentary (little to no exercise)',
    value: ActivityLevel.SEDENTARY,
  },
  {
    label: 'Lightly Active (light exercise 1-3 days/week)',
    value: ActivityLevel.LIGHTLY_ACTIVE,
  },
  {
    label: 'Moderately Active (moderate exercise 3-5 days/week)',
    value: ActivityLevel.MODERATELY_ACTIVE,
  },
  {
    label: 'Very Active (hard exercise 6-7 days/week)',
    value: ActivityLevel.VERY_ACTIVE,
  },
  {
    label: 'Extremely Active (hard exercise & work in a physical job)',
    value: ActivityLevel.EXTREMELY_ACTIVE,
  },
];

const TDEE = ({ navigation }) => {
  const { bwpCalculator } = useSelector(stateSelector);
  const dispatch = useDispatch();
  useEffect(() => {
    navigation.setOptions({
      title: 'Body Weight Planner',
      headerTitleStyle: styles.headerTitle,
      headerRight,
      headerLeft,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const headerRight = () => <Text style={styles.headerRight}>2/5</Text>;

  const headerLeft = () => (
    <TouchableOpacity
      activeOpacity={0.6}
      onPress={() => {
        dispatch(clearFormData());
        navigation.pop(2);
      }}
    >
      <Text style={styles.headerLeft}>Cancel</Text>
    </TouchableOpacity>
  );

  const setActivityLevel = (value) => {
    const activityLevel = bwpCalculator?.data?.allActivityLevels?.filter(
      (level) => level.coefficient === value,
    );
    dispatch(
      updateFormData({
        currentActivityLevel: value,
        currentActivityLevelId: activityLevel?.[0]?.id,
      }),
    );
  };

  const renderOptions = () => (
    <View style={styles.optionsView}>
      <RadioForm animation>
        {options.map((obj, i) => (
          <View key={i} style={styles.optionRow}>
            <RadioButton key={i}>
              <RadioButtonInput
                obj={obj}
                index={i}
                isSelected={
                  bwpCalculator?.data?.currentActivityLevel === obj.value
                }
                borderWidth={1}
                onPress={() => {
                  setActivityLevel(obj.value);
                }}
                buttonInnerColor={colors.azure}
                buttonOuterColor={colors.black}
                buttonSize={10}
                buttonOuterSize={10 * 2}
              />
              <RadioButtonLabel
                obj={obj}
                onPress={() => {
                  setActivityLevel(obj.value);
                }}
                index={i}
                labelStyle={styles.optionlabel}
                labelWrapStyle={{ flex: 1 }}
              />
            </RadioButton>
          </View>
        ))}
      </RadioForm>
    </View>
  );

  const renderTopView = () => {
    const adjustRate = true;
    const fieldsState = {
      sex: bwpCalculator?.data?.sex,
      weight: bwpCalculator?.data?.weight,
      height:
        bwpCalculator?.data?.units === UnitType.US
          ? Number(bwpCalculator?.data?.height_ft) * 12
            + Number(bwpCalculator?.data?.height_in)
          : Number(bwpCalculator?.data?.height_cm),
      age: bwpCalculator?.data?.age,
      currentActivityLevel: bwpCalculator?.data?.currentActivityLevel,
    };

    const bmr = calculate_BMR(fieldsState, adjustRate);
    return (
      <ColoredHeadingBox
        backgroundColor={colors.darkGreen}
        title="Total Daily Energy Expenditure"
        subtitle="(TDEE)"
      >
        <View style={styles.headerBottomView}>
          <View style={styles.amountView}>
            <Text style={styles.amount}>
              {format_thousands(bmr, false)}
              {' '}
              <Text style={styles.unit}>kcal/day</Text>
            </Text>
          </View>
          <View style={styles.methodView}>
            <Text style={styles.method}>
              Based on the Mifflin St. Jeor method
            </Text>
          </View>
        </View>
      </ColoredHeadingBox>
    );
  };

  const renderPageFooter = () => (
    <BWPNavigationFooter
      currentStep={2}
      onPressPrevious={() => navigation.goBack()}
      onPressNext={() => navigation.navigate(BWPConstants.GOAL)}
    />
  );

  return (
    <View style={styles.container}>
      <ProgressBar
        progress={40}
        barStyle={styles.barStyle}
        progressColor={colors.medYellow}
        style={styles.progressBackground}
      />
      <ScrollView bounces={false} showsVerticalScrollIndicator={false}>
        {renderTopView()}
        <View style={styles.questionView}>
          <Text style={styles.question}>
            How would
            {' '}
            <Text style={styles.username}>
              {bwpCalculator?.data?.clientName}
            </Text>
            {' '}
            describe their current activity level?
          </Text>
          {renderOptions()}
        </View>
      </ScrollView>
      {renderPageFooter()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  headerBottomView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: curvedScale(15),
  },
  progressBackground: {
    borderRadius: 0,
    width: '100%',
    borderWidth: 0,
    backgroundColor: colors.nasmBlue,
    height: 12,
  },
  barStyle: {
    borderRadius: 0,
  },
  amount: {
    color: colors.white,
    fontSize: curvedScale(24),
    fontFamily: 'Avenir-Heavy',
  },
  unit: {
    color: colors.white,
    fontSize: curvedScale(18),
  },
  method: {
    color: colors.white,
    opacity: 0.6,
    fontSize: curvedScale(12),
  },
  methodView: {
    width: '40%',
    alignItems: 'flex-end',
  },
  amountView: {
    width: '60%',
    alignItems: 'flex-start',
  },
  questionView: {
    padding: curvedScale(20),
  },
  question: {
    color: colors.black,
    fontSize: curvedScale(16),
  },
  username: {
    fontFamily: 'Avenir-Heavy',
  },
  optionsView: {
    marginTop: curvedScale(20),
  },
  optionlabel: {
    fontSize: curvedScale(12),
    color: colors.black,
    marginLeft: curvedScale(10),
  },
  optionRow: {
    paddingVertical: curvedScale(15),
    borderBottomWidth: 1,
    borderColor: colors.bordergrey,
  },
  headerRight: {
    color: colors.white,
    fontSize: 14,
    marginRight: curvedScale(10),
    fontFamily: 'Avenir-Medium',
  },
  headerLeft: {
    color: colors.white,
    fontSize: 14,
    marginLeft: curvedScale(10),
    fontFamily: 'Avenir-Medium',
    width: curvedScale(100),
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
    alignSelf: 'center',
  },
});

TDEE.propTypes = {
  navigation: PropTypes.object.isRequired,
};

export default TDEE;
