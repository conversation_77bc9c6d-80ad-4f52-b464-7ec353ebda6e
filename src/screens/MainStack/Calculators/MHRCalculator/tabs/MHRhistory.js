import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  View,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  StyleSheet,
  Alert,
  Image,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import moment from 'moment';
import PropTypes from 'prop-types';
import MHRConstants from '../MHRConstants';
import ScaledText from '../../../../../components/ScaledText';
import nasm from '../../../../../dataManager/apiConfig';
import { colors } from '../../../../../styles';
import { curvedScale, scaleHeight } from '../../../../../util/responsive';
import { MHR_TRAINING_ZONES } from '../../../../../types/MHRTypes';

const imgMhrResult = require('../../../../../assets/mhr.png');
const mhrArrow = require('../../../../../resources/rightArrow.png');

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

const defaultProps = {};

const initialState = {
  isLoading: false,
  refreshing: true,
  page: 1,
  hasNextPage: false,
  mhrHistoryList: [],
};

const stateSelector = (state) => state;

const MHRhistory = ({ navigation }) => {
  const { selectedClient } = useSelector(stateSelector);
  const [state, setState] = useState(initialState);

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener(
      'focus',
      refreshGraphAndList,
    );
    return unsubscribeFocus;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const refreshGraphAndList = () => {
    setState({
      ...state,
      refreshing: true,
    });
    getMHRHistory(1);
  };

  const getMHRHistory = async (page) => {
    const { mhrHistoryList } = state;
    let currentPage = state.page;
    if (page) {
      currentPage = page;
    }
    const userId = selectedClient?.id;
    try {
      const response = await nasm.api.getMHRHistory(userId, currentPage, 20);
      setState({
        ...state,
        mhrHistoryList:
          currentPage === 1 ? response : [...mhrHistoryList, ...response],
        hasNextPage: response && response.length > 0,
        page: currentPage + 1,
        isLoading: false,
        refreshing: false,
      });
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
      setState({
        ...state,
        isLoading: false,
        refreshing: false,
      });
    }
  };

  const onScroll = () => {
    if (state.hasNextPage && !state.isLoading) {
      setState({
        ...state,
        isLoading: true,
      });
      getMHRHistory();
    }
  };

  const navigateToResults = (item) => {
    navigation.navigate({
      name: MHRConstants.MHRResults,
      params: {
        mhrData: {
          selectedZone: MHR_TRAINING_ZONES.find(
            (zone) => zone.intensity_level === item.desired_intensity_zone,
          ),
          age: item.age,
          HRmax: item.bpm,
        },
        title: moment(item.created_at).format('M/D/YYYY'),
        comingFrom: MHRConstants.MHRHistory,
      },
      merge: true,
    });
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity
      key={item.id}
      style={styles.assessmentCell}
      onPress={() => navigateToResults(item)}
    >
      <View style={styles.rowContainer}>
        <ScaledText style={styles.date}>
          {moment(item.created_at).format('M/D/YYYY')}
        </ScaledText>
      </View>
      <View style={styles.rowContainer}>
        <View style={styles.centerStyle}>
          <Image source={imgMhrResult} style={styles.mhrIconStyle} />
        </View>
        <ScaledText>
          <ScaledText style={styles.MHRValue}>
            {`${Number(item.bpm).toFixed(1)}`}
          </ScaledText>
          <ScaledText style={styles.MHRtext}> bpm</ScaledText>
        </ScaledText>
        <View style={[styles.centerStyle, { marginLeft: curvedScale(1) }]}>
          <Image source={mhrArrow} style={styles.mhrArrowStyle} />
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderLoader = () => (
    <View style={styles.loaderView}>
      <ActivityIndicator size="large" animating={state.isLoading} />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={state.mhrHistoryList}
        keyExtractor={(item) => item.date}
        renderItem={renderItem}
        onEndReached={onScroll}
        onEndReachedThreshold={0.5}
        refreshControl={(
          <RefreshControl
            refreshing={state.refreshing}
            onRefresh={refreshGraphAndList}
          />
        )}
        ListFooterComponent={renderLoader}
        ListEmptyComponent={() => (
          <View style={styles.loaderView}>
            {!state.isLoading && !state.refreshing ? (
              <View style={styles.emptyDataView}>
                <ScaledText style={styles.emptyDataMessage}>
                  No history to show
                </ScaledText>
              </View>
            ) : null}
          </View>
        )}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyDataView: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: curvedScale(20),
  },
  emptyDataMessage: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: DeviceInfo.isTablet() ? curvedScale(10) : curvedScale(14),
  },
  loaderView: {
    marginVertical: curvedScale(10),
  },
  assessmentCell: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.silver51,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(57),
  },
  date: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(17),
    color: colors.black,
    paddingLeft: scaleHeight(2),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  centerStyle: {
    alignSelf: 'center',
  },
  mhrIconStyle: {
    width: curvedScale(17),
    height: curvedScale(14),
    marginRight: curvedScale(5),
  },
  mhrArrowStyle: {
    width: curvedScale(23),
    height: curvedScale(23),
    marginRight: curvedScale(8),
  },
  MHRValue: {
    color: colors.black,
    fontSize: curvedScale(16),
    fontWeight: 'bold',
    fontFamily: 'Avenir-Roman',
  },
  MHRtext: {
    color: colors.black,
    fontSize: curvedScale(13),
    fontFamily: 'Avenir-Medium',
    alignSelf: 'center',
    justifyContent: 'center',
  },
});

MHRhistory.propTypes = propTypes;
MHRhistory.defaultProps = defaultProps;

export default MHRhistory;
