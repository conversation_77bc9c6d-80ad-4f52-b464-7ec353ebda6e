import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  Alert,
  View,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import PropTypes from 'prop-types';
import nasm from '../../../../../dataManager/apiConfig';
import FloatingButton from '../../../../../components/FloatingButton';
import MHRResults from '../MHRResults';
import MHRFTU from '../MHRFTU';
import MHRConstants from '../MHRConstants';
import { MHR_TRAINING_ZONES } from '../../../../../types/MHRTypes';
import { colors } from '../../../../../styles';

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    pop: PropTypes.func,
    addListener: PropTypes.func,
    removeListener: PropTypes.func,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

const defaultProps = {};

const stateSelector = (state) => state;

const MHRCurrent = ({ navigation }) => {
  const { selectedClient } = useSelector(stateSelector);
  const [loading, setLoading] = useState(true);
  const [mhrData, setMhrData] = useState(null);

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', getCurrentMHR);
    return unsubscribeFocus;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  const getCurrentMHR = async () => {
    try {
      setLoading(true);
      const userId = selectedClient?.id;
      const response = await nasm.api.getCurrentMHR(userId);
      if (response && response.bpm) {
        setMhrData({
          selectedZone: MHR_TRAINING_ZONES.find(
            (zone) => zone.intensity_level === response.desired_intensity_zone,
          ),
          age: response.age,
          HRmax: response.bpm,
        });
        setLoading(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  const onPressAdd = () => {
    navigation.navigate({
      name: MHRConstants.MHRCalculator,
      merge: true,
    });
  };

  const renderLoader = () => (
    <View style={styles.loadingView}>
      <ActivityIndicator animating size="large" />
    </View>
  );

  if (loading) {
    return renderLoader();
  }

  return (
    <SafeAreaView style={styles.container}>
      {mhrData ? (
        <MHRResults mhrData={mhrData} navigation={navigation} />
      ) : (
        <MHRFTU navigation={navigation} />
      )}
      <FloatingButton onPress={onPressAdd} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

MHRCurrent.propTypes = propTypes;
MHRCurrent.defaultProps = defaultProps;

export default MHRCurrent;
