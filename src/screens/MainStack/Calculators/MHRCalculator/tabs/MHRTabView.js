import React, { Component } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { Dimensions } from 'react-native';
import PropTypes from 'prop-types';
import MHRCurrent from './MHRCurrent';
import MHRhistory from './MHRhistory';
import HeaderLeftButton from '../../../../../components/HeaderLeftButton';
import { colors, materialTabBarOptions } from '../../../../../styles';

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
    setParams: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

class MHRTabView extends Component {
  componentDidMount() {
    this.props.navigation.setOptions({
      headerTitle: 'MHR Calculator',
      headerLeft: this.renderHeaderLeft,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  render() {
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
        style={materialTabBarOptions.tabBarOptions.tabBarStyle}
      >
        <TabNav.Screen name="Current" component={MHRCurrent} />
        <TabNav.Screen name="History" component={MHRhistory} />
      </TabNav.Navigator>
    );
  }
}

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

MHRTabView.propTypes = propTypes;
MHRTabView.defaultProps = defaultProps;

export default MHRTabView;
