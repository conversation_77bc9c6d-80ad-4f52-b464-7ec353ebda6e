import { useContext } from 'react';
import {
  MHRDispatchContext,
  MHRStateContext,
} from '../../../../reducers/MHRContext';

const useMHRStateContext = () => {
  const state = useContext(MHRStateContext);
  if (state === undefined) {
    throw new Error('must be used within a Provider Component');
  }

  return state;
};

const useMHRDispatchContext = () => {
  const dispatch = useContext(MHRDispatchContext);
  if (dispatch === undefined) {
    throw new Error('must be used within a Provider Component');
  }

  return dispatch;
};

export { useMHRStateContext, useMHRDispatchContext };
