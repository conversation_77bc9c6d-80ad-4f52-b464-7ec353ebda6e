import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
  Platform,
  UIManager,
  StatusBar,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  LayoutAnimation,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';

const closeImg = require('../../../../assets/closeCircle.png');

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

const MHRLearnMore = ({ navigation }) => {
  useEffect(() => {
    StatusBar.setBarStyle('dark-content');
    setTimeout(() => {
      setCustomNavigationHeader();
    }, 300);
    return () => {
      StatusBar.setBarStyle('light-content');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setAnimation = () => {
    if (Platform.OS === 'android') {
      if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
      }
    }
    LayoutAnimation.configureNext({
      duration: 500,
      create: { type: 'linear', property: 'opacity' },
      update: { type: 'spring', springDamping: 0.5 },
    });
  };

  const setCustomNavigationHeader = () => {
    setAnimation();
    navigation.setOptions({
      headerStyle: { backgroundColor: colors.white, height: 100 },
      headerLeft: renderCloseImg,
    });
  };

  const goBack = () => {
    navigation.goBack();
  };

  const renderCloseImg = () => (
    <TouchableOpacity
      activeOpacity={0.6}
      hitSlop={{
        top: 20,
        bottom: 20,
        left: 20,
        right: 20,
      }}
      onPress={goBack}
      style={styles.closeImgView}
    >
      <Image source={closeImg} />
    </TouchableOpacity>
  );

  const renderPageContent = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <Text style={styles.title('Avenir-Heavy', 'bold')}>
        Maximal Heart Rate and Training Zones Calculator
        {' '}
        <Text style={styles.title()}>(MHR)</Text>
      </Text>
      <View style={styles.descriptionView}>
        <Text style={styles.descriptionHeader}>
          What is MHR?
          {'\n'}
        </Text>
        <Text style={styles.description}>
          Calculating HRmax is another method for establishing training
          intensity during cardiorespiratory exercise. Although measuring a
          client&apos;s actual maximal heart rate is effective, it is also
          impractical for fitness professionals because it requires testing
          clients at maximal capacity. Subsequently, many mathematical formulas
          that estimate HRmax have been developed. Once Hrmax is calculated,
          fitness professionals can have clients exercise at a certain
          percentage of their HRmax.
          {'\n'}
        </Text>
        <Text style={styles.descriptionHeader}>MHR Formula</Text>
        <Text style={styles.description}>
          {' '}
          {'\n'}
          Arguably the most commonly used formula for estimating HRmax is 220 -
          age. However, this formula was never intended to be used as an
          instrument for designing cardiorespiratory fitness programs because
          maximal heart rate varies significantly among individuals of the same
          age. Dr. William Haskell (one of the developers of the aforementioned
          formula) has been quoted as saying, “The formula was never supposed to
          be an absolute guide to rule people&apos;s training” (Kolata, 2001).
          For this reason, more appropriate regression formulas have been
          developed, such as the Tanaka formula, where HRmax is determined using
          the following formula: 208 - (0.7 x age) (Tanaka et al., 2001). It has
          been shown that this formula is more accurate than Haskell&apos;s 220
          - age formula for estimating an individual&apos;s HRmax (Roy &
          McCrory, 2015).
          {'\n'}
          {'\n'}
          Keep in mind, fitness professionals should never use this, or any
          other mathematical formula, as an absolute. A person&apos;s heart rate
          response to exercise is dependent on many additional factors,
          including genetics, medications, and stimulant use such as caffeine.
          However, the Tanaka formula is very simple to use and can be easily
          implemented as a general starting point for measuring
          cardiorespiratory training intensity.
          {'\n'}
          Consider the following example of a 40-year-old client who is tasked
          at exercising at 65% of her HRmax. The formula would be solved as
          follows:
          {'\n'}
          {'\n'}
          {`Tanaka formula: 208 - (0.7 x age) = HRmax
                208 - (0.7 x 40) = HRmax
                     208 - (28) = HRmax
                          180 = HRmax
180 beats per minute (bpm) is the client's estimated HRmax:

180 x 65% = 117
Thus, 117 bpm is the client's target heart rate.`}
        </Text>
      </View>
    </ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>{renderPageContent()}</SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  closeImgView: {
    marginHorizontal: curvedScale(20),
    width: 30,
    height: 30,
  },
  content: {
    marginTop: curvedScale(10),
    marginHorizontal: curvedScale(20),
  },
  title: (fontFamily = 'Avenir-Medium', fontWeight = '400') => ({
    color: colors.black,
    fontFamily,
    fontSize: curvedScale(23),
    fontWeight,
    lineHeight: 33,
    textAlign: 'left',
  }),
  descriptionView: {
    marginTop: curvedScale(20),
  },
  description: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
    lineHeight: 24,
    textAlign: 'left',
  },
  descriptionHeader: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    fontSize: curvedScale(17),
    lineHeight: 24,
    textAlign: 'left',
  },
});

MHRLearnMore.propTypes = propTypes;
MHRLearnMore.defaultProps = defaultProps;

export default MHRLearnMore;
