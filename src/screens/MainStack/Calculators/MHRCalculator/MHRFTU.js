import React, { Component } from 'react';
import {
  Text, Image, View, StyleSheet, SafeAreaView,
} from 'react-native';
import PropTypes from 'prop-types';
import { HeaderLeftButton } from '../../../../components';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';

const imgMHRFTUE = require('../../../../assets/mhr-ftu.png');

const propTypes = {
  navigation: PropTypes.object.isRequired,
};

const defaultProps = {};

class MHRFTU extends Component {
  componentDidMount() {
    this.props.navigation.setOptions({
      headerLeft: this.renderHeaderLeft,
      headerTitle: 'MHR Calculator',
      headerTitleStyle: styles.headerTitle,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  renderEmptyState = () => (
    <View style={styles.emptyStateContainer}>
      <View style={styles.emptyIconStyle}>
        <Image source={imgMHRFTUE} />
      </View>
      <Text style={styles.emptyStateHeaderText}>
        {"Create your client's first\nMHR calculation"}
      </Text>
      <Text style={styles.emptyStateBodyText}>
        {
          'The Maximal Heart Rate calculator estimates the\nintensity of your training.'
        }
      </Text>
    </View>
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        {this.renderEmptyState()}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
  headerButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyIconStyle: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateHeaderText: {
    color: colors.black,
    fontFamily: 'Avenir-Black',
    fontWeight: 'bold',
    fontSize: curvedScale(22),
    lineHeight: 30,
    textAlign: 'center',
    marginTop: curvedScale(20),
  },
  emptyStateBodyText: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    textAlign: 'center',
    marginTop: curvedScale(10),
  },
});

MHRFTU.propTypes = propTypes;
MHRFTU.defaultProps = defaultProps;

export default MHRFTU;
