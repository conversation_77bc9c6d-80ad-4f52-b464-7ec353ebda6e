import React, { useEffect } from 'react';
import {
  View,
  Text,
  Alert,
  TextInput,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Button from '../../../../components/Button';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import MHRConstants from './MHRConstants';
import { colors } from '../../../../styles';
import { curvedScale, scaleWidth } from '../../../../util/responsive';
import { removeAllSpecialCharacters } from '../../../../util/validate';
import {
  MHR_ACTION_TYPES,
  MHR_TRAINING_ZONES,
} from '../../../../types/MHRTypes';
import { isValidData } from './MHRCalculations';
import { MHRContextProvider } from '../../../../reducers/MHRContext';
import { useMHRDispatchContext, useMHRStateContext } from './useMHRContext';
import { onPressExternalLink } from '../../../../util/utils';

const propTypes = {
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
    navigate: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
};

const defaultProps = {};

const MHRCalculator = ({ navigation }) => {
  const dispatch = useMHRDispatchContext();
  const state = useMHRStateContext();

  const onEditValue = (key, value) => {
    dispatch({ type: MHR_ACTION_TYPES.EDIT, payload: { [key]: value } });
  };

  const onCalculate = () => {
    dispatch({ type: MHR_ACTION_TYPES.CALCULATE });
  };

  const onReset = () => {
    dispatch({ type: MHR_ACTION_TYPES.RESET });
    navigation.goBack();
  };

  useEffect(() => {
    navigation.setOptions({
      headerLeft: renderHeaderLeft,
      headerTitle: 'MHR Calculator',
      headerTitleStyle: styles.headerTitle,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const { age, selectedZone, HRmax } = state;
    if (HRmax) {
      navigate(MHRConstants.MHRResults, {
        mhrData: {
          age,
          selectedZone,
          HRmax,
        },
        onReset,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.HRmax]);

  const renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={onReset}
      title="Cancel"
      titleStyle={styles.headerButtonText}
    />
  );

  const navigate = (name, params) => {
    navigation.navigate({ name, params, merge: true });
  };

  const validateInputs = () => {
    try {
      if (isValidData(state.age, state.selectedZone?.id)) {
        onCalculate();
      }
    } catch (error) {
      Alert.alert('Error', error?.message || error);
    }
  };

  const stripNonNumericChars = (input) => input.replace(/[^0-9]/g, '');

  const renderPageHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitleBold}>
        Maximal Heart Rate and Training Zones Calculator
        <Text style={styles.headerTitleRegular}> (MHR)</Text>
      </Text>
      <Text style={styles.headerDescription}>
        Calculating HRmax is another method for establishing training intensity
        during cardiorespiratory exercise.
      </Text>
      {renderLearnMoreButton()}
    </View>
  );

  const renderLearnMoreButton = () => (
    <TouchableOpacity
      style={styles.learnMoreBtn}
      onPress={() => navigate(MHRConstants.MHRLearnMore)}
    >
      <Text style={styles.learnMoreBtnText}>Learn More</Text>
    </TouchableOpacity>
  );

  const renderAge = (textInputProps) => {
    const fieldValue = state.age;
    return (
      <View style={[styles.inputContainer, styles.customInputContainer]}>
        <View style={styles.inputLabel}>
          <Text style={styles.input}>Age: </Text>
        </View>
        <View style={styles.inputView}>
          <TextInput
            style={[styles.inputField, styles.customTextInput]}
            placeholder="0"
            placeholderTextColor={colors.fillDarkGrey}
            value={`${fieldValue}`}
            onChangeText={(newValue) => {
              const filteredValue = removeAllSpecialCharacters(newValue);
              onEditValue('age', stripNonNumericChars(filteredValue));
            }}
            keyboardType="numeric"
            returnKeyType="done"
            contextMenuhidden
            {...textInputProps}
          />
        </View>
      </View>
    );
  };

  const renderIntensities = () => (
    <View style={styles.intensitiesContainer}>
      <Text style={styles.textDesiredIntensity}>
        What is your desired intensity?
      </Text>
      {MHR_TRAINING_ZONES.map((zone) => (
        <View style={styles.zoneContainer}>
          <TouchableOpacity
            onPress={() => {
              onEditValue('selectedZone', zone);
            }}
            activeOpacity={0.6}
            style={styles.horizontalView}
          >
            <View>
              <View style={styles.radioButton}>
                {zone.id === state.selectedZone?.id ? (
                  <View style={styles.radioButtonSelected} />
                ) : null}
              </View>
            </View>
            <View style={styles.zoneInfoContainer}>
              <Text style={styles.zoneTitle}>
                {`${zone.title_long} (${zone.intensity_range_start}-${zone.intensity_range_end}%)`}
              </Text>
              <View style={styles.zoneDescriptionContainer}>
                {zone.description.map((descText) => (
                  <View style={styles.horizontalView}>
                    <Text style={styles.zoneDescription}>{'• '}</Text>
                    <Text style={styles.zoneDescription}>{`${descText}`}</Text>
                  </View>
                ))}
              </View>
            </View>
          </TouchableOpacity>
          <View style={styles.zonesSeparator} />
        </View>
      ))}
    </View>
  );

  const renderCalculateButton = () => (
    <View style={styles.calculateBtnView}>
      <Button
        textStyles={styles.calculateBtnText}
        title="Calculate"
        buttonStyle={[
          styles.createAccountButton,
          { borderRadius: scaleWidth(7.2) },
        ]}
        onPress={validateInputs}
      />
    </View>
  );

  const getTextInputProps = (maxLength, customStyle) => {
    const props = {
      maxLength,
    };
    if (customStyle) {
      props.style = customStyle;
    }
    return props;
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {renderPageHeader()}
        <View style={styles.pageView}>
          {renderAge(getTextInputProps(2))}
          {renderIntensities()}
        </View>
      </KeyboardAwareScrollView>
      {renderCalculateButton()}
      <View style={styles.bottomLinkContainer}>
        <TouchableOpacity
          onPress={() => onPressExternalLink(
            'https://www.cdc.gov/physicalactivity/basics/measuring/heartrate.htm',
          )}
        >
          <Text style={styles.bottomLink}>
            Based on the CDC Target and Maximum Heart Rate Article
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
  headerButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  scrollView: {
    height: '90%',
  },
  headerContainer: {
    paddingHorizontal: curvedScale(20),
    paddingTop: curvedScale(40),
    paddingBottom: curvedScale(20),
    backgroundColor: colors.offWhite,
  },
  headerTitleBold: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    fontWeight: '700',
  },
  headerTitleRegular: {
    color: colors.black,
    fontFamily: 'Avenir',
    fontSize: curvedScale(22),
    fontWeight: '400',
  },
  headerDescription: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(17),
    marginTop: curvedScale(20),
  },
  learnMoreBtn: {
    width: '40%',
    alignItems: 'center',
    borderRadius: 18.5,
    borderWidth: 1,
    borderColor: colors.disclaimerGrey,
    paddingHorizontal: curvedScale(16),
    paddingVertical: curvedScale(8),
    marginTop: curvedScale(20),
  },
  learnMoreBtnText: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(12),
    fontWeight: '500',
  },
  pageView: {
    padding: curvedScale(20),
    backgroundColor: colors.white,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: colors.actionSheetDivider,
    padding: curvedScale(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: curvedScale(40),
    marginTop: curvedScale(10),
  },
  customInputContainer: {
    marginLeft: 0,
    marginTop: curvedScale(10),
  },
  inputLabel: {
    width: '40%',
  },
  input: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
    fontWeight: '500',
  },
  inputView: {
    flexDirection: 'row',
    width: '60%',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  inputField: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Roman',
    fontWeight: '500',
  },
  customTextInput: {
    color: colors.black,
    width: '90%',
    textAlign: 'right',
    paddingVertical: 0,
  },
  intensitiesContainer: {
    marginVertical: curvedScale(10),
  },
  textDesiredIntensity: {
    color: colors.black,
    fontSize: curvedScale(17),
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    marginVertical: curvedScale(20),
  },
  horizontalView: {
    flexDirection: 'row',
  },
  zoneContainer: {
    padding: curvedScale(15),
  },
  radioButton: {
    width: curvedScale(20),
    height: curvedScale(20),
    borderColor: colors.darkGrey,
    borderWidth: 1,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    width: curvedScale(10),
    height: curvedScale(10),
    backgroundColor: colors.azure,
    borderRadius: 5,
  },
  zoneInfoContainer: {
    marginLeft: curvedScale(10),
  },
  zoneTitle: {
    color: colors.black,
    fontSize: curvedScale(13),
    fontFamily: 'Avenir-Roman',
    fontWeight: '500',
  },
  zoneDescriptionContainer: {
    padding: curvedScale(5),
    marginTop: curvedScale(10),
  },
  zoneDescription: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Roman',
    lineHeight: 20,
  },
  zonesSeparator: {
    height: 1,
    width: '100%',
    backgroundColor: colors.actionSheetDivider,
    marginTop: curvedScale(5),
  },
  calculateBtnView: {
    margin: curvedScale(20),
    height: '10%',
  },
  calculateBtnText: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(17),
    fontWeight: '700',
  },
  bottomLinkContainer: {
    marginVertical: curvedScale(20),
  },
  bottomLink: {
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Medium',
    color: colors.fillDarkGrey,
    alignSelf: 'center',
    textDecorationLine: 'underline',
    lineHeight: 20,
  },
});

MHRCalculator.propTypes = propTypes;
MHRCalculator.defaultProps = defaultProps;

const MHRRoot = ({ navigation }) => (
  <MHRContextProvider>
    <MHRCalculator navigation={navigation} />
  </MHRContextProvider>
);

MHRRoot.propTypes = propTypes;

export default MHRRoot;
