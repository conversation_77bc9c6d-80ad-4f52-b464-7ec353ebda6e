import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  Text,
  Alert,
  Image,
  View,
  StyleSheet,
  SafeAreaView,
  BackHandler,
  ScrollView,
} from 'react-native';
import PropTypes from 'prop-types';
import HeaderLeftButton from '../../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../../components/HeaderRightButton';
import nasm from '../../../../dataManager/apiConfig';
import LoadingSpinner from '../../../../components/LoadingSpinner';
import { colors } from '../../../../styles';
import { curvedScale } from '../../../../util/responsive';
import { CALCULATOR_CONTEXTS } from '../../../../reducers/calculatorContextReducer';
import { MHR_TRAINING_ZONES } from '../../../../types/MHRTypes';
import MHRConstants from './MHRConstants';

const imgMhr = require('../../../../assets/mhr.png');

const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
    pop: PropTypes.func,
    popToTop: PropTypes.func,
    setOptions: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      mhrData: PropTypes.object,
      title: PropTypes.string,
      comingFrom: PropTypes.string,
      onReset: PropTypes.func,
    }),
  }).isRequired,
  mhrData: PropTypes.object,
};

const defaultProps = {
  mhrData: null,
};

const stateSelector = (state) => state;

const MHRResults = ({ navigation, route, mhrData }) => {
  const state = {
    mhrData: route?.params?.mhrData || mhrData,
  };
  const [isLoading, setLoading] = useState(false);
  const { calculatorContext, selectedClient } = useSelector(stateSelector);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
      headerTitle: route?.params?.title || 'MHR Calculator',
      headerTitleStyle: styles.headerTitle,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', handleBackPress);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const goBack = (fixedPopCount) => {
    let popCount = fixedPopCount || 1;
    if (
      !fixedPopCount
      && calculatorContext?.type === CALCULATOR_CONTEXTS.SELECTED_CLIENT
    ) {
      popCount = 2;
    }
    navigation.pop(popCount);
  };

  const handleBackPress = () => {
    goBack();
    return true;
  };

  const saveMHRResults = async () => {
    try {
      const userId = selectedClient?.id;
      setLoading(true);
      const jsonData = {
        desired_intensity_zone: state.mhrData?.selectedZone?.intensity_level,
        bpm: state.mhrData?.HRmax,
        age: state.mhrData?.age,
      };
      const response = await nasm.api.saveMHRResults(jsonData, userId);
      if (response !== null) {
        goBack(2);
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    } finally {
      setLoading(false);
    }
  };

  const renderHeaderLeft = () => (
    <HeaderLeftButton
      title="Cancel"
      onPress={() => {
        let popCount = 2;
        if (route?.params?.comingFrom === MHRConstants.MHRHistory) {
          popCount = 1;
        }
        goBack(popCount);
      }}
      titleStyle={styles.headerButtonsText}
    />
  );

  const renderHeaderRight = () => {
    if (route?.params?.comingFrom === MHRConstants.MHRHistory) {
      return null;
    }
    if (calculatorContext?.type === CALCULATOR_CONTEXTS.TRAINER_ACCOUNT) {
      return (
        <HeaderRightButton
          title="Reset"
          onPress={route?.params?.onReset}
          titleStyle={styles.headerButtonsText}
        />
      );
    }
    return (
      <HeaderRightButton
        title="Save"
        onPress={saveMHRResults}
        titleStyle={styles.headerButtonsText}
      />
    );
  };

  const renderTopHeader = () => (
    <View style={styles.topHeader}>
      <Text style={styles.headerResultsText(colors.black)}>
        MHR
        {' '}
        <Text style={styles.headerResultsText(colors.fillDarkGrey)}>
          Results
        </Text>
      </Text>
      <View style={styles.headerMHRViewStyle}>
        <Image source={imgMhr} style={styles.headerIconStyle} />
        <Text
          style={styles.headerBPMValueText(
            colors.black,
            curvedScale(18),
            'bold',
          )}
        >
          {Number(state.mhrData.HRmax).toFixed(1)}
          {' '}
          <Text
            style={styles.headerBPMValueText(colors.darkGrey, curvedScale(12))}
          >
            bpm
          </Text>
        </Text>
      </View>
    </View>
  );

  const renderZoneCardHeader = (zone) => {
    const isDesiredZone = zone.id === state.mhrData.selectedZone.id;
    return (
      <View style={styles.zoneCardHeaderContainer(isDesiredZone)}>
        {isDesiredZone ? (
          <Text style={styles.zoneDesiredIntensity}>Desired Intensity</Text>
        ) : null}
        <View style={[styles.zoneCardHeaderView, styles.horizontalView()]}>
          <Text style={styles.zoneTitleShort(isDesiredZone)}>
            {zone.title_short}
          </Text>
          <Text style={styles.zoneTitleRange(isDesiredZone)}>
            {`${zone.intensity_range_start}-${zone.intensity_range_end}%`}
          </Text>
        </View>
      </View>
    );
  };

  const renderZoneRPEView = (zone) => (
    <View style={[styles.zoneBpmInfoView, styles.horizontalView()]}>
      <Text style={styles.zoneBpmInfoLabel}>RPE</Text>
      <Text style={styles.zoneBpmInfoLabelValue}>{zone.RPE}</Text>
    </View>
  );

  const renderZoneBpmInfoView = (zone) => (
    <View style={[styles.zoneBpmInfoView, styles.horizontalView('flex-start')]}>
      <Text style={styles.zoneBpmInfoLabel}>Target Heart Rate (BPM)</Text>
      <View>
        <Text style={styles.zoneBpmInfoLabelValue}>
          {`${zone.intensity_range_start}% = ${
            ((state.mhrData.HRmax * zone.intensity_range_start) / 100).toFixed(2)
          }`}
        </Text>
        <Text style={styles.zoneBpmInfoLabelValue}>
          {`${zone.intensity_range_end}% = ${
            ((state.mhrData.HRmax * zone.intensity_range_end) / 100).toFixed(2)
          }`}
        </Text>
      </View>
    </View>
  );

  const renderZoneDescriptionContainer = (zone) => (
    <View style={styles.zoneDescriptionContainer}>
      <Text style={styles.zoneDescriptionTitle}>Description</Text>
      {zone.description.map((descText) => (
        <View style={styles.horizontalView('flex-start')}>
          <Text style={styles.zoneDescription}>{'• '}</Text>
          <Text style={styles.zoneDescription}>{`${descText}`}</Text>
        </View>
      ))}
    </View>
  );

  const renderTrainingZones = () => (
    <>
      {MHR_TRAINING_ZONES.map((zone) => (
        <View style={styles.zoneCardView}>
          {renderZoneCardHeader(zone)}
          <View>
            <View style={styles.zoneBpmInfoContainer}>
              {renderZoneRPEView(zone)}
              {renderZoneBpmInfoView(zone)}
            </View>
            <View style={styles.zoneInfoSeparator} />
            {renderZoneDescriptionContainer(zone)}
          </View>
        </View>
      ))}
    </>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderTopHeader()}
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.zonesCardContainer}>{renderTrainingZones()}</View>
      </ScrollView>
      <LoadingSpinner
        visible={isLoading}
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerButtonsText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.paleGray2,
    padding: curvedScale(20),
  },
  headerResultsText: (color) => ({
    color,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    fontWeight: '700',
    textAlign: 'left',
  }),
  headerMHRViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIconStyle: {
    width: curvedScale(25),
    height: curvedScale(25),
    marginRight: curvedScale(5),
  },
  headerBPMValueText: (color, fontSize, fontWeight) => ({
    color,
    fontSize,
    fontWeight,
    fontFamily: 'Avenir',
    textAlign: 'right',
    justifyContent: 'center',
  }),
  zonesCardContainer: {
    padding: curvedScale(20),
  },
  zoneCardView: {
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.disclaimerGrey,
    marginTop: curvedScale(20),
  },
  zoneCardHeaderContainer: (isDesiredZone) => ({
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: isDesiredZone ? colors.duskBlue : colors.disclaimerGrey,
    padding: curvedScale(20),
  }),
  zoneCardHeaderView: {
    justifyContent: 'space-between',
  },
  zoneDesiredIntensity: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontWeight: '500',
    fontSize: curvedScale(13),
  },
  zoneTitleShort: (isDesiredZone) => ({
    color: isDesiredZone ? colors.white : colors.black,
    fontFamily: 'Avenir-Heavy',
    fontWeight: '700',
    fontSize: curvedScale(17),
    lineHeight: 33,
  }),
  zoneTitleRange: (isDesiredZone) => ({
    color: isDesiredZone ? colors.white : colors.black,
    fontFamily: 'Avenir-Heavy',
    fontWeight: '700',
    fontSize: curvedScale(24),
  }),
  zoneBpmInfoContainer: {
    padding: curvedScale(20),
  },
  zoneBpmInfoView: {
    justifyContent: 'space-between',
  },
  zoneBpmInfoLabel: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontWeight: '700',
    fontSize: curvedScale(14),
    lineHeight: 33,
  },
  zoneBpmInfoLabelValue: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontWeight: '700',
    fontSize: curvedScale(17),
    lineHeight: 33,
    textAlign: 'right',
  },
  zoneInfoSeparator: {
    height: 1,
    width: '100%',
    backgroundColor: colors.disclaimerGrey,
  },
  zoneDescriptionContainer: {
    padding: curvedScale(20),
  },
  zoneDescriptionTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontWeight: '700',
    fontSize: curvedScale(14),
    lineHeight: 33,
  },
  zoneDescription: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
  },
  horizontalView: (alignItems = 'center') => ({
    flexDirection: 'row',
    alignItems,
  }),
});

MHRResults.propTypes = propTypes;
MHRResults.defaultProps = defaultProps;

export default MHRResults;
