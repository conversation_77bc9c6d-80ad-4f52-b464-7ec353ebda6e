import React, { Component } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';

// Components
import { View, Dimensions } from 'react-native';
import Milestones from './Milestones';
import Activity from './Activity';

// Styles
import { materialTabBarOptions } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

class GoalsTabView extends Component {
  static router = TabNav.router;

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Goals',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  componentDidMount() {
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
  );

  updateHeaderRight = (headerRight) => {
    this.props.navigation.setParams({
      renderHeaderRight: headerRight,
    });
  };

  updateHeaderLeft = (headerLeft) => {
    this.props.navigation.setParams({
      renderHeaderLeft: headerLeft,
    });
  };

  render() {
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            // Based of https://github.com/satya164/react-native-tab-view/issues/944#issuecomment-599224375
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
        style={materialTabBarOptions.tabBarOptions.tabBarStyle}
        initialRouteName={
          this.props.route?.params?.initialRouteName || 'Milestones'
        }
      >
        <TabNav.Screen
          name="Milestones"
          component={Milestones}
          initialParams={{ updateHeaderRight: this.updateHeaderRight }}
        />
        <TabNav.Screen
          name="Activity"
          component={Activity}
          initialParams={{
            updateHeaderRight: this.updateHeaderRight,
            updateHeaderLeft: this.updateHeaderLeft,
          }}
        />
      </TabNav.Navigator>
    );
  }
}
export default GoalsTabView;
