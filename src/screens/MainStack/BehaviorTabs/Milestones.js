import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Components
import {
  View,
  Text,
  Image,
  Alert,
  BackHandler,
  TouchableOpacity,
  LayoutAnimation,
  FlatList,
  TextInput,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import DraggableFlatList from 'react-native-draggable-flatlist';
import analytics from '@react-native-firebase/analytics';
import { FloatingButton, LoadingSpinner, Swipeable } from '../../../components';
import nasm from '../../../dataManager/apiConfig';
import { androidSafeLayoutAnimation } from '../../../constants';

// Analytics

// Styles
import { colors } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import { removeAllSpecialCharacters } from '../../../util/validate';

// Images
const sortImage = require('../../../resources/sortGray.png');
const completedImage = require('../../../resources/completed.png');
const incompleteImage = require('../../../resources/checkmarkIncomplete.png');

const propTypes = {
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

class Milestones extends Component {
  constructor(props) {
    super(props);
    this.state = {
      largestOrdinal: 0,
      incompleteGoals: [],
      completeGoals: [],
      loadingMessage: null,
      listKey: '0',
      sorting: false,
      swiping: false,
      adding: false,
      editing: false,
      newGoalText: '',
      milestonesBeingDeleted: [],
      canMarkComplete: true,
      scrolling: false,
      isDisplayDeleteAlert: false,
    };
  }

  componentDidMount() {
    this.getMilestones();
    this.props.navigation.setParams({
      onPressRightNavButton: this.onPressSave,
    });
    this.backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      this.backPressed,
    );
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.route.params.updateHeaderRight(() => <View />);
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'goals' });
    });
  }

  componentWillUnmount() {
    this.backHandler.remove();
    this.unsubscribeFocus();
  }

  onFinishedAdding = async () => {
    const { incompleteGoals } = this.state;
    let { largestOrdinal } = this.state;
    try {
      if (this.state.newGoalText.length > 0) {
        const clientId = this.props.selectedClient?.id;
        const milestone = {
          name: this.state.newGoalText,
          ordinal: this.state.largestOrdinal + 1,
        };
        const goal = await nasm.api.createMilestone(milestone, clientId);
        incompleteGoals.push(goal);
        largestOrdinal = goal.ordinal;
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error.message
          || 'Error trying to create milestone. Please try again later.',
      );
    } finally {
      this.setState({ incompleteGoals, adding: false, largestOrdinal });
      this.props.navigation.setParams({ backDisabled: false });
      this.props.navigation.setParams({
        renderHeaderLeft: this.renderHeaderLeft,
      });
    }
  };

  onFinishedEdit = (goal) => {
    if (!goal.id) {
      return;
    }
    const { incompleteGoals } = this.state;
    incompleteGoals.find((g) => g.id === goal.id).updating = true;
    this.setState({ incompleteGoals }, () => {
      const updateRequest = { id: goal.id, name: goal.name };
      const userId = this.props.selectedClient?.id;
      nasm.api
        .updateMilestone(updateRequest, userId)
        .catch((error) => {
          analytics().logEvent('update_milestone_name_error', {
            userId,
            error_message: error.message || 'unknown_error',
          });
        })
        .finally(() => {
          const newIncompleteGoals = this.state.incompleteGoals;
          incompleteGoals.find((g) => g.id === goal.id).updating = false;
          this.setState({
            editing: false,
            incompleteGoals: newIncompleteGoals,
          });
        });
    });
  };

  onGoalSwippedLeft(index) {
    if (!(index >= 0 && index <= this.state.incompleteGoals.length)) {
      return;
    }
    const toDelete = this.state.incompleteGoals[index];
    if (!toDelete.id) {
      return;
    }
    if (this.state.milestonesBeingDeleted.includes(toDelete.id)) {
      // Already being deleted. Ignore this request.
      return;
    }
    if (!this.state.isDisplayDeleteAlert) {
      this.setState({ isDisplayDeleteAlert: true });
      Alert.alert(
        'Delete Goal',
        'Are you sure you would like to delete this goal?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              this.setState({ isDisplayDeleteAlert: false });
            },
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => {
              this.deleteMilestone(toDelete, index);
            },
          },
        ],
        undefined,
      );
    }
  }

  onGoalTextChanged = (text, index) => {
    if (!(index >= 0 && index <= this.state.incompleteGoals.length)) {
      return;
    }
    const { incompleteGoals } = this.state;
    incompleteGoals[index].name = text;
    this.setState({ incompleteGoals });
  };

  onLongPressGoal = (drag) => {
    if (!this.state.swiping && !this.state.editing) {
      drag();
      this.setState({ sorting: true });
    }
  };

  onPressAdd = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.props.navigation.setParams({ backDisabled: true });
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
    });
    this.setState({
      adding: true,
      newGoalText: '',
    });
    setTimeout(() => {
      if (this.draggableList && this.state.incompleteGoals.length > 0) {
        // ignoring lint error because _conmponent is from a node module
        // eslint-disable-next-line no-underscore-dangle
        this.draggableList.current?._listRef?.scrollToIndex({
          index: this.state.incompleteGoals.length - 1,
          viewOffset: 0,
          viewPosition: 0.3,
        });
      }
    }, 1000);
  };

  onPressSave = () => {
    Alert.alert('saved goals');
  };

  onStartEdit = (index) => {
    if (this.draggableList) {
      setTimeout(() => {
        // ignoring lint error because _component is from a node module
        // eslint-disable-next-line no-underscore-dangle
        this.draggableList.current?._listRef?.scrollToIndex({
          index,
          viewOffset: 0,
          viewPosition: 0.3,
        });
      }, 1000);
    }
    this.setState({ editing: true });
  };

  getLargestOrdinal() {
    let largestOrdinal = 0;
    this.state.incompleteGoals.forEach((goal) => {
      largestOrdinal = Math.max(largestOrdinal, goal.ordinal);
    });
    return largestOrdinal;
  }

  getMilestones = () => {
    this.setState({ loadingMessage: 'Loading Milestones...' }, async () => {
      try {
        const milestones = await nasm.api.getAllMilestones(
          this.props.selectedClient?.id,
        );
        const incompleteGoals = [];
        const completeGoals = [];
        let largestOrdinal = 0;
        milestones.forEach((milestone) => {
          if (milestone.status) {
            completeGoals.push(milestone);
          } else {
            incompleteGoals.push(milestone);
            if (milestone.ordinal > largestOrdinal) {
              largestOrdinal = milestone.ordinal;
            }
          }
        });
        this.setState({ completeGoals, incompleteGoals, largestOrdinal });
      } catch (error) {
        Alert.alert(
          'Error',
          error.message || 'Failed to load milestones. Try again later.',
        );
      } finally {
        this.setState({ loadingMessage: null });
      }
    });
  };

  backPressed = () => {
    const parentNav = this.props.navigation.getParent();
    const func = parentNav ? parentNav.goBack : this.props.navigation.goBack;
    if (!this.props.route.params?.backDisabled) {
      func();
    }
    return true;
  };

  deleteMilestone = async (milestoneToDelete, index) => {
    const { milestonesBeingDeleted } = this.state;
    const idToDelete = milestoneToDelete.id;
    milestonesBeingDeleted.push(idToDelete);
    this.setState(
      { milestonesBeingDeleted, isDisplayDeleteAlert: false },
      async () => {
        try {
          await nasm.api.deleteMilestone(idToDelete);
          const { incompleteGoals } = this.state;
          incompleteGoals.splice(index, 1);
          let { largestOrdinal } = this.state;
          if (milestoneToDelete.ordinal >= largestOrdinal) {
            largestOrdinal = this.getLargestOrdinal();
          }
          LayoutAnimation.configureNext(androidSafeLayoutAnimation);
          this.setState({ incompleteGoals, largestOrdinal });
        } catch (error) {
          Alert.alert(
            'Error',
            error.message || 'Failed to delete milestone. Try again later.',
          );
        } finally {
          const deleteIndex = milestonesBeingDeleted.indexOf(idToDelete);
          if (deleteIndex !== -1) {
            milestonesBeingDeleted.splice(deleteIndex, 1);
          }
          this.setState({ milestonesBeingDeleted });
        }
      },
    );
  };

  handleListReordering = (goals) => {
    let largestOrdinal = 0;
    const newMilestones = [];
    goals.forEach((goal, index) => {
      const copy = {
        id: goal.id,
      };
      copy.ordinal = index + 1;
      goal.ordinal = copy.ordinal;
      largestOrdinal = Math.max(largestOrdinal, copy.ordinal);
      newMilestones.push(copy);
    });
    this.setState({ incompleteGoals: goals, largestOrdinal, sorting: false });
    const userId = this.props.selectedClient?.id;
    nasm.api.updateMilestones(newMilestones, userId).catch((error) => {
      analytics().logEvent('reordering_milestones_error', {
        userId,
        error_message: error.message || 'unknown_error',
      });
    });
  };

  markMilestoneAsComplete = async (milestone, complete) => {
    if (this.state.canMarkComplete === true) {
      this.setState({ canMarkComplete: false });
      const { incompleteGoals, completeGoals } = this.state;
      let { largestOrdinal } = this.state;
      const arrayToRemoveFrom = complete ? incompleteGoals : completeGoals;
      const arrayToPushTo = complete ? completeGoals : incompleteGoals;
      const index = arrayToRemoveFrom.indexOf(milestone);
      if (index !== -1) {
        milestone.status = complete;
        if (!complete) {
          largestOrdinal = this.getLargestOrdinal() + 1;
          milestone.ordinal = largestOrdinal;
        } else if (milestone.ordinal >= largestOrdinal) {
          largestOrdinal = this.getLargestOrdinal();
        }
        try {
          const clientId = this.props.selectedClient?.id;
          const updateRequest = { id: milestone.id, status: milestone.status };
          await nasm.api.updateMilestone(updateRequest, clientId);
          arrayToRemoveFrom.splice(index, 1);
          arrayToPushTo.push(milestone);
          LayoutAnimation.configureNext(androidSafeLayoutAnimation);
          this.setState({ incompleteGoals, completeGoals, largestOrdinal });
        } catch (error) {
          Alert.alert(
            'Error',
            error.message
              || 'Unable to mark milestone as complete. Please try again later.',
          );
        } finally {
          this.setState({ canMarkComplete: true });
        }
      }
    }
  };

  reloadList = () => {
    this.setState({ listKey: this.state.listKey + 1 });
  };

  renderCompleteGoal = ({ item, index }) => {
    if (!item) return null;
    return (
      <TouchableOpacity
        key={index}
        onPress={() => this.markMilestoneAsComplete(item, false)}
        style={[styles.goalCell, { opacity: 0.3 }]}
      >
        <Image
          style={{ tintColor: colors.subGrey, marginLeft: 28 }}
          source={sortImage}
        />
        <View style={{ flex: 1 }}>
          <Text>{item.name}</Text>
        </View>
        <Image source={completedImage} />
      </TouchableOpacity>
    );
  };

  renderFooter = () => (
    <View style={{ paddingBottom: 200 }}>
      {this.state.adding && (
        <View style={styles.goalCell}>
          <Image
            style={{ tintColor: colors.subGrey, marginLeft: 28 }}
            source={sortImage}
          />
          <TextInput
            autoFocus
            style={{ flex: 1 }}
            value={this.state.newGoalText}
            onChangeText={(text) => {
              const filteredValue = removeAllSpecialCharacters(text);
              this.setState({ newGoalText: filteredValue });
            }}
            onBlur={this.onFinishedAdding}
            underlineColorAndroid="transparent"
            selectionColor={colors.nasmBlue}
            autoCorrect={false}
            multiline
            returnKeyType="done"
            blurOnSubmit
          />
        </View>
      )}
      <FlatList
        data={this.state.completeGoals}
        renderItem={this.renderCompleteGoal}
        extraData={this.state}
      />
    </View>
  );

  renderHeaderLeft = (params) => (
    <HeaderLeftButton
      style={{
        paddingHorizontal: 16,
        opacity: params.backDisabled ? 0.5 : 1,
      }}
      disabled={params.backDisabled}
      onPress={() => this.backPressed()}
    />
  );

  renderIncompleteGoal = ({
    item, getIndex, drag, isActive,
  }) => {
    if (!item) return null;
    const index = getIndex();
    return (
      <Swipeable
        onRightActionRelease={
          this.state.sorting ? undefined : () => this.onGoalSwippedLeft(index)
        }
        rightContent={
          this.state.sorting ? null : (
            <View
              style={{
                backgroundColor: 'red',
                flex: 1,
                justifyContent: 'center',
              }}
            >
              <Text style={styles.deleteText}>Delete</Text>
            </View>
          )
        }
      >
        <TouchableOpacity
          style={[
            styles.goalCell,
            isActive && {
              backgroundColor: colors.background,
              borderTopWidth: 2,
              borderBottomWidth: 2,
              borderColor: '#d7d7d7',
            },
          ]}
          onLongPress={() => this.onLongPressGoal(drag)}
          activeOpacity={1}
        >
          <Image
            style={{ tintColor: colors.subGrey, marginLeft: 28 }}
            source={sortImage}
          />
          <View style={{ flex: 1, flexDirection: 'row' }}>
            <TextInput
              value={item.name}
              onChangeText={(text) => {
                const filteredValue = removeAllSpecialCharacters(text);
                this.onGoalTextChanged(filteredValue, index);
              }}
              onFocus={() => this.onStartEdit(index)}
              onBlur={() => this.onFinishedEdit(item)}
              underlineColorAndroid="transparent"
              selectionColor={colors.lightBlue}
              autoCorrect={false}
              multiline
              returnKeyType="done"
              blurOnSubmit
            />
          </View>
          {item.updating && <ActivityIndicator color={colors.subGrey} />}
          {!item.updating && (
            <TouchableOpacity
              disabled={
                this.state.editing || this.state.sorting || this.state.swiping
              }
              onPress={() => this.markMilestoneAsComplete(item, true)}
            >
              <Image source={incompleteImage} />
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </Swipeable>
    );
  };

  render() {
    if (this.state.loadingMessage !== null) {
      return <LoadingSpinner visible title={this.state.loadingMessage} />;
    }
    return (
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : null}
      >
        <DraggableFlatList
          ref={(ref) => {
            this.draggableList = ref;
          }}
          key={this.state.listKey}
          keyExtractor={(item) => item.id}
          data={this.state.incompleteGoals}
          onDragEnd={({ data }) => this.handleListReordering(data)}
          renderItem={this.renderIncompleteGoal}
          ListFooterComponent={this.renderFooter}
          extraData={this.state}
          onScrollToIndexFailed={() => {}}
          onScrollBeginDrag={() => {
            this.setState({ scrolling: true });
          }}
          onScrollEndDrag={() => {
            this.setState({ scrolling: false });
          }}
          scrollEnabled={!this.state.swiping}
          activationDistance={20}
        />
        {!this.state.adding && <FloatingButton onPress={this.onPressAdd} />}
      </KeyboardAvoidingView>
    );
  }
}

const styles = {
  container: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  goalCell: {
    flexDirection: 'row',
    paddingVertical: 13,
    paddingRight: 28,
    borderColor: colors.subGreyLight,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  deleteText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.white,
    paddingLeft: 30,
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

Milestones.propTypes = propTypes;
const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(Milestones);
