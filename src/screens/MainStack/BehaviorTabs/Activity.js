import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Components
import { <PERSON><PERSON>rea<PERSON>iew, StatusBar, Alert } from 'react-native';
import analytics from '@react-native-firebase/analytics';
import { <PERSON><PERSON>iew, <PERSON><PERSON><PERSON><PERSON><PERSON>, MotivationView } from '../../../components';
import nasm from '../../../dataManager/apiConfig';
import { selectClient } from '../../../actions';
import { ROLES } from '../../../constants';

// Analytics

// Styles
import { colors } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

const propTypes = {
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    client_user: PropTypes.shape({
      goals: PropTypes.arrayOf(
        PropTypes.shape({
          name: PropTypes.string,
        }),
      ),
    }),
  }).isRequired,
  currentUser: PropTypes.shape({
    role: PropTypes.oneOf([ROLES.CLIENT, ROLES.TRAINER]),
    id: PropTypes.string,
  }),
  selectClient: PropTypes.func.isRequired,
};

class Activity extends Component {
  constructor(props) {
    super(props);
    let selectedFocus;
    if (
      props.selectedClient?.client_user.goals
      && props.selectedClient?.client_user.goals.length !== 0
    ) {
      [selectedFocus] = props.selectedClient?.client_user.goals;
    }
    this.state = {
      focusData: null,
      selectedFocus,
      motivation: '',
      motivationVisible: true,
      activityLevels: null,
      selectedActivityLevel: null,
      loadingSpinnerText: null,
      changesToSave: false,
    };
  }

  componentDidMount() {
    this.getAllData();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.route.params.updateHeaderLeft(this.renderHeaderLeft);
      this.props.route.params.updateHeaderRight(this.renderHeaderRight);
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'activity' });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onActivityLevelChanged = (activityLevel) => {
    this.setState({
      selectedActivityLevel: activityLevel.id,
      changesToSave: true,
    });
  };

  onFocusChanged = (focus) => {
    this.setState({ selectedFocus: focus, changesToSave: true });
  };

  onMotivationChanged = (motivation) => {
    this.setState({ motivation, changesToSave: true });
  };

  onMotivationVisibleChanged = (motivationVisible) => {
    this.setState({ motivationVisible });
  };

  onPressSave = () => {
    const userId = this.props.selectedClient?.id;
    if (!userId) {
      return;
    }
    this.setState({ loadingSpinnerText: 'Saving...' }, async () => {
      try {
        const requestObject = {};
        const {
          motivation = '',
          motivationVisible = true,
          selectedFocus,
          selectedActivityLevel,
        } = this.state;
        requestObject.motivation = motivation;
        requestObject.hide_motivation = !motivationVisible;
        if (selectedFocus && selectedFocus.id) {
          requestObject.goal_id = selectedFocus.id;
        }
        if (selectedActivityLevel) {
          requestObject.activity_level_id = selectedActivityLevel;
        }
        const updatedUser = await nasm.api.updateClientUser(
          requestObject,
          userId,
        );
        this.props.selectClient(updatedUser);
        this.setState({ loadingSpinnerText: null, changesToSave: false });
      } catch (error) {
        Alert.alert('Error', error.message);
      }
    });
  };

  getAllData = () => {
    this.setState({ loadingSpinnerText: 'Loading...' }, async () => {
      let {
        activityLevels,
        focusData,
        motivation,
        motivationVisible,
        selectedActivityLevel,
      } = this.state;
      try {
        activityLevels = await nasm.api.getActivityLevels();
        focusData = await nasm.api.getAllGoals();
        const user = this.props.selectedClient?.client_user;
        motivation = user.motivation || '';
        motivationVisible = !user.hide_motivation;
        if (user.activity_level_id) {
          selectedActivityLevel = activityLevels.find(
            (activityLevel) => activityLevel.id === user.activity_level_id,
          ).id || null;
        }
      } catch (error) {
        Alert.alert('Error', error.message);
      } finally {
        this.setState({
          activityLevels,
          focusData,
          motivation,
          motivationVisible,
          selectedActivityLevel,
          loadingSpinnerText: null,
          changesToSave: false,
        });
      }
    });
  };

  backPressed = () => {
    const parentNav = this.props.navigation.getParent();
    const func = parentNav ? parentNav.goBack : this.props.navigation.goBack;
    if (!this.state.changesToSave) {
      func();
    } else {
      Alert.alert(
        'Discard Changes?',
        'If you go back now, you will lose your changes.',
        [
          { text: 'Discard', style: 'cancel', onPress: () => func() },
          { text: 'Keep Editing', style: 'default' },
        ],
      );
    }
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton onPress={() => this.backPressed()} />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.onPressSave()}
      title="Save"
      titleStyle={styles.headerButtonText}
    />
  );

  render() {
    if (this.state.loadingSpinnerText) {
      return <LoadingSpinner visible title={this.state.loadingSpinnerText} />;
    }
    let shouldShowMotivationView = true;
    const editable = this.props.currentUser.role === ROLES.CLIENT;
    if (!editable) {
      shouldShowMotivationView = this.state.motivation.length > 0;
    }
    return (
      <SafeAreaView style={{ flex: 1 }}>
        {this.state.focusData && (
          <FocusView
            data={this.state.focusData}
            value={
              this.state.selectedFocus ? this.state.selectedFocus.id : undefined
            }
            selectedChangedCallback={this.onFocusChanged}
            Footer={(
              <>
                {this.state.activityLevels && (
                  <FocusView
                    title="Acitvity Level"
                    value={this.state.selectedActivityLevel}
                    selectedChangedCallback={this.onActivityLevelChanged}
                    data={this.state.activityLevels}
                  />
                )}
                {shouldShowMotivationView && (
                  <MotivationView
                    textChangedCallback={this.onMotivationChanged}
                    text={this.state.motivation}
                    visibleByTrainer={this.state.motivationVisible}
                    visibleChangedCallback={this.onMotivationVisibleChanged}
                    editable={editable}
                  />
                )}
              </>
            )}
            isShowBullet
          />
        )}
      </SafeAreaView>
    );
  }
}

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

Activity.propTypes = propTypes;
const mapStateToProps = ({ currentUser, selectedClient }) => ({
  currentUser,
  selectedClient,
});
const mapDispatchToProps = { selectClient };
export default connect(mapStateToProps, mapDispatchToProps)(Activity);
