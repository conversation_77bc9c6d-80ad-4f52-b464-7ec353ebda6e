import React, { Component } from 'react';
import { Dimensions } from 'react-native';
import PropTypes from 'prop-types';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { materialTabBarOptions } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import CurrentDailyReadiness from './CurrentDailyReadiness';
import HistoryDailyReadiness from './HistoryDailyReadiness';

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

const propTypes = {
  navigation: PropTypes.any.isRequired,
  route: PropTypes.any.isRequired,
};

const defaultProps = {};

class DailyReadinessTabView extends Component {
  static router = TabNav.router;

  componentDidMount() {
    this.props.navigation.setOptions({
      headerTitle: 'Daily Readiness',
      headerLeft: this.renderHeaderLeft,
    });
  }

  renderHeaderLeft = () => (
    <HeaderLeftButton onPress={() => this.props.navigation.goBack()} />
  );

  render() {
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
        style={materialTabBarOptions.tabBarOptions.tabBarStyle}
        initialRouteName={
          this.props.route?.params?.initialRouteName || 'CurrentDailyReadiness'
        }
      >
        <TabNav.Screen
          name="Current"
          component={CurrentDailyReadiness}
          initialParams={{ userIdOfClient: this.props.route.params.userId }}
        />
        <TabNav.Screen
          name="History"
          component={HistoryDailyReadiness}
          initialParams={{ userIdOfClient: this.props.route.params.userId }}
        />
      </TabNav.Navigator>
    );
  }
}

DailyReadinessTabView.propTypes = propTypes;
DailyReadinessTabView.defaultProps = defaultProps;

export default DailyReadinessTabView;
