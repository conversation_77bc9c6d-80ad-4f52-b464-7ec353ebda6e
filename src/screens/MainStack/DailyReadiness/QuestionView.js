import React from 'react';
import {
  Text,
  StyleSheet,
  View,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import PropTypes from 'prop-types';
import EmojiView from './EmojiView';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';

const QuestionView = (props) => {
  const {
    question,
    levels,
    onPressNext,
    onPressEmoji,
    selectedEmoji,
    notes,
    onChangeText,
    buttonTitle,
  } = props;
  return (
    <View>
      <Text style={styles.title}>{question}</Text>
      <View style={styles.levelView}>
        {selectedEmoji && (
          <Text style={styles.level}>
            {levels?.filter((level) => level.value === selectedEmoji)[0]?.label}
          </Text>
        )}
      </View>
      <EmojiView
        viewStyle={styles.emojiViewStyle}
        customEmojiStyle={styles.emojiStyle}
        onPress={onPressEmoji}
        selectedValue={selectedEmoji}
      />
      <TextInput
        placeholder="Notes"
        style={styles.inputStyle}
        value={notes}
        onChangeText={(val) => onChangeText(val)}
      />
      <View style={styles.btnView}>
        <TouchableOpacity
          disabled={!selectedEmoji}
          activeOpacity={0.6}
          style={styles.buttonStyle(!selectedEmoji)}
          onPress={onPressNext}
        >
          <Text style={styles.start(colors.white)}>{buttonTitle}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// PropTypes
const propTypes = {
  onPressNext: PropTypes.func,
  onPressEmoji: PropTypes.func,
  onChangeText: PropTypes.func,
  notes: PropTypes.string,
  question: PropTypes.string,
  levels: PropTypes.array,
  selectedEmoji: PropTypes.number,
  buttonTitle: PropTypes.string,
};

const defaultProps = {
  onPressNext: undefined,
  onPressEmoji: undefined,
  onChangeText: undefined,
  selectedEmoji: null,
  notes: '',
  question: '',
  buttonTitle: 'Next',
  levels: [],
};

QuestionView.propTypes = propTypes;
QuestionView.defaultProps = defaultProps;

const styles = StyleSheet.create({
  emojiViewStyle: {
    width: '90%',
    alignSelf: 'center',
  },
  emojiStyle: {
    width: curvedScale(42),
    height: curvedScale(42),
  },
  title: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(22),
    textAlign: 'center',
  },
  levelView: {
    height: curvedScale(50),
    alignItems: 'center',
    justifyContent: 'center',
  },
  level: {
    color: colors.black,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(20),
    textAlign: 'center',
  },
  btnView: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonStyle: (disabled) => ({
    width: '40%',
    borderRadius: 40,
    backgroundColor: colors.azure,
    opacity: disabled ? 0.5 : 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: curvedScale(8),
  }),
  inputStyle: {
    color: colors.black,
    backgroundColor: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(16),
    borderRadius: curvedScale(20),
    paddingVertical: curvedScale(8),
    paddingHorizontal: curvedScale(15),
    marginVertical: curvedScale(20),
  },
  start: (color) => ({
    color,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(14),
  }),
});
export default QuestionView;
