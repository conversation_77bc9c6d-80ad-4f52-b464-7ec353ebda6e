import React, { Component } from 'react';
import {
  View,
  Text,
  ScrollView,
  StatusBar,
  Image,
  Platform,
  StyleSheet,
} from 'react-native';
import moment from 'moment';
import PropTypes from 'prop-types';
import analytics from '@react-native-firebase/analytics';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import ScaledText from '../../../components/ScaledText';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';

const upArrow = require('../../../resources/arrowUpStatIcon.png');
const downArrow = require('../../../resources/arrowDownStatIcon.png');

const dateFormat = 'M/D/YY';

const propTypes = {
  navigation: PropTypes.shape({
    addListener: PropTypes.func,
    setParams: PropTypes.func,
    navigate: PropTypes.func,
    goBack: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      data: PropTypes.string,
      selectedReadiness: PropTypes.array,
    }),
  }).isRequired,
};

const defaultProps = {};

class DailyReadinessComparison extends Component {
  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    return {
      title: 'Compare',
      headerLeft: renderHeaderLeft,
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      selectedReadinessData: [],
      readinessData: this.props?.route?.params?.selectedReadiness?.sort(
        (a, b) => moment(a.created_at).diff(moment(b.created_at)),
      ),
    };
  }

  componentDidMount() {
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
    });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', {
        screen_name: 'daily_readiness_comparison',
      });
    });
    this.setSelectedReadinessData();
  }

  setSelectedReadinessData = () => {
    const { readinessData } = this.state;
    const arr = [];
    for (let i = 0; i < 5; i += 1) {
      let label = '';
      let key = '';
      let style = {};
      switch (i) {
        case 0:
          label = 'Energy';
          key = 'energy_level';
          style = [
            styles.legendStyle,
            { backgroundColor: colors.pinkishPurple },
          ];
          break;
        case 1:
          label = 'Mood';
          key = 'mood_level';
          style = [styles.squareStyle, { backgroundColor: colors.linkBlue }];
          break;
        case 2:
          label = 'Stress';
          key = 'stress_level';
          style = [
            styles.squareStyle,
            styles.stressStyle,
            { backgroundColor: colors.black },
          ];
          break;
        case 3:
          label = 'Soreness';
          key = 'overall_soreness_level';
          style = [styles.triangle, styles.soreStyle];
          break;
        default:
          label = 'Sleep';
          key = 'sleep_level';
          style = [styles.triangle, styles.sleepStyle2];
          break;
      }
      arr.push({
        label,
        value1: readinessData?.[0][key],
        value2: readinessData?.[1][key],
        style,
      });
    }
    this.setState({ selectedReadinessData: arr });
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  getRatingColor = (isSoreness, value) => {
    if (isSoreness) {
      switch (value) {
        case 1:
          return colors.midGreen;
        case 2:
          return colors.mildGreen;
        case 3:
          return colors.lightYellow;
        case 4:
          return colors.brickOrange;
        case 5:
          return colors.pink;
        default:
          return null;
      }
    }
    switch (value) {
      case 1:
        return colors.pink;
      case 2:
        return colors.brickOrange;
      case 3:
        return colors.lightYellow;
      case 4:
        return colors.mildGreen;
      case 5:
        return colors.midGreen;
      default:
        return null;
    }
  };

  renderRow = (label, value1, value2, viewStyle) => {
    let icon = '';
    const isSoreness = label.toLowerCase() === 'soreness';
    if (value2 > value1) {
      icon = upArrow;
    } else if (value1 > value2) {
      icon = downArrow;
    }
    if (isSoreness) {
      if (value2 > value1) {
        icon = downArrow;
      } else if (value1 > value2) {
        icon = upArrow;
      }
    }
    const ratingColor1 = this.getRatingColor(isSoreness, value1);
    const ratingColor2 = this.getRatingColor(isSoreness, value2);
    let rating1 = value1;
    let rating2 = value2;
    if (rating1 === 0) {
      rating1 = '-';
    }
    if (rating2 === 0) {
      rating2 = '-';
    }
    return (
      <View style={styles.rowContainer}>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.valueContainer}>
          <View style={[viewStyle, styles.symbolStyle]} />
          <View style={[styles.ratingView, { backgroundColor: ratingColor1 }]}>
            <Text style={styles.ratingLabel}>{rating1}</Text>
          </View>
        </View>
        <View style={styles.divider} />
        <View style={styles.valueContainer}>
          <View style={[viewStyle, styles.symbolStyle]} />
          <View style={[styles.ratingView, { backgroundColor: ratingColor2 }]}>
            <Text style={styles.ratingLabel}>{rating2}</Text>
          </View>
          <Image style={styles.arrowStyle} source={icon} />
        </View>
        <View style={styles.horizontalDivider} />
      </View>
    );
  };

  renderHeader = () => (
    <View style={styles.dateContainer}>
      <View style={styles.rowContainer}>
        <View style={styles.labelContainer} />
        {[1, 2].map((item, index) => (
          <View style={styles.dateValueContainer}>
            <ScaledText style={styles.dateText}>
              {moment(this.state.readinessData?.[index].created_at).format(
                dateFormat,
              )}
            </ScaledText>
          </View>
        ))}
      </View>
    </View>
  );

  render() {
    const { selectedReadinessData } = this.state;
    return (
      <ScrollView>
        {this.renderHeader()}
        {selectedReadinessData.map((item) => this.renderRow(item.label, item.value1, item.value2, item.style))}
        <View style={styles.horizontalDivider} />
      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  dateContainer: {
    backgroundColor: colors.medYellow,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  labelContainer: {
    flex: 4,
    justifyContent: 'center',
    marginTop: curvedScale(10),
  },
  dateValueContainer: {
    flex: 3,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: curvedScale(45),
  },
  valueContainer: {
    flex: 3,
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: curvedScale(10),
    alignSelf: 'center',
  },
  dateText: {
    color: colors.white,
    fontSize: curvedScale(17),
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  legendStyle: {
    width: curvedScale(5),
    height: curvedScale(5),
    borderRadius: curvedScale(2.5),
  },
  squareStyle: {
    width: curvedScale(5),
    height: curvedScale(5),
  },
  stressStyle: {
    transform: [{ rotate: '45deg' }],
  },
  soreStyle: {
    borderLeftWidth: curvedScale(4),
    borderRightWidth: curvedScale(4),
    borderBottomWidth: curvedScale(8),
  },
  sleepStyle2: {
    borderLeftWidth: curvedScale(4),
    borderRightWidth: curvedScale(4),
    borderTopWidth: curvedScale(8),
    borderTopColor: colors.medYellow,
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: curvedScale(14),
    fontWeight: '500',
    color: colors.white,
  },
  label: {
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    color: colors.black,
    marginLeft: curvedScale(20),
  },
  divider: {
    width: Platform.OS === 'ios' ? 1 : 2,
    backgroundColor: colors.subGreyLight,
    height: curvedScale(50),
  },
  horizontalDivider: {
    height: Platform.OS === 'ios' ? 1 : 2,
    backgroundColor: colors.subGreyLight,
  },
  triangle: {
    width: curvedScale(5),
    height: curvedScale(5),
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderBottomColor: colors.nasmRed,
  },
  symbolStyle: {
    alignSelf: 'center',
  },
  ratingView: {
    justifyContent: 'center',
    alignItems: 'center',
    width: curvedScale(20),
    height: curvedScale(20),
    borderRadius: curvedScale(20 / 2),
    padding: curvedScale(2),
    marginHorizontal: curvedScale(5),
  },
  ratingLabel: {
    color: colors.black,
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Heavy',
    textAlign: 'center',
  },
  arrowStyle: {
    width: curvedScale(12),
    height: curvedScale(12),
    alignSelf: 'center',
  },
});

DailyReadinessComparison.propTypes = propTypes;
DailyReadinessComparison.defaultProps = defaultProps;

export default DailyReadinessComparison;
