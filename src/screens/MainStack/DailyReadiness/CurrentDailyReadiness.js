import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  SafeAreaView,
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  Alert,
  Platform,
  LayoutAnimation,
  UIManager,
  ScrollView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import PropTypes from 'prop-types';
import IconFeader from 'react-native-vector-icons/Feather';
import moment from 'moment';
import nasm from '../../../dataManager/apiConfig';
import DailyReadinessGraphSection from './DailyReadinessGraphSection';
import EmojiView from './EmojiView';
import Body from '../../../components/react-native-body-highlighter';
import { curvedScale } from '../../../util/responsive';
import { ScaledText } from '../../../components';
import { colors } from '../../../styles';
import {
  energy_level,
  getReadinessRatingLabel,
  getSelectedLevelLabel,
  mood_level,
  sleep_level,
  stress_level,
} from './DailyReadinessConstants';
import { ROLES } from '../../../constants';
import InformationBox from '../../../components/InformationBox';

const periods = {
  WEEK: 'WEEKLY',
  MONTH: 'MONTHLY',
  YEAR: 'YEARLY',
};

const dateFormat = 'YYYY-MM-DD';

const propTypes = {
  currentUser: PropTypes.shape({
    role: PropTypes.string,
    client_user: PropTypes.shape({
      id: PropTypes.string,
    }),
  }),
  navigation: PropTypes.shape({
    addListener: PropTypes.func,
    navigate: PropTypes.func,
    goBack: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      userIdOfClient: PropTypes.string,
    }),
  }),
  assessment_id: PropTypes.string,
  userIdOfClient: PropTypes.string,
};

const defaultProps = {
  route: null,
  currentUser: null,
  assessment_id: '',
  userIdOfClient: '',
};

class CurrentDailyReadiness extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
      period: periods.WEEK,
      endDate: moment(),
      currentAssessmentData: '',
      graphData: [],
      showTopView: false,
      isFilledToday: false,
      isClient: this.props.currentUser.role === ROLES.CLIENT,
      userIdOfClient:
        this.props.route?.params?.userIdOfClient || this.props.userIdOfClient,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.getDailyCurrentAssessment();
      this.getGraphData();
    });
  }

  componentWillUnmount() {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
  }

  getDailyCurrentAssessment = async () => {
    const { isClient, userIdOfClient } = this.state;
    try {
      let response;
      if (this.props.assessment_id) {
        response = await nasm.api.getReadinessDetails(this.props.assessment_id);
      } else {
        response = await nasm.api.getDailyCurrentAssessment(userIdOfClient);
      }
      const created_at = moment(response?.created_at).format(dateFormat);
      const today = moment().format(dateFormat);
      const isFilledToday = response && moment(created_at).isSame(today);
      this.setState({
        currentAssessmentData: response,
        isFilledToday,
        showTopView: response === null || (response !== null && isClient),
        isLoading: false,
      });
      this.setAnimation();
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
        [
          {
            text: 'Ok',
            onPress: this.props.navigation.goBack(),
          },
        ],
        { cancelable: false },
      );
    }
  };

  getGraphData = async () => {
    const userId = this.state.userIdOfClient;
    const currentDate = moment().format();
    try {
      const response = await nasm.api.getDailyHistoryGraphAssessment(
        currentDate,
        userId,
        this.state.period,
      );
      this.setGraphData(response);
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  setGraphData = (response) => {
    const graphData = [];
    response.forEach((historyObj) => {
      let divisibleBy = 4;
      const {
        created_at,
        energy_level: energyLevel,
        mood_level: moodLevel,
        stress_level: stressLevel,
        sleep_level: sleepLevel,
        overall_soreness_level,
        month,
      } = historyObj;

      if (Number(overall_soreness_level) > 0) {
        divisibleBy = 5;
      }

      const dateNumber = moment(created_at).format('D');
      let xAxisVal = moment(created_at)
        .format('ddd')
        .toUpperCase()
        .concat(`\n${dateNumber}`);
      if (this.state.period === periods.MONTH) {
        xAxisVal = moment(created_at).format('D');
      } else if (this.state.period === periods.YEAR) {
        xAxisVal = `${month}`;
      }

      const avgValue = (Number(energyLevel)
          + Number(moodLevel)
          + Number(stressLevel)
          + Number(sleepLevel)
          + Number(overall_soreness_level))
        / divisibleBy;

      const obj = {
        day: xAxisVal,
        average_of_assessment_levels: Number(avgValue),
      };

      if (this.state.period === periods.YEAR) {
        graphData.push(obj);
      } else {
        const getIndex = graphData.findIndex((ele) => ele.day === xAxisVal);
        if (getIndex === -1) {
          graphData.push(obj);
        }
      }
    });
    this.setState({ graphData });
  };

  setAnimation = () => {
    if (Platform.OS === 'android') {
      if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
      }
    }
    LayoutAnimation.configureNext({
      duration: 500,
      create: { type: 'linear', property: 'opacity' },
      update: { type: 'spring', springDamping: 0.8 },
    });
  };

  getSorenessLabel = (overall_soreness_level = 1) => {
    const sorenessLabels = [
      'Not Sore',
      'Mildly',
      'Moderately',
      'Very',
      'Extreme',
    ];
    return overall_soreness_level
      ? sorenessLabels[overall_soreness_level - 1]
      : sorenessLabels[0];
  };

  renderTopBarColor = (average_of_assessment_levels) => {
    if (average_of_assessment_levels < 2) {
      return colors.pink;
    }
    if (average_of_assessment_levels < 3) {
      return colors.lightYellow;
    }
    if (average_of_assessment_levels < 4) {
      return colors.mildGreen;
    }
    return colors.midGreen;
  };

  renderReadinessScoreView = (currentAssessmentData) => (
    <View
      style={styles.readinessInnerButton(
        this.renderTopBarColor(
          currentAssessmentData?.average_of_assessment_levels,
        ),
      )}
    >
      <View style={styles.readinessHeaderView}>
        <Text style={styles.statusText}>
          Readiness:
          {' '}
          {getReadinessRatingLabel(
            this.state.currentAssessmentData?.average_of_assessment_levels,
          )}
        </Text>
        <View style={styles.avgView}>
          <Text style={styles.avgRating}>
            {currentAssessmentData?.average_of_assessment_levels}
          </Text>
          <Text style={styles.avgText}> Avg.</Text>
        </View>
      </View>
      <ScaledText style={styles.scoreDescriptionText}>
        A high score means you’re ready for a higher-intensity workout. A low
        score means your body is fatigued from a tough workout, poor sleep,
        stress or strain on the body—or a combination of these factors.
      </ScaledText>
    </View>
  );

  renderPeriodPicker = () => (
    <View style={styles.periodPickerContainer}>
      {this.renderPeriodView({ label: 'Week', period: periods.WEEK })}
      <View style={styles.separatorView} />
      {this.renderPeriodView({ label: 'Month', period: periods.MONTH })}
      <View style={styles.separatorView} />
      {this.renderPeriodView({ label: 'Year', period: periods.YEAR })}
    </View>
  );

  renderPeriodView = ({ label, period }) => (
    <>
      <TouchableOpacity
        activeOpacity={0.6}
        style={
          this.state.period === period
            ? styles.selectedContainer
            : styles.unselectedContainer
        }
        onPress={() => {
          if (period !== this.state.period) {
            this.setState(
              {
                endDate: moment(),
                period,
              },
              () => {
                this.getGraphData();
              },
            );
          }
        }}
      >
        <ScaledText
          style={
            this.state.period === period
              ? styles.selectedPeriod
              : styles.unselectedPeriod
          }
        >
          {label}
        </ScaledText>
      </TouchableOpacity>
    </>
  );

  getAxesData = () => {
    const axesData = [];
    switch (this.state.period) {
      case periods.YEAR:
        for (let i = 1; i < 12 + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      case periods.MONTH:
        for (let i = 1; i < this.state.endDate.daysInMonth() + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      default:
        for (let i = 1; i < 8; i += 1) {
          const date = moment()
            .startOf('week')
            .add(i - 1, 'days');
          const dayLabel = moment(date).format('ddd');
          const dateNumber = moment(date).format('D');
          const label = `${dayLabel.toUpperCase()}\n${dateNumber}`;
          axesData.push(label);
        }
        break;
    }
    return axesData;
  };

  renderGraph = () => (
    <DailyReadinessGraphSection
      ref={(ref) => {
        this.graphSection = ref;
      }}
      width={Dimensions.get('window').width / 1.1}
      endDate={this.state.endDate}
      axesData={this.getAxesData()}
      assessments={this.state.graphData}
      isMonth={this.state.period === periods.MONTH}
    />
  );

  levelType = (key, value) => {
    switch (key) {
      case 'energy_level':
        return getSelectedLevelLabel(energy_level, value);
      case 'mood_level':
        return getSelectedLevelLabel(mood_level, value);
      case 'stress_level':
        return getSelectedLevelLabel(stress_level, value);
      case 'sleep_level':
        return getSelectedLevelLabel(sleep_level, value);
      default:
        return null;
    }
  };

  getRatingColor = (value) => {
    switch (value) {
      case 1:
        return colors.pink;
      case 2:
        return colors.brickOrange;
      case 3:
        return colors.lightYellow;
      case 4:
        return colors.mildGreen;
      case 5:
        return colors.midGreen;
      default:
        return null;
    }
  };

  editQuestion = (selectedIndex) => {
    const { currentAssessmentData } = this.state;
    const created_at = moment(currentAssessmentData.created_at).format(
      dateFormat,
    );
    if (moment(created_at).isSame(moment().format(dateFormat))) {
      if (selectedIndex === 5) {
        this.props.navigation.navigate({
          name: 'LevelOfSoreness',
          params: {
            selectedIndex,
            mode: 'edit',
            jsonData: currentAssessmentData,
          },
        });
      } else {
        this.props.navigation.navigate({
          name: 'DailyReadiness',
          params: {
            selectedIndex,
            mode: 'edit',
            currentAssessmentData,
          },
        });
      }
    }
  };

  renderItem = (title, levelKey, notesKey, index) => {
    const { currentAssessmentData, isFilledToday, isClient } = this.state;
    const levelValue = currentAssessmentData[levelKey];
    const label = index < 5
      ? this.levelType(levelKey, levelValue)
      : this.getSorenessLabel(levelValue);
    const ratingColor = this.getRatingColor(levelValue);
    const notes = this.state.currentAssessmentData[notesKey];
    return (
      <TouchableOpacity
        disabled={!isFilledToday || !isClient}
        style={styles.item}
        onPress={() => this.editQuestion(index)}
      >
        <View style={styles.leftBox}>
          <View style={styles.itemTitleValueStyle}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.value}>{label}</Text>
          </View>
          {notes ? <Text style={styles.subTitle}>{notes}</Text> : null}
        </View>
        <View style={styles.rightBox}>
          {index < 5 ? (
            <View style={[styles.ratingView, { backgroundColor: ratingColor }]}>
              <Text style={styles.ratingLabel}>{levelValue}</Text>
            </View>
          ) : null}
          {isFilledToday && isClient ? (
            <IconFeader name="chevron-right" size={20} color={colors.subGrey} />
          ) : null}
        </View>
      </TouchableOpacity>
    );
  };

  getColorToFill = (muscle) => {
    let color;
    if (muscle.intensity) color = colors[muscle.intensity];
    else color = muscle.color;

    return color;
  };

  getColorToFill = (muscle) => {
    let color;
    if (muscle.intensity) color = colors[muscle.intensity];
    else color = muscle.color;

    return color;
  };

  renderBodySvg = (muscleData) => (
    <View style={styles.bodyContainer}>
      <View style={styles.bodyView}>
        <Body
          data={muscleData}
          frontOnly
          scale={curvedScale(1)}
          colors={[
            colors.midGreen,
            colors.mildGreen,
            colors.orange,
            colors.brickOrange,
            colors.pink,
          ]}
        />
      </View>
      <View style={styles.bodyView}>
        <Body
          data={muscleData}
          backOnly
          scale={curvedScale(1)}
          colors={[
            colors.midGreen,
            colors.mildGreen,
            colors.orange,
            colors.brickOrange,
            colors.pink,
          ]}
        />
      </View>
    </View>
  );

  renderSkipView = () => (
    <TouchableOpacity
      style={styles.skipButton}
      onPress={() => {
        this.setAnimation();
        this.setState({ showTopView: false });
      }}
    >
      <ScaledText style={styles.skipText}>Skip Today</ScaledText>
    </TouchableOpacity>
  );

  renderNewEntryView = () => (
    <TouchableOpacity
      style={styles.newEntryButton}
      onPress={() => {
        this.props.navigation.navigate({
          name: 'DailyReadiness',
          merge: true,
        });
      }}
    >
      <ScaledText style={styles.newEntryText}>New Entry</ScaledText>
      <IconFeader name="plus" size={curvedScale(20)} color={colors.white} />
    </TouchableOpacity>
  );

  render() {
    const {
      currentAssessmentData,
      isFilledToday,
      showTopView,
      isClient,
      isLoading,
    } = this.state;
    const sore_body_parts = currentAssessmentData?.sore_body_parts;
    let soreMuscles = [];
    if (sore_body_parts?.length) {
      soreMuscles = sore_body_parts.map((muscleObj) => {
        // swap left and right body part positions here to the correct side
        // e.g. LevelOfSoreness screen selects the right bicep and save change
        // but when viewing the preview in the CurrentDailyReadiness screen,
        // the body part shown visually becomes the left bicep instead.
        // The code below addresses that issue to ensure the sides match
        // between LevelOfSoreness screen and CurrentDailyReadiness screen.
        let whole_part = muscleObj.body_part.name;
        const lastIndex = whole_part.lastIndexOf('_');
        if (lastIndex > 0) {
          const part_name = whole_part.slice(0, lastIndex);
          const part_dir = whole_part.slice(lastIndex + 1);
          const parts = [part_name, part_dir];
          if (parts.length === 2) {
            if (parts[1] === 'left') {
              whole_part = `${parts[0]}_right`;
            } else if (parts[1] === 'right') {
              whole_part = `${parts[0]}_left`;
            }
          }
        }
        const obj = {
          slug: whole_part,
          intensity: muscleObj.soreness_level,
        };
        return obj;
      });
    }

    return (
      <SafeAreaView style={styles.container}>
        {isLoading ? (
          <View style={styles.loaderView}>
            <ActivityIndicator animating size="large" />
          </View>
        ) : (
          <ScrollView>
            {isFilledToday && isClient ? (
              <InformationBox
                titleComponent={(
                  <Text style={styles.topViewText}>
                    A
                    {' '}
                    <Text style={styles.topViewTextBold}>
                      Daily Readiness Assessment
                    </Text>
                    {' '}
                    has been filled out today.
                  </Text>
                )}
                descComponent={(
                  <Text style={styles.topViewText}>
                    You can make edits to today&apos;s or take a new one
                    tomorrow.
                  </Text>
                )}
              />
            ) : (
              <>
                {showTopView ? (
                  <View style={styles.topNewEntryView}>
                    <View style={styles.topNewInnerView}>
                      <View style={styles.barLineView()} />
                      <View style={styles.barLineView(true)} />
                      <EmojiView
                        viewStyle={styles.emojiViewStyle}
                        customEmojiStyle={styles.emojiCustomViewStyle}
                        disabled
                      />
                    </View>
                    <ScaledText style={styles.topNewInnerText}>
                      It looks like a Daily Readiness Assessment hasn’t been
                      filled out today.
                    </ScaledText>
                    {isClient ? (
                      <View style={styles.topNewButtonView}>
                        {currentAssessmentData ? this.renderSkipView() : null}
                        {this.renderNewEntryView()}
                      </View>
                    ) : null}
                  </View>
                ) : null}
              </>
            )}
            {currentAssessmentData ? (
              <>
                <View style={styles.readinessScoreView}>
                  {this.renderReadinessScoreView(currentAssessmentData)}
                </View>
                <View style={styles.scoreGraphView}>
                  {this.renderPeriodPicker()}
                  {this.renderGraph()}
                </View>
                <View>
                  {this.renderItem(
                    'Energy Level:',
                    'energy_level',
                    'energy_level_notes',
                    1,
                  )}
                  {this.renderItem(
                    'Overall Mood:',
                    'mood_level',
                    'mood_level_notes',
                    2,
                  )}
                  {this.renderItem(
                    'Stress Level:',
                    'stress_level',
                    'stress_level_notes',
                    3,
                  )}
                  {this.renderItem(
                    'Overall Sleep:',
                    'sleep_level',
                    'sleep_level_notes',
                    4,
                  )}
                  {this.renderItem(
                    'Level of soreness:',
                    'overall_soreness_level',
                    'sore_notes',
                    5,
                  )}
                  {sore_body_parts?.length
                    ? this.renderBodySvg(soreMuscles)
                    : null}
                </View>
              </>
            ) : null}
          </ScrollView>
        )}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loaderView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    padding: curvedScale(20),
    justifyContent: 'space-between',
  },
  itemTitleValueStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    fontSize: curvedScale(13),
  },
  value: {
    marginLeft: curvedScale(5),
    color: colors.darkGrey,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(13),
  },
  subTitle: {
    color: colors.darkGrey,
    fontFamily: 'Avenir',
    fontSize: curvedScale(11),
    marginTop: curvedScale(5),
  },
  leftBox: {
    justifyContent: 'center',
  },
  rightBox: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  ratingView: {
    justifyContent: 'center',
    alignItems: 'center',
    width: curvedScale(25),
    height: curvedScale(25),
    borderRadius: curvedScale(25 / 2),
    padding: curvedScale(2),
    marginRight: curvedScale(15),
  },
  ratingLabel: {
    color: colors.black,
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Heavy',
    textAlign: 'center',
  },
  bodyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    margin: curvedScale(20),
  },
  bodyView: {
    width: '47%',
    alignItems: 'center',
    padding: curvedScale(10),
    borderWidth: 1,
    borderColor: colors.fillDarkGrey,
    borderRadius: 10,
  },
  topViewText: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(13),
    color: colors.white,
  },
  topViewTextBold: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    fontSize: curvedScale(14),
  },
  topNewEntryView: {
    paddingHorizontal: '3%',
    paddingVertical: '5%',
    backgroundColor: colors.dustyWhite,
    alignItems: 'center',
  },
  topNewInnerView: {
    width: curvedScale(143),
    height: curvedScale(66),
    backgroundColor: colors.white,
    borderRadius: curvedScale(25),
  },
  barLineView: (isBottomView) => ({
    height: curvedScale(5),
    backgroundColor: colors.disclaimerGrey,
    borderRadius: curvedScale(2.5),
    marginHorizontal: isBottomView ? curvedScale(45) : curvedScale(25),
    marginTop: isBottomView ? curvedScale(5) : curvedScale(15),
  }),
  emojiCustomViewStyle: {
    alignSelf: 'center',
    width: curvedScale(16),
    height: curvedScale(16),
  },
  emojiViewStyle: {
    marginVertical: curvedScale(10),
    alignSelf: 'center',
    marginVartical: curvedScale(10),
    width: curvedScale(20),
  },
  topNewInnerText: {
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(13),
    color: colors.darkGrey,
    alignSelf: 'center',
    textAlign: 'center',
    marginTop: curvedScale(10),
    marginHorizontal: curvedScale(30),
  },
  topNewButtonView: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: curvedScale(20),
  },
  skipButton: {
    borderRadius: curvedScale(25),
    borderWidth: 1,
    borderColor: colors.subGreyTwo,
    padding: curvedScale(15),
    paddingVertical: curvedScale(4),
    marginRight: curvedScale(10),
    justifyContent: 'center',
    alignItems: 'center',
  },
  newEntryButton: {
    borderRadius: curvedScale(25),
    padding: curvedScale(15),
    paddingVertical: curvedScale(4),
    backgroundColor: colors.medYellow,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  skipText: {
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(10),
    color: colors.subGrey,
  },
  newEntryText: {
    fontFamily: 'Avenir-Black',
    fontSize: curvedScale(10),
    color: colors.white,
    marginRight: curvedScale(8),
  },
  readinessScoreView: {
    paddingHorizontal: curvedScale(15),
  },
  scoreGraphView: {
    flex: 1,
    paddingHorizontal: curvedScale(20),
  },
  readinessInnerButton: (borderTopColor) => ({
    paddingHorizontal: '5%',
    paddingVertical: '2%',
    borderRadius: curvedScale(6),
    borderWidth: 1,
    borderColor: colors.subGreyLight,
    borderTopWidth: 10,
    borderTopColor,
    backgroundColor: colors.white,
    marginTop: 10,
  }),
  statusText: {
    fontWeight: 'bold',
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(16),
    color: colors.black,
  },
  readinessHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  avgView: {
    flexDirection: 'row',
    paddingRight: curvedScale(12),
  },
  avgRating: {
    fontWeight: 'bold',
    fontFamily: 'Avenir-Heavy',
    color: colors.black,
    fontSize: curvedScale(25),
    marginRight: 2,
  },
  avgText: {
    color: colors.black,
    fontSize: curvedScale(16),
    fontFamily: 'Avenir-Roman',
    marginTop: curvedScale(10),
  },
  scoreDescriptionText: {
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(13),
    color: colors.subGrey,
    marginTop: curvedScale(5),
  },
  periodPickerContainer: {
    margin: curvedScale(20),
    flexDirection: 'row',
    alignSelf: 'center',
    borderColor: colors.subGrey,
    borderWidth: 1,
    borderRadius: curvedScale(4),
    alignItems: 'center',
  },
  selectedContainer: {
    flex: 1,
    padding: curvedScale(5),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGrey,
  },
  unselectedContainer: {
    flex: 1,
    padding: curvedScale(5),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  selectedPeriod: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    alignSelf: 'center',
    fontSize: curvedScale(13),
  },
  unselectedPeriod: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    alignSelf: 'center',
    fontSize: curvedScale(13),
  },
  separatorView: {
    width: 1,
    height: '100%',
    backgroundColor: colors.subGrey,
  },
});

const mapStateToProps = ({ currentUser }) => ({ currentUser });
const mapDispatchToProps = null;

CurrentDailyReadiness.propTypes = propTypes;
CurrentDailyReadiness.defaultProps = defaultProps;

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(CurrentDailyReadiness);
