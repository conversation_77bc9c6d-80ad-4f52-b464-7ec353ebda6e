import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Dimensions, View } from 'react-native';
import {
  VictoryLine,
  VictoryScatter,
  VictoryAxis,
  VictoryChart,
} from 'victory-native';
import { colors } from '../../../styles';
import { curvedScale, scaleWidth } from '../../../util/responsive';

// PropTypes
const propTypes = {
  assessments: PropTypes.array,
  loading: PropTypes.bool,
  width: PropTypes.any,
  axesData: PropTypes.any,
  isMonth: PropTypes.bool,
};
const defaultProps = {
  assessments: [],
  renderEmptyStateView: null,
  loading: false,
  width: Dimensions.get('window').width,
  axesData: ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'],
  isMonth: false,
};

class DailyReadinessGraphSection extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  getGraphPoints = () => {
    const { axesData, assessments } = this.props;
    const graphPoints = [];
    axesData.forEach((element) => {
      const isFound = assessments.some((obj) => {
        if (obj.day === element) {
          graphPoints.push({ x: obj.day, y: obj.average_of_assessment_levels });
          return true;
        }
        return false;
      });

      if (!isFound) {
        graphPoints.push({ x: element, y: 0 });
      }
    });
    return graphPoints;
  };

  renderGraph() {
    return (
      <View>
        <View style={styles.midSection}>
          <VictoryChart
            height={curvedScale(160)}
            width={this.props.width}
            domainPadding={5}
            padding={{
              top: curvedScale(30),
              left: curvedScale(20),
              right: curvedScale(20),
              bottom: curvedScale(10),
            }}
          >
            <VictoryAxis
              orientation="top"
              key={0}
              style={{
                axis: { stroke: '' },
                tickLabels: {
                  fill: colors.subGrey,
                  fontSize: curvedScale(this.props.isMonth ? 8 : 11),
                  fontFamily: 'Avenir-Medium',
                  padding: curvedScale(7),
                },
                grid: { stroke: colors.lineLightBlue },
              }}
              tickValues={this.props.axesData}
            />
            <VictoryAxis
              key={1}
              dependentAxis
              style={{
                grid: {
                  stroke: colors.subGrey,
                  strokeWidth: 0,
                },
                axis: {
                  strokeWidth: 0,
                },
                tickLabels: {
                  fill: colors.white,
                  fontSize: curvedScale(11),
                  fontFamily: 'Avenir-Medium',
                },
              }}
              tickValues={[1, 2, 3, 4, 5]}
            />
            {this.renderGraphLine(this.getGraphPoints())}
            {this.renderGraphPointsWithMap(this.getGraphPoints())}
          </VictoryChart>
        </View>
      </View>
    );
  }

  renderGraphLine = (map) => {
    if (!map || !map.length) {
      return null;
    }
    return (
      <VictoryLine
        style={{
          data: {
            stroke: colors.nasmBlue,
            strokeWidth: 2,
          },
        }}
        data={map}
      />
    );
  };

  renderGraphPointsWithMap = (map) => {
    if (!map) {
      return null;
    }
    return (
      <VictoryScatter
        style={{ data: { fill: colors.nasmBlue } }}
        size={4}
        symbol="diamond"
        data={map}
      />
    );
  };

  render() {
    let view = null;
    if (this.props.loading) {
      view = this.renderGraph();
    } else {
      view = this.renderGraph();
    }
    return <View style={styles.cardStyle}>{view}</View>;
  }
}

const styles = {
  cardStyle: {
    marginBottom: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  midSection: {
    paddingTop: curvedScale(8),
    backgroundColor: colors.white,
    borderBottomLeftRadius: curvedScale(5),
    borderBottomRightRadius: curvedScale(5),
  },
  emptyStateView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: scaleWidth(5),
  },
};

DailyReadinessGraphSection.propTypes = propTypes;
DailyReadinessGraphSection.defaultProps = defaultProps;

export default DailyReadinessGraphSection;
