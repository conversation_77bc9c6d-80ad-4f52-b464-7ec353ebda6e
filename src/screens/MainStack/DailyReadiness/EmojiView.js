import React from 'react';
import {
  View, Text, StyleSheet, TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import { curvedScale } from '../../../util/responsive';
import { colors } from '../../../styles';

const emojis = [
  { value: 1, color: colors.pink },
  { value: 2, color: colors.brickOrange },
  { value: 3, color: colors.lightYellow },
  { value: 4, color: colors.mildGreen },
  { value: 5, color: colors.midGreen },
];

const EmojiView = (props) => {
  const {
    selectedValue,
    onPress,
    viewStyle,
    disabled = false,
    customEmojiStyle: customRatingContainer,
  } = props;
  return (
    <View style={[styles.ratingRow, viewStyle]}>
      {emojis.map((element) => {
        const opacity = element.value === selectedValue || disabled ? 1 : 0.5;
        const customWidth = customRatingContainer?.width ?? 35;
        return (
          <TouchableOpacity
            style={styles.ratingContainer}
            disabled={disabled}
            activeOpacity={0.6}
            key={element.value}
            onPress={() => onPress(element.value)}
          >
            <View
              style={[
                styles.ratingView,
                {
                  opacity,
                  backgroundColor: element.color,
                  ...customRatingContainer,
                  borderRadius: curvedScale(customWidth / 2),
                },
              ]}
            >
              <Text style={styles.ratingLabel}>{element.value}</Text>
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

// PropTypes
const propTypes = {
  onPress: PropTypes.func,
  disabled: PropTypes.bool,
  selectedValue: PropTypes.number.isRequired,
  viewStyle: PropTypes.object,
  customEmojiStyle: PropTypes.object,
};

const defaultProps = {
  disabled: false,
  onPress: null,
  viewStyle: null,
  customEmojiStyle: null,
};

// Export
EmojiView.propTypes = propTypes;
EmojiView.defaultProps = defaultProps;
export default EmojiView;

const styles = StyleSheet.create({
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingContainer: {
    marginHorizontal: curvedScale(12),
  },
  ratingView: {
    justifyContent: 'center',
    alignItems: 'center',
    width: curvedScale(35),
    height: curvedScale(35),
    borderRadius: curvedScale(35 / 2),
  },
  ratingLabel: {
    color: colors.black,
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Heavy',
    textAlign: 'center',
  },
});
