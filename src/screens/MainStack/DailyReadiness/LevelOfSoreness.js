import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Image,
  Alert,
  Text,
  Animated,
  Platform,
  TextInput,
  UIManager,
  StatusBar,
  StyleSheet,
  SafeAreaView,
  LayoutAnimation,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import PropTypes from 'prop-types';
import Body from '../../../components/react-native-body-highlighter';
import nasm from '../../../dataManager/apiConfig';
import FeedbackSubmitted from './FeedbackSubmitted';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';

const closeImg = require('../../../assets/closeCircle.png');
const flipFrontImg = require('../../../assets/flip_front.png');
const flipBackImg = require('../../../assets/flip_back.png');
const zoomInImg = require('../../../assets/zoom_in.png');
const zoomOutImg = require('../../../assets/zoom_out.png');
const imgReset = require('../../../assets/reset.png');

const excludedMuscles = ['head'];

const propTypes = {
  navigation: PropTypes.shape({
    setOptions: PropTypes.func,
    goBack: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      jsonData: PropTypes.object,
      mode: PropTypes.string,
    }),
  }).isRequired,
};

const defaultProps = {};

const initialState = {
  sore_notes: '',
  muscleData: [],
  backOnly: false,
  isZoomed: false,
  scale: 2.0,
};

const LevelOfSoreness = ({ navigation, route }) => {
  const jsonData = route?.params?.jsonData;
  if (route.params && route.params.mode === 'edit') {
    initialState.muscleData = jsonData.sore_body_parts.map((muscleObj) => ({
      slug: muscleObj.body_part.name,
      intensity: muscleObj.soreness_level,
    }));
    initialState.sore_notes = jsonData.sore_notes;
  }
  const flipAnimation = useRef(new Animated.Value(0)).current;
  const [state, setState] = useState(initialState);
  const [loading, setLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [bodyPartsArr, setBodyPartsArr] = useState([]);

  useEffect(() => {
    StatusBar.setBarStyle('dark-content');
    setTimeout(() => {
      setAnimation();
      setNavigationHeader();
    }, 500);
    return () => {
      StatusBar.setBarStyle('light-content');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    async function getBodyParts() {
      try {
        const response = await nasm.api.getBodyParts();
        setBodyPartsArr(response);
      } catch (error) {
        Alert.alert(
          'Error',
          error?.message
            || error
            || 'Something went wrong! Please try again later.',
        );
      }
    }
    getBodyParts();
  }, []);

  const setNavigationHeader = () => {
    navigation.setOptions({
      headerShown: false,
    });
  };

  const setAnimation = () => {
    if (Platform.OS === 'android') {
      if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
      }
    }
    LayoutAnimation.configureNext({
      duration: 500,
      create: { type: 'linear', property: 'opacity' },
      update: { type: 'spring', springDamping: 0.5 },
    });
  };

  const goBack = () => {
    navigation.goBack();
  };

  const flipToFrontStyle = {
    transform: [
      {
        rotateY: flipAnimation.interpolate({
          inputRange: [0, 180],
          outputRange: ['0deg', '180deg'],
        }),
      },
    ],
  };
  const flipToBackStyle = {
    transform: [
      {
        rotateY: flipAnimation.interpolate({
          inputRange: [0, 180],
          outputRange: ['180deg', '360deg'],
        }),
      },
    ],
  };

  const flipToFront = () => {
    Animated.timing(flipAnimation, {
      toValue: 180,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const flipToBack = () => {
    Animated.timing(flipAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const handleMuscleTap = (tappedMuscle) => {
    if (excludedMuscles.indexOf(tappedMuscle.slug) === -1) {
      const muscleData = [...state.muscleData];
      const muscleIndex = muscleData.findIndex(
        (muscleObj) => muscleObj.slug === tappedMuscle.slug,
      );
      if (muscleIndex > -1) {
        const muscleObj = muscleData[muscleIndex];
        if (muscleObj.intensity < 5) {
          muscleData[muscleIndex].intensity += 1;
        } else {
          muscleData.splice(muscleIndex, 1);
        }
      } else {
        muscleData.push({
          slug: tappedMuscle.slug,
          intensity: 1,
        });
      }
      setState({
        ...state,
        muscleData,
      });
    }
  };

  const resetSelectedMuscles = () => {
    setState({
      ...state,
      muscleData: [],
    });
  };

  const saveReadinessFeedback = async () => {
    try {
      setLoading(true);
      let avgSorenessLevel = 0;
      let totalSoreness = 0;
      let sore_body_parts = [];
      const muscleData = [...state.muscleData];
      if (muscleData.length) {
        sore_body_parts = muscleData
          .map((muscle) => {
            totalSoreness += muscle.intensity;
            const bodyPart = bodyPartsArr.find(
              (part) => part.name === muscle.slug,
            );
            return {
              body_part_id: bodyPart?.id || null,
              soreness_level: muscle.intensity,
            };
          })
          .filter((bodyPart) => bodyPart.body_part_id !== null);
        avgSorenessLevel = Math.round(totalSoreness / muscleData.length);
      }
      const soreJsonData = {
        ...jsonData,
        sore_notes: state.sore_notes,
        overall_soreness_level: avgSorenessLevel || 0,
        sore_body_parts,
      };
      if (route.params.mode === 'edit') {
        await nasm.api.updateReadinessFeedback(soreJsonData, jsonData.id);
        goBack();
      } else {
        await nasm.api.saveReadinessFeedback(soreJsonData);
        setShowSuccessModal(true);
      }
    } catch (error) {
      if (error.code === 9023) {
        // error letting user know they aren't allowed to update a daily readiness assessment in the past
        showAlert('Error', error.message);
      } else {
        showAlert('Error', 'Something went wrong! Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  const showAlert = (title, message) => {
    Alert.alert(
      title,
      message,
      [
        {
          text: 'Okay',
          onPress: goBack,
        },
      ],
      { isCancellable: false },
    );
  };

  const renderCloseImg = () => (
    <TouchableOpacity
      activeOpacity={0.6}
      hitSlop={{
        top: 20,
        bottom: 20,
        left: 20,
        right: 20,
      }}
      onPress={goBack}
      style={styles.closeImgView}
    >
      <Image source={closeImg} />
    </TouchableOpacity>
  );

  const renderResetButton = () => (
    <View style={styles.resetMusclesView}>
      <Text style={styles.selectedMusclesLabel}>
        {state.muscleData?.length}
        {' '}
        Selected
      </Text>
      <TouchableOpacity
        activeOpacity={0.6}
        style={styles.btnReset}
        onPress={resetSelectedMuscles}
      >
        <Image source={imgReset} />
      </TouchableOpacity>
    </View>
  );

  const renderSorenessLevels = (color, text) => (
    <View style={styles.sorenessLevel}>
      <View style={styles.sorenessColor(color)} />
      <Text style={styles.sorenessType}>{text}</Text>
    </View>
  );

  const renderSorenessInfo = () => (
    <View style={styles.sorenessInfo}>
      <View style={styles.buttonsContainer}>
        {renderFlipButton()}
        {renderZoomInButton()}
      </View>
      <Text style={styles.sorenessDescription}>
        Tap each muscle or joint (up to five times)
      </Text>
      <View style={styles.sorenessLevelsContainer}>
        {renderSorenessLevels(colors.pink, 'Extreme')}
        {renderSorenessLevels(colors.brickOrange, 'Very')}
        {renderSorenessLevels(colors.orange, 'Moderately')}
        {renderSorenessLevels(colors.mildGreen, 'Mildly')}
        {renderSorenessLevels(colors.midGreen, 'Not Sore')}
      </View>
    </View>
  );

  const renderBodyHighlighter = () => (
    <View>
      <Text style={styles.sideLabel1}>{state.backOnly ? 'Left' : 'Right'}</Text>
      <Animated.View
        style={[
          styles.bodyContainer,
          !state.backOnly ? { ...flipToBackStyle } : { ...flipToFrontStyle },
        ]}
      >
        <Body
          data={state.muscleData}
          frontOnly={!state.backOnly}
          backOnly={state.backOnly}
          scale={curvedScale(state.scale)}
          colors={[
            colors.midGreen,
            colors.mildGreen,
            colors.orange,
            colors.brickOrange,
            colors.pink,
          ]}
          onMusclePress={handleMuscleTap}
        />
      </Animated.View>
      <Text style={styles.sideLabel2}>{state.backOnly ? 'Right' : 'Left'}</Text>
    </View>
  );

  const renderFlipButton = () => (
    <TouchableOpacity
      style={styles.btnContainerView}
      activeOpacity={0.6}
      onPress={() => {
        if (state.backOnly) {
          flipToBack();
        } else {
          flipToFront();
        }
        setState({
          ...state,
          backOnly: !state.backOnly,
        });
      }}
    >
      <Animated.Image
        style={styles.btnView}
        source={state.backOnly ? flipBackImg : flipFrontImg}
      />
      <Text style={styles.btnText}>{state.backOnly ? 'Front' : 'Back'}</Text>
    </TouchableOpacity>
  );

  const renderZoomInButton = () => (
    <TouchableOpacity
      style={styles.btnContainerView}
      activeOpacity={0.6}
      onPress={() => {
        setState({
          ...state,
          isZoomed: true,
          scale: 3.5,
        });
        setAnimation();
      }}
    >
      <Image style={styles.btnView} source={zoomInImg} />
      <Text style={styles.btnText}>100%</Text>
    </TouchableOpacity>
  );

  const renderZoomOutButton = () => (
    <TouchableOpacity
      style={styles.btnContainerView}
      activeOpacity={0.6}
      onPress={() => {
        setState({
          ...state,
          isZoomed: false,
          scale: 2.0,
        });
        setAnimation();
      }}
    >
      <Image style={styles.btnView} source={zoomOutImg} />
      <Text style={styles.btnText}>50%</Text>
    </TouchableOpacity>
  );

  const renderPageContent = () => (
    <View style={styles.pageContent}>
      <View style={styles.headerButtonsContainer}>
        {state.isZoomed ? renderZoomOutButton() : renderCloseImg()}
        {state.muscleData?.length ? renderResetButton() : null}
      </View>
      {renderBodyHighlighter()}
      {!state.isZoomed ? renderSorenessInfo() : null}
    </View>
  );

  const renderNotesInput = () => (
    <View style={styles.notesView}>
      <TextInput
        style={styles.notesInput}
        placeholder="Notes"
        value={state.sore_notes}
        onChangeText={(newNote) => {
          setState({
            ...state,
            sore_notes: newNote,
          });
        }}
      />
    </View>
  );

  const renderBottomLayout = () => (
    <View style={styles.bottomLayuot}>{renderNotesInput()}</View>
  );

  const renderFinishButton = () => {
    if (loading) {
      return (
        <View style={styles.loaderView}>
          <ActivityIndicator animating size="large" color={colors.subGrey} />
        </View>
      );
    }
    return (
      <View style={styles.bottomLayuot}>
        <TouchableOpacity
          activeOpacity={0.6}
          style={styles.finishedBtn}
          onPress={saveReadinessFeedback}
        >
          <Text style={styles.finishedBtnLabel}>
            {route.params.mode === 'edit' ? 'Save Changes' : 'Finish'}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderSuccessModal = () => (
    <Modal animationType="slide" transparent visible={showSuccessModal}>
      {renderSubmittedView()}
    </Modal>
  );

  const renderSubmittedView = () => (
    <View style={styles.bottomSheetContainer}>
      <View style={styles.bottomSheet}>
        <FeedbackSubmitted
          goBack={() => {
            setShowSuccessModal(false);
            goBack();
          }}
        />
      </View>
    </View>
  );

  return (
    <KeyboardAwareScrollView
      extraScrollHeight={20}
      keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
      contentContainerStyle={styles.container}
      bounces={false}
    >
      {renderPageContent()}
      {!state.isZoomed ? renderBottomLayout() : null}
      {!state.isZoomed ? renderFinishButton() : null}
      {renderSuccessModal()}
      <SafeAreaView />
    </KeyboardAwareScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
  },
  pageContent: {
    backgroundColor: colors.white,
  },
  headerButtonsContainer: {
    marginTop: curvedScale(50),
    flexDirection: 'row',
    alignItems: 'center',
  },
  closeImgView: {
    marginHorizontal: curvedScale(20),
    width: curvedScale(30),
    height: curvedScale(30),
    flex: 3,
  },
  resetMusclesView: {
    justifyContent: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
    flex: 7,
  },
  selectedMusclesLabel: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(14),
    textAlign: 'center',
  },
  btnReset: {
    borderWidth: 1,
    borderColor: colors.fillDarkGrey,
    borderRadius: 20,
    marginHorizontal: curvedScale(20),
    width: curvedScale(30),
    height: curvedScale(30),
    alignItems: 'center',
    justifyContent: 'center',
  },
  sorenessInfo: {
    paddingVertical: curvedScale(20),
    margin: curvedScale(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  sorenessDescription: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(16),
    textAlign: 'center',
    marginTop: curvedScale(10),
  },
  sorenessLevelsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    justifyContent: 'space-around',
    margin: curvedScale(10),
  },
  sorenessLevel: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: curvedScale(10),
  },
  sorenessColor: (backgroundColor) => ({
    width: 8,
    height: 8,
    borderRadius: 8 / 2,
    backgroundColor,
  }),
  sorenessType: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(12),
    textAlign: 'center',
    marginLeft: curvedScale(5),
  },
  bodyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  btnContainerView: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: curvedScale(20),
    borderWidth: 1,
    borderColor: colors.disclaimerGrey,
    paddingVertical: curvedScale(7.5),
    paddingHorizontal: curvedScale(15),
    marginLeft: curvedScale(20),
  },
  btnView: {
    width: curvedScale(25),
    height: curvedScale(25),
  },
  btnText: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(15),
    fontWeight: '700',
    marginLeft: curvedScale(10),
  },
  bottomLayuot: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: curvedScale(20),
  },
  notesView: {
    borderRadius: 20,
    backgroundColor: colors.background,
    width: '100%',
  },
  notesInput: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(15),
    fontWeight: '500',
    paddingVertical: curvedScale(10),
    paddingHorizontal: curvedScale(20),
  },
  finishedBtn: {
    backgroundColor: colors.blue,
    borderRadius: curvedScale(20),
    paddingVertical: curvedScale(10),
    paddingHorizontal: curvedScale(50),
    marginVertical: curvedScale(20),
    marginHorizontal: curvedScale(20),
  },
  finishedBtnLabel: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(15),
    fontWeight: 'bold',
    textAlign: 'center',
  },
  loaderView: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: curvedScale(10),
  },
  bottomSheetContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: colors.pickerOverlayBg,
  },
  bottomSheet: {
    backgroundColor: colors.veryLightBlue,
    borderTopRightRadius: curvedScale(17),
    borderTopLeftRadius: curvedScale(17),
    paddingHorizontal: curvedScale(25),
  },
  sideLabel1: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(15),
    fontWeight: '700',
    position: 'absolute',
    left: 30,
    top: 50,
  },
  sideLabel2: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(15),
    fontWeight: '700',
    position: 'absolute',
    right: 30,
    top: 50,
  },
});

LevelOfSoreness.propTypes = propTypes;
LevelOfSoreness.defaultProps = defaultProps;

export default LevelOfSoreness;
