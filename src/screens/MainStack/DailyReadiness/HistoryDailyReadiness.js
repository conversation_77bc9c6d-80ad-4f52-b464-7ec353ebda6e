import moment from 'moment';
import React, { Component } from 'react';
import {
  FlatList,
  Image,
  Text,
  RefreshControl,
  TouchableOpacity,
  View,
  Alert,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  Dimensions,
  ScrollView,
} from 'react-native';
import PropTypes from 'prop-types';
import DeviceInfo from 'react-native-device-info';
import {
  VictoryLine,
  VictoryScatter,
  VictoryAxis,
  VictoryChart,
} from 'victory-native';
import { BottomBannerActionView, ScaledText } from '../../../components';
import nasm from '../../../dataManager/apiConfig';
import { colors } from '../../../styles';
import { curvedScale, scaleHeight } from '../../../util/responsive';

const completedImage = require('../../../resources/completed.png');
const incompleteImage = require('../../../resources/checkmarkIncomplete.png');
const rightArrow = require('../../../resources/rightArrow.png');

const periods = {
  WEEK: 'WEEKLY',
  MONTH: 'MONTHLY',
  YEAR: 'YEARLY',
};

const initialGraphData = {
  energy: [],
  mood: [],
  stress: [],
  soreness: [],
  sleep: [],
};

const propTypes = {
  navigation: PropTypes.shape({
    addListener: PropTypes.func,
    navigate: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.shape({
      userIdOfClient: PropTypes.string,
    }),
  }).isRequired,
};

const defaultProps = {};

class HistoryDailyReadiness extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      refreshing: false,
      page: 1,
      hasNextPage: false,
      currentDate: moment(),
      graphData: initialGraphData,
      readinessData: [],
      selectedReadiness: [],
      period: periods.WEEK,
    };
    this.scrollViewRef = null;
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.refreshGraphAndList();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  getReadinessHistory = async () => {
    const {
      period, currentDate, page = 1, readinessData,
    } = this.state;
    const userId = this.props?.route?.params?.userIdOfClient;
    try {
      const response = await nasm.api.getDailyHistoryAssessment(
        currentDate.format(),
        userId,
        period,
        page,
        20,
      );
      this.setState({
        readinessData: page === 1 ? response : [...readinessData, ...response],
        isLoading: false,
        hasNextPage: response && response.length > 0,
        page: page + 1,
      });
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  getReadinessGraphHistory = async () => {
    const { period, currentDate } = this.state;
    const userId = this.props?.route?.params?.userIdOfClient;
    try {
      const response = await nasm.api.getDailyHistoryGraphAssessment(
        currentDate.format(),
        userId,
        period,
      );
      if (response.length) {
        this.setGraphData(response);
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  setGraphData = (response) => {
    const energyData = [];
    const moodData = [];
    const stressData = [];
    const sleepData = [];
    const sorenessData = [];
    response.forEach((historyObj) => {
      const {
        created_at,
        month,
        energy_level,
        mood_level,
        stress_level,
        sleep_level,
        overall_soreness_level,
      } = historyObj;
      const dateNumber = moment(created_at).format('D');
      let xAxisVal = moment(created_at)
        .format('ddd')
        .toUpperCase()
        .concat(`\n${dateNumber}`);
      if (this.state.period === periods.MONTH) {
        xAxisVal = moment(created_at).format('D');
      } else if (this.state.period === periods.YEAR) {
        xAxisVal = month;
      }

      // Energy
      const energyObj = {
        x: xAxisVal,
        y: Number(energy_level),
      };
      if (this.state.period === periods.YEAR) {
        energyData.push(energyObj);
      } else {
        const getIndex = energyData.findIndex((ele) => ele.x === xAxisVal);
        if (getIndex === -1) {
          energyData.push(energyObj);
        }
      }

      // Mood
      const moodObj = {
        x: xAxisVal,
        y: Number(mood_level),
      };
      if (this.state.period === periods.YEAR) {
        moodData.push(moodObj);
      } else {
        const getIndex = moodData.findIndex((ele) => ele.x === xAxisVal);
        if (getIndex === -1) {
          moodData.push(moodObj);
        }
      }

      // Stress
      const stressObj = {
        x: xAxisVal,
        y: Number(stress_level),
      };
      if (this.state.period === periods.YEAR) {
        stressData.push(stressObj);
      } else {
        const getIndex = stressData.findIndex((ele) => ele.x === xAxisVal);
        if (getIndex === -1) {
          stressData.push(stressObj);
        }
      }

      // Sleep
      const sleepObj = {
        x: xAxisVal,
        y: Number(sleep_level),
      };
      if (this.state.period === periods.YEAR) {
        sleepData.push(sleepObj);
      } else {
        const getIndex = sleepData.findIndex((ele) => ele.x === xAxisVal);
        if (getIndex === -1) {
          sleepData.push(sleepObj);
        }
      }

      // Soreness
      const sorenessObj = {
        x: xAxisVal,
        y: Number(overall_soreness_level),
      };
      if (this.state.period === periods.YEAR) {
        sorenessData.push(sorenessObj);
      } else {
        const getIndex = sorenessData.findIndex((ele) => ele.x === xAxisVal);
        if (getIndex === -1) {
          sorenessData.push(sorenessObj);
        }
      }
    });
    const graphHistoryData = {
      energy: energyData,
      mood: moodData,
      stress: stressData,
      soreness: sorenessData,
      sleep: sleepData,
    };
    this.setState({ graphData: graphHistoryData });
  };

  refreshGraphAndList = (
    currentDate = moment(),
    period = this.state.period,
  ) => {
    this.setState(
      {
        isLoading: true,
        page: 1,
        currentDate,
        graphData: [],
        readinessData: [],
        selectedReadiness: [],
        period,
      },
      () => {
        this.getReadinessHistoryData();
      },
    );
  };

  onScroll = () => {
    if (this.state.hasNextPage && !this.state.isLoading) {
      this.setState({ isLoading: true }, () => {
        this.getReadinessHistory();
      });
    }
  };

  getReadinessHistoryData = async () => {
    try {
      await this.getReadinessHistory();
      await this.getReadinessGraphHistory();
    } catch (error) {
      Alert.alert(
        'Error',
        error.message
          || 'Unable to refresh daily readiness history. Please try again later.',
      );
    } finally {
      this.setState({ refreshing: false });
    }
  };

  getXAxisData = () => {
    const axesData = [];
    const weekStart = this.state.currentDate.startOf('week');
    switch (this.state.period) {
      case periods.YEAR:
        for (let i = 1; i < 12 + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      case periods.MONTH:
        for (let i = 1; i < this.state.currentDate.daysInMonth() + 1; i += 1) {
          axesData.push(i.toString());
        }
        break;
      default:
        for (let i = 0; i < 7; i += 1) {
          const date = moment(weekStart).add(i, 'days');
          const dayLabel = moment(date).format('ddd');
          const dateNumber = moment(date).format('D');
          const label = `${dayLabel.toUpperCase()}\n${dateNumber}`;
          axesData.push(label);
        }
        break;
    }
    return axesData;
  };

  getCalendarHeaderText = ({
    period,
    currentDate,
    dateFormat = 'D MMMM YYYY',
    yearDateFormat = 'MMMM YYYY',
  }) => {
    let start;
    let end;
    switch (period) {
      case periods.WEEK:
        start = currentDate.clone().startOf('week').format(dateFormat);
        end = currentDate.clone().endOf('week').format(dateFormat);
        break;
      case periods.MONTH:
        start = currentDate.clone().startOf('month').format(dateFormat);
        end = currentDate.clone().endOf('month').format(dateFormat);
        break;
      case periods.YEAR:
        start = currentDate.clone().startOf('year').format(yearDateFormat);
        end = currentDate.clone().endOf('year').format(yearDateFormat);
        break;
      default:
        throw new Error(`Invalid period: ${period}`);
    }
    return `${start} - ${end}`;
  };

  isCellSelected = (item) => {
    const { selectedReadiness } = this.state;
    for (let i = 0; i < selectedReadiness.length; i += 1) {
      const selectedId = selectedReadiness[i];
      if (item === selectedId) {
        return true;
      }
    }
    return false;
  };

  isAssessmentSelected = (assessment) => {
    const { selectedReadiness } = this.state;
    for (let i = 0; i < selectedReadiness.length; i += 1) {
      const selectedReadiness1 = selectedReadiness[i];
      if (selectedReadiness1.created_at === assessment.created_at) {
        return true;
      }
    }
    return false;
  };

  readinessCellSelected = (assessment) => {
    const { selectedReadiness } = this.state;
    if (this.isAssessmentSelected(assessment)) {
      this.removeAssessmentFromSelected(assessment);
    } else {
      if (selectedReadiness.length > 1) return;
      selectedReadiness.push(assessment);
      this.setState({ selectedReadiness }, () => {
        setTimeout(() => {
          this.flatList.scrollToItem({ item: assessment });
        }, 100);
      });
    }
  };

  clearSelectedAssessments = () => {
    this.setState({ selectedReadiness: [] });
  };

  removeAssessmentFromSelected = (assessment) => {
    const { selectedReadiness } = this.state;
    let index = -1;
    for (let i = 0; i < selectedReadiness.length; i += 1) {
      const selectedReadiness1 = selectedReadiness[i];
      if (selectedReadiness1.created_at === assessment.created_at) {
        index = i;
        break;
      }
    }
    if (index !== -1) {
      selectedReadiness.splice(index, 1);
      this.setState({ selectedReadiness });
    }
  };

  normalizeData = (data) => {
    const normalized = [];
    let max = -1;
    data.forEach((element) => {
      if (max === -1) {
        max = element.y;
      }
      if (element.y > max) {
        max = element.y;
      }
    });
    data.forEach((element) => {
      if (max !== 0) {
        normalized.push({ x: element.x, y: element.y / max + 0.1 });
      }
    });
    return normalized;
  };

  leftArrowClicked = () => {
    const { currentDate } = this.state;
    const newDate = currentDate.clone();
    const dateFormat = 'D MMMM YYYY';
    if (this.state.period === periods.YEAR) {
      newDate.subtract(1, 'years');
    } else if (this.state.period === periods.MONTH) {
      newDate.subtract(1, 'months');
    } else {
      newDate.subtract(7, 'days').format(dateFormat);
    }
    this.refreshGraphAndList(newDate);
  };

  rightArrowClicked = () => {
    const { currentDate } = this.state;
    const newDate = currentDate.clone();
    const dateFormat = 'D MMMM YYYY';
    if (this.state.period === periods.YEAR) {
      newDate.add(1, 'years');
    } else if (this.state.period === periods.MONTH) {
      newDate.add(1, 'months');
    } else {
      newDate.add(7, 'days').format(dateFormat);
    }
    this.refreshGraphAndList(newDate);
  };

  renderGraph = () => {
    const { graphData, period } = this.state;
    const xAxisData = this.getXAxisData();
    let width = Dimensions.get('window').width / 1.1;
    if (period === periods.MONTH) {
      width = Dimensions.get('window').width * 1.5;
    }
    return (
      <VictoryChart
        height={curvedScale(200)}
        width={width}
        domainPadding={10}
        padding={{
          top: curvedScale(30),
          left: curvedScale(20),
          right: curvedScale(20),
          bottom: curvedScale(10),
        }}
      >
        <VictoryAxis
          key={0}
          style={{
            axis: { stroke: '' },
            grid: { stroke: colors.silver },
            tickLabels: {
              fill: colors.subGrey,
              fontSize: curvedScale(11),
              fontFamily: 'Avenir-Medium',
              padding: curvedScale(5),
            },
          }}
          orientation="top"
          tickValues={xAxisData}
        />
        <VictoryAxis
          key={1}
          dependentAxis
          style={{
            grid: {
              stroke: colors.subGrey,
              strokeWidth: 0,
            },
            axis: {
              strokeWidth: 0,
            },
            tickLabels: {
              fill: colors.subGrey,
              fontSize: curvedScale(11),
              fontFamily: 'Avenir-Medium',
              padding: curvedScale(10),
            },
          }}
          tickValues={[1, 2, 3, 4, 5]}
        />
        {this.renderGraphLine(graphData.energy, colors.pinkishPurple)}
        {this.renderGraphPoints(
          graphData.energy,
          colors.pinkishPurple,
          'circle',
        )}

        {this.renderGraphLine(graphData.mood, colors.linkBlue)}
        {this.renderGraphPoints(graphData.mood, colors.linkBlue, 'square')}

        {this.renderGraphLine(graphData.stress, colors.black)}
        {this.renderGraphPoints(graphData.stress, colors.black, 'diamond')}

        {this.renderGraphLine(graphData.soreness, colors.nasmRed)}
        {this.renderGraphPoints(
          graphData.soreness,
          colors.nasmRed,
          'triangleUp',
        )}

        {this.renderGraphLine(graphData.sleep, colors.medYellow)}
        {this.renderGraphPoints(
          graphData.sleep,
          colors.medYellow,
          'triangleDown',
        )}
      </VictoryChart>
    );
  };

  renderGraphHeader = () => {
    const headerText = this.getCalendarHeaderText({
      period: this.state.period,
      currentDate: this.state.currentDate,
    });
    return (
      <View style={styles.graphHeaderSection}>
        <TouchableOpacity onPress={this.leftArrowClicked}>
          <Image
            style={[styles.imageStyle, { transform: [{ rotate: '180deg' }] }]}
            source={rightArrow}
          />
        </TouchableOpacity>
        <ScaledText style={styles.graphHeaderText}>{headerText}</ScaledText>
        <TouchableOpacity onPress={this.rightArrowClicked}>
          <Image style={styles.imageStyle} source={rightArrow} />
        </TouchableOpacity>
      </View>
    );
  };

  renderGraphLegend = () => {
    const underTextStyle = [
      styles.underText,
      { fontSize: 16, paddingLeft: 10 },
    ];
    const legendDotStyle = {
      width: curvedScale(7),
      height: curvedScale(7),
      borderRadius: curvedScale(3.5),
    };
    const legendSquareStyle = { width: curvedScale(7), height: curvedScale(7) };
    return (
      <View style={styles.graphViewStyle}>
        <View style={styles.dotAndLabel}>
          <View
            style={[legendDotStyle, { backgroundColor: colors.pinkishPurple }]}
          />
          <ScaledText style={underTextStyle}>Energy</ScaledText>
        </View>
        <View style={styles.dotAndLabel}>
          <View
            style={[legendSquareStyle, { backgroundColor: colors.linkBlue }]}
          />
          <ScaledText style={underTextStyle}>Mood</ScaledText>
        </View>
        <View style={styles.dotAndLabel}>
          <View
            style={[
              legendSquareStyle,
              {
                backgroundColor: colors.black,
                transform: [{ rotate: '45deg' }],
              },
            ]}
          />
          <ScaledText style={underTextStyle}>Stress</ScaledText>
        </View>
        <View style={styles.dotAndLabel}>
          <View
            style={[
              styles.triangle,
              {
                borderLeftWidth: curvedScale(4),
                borderRightWidth: curvedScale(4),
                borderBottomWidth: curvedScale(8),
              },
            ]}
          />
          <ScaledText style={underTextStyle}>Soreness</ScaledText>
        </View>
        <View style={styles.dotAndLabel}>
          <View
            style={[
              styles.triangle,
              {
                borderLeftWidth: curvedScale(4),
                borderRightWidth: curvedScale(4),
                borderTopWidth: curvedScale(8),
                borderTopColor: colors.medYellow,
              },
            ]}
          />
          <ScaledText style={underTextStyle}>Sleep</ScaledText>
        </View>
      </View>
    );
  };

  renderGraphLine = (data, color) => {
    if (data && data.length > 1) {
      // need at least 2 points for a line
      return (
        <VictoryLine
          style={{
            data: { stroke: color, strokeWidth: 2 },
          }}
          data={data}
          interpolation="catmullRom"
        />
      );
    }
    return null;
  };

  renderGraphPoints = (data, color, symbol) => {
    if (data) {
      return (
        <VictoryScatter
          style={{ data: { fill: color } }}
          size={curvedScale(4)}
          symbol={symbol}
          data={data}
        />
      );
    }
    return null;
  };

  confirmDelete = () => {
    Alert.alert(
      'Delete Assessment',
      'Are you sure you would like to delete this assessment?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: null,
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: null,
        },
      ],
      { cancelable: true },
    );
  };

  renderCell = ({ item }) => {
    const legendDotStyle = { ...styles.legendStyle };
    const legendSquareStyle = { ...styles.squareStyle };
    const stressStyle = {
      ...legendSquareStyle,
      ...styles.stressStyle,
    };
    const sorenessStyle = {
      ...styles.triangle,
      ...styles.soreStyle,
    };
    const sleepStyle = {
      ...styles.triangle,
      borderLeftWidth: curvedScale(4),
      borderRightWidth: curvedScale(4),
      borderTopWidth: curvedScale(8),
      borderTopColor: colors.medYellow,
    };
    const selected = this.isCellSelected(item)
      ? completedImage
      : incompleteImage;
    return (
      <View>
        <TouchableOpacity
          style={styles.itemCell}
          activeOpacity={0.6}
          onPress={() => this.readinessCellSelected(item)}
        >
          <View style={styles.textSection}>
            <ScaledText style={styles.date}>
              {moment(item.created_at).format('M/D/YY')}
            </ScaledText>
            {this.renderReadinessValue(
              legendDotStyle,
              colors.pinkishPurple,
              item.energy_level,
              'energy',
            )}
            {this.renderReadinessValue(
              legendSquareStyle,
              colors.linkBlue,
              item.mood_level,
              'mood',
            )}
            {this.renderReadinessValue(
              stressStyle,
              colors.black,
              item.stress_level,
              'stress',
            )}
            {this.renderReadinessValue(
              sorenessStyle,
              '',
              item.overall_soreness_level,
              'sore',
            )}
            {this.renderReadinessValue(
              sleepStyle,
              '',
              item.sleep_level,
              'sleep',
            )}
            <TouchableOpacity onPress={() => this.readinessCellSelected(item)}>
              <Image style={styles.checkBox} source={selected} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  getRatingColor = (value) => {
    switch (value) {
      case 1:
        return colors.pink;
      case 2:
        return colors.brickOrange;
      case 3:
        return colors.lightYellow;
      case 4:
        return colors.mildGreen;
      case 5:
        return colors.midGreen;
      default:
        return null;
    }
  };

  renderReadinessValue = (colorViewStyle, backgroundColor, value, type) => {
    const ratingColor = this.getRatingColor(value);
    let rating = value;
    if (type === 'sore' && value === 0) {
      rating = '-';
    }
    return (
      <View style={styles.dotAndLabel1}>
        <View style={[colorViewStyle, { backgroundColor }]} />
        <View style={[styles.ratingView, { backgroundColor: ratingColor }]}>
          <Text style={styles.ratingLabel}>{rating}</Text>
        </View>
      </View>
    );
  };

  renderPeriodPicker = () => (
    <View style={styles.periodPickerContainer}>
      {this.renderPeriodView({ label: 'Week', period: periods.WEEK })}
      <View style={styles.separatorView} />
      {this.renderPeriodView({ label: 'Month', period: periods.MONTH })}
      <View style={styles.separatorView} />
      {this.renderPeriodView({ label: 'Year', period: periods.YEAR })}
    </View>
  );

  renderPeriodView = ({ label, period }) => (
    <>
      <TouchableOpacity
        activeOpacity={0.6}
        style={
          this.state.period === period
            ? styles.selectedContainer
            : styles.unselectedContainer
        }
        onPress={() => {
          if (period !== this.state.period) {
            if (this.scrollViewRef) {
              this.scrollViewRef?.scrollTo({ x: 0, y: 0, animated: true });
            }
            this.refreshGraphAndList(this.state.currentDate, period);
          }
        }}
      >
        <ScaledText
          style={
            this.state.period === period
              ? styles.selectedPeriod
              : styles.unselectedPeriod
          }
        >
          {label}
        </ScaledText>
      </TouchableOpacity>
    </>
  );

  render() {
    const {
      selectedReadiness,
      readinessData,
      refreshing,
      isLoading,
      period,
    } = this.state;
    const bottomTitle = `${selectedReadiness.length} Selected`;
    const isMonthView = period === periods.MONTH;
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          ref={(ref) => {
            this.flatList = ref;
          }}
          contentContainerStyle={styles.listContentContainer}
          ListHeaderComponent={(
            <View>
              {this.renderPeriodPicker()}
              {this.renderGraphHeader()}
              <ScrollView
                ref={(ref) => {
                  this.scrollViewRef = ref;
                }}
                horizontal
                contentContainerStyle={styles.graphSectionContainer(
                  isMonthView,
                )}
              >
                {this.renderGraph()}
              </ScrollView>
              {this.renderGraphLegend()}
            </View>
          )}
          ListFooterComponent={() => (
            <View style={styles.loaderView}>
              <ActivityIndicator size="large" animating={isLoading} />
            </View>
          )}
          ListEmptyComponent={() => (
            <View style={styles.loaderView}>
              {!isLoading && !refreshing ? (
                <View style={styles.emptyDataView}>
                  <ScaledText style={styles.emptyDataMessage}>
                    No history to show
                  </ScaledText>
                </View>
              ) : null}
            </View>
          )}
          data={readinessData}
          keyExtractor={(item) => item.id}
          renderItem={this.renderCell}
          refreshControl={(
            <RefreshControl
              refreshing={refreshing}
              onRefresh={this.refreshGraphAndList}
            />
          )}
          onEndReached={this.onScroll}
          onEndReachedThreshold={0.5}
        />
        {selectedReadiness.length > 0 && (
          <BottomBannerActionView
            title={bottomTitle}
            buttonTitle="Compare"
            buttonDisabled={selectedReadiness.length !== 2}
            onPress={() => {
              this.props.navigation.navigate({
                name: 'DailyReadinessComparison',
                params: {
                  selectedReadiness,
                },
              });
              this.clearSelectedAssessments();
            }}
          />
        )}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  periodPickerContainer: {
    margin: curvedScale(20),
    flexDirection: 'row',
    alignSelf: 'center',
    borderColor: colors.subGrey,
    borderWidth: 1,
    borderRadius: curvedScale(4),
    alignItems: 'center',
  },
  selectedContainer: {
    flex: 1,
    padding: curvedScale(5),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGrey,
  },
  unselectedContainer: {
    flex: 1,
    padding: curvedScale(5),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  selectedPeriod: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    alignSelf: 'center',
    fontSize: curvedScale(13),
  },
  unselectedPeriod: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    alignSelf: 'center',
    fontSize: curvedScale(13),
  },
  graphHeaderSection: {
    width: '100%',
    padding: curvedScale(15),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.silver51,
  },
  legendStyle: {
    width: curvedScale(5),
    height: curvedScale(5),
    borderRadius: curvedScale(2.5),
  },
  squareStyle: {
    width: curvedScale(5),
    height: curvedScale(5),
  },
  stressStyle: {
    transform: [{ rotate: '45deg' }],
  },
  soreStyle: {
    borderLeftWidth: curvedScale(4),
    borderRightWidth: curvedScale(4),
    borderBottomWidth: curvedScale(8),
  },
  imageStyle: {
    tintColor: colors.cloudyBlue,
  },
  graphHeaderText: {
    fontFamily: 'Avenir-Medium',
    color: colors.black,
    alignSelf: 'center',
  },
  graphSectionContainer: (isMonthView) => ({
    width: isMonthView ? '150%' : '100%',
    backgroundColor: colors.pickerBg,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: curvedScale(10),
    paddingLeft: isMonthView ? curvedScale(20) : 0,
  }),
  itemCell: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.paleGray2,
  },
  dotAndLabel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: curvedScale(9),
  },
  dotAndLabel1: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  graphViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    width: '100%',
    marginVertical: curvedScale(10),
  },
  textSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scaleHeight(2),
    height: scaleHeight(10),
  },
  date: {
    fontFamily: 'Avenir-Medium',
    fontSize: DeviceInfo.isTablet() ? curvedScale(10) : curvedScale(14),
    color: colors.black,
    minWidth: curvedScale(80),
  },
  underText: {
    fontFamily: 'Avenir-Medium',
    color: colors.black,
    paddingRight: curvedScale(4),
  },
  triangle: {
    width: curvedScale(2),
    height: curvedScale(2),
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderBottomColor: colors.nasmRed,
  },
  ratingView: {
    justifyContent: 'center',
    alignItems: 'center',
    width: curvedScale(20),
    height: curvedScale(20),
    borderRadius: curvedScale(20 / 2),
    padding: curvedScale(2),
    marginLeft: curvedScale(9),
  },
  ratingLabel: {
    color: colors.black,
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Heavy',
    textAlign: 'center',
  },
  separatorView: {
    width: 1,
    height: '100%',
    backgroundColor: colors.subGrey,
  },
  emptyDataView: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: curvedScale(20),
  },
  emptyDataMessage: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: DeviceInfo.isTablet() ? curvedScale(10) : curvedScale(14),
  },
  loaderView: {
    marginVertical: curvedScale(10),
  },
  checkBox: {
    height: curvedScale(20),
    width: curvedScale(20),
  },
});

HistoryDailyReadiness.propTypes = propTypes;
HistoryDailyReadiness.defaultProps = defaultProps;

export default HistoryDailyReadiness;
