import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  TouchableOpacity,
  FlatList,
  View,
  StyleSheet,
  Dimensions,
  Image,
  Text,
  Alert,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import PropTypes from 'prop-types';
import QuestionView from './QuestionView';
import IntroView from './IntroView';
import KeyboardHandler from '../../../components/KeyboardHandler';
import nasm from '../../../dataManager/apiConfig';
import FeedbackSubmitted from './FeedbackSubmitted';
import { track } from '../../../util/Analytics';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';
import {
  energy_level,
  mood_level,
  sleep_level,
  stress_level,
} from './DailyReadinessConstants';
import { logException } from '../../../util/logging';

const screenWidth = Dimensions.get('window').width;
const closeIcon = require('../../../assets/closeCircle.png');
const soreImg = require('../../../assets/sore.png');

const questionTypes = ['energy', 'mood', 'stress'];

const stateSelector = (state) => state;

const DailyReadiness = ({ navigation, route }) => {
  const flatListRef = useRef();
  const { currentUser } = useSelector(stateSelector);
  let selectedIndex = 0;
  if (route.params && route.params.mode === 'edit') {
    selectedIndex = route?.params?.selectedIndex;
  }
  const [activeIndex, setActiveIndex] = useState(selectedIndex || 0);
  const currentAssessmentData = route.params && route.params.currentAssessmentData
    ? route.params.currentAssessmentData
    : {};

  const initialState = {
    energy_level: null,
    energy_level_notes: '',
    mood_level: null,
    mood_level_notes: '',
    stress_level: null,
    stress_level_notes: '',
    sleep_level: null,
    sleep_level_notes: '',
  };
  if (route.params && route.params.mode === 'edit') {
    Object.keys(currentAssessmentData).forEach((ele) => {
      if (ele in initialState) {
        initialState[ele] = currentAssessmentData[ele];
      }
    });
  }
  const [state, setState] = useState(initialState);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    nasm.api.updateReadinessFeedbackTime().catch((err) => logException(err));
  }, []);

  const changeActiveScreen = (index) => {
    setActiveIndex(index);
    flatListRef?.current?.scrollToIndex({ animated: true, index });
  };

  const goBack = () => {
    navigation.goBack();
  };

  const showDismissAlert = () => {
    if (route.params.mode === 'edit') {
      goBack();
      return;
    }
    if (activeIndex > 5) {
      return;
    }
    Alert.alert(
      'Cancel Assessment',
      'Are you sure you want to cancel your readiness assessment?',
      [
        {
          text: 'Yes',
          onPress: () => {
            if (activeIndex > 0) {
              track(
                `daily_readiness_cancelled_at_${
                  questionTypes[activeIndex - 1]
                }_level`,
              );
            } else {
              track('daily_readiness_cancelled');
            }
            goBack();
          },
        },
        {
          text: 'No',
        },
      ],
      { cancelable: false },
    );
  };

  const gotoNextQuestion = (index) => {
    Keyboard.dismiss();
    if (route.params.mode === 'edit') {
      updateChanges();
    } else {
      changeActiveScreen(index + 1);
    }
  };

  const updateChanges = async () => {
    try {
      const jsonData = {
        client_user_id: currentUser.client_user.id,
        energy_level: state.energy_level,
        energy_level_notes: state.energy_level_notes,
        mood_level: state.mood_level,
        mood_level_notes: state.mood_level_notes,
        stress_level: state.stress_level,
        stress_level_notes: state.stress_level_notes,
        sleep_level: state.sleep_level,
        sleep_level_notes: state.sleep_level_notes,
        overall_soreness_level: currentAssessmentData.overall_soreness_level,
        sore_notes: currentAssessmentData.sore_notes,
        sore_body_parts: currentAssessmentData.sore_body_parts.map(
          ({ body_part_id, soreness_level }) => ({
            body_part_id,
            soreness_level,
          }),
        ),
      };
      await nasm.api.updateReadinessFeedback(
        jsonData,
        currentAssessmentData.id,
      );
      goBack();
    } catch (error) {
      showAlert('Error', 'Something went wrong! Please try again later.');
    }
  };

  const saveReadinessFeedback = async (is_sore) => {
    try {
      const jsonData = {
        client_user_id: currentUser.client_user.id,
        energy_level: state.energy_level,
        energy_level_notes: state.energy_level_notes,
        mood_level: state.mood_level,
        mood_level_notes: state.mood_level_notes,
        stress_level: state.stress_level,
        stress_level_notes: state.stress_level_notes,
        sleep_level: state.sleep_level,
        sleep_level_notes: state.sleep_level_notes,
        is_sore,
        sore_body_parts: [],
      };
      if (is_sore) {
        navigation.navigate({
          name: 'LevelOfSoreness',
          params: {
            jsonData,
          },
        });
      } else {
        setLoading(true);
        await nasm.api.saveReadinessFeedback(jsonData);
        gotoNextQuestion(activeIndex);
      }
    } catch (error) {
      showAlert('Error', 'Something went wrong! Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const showAlert = (title, message) => {
    Alert.alert(
      title,
      message,
      [
        {
          text: 'Okay',
          onPress: goBack,
        },
      ],
      { isCancellable: false },
    );
  };

  const getReadinessView = (index) => {
    switch (index) {
      case 1:
        return (
          <QuestionView
            question="What's your Energy Level?"
            levels={energy_level}
            selectedEmoji={state.energy_level}
            onPressEmoji={(value) => {
              setState({
                ...state,
                energy_level: value,
              });
            }}
            notes={state.energy_level_notes}
            onChangeText={(energy_level_notes) => {
              setState({
                ...state,
                energy_level_notes,
              });
            }}
            onPressNext={() => gotoNextQuestion(index)}
            buttonTitle={route.params.mode === 'edit' ? 'Save Changes' : 'Next'}
          />
        );
      case 2:
        return (
          <QuestionView
            question="How's your Overall Mood?"
            levels={mood_level}
            selectedEmoji={state.mood_level}
            onPressEmoji={(value) => {
              setState({
                ...state,
                mood_level: value,
              });
            }}
            notes={state.mood_level_notes}
            onChangeText={(mood_level_notes) => {
              setState({
                ...state,
                mood_level_notes,
              });
            }}
            onPressNext={() => gotoNextQuestion(index)}
            buttonTitle={route.params.mode === 'edit' ? 'Save Changes' : 'Next'}
          />
        );
      case 3:
        return (
          <QuestionView
            question="How Stressed Are You?"
            levels={stress_level}
            selectedEmoji={state.stress_level}
            onPressEmoji={(value) => {
              setState({
                ...state,
                stress_level: value,
              });
            }}
            notes={state.stress_level_notes}
            onChangeText={(stress_level_notes) => {
              setState({
                ...state,
                stress_level_notes,
              });
            }}
            onPressNext={() => gotoNextQuestion(index)}
            buttonTitle={route.params.mode === 'edit' ? 'Save Changes' : 'Next'}
          />
        );
      case 4:
        return (
          <QuestionView
            question="How'd You Sleep?"
            levels={sleep_level}
            selectedEmoji={state.sleep_level}
            onPressEmoji={(value) => {
              setState({
                ...state,
                sleep_level: value,
              });
            }}
            notes={state.sleep_level_notes}
            onChangeText={(sleep_level_notes) => {
              setState({
                ...state,
                sleep_level_notes,
              });
            }}
            onPressNext={() => gotoNextQuestion(index)}
            buttonTitle={route.params.mode === 'edit' ? 'Save Changes' : 'Next'}
          />
        );
      case 5:
        return (
          <View style={styles.soreView}>
            <Image source={soreImg} />
            <Text style={styles.sore}>Are you sore?</Text>
            {loading ? (
              <View style={styles.loaderView}>
                <ActivityIndicator
                  animating
                  size="large"
                  color={colors.subGrey}
                />
              </View>
            ) : (
              <>
                <TouchableOpacity
                  activeOpacity={0.6}
                  style={styles.buttonStyle(1)}
                  onPress={() => {
                    saveReadinessFeedback(true);
                  }}
                >
                  <Text style={styles.btnText}>Yes</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  activeOpacity={0.6}
                  onPress={() => {
                    track(
                      `Daily Readiness Feedback closed on Question ${activeIndex}`,
                    );
                    saveReadinessFeedback(false);
                  }}
                  style={styles.buttonStyle(0)}
                >
                  <Text style={styles.btnText}>No</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        );
      case 6:
        return <FeedbackSubmitted goBack={goBack} />;
      default:
        return (
          <IntroView
            onPressStart={() => {
              track('daily_readiness_started');
              changeActiveScreen(index + 1);
            }}
            onPressNotNow={() => {
              track('daily_readiness_cancelled');
              goBack();
            }}
          />
        );
    }
  };

  const renderItem = ({ index }) => (
    <View style={styles.itemView}>{getReadinessView(index)}</View>
  );

  return (
    <View style={styles.container}>
      <KeyboardHandler container={styles.container}>
        <TouchableOpacity
          style={[styles.container, styles.background]}
          activeOpacity={1}
          onPress={showDismissAlert}
        />
        <View style={styles.sheet}>
          {activeIndex > 0 && activeIndex < 6 ? (
            <View style={styles.closeRow}>
              <TouchableOpacity
                activeOpacity={0.6}
                onPress={() => {
                  showDismissAlert();
                }}
              >
                <Image source={closeIcon} />
              </TouchableOpacity>
              {route.params.mode !== 'edit' ? (
                <Text style={styles.activeIndexStyle}>
                  {activeIndex}
                  /5
                </Text>
              ) : null}
            </View>
          ) : null}
          <View style={styles.sheetInnerView(activeIndex)}>
            <FlatList
              ref={flatListRef}
              data={['1', '2', '3', '4', '5', '6', '7']}
              renderItem={renderItem}
              initialScrollIndex={route?.params?.selectedIndex || 0}
              onScrollToIndexFailed={() => {
                const wait = new Promise((resolve) => setTimeout(resolve, 500));
                wait.then(() => {
                  flatListRef?.current?.scrollToIndex({
                    animated: true,
                    index: route?.params?.selectedIndex || 0,
                  });
                });
              }}
              horizontal
              pagingEnabled
              bounces={false}
              keyboardShouldPersistTaps="handled"
              scrollEnabled={false}
              showsHorizontalScrollIndicator={false}
            />
          </View>
        </View>
      </KeyboardHandler>
    </View>
  );
};

// PropTypes
const propTypes = {
  navigation: PropTypes.any.isRequired,
  currentUser: PropTypes.shape({
    id: PropTypes.string,
  }),
  route: PropTypes.any.isRequired,
};

const defaultProps = {
  currentUser: null,
};

DailyReadiness.propTypes = propTypes;
DailyReadiness.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  background: {
    backgroundColor: colors.pickerOverlayBg,
  },
  sheet: {
    backgroundColor: colors.veryLightBlue,
    borderTopRightRadius: 17,
    borderTopLeftRadius: 17,
    marginTop: -10,
  },
  sheetInnerView: (activeIndex) => ({
    paddingHorizontal: curvedScale(30),
    paddingVertical: activeIndex > 0 ? 0 : curvedScale(20),
  }),
  closeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: curvedScale(20),
    paddingVertical: curvedScale(10),
  },
  activeIndexStyle: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(17),
  },
  itemView: {
    width: screenWidth - curvedScale(60),
  },
  buttonStyle: type => ({
    width: curvedScale(100),
    borderRadius: 40,
    backgroundColor: type ? colors.linkBlue : colors.nasmBlue,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: curvedScale(8),
    marginTop: type ? curvedScale(20) : curvedScale(15),
  }),
  btnText: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(14),
  },
  soreView: {
    alignItems: 'center',
  },
  sore: {
    color: colors.black,
    fontSize: curvedScale(20),
    fontFamily: 'Avenir-Heavy',
    marginTop: curvedScale(20),
  },
  loaderView: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: curvedScale(50),
  },
});

export default DailyReadiness;
