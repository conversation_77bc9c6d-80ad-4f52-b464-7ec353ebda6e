export const energy_level = [
  { value: 1, label: 'Exhausted' },
  { value: 2, label: 'Tired' },
  { value: 3, label: 'Okay' },
  { value: 4, label: 'Good' },
  { value: 5, label: 'High Energy' },
];

export const mood_level = [
  { value: 1, label: 'Awful' },
  { value: 2, label: 'Bad' },
  { value: 3, label: 'Meh' },
  { value: 4, label: 'Good' },
  { value: 5, label: 'Super' },
];

export const stress_level = [
  { value: 1, label: 'Extremely' },
  { value: 2, label: 'Very' },
  { value: 3, label: 'Sort of' },
  { value: 4, label: 'Little to none' },
  { value: 5, label: "I'm Good" },
];

export const sleep_level = [
  { value: 1, label: 'Awful' },
  { value: 2, label: 'Badly' },
  { value: 3, label: 'Meh' },
  { value: 4, label: 'Well' },
  { value: 5, label: 'Super' },
];

export const getSelectedLevelLabel = (levels, levelValue) => levels?.filter((level) => level.value === levelValue)[0]?.label;

export const getReadinessRatingLabel = (avgReadinessRating = 0) => {
  let label = 'N/A';
  if (avgReadinessRating > 0 && avgReadinessRating <= 2) {
    label = 'Low';
  } else if (avgReadinessRating > 2 && avgReadinessRating <= 3) {
    label = 'Moderate / Fair';
  } else if (avgReadinessRating > 3 && avgReadinessRating <= 4) {
    label = 'Very Good';
  } else if (avgReadinessRating > 4) {
    label = 'Excellent';
  }
  return label;
};
