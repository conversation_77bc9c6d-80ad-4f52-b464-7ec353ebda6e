import React from 'react';
import {
  StyleSheet, Text, View, Image, TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';

const imgSuccess = require('../../../assets/checkmark.png');

const propTypes = {
  goBack: PropTypes.func,
};

const defaultProps = {
  goBack: null,
};

const FeedbackSubmitted = (props) => (
  <View style={styles.submittedView}>
    <Image source={imgSuccess} />
    <Text style={styles.successTitle}>Thanks for Sharing!</Text>
    <Text style={styles.successDescription}>
      Your feedback helps your trainer make customized programs suited just for
      you.
    </Text>
    <TouchableOpacity
      style={styles.btnDone}
      activeOpacity={0.6}
      onPress={props.goBack}
    >
      <Text style={styles.btnLabel}>Done</Text>
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  submittedView: {
    alignItems: 'center',
    marginVertical: curvedScale(30),
  },
  successTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(24),
    marginVertical: curvedScale(15),
    textAlign: 'center',
  },
  successDescription: {
    color: colors.black,
    fontFamily: 'Avenir-Book',
    fontSize: curvedScale(17),
    marginVertical: curvedScale(15),
    textAlign: 'center',
  },
  btnDone: {
    width: '50%',
    marginVertical: curvedScale(20),
    marginHorizontal: curvedScale(25),
    backgroundColor: colors.azure,
    borderRadius: curvedScale(20),
    padding: curvedScale(10),
  },
  btnLabel: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(16),
    textAlign: 'center',
  },
});

FeedbackSubmitted.propTypes = propTypes;
FeedbackSubmitted.defaultProps = defaultProps;

export default FeedbackSubmitted;
