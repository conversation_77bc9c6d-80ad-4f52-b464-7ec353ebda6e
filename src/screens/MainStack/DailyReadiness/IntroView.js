import React from 'react';
import {
  Text, StyleSheet, View, TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import EmojiView from './EmojiView';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';

const IntroView = (props) => {
  const { onPressStart, onPressNotNow } = props;
  return (
    <View>
      <EmojiView viewStyle={styles.emojiViewStyle} disabled />
      <Text style={styles.title}>Daily Readiness</Text>
      <Text style={styles.description}>
        How ready are you for your next workout?
      </Text>
      <Text style={styles.description}>
        Sum it up for your trainer with these
      </Text>
      <Text style={styles.description}>5 questions.</Text>
      <View style={styles.btnView}>
        <TouchableOpacity
          activeOpacity={0.6}
          style={styles.buttonStyle}
          onPress={onPressStart}
        >
          <Text style={styles.start(colors.white)}>Let&apos;s Go</Text>
        </TouchableOpacity>
        <TouchableOpacity
          activeOpacity={0.6}
          style={styles.notNowBtnStyle}
          onPress={onPressNotNow}
        >
          <Text style={styles.start(colors.azure)}>Not Now</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// PropTypes
const propTypes = {
  onPressStart: PropTypes.func,
  onPressNotNow: PropTypes.func,
};

const defaultProps = {
  onPressStart: undefined,
  onPressNotNow: undefined,
};

IntroView.propTypes = propTypes;
IntroView.defaultProps = defaultProps;

const styles = StyleSheet.create({
  emojiViewStyle: {
    width: '90%',
    alignSelf: 'center',
    marginVertical: curvedScale(20),
  },
  title: {
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(24),
    textAlign: 'center',
    color: colors.black,
    marginBottom: curvedScale(20),
  },
  description: {
    fontFamily: 'Avenir-Book',
    fontSize: curvedScale(17),
    textAlign: 'center',
    color: colors.black,
    lineHeight: curvedScale(28),
  },
  btnView: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: curvedScale(20),
  },
  buttonStyle: {
    width: '40%',
    borderRadius: 40,
    backgroundColor: colors.azure,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: curvedScale(8),
  },
  notNowBtnStyle: {
    width: '40%',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: curvedScale(8),
    marginTop: curvedScale(10),
  },
  start: (color) => ({
    color,
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(14),
  }),
});
export default IntroView;
