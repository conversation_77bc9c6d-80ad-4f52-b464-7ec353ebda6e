import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import moment from 'moment';
import PropTypes from 'prop-types';
import CurrentDailyReadiness from './CurrentDailyReadiness';
import { colors } from '../../../styles';

const propTypes = {
  route: PropTypes.any.isRequired,
  navigation: PropTypes.any.isRequired,
};

const defaultProps = {};

const ReadinessDetails = ({ route, navigation }) => {
  const item = route?.params?.item;
  const assessment_id = item?.assessment_id;
  const userIdOfClient = route?.params?.userIdOfClient;
  useEffect(() => {
    navigation.setOptions({
      headerTitle: moment(item?.created_at).format('M/D/YYYY'),
    });
  }, [navigation, item?.created_at]);
  return (
    <View style={styles.container}>
      <CurrentDailyReadiness
        navigation={navigation}
        assessment_id={assessment_id}
        userIdOfClient={userIdOfClient}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});

ReadinessDetails.propTypes = propTypes;
ReadinessDetails.defaultProps = defaultProps;

export default ReadinessDetails;
