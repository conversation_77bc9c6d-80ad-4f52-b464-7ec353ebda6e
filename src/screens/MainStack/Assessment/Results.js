import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import {
  StatusBar,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
  StyleSheet,
} from 'react-native';
import nasm from '../../../dataManager/apiConfig';
import {
  BackHandler,
  DashboardProfile,
  ResultMuscleList,
} from '../../../components';

// Helpers
import { resetToClientDashboard } from '../../../actions';
import { colors, shadow } from '../../../styles';
import { showPainVideoIfAble } from '../../../FTUEVideoManager';
import { EXERCISE_CONTEXTS, sortCorrectiveExercises } from '../../../constants';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

const arrowIcon = require('../../../resources/buttonPlayLight.png');
const check = require('../../../resources/imgCheckmarkOutline.png');
const topIcon = require('../../../resources/imgRecAssess.png');
const closeBtn = require('../../../resources/xLightBlue.png');

// PropTypes
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    state: PropTypes.shape({
      params: PropTypes.shape({
        resultData: PropTypes.object,
        generated_program_identifier: PropTypes.string,
      }),
    }),
  }).isRequired,
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    role: PropTypes.string,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    avatar_url: PropTypes.string,
    first_name: PropTypes.string,
    last_name: PropTypes.string,
  }).isRequired,
  showPainVideoIfAble: PropTypes.func.isRequired,
};
const defaultProps = {};

// Class
class Results extends Component {
  constructor(props) {
    super(props);
    this.state = {
      resultData: this.props.route.params?.resultData || {},
      pain: this.props.route.params?.pain,
      isLoading: false,
      correctiveExercises: [],
    };
    this.resetToClientDashboard = resetToClientDashboard.bind(this);
  }

  componentDidMount() {
    this.setNavigationOptions();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'results' });
    });
    this.props.navigation.setParams({
      onPressFinish: this.onPressFinish,
      onPressContinue: this.onPressContinue,
    });
    if (this.state.pain) {
      this.props.showPainVideoIfAble(
        this.props.currentUser.id,
        this.props.navigation,
      );
    }
    analytics().logEvent('complete_assessment', {
      client_id: this.props.selectedClient?.id,
    });
    this.setState({
      correctiveExercises: sortCorrectiveExercises(
        this.state.resultData.corrective_exercises,
      ),
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressContinue = async () => {
    const { selectedClient } = this.props;
    try {
      const goals = await nasm.api.getAllGoals();
      this.props.navigation.navigate('ProgramCatalog', {
        selectedClient,
        goals,
      });
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  onPressExercise = (exercise) => {
    this.props.navigation.navigate('ExerciseDetails', {
      exercise,
      exerciseContext: EXERCISE_CONTEXTS.NASM_EXERCISE,
    });
  };

  onPressFinish = () => {
    this.resetToClientDashboard();
  };

  setNavigationOptions = () => {
    this.props.navigation.setOptions({
      title: 'RESULTS',
      headerRight: () => (
        <HeaderRightButton
          onPress={this.onPressContinue}
          title="NEXT"
          titleStyle={styles.headerRightText}
        />
      ),
      headerLeft: () => (
        <HeaderLeftButton
          onPress={this.onPressFinish}
          buttonImage={<Image source={closeBtn} />}
        />
      ),
    });
  };

  renderPageHeader = () => (
    <>
      <BackHandler allowed={false} />
      <View style={styles.topContainer}>
        <Image source={topIcon} />
        <Text style={styles.headerText}>
          Below are the overactive and underactive muscles based on your
          client’s assessment as well as NASM EDGE’s recommended exercises.
        </Text>
      </View>
      {/* User Profile Picture */}
      <View style={styles.profileContainer}>
        <DashboardProfile
          containerStyle={styles.pictureStyleContainer}
          profileImage={this.props.selectedClient?.avatar_url}
        />
        <Text style={{ fontWeight: '900', marginTop: 5 }}>
          {`${this.props.selectedClient?.first_name} ${this.props.selectedClient?.last_name}`}
        </Text>
        <Text style={styles.subtitle}>OHSA RESULTS</Text>
      </View>
    </>
  );

  renderPageFooter = () => (
    <>
      <ResultMuscleList
        muscles={
          this.state.resultData.under_active_muscles
            ? this.state.resultData.under_active_muscles
            : []
        }
        Footer={(
          <View style={styles.cardStyle}>
            <View style={styles.titleContainer}>
              <Image source={check} />
              <Text style={styles.recomendedTitle}>
                When creating a program, the following exercises are available
                as recommendations based on your clients OHSA results.
              </Text>
            </View>
            <FlatList
              data={this.state.correctiveExercises}
              keyExtractor={(item) => item.id}
              renderItem={({ item, index }) => (
                <TouchableOpacity
                  key={index}
                  style={styles.rowContainer}
                  onPress={() => this.onPressExercise(item)}
                >
                  <Image source={arrowIcon} />
                  <Text style={styles.exerciseName}>{item.name}</Text>
                </TouchableOpacity>
              )}
            />
            {!this.state.correctiveExercises
              || (this.state.correctiveExercises.length === 0 && (
                <Text style={styles.emptyStateText}>
                  No Recomended Exercises
                </Text>
              ))}
          </View>
        )}
      />
    </>
  );

  render() {
    return (
      <View
        style={{
          paddingBottom: 10,
          paddingTop: 0,
          paddingHorizontal: 0,
        }}
      >
        <View style={{ paddingHorizontal: 18 }}>
          <ResultMuscleList
            overactive
            muscles={
              this.state.resultData.over_active_muscles
                ? this.state.resultData.over_active_muscles
                : []
            }
            Header={<View>{this.renderPageHeader()}</View>}
            Footer={<View>{this.renderPageFooter()}</View>}
          />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  cardStyle: {
    borderRadius: 3,
    backgroundColor: colors.white,
    marginBottom: 10,
    ...shadow,
  },
  profileContainer: {
    alignItems: 'center',
    marginVertical: 15,
  },
  pictureStyleContainer: {
    margin: 0,
  },
  headerRightText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    fontWeight: 'normal',
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'right',
    color: colors.lightBlue,
  },
  subtitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10,
    color: colors.subGrey,
    marginTop: 3,
  },
  rowContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: 'rgba(124, 128, 132, 0.1)',
  },
  exerciseName: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.black,
    marginLeft: 7,
  },
  emptyStateText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.black,
    marginHorizontal: 20,
    marginVertical: 20,
  },
  titleContainer: {
    backgroundColor: colors.goodGreen,
    paddingHorizontal: 20,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    borderTopRightRadius: 3,
    borderTopLeftRadius: 3,
  },
  topContainer: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    color: colors.subGrey,
    marginLeft: 20,
    marginRight: 25,
  },
  recomendedTitle: {
    flex: 1,
    marginHorizontal: 16,
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '800',
    color: colors.white,
  },
});

// Export
Results.propTypes = propTypes;
Results.defaultProps = defaultProps;
const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = { showPainVideoIfAble };
export default connect(mapStateToProps, mapDispatchToProps)(Results);
