import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import {
  StatusBar, Text, View, Alert, ActivityIndicator,
} from 'react-native';
import nasm from '../../../dataManager/apiConfig';
import { PageContainer, LoadingSpinner } from '../../../components';
import { AssessmentCard, AssessmentRow } from './components';

// Constants
import { colors } from '../../../styles';
import { resetToClientDashboard } from '../../../actions';
import HeaderRightButton from '../../../components/HeaderRightButton';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import { EXERCISE_CONTEXTS } from '../../../constants';

// Images
const normalFrontImage = require('../../../resources/shoeNormalFront.png');
const normalSideImage = require('../../../resources/shoeNormalSide.png');
const normalBackImage = require('../../../resources/shoeNormalBack.png');
const painImage = require('../../../resources/assessmentPain.png');

// Props
const propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    state: PropTypes.shape({
      params: PropTypes.shape({
        title: PropTypes.string,
        clientId: PropTypes.string,
      }),
    }).isRequired,
  }).isRequired,
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    role: PropTypes.string,
  }).isRequired,
};
const defaultProps = {};

class OHSA extends Component {
  static navigationOptions = ({ navigation, route }) => {
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Overhead Squat',
      headerLeft: () => (
        <HeaderLeftButton
          title="Cancel"
          disabled={route.params?.saving}
          onPress={() => navigation.goBack()}
          titleStyle={styles.headerButtonText}
        />
      ),
      headerRight: () => renderHeaderRight(route),
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      videoUri: null,
      videoVisible: false,
      compMap: {},
      painSelected: false,
      isLoading: false,
    };
    this.resetToClientDashboard = resetToClientDashboard.bind(this);
  }

  componentDidMount() {
    this.props.navigation.setParams({
      renderHeaderRight: this.renderHeaderRight,
      onPressSkipOHSA: this.onPressSkipOHSA,
    });
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'ohsa' });
    });

    this.setState({ isLoading: true }, () => {
      nasm.api
        .getAssessmentByName('ohsa')
        .then((assessment) => {
          if (assessment) {
            const { compensations } = assessment;
            const compMap = compensations.reduce((obj, item, index) => {
              obj[item.id] = index;
              return obj;
            }, {});
            this.setState({ data: compensations, compMap, isLoading: false });
          }
        })
        .catch((error) => {
          Alert.alert('Error', error.message);
        });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onButtonPressed = (id, isPain = false) => {
    const { data } = this.state;
    const dataMap = this.state.compMap;
    const index = parseInt(id, 10);
    let { painSelected } = this.state;
    if (index < data.length) {
      if (data[index].dependent_comp_id != null) {
        const oppositeIndex = dataMap[data[index].dependent_comp_id];
        data[oppositeIndex].value = false;
      }
      data[index].value = !data[index].value;
      painSelected = isPain ? data[index].value : this.state.painSelected;
    }
    this.setState({ data, painSelected });
  };

  onDismissVideo = () => {
    this.setState({ videoVisible: false });
  };

  onPressFrontOverheadSquat = () => {};

  onPressNext = () => {
    this.props.navigation.setParams({ saving: true });
    this.submitOHST(this.state.data);
  };

  onPressRearOverheadSquat = () => {};

  onPressSideOverheadSquat = () => {};

  onPressSkipOHSA = () => {
    this.resetToClientDashboard();
  };

  onPressVideo = (videoUri) => {
    this.props.navigation.navigate('VideoModal', { uri: videoUri });
  };

  onPressWorkout = () => {
    // TODO: update when we have videos from Fusionetics
    // the ugliest bit of code you will see all day.
    const exercise = this.props.programs[0].Strategies[0].Techniques[0]
      .Exercises[0];
    this.props.navigation.navigate('ExerciseDetails', {
      exercise,
      exerciseContext: EXERCISE_CONTEXTS.NASM_EXERCISE,
    });
  };

  submitOHST = async (data) => {
    // submit the assessment
    const userId = this.props.selectedClient?.id;
    nasm.api
      .submitOHST(userId, { assessment_data: data })
      .then(() => {
        this.props.navigation.goBack();
      })
      .catch((err) => {
        Alert.alert('Error', err.message);
        this.props.navigation.setParams({ saving: false });
      })
      .finally(() => {});
  };

  buildData() {
    const ohsaData = {};
    for (let i = 0; i < this.state.data.length; i += 1) {
      ohsaData[this.state.data[i].value] = this.state.data[i].selected;
    }
    return ohsaData;
  }

  renderHeaderRight = (route) => {
    if (route.params?.saving) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          <ActivityIndicator color={colors.white} />
        </View>
      );
    }
    return (
      <HeaderRightButton
        onPress={() => this.onPressNext()}
        title="Save"
        titleStyle={styles.headerButtonText}
      />
    );
  };

  render() {
    return (
      <View style={{ flex: 1 }}>
        <PageContainer
          containerStyle={{ paddingHorizontal: 0, paddingTop: 0 }}
          scrollEnabled
          statusBarContent="light-content"
        >
          <AssessmentCard
            cardTitle="Front"
            headerIconSource={normalFrontImage}
            videoTitle="Overhead Squat"
            videoUri="https://production.smedia.lvp.llnw.net/c795395f130d4737be848be06f52ea62/Oo/UTA596-Rk9VTBNCXN9yatNbz9QaubjJaIF7_0f5xo/nasm_edge_tips_conducting_ohsa.mp4?x=0&h=f913e4d75db3cead511863aab92d768f"
            onPressPlay={this.onPressVideo}
          >
            <View style={styles.spaceBetweenContainer}>
              <Text style={styles.subheaderTextStyle}>Foot/Ankle</Text>
              <View style={styles.leftRightContainer}>
                <Text style={styles.leftRightTextStyle}>L</Text>
                <Text style={styles.leftRightTextStyle}>R</Text>
              </View>
            </View>
            <AssessmentRow
              title="Foot Turns Out"
              leftButtonId="1"
              rightButtonId="0"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
            />
            <AssessmentRow
              title="Foot Flattens"
              leftButtonId="3"
              rightButtonId="2"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
            />
            <View style={styles.spaceBetweenContainer}>
              <Text style={styles.subheaderTextStyle}>Knee</Text>
              <View style={styles.leftRightContainer}>
                <Text style={styles.leftRightTextStyle}>L</Text>
                <Text style={styles.leftRightTextStyle}>R</Text>
              </View>
            </View>
            <AssessmentRow
              title="Knee Moves In (Valgus)"
              leftButtonId="5"
              rightButtonId="4"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
            />
            <AssessmentRow
              title="Knee Moves Out (Varus)"
              leftButtonId="7"
              rightButtonId="6"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
              separator={false}
            />
          </AssessmentCard>

          <AssessmentCard
            cardTitle="Side"
            headerIconSource={normalSideImage}
            onPressPlay={this.onPressVideo}
          >
            <View style={styles.spaceBetweenContainer}>
              <Text style={styles.subheaderTextStyle}>L-P-H-C</Text>
            </View>
            <AssessmentRow
              title="Excessive Forward Lean"
              leftButtonId="8"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
            />
            <AssessmentRow
              title="Low Back Arches"
              leftButtonId="9"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
            />
            <AssessmentRow
              title="Low Back Rounds"
              leftButtonId="10"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
            />

            <View style={styles.spaceBetweenContainer}>
              <Text style={styles.subheaderTextStyle}>Shoulder</Text>
            </View>
            <AssessmentRow
              title="Arms Fall Forward"
              leftButtonId="11"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
              separator={false}
            />
          </AssessmentCard>

          <AssessmentCard
            cardTitle="Rear"
            headerIconSource={normalBackImage}
            onPressPlay={this.onPressVideo}
          >
            <View style={styles.spaceBetweenContainer}>
              <Text style={styles.subheaderTextStyle}>Foot/Ankle</Text>
              <View style={styles.leftRightContainer}>
                <Text style={styles.leftRightTextStyle}>L</Text>
                <Text style={styles.leftRightTextStyle}>R</Text>
              </View>
            </View>
            <AssessmentRow
              title="Heels of Foot Lift"
              leftButtonId="13"
              rightButtonId="12"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
            />

            <View style={styles.spaceBetweenContainer}>
              <Text style={styles.subheaderTextStyle}>L-P-H-C</Text>
              <View style={styles.leftRightContainer}>
                <Text style={styles.leftRightTextStyle}>L</Text>
                <Text style={styles.leftRightTextStyle}>R</Text>
              </View>
            </View>
            <AssessmentRow
              title={'Asymmetrical \nWeight Shift'}
              leftButtonId="15"
              rightButtonId="14"
              onPressButton={this.onButtonPressed}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
              separator={false}
            />
          </AssessmentCard>

          <AssessmentCard
            cardTitle="Client Health"
            headerIconSource={painImage}
          >
            <AssessmentRow
              title={'Is the Client \nexperiencing pain?'}
              leftButtonId="16"
              onPressButton={(id) => this.onButtonPressed(id, true)}
              onPressWorkout={this.onPressWorkout}
              selectedItems={this.state.data}
              separator={false}
            />
          </AssessmentCard>
        </PageContainer>
        <LoadingSpinner
          visible={this.state.isLoading}
          size="large"
          color="gray"
          backgroundColor="rgba(255, 255, 255, 0.75)"
        />
      </View>
    );
  }
}

// Export this for the other OHSA screens
export const styles = {
  container: {
    flex: 1,
  },
  subheaderTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 19,
    fontWeight: '900',
    color: colors.black,
  },
  spaceBetweenContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    justifyContent: 'space-between',
  },
  leftRightContainer: {
    flexDirection: 'row',
    width: '45%',
    backgroundColor: '#f8f8f9',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 16,
  },
  leftRightTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '900',
    textAlign: 'center',
    marginHorizontal: 20,
    marginVertical: 6,
    color: colors.subGrey,
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};

// Export
OHSA.propTypes = propTypes;
OHSA.defaultProps = defaultProps;
const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(OHSA);
