import React from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image, StyleSheet, TouchableOpacity, Text, View,
} from 'react-native';

// Constants
import { colors, shadow } from '../../../../styles';

const playImage = require('../../../../resources/buttonPlayLightBlue.png');

// PropTypes
const propTypes = {
  children: PropTypes.node,
  cardTitle: PropTypes.string.isRequired,
  headerIconSource: PropTypes.any.isRequired,
  videoUri: PropTypes.string,
  videoTitle: PropTypes.string,
  onPressPlay: PropTypes.func,
};
const defaultProps = {
  children: null,
  videoUri: null,
  videoTitle: null,
  videoDescription: null,
  onPressPlay: null,
};

// Component Class
function AssessmentCard(props) {
  return (
    <View>
      {props.videoTitle && (
        <TouchableOpacity
          style={styles.videoButtonContainer}
          onPress={() => props.onPressPlay(props.videoUri)}
        >
          {props.videoUri && <Image source={playImage} />}
          <Text style={styles.videoTitleText}>{props.videoTitle}</Text>
        </TouchableOpacity>
      )}
      <View style={styles.cardStyle}>
        <View style={styles.cardHeadercontainer}>
          <Text style={styles.headerText}>{props.cardTitle}</Text>
          <Image style={{ marginRight: 22 }} source={props.headerIconSource} />
        </View>
        <View style={styles.divider} />
        {props.children}
      </View>
    </View>
  );
}

// Export
AssessmentCard.propTypes = propTypes;
AssessmentCard.defaultProps = defaultProps;
export default AssessmentCard;

// Styles
const styles = StyleSheet.create({
  videoButtonContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 12,
    marginHorizontal: 18,
    marginTop: 18,
    borderRadius: 3,
    backgroundColor: colors.buttonBlue,
    alignItems: 'center',
    ...shadow,
  },
  videoTitleText: {
    fontFamily: 'Avenir',
    fontSize: 19,
    fontWeight: '900',
    color: colors.white,
    textAlign: 'center',
    marginHorizontal: 10,
  },
  cardStyle: {
    borderRadius: 3,
    marginHorizontal: 18,
    backgroundColor: colors.white,
    marginTop: 18,
    paddingHorizontal: 16,
    ...shadow,
  },
  cardHeadercontainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    justifyContent: 'space-between',
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 24,
    fontWeight: '900',
    color: colors.subGrey,
  },
  divider: {
    height: 2,
    width: '50%',
    backgroundColor: colors.silver,
  },
});
