import React from 'react';
import PropTypes from 'prop-types';

// Components
import {
  StyleSheet, Text, TouchableOpacity, View,
} from 'react-native';
import { OHSAButton } from '../../../../components';

// Constants
import { colors } from '../../../../styles';

// PropTypes
const propTypes = {
  title: PropTypes.string.isRequired,
  onPressWorkout: PropTypes.func.isRequired,
  onPressButton: PropTypes.func.isRequired,
  leftButtonId: PropTypes.string.isRequired,
  rightButtonId: PropTypes.string,
  selectedItems: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.bool,
    }),
  ),
  separator: PropTypes.bool,
};
const defaultProps = {
  children: null,
  rightButtonId: null,
  separator: true,
  selectedItems: null,
};

// Component Class
class AssessmentRow extends React.Component {
  getSelectionState = (id) => {
    if (this.props.selectedItems) {
      return this.props.selectedItems[parseInt(id, 10)].value;
    }
    return false;
  };

  render() {
    return (
      <TouchableOpacity
        style={[
          styles.container,
          { borderBottomWidth: this.props.separator ? 1 : 0 },
        ]}
        onPress={this.props.onPressWorkout}
        disabled
      >
        <Text style={styles.descTextStyle}>{this.props.title}</Text>
        <View style={styles.selectableContainer}>
          <OHSAButton
            id={this.props.leftButtonId}
            onPress={this.props.onPressButton}
            selected={this.getSelectionState(this.props.leftButtonId)}
          />
          {this.props.rightButtonId && (
            <OHSAButton
              id={this.props.rightButtonId}
              onPress={this.props.onPressButton}
              selected={this.getSelectionState(this.props.rightButtonId)}
            />
          )}
        </View>
      </TouchableOpacity>
    );
  }
}

// Export
AssessmentRow.propTypes = propTypes;
AssessmentRow.defaultProps = defaultProps;
export default AssessmentRow;

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderColor: colors.subGreyLight,
  },
  descTextStyle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.black,
  },
  selectableContainer: {
    flexDirection: 'row',
    width: '45%',
  },
});
