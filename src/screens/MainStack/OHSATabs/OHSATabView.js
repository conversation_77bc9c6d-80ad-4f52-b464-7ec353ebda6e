import React, { Component } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { connect } from 'react-redux';

// Components
import {
  View, Image, Dimensions, StyleSheet,
} from 'react-native';
import { showAssessmentVideoIfAble } from '../../../FTUEVideoManager';
import Current from './CurrentOHSA';
import History from './OHSAHistory';

// Styles
import { colors, materialTabBarOptions } from '../../../styles';
import HeaderLeftButton from '../../../components/HeaderLeftButton';
import HeaderRightButton from '../../../components/HeaderRightButton';

const infoIcon = require('../../../resources/info.png');

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

class OHSATabView extends Component {
  componentDidMount() {
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
      renderHeaderRight: this.renderHeaderRight,
    });
    this.props.showAssessmentVideoIfAble(
      this.props.currentUser.id,
      this.props.navigation,
    );
  }

  static router = TabNav.router;

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Overhead Squat',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.props.navigation.navigate('VideoModal', {
        uri: 'https://production.smedia.lvp.llnw.net/c795395f130d4737be848be06f52ea62/Oo/UTA596-Rk9VTBNCXN9yatNbz9QaubjJaIF7_0f5xo/nasm_edge_tips_conducting_ohsa.mp4?x=0&h=f913e4d75db3cead511863aab92d768f',
      })}
      buttonImage={<Image source={infoIcon} />}
    />
  );

  render() {
    const backgroundColor = this.props.trainerActiveProfile?.ClubId
      ? colors.black
      : colors.duskBlue;
    return (
      <TabNav.Navigator
        screenOptions={{
          ...materialTabBarOptions.defaultNavigationOptions,
          ...materialTabBarOptions.tabBarOptions,
          tabBarStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarStyle,
            backgroundColor,
          },
          tabBarIndicatorStyle: {
            ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
            width: indicatorWidth,
            // Based of https://github.com/satya164/react-native-tab-view/issues/944#issuecomment-599224375
            left: (tabWidth - indicatorWidth) / 2,
          },
          swipeEnabled: false,
        }}
        style={materialTabBarOptions.tabBarOptions.tabBarStyle}
      >
        <TabNav.Screen name="Current" component={Current} />
        <TabNav.Screen name="History" component={History} />
      </TabNav.Navigator>
    );
  }
}

const styles = StyleSheet.create({
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
});

const mapStateToProps = ({ currentUser, trainerActiveProfile }) => ({
  currentUser,
  trainerActiveProfile,
});
const mapDispatchToProps = { showAssessmentVideoIfAble };

export default connect(mapStateToProps, mapDispatchToProps)(OHSATabView);
