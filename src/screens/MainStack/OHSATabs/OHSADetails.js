import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import moment from 'moment';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import {
  StyleSheet, Text, Alert, Image, SafeAreaView,
} from 'react-native';
import { OHSAView } from '../../../components';
import {
  EXERCISE_CONTEXTS,
  ROLES,
  sortCorrectiveExercises,
} from '../../../constants';
import nasm from '../../../dataManager/apiConfig';

// Styles
import { colors } from '../../../styles';
import HeaderRightButton from '../../../components/HeaderRightButton';
import HeaderLeftButton from '../../../components/HeaderLeftButton';

// Images
const infoIcon = require('../../../resources/info.png');

const propTypes = {
  currentUser: PropTypes.shape({
    role: PropTypes.string,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.any,
  }).isRequired,
};

class CurrentOHSA extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataset: {},
      correctiveExercises: [],
    };
  }

  componentDidMount() {
    this.setNavigationOptions();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', { screen_name: 'current_ohsa' });
      this.getOHSA();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressExercise = (exercise) => {
    this.props.navigation.navigate('ExerciseDetails', {
      exercise,
      exerciseContext: EXERCISE_CONTEXTS.NASM_EXERCISE,
      isEditNameIconShow: false,
    });
  };

  getOHSA = async () => {
    const assessment = this.props.route.params?.assessment;
    if (assessment) {
      try {
        const dataset = await nasm.api.getCorrectiveAssessment(
          this.props.selectedClient?.id,
          assessment.id,
        );
        this.setState({
          dataset,
          correctiveExercises: sortCorrectiveExercises(
            dataset.corrective_exercises,
          ),
        });
      } catch (error) {
        Alert.alert('Error', error.message);
      }
    } else {
      Alert.alert('Error', 'missing assessment id');
    }
  };

  setNavigationOptions = () => {
    this.props.navigation.setOptions({
      title: 'Overhead Squat',
      headerRight: () => this.renderHeaderRight(),
      headerLeft: () => (
        <HeaderLeftButton
          onPress={() => this.props.navigation.goBack()}
          titleStyle={styles.headerButtonText}
        />
      ),
    });
  };

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.props.navigation.navigate('VideoModal', {
        uri: 'https://production.smedia.lvp.llnw.net/c795395f130d4737be848be06f52ea62/Oo/UTA596-Rk9VTBNCXN9yatNbz9QaubjJaIF7_0f5xo/nasm_edge_tips_conducting_ohsa.mp4?x=0&h=f913e4d75db3cead511863aab92d768f',
      })}
      buttonImage={<Image source={infoIcon} />}
    />
  );

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.date}>
          {moment(this.state.dataset.assessment_date).format('MMMM D, YYYY')}
        </Text>
        <OHSAView
          dataset={this.state.dataset}
          correctiveExercises={this.state.correctiveExercises}
          isTrainer={this.props.currentUser.role === ROLES.TRAINER}
          onPressExercise={this.onPressExercise}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  date: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18,
    color: colors.subGrey,
    marginTop: 27,
    marginLeft: 30,
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
});

CurrentOHSA.propTypes = propTypes;

const mapStateToProps = ({ selectedClient, currentUser }) => ({
  selectedClient,
  currentUser,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(CurrentOHSA);
