import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';

// Analytics
import analytics from '@react-native-firebase/analytics';

// Components
import { View, Alert } from 'react-native';
import nasm from '../../../dataManager/apiConfig';
import { OHSAView, FloatingButton } from '../../../components';
import {
  EXERCISE_CONTEXTS,
  ROLES,
  sortCorrectiveExercises,
} from '../../../constants';

const propTypes = {
  currentUser: PropTypes.shape({
    role: PropTypes.string,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.any,
  }).isRequired,
};

class CurrentOHSA extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataset: {},
      correctiveExercises: [],
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', { screen_name: 'current_ohsa' });
      this.getLastOHSA();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressExercise = (exercise) => {
    this.props.navigation.navigate('ExerciseDetails', {
      exercise,
      exerciseContext: EXERCISE_CONTEXTS.NASM_EXERCISE,
      isEditNameIconShow: false,
    });
  };

  getLastOHSA = async () => {
    try {
      const dataset = await nasm.api.getAssessmentResult(
        this.props.selectedClient?.id,
      );
      this.setState({
        dataset,
        correctiveExercises: sortCorrectiveExercises(
          dataset.corrective_exercises,
        ),
      });
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  render() {
    return (
      <View style={{ flex: 1 }}>
        <OHSAView
          showUpdatedOn
          dataset={this.state.dataset}
          correctiveExercises={this.state.correctiveExercises}
          isTrainer={this.props.currentUser.role === ROLES.TRAINER}
          onPressExercise={this.onPressExercise}
        />
        {this.props.currentUser.role === ROLES.TRAINER && (
          <FloatingButton
            onPress={() => this.props.navigation.navigate('OHSA')}
          />
        )}
      </View>
    );
  }
}

// Export
CurrentOHSA.propTypes = propTypes;
const mapStateToProps = ({ selectedClient, currentUser }) => ({
  selectedClient,
  currentUser,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(CurrentOHSA);
