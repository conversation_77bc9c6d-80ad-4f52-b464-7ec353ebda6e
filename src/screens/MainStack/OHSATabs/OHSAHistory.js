import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

// Components
import {
  View,
  Text,
  Alert,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Image,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import nasm from '../../../dataManager/apiConfig';

// Analytics

// Styles
import { colors } from '../../../styles';

const arrowIcon = require('../../../resources/imgRightArrowGray.png');

class OHSAHistory extends Component {
  constructor(props) {
    super(props);
    this.state = {
      assessments: [],
      isRefreshing: false,
      page: 0,
      hasNextPage: true,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      analytics().logEvent('screen_view', { screen_name: 'ohsa_history' });
      this.getAssessments(true);
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  getAssessments = async (refresh) => {
    if (!refresh && !this.state.hasNextPage) return;
    let { assessments } = this.state;
    const page = refresh ? 1 : this.state.page + 1;
    try {
      const result = await nasm.api.getAllCorrectiveAssessments(
        this.props.selectedClient?.id,
        page,
        15,
      );
      if (refresh) {
        assessments = result.corrective_assessments;
      } else {
        assessments = [...assessments, ...result.corrective_assessments];
      }
      const hasNextPage = result.nextPage && result.nextPage.length > 0;
      this.setState({ assessments, page, hasNextPage });
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      this.setState({ isLoading: false, isRefreshing: false });
    }
  };

  renderAssessment = ({ item, index }) => (
    <TouchableOpacity
      key={index}
      onPress={() => this.props.navigation.navigate('OHSADetails', { assessment: item })}
      style={styles.assessmentContainer}
    >
      <Text style={styles.assessmentText}>
        {moment(item.assessment_date).format('M/D/YY')}
      </Text>
      <View style={{ flex: 1 }}>
        <Text style={styles.assessmentText}>OHSA</Text>
      </View>
      <Image source={arrowIcon} />
    </TouchableOpacity>
  );

  render() {
    return (
      <FlatList
        data={this.state.assessments}
        renderItem={this.renderAssessment}
        onEndReached={() => this.getAssessments()}
        keyExtractor={(item) => item.id}
        refreshControl={(
          <RefreshControl
            refreshing={this.state.isRefreshing}
            onRefresh={() => this.setState({ isRefreshing: true }, () => this.getAssessments(true))}
          />
        )}
      />
    );
  }
}

const styles = {
  assessmentContainer: {
    padding: 30,
    borderColor: colors.subGreyLight,
    borderBottomWidth: 1,
    flexDirection: 'row',
  },
  assessmentText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 16,
    color: colors.black,
    marginRight: 30,
  },
};

const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(OHSAHistory);
