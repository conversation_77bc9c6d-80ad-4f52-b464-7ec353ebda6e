import React from 'react';
import {
  Text, View, StyleSheet, Image, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import PropTypes from 'prop-types';
import * as Sentry from '@sentry/react-native';
import { colors } from '../../../styles';
import { curvedScale, scaleHeight } from '../../../util/responsive';

const noImage = require('../../../resources/defaultProfile.png');
const arrowIcon = require('../../../resources/imgRightArrowGray.png');
const workoutEmoji = require('../../../resources/fire-filled.png');
const tickIcon = require('../../../resources/on.png');

const userImageSize = scaleHeight(6);
const iconSize = scaleHeight(2);

const propTypes = {
  goToWorkoutSummary: PropTypes.func,
  item: PropTypes.object.isRequired,
  containerStyle: PropTypes.object,
  showUsersInfo: PropTypes.bool,
};

const defaultProps = {
  containerStyle: null,
  showUsersInfo: false,
  goToWorkoutSummary: null,
};

const WorkoutActivityCard = ({
  goToWorkoutSummary,
  item,
  containerStyle,
  showUsersInfo,
}) => {
  const is_rook_activity = item.activity_type === 'rook_data_source_activities';
  if (!is_rook_activity && !item.workout_feedback) {
    Sentry.captureMessage(
      'ACTIVITY FEED: workout_feedback is NULL or undefined',
    );
    return null;
  }
  const incompleteStyle = { color: colors.nasmRed, opacity: 0.7 };
  const modifiedStyle = { color: colors.vividBlue };
  const commentStyle = { color: colors.black, opacity: 0.9 };
  const completeStyle = { color: colors.avatarGreen };

  const statusFlex = showUsersInfo ? 8.3 : 1;
  const source = item.user?.avatar_url
    ? { uri: item.user.avatar_url }
    : noImage;
  const getWorkoutDate = () => moment(item.created_at).format('M/D/YY');

  const incomplete_exercise_count = item.incomplete_exercise_count
    ?? item.workout_feedback?.incomplete_exercise_count
    ?? -1;
  const modified_exercise_count = item.modified_exercise_count
    ?? item.workout_feedback?.modified_exercise_count
    ?? -1;
  const has_feedback = item.has_feedback ?? item.workout_feedback?.has_feedback ?? false;

  return (
    <TouchableOpacity
      onPress={goToWorkoutSummary}
      style={[styles.workoutActivityContainer, containerStyle]}
      disabled={is_rook_activity}
    >
      <View style={styles.workoutHorizontalView}>
        {showUsersInfo && (
          <View style={{ marginRight: 10 }}>
            <View style={styles.userImageView}>
              <Image style={styles.userImage} source={source} />
            </View>
            <View style={styles.emojiView}>
              <Image
                style={styles.emoji}
                source={is_rook_activity ? tickIcon : workoutEmoji}
              />
            </View>
          </View>
        )}
        <View style={[styles.workoutView, { flex: statusFlex }]}>
          {showUsersInfo && (
            <View style={styles.userNameView}>
              <Text numberOfLines={1} style={styles.workoutName}>
                {item.user?.first_name}
              </Text>
              <View style={styles.dateView}>
                <Text numberOfLines={1} style={styles.date}>
                  {getWorkoutDate()}
                </Text>
              </View>
              {is_rook_activity ? null : (
                <Image style={styles.arrowIcon} source={arrowIcon} />
              )}
            </View>
          )}
          <Text numberOfLines={1} style={styles.workoutName}>
            {item.name || item.workout_name}
          </Text>
          <View style={styles.workoutStatusView(showUsersInfo)}>
            <View style={styles.workoutStatusInnerView}>
              {/* Workout is completed with no modifications */}
              {incomplete_exercise_count === 0
              && modified_exercise_count === 0 ? (
                <Text
                  numberOfLines={1}
                  style={[styles.workoutStatus(showUsersInfo), completeStyle]}
                >
                  complete
                </Text>
                ) : (
                  <>
                    {incomplete_exercise_count > 0 ? (
                      <Text
                        numberOfLines={1}
                        style={[
                          styles.workoutStatus(showUsersInfo),
                          incompleteStyle,
                        ]}
                      >
                        {`${incomplete_exercise_count} incomplete`}
                        {' '}
                      </Text>
                    ) : null}
                  </>
                )}
              {modified_exercise_count > 0 ? (
                <Text
                  numberOfLines={1}
                  style={[styles.workoutStatus(showUsersInfo), modifiedStyle]}
                >
                  {`${modified_exercise_count} modified`}
                  {' '}
                </Text>
              ) : null}
              {has_feedback ? (
                <Text
                  numberOfLines={1}
                  style={[styles.workoutStatus(showUsersInfo), commentStyle]}
                >
                  feedback
                  {' '}
                </Text>
              ) : null}
            </View>
            {!showUsersInfo && (
              <View style={styles.dateView}>
                <Text numberOfLines={1} style={styles.date}>
                  {getWorkoutDate()}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

WorkoutActivityCard.propTypes = propTypes;
WorkoutActivityCard.defaultProps = defaultProps;

export default WorkoutActivityCard;

const styles = StyleSheet.create({
  workoutActivityContainer: {
    borderColor: colors.colorsFillLight2,
    borderBottomWidth: 1,
    paddingVertical: 20,
  },
  workoutHorizontalView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  workoutView: {
    justifyContent: 'center',
  },
  userImageView: {
    width: userImageSize,
    height: userImageSize,
  },
  userImage: {
    width: '100%',
    height: '100%',
    borderRadius: userImageSize / 2,
  },
  emojiView: {
    width: 25,
    height: 25,
    position: 'absolute',
    bottom: -5,
    right: -5,
    borderRadius: 25 / 2,
    backgroundColor: colors.white,
  },
  emoji: {
    width: '100%',
    height: '100%',
  },
  userNameView: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  arrowIcon: {
    width: iconSize,
    height: iconSize,
    marginLeft: 10,
  },
  workoutName: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    width: '75%',
    fontSize: curvedScale(12),
  },
  workoutStatusView: (showUsersInfo) => ({
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: showUsersInfo ? 2 : 5,
  }),
  workoutStatusInnerView: {
    flex: 7,
    flexDirection: 'row',
    alignItems: 'center',
  },
  workoutStatus: (showUsersInfo) => ({
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(10),
    alignSelf: 'flex-start',
    maxWidth: showUsersInfo ? null : '33%',
    marginRight: 10,
  }),
  dateView: {
    flex: 3,
    alignItems: 'flex-end',
  },
  date: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(10),
  },
});
