import React, { Component } from 'react';
import {
  SafeAreaView,
  Platform,
  View,
  Image,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

import { colors } from '../../../styles';
import { saveToStorage } from '../../../util/Storage.utils';

const closeIcon = require('../../../assets/closeCircle.png');
const workoutFeedback = require('../../../assets/workoutFeedbackImg.png');

class WorkoutIntroModal extends Component {
  static navigationOptions = () => ({
    headerTransparent: true,
    headerShown: false,
  });

  componentDidMount() {
    saveToStorage(this.props.route?.params?.storageKey, true);
  }

  closeModal = () => {
    this.props.navigation.goBack();
    if (this.props.route?.params?.onResponded) {
      this.props.route?.params?.onResponded();
    }
  };

  render() {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.imageContainer}>
          <TouchableOpacity activeOpacity={0.7} onPress={this.closeModal}>
            <Image source={closeIcon} style={styles.image} />
          </TouchableOpacity>
        </View>
        <View style={styles.pageContainer}>
          <View style={styles.contentView}>
            <Image source={workoutFeedback} />
            <Text style={styles.headerText}>Workout Feedback</Text>
            <Text style={styles.descriptionText}>
              During your workout, if you needed to adjust the reps, weight, or
              any item of an exercise, tap the item and let your trainer know.
              After your workout, provide additional feedback regarding the
              level of difficulty or a general message.
              {' '}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          activeOpacity={0.7}
          style={styles.btnView}
          onPress={this.closeModal}
        >
          <Text style={styles.saveButtonStyle}>Okay!</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.transparentBlack,
  },
  headerText: {
    color: colors.white,
    fontFamily: 'Avenir-Black',
    fontSize: 30,
    fontWeight: 'bold',
    marginHorizontal: 16,
    marginTop: 10,
    alignSelf: 'center',
  },
  descriptionText: {
    color: colors.white,
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    marginHorizontal: 16,
    lineHeight: 20,
    marginTop: 13,
    alignItems: 'center',
    textAlign: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    marginTop: Platform.OS === 'ios' ? 30 : 60,
    marginHorizontal: 20,
  },
  image: {
    width: 30,
    height: 30,
  },
  pageContainer: {
    justifyContent: 'center',
    flex: 1,
    alignItems: 'center',
  },
  contentView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnView: {
    backgroundColor: colors.buttonBlue,
    borderRadius: 30,
    paddingVertical: 5,
    width: '30%',
    alignSelf: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  saveButtonStyle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 14,
    marginVertical: 10,
    backgroundColor: colors.buttonBlue,
    paddingHorizontal: 20,
  },
});

export default WorkoutIntroModal;
