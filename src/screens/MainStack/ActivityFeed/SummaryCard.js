import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  ImageBackground,
  SafeAreaView,
  View,
  Image,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  FlatList,
} from 'react-native';
import PropTypes from 'prop-types';
import moment from 'moment';
import AnimatedCircularProgress from '../../../components/CircularAnimated/AnimatedCircularProgress';
import nasm from '../../../dataManager/apiConfig';
import LoadingSpinner from '../../../components/LoadingSpinner';
import getExerciseName from '../../../util/exerciseUtils';
import { colors, shadow } from '../../../styles';
import { ROLES } from '../../../constants';
import {
  getRestTempoStringForExercise,
  getSideStringForExercise,
  getTempoStringForExercise,
} from '../../../util/programUtils';
import { track } from '../../../util/Analytics';
import { curvedScale } from '../../../util/responsive';

const rightArrow = require('../../../resources/rightArrow.png');

const propTypes = {
  currentUser: PropTypes.shape({
    role: PropTypes.string,
    first_name: PropTypes.string,
    id: PropTypes.string,
  }),
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
  }),
  navigation: PropTypes.shape({
    pop: PropTypes.func,
    popToTop: PropTypes.func,
    navigate: PropTypes.func,
    addListener: PropTypes.func,
  }).isRequired,
  route: PropTypes.shape({
    params: PropTypes.object,
  }).isRequired,
};

const defaultProps = {
  currentUser: null,
  selectedClient: null,
};

const closeIcon = require('../../../assets/closeCircle.png');
const blueBanner = require('../../../assets/blueBanner.png');
const noImage = require('../../../resources/imgExerciseBlockDefault.png');

const exhausted = require('../../../resources/exhausted.png');
const tired = require('../../../resources/tired.png');
const okay = require('../../../resources/okay.png');
const good = require('../../../resources/good.png');
const high = require('../../../resources/high.png');

const ratings = [
  { rating: 1, icon: exhausted },
  { rating: 2, icon: tired },
  { rating: 3, icon: okay },
  { rating: 4, icon: good },
  { rating: 5, icon: high },
];

class SummaryCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showLoading: true,
      goal: '0',
      workoutSummaryResult: null,
      isFeedbackGiven: false,
      clientUserId: props.route?.params?.userId,
      clientUserName:
        props.route?.params?.userName
        || props.selectedClient?.first_name
        || props.currentUser.first_name,
      scheduledWorkoutId: props.route?.params?.scheduledWorkoutId,
      program_start_date: props.route?.params?.program_start_date,
      program_end_date: props.route?.params?.program_end_date,
      isTrainer: this.props.currentUser.role === ROLES.TRAINER,
      comingFrom: this.props.route?.params?.comingFrom || '',
    };
  }

  componentDidMount() {
    this.setNavigationOptions();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      const { refreshPage = true } = this.props.route?.params;
      if (refreshPage) {
        this.getWorkoutSummary();
      }
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  onPressExercise = (exerciseItem) => {
    const name = 'ExerciseDetails';
    const params = {
      exercise: exerciseItem,
      isEditable: false,
      isEditNameIconShow: false,
      comingFrom: 'SummaryCard',
    };
    this.props.navigation.navigate({ name, params, merge: true });
  };

  setNavigationOptions = () => {
    this.props.navigation.setOptions({
      headerTransparent: true,
      headerShown: false,
    });
  };

  getWorkoutSummary = async () => {
    const {
      scheduledWorkoutId,
      isTrainer,
      program_start_date,
      program_end_date,
    } = this.state;
    let userId;
    if (isTrainer) {
      userId = this.state.clientUserId || this.props.selectedClient?.id;
    }
    try {
      const workoutSummaryResult = await nasm.api.getWorkoutSummary({
        scheduledWorkoutId,
        userId,
        program_start_date,
        program_end_date,
      });
      const { workout_activity, workout_feedback } = workoutSummaryResult;
      const isFeedbackGiven = isTrainer || workout_feedback?.message?.length > 0;
      const goal = (workout_activity.exercises_complete
          / workout_activity.exercises_total)
        * 100;
      this.setState(
        {
          workoutSummaryResult,
          goal: Math.ceil(goal),
          isFeedbackGiven,
        },
        () => {
          if (!isFeedbackGiven) {
            const data = {
              message: '',
              rating: 0,
            };
            this.setFeedback(data);
          }
        },
      );
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || error,
        [
          {
            text: 'OK',
            onPress: () => {
              this.returnToDashboard();
            },
          },
        ],
        { cancelable: false },
      );
    } finally {
      this.setState({ showLoading: false }, () => {
        track('workout_summary', {
          user_id: this.props.currentUser?.id,
          user_schedule_workout_id: this.state.scheduledWorkoutId,
          workout_name:
            this.state.workoutSummaryResult?.workout_activity?.workout_name,
          completion_rate: `${this.state.goal}%`,
        });
      });
    }
  };

  getFormattedDuration = (duration) => {
    if (duration) {
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      return `${`0${hours}`.slice(-2)}:${`0${minutes}`.slice(-2)}`;
    }
    return '00:00';
  };

  setFeedback = (data) => {
    const { workoutSummaryResult } = this.state;
    if (workoutSummaryResult) {
      const updatedWorkoutResult = {
        ...workoutSummaryResult,
        workout_feedback: {
          ...workoutSummaryResult.workout_feedback,
          message: data?.message,
          rating: data?.rating,
        },
      };
      this.setState({ workoutSummaryResult: updatedWorkoutResult });
    }
  };

  getExerciseData = (item) => {
    let exData = {
      ...item,
      name: getExerciseName(this.props.currentUser, item),
    };
    if (
      item?.exercise_results
      && item?.exercise_results?.scheduled_progression_regression_exercise_id
    ) {
      const completeExer = item.progressions_regressions.find(
        (ex) => ex.id
          === item?.exercise_results?.scheduled_progression_regression_exercise_id,
      );
      if (completeExer) {
        exData = {
          ...exData,
          exerciseType: completeExer?.exerciseType,
        };
      }
    }
    return exData;
  };

  getVariableName = (variableName) => {
    let displayName = variableName;
    if (variableName === 'dur_seconds') {
      displayName = 'Duration';
    } else if (variableName === 'rest_tempo') {
      displayName = 'Rest Tempo';
    } else if (variableName === 'exercise_sides') {
      displayName = 'Sides';
    }
    return displayName;
  };

  getExerciseVariables = (item) => {
    const { exercise_results } = item;
    if (exercise_results) {
      let isAnyVariableModified = false;
      const variablesData = Object.entries(exercise_results).filter(
        (variable) => !variable[0].includes('units')
          && !variable[0].includes('total_dur_seconds')
          && !variable[0].includes('id')
          && !variable[0].includes('at'),
      );
      const variables = variablesData.map((variable) => {
        const variableName = variable[0];
        let variableAssignedValue = item[variableName];
        let variableAdjustedValue = variable[1];

        // Checking if the variable is modified
        if (
          variableAssignedValue !== null
          && variableAdjustedValue !== null
          && variableAssignedValue !== variableAdjustedValue
        ) {
          isAnyVariableModified = true;
        }

        // Retrieving display values for some specific variables
        if (variableName === 'tempo') {
          variableAssignedValue = getTempoStringForExercise({
            tempo: variableAssignedValue,
          });
          variableAdjustedValue = getTempoStringForExercise({
            tempo: variableAdjustedValue,
          });
        } else if (variableName === 'rest_tempo') {
          variableAssignedValue = getRestTempoStringForExercise({
            rest_tempo: variableAssignedValue,
          });
          variableAdjustedValue = getRestTempoStringForExercise({
            rest_tempo: variableAdjustedValue,
          });
        } else if (variableName === 'exercise_sides') {
          variableAssignedValue = getSideStringForExercise({
            exercise_sides: variableAssignedValue,
          });
          variableAdjustedValue = getSideStringForExercise({
            exercise_sides: variableAdjustedValue,
          });
        }

        // Checking for variable units
        let originalVariableUnit = '';
        if (item[`${variableName}_units`]) {
          originalVariableUnit = item[`${variableName}_units`];
        }

        let modifiedVariableUnit = '';
        if (originalVariableUnit) {
          modifiedVariableUnit = `${exercise_results[`${variableName}_units`]}`;
        }

        let assignedVariableWithUnit = variableAssignedValue;
        let adjustedVariableWithUnit = variableAdjustedValue;

        if (originalVariableUnit) {
          assignedVariableWithUnit = `${variableAssignedValue} (${originalVariableUnit})`;
        }

        if (modifiedVariableUnit) {
          adjustedVariableWithUnit = `${variableAdjustedValue} (${modifiedVariableUnit})`;
        }

        return this.renderModifications(
          this.getVariableName(variableName),
          assignedVariableWithUnit ?? 'N/A',
          adjustedVariableWithUnit ?? 'N/A',
        );
      });
      if (isAnyVariableModified) {
        return variables;
      }
    }
    return null;
  };

  gotoFeedback = () => {
    const {
      comingFrom,
      isTrainer,
      workoutSummaryResult,
      isFeedbackGiven,
      scheduledWorkoutId,
    } = this.state;
    this.props.navigation.navigate({
      name: 'GiveFeedback',
      merge: true,
      params: {
        isFeedbackGiven,
        scheduledWorkoutId,
        comingFrom,
        isTrainer,
        feedBack: workoutSummaryResult?.workout_feedback?.message,
        feedbackRating: workoutSummaryResult?.workout_feedback?.rating,
        workout_date: workoutSummaryResult?.workout_activity?.workout_date,
        onFeedbackGiven: (feedback) => this.setFeedback(feedback),
      },
    });
  };

  returnToDashboard = async () => {
    if (this.state.comingFrom === 'GuidedWorkout') {
      this.props.navigation.popToTop();
    } else {
      this.goBack();
    }
  };

  goBack = (popCount = 1) => {
    this.props.navigation.pop(popCount);
  };

  modifyWorkout = () => {
    const hasProgramName = false;
    this.props.navigation.navigate({
      name: hasProgramName ? 'EditProgram' : 'EditWorkout',
      params: null,
      merge: true,
    });
  };

  viewWorkout = async () => {
    const clientId = this.props.currentUser.role === ROLES.CLIENT
      ? this.props.currentUser.id
      : this.props.route?.params?.userId;

    if (!clientId) {
      Alert.alert(
        "Sorry, we're not able to find any workout details for this client.",
      );
      return;
    }

    const scheduleWorkoutId = this.props.route?.params?.scheduledWorkoutId;
    try {
      const workoutData = await nasm.api.getScheduleWorkoutDetailById({
        clientId,
        user_schedule_workout_id: scheduleWorkoutId,
        program_start_date: this.state.program_start_date,
        program_end_date: this.state.program_end_date,
      });
      this.props.navigation.navigate({
        name: 'WorkoutOverview',
        params: { workoutData },
        merge: true,
      });
    } catch (e) {
      Alert.alert('Error', 'Unable to open scheduled workout details');
    }
  };

  saveWorkoutFeedback = async () => {
    const {
      isTrainer,
      workoutSummaryResult,
      isFeedbackGiven,
      scheduledWorkoutId,
    } = this.state;
    if (isTrainer || isFeedbackGiven) {
      this.returnToDashboard();
      return;
    }
    try {
      this.setState({ showLoading: true });
      const feedbackObj = {
        user_schedule_workout_id: scheduledWorkoutId,
        message: workoutSummaryResult?.workout_feedback?.message,
        rating: workoutSummaryResult?.workout_feedback?.rating,
      };
      await nasm.api.updateWorkoutFeedback(feedbackObj);
    } catch (error) {
      Alert.alert(
        'Error',
        error.message
          || error
          || "Your feedback couldn't be posted. Please try again.",
      );
    } finally {
      this.setState({ showLoading: false }, () => {
        this.returnToDashboard();
      });
    }
  };

  renderSeparator = () => <View style={styles.separatorStyle} />;

  renderHeaderComponent = () => (
    <View>
      {this.renderHeader()}
      {this.renderContent()}
    </View>
  );

  renderHeader = () => (
    <ImageBackground source={blueBanner} style={styles.imageBg}>
      <View style={styles.headerContainer}>
        <View style={styles.headerView}>
          <TouchableOpacity
            onPress={() => this.goBack(this.state.comingFrom === 'GuidedWorkout' ? 2 : 1)}
          >
            <Image source={closeIcon} style={styles.closeImgStyle} />
          </TouchableOpacity>
          <Text style={styles.modifiedDate}>
            {`${moment(
              this.state.workoutSummaryResult?.workout_activity?.workout_date,
            ).format('M/D/YY')}`}
          </Text>
        </View>
        <Text style={styles.headerTitle}>
          {this.state.workoutSummaryResult?.workout_activity?.workout_name}
        </Text>
        {this.renderActivityCard()}
      </View>
    </ImageBackground>
  );

  renderActivityCard = () => (
    <View style={styles.cardStyle}>
      <View>
        <Text style={styles.cardTitle}>Activity</Text>
      </View>
      <View style={styles.cardInnerContainer}>
        <View style={styles.cardItems}>
          <Text style={styles.cardTextTitle}>
            {this.getFormattedDuration(
              this.state.workoutSummaryResult?.workout_activity
                ?.workout_duration,
            )}
          </Text>
          <Text style={styles.cardbottomTitle}>Duration</Text>
        </View>
        <AnimatedCircularProgress
          size={90}
          width={6}
          fill={this.state.goal}
          lineCap="round"
          arcSweepAngle={270}
          rotation={225}
          tintColor={colors.azure}
          backgroundColor={colors.cyanBlue}
        >
          {() => (
            <View style={[styles.cardItems, { marginTop: 17 }]}>
              <Text style={styles.cardTextTitle}>{`${this.state.goal}%`}</Text>
              <Text style={styles.cardbottomTitle}>Goal</Text>
            </View>
          )}
        </AnimatedCircularProgress>
        <View style={styles.cardItems}>
          <Text style={styles.cardTextTitle}>
            {this.state.workoutSummaryResult?.workout_activity?.exercises_total}
          </Text>
          <Text style={styles.cardbottomTitle}>Exercises</Text>
        </View>
      </View>
    </View>
  );

  renderModifications = (name, assigned, adjusted) => (
    <View>
      <View style={styles.innerMainView}>
        <View style={styles.container}>
          <Text style={styles.listData}>{name}</Text>
        </View>
        <View style={styles.container}>
          <Text style={styles.listData}>{assigned}</Text>
        </View>
        <View style={styles.container}>
          <Text style={styles.listData}>{adjusted}</Text>
        </View>
      </View>
    </View>
  );

  renderExerciseVariables = (item) => (
    <View>
      <View style={styles.listHeader}>
        <View style={styles.container}>
          <Text style={styles.listTitle}>Name</Text>
        </View>
        <View style={styles.container}>
          <Text style={styles.listTitle}>Assigned</Text>
        </View>
        <View style={styles.container}>
          <Text style={styles.listTitle}>Adjusted</Text>
        </View>
      </View>
      <View>{this.getExerciseVariables(item)}</View>
    </View>
  );

  renderItem = ({ item }) => {
    const exercise = this.getExerciseData(item);
    return (
      <View style={styles.container}>
        <View style={styles.itemListView}>
          <View style={styles.innerListView}>
            <Image
              source={
                exercise?.image_url ? { uri: exercise?.image_url } : noImage
              }
              style={styles.listImageStyle}
            />
          </View>
          <View style={styles.itemTextView}>
            <View style={styles.itemView}>
              <TouchableOpacity
                activeOpacity={0.6}
                onPress={() => this.onPressExercise(item)}
                style={styles.nameRow}
              >
                <View>
                  <Text style={styles.exerciseName()}>{exercise?.name}</Text>
                  {exercise?.exerciseType ? (
                    <Text style={styles.exerciseName(true)}>
                      {exercise?.exerciseType}
                    </Text>
                  ) : null}
                </View>
                <Image source={rightArrow} style={styles.arrow} />
              </TouchableOpacity>
              {item.is_complete && this.getExerciseVariables(item) ? (
                this.renderExerciseVariables(exercise)
              ) : (
                <Text style={styles.statusStyle(item.is_complete)}>
                  {item.is_complete ? 'Complete' : 'Incomplete'}
                </Text>
              )}
            </View>
          </View>
        </View>
      </View>
    );
  };

  renderGiveFeedbackView = () => {
    if (this.props.currentUser.role === ROLES.TRAINER) {
      return null;
    }
    return (
      <View style={styles.feedbackView}>
        <View style={styles.feedbackContainer}>
          <Text style={styles.feedbackTextStyle}>Give Feedback</Text>
        </View>
        <TouchableOpacity
          activeOpacity={0.6}
          onPress={this.gotoFeedback}
          style={styles.searchHeaderLight}
        >
          <Text style={styles.feedbackMessage}>
            Share your thoughts (Optional)
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderFeebackView = () => {
    const rating = this.state.workoutSummaryResult?.workout_feedback?.rating;
    const feedback = this.state.workoutSummaryResult?.workout_feedback?.message;
    const emoji = ratings.filter((e) => e.rating === rating);
    return (
      <View>
        {feedback && rating ? (
          <TouchableOpacity
            style={styles.feedbackReviewContainer}
            onPress={this.gotoFeedback}
          >
            <View style={styles.userFeedbackView}>
              {emoji && emoji[0] && emoji[0].icon && (
                <Image source={emoji[0].icon} style={styles.emojiStyle} />
              )}
              <Text style={styles.feedbackUserName}>
                {`${this.state.clientUserName}'s`}
                {' '}
                Feedback
              </Text>
            </View>
            <Text numberOfLines={2} style={styles.feedbackData}>
              {feedback}
            </Text>
          </TouchableOpacity>
        ) : (
          this.renderGiveFeedbackView()
        )}
      </View>
    );
  };

  renderContent = () => (
    <View style={styles.content}>
      {this.renderFeebackView()}
      {this.state.workoutSummaryResult?.scheduled_exercises?.length > 0 && (
        <>
          <View style={styles.modificationsView}>
            <Text style={styles.modificationsText}>Modifications</Text>
          </View>
        </>
      )}
    </View>
  );

  renderButton = () => {
    if (this.state.comingFrom === 'GuidedWorkout') {
      return (
        <View style={styles.buttonView}>
          <TouchableOpacity
            activeOpacity={0.6}
            onPress={() => {
              this.saveWorkoutFeedback();
            }}
            style={styles.doneButton}
          >
            <Text style={styles.bottomText}>Done</Text>
          </TouchableOpacity>
        </View>
      );
    }
    return (
      <View style={styles.buttonView}>
        <TouchableOpacity
          activeOpacity={0.6}
          style={styles.bottomButtons}
          onPress={this.viewWorkout}
        >
          <Text style={[styles.bottomText, { color: colors.subGrey }]}>
            View
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  render() {
    return (
      <View style={styles.container}>
        <FlatList
          data={this.state.workoutSummaryResult?.scheduled_exercises}
          keyExtractor={(item) => item.id}
          ItemSeparatorComponent={this.renderSeparator}
          renderItem={this.renderItem}
          ListHeaderComponent={this.renderHeaderComponent}
          showsVerticalScrollIndicator={false}
          style={{ ...backgroundColorStyle }}
        />
        {this.renderButton()}
        <SafeAreaView />
        {this.state.showLoading && (
          <LoadingSpinner
            visible
            backgroundColor={colors.buttonBorderDisabled}
          />
        )}
      </View>
    );
  }
}

const backgroundColorStyle = { backgroundColor: colors.white };

const styles = StyleSheet.create({
  container: {
    flex: 1,
    ...backgroundColorStyle,
  },
  imageBg: {
    paddingTop: 20,
  },
  headerContainer: {
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerView: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  closeImgStyle: {
    width: 35,
    height: 35,
    marginVertical: 25,
  },
  modifiedDate: {
    fontSize: 12,
    fontFamily: 'Avenir-Medium',
    color: colors.white,
    fontWeight: '700',
  },
  headerTitle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 25,
  },
  cardStyle: {
    ...backgroundColorStyle,
    ...shadow,
    borderRadius: 15,
    marginBottom: -50,
    marginTop: 20,
    paddingBottom: 30,
    width: '100%',
  },
  cardTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Black',
    fontSize: 20,
    marginHorizontal: 20,
    marginTop: 20,
  },
  listTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Black',
    fontSize: 13,
  },
  cardInnerContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  cardItems: {
    flex: 1,
    alignItems: 'center',
    marginTop: 22,
    ...backgroundColorStyle,
  },
  cardTextTitle: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 20,
  },
  cardbottomTitle: {
    marginVertical: 7,
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
  },
  separatorStyle: {
    backgroundColor: colors.subGrey,
    height: 0.5,
    marginHorizontal: 10,
  },
  content: {
    backgroundColor: colors.white,
    paddingHorizontal: 20,
    marginTop: 40,
    flex: 1,
  },
  feedbackView: {
    ...backgroundColorStyle,
  },
  feedbackContainer: {
    marginTop: 20,
  },
  feedbackReviewContainer: {
    marginTop: 20,
  },
  userFeedbackView: {
    flexDirection: 'row',
  },
  emojiStyle: {
    width: 23,
    height: 23,
  },
  feedbackUserName: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    marginHorizontal: 10,
  },
  feedbackData: {
    fontFamily: 'Avenir-ROman',
    fontSize: 13,
    color: colors.black,
    marginTop: 12,
    lineHeight: 20,
  },
  searchHeaderLight: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 5,
    paddingTop: 5,
    borderWidth: 1,
    borderColor: colors.silver,
    marginTop: 10,
  },
  feedbackTextStyle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  feedbackMessage: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    width: '100%',
    alignItems: 'center',
    marginHorizontal: 10,
    color: colors.subGrey,
    paddingVertical: 5,
    borderColor: colors.lightGrey,
  },
  modificationsView: {
    marginTop: 30,
  },
  modificationsText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 16,
    color: colors.black,
  },
  innerListView: {
    width: 76,
    height: 53,
    borderRadius: 7,
  },
  listImageStyle: {
    borderRadius: 5,
    width: '100%',
    height: '100%',
    marginHorizontal: 10,
  },
  buttonView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginVertical: 20,
    alignItems: 'center',
  },
  doneButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 24,
    borderWidth: 2,
    alignItems: 'center',
    borderColor: colors.greenselect,
    backgroundColor: colors.greenselect,
  },
  bottomButtons: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 24,
    borderWidth: 2,
    alignItems: 'center',
    borderColor: colors.buttonBorderDisabled,
    backgroundColor: colors.white,
  },
  bottomText: {
    color: colors.white,
    fontFamily: 'Avenir',
    fontSize: 16,
    fontWeight: '900',
  },
  itemView: {
    marginLeft: 11,
    marginRight: 11,
    justifyContent: 'center',
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  arrow: {
    tintColor: colors.fillDarkGrey,
    width: curvedScale(20),
    height: curvedScale(20),
  },
  exerciseName: (hasProReg) => ({
    fontFamily: hasProReg ? 'Avenir-Heavy' : 'Avenir-Medium',
    fontSize: 15,
    color: hasProReg ? colors.azure : colors.black,
    textTransform: 'capitalize',
    marginTop: 5,
  }),
  listData: {
    fontFamily: 'Avenir-Medium',
    fontSize: 13,
    color: colors.black,
    textTransform: 'capitalize',
  },
  statusStyle: (isComplete) => ({
    marginTop: 3,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: isComplete ? colors.greenselect : colors.nasmRed,
    opacity: isComplete ? 0.9 : 0.7,
  }),
  innerMainView: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: 3,
  },
  listHeader: {
    flex: 1,
    flexDirection: 'row',
    marginTop: 11,
  },
  itemListView: {
    flexDirection: 'row',
    flex: 1,
    marginHorizontal: 15,
    marginVertical: 10,
  },
  itemTextView: {
    flex: 1,
    marginHorizontal: 10,
  },
});

SummaryCard.propTypes = propTypes;
SummaryCard.defaultProps = defaultProps;

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  selectedClient: state.selectedClient,
});

export default connect(mapStateToProps)(SummaryCard);
