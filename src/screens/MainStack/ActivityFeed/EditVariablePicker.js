import React, { Component } from 'react';
import {
  StyleSheet,
  SafeAreaView,
  View,
  Text,
  TouchableOpacity,
  BackHandler,
  Alert,
} from 'react-native';
import { Picker as CurvedPicker } from 'react-native-wheel-pick';
import PropTypes from 'prop-types';
import { colors } from '../../../styles';

const propTypes = {
  route: PropTypes.shape({
    params: PropTypes.shape({
      title: PropTypes.string,
      data: PropTypes.array,
      selectedValues: PropTypes.array,
      onDismiss: PropTypes.func,
      onConfirm: PropTypes.func,
      hasMeasurementUnit: PropTypes.bool,
    }),
  }).isRequired,
  navigation: PropTypes.shape({
    goBack: PropTypes.func,
  }).isRequired,
};

class EditVariablePicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      title: props.route?.params?.title || '',
      pickerData: props.route?.params?.data || [],
      originalPickerData: props.route?.params?.data || [],
      selectedValues: props.route?.params?.selectedValues || [],
      hasMeasurementUnit: props.route?.params?.hasMeasurementUnit || false,
    };
    this.useCalculatedFlex = this.state.pickerData.length > 3 && this.state.hasMeasurementUnit;
    this.measurementUnitIndex = this.state.pickerData.length - 1;
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onCancel);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onCancel);
  }

  goBack = () => {
    this.props.navigation.goBack();
  };

  onCancel = () => {
    const onDismiss = this.props.route?.params?.onDismiss;
    if (onDismiss) {
      onDismiss();
    }
    this.goBack();
  };

  onConfirmValue = () => {
    const { selectedValues, title } = this.state;
    if (this.getZeroValuesCount() === selectedValues.length) {
      Alert.alert('Error', `${title} cannot be zero.`);
      return;
    }
    if (this.props.route?.params?.onConfirm) {
      this.props.route?.params?.onConfirm(selectedValues);
    }
    this.goBack();
  };

  getZeroValuesCount = () => {
    const { selectedValues } = this.state;
    let count = 0;
    if (selectedValues.length > 1) {
      selectedValues.forEach((value) => {
        const intValue = parseInt(value, 10);
        if (Number.isNaN(intValue) || intValue < 1) {
          count += 1;
        }
      });
    }
    return count;
  };

  getSelectedValue = (index) => {
    const { selectedValues } = this.state;
    const value = selectedValues[index];
    return value;
  };

  updatePickerData = () => {
    const {
      title,
      selectedValues,
      pickerData,
      originalPickerData,
    } = this.state;
    if (title.toLowerCase() === 'duration') {
      /*
       * Maximum allowed duration input while creating an exercise can be a 5-digits number,
       * so max input can be 99999 (seconds).
       * Selectabe Hours = 27
       * Selectabe Minutes = 46 (when selected hour is 27)
       * Selectabe Seconds = 39 (when selected hour is 27)
       */
      const selectedHour = selectedValues[0];
      if (selectedHour.includes('27')) {
        const hours = pickerData[0];
        const minutes = pickerData[1].filter((min) => parseInt(min, 10) < 47);
        const seconds = pickerData[2].filter((sec) => parseInt(sec, 10) < 40);
        const filteredData = [hours, minutes, seconds];
        this.setState({ pickerData: filteredData });
      } else {
        this.setState({ pickerData: originalPickerData });
      }
    }
  };

  renderButton = () => (
    <TouchableOpacity
      onPress={this.onConfirmValue}
      style={styles.bottomButtonParent}
      activeOpacity={0.6}
    >
      <View style={styles.bottomButton}>
        <Text style={styles.labelConfirm}>Confirm</Text>
      </View>
    </TouchableOpacity>
  );

  render() {
    const { title, pickerData, selectedValues } = this.state;
    return (
      <View style={styles.container}>
        <TouchableOpacity
          onPress={this.onCancel}
          activeOpacity={1}
          style={styles.background}
        />
        <View activeOpacity={1} style={styles.sheet}>
          <View style={styles.sheet}>
            <View style={styles.titleView}>
              <Text style={styles.title}>{title}</Text>
            </View>
            <View style={styles.pickerView}>
              {pickerData.map((data, index) => (
                <CurvedPicker
                  style={[
                    styles.picker,
                    this.useCalculatedFlex
                      && this.measurementUnitIndex === index
                      && styles.calculatedFlexStyle,
                  ]}
                  itemStyle={styles.pickerItem}
                  textSize={20}
                  selectedValue={this.getSelectedValue(index)}
                  pickerData={data}
                  selectBackgroundColor={colors.transparent}
                  onValueChange={(newValue) => {
                    const values = [...selectedValues];
                    values[index] = newValue;
                    this.setState({ selectedValues: values }, () => {
                      this.updatePickerData();
                    });
                  }}
                />
              ))}
            </View>
            {this.renderButton()}
          </View>
        </View>
        <SafeAreaView style={styles.safeArea} />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  safeArea: {
    backgroundColor: colors.pickerBg,
  },
  background: {
    height: '100%',
    backgroundColor: colors.pickerOverlayBg,
  },
  sheet: {
    backgroundColor: colors.pickerBg,
    borderTopRightRadius: 17,
    borderTopLeftRadius: 17,
  },
  titleView: {
    borderBottomWidth: 1,
    borderColor: colors.bordergrey,
    paddingVertical: 15,
  },
  title: {
    fontFamily: 'SFProText-Regular',
    fontSize: 16,
    textAlign: 'center',
    color: colors.fillDarkGrey,
    textTransform: 'capitalize',
  },
  bottomButton: {
    width: '80%',
    borderColor: colors.duskBlue,
    backgroundColor: colors.duskBlue,
    borderRadius: 28,
    borderWidth: 1,
    paddingVertical: 12,
  },
  bottomButtonParent: {
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  labelConfirm: {
    textAlign: 'center',
    fontSize: 17,
    fontWeight: '600',
    color: colors.white,
  },
  pickerView: {
    backgroundColor: colors.pickerBg,
    flexDirection: 'row',
    alignItems: 'center',
  },
  picker: {
    flex: 2,
    backgroundColor: colors.pickerBg,
  },
  calculatedFlexStyle: {
    flex: 4,
  },
  pickerItem: {
    flex: 1,
    fontSize: 18,
  },
});

EditVariablePicker.propTypes = propTypes;

export default EditVariablePicker;
