import { connect } from 'react-redux';
import React, { Component } from 'react';
import {
  SafeAreaView,
  View,
  StyleSheet,
  Image,
  Text,
  Alert,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { KeyboardAwareFlatList } from 'react-native-keyboard-aware-scroll-view';
import PropTypes from 'prop-types';
import nasm from '../../../dataManager/apiConfig';
import HeaderSearchBar from '../../../components/HeaderSearchBar';
import WorkoutActivityCard from './WorkoutActivityCard';
import { colors } from '../../../styles';
import { ROLES } from '../../../constants';
import { selectClient } from '../../../reducers/selectedClientReducer';
import DailyReadinessFeedCard from './DailyReadinessFeedCard';

const allImage = require('../../../resources/allGray.png');
const ftuIcon = require('../../../resources/iconNoActivity.png');

const propTypes = {
  navigation: PropTypes.object.isRequired,
  route: PropTypes.object.isRequired,
  currentUser: PropTypes.object,
  selectedClient: PropTypes.object,
  selectClient: PropTypes.func,
};

const defaultProps = {
  currentUser: null,
  selectedClient: null,
  selectClient: null,
};

const filterOptions = {
  all: 'All',
  feedback: 'Feedback',
  completed: 'Completed',
  incompleted: 'Incomplete',
};

class ActivityFeed extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: true,
      hasNextPage: true,
      refreshing: false,
      searchStr: '',
      workoutActivityResult: [],
      page: 1,
      size: 20,
      filters: null,
      comingFrom: this.props.route?.params?.comingFrom || '',
      club_id: this.props.trainerActiveProfile?.ClubId,
      location_id: this.props.trainerActiveProfile?.Locations?.Id,
    };
  }

  componentDidMount() {
    this.setHeaderTitle();
    nasm.api.checkOrUpdateActivity('put');
    this.onRefresh();
  }

  onScroll = () => {
    const { hasNextPage, loading, page } = this.state;
    if (hasNextPage && !loading) {
      this.setState(
        {
          loading: true,
          page: page + 1,
        },
        () => {
          this.getWorkoutActivity();
        },
      );
    }
  };

  onRefresh = () => {
    this.setState(
      {
        refreshing: true,
        page: 1,
      },
      () => {
        this.getWorkoutActivity();
      },
    );
  };

  onFilterSelected = (value) => {
    const selectedFilter = Object.keys(filterOptions).filter((key) => key.includes(value.toLowerCase()));
    this.setState({ filters: selectedFilter[0].toLowerCase() }, () => {
      this.onRefresh();
    });
  };

  setHeaderTitle = () => {
    let title = 'Workout Activity';
    if (this.state.comingFrom === 'DashboardTrainer') {
      title = 'Activity Feed';
    }
    this.props.navigation.setOptions({ title });
  };

  getUserId = () => {
    let userId = null;
    if (this.props.currentUser.role === ROLES.CLIENT) {
      userId = this.props.currentUser.id;
    } else if (
      this.state.comingFrom !== 'DashboardTrainer'
      && this.props.selectedClient?.id
    ) {
      userId = this.props.selectedClient?.id;
    }
    return userId;
  };

  getWorkoutActivity = async () => {
    const {
      page,
      size,
      searchStr,
      filters,
      workoutActivityResult,
      club_id,
      location_id,
    } = this.state;
    const userId = this.getUserId();
    let response;
    try {
      const reqObj = {
        userId,
        page,
        size,
        searchStr,
        filters,
        club_id,
        location_id,
      };
      response = await nasm.api.getAllActivityFeed(reqObj);
      this.setState({
        workoutActivityResult:
          page === 1 ? response : [...workoutActivityResult, ...response],
        refreshing: false,
        loading: false,
        hasNextPage: response.length >= size,
      });
    } catch (error) {
      Alert.alert('Error', error.message || error);
    } finally {
      this.setState({ loading: false });
    }
  };

  goToWorkoutSummary = async (item) => {
    /*
        1. Trainer can select a client from their client list where redux will
        store the client they selected
        2. Trainer can select from the bell icon and click on an activity. Client will not be in the redux store.
        3. Client can view their own workout activities
     */
    const clientUserId = this.props.currentUser.role === ROLES.CLIENT
      ? this.props.currentUser.id
      : item?.user?.id ?? this.props?.selectedClient?.id;

    if (!clientUserId) {
      Alert.alert(
        "Sorry, we're not able to find any workout activity for this client.",
      );
      return;
    }

    const client = await nasm.api.getUserById(clientUserId);
    this.props.selectClient(client);

    this.props.navigation.navigate({
      name: 'SummaryCard',
      params: {
        userId: clientUserId,
        scheduledWorkoutId:
          item.user_schedule_workout_id
          ?? item.workout_feedback.user_schedule_workout_id,
        program_start_date:
          item.workout_feedback?.user_schedule_workout?.user_schedule
            ?.start_date,
        program_end_date:
          item.workout_feedback?.user_schedule_workout?.user_schedule?.end_date,
      },
      merge: true,
    });
  };

  openFilterScreen = () => {
    this.props.navigation.navigate('ActivityFeedFilters', {
      updateFilters: this.updateFilters,
      selectedFilters: this.state.filters,
    });
  };

  updateFilters = (filters) => {
    this.setState({ filters }, () => {
      setTimeout(() => {
        this.onRefresh();
      }, 200);
    });
  };

  openDetails = (item) => {
    this.props.navigation.navigate('ReadinessDetails', {
      item,
      userIdOfClient: item.user.id,
    });
  };

  renderFilterButton = () => (
    <TouchableOpacity style={styles.filterView} onPress={this.openFilterScreen}>
      <Image source={allImage} />
    </TouchableOpacity>
  );

  renderHeader = () => (
    <View style={styles.searchBox}>
      <HeaderSearchBar
        placeholder="Search"
        searchText={this.state.searchStr}
        renderRightView={this.renderFilterButton}
        clearable
        paddingTop={10}
        light
        shadow={false}
        onChangeText={(searchStr) => this.setState(
          {
            searchStr,
            loading: this.state.workoutActivityResult.length === 0,
          },
          () => {
            this.onRefresh();
          },
        )}
      />
    </View>
  );

  renderWorkoutActivity = ({ item }) => {
    if (item.activity_type === 'daily_readiness_assessment') {
      return (
        <DailyReadinessFeedCard
          item={item}
          gotoDetail={() => this.openDetails(item)}
        />
      );
    }
    return (
      <WorkoutActivityCard
        item={item}
        goToWorkoutSummary={() => this.goToWorkoutSummary(item)}
        showUsersInfo={!!item.user}
      />
    );
  };

  // First time user experience: no workout activity
  // has been recorded yet
  renderEmptyComponent = () => (
    <View style={styles.emptyView}>
      <Image source={ftuIcon} />
      <Text style={styles.emptyHeader}>No client workout activity</Text>
      <Text style={styles.emptySubheader}>
        When workouts are performed, feedback and modifications will show up
        here.
      </Text>
    </View>
  );

  renderNoSearchResults = () => (
    <View style={styles.noResultsFoundCont}>
      <Text style={styles.noResultsFoundText}>No Results Found</Text>
    </View>
  );

  renderLoader = () => (
    <View style={styles.loaderView}>
      <ActivityIndicator animating={this.state.loading} size="large" />
    </View>
  );

  renderActivityList = () => {
    const { refreshing, loading, workoutActivityResult } = this.state;
    return (
      <KeyboardAwareFlatList
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={this.onRefresh} />
        }
        data={workoutActivityResult}
        renderItem={this.renderWorkoutActivity}
        keyExtractor={(item) => item.activity_id}
        ListEmptyComponent={
          loading || refreshing ? null : this.renderNoSearchResults
        }
        ListFooterComponent={this.renderLoader}
        onEndReached={({ distanceFromEnd }) => {
          if (distanceFromEnd >= 0) {
            this.onScroll();
          }
        }}
        onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  render() {
    const {
      workoutActivityResult, loading, searchStr, filters, refreshing,
    } = this.state;
    const isFtu = workoutActivityResult.length === 0
      && !loading
      && !refreshing
      && !searchStr
      && !filters;
    return (
      <View testID="WorkoutActivty" style={styles.container}>
        {isFtu ? (
          this.renderEmptyComponent()
        ) : (
          <>
            {this.renderHeader()}
            {this.renderActivityList()}
          </>
        )}
        <SafeAreaView style={styles.safeArea} />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyView: {
    flex: 1,
    justifyContent: 'center',
    marginVertical: 10,
    marginHorizontal: 32,
    alignItems: 'center',
  },
  emptyHeader: {
    fontSize: 24,
    fontFamily: 'Avenir-Roman',
    textAlign: 'center',
    marginTop: 8,
  },
  emptySubheader: {
    fontSize: 15,
    fontFamily: 'Avenir-Roman',
    color: colors.fillDarkGrey,
    textAlign: 'center',
  },
  noResultsFoundCont: {
    marginTop: '10%',
  },
  noResultsFoundText: {
    textAlign: 'center',
    fontFamily: 'Avenir-Medium',
    fontSize: 15,
  },
  loaderView: {
    marginVertical: 10,
  },
  searchBox: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 10,
  },
  filterView: {
    marginRight: 24,
  },
});

ActivityFeed.propTypes = propTypes;
ActivityFeed.defaultProps = defaultProps;

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  selectedClient: state.selectedClient,
  trainerActiveProfile: state.trainerActiveProfile,
});

const mapDispatchToProps = {
  selectClient,
};

export default connect(mapStateToProps, mapDispatchToProps)(ActivityFeed);
