import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  Alert,
  Image,
  StyleSheet,
  SectionList,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import lodash from 'lodash.clonedeep';
import nasm from '../../../dataManager/apiConfig';
import HeaderRightButton from '../../../components/HeaderRightButton';
import Button from '../../../components/Button';
import { colors } from '../../../styles';
import { curvedScale } from '../../../util/responsive';

const propTypes = {
  navigation: PropTypes.any.isRequired,
  route: PropTypes.any.isRequired,
};

const defaultProps = {};

const checkboxSelected = require('../../../resources/completed.png');
const checkboxUnselected = require('../../../resources/checkmarkIncomplete.png');

const FILTERS = [
  {
    id: 1,
    title: 'Groups',
    type: 'trainer_groups',
    data: [
      {
        id: 101,
        key: 'individual',
        value: 'Individual',
      },
    ],
  },
  {
    id: 2,
    title: 'Workout Activity',
    type: 'workout_activity',
    data: [
      {
        id: 201,
        key: 'feedback',
        value: 'Feedback',
      },
      {
        id: 202,
        key: 'completed',
        value: 'Completed',
      },
      {
        id: 203,
        key: 'incompleted',
        value: 'Incomplete',
      },
    ],
  },
  {
    id: 3,
    title: 'Readiness Assessment',
    type: 'daily_readiness_assessment',
    data: [
      {
        id: 301,
        key: '5',
        value: 'Readiness Score 5',
      },
      {
        id: 302,
        key: '4',
        value: 'Readiness Score 4',
      },
      {
        id: 303,
        key: '3',
        value: 'Readiness Score 3',
      },
      {
        id: 304,
        key: '2',
        value: 'Readiness Score 2',
      },
      {
        id: 305,
        key: '1',
        value: 'Readiness Score 1',
      },
    ],
  },
];

const initialFilters = {
  trainer_groups: [],
  workout_activity: [],
  daily_readiness_assessment: [],
};

const ActivityFeedFilters = ({ navigation, route }) => {
  const [selectedFilters, setSelectedFilters] = useState(
    route.params.selectedFilters || initialFilters,
  );

  const [allFilters, setFilters] = useState([]);

  useEffect(() => {
    navigation.setOptions({
      title: 'Filter Workouts',
      headerTitleStyle: styles.headerTitleStyle,
      headerRight: renderHeaderRight,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  useEffect(() => {
    getTrainerGroups();
  }, []);

  const getTrainerGroups = async () => {
    try {
      const response = await nasm.api.getTrainerGroups();
      if (response) {
        const filters = lodash(FILTERS);
        response.forEach((group) => {
          filters[0].data.push({
            id: group.id,
            key: group.id,
            value: group.title,
          });
        });
        setFilters([...filters]);
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error?.message || 'Something went wrong! Please try again later.',
      );
    }
  };

  const handleFilterSelection = (itemKey, filterType) => {
    const filters = lodash(selectedFilters);
    const index = filters[filterType].indexOf(itemKey);
    if (index > -1) {
      filters[filterType].splice(index, 1);
    } else {
      filters[filterType].push(itemKey);
    }
    setSelectedFilters(filters);
  };

  const clearFilters = () => {
    setSelectedFilters({
      workout_activity: [],
      daily_readiness_assessment: [],
    });
  };

  const saveFilters = () => {
    route?.params?.updateFilters(selectedFilters);
    navigation.pop();
  };

  const renderHeaderRight = () => (
    <HeaderRightButton onPress={clearFilters} title="Clear All" />
  );

  const renderSectionHeader = ({ section: { title } }) => (
    <View style={styles.sectionHeaderContainer}>
      <Text style={styles.sectionHeaderText(13)}>{title}</Text>
    </View>
  );

  const renderFilter = ({ item, section }) => (
    <TouchableOpacity
      style={styles.itemView}
      onPress={() => handleFilterSelection(item.key, section.type)}
    >
      <Text style={styles.sectionHeaderText(17)}>{item.value}</Text>
      <Image
        source={
          selectedFilters[section?.type]?.indexOf(item?.key) > -1
            ? checkboxSelected
            : checkboxUnselected
        }
      />
    </TouchableOpacity>
  );

  const renderItemSeparator = () => <View style={styles.dividerLine} />;

  const renderButton = () => (
    <View style={styles.buttonsContainerView}>
      <Button
        title="Save"
        onPress={saveFilters}
        containerStyle={styles.buttonContainerStyle}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <SectionList
        sections={allFilters}
        renderItem={renderFilter}
        renderSectionHeader={renderSectionHeader}
        ItemSeparatorComponent={renderItemSeparator}
        keyExtractor={(item) => item.id}
        initialNumToRender={11}
        stickySectionHeadersEnabled
      />
      {renderButton()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  headerTitleStyle: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  sectionHeaderContainer: {
    backgroundColor: colors.colorsFillLight2,
    paddingHorizontal: curvedScale(10),
  },
  sectionHeaderText: (fontSize) => ({
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(fontSize),
    fontWeight: '500',
    padding: curvedScale(10),
  }),
  itemView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: curvedScale(10),
    padding: curvedScale(10),
  },
  dividerLine: {
    backgroundColor: colors.lineLightBlue,
    height: 1,
    width: '100%',
  },
  buttonsContainerView: {
    margin: curvedScale(20),
  },
  buttonContainerStyle: {
    borderRadius: curvedScale(50),
  },
});

ActivityFeedFilters.propTypes = propTypes;
ActivityFeedFilters.defaultProps = defaultProps;

export default ActivityFeedFilters;
