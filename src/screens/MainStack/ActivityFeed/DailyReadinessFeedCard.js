import React from 'react';
import {
  Text, View, Image, StyleSheet, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import PropTypes from 'prop-types';
import * as Sentry from '@sentry/react-native';
import { colors } from '../../../styles';
import { curvedScale, scaleHeight } from '../../../util/responsive';
import { getReadinessRatingLabel } from '../DailyReadiness/DailyReadinessConstants';

const noImage = require('../../../resources/defaultProfile.png');
const arrowIcon = require('../../../resources/imgRightArrowGray.png');

const userImageSize = scaleHeight(6);
const iconSize = scaleHeight(2);

const propTypes = {
  gotoDetail: PropTypes.func,
  item: PropTypes.object.isRequired,
  containerStyle: PropTypes.object,
};

const defaultProps = {
  containerStyle: null,
  showUsersInfo: false,
  gotoDetail: null,
};

const DailyReadinessFeedCard = ({ gotoDetail, item, containerStyle }) => {
  if (!item.daily_readiness_assessment) {
    Sentry.captureMessage(
      'ACTIVITY FEED: daily_readiness_assessment is NULL or undefined',
    );
    return null;
  }
  const source = item.user?.avatar_url
    ? { uri: item.user.avatar_url }
    : noImage;

  const getDate = () => moment(item.created_at).format('M/D/YY');

  const getRatingColor = (value) => {
    switch (value) {
      case 1:
        return colors.pink;
      case 2:
        return colors.brickOrange;
      case 3:
        return colors.lightYellow;
      case 4:
        return colors.mildGreen;
      case 5:
        return colors.midGreen;
      default:
        return null;
    }
  };

  const renderUserAvatar = () => {
    const score = parseInt(
      item.daily_readiness_assessment.average_of_assessment_levels,
      10,
    );
    const ratingColor = getRatingColor(score);
    return (
      <View style={{ marginRight: 10 }}>
        <View style={styles.userImageView}>
          <Image style={styles.userImage} source={source} />
        </View>
        <View style={styles.emojiView}>
          <View style={[styles.ratingView, { backgroundColor: ratingColor }]}>
            <Text style={styles.ratingLabel}>{score}</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderNameAndDate = () => (
    <View style={styles.userNameView}>
      <Text numberOfLines={1} style={styles.userName}>
        {item.user?.first_name}
      </Text>
      <View style={styles.dateView}>
        <Text numberOfLines={1} style={styles.date}>
          {getDate()}
        </Text>
      </View>
      <Image style={styles.arrowIcon} source={arrowIcon} />
    </View>
  );

  const renderReadinessScore = () => (
    <View style={styles.statusInnerView}>
      <Text numberOfLines={1} style={styles.userName}>
        {parseFloat(
          item.daily_readiness_assessment.average_of_assessment_levels,
        )?.toFixed(1)}
        {' Avg. '}
        {getReadinessRatingLabel(
          item.daily_readiness_assessment.average_of_assessment_levels,
        )}
      </Text>
    </View>
  );

  return (
    <TouchableOpacity
      onPress={gotoDetail}
      style={[styles.activityContainer, containerStyle]}
    >
      <View style={styles.horizontalView}>
        {renderUserAvatar()}
        <View style={styles.rowView}>
          {renderNameAndDate()}
          <Text numberOfLines={1} style={styles.assessment}>
            Daily Readiness Assessment
          </Text>
          {renderReadinessScore()}
        </View>
      </View>
    </TouchableOpacity>
  );
};

DailyReadinessFeedCard.propTypes = propTypes;
DailyReadinessFeedCard.defaultProps = defaultProps;

export default DailyReadinessFeedCard;

const styles = StyleSheet.create({
  activityContainer: {
    borderColor: colors.colorsFillLight2,
    borderBottomWidth: 1,
    paddingVertical: 5,
  },
  horizontalView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  rowView: {
    justifyContent: 'space-between',
    flex: 1,
    paddingVertical: 5,
  },
  userImageView: {
    width: userImageSize,
    height: userImageSize,
  },
  userImage: {
    width: '100%',
    height: '100%',
    borderRadius: userImageSize / 2,
  },
  emojiView: {
    width: 25,
    height: 25,
    position: 'absolute',
    bottom: -5,
    right: -5,
    borderRadius: 25 / 2,
    padding: 4,
    backgroundColor: colors.white,
  },
  ratingView: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    borderRadius: 50,
  },
  ratingLabel: {
    color: colors.black,
    fontSize: curvedScale(12),
    fontFamily: 'Avenir-Heavy',
    textAlign: 'center',
  },
  userNameView: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  arrowIcon: {
    width: iconSize,
    height: iconSize,
    marginLeft: 10,
  },
  assessment: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    marginVertical: 3,
    fontSize: curvedScale(12),
  },
  userName: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    flex: 7,
    fontSize: curvedScale(12),
  },
  statusInnerView: {
    flex: 1,
  },
  dateView: {
    flex: 3,
    alignItems: 'flex-end',
  },
  date: {
    color: colors.fillDarkGrey,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(10),
  },
});
