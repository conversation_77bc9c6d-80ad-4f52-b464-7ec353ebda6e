import React, { Component } from 'react';
import {
  View,
  StyleSheet,
  Text,
  Image,
  TouchableOpacity,
  Platform,
  Keyboard,
  SafeAreaView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import PropTypes from 'prop-types';
import analytics from '@react-native-firebase/analytics';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { colors } from '../../../styles';
import { TextInput } from '../../../components';
import nasm from '../../../dataManager/apiConfig';

const exhausted = require('../../../resources/exhausted.png');
const tired = require('../../../resources/tired.png');
const okay = require('../../../resources/okay.png');
const good = require('../../../resources/good.png');
const high = require('../../../resources/high.png');

const ratings = [
  { rating: 1, icon: exhausted },
  { rating: 2, icon: tired },
  { rating: 3, icon: okay },
  { rating: 4, icon: good },
  { rating: 5, icon: high },
];

const RatingEmojis = (ratingProps) => {
  const {
    source, rating, rateWorkout, feedbackRating,
  } = ratingProps;
  const opacity = feedbackRating === rating ? 1 : 0.5;
  return (
    <TouchableOpacity
      activeOpacity={0.6}
      key={rating}
      onPress={() => rateWorkout(rating)}
    >
      <Image style={{ opacity }} source={source} />
    </TouchableOpacity>
  );
};

// PropTypes
const propTypes = {
  navigation: PropTypes.any.isRequired,
  route: PropTypes.shape({
    params: PropTypes.object,
  }).isRequired,
};

const defaultProps = {
  currentUser: null,
};

export default class GiveFeedback extends Component {
  constructor(props) {
    super(props);
    this.state = {
      feedbackRating: props.route?.params?.feedbackRating || '',
      feedBack: props.route?.params?.feedBack || '',
      isFeedbackGiven: props.route?.params?.isFeedbackGiven || false,
      workout_date: props.route?.params?.workout_date,
      loading: false,
    };
  }

  goBack = () => {
    Keyboard.dismiss();
    this.props.navigation.goBack();
  };

  rateWorkout = (value) => {
    Keyboard.dismiss();
    this.setState({ feedbackRating: value });
  };

  onChangeFeeback = (value) => {
    this.setState({ feedBack: value });
  };

  submitFeedback = async () => {
    const { feedbackRating, feedBack } = this.state;
    let message = '';
    if (!feedbackRating) {
      message = 'Please rate the workout';
    } else if (!feedBack) {
      message = 'Please write a feedback';
    }
    if (message.length) {
      Alert.alert('Error', message);
      return;
    }
    await this.saveWorkoutFeedback();
  };

  saveWorkoutFeedback = async () => {
    const {
      comingFrom,
      isTrainer,
      onFeedbackGiven,
      scheduledWorkoutId,
    } = this.props.route?.params;
    const {
      feedBack,
      feedbackRating,
      isFeedbackGiven,
      workout_date,
    } = this.state;
    const feedbackObj = {
      user_schedule_workout_id: scheduledWorkoutId,
      message: feedBack,
      rating: feedbackRating,
      workout_date,
    };
    if (isTrainer || isFeedbackGiven) {
      this.goBack();
    } else if (comingFrom === 'GuidedWorkout' && onFeedbackGiven) {
      this.props.navigation.navigate({
        name: 'SummaryCard',
        params: {
          refreshPage: false,
        },
        merge: true,
      });
      onFeedbackGiven(feedbackObj);
    } else {
      try {
        this.setState({ loading: true });
        await nasm.api.updateWorkoutFeedback(feedbackObj);
        if (onFeedbackGiven) {
          onFeedbackGiven(feedbackObj);
          analytics().logEvent('screen_view', {
            screen_name: 'workout_feedback_client_sent',
          });
        }
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || error
            || "Your feedback couldn't be posted. Please try again.",
        );
      } finally {
        this.setState({ loading: false }, () => {
          this.goBack();
        });
      }
    }
  };

  render() {
    const { feedbackRating, feedBack } = this.state;
    return (
      <View style={[styles.container, styles.customMarginTop]}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={Keyboard.dismiss}
          style={styles.headerView}
        >
          <View style={styles.headerRow}>
            <TouchableOpacity
              style={styles.Btn}
              activeOpacity={0.6}
              onPress={this.goBack}
            >
              <Text style={styles.cancel}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.title}>Give Feedback</Text>
            <View style={styles.Btn}>
              {this.state.loading ? (
                <ActivityIndicator animating color={colors.darkGrey} />
              ) : (
                <TouchableOpacity
                  activeOpacity={0.6}
                  onPress={this.submitFeedback}
                >
                  <Text style={styles.cancel}>Done</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View
            pointerEvents={this.state.isFeedbackGiven ? 'none' : 'auto'}
            style={styles.emojiView}
          >
            <View style={styles.emojiRow}>
              {ratings.map((element) => (
                <RatingEmojis
                  source={element.icon}
                  rateWorkout={this.rateWorkout}
                  feedbackRating={feedbackRating}
                  rating={element.rating}
                />
              ))}
            </View>
            <Text style={styles.workout}>How was your workout?</Text>
          </View>
        </TouchableOpacity>
        <View style={styles.inputView}>
          <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">
            <TextInput
              editable={!this.state.isFeedbackGiven}
              value={feedBack}
              onChangeText={this.onChangeFeeback}
              style={styles.input}
              underlineColorAndroid="transparent"
              placeholderTextColor={colors.fillDarkGrey}
              containerStyle={styles.inputContainer}
              multiline
              placeholder="Share your thoughts (Optional)"
              showIcon={false}
            />
          </KeyboardAwareScrollView>
        </View>
        <SafeAreaView />
      </View>
    );
  }
}

// Export
GiveFeedback.propTypes = propTypes;
GiveFeedback.defaultProps = defaultProps;

// Styles
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  headerView: {
    backgroundColor: colors.loadingStateGray,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  Btn: {
    padding: 20,
  },
  title: {
    fontSize: 17,
    color: colors.black,
    fontFamily: 'SFProText-Medium',
  },
  cancel: {
    fontSize: 17,
    fontFamily: 'SFProText-Regular',
    color: colors.azure,
  },
  emojiView: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderColor: colors.bordergrey,
  },
  emojiRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '70%',
    alignSelf: 'center',
    alignItems: 'center',
  },
  workout: {
    fontSize: 17,
    fontFamily: 'SFProText-Regular',
    color: colors.black,
    alignSelf: 'center',
    marginVertical: 20,
  },
  input: {
    fontFamily: 'Avenir-Roman',
    fontSize: 17,
    color: colors.black,
    width: '100%',
    height: '100%',
    textAlignVertical: 'top',
  },
  inputContainer: {
    borderBottomWidth: 0,
    height: '95%',
  },
  inputView: {
    backgroundColor: colors.white,
    paddingHorizontal: 20,
    flex: 8,
  },
  customMarginTop: {
    marginTop: Platform.OS === 'android' ? 50 : 0,
  },
});
