import mergeWith from 'lodash.mergewith';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
// Components
import {
  Alert,
  FlatList,
  Image,
  LayoutAnimation,
  Platform,
  SectionList,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { connect } from 'react-redux';
import { CalendarWithToggle } from '../../../components';
import Button from '../../../components/Button';
import { launchAvatar, ROLES } from '../../../constants';
import nasm from '../../../dataManager/apiConfig';
import { selectDay } from '../../../reducers/selectedDayReducer';
import { updateCurrentUser } from '../../../actions';
// Styles
import { colors } from '../../../styles';
import { logComponentException } from '../../../util/logging';
import FoodLogCell from './FoodLogCell';

const avatarLogo = require('../../../resources/avatarLogoHorizontalGrey.png');
const nasmEdge = require('../../../resources/nasmEdgeBlackVertical3X.png');

const propTypes = {
  selectDay: PropTypes.func,
  currentUser: PropTypes.shape({
    role: PropTypes.string,
    id: PropTypes.string,
    avatar_connected: PropTypes.number,
  }),
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    client_user: PropTypes.object,
  }).isRequired,
  appointments: PropTypes.array,
};

const defaultProps = {
  selectDay: null,
  currentUser: {},
  appointments: [],
};

class FoodLog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      weekView: true,
      calendarLoading: true,
      scheduleDays: [],
      selectedDay: moment().format('YYYY-MM-DD'),
      foodData: [],
    };
  }

  componentDidMount = () => {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.route.params.updateHeaderRight(() => <View />);
    });
    this.getFoodData();
  };

  componentWillUnmount = () => {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
  };

  avatarFoodLog = async (date, id) => {
    try {
      const response = await nasm.api.getFoodLogData(id, date);
      return response;
    } catch (error) {
      logComponentException('ConnectAvatarNutrition', error);
      Alert.alert('Avatar Nutrition', 'Avatar Nutrition account link invalid', [
        {
          text: 'Ok',
          onPress: async () => {
            const isClient = this.props.currentUser.role === ROLES.CLIENT;
            if (isClient) {
              await nasm.api.getMyUser(this.props.currentUser.id).then((user) => {
                this.props.updateCurrentUser(user);
              });
            }
            this.props.navigation.pop(2);
          },
        },
      ]);
    }
    return null;
  };

  getFoodData = async () => {
    const userId = this.props.currentUser.role === ROLES.TRAINER
      ? this.props.selectedClient?.id
      : this.props.currentUser.id;
    const foodData = await this.avatarFoodLog(this.state.selectedDay, userId);

    if (foodData !== undefined) {
      this.setState({ foodData, calendarLoading: false });
    } else {
      this.setState({ calendarLoading: false });
    }
  };

  getMarkedDates = () => {
    const markedDates = JSON.parse(
      JSON.stringify(this.props.appointments.appointmentsListByDate),
    );

    const booked = {
      key: 'booked',
      color: colors.buttonBlue,
      selectedDotColor: colors.buttonBlue,
    };

    const { scheduleDays, selectedDay } = this.state;

    const completedWorkout = {
      selected: true,
      selectedColor: colors.scheduleDayComplete,
    };
    const missedWorkout = {
      selected: true,
      selectedColor: colors.missedWorkout,
    };
    const scheduledFutureWorkout = {
      selected: true,
      selectedColor: colors.scheduledFutureWorkout,
    };
    const selectedDayBacklight = { selected: true, selectedColor: 'black' };

    const updatedGoals = {
      key: 'updatedGoals',
      color: colors.azure,
      selectedDotColor: colors.azure,
    };
    const updatedNutrition = {
      key: 'updatedNutrition',
      color: colors.peaGreen,
      selectedDotColor: colors.peaGreen,
    };
    const updatedMeasurements = {
      key: 'updatedMeasurements',
      color: colors.rustyRed,
      selectedDotColor: colors.rustyRed,
    };
    const updatedAssessments = {
      key: 'updatedAssessments',
      color: colors.buttonBlue,
      selectedDotColor: colors.buttonBlue,
    };
    const updatedOhsa = {
      key: 'updatedAssessments',
      color: colors.duskBlue,
      selectedDotColor: colors.buttonBlue,
    };

    if (!scheduleDays) {
      return { [selectedDay]: { selected: true } };
    }

    const dotsSessions = [];
    dotsSessions.push(booked);
    Object.keys(markedDates).forEach((key) => {
      markedDates[key] = { dots: dotsSessions };
    });

    const dotsPrograms = [];

    const marked = scheduleDays.reduce((days, scheduleDay) => {
      const workout = scheduleDay.scheduled_workout;
      if (scheduleDay.has_goal) {
        dotsPrograms.push(updatedGoals);
      }
      if (scheduleDay.has_nutrition) {
        dotsPrograms.push(updatedNutrition);
      }
      if (scheduleDay.has_measurement) {
        dotsPrograms.push(updatedMeasurements);
      }
      if (scheduleDay.has_perf_assessment) {
        dotsPrograms.push(updatedAssessments);
      }
      if (scheduleDay.has_assessment) {
        dotsPrograms.push(updatedOhsa);
      }

      let backlight;
      const currentDate = moment();
      if (!moment(scheduleDay.date).isSame(selectedDay, 'day')) {
        if (workout) {
          if (currentDate.isSameOrBefore(moment(workout.workout_date), 'day')) {
            backlight = scheduledFutureWorkout;
          } else if (currentDate.isAfter(moment(workout.workout_date), 'day')) {
            backlight = missedWorkout;
          }
          if (workout.is_complete) {
            backlight = completedWorkout;
          }
        }
      } else {
        backlight = selectedDayBacklight;
      }
      return {
        ...days,
        [moment(scheduleDay.date).format('YYYY-MM-DD')]: {
          dots: dotsPrograms,
          ...backlight,
          dark: moment(scheduleDay.date).isSame(selectedDay, 'day'),
          marked: true,
        },
      };
    }, {});

    const clientMarkedDates = mergeWith(markedDates, marked);
    const selected = clientMarkedDates[selectedDay];
    clientMarkedDates[selectedDay] = {
      ...selected,
      selected: true,
      marked: true,
      dark: true,
      ...selectedDayBacklight,
    };
    return clientMarkedDates;
  };

  getWeeklyCustomDatesStyles = () => {
    const { scheduleDays } = this.state;

    if (!scheduleDays) {
      return [];
    }

    const reducedDays = scheduleDays
      .filter((day) => !!day.scheduled_workout)
      .reduce((days, scheduleDay) => {
        const workout = scheduleDay.scheduled_workout;
        let { is_complete } = workout;
        if (
          is_complete
          && days[workout.workout_date]
          && days[workout.workout_date].is_complete === false
        ) {
          is_complete = false;
        }
        return {
          ...days,
          [workout.workout_date]: {
            ...scheduleDay,
            is_complete,
          },
        };
      }, {});

    const marked = Object.values(reducedDays).map((scheduleDay) => {
      let backgroundColor = 'transparent';
      if (scheduleDay.is_complete) {
        backgroundColor = 'rgba(70, 154, 30, 0.2)';
      } else if (moment(scheduleDay.date).isBefore(moment(), 'day')) {
        backgroundColor = 'rgba(246, 170, 44, 0.2)';
      } else {
        backgroundColor = 'rgba(182, 189, 195, 0.2)';
      }
      return {
        startDate: moment(scheduleDay.date),
        dateContainerStyle: { backgroundColor },
      };
    });

    return marked;
  };

  getWeeklyMarkedDates = () => {
    const markedDates = JSON.parse(
      JSON.stringify(this.props.appointments.appointmentsListByDate),
    );

    const booked = { key: 'booked', color: colors.nasmBlue };

    const { scheduleDays } = this.state;
    if (!scheduleDays) {
      return [];
    }

    const dates = [];
    const dotsSessions = [];
    const reducedMarked = [];
    dotsSessions.push(booked);
    Object.keys(markedDates).map((key, index) => {
      reducedMarked.push(
        (markedDates[index] = { date: key, dots: dotsSessions }),
      );
      return null;
    });

    const dotsPrograms = [];
    scheduleDays.forEach((scheduleDay) => {
      if (scheduleDay.has_goal) {
        dotsPrograms.push({ key: 'goal', color: colors.azure });
      }
      if (scheduleDay.has_nutrition) {
        dotsPrograms.push({ key: 'nutrition', color: colors.peaGreen });
      }
      if (scheduleDay.has_measurement) {
        dotsPrograms.push({ key: 'measurement', color: colors.rustyRed });
      }
      if (scheduleDay.has_perf_assessment) {
        dotsPrograms.push({ key: 'assessment', color: colors.nasmBlue });
      }
      if (scheduleDay.has_assessment) {
        dotsPrograms.push({ key: 'ohsa', color: colors.duskBlue });
      }
      if (dotsPrograms.length > 0) {
        dates.push({ date: scheduleDay.date, dots: dotsPrograms });
      }
    });
    return reducedMarked.concat(dates);
  };

  onToggleCalendar = () => {
    LayoutAnimation.easeInEaseOut();
    this.setState((prevState) => ({
      weekView: !prevState.weekView,
    }));
  };

  renderLoadingState = () => {
    const loadingData = [0, 0, 0, 0, 0];
    return (
      <FlatList
        data={loadingData}
        renderItem={({ item, index }) => (
          <FoodLogCell key={index} foodLogData={item} />
        )}
      />
    );
  };

  getSections = () => {
    const baseSectionList = [
      {
        title: 'breakfast',
      },
      {
        title: 'lunch',
      },
      {
        title: 'dinner',
      },
      {
        title: 'snacks',
      },
      {
        title: 'other',
      },
    ];

    const sectionList = [];
    if (this.state.foodData) {
      const data = Object.entries(this.state.foodData);
      baseSectionList.map((listItem) => {
        for (let i = 0; i < data.length; i += 1) {
          if (data[i][0] === listItem.title) {
            const sectionTitle = data[i][0].charAt(0).toUpperCase()
              + data[i][0].slice(1).toLowerCase();
            sectionList.push({
              title: sectionTitle,
              data: data[i][1],
            });
            break;
          }
        }
        return sectionList;
      });
    }
    return sectionList;
  };

  onPressDay = async (day) => {
    this.props.selectDay(day);
    this.setState({ calendarLoading: true, selectedDay: day }, () => this.getFoodData());
  };

  renderRow = (item) => {
    const array = [];
    const data = item.item.detail;
    array.push(data);
    return (
      <FlatList
        data={array}
        renderItem={({ item: newItem, index }) => (
          <FoodLogCell key={index} foodLogData={newItem} />
        )}
      />
    );
  };

  renderSectionHeader = ({ section }) => (
    <View style={styles.sectionHeaderContainer}>
      <Text style={styles.sectionHeaderText}>{section.title}</Text>
      <View style={styles.unitHeadingContainer}>
        <Text style={styles.unitHeadingText}>
          {`${section.data[0].protein.toFixed(1)} p`}
        </Text>
        <Text style={styles.unitHeadingText}>
          {' '}
          {`${section.data[0].carbs.toFixed(1)} c`}
        </Text>
        <Text style={styles.unitHeadingText}>
          {' '}
          {`${section.data[0].fat.toFixed(1)} f`}
        </Text>
        <Text style={styles.kcalHeadingText}>
          {' '}
          {`${section.data[0].calories.toFixed(1)} kcal`}
        </Text>
      </View>
    </View>
  );

  renderEmptySection = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyDataText}>No Data Found </Text>
    </View>
  );

  onRefresh = () => {
    this.setState({ calendarLoading: true }, () => this.getFoodData());
  };

  isWalkinClient() {
    if (this.props.currentUser.role === ROLES.CLIENT) {
      return !this.props.selectedClient?.client_user.trainer;
    }
    return false;
  }

  renderList() {
    // need to pass in scroll enabled flag based on whether or not one of the records
    // in the list is being swiped on
    return (
      <SectionList
        style={styles.list}
        data={this.state.foodData}
        sections={this.getSections()}
        renderItem={this.renderRow}
        renderSectionHeader={(section) => this.renderSectionHeader(section)}
        keyExtractor={(item, index) => index.toString()}
        ListEmptyComponent={this.renderEmptySection}
        ListHeaderComponent={(
          <View style={{ backgroundColor: colors.white }}>
            <CalendarWithToggle
              weekView={this.state.weekView}
              isLoading={false}
              isWalkin={this.isWalkinClient()}
              selectedDay={this.state.selectedDay}
              markedDates={this.getMarkedDates()}
              weeklyMarkedDates={this.getWeeklyMarkedDates()}
              customDatesStyles={this.getWeeklyCustomDatesStyles()}
              onDateSelected={this.onPressDay}
              onToggle={this.onToggleCalendar}
            />
          </View>
        )}
      />
    );
  }

  renderFooter = () => (
    <View style={styles.footerContainer}>
      <View style={styles.logoContainer}>
        <Image source={avatarLogo} style={styles.logoStyle} />
        <Image source={nasmEdge} style={styles.logoStyle} />
      </View>

      <Text style={styles.avatarText}>
        Nutrition data provided by Avatar Nutrition
      </Text>

      {this.props.currentUser.role === ROLES.CLIENT ? (
        <Button
          title="Launch Avatar Nutrition"
          containerStyle={styles.launchAvatarButton}
          textStyles={styles.launchButtonTextStyle}
          onPress={() => launchAvatar(Platform.OS)}
        />
      ) : null}
    </View>
  );

  render() {
    return (
      <View style={styles.mainStyle}>
        {this.state.calendarLoading
          ? this.renderLoadingState()
          : this.renderList()}
        {this.renderFooter()}
      </View>
    );
  }
}

FoodLog.propTypes = propTypes;
FoodLog.defaultProps = defaultProps;

const styles = StyleSheet.create({
  mainStyle: {
    flex: 1,
    backgroundColor: colors.white,
  },
  sectionHeaderContainer: {
    backgroundColor: colors.veryLightBlue,
    height: 34,
    paddingHorizontal: 20,
    alignItems: 'center',
    flexDirection: 'row',
  },
  sectionHeaderText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 13,
    fontWeight: '600',
    color: colors.subGrey,
    flex: 1,
  },
  list: {
    backgroundColor: colors.white,
  },
  launchAvatarButton: {
    alignSelf: 'center',
    borderRadius: 30,
    borderWidth: 2,
    width: '90%',
    height: 50,
    backgroundColor: colors.white,
    marginVertical: 20,
    borderColor: colors.subGreyLight,
  },
  footerContainer: {
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: 1,
    borderTopColor: colors.subGreyLight,
    marginTop: 20,
  },
  avatarText: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 15,
  },
  logoContainer: {
    flexDirection: 'row',
    marginTop: 20,
  },
  logoStyle: {
    marginHorizontal: 10,
  },
  unitHeadingText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 13,
    fontWeight: '600',
    color: colors.subGrey,
  },
  kcalHeadingText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 13,
    fontWeight: '600',
    color: colors.subGrey,
    paddingHorizontal: 12,
  },
  unitHeadingContainer: {
    flexDirection: 'row',
    flex: 2,
    justifyContent: 'space-between',
  },
  launchButtonTextStyle: {
    fontFamily: 'Avenir-Heavy',
    color: colors.subGrey,
    fontSize: 17,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  emptyDataText: {
    fontFamily: 'Avenir-Roman',
    color: colors.black,
    fontSize: 22,
  },
});
const getProps = (state) => state;
const mapStateToProps = (state) => getProps(state);
const mapDispatchToProps = {
  selectDay,
  updateCurrentUser,
};
export default connect(mapStateToProps, mapDispatchToProps)(FoodLog);
