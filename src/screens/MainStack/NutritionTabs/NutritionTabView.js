import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
// Components
import { Dimensions, View, SafeAreaView } from 'react-native';
import { connect } from 'react-redux';
import LoadingSpinner from '../../../components/LoadingSpinner';
import { selectClient, updateCurrentUser } from '../../../actions';
import nasm from '../../../dataManager/apiConfig';
import { ROLES } from '../../../constants';
// Styles
import { colors, materialTabBarOptions } from '../../../styles';
import Current from './Current';
import FoodLog from './FoodLog';
import History from './History';
import Macros from './Macros';
import HeaderLeftButton from '../../../components/HeaderLeftButton';

const TabNav = createMaterialTopTabNavigator();

const { width } = Dimensions.get('window');
const tabWidth = width / 2;
const indicatorWidth = tabWidth / 1.2;

const propTypes = {
  currentUser: PropTypes.shape({
    role: PropTypes.oneOf([ROLES.CLIENT, ROLES.TRAINER]),
    id: PropTypes.string,
    avatar_connected: PropTypes.number,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
};

class NutritionTabView extends Component {
  static router = TabNav.router;

  static navigationOptions = ({ route }) => {
    const renderHeaderLeft = route.params?.renderHeaderLeft ?? (() => <View />);
    const renderHeaderRight = route.params?.renderHeaderRight ?? (() => <View />);
    return {
      title: 'Nutrition',
      headerLeft: renderHeaderLeft,
      headerRight: renderHeaderRight,
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      avatarConnected: false,
    };
  }

  async componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener(
      'focus',
      async () => {
        this.fetchAvatarStatus();
      },
    );
    this.props.navigation.setParams({
      renderHeaderLeft: this.renderHeaderLeft,
    });
  }

  componentWillUnmount = () => {
    this.unsubscribeFocus();
  };

  renderHeaderLeft = () => (
    <HeaderLeftButton
      style={styles.headerLeft}
      onPress={() => this.props.navigation.goBack()}
      titleStyle={styles.headerButtonText}
    />
  );

  updateHeaderRight = (headerRight) => {
    this.props.navigation.setParams({
      renderHeaderRight: headerRight,
    });
  };

  fetchAvatarStatus = async () => {
    this.setState({ avatarConnected: false });
    const isTrainer = this.props.currentUser.role === ROLES.TRAINER;
    const userId = isTrainer
      ? this.props.selectedClient?.id
      : this.props.currentUser.id;
    const client = await nasm.api.getUserById(userId);
    if (isTrainer) {
      this.props.selectClient(client);
    } else {
      this.props.updateCurrentUser(client);
    }
    this.setState({ avatarConnected: client.avatar_connected });
  };

  render() {
    return typeof this.state.avatarConnected === 'boolean' ? (
      <LoadingSpinner
        visible
        size="large"
        backgroundColor="rgba(0, 0, 0, 0.25)"
      />
    ) : (
      <SafeAreaView style={styles.container}>
        <TabNav.Navigator
          screenOptions={{
            ...materialTabBarOptions.defaultNavigationOptions,
            ...materialTabBarOptions.tabBarOptions,
            tabBarIndicatorStyle: {
              ...materialTabBarOptions.tabBarOptions.tabBarIndicatorStyle,
              width: indicatorWidth,
              // Based of https://github.com/satya164/react-native-tab-view/issues/944#issuecomment-599224375
              left: (tabWidth - indicatorWidth) / 2,
            },
            swipeEnabled: false,
          }}
          style={materialTabBarOptions.tabBarOptions.tabBarStyle}
        >
          <TabNav.Screen
            name={this.state.avatarConnected ? 'Macros' : 'Current'}
            component={this.state.avatarConnected ? Macros : Current}
            initialParams={{
              updateHeaderRight: this.updateHeaderRight,
              updateAvatarConnected: this.fetchAvatarStatus,
            }}
          />
          <TabNav.Screen
            name={this.state.avatarConnected ? 'Food Log' : 'History'}
            component={this.state.avatarConnected ? FoodLog : History}
            initialParams={{ updateHeaderRight: this.updateHeaderRight }}
          />
        </TabNav.Navigator>
      </SafeAreaView>
    );
  }
}

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  headerLeft: {
    paddingHorizontal: 10,
  },
  container: {
    flex: 1,
  },
};

NutritionTabView.propTypes = propTypes;

NutritionTabView.defaultProps = {
  navigation: null,
};

const mapStateToProps = () => ({ currentUser, selectedClient }) => ({
  currentUser,
  selectedClient,
});
const mapDispatchToProps = { updateCurrentUser, selectClient };
export default connect(mapStateToProps, mapDispatchToProps)(NutritionTabView);
