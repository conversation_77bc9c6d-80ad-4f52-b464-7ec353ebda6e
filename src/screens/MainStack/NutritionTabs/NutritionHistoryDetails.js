import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Moment from 'moment';

// Components
import {
  Alert, View, Text, TouchableOpacity,
} from 'react-native';
import nasm from '../../../dataManager/apiConfig';
import {
  KeyboardHandler,
  NutritionDetails,
  LoadingSpinner,
} from '../../../components';

// Styles
import { colors } from '../../../styles';
import HeaderRightButton from '../../../components/HeaderRightButton';

const propTypes = {
  nutrition: PropTypes.shape({
    id: PropTypes.string,
    protein: PropTypes.number,
    carbohydrates: PropTypes.number,
    fat: PropTypes.number,
    calories: PropTypes.number,
    created_at: PropTypes.string,
  }),
  onNutritionRecordDeleted: PropTypes.func,
  onNutritionRecordUpdated: PropTypes.func,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
    last_name: PropTypes.string,
    avatar_url: PropTypes.string,
  }).isRequired,
};

class NutritionHistoryDetails extends Component {
  static navigationOptions = ({ route }) => ({
    title: 'Nutrition',
    headerRight: () => NutritionHistoryDetails.renderHeaderRight(route),
  });

  static renderHeaderRight = (route) => (
    <HeaderRightButton
      onPress={() => {
        const onPressSave = route.params?.onPressSave;
        if (onPressSave) {
          onPressSave();
        }
      }}
      title="Save"
      titleStyle={styles.saveButton}
    />
  );

  constructor(props) {
    super(props);

    const nutrition = props.route.params?.nutrition;
    this.state = {
      nutrition,
      loadingStateText: null,
    };
  }

  componentDidMount() {
    this.props.navigation.setParams({ onPressSave: this.onPressSave });
  }

  onPressSave = () => {
    let nutrition = this.state.nutrition || {};
    if (!nutrition.id) {
      return;
    }
    this.setState({ loadingStateText: 'Saving...' }, async () => {
      try {
        const {
          protein, carbohydrates, fat, calories,
        } = nutrition;
        const data = {
          protein,
          carbohydrates,
          fat,
          calories,
        };
        const userId = this.props.selectedClient?.id;
        nutrition = await nasm.api.updateNutritionInformation(
          data,
          userId,
          nutrition.id,
        );
        const callback = this.props.route.params?.onNutritionRecordUpdated;
        if (typeof callback === 'function') {
          callback(nutrition);
        }
        Alert.alert('Saved!', '');
      } catch (error) {
        Alert.alert(
          'Error Saving',
          error.message
            || 'Failed to update nutrition information. Try agian later.',
        );
      } finally {
        this.setState({ loadingStateText: null, nutrition });
      }
    });
  };

  deleteNutritionRecord = () => {
    this.setState({ loadingStateText: 'Deleting...' }, async () => {
      try {
        const userId = this.props.selectedClient?.id;
        const nutritionId = this.state.nutrition.id;
        await nasm.api.deleteNutritionRecord(userId, nutritionId);
        const callback = this.props.route.params?.onNutritionRecordDeleted;
        if (typeof callback === 'function') {
          callback(this.state.nutrition);
        }
        this.props.navigation.goBack();
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || 'Failed to delete nutrition record. Please try again later.',
        );
        this.setState({ loadingStateText: null });
      }
    });
  };

  handleNutritionPropertyUpdate = (key, value) => {
    const nutrition = this.state.nutrition || {};
    nutrition[key] = value;
    this.setState({ nutrition });
  };

  presentDeletionConfirmationAlert = () => {
    Alert.alert(
      'Delete Record',
      'Are you sure you want to delete this nutrition record?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          onPress: this.deleteNutritionRecord,
          style: 'destructive',
        },
      ],
    );
  };

  render() {
    if (this.state.loadingStateText) {
      return <LoadingSpinner title={this.state.loadingStateText} visible />;
    }
    let date = null;
    if (this.state.nutrition && this.state.nutrition.created_at) {
      date = Moment(this.state.nutrition.date).format('MMMM D, YYYY');
    }
    return (
      <KeyboardHandler scrollEnabled>
        {date && <Text style={styles.dateText}>{date}</Text>}
        <NutritionDetails
          style={styles.nutritionDetails}
          onChangeNutritionProperty={this.handleNutritionPropertyUpdate}
          nutrition={this.state.nutrition}
        />
        <View style={styles.deleteButtonContainer}>
          <TouchableOpacity
            onPress={this.presentDeletionConfirmationAlert}
            style={styles.deleteButton}
          >
            <Text style={styles.deleteButtonText}>Delete</Text>
          </TouchableOpacity>
        </View>
      </KeyboardHandler>
    );
  }
}

const styles = {
  nutritionDetails: {
    paddingVertical: 32,
  },
  deleteButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  deleteButton: {
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.subGrey,
  },
  deleteButtonText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 12,
    fontWeight: '700',
    color: colors.subGrey,
    paddingHorizontal: 30,
    paddingVertical: 7,
  },
  saveButton: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
  },
  dateText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18,
    color: colors.subGrey,
    marginHorizontal: 32,
    marginTop: 28,
  },
};

NutritionHistoryDetails.propTypes = propTypes;
const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = {};
export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(NutritionHistoryDetails);
