import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Moment from 'moment';

// Components
import {
  StatusBar,
  View,
  Text,
  Alert,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import nasm from '../../../dataManager/apiConfig';
import {
  NutritionHorizontalBar,
  LoadingSpinner,
  Swipeable,
} from '../../../components';

// Analytics

// Styles
import { colors } from '../../../styles';

const propTypes = {
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
    last_name: PropTypes.string,
    avatar_url: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
  }).isRequired,
  nutritionHistoryRecordDeleted: PropTypes.func,
  nutritionHistoryRecordUpdated: PropTypes.func,
};

class History extends Component {
  constructor(props) {
    super(props);
    this.state = {
      nutritionInfo: [],
      refreshing: false,
      hasNextPage: true,
      page: -1,
      pageSize: 10,
      loadingStateText: null,
      swiping: false,
    };
  }

  componentDidMount() {
    this.getUserNutritionInfo();
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.route.params.updateHeaderRight(() => <View />);
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'nutrition_history' });
      this.refreshNutritionHistory();
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  getUserNutritionInfo = async () => {
    if (this.state.hasNextPage) {
      let { nutritionInfo, page, hasNextPage } = this.state;
      page += 1;
      try {
        const userId = this.props.selectedClient?.id;
        const response = await nasm.api.getUserNutritionInfo(
          userId,
          page,
          this.state.pageSize,
        );
        nutritionInfo = nutritionInfo.concat(response.nutritionList);
        hasNextPage = response.nextPage.length > 0;
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || 'Unable to get nutrition history. Please try again later.',
        );
      } finally {
        this.setState({ nutritionInfo, page, hasNextPage });
      }
    }
  };

  deleteNutritionRecord = (nutrition) => {
    this.setState({ loadingStateText: 'Deleting...' }, async () => {
      try {
        const userId = this.props.selectedClient?.id;
        const nutritionId = nutrition.id;
        await nasm.api.deleteNutritionRecord(userId, nutritionId);
        this.handleNutritionRecordDeleted(nutrition);
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || 'Failed to delete nutrition record. Please try again later.',
        );
      } finally {
        this.setState({ loadingStateText: null });
      }
    });
  };

  handleNutritionCellSelected = (nutrition) => {
    this.props.navigation.navigate('NutritionHistoryDetails', {
      nutrition,
      onNutritionRecordUpdated: this.handleNutritionRecordUpdated,
    });
  };

  handleNutritionRecordDeleted = (nutritionRecord) => {
    if (nutritionRecord && nutritionRecord.id) {
      const { nutritionInfo } = this.state;
      const index = nutritionInfo.findIndex(
        (item) => item.id === nutritionRecord.id,
      );
      if (index >= 0 && index < nutritionInfo.length) {
        nutritionInfo.splice(index, 1);
      }
      this.setState({ nutritionInfo });
    }
  };

  handleNutritionRecordSwipedLeft = (nutrition) => {
    Alert.alert(
      'Delete Record',
      'Are you sure you would like to delete this nutrition record?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => this.deleteNutritionRecord(nutrition),
        },
      ],
      undefined,
    );
  };

  handleNutritionRecordUpdated = (nutritionRecord) => {
    if (nutritionRecord && nutritionRecord.id) {
      const { nutritionInfo } = this.state;
      const index = nutritionInfo.findIndex(
        (item) => item.id === nutritionRecord.id,
      );
      if (index >= 0 && index < nutritionInfo.length) {
        nutritionInfo[index] = nutritionRecord;
      }
      this.setState({ nutritionInfo });
    }
  };

  refreshNutritionHistory = () => {
    let { nutritionInfo, page, hasNextPage } = this.state;
    this.setState({ refreshing: true }, async () => {
      try {
        const userId = this.props.selectedClient?.id;
        const response = await nasm.api.getUserNutritionInfo(
          userId,
          0,
          this.state.pageSize,
        );
        nutritionInfo = response.nutritionList;
        page = 0;
        hasNextPage = response.nextPage.length > 0;
      } catch (error) {
        Alert.alert(
          'Error',
          error.message
            || 'Unable to refresh nutrition history. Please try again later.',
        );
      } finally {
        this.setState({
          refreshing: false,
          nutritionInfo,
          page,
          hasNextPage,
        });
      }
    });
  };

  renderNutritionCell = ({ item }) => {
    const underTextStyle = [styles.underText, { fontSize: 12, paddingLeft: 3 }];
    const legendDotStyle = { width: 5, height: 5, borderRadius: 2.5 };
    return (
      <Swipeable
        key={item.id}
        onSwipeStart={() => {
          this.setState({ swiping: true });
        }}
        onRightActionRelease={() => {
          this.setState({ swiping: false });
          this.handleNutritionRecordSwipedLeft(item);
        }}
        rightContent={(
          <View style={styles.deleteButtonContainer}>
            <Text style={styles.deleteText}>Delete</Text>
          </View>
        )}
      >
        <TouchableOpacity
          onPress={() => this.handleNutritionCellSelected(item)}
          style={styles.nutritionCell}
        >
          <View style={styles.textSection}>
            {item.created_at && (
              <Text style={styles.date}>
                {Moment(item.date).format('M/D/YY')}
              </Text>
            )}
            <View style={styles.dotAndLabel}>
              <View
                style={[
                  legendDotStyle,
                  { backgroundColor: colors.macaroniAndCheese },
                ]}
              />
              <Text style={underTextStyle}>{`${item.protein}g`}</Text>
            </View>
            <View style={styles.dotAndLabel}>
              <View
                style={[
                  legendDotStyle,
                  { backgroundColor: colors.pinkishPurple },
                ]}
              />
              <Text style={underTextStyle}>{`${item.carbohydrates}g`}</Text>
            </View>
            <View style={styles.dotAndLabel}>
              <View
                style={[legendDotStyle, { backgroundColor: colors.nasmBlue }]}
              />
              <Text style={underTextStyle}>{`${item.fat}g`}</Text>
            </View>
            <Text style={styles.kcalText}>{`kcal: ${item.calories}`}</Text>
          </View>
          <View style={styles.barSection}>
            <NutritionHorizontalBar {...item} />
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };

  render() {
    if (this.state.loadingStateText !== null) {
      return <LoadingSpinner title={this.state.loadingStateText} visible />;
    }
    return (
      <View style={{ flex: 1 }}>
        <View style={styles.legendContainer}>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.legendDot,
                { backgroundColor: colors.macaroniAndCheese },
              ]}
            />
            <Text
              style={[styles.underText, { color: colors.macaroniAndCheese }]}
            >
              protein
            </Text>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[
                styles.legendDot,
                { backgroundColor: colors.pinkishPurple },
              ]}
            />
            <Text style={[styles.underText, { color: colors.pinkishPurple }]}>
              carbs
            </Text>
          </View>
          <View style={styles.dotAndLabel}>
            <View
              style={[styles.legendDot, { backgroundColor: colors.nasmBlue }]}
            />
            <Text style={[styles.underText, { color: colors.nasmBlue }]}>
              fat
            </Text>
          </View>
        </View>
        <FlatList
          scrollEnabled={!this.state.swiping}
          data={this.state.nutritionInfo}
          renderItem={this.renderNutritionCell}
          keyExtractor={(item) => item.id}
          onEndReached={this.getUserNutritionInfo}
          refreshControl={(
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.refreshNutritionHistory}
            />
          )}
        />
      </View>
    );
  }
}

const styles = {
  legendContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 26,
    backgroundColor: 'rgb(248,249,251)',
  },
  underText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 16,
    color: 'rgb(124, 128, 132)',
    paddingLeft: 9,
    paddingRight: 10,
  },
  dotAndLabel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 9,
    height: 9,
    borderRadius: 4.5,
  },
  nutritionCell: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: 'rgb(242,243,243)',
  },
  date: {
    color: '#000',
    fontSize: 16,
    fontFamily: 'Avenir-Roman',
  },
  barSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    height: 32,
    paddingTop: 10,
    paddingHorizontal: 28,
    paddingBottom: 18,
  },
  kcalText: {
    fontSize: 12,
    fontFamily: 'Avenir-Roman',
    color: colors.subGrey,
  },
  textSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 28,
    height: 34,
    paddingTop: 10,
  },
  deleteButtonContainer: {
    backgroundColor: 'red',
    flex: 1,
    justifyContent: 'center',
  },
  deleteText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.white,
    paddingLeft: 30,
  },
};

History.propTypes = propTypes;
const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(History);
