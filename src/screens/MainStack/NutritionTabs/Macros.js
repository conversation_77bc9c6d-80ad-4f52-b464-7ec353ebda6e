/* eslint-disable no-nested-ternary */
import mergeWith from 'lodash.mergewith';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
// Components
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  LayoutAnimation,
  Platform,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { connect } from 'react-redux';
import { CalendarWithToggle } from '../../../components';
import Button from '../../../components/Button';
import ProgressCircle from '../../../components/ProgressCircle';
import { launchAvatar, ROLES } from '../../../constants';
import nasm from '../../../dataManager/apiConfig';
import { selectDay } from '../../../reducers/selectedDayReducer';
import { updateCurrentUser } from '../../../actions';
// Styles
import { colors } from '../../../styles';
import { logComponentException } from '../../../util/logging';
import { curvedScale, scaleHeight } from '../../../util/responsive';

const averages = require('../../../resources/averages.png');
const avatarLogo = require('../../../resources/avatarLogoHorizontalGrey.png');
const nasmEdge = require('../../../resources/nasmEdgeBlackVertical3X.png');
const calendar = require('../../../resources/calendar.png');

const periods = {
  CONSUMED: 'Consumed',
  REMAINING: 'Remainig',
};

const circle = [
  {
    key: 'PROTEIN',
  },
  {
    key: 'CARBS',
  },
  {
    key: 'FAT',
  },
];

const propTypes = {
  selectDay: PropTypes.func,
  currentUser: PropTypes.shape({
    id: PropTypes.string,
    role: PropTypes.string,
  }),
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    client_user: PropTypes.object,
  }).isRequired,
  appointments: PropTypes.array,
  route: PropTypes.shape({
    params: PropTypes.shape({
      updateAvatarConnected: PropTypes.func,
    }),
  }).isRequired,
};

const defaultProps = {
  selectDay: null,
  currentUser: {},
  appointments: [],
};

class Macros extends Component {
  constructor(props) {
    super(props);
    this.state = {
      period: periods.CONSUMED,
      weekView: true,
      calendarLoading: false,
      scheduleDays: [],
      selectedDay: moment().format('YYYY-MM-DD'),
      showAverage: false,
      macrosData: {},
      avatarUserProfile: {},
      macrosAverage: {},
    };
  }

  componentDidMount = () => {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.props.route.params.updateHeaderRight(() => <View />);
    });
    this.setState({ calendarLoading: true });
    this.getMacrosAndProfileData();
  };

  componentWillUnmount = () => {
    if (this.unsubscribeFocus) {
      this.unsubscribeFocus();
    }
  };

  getMacrosData = async (userId, date) => nasm.api.getMacrosData(userId, date).catch((error) => {
    if (error.status === 400 && error.code === 1000) {
      return Alert.alert(
        'Avatar Nutrition',
        'Avatar Nutrition account link invalid',
        [
          {
            text: 'Ok',
            onPress: async () => {
              const isClient = this.props.currentUser.role === ROLES.CLIENT;
              if (isClient) {
                await nasm.api
                  .getMyUser(this.props.currentUser.id)
                  .then((user) => {
                    this.props.updateCurrentUser(user);
                  });
              }
              this.props.navigation.pop(2);
            },
          },
        ],
      );
    }
    logComponentException('Macros', error);
    return error;
  });

  getAvatarUserProfileData = async (userId) => {
    try {
      const response = await nasm.api.getAvatarUserProfileData(userId);
      return response;
    } catch (error) {
      logComponentException('Macros', error);
      return error;
    }
  };

  getMacrosAverage = async (userId) => {
    try {
      const response = await nasm.api.getMacrosAverage(userId);
      return response;
    } catch (error) {
      logComponentException('Macros', error);
      return error;
    }
  };

  getMacrosAndProfileData = async () => {
    const userId = this.props.currentUser.role === ROLES.TRAINER
      ? this.props.selectedClient?.id
      : this.props.currentUser.id;
    const macrosData = await this.getMacrosData(userId, this.state.selectedDay);
    const avatarUserProfile = await this.getAvatarUserProfileData(userId);
    const macrosAverage = await this.getMacrosAverage(userId);
    this.setState({
      calendarLoading: false,
      macrosData,
      avatarUserProfile,
      macrosAverage,
    });
  };

  onPressDay = async (day) => {
    this.props.selectDay(day);
    this.setState({ calendarLoading: true, selectedDay: day }, () => this.getMacrosAndProfileData());
  };

  getMarkedDates = () => {
    const markedDates = JSON.parse(
      JSON.stringify(this.props.appointments.appointmentsListByDate),
    );

    const booked = {
      key: 'booked',
      color: colors.buttonBlue,
      selectedDotColor: colors.buttonBlue,
    };

    const { scheduleDays, selectedDay } = this.state;

    const completedWorkout = {
      selected: true,
      selectedColor: colors.scheduleDayComplete,
    };
    const missedWorkout = {
      selected: true,
      selectedColor: colors.missedWorkout,
    };
    const scheduledFutureWorkout = {
      selected: true,
      selectedColor: colors.scheduledFutureWorkout,
    };
    const selectedDayBacklight = { selected: true, selectedColor: 'black' };

    const updatedGoals = {
      key: 'updatedGoals',
      color: colors.azure,
      selectedDotColor: colors.azure,
    };
    const updatedNutrition = {
      key: 'updatedNutrition',
      color: colors.peaGreen,
      selectedDotColor: colors.peaGreen,
    };
    const updatedMeasurements = {
      key: 'updatedMeasurements',
      color: colors.rustyRed,
      selectedDotColor: colors.rustyRed,
    };
    const updatedAssessments = {
      key: 'updatedAssessments',
      color: colors.buttonBlue,
      selectedDotColor: colors.buttonBlue,
    };
    const updatedOhsa = {
      key: 'updatedAssessments',
      color: colors.duskBlue,
      selectedDotColor: colors.buttonBlue,
    };

    if (!scheduleDays) {
      return { [selectedDay]: { selected: true } };
    }

    const dotsSessions = [];
    dotsSessions.push(booked);
    Object.keys(markedDates).forEach((key) => {
      markedDates[key] = { dots: dotsSessions };
    });

    const dotsPrograms = [];

    const marked = scheduleDays.reduce((days, scheduleDay) => {
      const workout = scheduleDay.scheduled_workout;
      if (scheduleDay.has_goal) {
        dotsPrograms.push(updatedGoals);
      }
      if (scheduleDay.has_nutrition) {
        dotsPrograms.push(updatedNutrition);
      }
      if (scheduleDay.has_measurement) {
        dotsPrograms.push(updatedMeasurements);
      }
      if (scheduleDay.has_perf_assessment) {
        dotsPrograms.push(updatedAssessments);
      }
      if (scheduleDay.has_assessment) {
        dotsPrograms.push(updatedOhsa);
      }

      let backlight;
      const currentDate = moment();
      if (!moment(scheduleDay.date).isSame(selectedDay, 'day')) {
        if (workout) {
          if (currentDate.isSameOrBefore(moment(workout.workout_date), 'day')) {
            backlight = scheduledFutureWorkout;
          } else if (currentDate.isAfter(moment(workout.workout_date), 'day')) {
            backlight = missedWorkout;
          }
          if (workout.is_complete) {
            backlight = completedWorkout;
          }
        }
      } else {
        backlight = selectedDayBacklight;
      }
      return {
        ...days,
        [moment(scheduleDay.date).format('YYYY-MM-DD')]: {
          dots: dotsPrograms,
          ...backlight,
          dark: moment(scheduleDay.date).isSame(selectedDay, 'day'),
          marked: true,
        },
      };
    }, {});

    const clientMarkedDates = mergeWith(markedDates, marked);
    const selected = clientMarkedDates[selectedDay];
    clientMarkedDates[selectedDay] = {
      ...selected,
      selected: true,
      marked: true,
      dark: true,
      ...selectedDayBacklight,
    };
    return clientMarkedDates;
  };

  getWeeklyCustomDatesStyles = () => {
    const { scheduleDays } = this.state;

    if (!scheduleDays) {
      return [];
    }

    const reducedDays = scheduleDays
      .filter((day) => !!day.scheduled_workout)
      .reduce((days, scheduleDay) => {
        const workout = scheduleDay.scheduled_workout;
        let { is_complete } = workout;
        if (
          is_complete
          && days[workout.workout_date]
          && days[workout.workout_date].is_complete === false
        ) {
          is_complete = false;
        }
        return {
          ...days,
          [workout.workout_date]: {
            ...scheduleDay,
            is_complete,
          },
        };
      }, {});

    const marked = Object.values(reducedDays).map((scheduleDay) => {
      let backgroundColor = 'transparent';
      if (scheduleDay.is_complete) {
        backgroundColor = colors.scheduleDayComplete;
      } else if (moment(scheduleDay.date).isBefore(moment(), 'day')) {
        backgroundColor = colors.missedWorkout;
      } else {
        backgroundColor = colors.scheduledFutureWorkout;
      }
      return {
        startDate: moment(scheduleDay.date),
        dateContainerStyle: { backgroundColor },
      };
    });

    return marked;
  };

  getWeeklyMarkedDates = () => {
    const markedDates = JSON.parse(
      JSON.stringify(this.props.appointments.appointmentsListByDate),
    );

    const booked = { key: 'booked', color: colors.nasmBlue };

    const { scheduleDays } = this.state;
    if (!scheduleDays) {
      return [];
    }

    const dates = [];
    const dotsSessions = [];
    const reducedMarked = [];
    dotsSessions.push(booked);
    Object.keys(markedDates).map((key, index) => {
      reducedMarked.push(
        (markedDates[index] = { date: key, dots: dotsSessions }),
      );
      return null;
    });

    const dotsPrograms = [];
    scheduleDays.forEach((scheduleDay) => {
      if (scheduleDay.has_goal) {
        dotsPrograms.push({ key: 'goal', color: colors.azure });
      }
      if (scheduleDay.has_nutrition) {
        dotsPrograms.push({ key: 'nutrition', color: colors.peaGreen });
      }
      if (scheduleDay.has_measurement) {
        dotsPrograms.push({ key: 'measurement', color: colors.rustyRed });
      }
      if (scheduleDay.has_perf_assessment) {
        dotsPrograms.push({ key: 'assessment', color: colors.nasmBlue });
      }
      if (scheduleDay.has_assessment) {
        dotsPrograms.push({ key: 'ohsa', color: colors.duskBlue });
      }
      if (dotsPrograms.length > 0) {
        dates.push({ date: scheduleDay.date, dots: dotsPrograms });
      }
    });
    return reducedMarked.concat(dates);
  };

  onToggleCalendar = () => {
    LayoutAnimation.easeInEaseOut();
    this.setState((prevState) => ({
      weekView: !prevState.weekView,
    }));
  };

  renderPeriodPicker = () => (
    <View style={styles.weekMonthYearPickerContainer}>
      <View style={styles.weekMonthYearPicker}>
        <TouchableOpacity
          style={
            this.state.period === periods.CONSUMED
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState({
              period: periods.CONSUMED,
            });
          }}
        >
          <Text
            style={
              this.state.period === periods.CONSUMED
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Consumed
          </Text>
        </TouchableOpacity>

        <View style={styles.periodPickerSperator} />

        <TouchableOpacity
          style={
            this.state.period === periods.REMAINING
              ? styles.selectedContainer
              : styles.unselectedContainer
          }
          onPress={() => {
            this.setState({
              period: periods.REMAINING,
            });
          }}
        >
          <Text
            style={
              this.state.period === periods.REMAINING
                ? styles.selectedPeriod
                : styles.unselectedPeriod
            }
          >
            Remaining
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  renderCircle = (item) => {
    let type;
    let unitValue = 0;
    let totalValue;
    if (this.state.macrosData.tracked) {
      const trackedData = this.state.showAverage
        ? this.state.macrosAverage.tracked
        : this.state.macrosData.tracked;
      const targetData = this.state.showAverage
        ? this.state.macrosAverage.target
        : this.state.macrosData.target;
      const activeTab = this.state.period === periods.CONSUMED ? 0 : 1;
      const protein = (trackedData.protein * 100) / targetData.protein;
      const carbs = (trackedData.carbs * 100) / targetData.carbs;
      const fat = (trackedData.fat * 100) / targetData.fat;
      switch (item.key) {
        case 'PROTEIN':
          type = protein;
          totalValue = this.roundedValue(targetData.protein);
          unitValue = activeTab
            ? this.roundedValue(targetData.protein)
              - this.roundedValue(trackedData.protein)
            : this.roundedValue(trackedData.protein);
          break;
        case 'CARBS':
          type = carbs;
          totalValue = this.roundedValue(targetData.carbs);
          unitValue = activeTab
            ? this.roundedValue(targetData.carbs)
              - this.roundedValue(trackedData.carbs)
            : this.roundedValue(trackedData.carbs);
          break;
        case 'FAT':
          type = fat;
          totalValue = this.roundedValue(targetData.fat);
          unitValue = activeTab
            ? this.roundedValue(targetData.fat)
              - this.roundedValue(trackedData.fat)
            : this.roundedValue(trackedData.fat);
          break;
        default:
          type = 0;
          totalValue = 0;
          unitValue = 0;
      }
    } else {
      return null;
    }

    return (
      <View style={styles.maxMinContainer} key={item.key}>
        <Text style={styles.maxMinText} allowFontScaling={false}>
          MAX
        </Text>
        <View>
          <View style={styles.minContainer}>
            <Text style={styles.maxMinText} allowFontScaling={false}>
              MIN
            </Text>
          </View>

          <ProgressCircle percent={type} radius={scale * 53} borderWidth={5}>
            <Text style={styles.headingText} allowFontScaling={false}>
              {item.key}
            </Text>
            <Text style={styles.unitValueText} allowFontScaling={false}>
              {unitValue}
            </Text>
            <Text style={styles.headingText} allowFontScaling={false}>
              {`OF ${totalValue}`}
            </Text>
          </ProgressCircle>
        </View>
      </View>
    );
  };

  renderFooter = () => (
    <View>
      <View style={styles.footerContainer}>
        <View style={styles.logoContainer}>
          <Image source={avatarLogo} style={styles.logoStyle} />
          <Image source={nasmEdge} style={styles.logoStyle} />
        </View>

        <Text style={styles.avatarText}>
          Nutrition data provided by Avatar Nutrition
        </Text>
      </View>

      {this.props.currentUser.role === ROLES.CLIENT ? (
        <Button
          title="Launch Avatar Nutrition"
          containerStyle={styles.launchAvatarButton}
          textStyles={styles.launchButtonTextStyle}
          onPress={() => launchAvatar(Platform.OS)}
        />
      ) : null}
    </View>
  );

  showTodayDetails = () => {
    this.setState({ showAverage: false });
  };

  showAverageDetails = () => {
    this.setState({ showAverage: true });
  };

  roundedValue = (value) => (Math.round(value * 10) / 10).toFixed(0);

  progressBar = () => {
    const trackedData = this.state.showAverage
      ? this.state.macrosAverage.tracked
      : this.state.macrosData.tracked;
    const targetData = this.state.showAverage
      ? this.state.macrosAverage.target
      : this.state.macrosData.target;
    const activeTab = this.state.period === periods.CONSUMED ? 0 : 1;
    const calories = activeTab
      ? targetData.calories - trackedData.calories
      : trackedData.calories;
    const fiber = activeTab
      ? targetData.min_fiber - trackedData.fiber
      : trackedData.fiber;
    let calorieRatio = (trackedData.calories * 100) / targetData.calories;
    if (calorieRatio > 100) {
      calorieRatio = 100;
    }
    let fiberRatio = (trackedData.fiber * 100) / targetData.min_fiber;
    if (fiberRatio > 100) {
      fiberRatio = 100;
    }
    return (
      <View>
        <View style={styles.progressBarContainer}>
          {/* Calories Section  */}

          <View style={styles.centerStyle}>
            <Text style={styles.caloriesText}>
              {this.state.showAverage ? 'AVG. CALORIES' : 'CALORIES'}
            </Text>
            <View style={styles.outerProgressBar}>
              <View
                style={[
                  styles.innerProgressBarStyle,
                  {
                    width: `${calorieRatio}%`,
                  },
                ]}
              />
            </View>
            <Text style={styles.infoText}>
              {`${this.roundedValue(calories)} ${activeTab ? 'LEFT' : 'EATEN'}`}
            </Text>
          </View>

          {/* show today and averages */}

          {this.state.showAverage ? (
            <TouchableOpacity
              style={styles.contentCenterStyle}
              onPress={() => this.showTodayDetails()}
            >
              <Image
                source={calendar}
                style={styles.calendarIconStyle}
                resizeMode="contain"
              />
              <Text style={styles.showTodayText}>Show today</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.contentCenterStyle}
              onPress={() => this.showAverageDetails()}
            >
              <Image
                source={averages}
                style={styles.averageIconStyle}
                resizeMode="contain"
              />
              <Text style={styles.showAveragesText}>Show averages</Text>
            </TouchableOpacity>
          )}

          {/* Fiber Section */}

          <View style={styles.centerStyle}>
            <Text style={styles.caloriesText}>
              {this.state.showAverage ? 'AVG. FIBER' : 'FIBER'}
            </Text>
            <View style={styles.outerProgressBar}>
              <View
                style={[
                  styles.innerProgressBarStyle,
                  {
                    width: `${fiberRatio}%`,
                  },
                ]}
              />
            </View>
            <Text style={styles.infoText}>
              {`${this.roundedValue(fiber)} ${activeTab ? 'LEFT' : 'EATEN'}`}
            </Text>
          </View>
        </View>
        {this.state.showAverage && (
          <Text style={styles.checkIn}>
            Averages are based on each recorded day since last check-in.
          </Text>
        )}
      </View>
    );
  };

  onRefresh = () => {
    this.setState({ calendarLoading: true }, () => this.getMacrosAndProfileData());
  };

  isWalkinClient() {
    if (this.props.currentUser.role === ROLES.CLIENT) {
      return !this.props.selectedClient?.client_user.trainer;
    }
    return false;
  }

  renderEmptySection = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyDataText}>No Data Found </Text>
    </View>
  );

  render() {
    return (
      <View style={styles.main}>
        <ScrollView
          contentContainerStyle={styles.container}
          testID="Macros"
          refreshControl={(
            <RefreshControl
              refreshing={this.state.calendarLoading}
              onRefresh={() => this.onRefresh()}
            />
          )}
        >
          <View style={styles.flexStyle}>
            <View style={{ backgroundColor: colors.white }}>
              <CalendarWithToggle
                ref={(ref) => {
                  this.calendar = ref;
                }}
                weekView={this.state.weekView}
                isLoading={false}
                isWalkin={this.isWalkinClient()}
                selectedDay={this.state.selectedDay}
                markedDates={this.getMarkedDates()}
                weeklyMarkedDates={this.getWeeklyMarkedDates()}
                customDatesStyles={this.getWeeklyCustomDatesStyles()}
                onDateSelected={this.onPressDay}
                onToggle={this.onToggleCalendar}
              />
            </View>

            {this.state.calendarLoading ? null : this.state.macrosData
              && this.state.macrosData.target ? (
                <View>
                  {this.renderPeriodPicker()}
                  <View style={styles.weightFatContainer}>
                    <View style={styles.weightContainer}>
                      <Text style={styles.caloriesText}>WEIGHT</Text>
                      <Text style={styles.infoText}>
                        {this.state.avatarUserProfile.profile.latest_check_in
                          ? this.state.avatarUserProfile.profile.use_metric_units
                            ? `${this.state.avatarUserProfile.profile.latest_check_in.weight} kg`
                            : `${this.roundedValue(
                              this.state.avatarUserProfile.profile
                                .latest_check_in.weight * 2.205,
                            )} lbs`
                          : '0 kg'}
                      </Text>
                    </View>

                    <View style={styles.fatContainer}>
                      <Text style={styles.caloriesText}>BODY FAT</Text>
                      <Text style={styles.infoText}>
                        {this.state.avatarUserProfile.profile.latest_check_in
                          ? `${this.state.avatarUserProfile.profile.latest_check_in.body_fat}%`
                          : '0%'}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.progressContainer}>
                    <FlatList
                      renderItem={({ item }) => this.renderCircle(item)}
                      data={circle}
                      horizontal
                      contentContainerStyle={styles.flatListContainerStyle}
                      scrollEnabled={false}
                    />
                  </View>

                  {this.progressBar()}
                </View>
              ) : (
                this.renderEmptySection()
              )}
          </View>
          {this.renderFooter()}
        </ScrollView>
      </View>
    );
  }
}

Macros.propTypes = propTypes;
Macros.defaultProps = defaultProps;

const scale = Dimensions.get('window').width / 400;

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flexGrow: 1,
  },
  weekMonthYearPickerContainer: {
    height: scaleHeight(12),
    width: '100%',
    alignItems: 'center',
  },
  weekMonthYearPicker: {
    height: '35%',
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: scaleHeight(4),
    marginHorizontal: '20%',
    borderColor: colors.subGrey,
    borderWidth: 1,
    borderRadius: curvedScale(4),
  },
  selectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGrey,
  },
  unselectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedPeriod: {
    color: colors.white,
    alignSelf: 'center',
    fontFamily: 'Avenir-Roman',
    fontSize: 13 * scale,
    fontWeight: 'bold',
  },
  unselectedPeriod: {
    fontFamily: 'Avenir-Roman',
    alignSelf: 'center',
    color: colors.subGrey,
    fontSize: 13 * scale,
    fontWeight: 'bold',
  },
  launchAvatarButton: {
    alignSelf: 'center',
    borderRadius: 30,
    borderWidth: 2,
    width: '90%',
    height: 50,
    backgroundColor: colors.white,
    marginVertical: 20,
    borderColor: colors.subGreyLight,
  },
  launchButtonTextStyle: {
    fontFamily: 'Avenir-Heavy',
    color: colors.subGrey,
    fontSize: 17,
    fontWeight: '600',
  },
  footerContainer: {
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginBottom: 10,
  },
  logoContainer: {
    flexDirection: 'row',
    marginTop: 25,
  },
  logoStyle: {
    marginHorizontal: 10,
  },
  avatarText: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 15,
  },
  showAveragesText: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
  },
  showTodayText: {
    color: colors.macaroniAndCheese,
    fontFamily: 'Avenir-Heavy',
    fontSize: 14,
    marginTop: 8,
  },
  progressContainer: {
    marginHorizontal: 20,
    flexDirection: 'row',
    marginTop: 30,
  },
  maxMinText: {
    fontSize: 12 * scale,
    fontFamily: 'Avenir-Medium',
    color: colors.subGrey,
  },
  headingText: {
    fontSize: 15 * scale,
    color: colors.subGrey,
    fontFamily: 'Avenir-Medium',
  },
  unitValueText: {
    fontSize: 22 * scale,
    fontWeight: 'bold',
    fontFamily: 'Avenir-Heavy',
  },
  outerProgressBar: {
    width: '50%',
    height: 5,
    borderRadius: 8,
    backgroundColor: colors.outerProgress,
    justifyContent: 'center',
    marginVertical: 6,
  },
  innerProgressBarStyle: {
    width: '100%',
    height: 5,
    borderRadius: 8,
    backgroundColor: colors.azure,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 25,
  },
  caloriesText: {
    color: colors.subGrey,
    fontWeight: '600',
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
  },
  checkIn: {
    color: colors.subGrey,
    fontWeight: '600',
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
    alignSelf: 'center',
    marginTop: 10,
  },
  centerStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  infoText: {
    color: colors.black,
    fontWeight: 'bold',
    fontFamily: 'Avenir-Heavy',
    fontSize: 14,
  },
  periodPickerSperator: {
    width: 1,
    height: '100%',
    backgroundColor: colors.subGrey,
  },
  flatListContainerStyle: {
    justifyContent: 'space-between',
    flex: 1,
  },
  fatContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  weightFatContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  weightContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 40,
  },
  flexStyle: {
    flex: 1,
  },
  contentCenterStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  calendarIconStyle: {
    width: 28,
    height: 27,
  },
  averageIconStyle: {
    width: 37,
    height: 37,
  },
  maxMinContainer: {
    alignItems: 'center',
  },
  minContainer: {
    position: 'absolute',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  emptyDataText: {
    fontFamily: 'Avenir-Roman',
    color: colors.black,
    fontSize: 22,
  },
});

const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  selectedClient: state.selectedClient,
  appointments: state.appointments,
});
const mapDispatchToProps = {
  selectDay,
  updateCurrentUser,
};
export default connect(mapStateToProps, mapDispatchToProps)(Macros);
