import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Moment from 'moment';

// Components
import {
  StatusBar,
  Alert,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import nasm from '../../../dataManager/apiConfig';
import { ROLES, calculateCalories } from '../../../constants';
import {
  KeyboardHandler,
  NutritionDetails,
  LoadingSpinner,
} from '../../../components';
import AvatarFlashCard from '../../../components/AvatarFlashCard';

// Analytics

// Styles
import { colors } from '../../../styles';
import { scaleWidth } from '../../../util/responsive';
import HeaderRightButton from '../../../components/HeaderRightButton';

const bgImage = require('../../../resources/avatarBg.png');
const avatarLogo = require('../../../resources/avatarLogo.png');

const propTypes = {
  currentUser: PropTypes.shape({
    role: PropTypes.oneOf([ROLES.CLIENT, ROLES.TRAINER]),
    id: PropTypes.string,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
    last_name: PropTypes.string,
    avatar_url: PropTypes.string,
  }).isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func,
    goBack: PropTypes.func,
  }).isRequired,
};

class Current extends Component {
  constructor(props) {
    super(props);
    const nutritionInfo = props.route.params?.nutrition;
    this.state = {
      nutritionInfo,
      loadingText: null,
      activityLevels: [],
    };
  }

  componentDidMount() {
    this.getActivityLevels();
    // Required initially, as the focus is not called
    this.props.route.params.updateHeaderRight(this.renderHeaderRight);
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      // Required when the user comes back from History Tab
      this.props.route.params.updateHeaderRight(this.renderHeaderRight);
      this.getCurrentNutritionInfo();
      StatusBar.setBarStyle('light-content');
      analytics().logEvent('screen_view', { screen_name: 'current_nutrition' });
    });
  }

  componentWillUnmount() {
    this.unsubscribeFocus();
  }

  handleNutritionDeleted = (nutrition) => {
    this.setState({ nutritionInfo: nutrition });
    const callback = this.props.route.params?.nutritionDeletedCallback;
    if (typeof callback === 'function') {
      callback(nutrition);
    }
  };

  handleNutritionHistoryRecordUpdated = (nutritionHistory) => {
    if (nutritionHistory && nutritionHistory.id) {
      if (
        this.state.nutritionInfo.id
        && nutritionHistory.id === this.state.nutritionInfo.id
      ) {
        this.setState({ nutritionInfo: nutritionHistory });
        const callback = this.props.route.params?.nutritionUpdateCallback;
        if (typeof callback === 'function') {
          callback(nutritionHistory);
        }
      }
    }
  };

  handleNutritionPropertyUpdate = (key, value) => {
    const nutritionInfo = this.state.nutritionInfo || {};
    nutritionInfo[key] = value;
    this.setState({ nutritionInfo });
  };

  handleNutritionUpdates = (updatedNutrition) => {
    this.setState({ nutritionInfo: updatedNutrition });
    const callback = this.props.route.params?.nutritionUpdateCallback;
    if (typeof callback === 'function') {
      callback(updatedNutrition);
    }
  };

  onClickFashCard = () => {
    this.props.navigation.navigate('ConnectAvatarNutrition');
  };

  onPressSave = () => {
    const { nutritionInfo } = this.state;
    if (!nutritionInfo) {
      Alert.alert('Error', 'Please input the nutrition information');
    }
    let wasRecordedToday = false;
    if (nutritionInfo && nutritionInfo.created_at) {
      const createdAt = new Moment(nutritionInfo.created_at);
      const today = new Moment().startOf('day');
      wasRecordedToday = createdAt.isSame(today, 'd');
    }
    if (nutritionInfo && nutritionInfo.id && wasRecordedToday) {
      this.updateNutritionRecord();
    } else {
      this.createNewNutritionRecord();
    }
  };

  getActivityFactorForId = (id) => {
    if (!id) return 1;
    const { activityLevels } = this.state;
    for (let i = 0; i < activityLevels.length; i += 1) {
      const activityLevel = activityLevels[i];
      if (activityLevel.id === id) {
        return activityLevel.coefficient;
      }
    }
    return 1;
  };

  getActivityLevels = async () => {
    const activityLevels = await nasm.api.getActivityLevels();
    this.setState({ activityLevels });
  };

  getAgeFromDob = (dob) => {
    if (dob) {
      const dobMoment = Moment(dob);
      return new Moment().diff(dobMoment, 'years');
    }
    return null;
  };

  getCurrentNutritionInfo = async () => {
    const nutritionInfo = await nasm.api.getLatestNutrition(
      this.props.selectedClient?.id,
    );
    this.setState({ nutritionInfo });
  };

  getFocusFactorForClient = (client) => {
    const { goals } = client;
    if (goals && goals.length > 0) {
      if (goals[0].name === 'Lose Weight') {
        return 0.8;
      }
      if (goals[0].name === 'Increase Strength') {
        return 1.2;
      }
      if (goals[0].name === 'Improve Performance') {
        return 1.1;
      }
      return 1;
    }
    return null;
  };

  hasRequiredInfoForAutoCalculate = () => {
    const dob = this.props.selectedClient?.birth_date;
    if (!dob) return false;
    const age = this.getAgeFromDob(dob);
    if (!age) return false;
    const client = this.props.selectedClient?.client_user;
    if (!client || !client.weight || !client.height) return false;
    const focusFactor = this.getFocusFactorForClient(client);
    if (!focusFactor) return false;
    return true;
  };

  autoCalculateNutrition = () => {
    const dob = this.props.selectedClient?.birth_date;
    const age = this.getAgeFromDob(dob);
    const client = this.props.selectedClient?.client_user;
    const activityFactor = this.getActivityFactorForId(
      client.activity_level_id,
    );
    const focusFactor = this.getFocusFactorForClient(client);

    const weight = client.weight * 0.4535923;
    const height = client.height * 2.54;
    let valueBasedOnGender = 5;
    if (this.props.selectedClient?.gender_type === 2) valueBasedOnGender = -161;

    const bmr = 9.99 * weight + 6.25 * height - 4.92 * age + valueBasedOnGender;
    const maintenanceCalories = bmr * activityFactor * focusFactor;
    const protein = Math.round(client.weight);
    const fat = Math.round((maintenanceCalories * 0.25) / 9);
    const carbohydrates = Math.round(
      (maintenanceCalories - protein * 4 - fat * 9) / 4,
    );

    const calories = calculateCalories(protein, carbohydrates, fat);

    const oldNutritionInfo = this.state.nutritionInfo || {};
    const nutritionInfo = {};
    Object.keys(oldNutritionInfo).forEach((key) => {
      nutritionInfo[key] = oldNutritionInfo[key];
    });
    nutritionInfo.protein = protein;
    nutritionInfo.fat = fat;
    nutritionInfo.carbohydrates = carbohydrates;
    nutritionInfo.calories = calories;
    this.setState({ nutritionInfo });
  };

  createNewNutritionRecord = () => {
    if (!this.state.nutritionInfo) {
      return;
    }
    this.setState({ loadingText: 'Saving...' }, async () => {
      try {
        const {
          protein = 0,
          carbohydrates = 0,
          fat = 0,
          calories = 0,
        } = this.state.nutritionInfo;
        const data = {
          assigned_time: new Moment().format(),
          protein,
          carbohydrates,
          fat,
          calories,
        };
        const response = await nasm.api.createNutritionRecord(
          data,
          this.props.selectedClient?.id,
        );
        this.handleNutritionUpdates(response);
        Alert.alert('Saved', '');
      } catch (error) {
        Alert.alert(
          'Error Saving',
          error.message
            || 'Failed to save nutrition info. Please try again later.',
        );
      } finally {
        this.setState({ loadingText: null });
      }
    });
  };

  showAutoCalculateAlert = () => {
    let buttonOptions = [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Submit',
        style: 'default',
        onPress: this.autoCalculateNutrition,
      },
    ];
    if (!this.hasRequiredInfoForAutoCalculate()) {
      buttonOptions = [{ text: 'Dismiss', style: 'cancel' }];
    }
    Alert.alert(
      'Auto-Calculate',
      'Requires Height, Weight, Age, and Focus to calculate. If Body Fat Percentage and Activity Level is specified, they will also be used. Otherwise enter values.',
      buttonOptions,
    );
  };

  updateNutritionRecord = () => {
    this.setState({ loadingText: 'Updating...' }, async () => {
      try {
        const {
          id,
          protein = 0,
          carbohydrates = 0,
          fat = 0,
          calories = 0,
        } = this.state.nutritionInfo;
        const data = {
          protein,
          carbohydrates,
          fat,
          calories,
        };
        const response = await nasm.api.updateNutritionInformation(
          data,
          this.props.selectedClient?.id,
          id,
        );
        this.handleNutritionUpdates(response);
        Alert.alert('Updated', '');
      } catch (error) {
        Alert.alert(
          'Error Saving',
          error.message
            || 'Failed to save nutrition info. Please try again later.',
        );
      } finally {
        this.setState({ loadingText: null });
      }
    });
  };

  renderHeaderRight = () => (
    <HeaderRightButton
      onPress={() => this.onPressSave()}
      title="Save"
      titleStyle={styles.headerButtonText}
    />
  );

  render() {
    if (this.state.loadingText) {
      return <LoadingSpinner title={this.state.loadingText} visible />;
    }
    return (
      <KeyboardHandler scrollEnabled>
        <ScrollView style={{ flex: 1 }}>
          <NutritionDetails
            style={styles.nutritionDetails}
            onChangeNutritionProperty={this.handleNutritionPropertyUpdate}
            nutrition={this.state.nutritionInfo}
          />
          <View>
            <TouchableOpacity
              onPress={this.showAutoCalculateAlert}
              style={styles.autoCalculateButton}
            >
              <Text style={styles.autoCalculateText}>Auto-Calculate</Text>
            </TouchableOpacity>
          </View>
          {this.props.currentUser.role === ROLES.CLIENT && (
            <AvatarFlashCard
              visible
              cardTitle={'Connect with\nAvatar Nutrition'}
              logo={avatarLogo}
              image={bgImage}
              onClick={this.onClickFashCard}
            />
          )}
        </ScrollView>
      </KeyboardHandler>
    );
  }
}

const styles = {
  nutritionDetails: {
    paddingVertical: 44,
  },
  autoCalculateButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 28,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.subGrey,
  },
  autoCalculateText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 12,
    fontWeight: '700',
    color: colors.subGrey,
    paddingHorizontal: 20,
    paddingVertical: 7,
  },
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  connectAvatarContainer: {
    flex: 1,
    backgroundColor: 'red',
  },
  bkgImage: {
    width: scaleWidth(80),
    height: 120,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarLogo: {
    width: 60,
    height: 60,
    position: 'absolute',
    left: 40,
  },
  headingText: {
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    fontSize: 18,
    color: colors.black,
    alignSelf: 'center',
    marginLeft: 100,
  },
};

Current.propTypes = propTypes;
const mapStateToProps = ({ currentUser, selectedClient }) => ({
  currentUser,
  selectedClient,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(Current);
