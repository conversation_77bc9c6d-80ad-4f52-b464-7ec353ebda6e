import PropTypes from 'prop-types';
import React, { Component } from 'react';
import {
  Dimensions, Image, StyleSheet, Text, View,
} from 'react-native';
import { colors } from '../../../styles';

const propTypes = {
  nutrition: PropTypes.shape({
    protein: PropTypes.number,
    carbohydrates: PropTypes.number,
    fat: PropTypes.number,
    calories: PropTypes.number,
    updated_at: PropTypes.string,
  }),
};
const defaultProps = {
  nutrition: null,
  renderEmptyStateView: null,
  showUpdatedText: true,
  loading: true,
};

class FoodLogCell extends Component {
  renderFoodDetails = (item) => {
    const details = item;
    return details ? (
      <View>
        <View style={{ padding: 16, flexDirection: 'row' }}>
          <Image
            resizeMode="contain"
            source={{ uri: details.thumbnailUrl }}
            style={styles.imageStyle}
          />
          <View style={{ marginLeft: 15, flex: 1 }}>
            <Text style={styles.nameText}>{details.name}</Text>
            <View style={styles.baseContainer}>
              <Text style={styles.unitText}>
                {`${details.serving} ${details.baseUnit}`}
              </Text>
              <View style={styles.baseTextContainer}>
                <Text style={styles.baseText}>
                  {`${details.baseProtein.toFixed(1)} p`}
                </Text>
                <Text style={styles.baseText}>
                  {`${details.baseCarbs.toFixed(1)} c`}
                </Text>
                <Text style={styles.baseText}>
                  {`${details.baseFat.toFixed(1)} f`}
                </Text>
              </View>
            </View>
          </View>
        </View>
        <View style={styles.divider} />
      </View>
    ) : (
      <View>
        <View style={styles.loadingMainContainer} />
        <View style={{ marginVertical: 15 }}>
          <View style={styles.loadingHeader} />
          <View style={{ flexDirection: 'row', marginTop: 5 }}>
            <View style={{ flex: 1 }}>
              <View style={styles.nameLoadingStyle} />
            </View>
            <View style={styles.quantityInfoLoading} />
            <View style={styles.quantityInfoLoading} />
            <View style={styles.kcalLoadingStyle} />
          </View>
        </View>
      </View>
    );
  };

  render() {
    const { foodLogData } = this.props;
    return <View>{this.renderFoodDetails(foodLogData)}</View>;
  }
}
FoodLogCell.propTypes = propTypes;
FoodLogCell.defaultProps = defaultProps;

const scale = Dimensions.get('window').width / 400;

const styles = StyleSheet.create({
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: colors.subGreyLight,
  },
  baseText: {
    color: colors.subGrey,
    fontSize: 11,
    fontFamily: 'Avenir-Roman',
  },
  baseTextContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
  },
  baseContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-end',
  },
  unitText: {
    color: colors.subGrey,
    fontSize: 11,
    flex: 1,
    fontFamily: 'Avenir-Roman',
  },
  nameText: {
    flex: 1,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  imageStyle: {
    width: 56,
    height: 39,
    borderRadius: 5,
    alignSelf: 'center',
  },
  loadingMainContainer: {
    backgroundColor: colors.veryLightBlue,
    flex: 1,
    height: 34,
  },
  loadingHeader: {
    backgroundColor: colors.veryLightBlue,
    height: 18 * scale,
    width: 100 * scale,
    marginHorizontal: 16,
    borderRadius: 5,
  },
  nameLoadingStyle: {
    backgroundColor: colors.veryLightBlue,
    height: 12 * scale,
    width: 60 * scale,
    marginHorizontal: 16,
    borderRadius: 5,
  },
  quantityInfoLoading: {
    backgroundColor: colors.veryLightBlue,
    height: 12 * scale,
    width: 35 * scale,
    marginHorizontal: 10,
    borderRadius: 5,
  },
  kcalLoadingStyle: {
    backgroundColor: colors.veryLightBlue,
    height: 12 * scale,
    width: 35 * scale,
    marginHorizontal: 10,
    marginRight: 16,
    borderRadius: 5,
  },
});

export default FoodLogCell;
