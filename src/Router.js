import React, { Component, useState } from 'react';
import {
  NavigationContainer,
  CommonActions,
  DefaultTheme,
  useLinkTo,
} from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import {
  View,
  AppState,
  NativeModules,
  Platform,
  Dimensions,
} from 'react-native';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import branch from 'react-native-branch';
import analytics from '@react-native-firebase/analytics';
import { OneSignal, LogLevel } from 'react-native-onesignal';
import { OverlayProvider } from 'stream-chat-react-native';
import * as Sentry from '@sentry/react-native';
import DeviceInfo from 'react-native-device-info';
import { RookSyncGate as RookSyncGateAndroid } from 'react-native-rook-sdk-health-connect';
import { RookSyncGate as RookSyncGateIOS } from 'react-native-rook-sdk-apple-health';
import { colors, header, headerTitleStyle } from './styles';
import * as screens from './screens';
import HeaderLeftButton from './components/HeaderLeftButton';
import * as db from './dataManager';
import { ROLES, getScreensFromRoutes } from './constants';
import { getFocusedRouteName } from './util/utils';
import {
  selectDeepLink,
  deselectDeepLink,
  registerForPush,
  logout,
} from './actions';
import nasm from './dataManager/apiConfig';
import artichoke from './api/artichoke/artichoke';
import BottomTab from './screens/BottomTab/BottomTab';
import { applicationInitAction } from './actions/artichoke/Application.actions';
import artichokeStack from './components/artichoke/navigators';
import HeaderRightButton from './components/HeaderRightButton';
import { BASE_URL } from './apiConstants';
import {
  ROOK_CLIENT_UUID,
  ROOK_ENV,
  ROOK_SECRET,
} from './api/rook/RookConstants';

const { height } = Dimensions.get('window');

const navTheme = DefaultTheme;
navTheme.colors.background = colors.white;

const RouterStack = createStackNavigator();

const AppStack = createStackNavigator();

const ModalStack = createStackNavigator();

const MainStack = createStackNavigator();

function MainStackComp(props) {
  return (
    <MainStack.Navigator
      screenOptions={({ navigation }) => ({
        title: null,
        gestureEnabled: false,
        headerBackTitle: null,
        headerTitleAlign: 'center',
        headerLeft: () => (
          <HeaderLeftButton onPress={() => navigation.goBack()} />
        ),
        headerRight: () => <View />, // empty view helps to center headers on Android
        headerStyle: {
          backgroundColor: props.route.params?.trainerActiveProfile?.ClubId
            ? colors.black
            : colors.duskBlue,
          borderBottomWidth: 0,
          shadowOffset: { height: 0, width: 0 },
        },
        headerTintColor: colors.lightBlue,
        headerTitleStyle,
        headerTitleContainerStyle: { width: '70%' },
      })}
    >
      <MainStack.Screen
        name="BottomTab"
        key="BottomTab"
        options={({ route }) => {
          const currentRoute = route.state?.routes[route.state.index];
          let headerShown = false;
          let title = '';
          let headerLeft = () => <View />;
          let headerRight = () => <View />;
          if (currentRoute) {
            headerShown = currentRoute.params?.title ?? false;
            title = currentRoute.params?.title ?? '';
            if (currentRoute.params?.renderHeaderLeft) {
              headerLeft = () => currentRoute.params.renderHeaderLeft(currentRoute.params);
            }
            if (currentRoute.params?.renderHeaderRight) {
              headerRight = () => currentRoute.params.renderHeaderRight(currentRoute.params);
            }
          }
          return {
            headerShown,
            title,
            headerLeft,
            headerRight,
          };
        }}
        component={BottomTab}
      />
      {getScreensFromRoutes(MainStack, screens.MainStack)}
      {artichokeStack(AppStack, props.route.params?.currentUser)}
      <MainStack.Screen
        name="tabClient"
        key="tabClient"
        component={screens.ClientTabs.ClientHomeTabs}
        options={({ navigation, route }) => {
          const title = route.params?.title ?? '';
          const renderHeaderRight = () => (
            <HeaderRightButton
              onPress={() => {
                if (route.params?.clearDay) {
                  route.params.clearDay();
                }
                if (navigation.canGoBack()) {
                  navigation.goBack();
                }
              }}
              title="Done"
              titleStyle={styles.headerButtonText}
            />
          );
          const renderHeaderLeft = () => <View />;
          return {
            title,
            headerLeft: renderHeaderLeft,
            headerRight: renderHeaderRight,
            headerShadowVisible: false,
          };
        }}
      />
      <MainStack.Screen
        name="GroupTabs"
        key="GroupTabs"
        component={screens.GroupTabs.GroupTabView}
      />
    </MainStack.Navigator>
  );
}

function ModalStackComp(props) {
  return (
    <ModalStack.Navigator
      detachInactiveScreens={false}
      screenOptions={{
        headerTitleAlign: 'center',
        headerShown: false,
        gestureEnabled: false,
        cardStyle: { backgroundColor: 'transparent' },
        presentation: 'modal',
      }}
    >
      <ModalStack.Screen
        name="MainStack"
        key="MainStack"
        component={MainStackComp}
        initialParams={{
          currentUser: props.route.params?.currentUser,
          trainerActiveProfile: props.route.params?.trainerActiveProfile,
        }}
      />
      {getScreensFromRoutes(ModalStack, screens.Modals)}
    </ModalStack.Navigator>
  );
}

const propTypes = {
  currentUser: PropTypes.shape({
    role: PropTypes.oneOf([ROLES.CLIENT, ROLES.TRAINER]),
    id: PropTypes.string,
  }),
  selectDeepLink: PropTypes.func,
  deselectDeepLink: PropTypes.func,
  deepLink: PropTypes.object,
  registerForPush: PropTypes.func,
};
const defaultProps = {};

class Router extends Component {
  constructor(properties) {
    super(properties);

    this.state = {
      appState: AppState.currentState,
      isShowingSubscriptionAlert: false,
      notificationId: null,
    };

    this.configureOneSignalSDK();
  }

  componentDidMount() {
    AppState.addEventListener('change', this.handleAppStateChange);
    this.subscribeToBranchLinks();
    nasm.api.setLogoutHandler(this.resetToLogin);
  }

  componentWillUnmount() {
    AppState.removeEventListener('change', this.handleAppStateChange);
    OneSignal.Notifications.removeEventListener('click', this.onOpened);
  }

  handleAppStateChange = (nextAppState) => {
    if (this.state.appState.match('background') && nextAppState === 'active') {
      if (
        this.props.currentUser
        && this.props.currentUser.role === ROLES.TRAINER
      ) {
        this.subscribeToBranchLinks();
      }
      if (this.state.isShowingSubscriptionAlert) {
        return;
      }
      // app entered foreground
      db.hasToken().then((hasToken) => {
        if (this.isUserLoggedIn(hasToken)) {
          // user is logged in
          // update device data
          this.props.registerForPush();

          // check if terms have updated
          nasm.api
            .checkTerms()
            .then(({ requires_pp_update, requires_tc_update }) => {
              if (requires_tc_update) {
                // display new terms
                this.props.navigation.navigate('TermsAndConditions', {
                  updatePrivacy: requires_pp_update,
                  navWhenDone: (navigation) => {
                    navigation.navigate('ModalStack');
                    this.deepLinkIfAvailable();
                  },
                });
              } else if (requires_pp_update) {
                this.props.navigation.navigate('PrivacyPolicy', {
                  navWhenDone: (navigation) => {
                    navigation.navigate('ModalStack');
                    this.deepLinkIfAvailable();
                  },
                });
              } else {
                this.deepLinkIfAvailable();
              }
            });
        }
      });
    }

    this.setState({ appState: nextAppState });
  };

  onOpened = async (openResult) => {
    this.setState({
      notificationId: openResult.notification?.notificationId,
    });
    await branch.getLatestReferringParams();
    if (
      openResult.notification.additionalData
      && openResult.notification.additionalData.branch
    ) {
      branch.openURL(openResult.notification.additionalData.branch);
    }
  };

  deepLinkIfAvailable = () => {
    // check if we need to deep link
    if (this.props.deepLink) {
      this.props.linkTo(this.props.deepLink);
      this.props.deselectDeepLink();
    }
  };

  isUserLoggedIn = (hasToken) => {
    let loggedIn = false;
    if (hasToken) {
      // make sure current user is set
      if (this.props.currentUser) {
        if (this.props.currentUser.role === ROLES.TRAINER) {
          loggedIn = true;
        } else if (this.props.selectedClient) {
          // cliens must have the sectedClient set
          loggedIn = true;
        }
      }
    }
    return loggedIn;
  };

  isWalkinClient = (user) => {
    if (!user) {
      return false;
    }
    if (user.role === ROLES.CLIENT) {
      return !user.client_user.trainer;
    }
    return false;
  };

  navigateToSubscriptions = () => {
    this.router.dispatch(
      CommonActions.navigate({
        name: 'Subscriptions',
      }),
    );
  };

  resetToLogin = () => {
    this.props.logout();
    const resetAction = CommonActions.reset({
      index: 0,
      routes: [{ name: 'Welcome' }],
    });
    this.router.dispatch(resetAction);
  };

  subscribeToBranchLinks = () => {
    branch.subscribe(({ error, params }) => {
      if (error) {
        return;
      }

      // params will never be null if error is null

      if (params['+non_branch_link']) {
        // const nonBranchUrl = params['+non_branch_link']
        // Route non-Branch URL if appropriate.
        return;
      }

      if (!params['+clicked_branch_link']) {
        // Indicates initialization success and some other conditions.
        // No link was opened.
        return;
      }

      // A Branch link was opened.
      if (params['~channel']) {
        analytics().setUserProperty('branch_link_channel', params['~channel']);
      }
      if (params['~campaign']) {
        analytics().setUserProperty(
          'branch_link_campaign',
          params['~campaign'],
        );
      }
      if (params['~id']) {
        analytics().setUserProperty('branch_link_id', `${params['~id']}`);
      }
      if (params['~referring_link']) {
        analytics().setUserProperty(
          'branch_link_url',
          params['~referring_link'],
        );
      }
      if (params['+referrer']) {
        analytics().setUserProperty(
          'branch_link_referrer',
          params['+referrer'],
        );
      }

      // check params for deep link info
      if (params.route_v2) {
        const deepLink = `/Router${params.route_v2}&notificationId=${this.state.notificationId}`;
        if (this.state.appState === 'active') {
          // app in forground
          // check if logged in
          db.hasToken().then((hasToken) => {
            if (this.isUserLoggedIn(hasToken)) {
              // user logged in
              // handle deep link now
              if (deepLink.includes('channelId')) {
                const screenName = this.props.currentUser.role === ROLES.TRAINER
                  ? 'ChannelList'
                  : 'Chat';
                const url = new URL(BASE_URL + deepLink);
                const channelId = new URLSearchParams(url.search).get(
                  'channelId',
                );
                const notificationId = new URLSearchParams(url.search).get(
                  'notificationId',
                );
                this.props.navigation.navigate(screenName, {
                  channelId,
                  notificationId,
                });
              } else {
                this.props.linkTo(deepLink);
              }
            } else {
              // user not logged in
              // handle deep link after login
              this.props.selectDeepLink(deepLink);
            }
          });
        } else {
          // app in background
          // handle deep link when app becomes active
          this.props.selectDeepLink(deepLink);
        }
      }
    });
  };

  configureOneSignalSDK() {
    const config = NativeModules.ENVConfig;
    if (config) {
      const id = config.oneSignalAppId;
      if (id) {
        OneSignal.Debug.setLogLevel(LogLevel.Warn);
        OneSignal.initialize(id);
        OneSignal.Notifications.addEventListener(
          'click',
          this.onOpened.bind(this),
        );
      }
    }
  }

  renderAppComponent = () => {
    const params = {
      linkTo: this.props.linkTo,
    };
    return (
      <AppStack.Navigator
        screenOptions={({ route, navigation }) => {
          const headerOption = { ...header.default };
          if (route.name === 'ModalStack') {
            headerOption.headerShown = false;
          }
          return {
            title: null,
            gestureEnabled: false,
            headerBackTitle: null,
            headerTitleAlign: 'center',
            headerLeft: () => (
              <HeaderLeftButton onPress={() => navigation.goBack()} />
            ),
            headerRight: () => <View />, // empty view helps to center headers on Android
            ...headerOption,
          };
        }}
      >
        {getScreensFromRoutes(AppStack, screens.AppStack, params)}
        <AppStack.Screen
          name="ModalStack"
          key="ModalStack"
          component={ModalStackComp}
          initialParams={{ currentUser: this.props.currentUser }}
        />
        <AppStack.Screen
          name="WebView"
          key="WebView"
          options={screens.WebView.navigationOptions}
          component={screens.WebView}
        />
      </AppStack.Navigator>
    );
  };

  renderAndroidElement = () => (
    <RookSyncGateAndroid
      environment={ROOK_ENV}
      clientUUID={ROOK_CLIENT_UUID}
      password={ROOK_SECRET}
    >
      {this.renderAppComponent()}
    </RookSyncGateAndroid>
  );

  renderIOSElement = () => (
    <RookSyncGateIOS
      environment={ROOK_ENV}
      clientUUID={ROOK_CLIENT_UUID}
      password={ROOK_SECRET}
    >
      {this.renderAppComponent()}
    </RookSyncGateIOS>
  );

  render() {
    return Platform.OS === 'android'
      ? this.renderAndroidElement()
      : this.renderIOSElement();
  }
}

// Export
Router.propTypes = propTypes;
Router.defaultProps = defaultProps;
const mapStateToProps = (state) => ({
  currentUser: state.currentUser,
  trainerActiveProfile: state.trainerActiveProfile,
  selectedClient: state.selectedClient,
  deepLink: state.deepLink,
  applicationLoading: state.application.applicationLoading,
  login: state.login,
  user: state.user,
});

const mapDispatchToProps = {
  selectDeepLink,
  deselectDeepLink,
  registerForPush,
  logout,
  applicationInitAction,
};
const ConnectedComponent = connect(mapStateToProps, mapDispatchToProps)(Router);

function RouterComponent(props) {
  const linkTo = useLinkTo();
  return <ConnectedComponent {...props} linkTo={linkTo} />;
}

export default function () {
  const [focusedRouteName, setFocusedRouteName] = useState('');
  const config = {
    screens: {
      Router: {
        screens: {
          ModalStack: {
            screens: {
              MainStack: {
                path: '/Router/ModalStack/MainStack',
                screens: {
                  BottomTab: {
                    path: '/BottomTab',
                    screens: {
                      Home: {
                        path: '/Home',
                        screens: {
                          Chat: {
                            screens: {
                              ChannelList: '/Chat/ChannelList',
                            },
                          },
                        },
                      },
                      ServicesTab: '/ServicesTab',
                      Libraries: '/Libraries',
                      Account: '/Account',
                    },
                  },
                  stripecallback: '/stripecallback',
                },
              },
              AddClientModal: {
                screens: {
                  CreateClient:
                    '/Router/ModalStack/AddClientModal/CreateClient',
                },
              },
              ClubConnectModal: {
                screens: {
                  CreateClient:
                    '/Router/ModalStack/ClubConnectModal/ClubConnectCreateClient',
                },
              },
            },
          },
        },
      },
    },
  };
  const linking = {
    prefixes: ['nasm://'],
    config,
  };
  return (
    <NavigationContainer
      ref={(router) => {
        artichoke.api.setRouterContext(router);
      }}
      linking={linking}
      theme={navTheme}
      onStateChange={(newNavState) => {
        const newFocusedRouteName = getFocusedRouteName(newNavState);
        if (newFocusedRouteName !== focusedRouteName) {
          Sentry.addBreadcrumb({
            category: 'navigation',
            data: {
              to: newFocusedRouteName,
              from: focusedRouteName,
            },
            level: 'info',
          });
          setFocusedRouteName(newFocusedRouteName);
        }
      }}
    >
      <OverlayProvider
        bottomInset={bottomSheetInset}
        topInset={Platform.OS === 'android' ? 100 : 50}
        attachmentPickerBottomSheetHeight={300}
        attachmentSelectionBarHeight={selectionBarHeight()}
        translucentStatusBar
      >
        <RouterStack.Navigator screenOptions={{ headerShown: false }}>
          <RouterStack.Screen
            name="Router"
            key="Router"
            component={RouterComponent}
          />
        </RouterStack.Navigator>
      </OverlayProvider>
    </NavigationContainer>
  );
}

const bottomSheetInset = () => {
  if (Platform.OS === 'ios' && height > 667) {
    return 75;
  }
  return 50;
};

const selectionBarHeight = () => {
  if (DeviceInfo.isTablet()) {
    return 65;
  }
  if (Platform.OS === 'android') {
    return 50;
  }
  return 40;
};

const styles = {
  headerButtonText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
};
