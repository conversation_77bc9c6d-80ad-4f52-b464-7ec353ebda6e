import { all } from 'redux-saga/effects';
import { applicationInitSaga } from './Application.saga';
import { servicesSaga } from './Services.saga';
import { locationsSaga } from './Locations.saga';
import { appointmentsSaga } from './Appointments.saga';
import { clientsSaga } from './Clients.saga';
import { clientAppointmentsSaga } from './ClientAppointments.saga';
import { clientServicesSaga } from './ClientServices.saga';
import { salesSaga } from './Sales.saga';

export default function* rootSaga() {
  yield all([
    applicationInitSaga(),
    appointmentsSaga(),
    locationsSaga(),
    servicesSaga(),
    clientsSaga(),
    clientAppointmentsSaga(),
    clientServicesSaga(),
    salesSaga(),
  ]);
}
