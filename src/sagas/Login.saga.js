import { call, put, takeLatest } from 'redux-saga/effects';
import { login } from '../api/artichoke/Authentication.api';
import {
  LOGIN,
  createSetLoginStateAction,
  LOGOUT,
} from '../actions/artichoke/Login.actions';
import { saveToStorage } from '../util/Storage.utils';
import nasm from '../dataManager/apiConfig';
import {
  clearStateOnLogoutAction,
  SetApplicationStateAction,
} from '../actions/artichoke/Application.actions';

function* authenticate(action) {
  try {
    yield call(saveToStorage, 'isLoggedIn', true);
    yield put(createSetLoginStateAction(true));
    const response = yield call(login, action.payload);
    if (response.status === 200) {
      yield call(saveToStorage, 'isLoggedIn', true);
      yield put(createSetLoginStateAction(true));
    }
  } catch (error) {}
}

function* logout() {
  try {
    yield put(SetApplicationStateAction(true));
    yield call(nasm.api.deleteToken);
    yield put(createSetLoginStateAction(false));
    yield put(SetApplicationStateAction(false));
    yield put(clearStateOnLogoutAction());
  } catch (error) {
    yield put(SetApplicationStateAction(false));
  }
}
export function* loginSaga() {
  yield takeLatest(LOGIN, authenticate);
  yield takeLatest(LOGOUT, logout);
}
