import {
  call, put, takeLatest, select,
} from 'redux-saga/effects';
import moment from 'moment';
import * as Services from '../api/artichoke/client/Services.api';
import {
  CLIENT_SET_SERVICE_LOCATIONS,
  CLIENT_GET_SERVICE_BY_ID_ACTION,
  setSelectedServiceAction,
} from '../actions/artichoke/Services.actions';

import { setLoaderStateAction } from '../actions/artichoke/Loaders.actions';
import { createSetLoginStateAction } from '../actions/artichoke/Login.actions';
import {
  setSelectedLocationAction,
  setSelectedExtraLocationAction,
} from '../actions/artichoke/Locations.actions';

function* setServiceLocations(action) {
  const service = yield select((state) => state.services.selectedService);
  if (service) {
    const newService = {
      ...service,
      clientLocationEnabled: action.payload.clientLocationEnabled,
      offeredOnline: actions.payload.offeredOnline,
      selfBookingAddresses: action.payload.locationsList,
    };
    yield put(setSelectedServiceAction(newService));
  }
}

function* getService(action) {
  try {
    yield put(setLoaderStateAction({key: 'getServiceLoading', value: true}));
    const response = yield call(Services.getServiceById, action.payload);
    if (response.status === 200) {
      yield put(setSelectedServiceAction(response.data));
      const productAddresses = response.data.selfBookingAddresses || [];
      const locations = productAddresses.map((location) => ({ id: location.id }));
      const extraLocations = [];
      if (response.data.clientLocationEnabled) {
        extraLocations.push({ id: 0 });
      }
      if (response.data.offeredOnline) {
        extraLocations.push({ id: 1 });
      }
      yield put(setSelectedExtraLocationAction(extraLocations));
      yield put(setSelectedLocationAction(locations));
      yield put(setLoaderStateAction({key: 'getServiceLoading', value: false}));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({key: 'getServiceLoading', value: false}));
  }
}

export function* clientServicesSaga() {
  yield takeLatest(CLIENT_SET_SERVICE_LOCATIONS, setServiceLocations);
  yield takeLatest(CLIENT_GET_SERVICE_BY_ID_ACTION, getService);
}
