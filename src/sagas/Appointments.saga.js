import {
  call, put, takeLatest, select,
} from 'redux-saga/effects';
import moment from 'moment';
import { Alert } from 'react-native';
import * as Appointments from '../api/artichoke/Appointments.api';
import * as Clients from '../api/artichoke/Clients.api';
import {
  GET_APPOINTMENTS_BY_DATE,
  GET_CLIENT_BOOKINGS_ACTION,
  GET_SERVICE_APPOINTMENTS_ACTION,
  GET_BLOCKING_APPOINTMENTS_BY_DATE,
  SAVE_APPOINTMENT_ACTION,
  UPDATE_APPOINTMENT_ACTION,
  GET_APPOINTMENT_ACTION,
  DELETE_APPOINTMENT_ACTION,
  SAVE_SLOTBLOCKER_ACTION,
  GET_SLOTBLOCKER_ACTION,
  UPDATE_SLOTBLOCKER_ACTION,
  DELETE_SLOTBLOCKER_ACTION,
  CHECKIN_WITH_BALANCE_ACTION,
  CHECKIN_WITH_CASH_OR_CHECK_ACTION,
  <PERSON>EC<PERSON><PERSON>_FREE_ACTION,
  CHECKIN_WITH_CREDIT_CARD_ACTION,
  SAVE_CLASS_ACTION,
  BUY_PACKAGE_WITH_CASH_OR_CHECK_ACTION,
  BUY_SERVICE_WITH_CASH_OR_CHECK_ACTION,
  BUY_PACKAGE_WITH_CREDIT_CARD_ACTION,
  BUY_SERVICE_WITH_CREDIT_CARD_ACTION,
  GET_CLASS_ACTION,
  UPDATE_CLASS_ACTION,
  setAppointmentsByDateAction,
  setServiceAppointmentsAction,
  setBlockingAppointmentsByDateAction,
  setSelectedAppointmentAction,
  setSelectedSlotBlockerAction,
  setClassAction,
  setClientBookingsAction,
  setSuccessAction,
  setPurchasingAction,
  GET_APPOINTMENT_SERVICE_DETAILS,
  setNewAppointmentValuesAction,
  GET_CLASS_SERVICE_DETAILS,
  setNewClassValuesAction,
  END_VIDEO_CALL_SESSION_ACTION,
  UNCHECKIN_ACTION,
} from '../actions/artichoke/Appointments.actions';

import { createSetLoginStateAction } from '../actions/artichoke/Login.actions';
import { setLoaderStateAction } from '../actions/artichoke/Loaders.actions';
import { APPLICATION_ROUTES, ROLES } from '../constants';
import * as NavigatorService from '../util/NavigatorService';
import {
  logSuccessfulStripePurchase,
  logFailedStripePurchase,
  PRODUCT_TYPES,
  PAYMENT_METHODS,
  track,
} from '../util/Analytics';
import * as Services from '../api/artichoke/Services.api';

function* getAppointmentsList(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'appointmentsListLoading', value: true }),
    );
    const currentDate = moment().unix() * 1000;
    const response = yield call(
      Appointments.getServiceAppointments,
      action.payload,
      currentDate,
    );
    if (response.status === 200) {
      yield put(setServiceAppointmentsAction(response.data.Appointment));
    }
    yield put(
      setLoaderStateAction({ key: 'appointmentsListLoading', value: false }),
    );
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'appointmentsListLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}
function* getAppointmentsListByDate(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'appointmentsListLoading', value: true }),
    );
    const responseActiveUser = yield select((state) => state.user.details);
    let startOfMonth = moment().startOf('month').format('YYYY-MM-DD');
    if (action?.payload) {
      startOfMonth = moment(action.payload)
        .startOf('month')
        .format('YYYY-MM-DD');
    }
    const response = yield call(
      Appointments.getAppointmentsByDate,
      responseActiveUser.id,
      startOfMonth,
      31,
    );
    if (response.status === 200) {
      yield put(setAppointmentsByDateAction(response.data.appointments));
      yield put(
        setLoaderStateAction({ key: 'appointmentsListLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'appointmentsListLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}
function* getBlockingAppointmentsListByDate(action) {
  const date = moment(action.payload).format('YYYY-MM-DD');
  const userId = yield select((state) => state.user.details.id);
  try {
    const response = yield call(
      Appointments.getBookedAppointmentsByDate,
      userId,
      date,
      1,
    );
    if (response.status === 200) {
      yield put(setBlockingAppointmentsByDateAction(response.data.Appointment));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}
function showAlert() {
  return new Promise((resolve) => {
    Alert.alert(
      'Conflict Alert',
      'This repeating appointment will overlaps existing appointments.',
      [
        {
          text: 'Double Book',
          onPress: () => {
            resolve(true);
          },
        },
        {
          text: 'Cancel',
          onPress: () => {
            resolve(false);
          },
        },
      ],
      { cancelable: false },
    );
  });
}
function showOverlapsClassAlert() {
  return new Promise((resolve) => {
    Alert.alert(
      'Conflict Alert',
      'This repeating class will overlaps existing classes.',
      [
        {
          text: 'Continue',
          onPress: () => {
            resolve(true);
          },
        },
        {
          text: 'Cancel',
          onPress: () => {
            resolve(false);
          },
        },
      ],
      { cancelable: false },
    );
  });
}
function showDeleteAlert() {
  return new Promise((resolve) => {
    Alert.alert(
      'Delete Alert',
      'This is a repeating event.',
      [
        {
          text: 'Delete This Event Only',
          onPress: () => {
            resolve({ deleteAll: false });
          },
        },
        {
          text: 'Delete All Future Events',
          onPress: () => {
            resolve({ deleteAll: true });
          },
        },
        {
          text: 'Cancel',
          onPress: () => {
            resolve(null);
          },
        },
      ],
      { cancelable: false },
    );
  });
}

function* saveAppointment() {
  yield put(setLoaderStateAction({ key: 'saveSessionLoading', value: true }));
  const newAppointment = yield select(
    (state) => state.appointments.newAppointmentValues,
  );
  const isSendNotificationsEnabled = yield select(
    (state) => state.appointments.newAppointmentValues.sendNotifications,
  );
  const userId = yield select((state) => state.user.details.id);

  const arrClients = newAppointment.clients.map((client) => ({
    Client: { id: client.id },
  }));
  let name = '';
  if (newAppointment.clients.length > 1) {
    name = 'Multiple Clients';
  } else if (newAppointment.clients[0].user) {
    name = `${newAppointment.clients[0].user.firstName} ${newAppointment.clients[0].user.lastName}`;
  } else if (
    newAppointment.clients[0].Client
    && newAppointment.clients[0].Client.user
  ) {
    name = `${newAppointment.clients[0].Client.user.firstName} ${newAppointment.clients[0].Client.user.lastName}`;
  }

  const appointment = {
    Invitee: arrClients,
    ProductDuration: { id: newAppointment.service.ProductDuration[0].id },
    addOnProducts: null,
    clientAddressId: null, // for the moment
    description: newAppointment.service.description,
    doubleBook: false, // if overlaps
    start: `${moment(newAppointment.date, 'MM/DD/YYYY').format('MM/DD/YYYY')} ${
      newAppointment.time
    }`,
    end: moment(
      `${moment(newAppointment.date, 'MM/DD/YYYY').format('MM/DD/YYYY')} ${
        newAppointment.time
      }`,
      'MM/DD/YYYY HH:mm A',
    )
      .add(newAppointment.service.ProductDuration[0].duration.duration, 'm')
      .format('MM/DD/YYYY hh:mm A'),
    name,
    onsiteAddressId:
      newAppointment.appointmentLocation.id === 1
      || newAppointment.appointmentLocation.id === 2
        ? 0
        : newAppointment.appointmentLocation.id,
    remotely: newAppointment.appointmentLocation.id === 1, // for the moment
    inapp: newAppointment.appointmentLocation.id === 2,
    clientAddressEnable: newAppointment.appointmentLocation.id === 0,
    userId,
  };

  if (newAppointment.repeatIntervalType) {
    appointment.Repeat = {
      RepeatInterval: {
        DayAndTimesOfWeek: [
          {
            MinuteOfHour: moment(newAppointment.time, 'hh:mm A').format('m'),
            DaysOfWeek: moment(newAppointment.date, 'MM/DD/YYYY')
              .format('dddd')
              .toUpperCase(),
            hourOfDay: moment(newAppointment.time, 'hh:mm A').format('H'),
          },
        ],
        RepeatIntervalType: newAppointment.repeatIntervalType,
      },
      RepeatUntil: {
        RepeatUntilType: newAppointment.repeatUntilType,
        count: newAppointment.count,
        untilDay: newAppointment.untilDay
          ? moment(newAppointment.untilDay, 'MM/DD/YYYY').format('MM/DD/YYYY')
          : null,
      },
    };
  }

  try {
    const response = yield call(
      Appointments.checkOverlapsAppointment,
      appointment,
    );
    if (response.status === 200) {
      if (response.data.Appointment.length) {
        const alertResponse = yield call(showAlert);
        if (alertResponse) {
          appointment.doubleBook = true;
          const responseSaveDouble = yield call(
            Appointments.saveAppointment,
            appointment,
            isSendNotificationsEnabled,
          );
          if (responseSaveDouble.status === 201) {
            const responseApp = yield call(
              Appointments.getAppointmentById,
              responseSaveDouble.data.id,
              track('appointment_created'),
            );
            if (responseApp.status === 200) {
              yield put(setSelectedAppointmentAction(responseApp.data));

              if (responseApp.data.inapp.toString() === 'true') {
                track('video_call_appointment_created', {
                  channel: responseApp.data.appointmentToken,
                });
              }
              yield getAppointmentsListByDate();
              NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
            }
          }
        }
      } else {
        const responseSave = yield call(
          Appointments.saveAppointment,
          appointment,
          isSendNotificationsEnabled,
        );
        if (responseSave.status === 201) {
          const responseApp = yield call(
            Appointments.getAppointmentById,
            responseSave.data.id,
            track('appointment_created'),
          );
          if (responseApp.status === 200) {
            yield put(setSelectedAppointmentAction(responseApp.data));
            yield getAppointmentsListByDate();
            NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
          }
        }
      }
    }
    yield put(
      setLoaderStateAction({ key: 'saveSessionLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on saving appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'saveSessionLoading', value: false }),
    );
  }
}

function* updateAppointment(action) {
  yield put(
    setLoaderStateAction({ key: 'updateAppointmentLoading', value: true }),
  );
  const newAppointment = yield select(
    (state) => state.appointments.newAppointmentValues,
  );
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );
  const isSendNotificationsEnabled = yield select(
    (state) => state.appointments.newAppointmentValues.sendNotifications,
  );
  const userId = yield select((state) => state.user.details.id);

  const arrClients = newAppointment.clients.map((client) => ({
    Client: { id: client.Client ? client.Client.id : client.id },
  }));
  if (!newAppointment.isScheduled && arrClients.length < 1) {
    yield put(
      setLoaderStateAction({ key: 'updateAppointmentLoading', value: false }),
    );
    Alert.alert(
      'Sessions must have at least one client selected',
      'Please consider cancelling the session',
      [
        {
          text: 'Ok',
        },
      ],
      { cancelable: false },
    );
  } else {
    let appointmentName = '';
    if (newAppointment.clients.length > 1) {
      appointmentName = 'Multiple Clients';
    } else if (newAppointment.clients && newAppointment.clients.length === 1) {
      appointmentName = newAppointment.clients[0].Client
        ? `${newAppointment.clients[0].Client.user.firstName} ${newAppointment.clients[0].Client.user.lastName}`
        : `${newAppointment.clients[0].user.firstName} ${newAppointment.clients[0].user.lastName}`;
    }
    const appointmentId = newAppointment.appointmentLocation?.id === 1
      || newAppointment.appointmentLocation?.id === 2
      ? 0
      : newAppointment.appointmentLocation?.id;
    const appointment = {
      id: selectedAppointment.id,
      Invitee: arrClients,
      ProductDuration: { id: newAppointment.service.ProductDuration[0].id },
      addOnProducts: null,
      clientAddressId: null, // for the moment
      description: newAppointment.service.name,
      doubleBook: false, // if overlaps
      start: `${moment(newAppointment.date, 'MM/DD/YYYY').format(
        'MM/DD/YYYY',
      )} ${newAppointment.time}`,
      end: moment(
        `${moment(newAppointment.date, 'MM/DD/YYYY').format('MM/DD/YYYY')} ${
          newAppointment.time
        }`,
        'MM/DD/YYYY HH:mm A',
      )
        .add(newAppointment.service.ProductDuration[0].duration.duration, 'm')
        .format('MM/DD/YYYY hh:mm A'),
      name: appointmentName,
      onsiteAddressId: newAppointment.appointmentLocation
        ? appointmentId
        : null,
      remotely:
        newAppointment.remotely.toString() === 'true'
          ? true
          : newAppointment.appointmentLocation?.id === 1, // for the moment
      clientAddressEnable:
        newAppointment.clientAddressEnable.toString() === 'true'
          ? true
          : newAppointment.appointmentLocation?.id === 0, // for the moment
      inapp:
        newAppointment.inapp.toString() === 'true'
          ? true
          : newAppointment.appointmentLocation?.id === 2, // for the moment
      userId,
    };
    if (newAppointment.repeatIntervalType) {
      appointment.Repeat = {
        RepeatInterval: {
          DayAndTimesOfWeek: [
            {
              MinuteOfHour: moment(newAppointment.time, 'hh:mm A').format('m'),
              DaysOfWeek: moment(newAppointment.date, 'MM/DD/YYYY')
                .format('dddd')
                .toUpperCase(),
              hourOfDay: moment(newAppointment.time, 'hh:mm A').format('H'),
            },
          ],
          RepeatIntervalType: newAppointment.repeatIntervalType,
        },
        RepeatUntil: {
          RepeatUntilType: newAppointment.repeatUntilType,
          count: newAppointment.count,
          untilDay: newAppointment.untilDay
            ? moment(newAppointment.untilDay, 'MM/DD/YYYY').format('MM/DD/YYYY')
            : null,
        },
      };
    } else {
      appointment.Repeat = null;
    }
    const allInSeries = action.payload !== null
      ? `?allInSeries=${action.payload.allInSeries}`
      : '';
    try {
      const response = yield call(
        Appointments.checkOverlapsAppointment,
        appointment,
      );
      if (response.status === 200) {
        if (response.data.Appointment.length) {
          const alertResponse = yield call(showAlert);
          if (alertResponse) {
            appointment.doubleBook = true;
            const responseSaveDouble = yield call(
              Appointments.updateAppointment,
              appointment,
              allInSeries,
              isSendNotificationsEnabled,
            );
            if (responseSaveDouble.status === 200) {
              const responseApp = yield call(
                Appointments.getAppointmentById,
                appointment.id,
              );
              if (responseApp.status === 200) {
                yield put(setSelectedAppointmentAction(responseApp.data));
                yield call(getAppointmentsListByDate);
                NavigatorService.navigate(
                  APPLICATION_ROUTES.INDIVIDUAL_APPOINTMENT,
                );
              }
            }
          }
        } else {
          const responseSave = yield call(
            Appointments.updateAppointment,
            appointment,
            allInSeries,
            isSendNotificationsEnabled,
          );
          if (responseSave.status === 200) {
            const responseApp = yield call(
              Appointments.getAppointmentById,
              appointment.id,
            );
            if (responseApp.status === 200) {
              yield put(setSelectedAppointmentAction(responseApp.data));
              yield call(getAppointmentsListByDate);
              NavigatorService.navigate(
                APPLICATION_ROUTES.INDIVIDUAL_APPOINTMENT,
              );
            }
          }
        }
        yield put(
          setLoaderStateAction({
            key: 'updateAppointmentLoading',
            value: false,
          }),
        );
      }
    } catch (error) {
      if (error.response.status === 401) {
        yield put(createSetLoginStateAction(false));
      } else {
        Alert.alert(
          'Error on saving appointment',
          error.response.data.message,
          [
            {
              text: 'Ok',
            },
          ],
          { cancelable: false },
        );
      }
      yield put(
        setLoaderStateAction({ key: 'updateAppointmentLoading', value: false }),
      );
    }
  }
}

function* getAppointment(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'showAppointmentLoading', value: true }),
    );
    const response = yield call(
      Appointments.getAppointmentById,
      action.payload,
    );
    if (response.status === 200) {
      yield put(setSelectedAppointmentAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'showAppointmentLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'showAppointmentLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on saving appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  }
}

function* deleteAppointment(action) {
  yield put(
    setLoaderStateAction({ key: 'cancelAppointmentLoading', value: true }),
  );
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );
  try {
    if (selectedAppointment.Repeat.RepeatInterval.RepeatIntervalType) {
      const alertResponse = yield call(showDeleteAlert);
      if (alertResponse) {
        const deleteAll = alertResponse !== null ? `?deleteAll=${alertResponse.deleteAll}` : '';
        const responseDeleteEvents = yield call(
          Appointments.deleteAppointment,
          selectedAppointment.id,
          deleteAll,
        );
        if (responseDeleteEvents.status === 200) {
          yield call(getAppointmentsListByDate);
          NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
          yield put(
            setLoaderStateAction({
              key: 'cancelAppointmentLoading',
              value: false,
            }),
          );
        }
      } else {
        yield put(
          setLoaderStateAction({
            key: 'cancelAppointmentLoading',
            value: false,
          }),
        );
      }
    } else {
      let responseDelete;
      if (action.payload) {
        const deleteAll = `?deleteAll=${action.payload}`;
        responseDelete = yield call(
          Appointments.deleteAppointment,
          selectedAppointment.id,
          deleteAll,
        );
      } else {
        responseDelete = yield call(
          Appointments.deleteAppointment,
          selectedAppointment.id,
          '?deleteAll=false',
        );
      }
      if (responseDelete.status === 200) {
        yield call(getAppointmentsListByDate);
        NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
        yield put(
          setLoaderStateAction({
            key: 'cancelAppointmentLoading',
            value: false,
          }),
        );
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
      yield put(
        setLoaderStateAction({ key: 'cancelAppointmentLoading', value: false }),
      );
    } else {
      Alert.alert(
        'Error on deleting appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'cancelAppointmentLoading', value: false }),
    );
  }
}

function* saveSlotBlocker() {
  yield put(
    setLoaderStateAction({ key: 'saveSlotBlockerLoading', value: true }),
  );
  const newSlotBlocker = yield select(
    (state) => state.appointments.newSlotBlocker,
  );
  const userId = yield select((state) => state.user.details.id);

  const slotblocker = {
    description: newSlotBlocker.description,
    start: `${newSlotBlocker.date} ${newSlotBlocker.start}`,
    end: `${newSlotBlocker.date} ${newSlotBlocker.end}`,
    userId,
  };

  if (newSlotBlocker.repeatIntervalType) {
    slotblocker.Repeat = {
      RepeatInterval: {
        RepeatIntervalType: newSlotBlocker.repeatIntervalType,
      },
      RepeatUntil: {
        RepeatUntilType: newSlotBlocker.repeatUntilType,
        count: newSlotBlocker.count,
        untilDay: newSlotBlocker.untilDay ? newSlotBlocker.untilDay : null,
      },
    };
  }

  try {
    const responseSave = yield call(Appointments.saveSlotBlocker, slotblocker);
    if (responseSave.status === 201 || responseSave.status === 200) {
      yield getAppointmentsListByDate();
      NavigatorService.navigate(APPLICATION_ROUTES.DASHBOARD);
      yield put(
        setLoaderStateAction({ key: 'saveSlotBlockerLoading', value: false }),
      );
    }
  } catch (error) {
    if (
      error.response
      && error.response.status
      && error.response.status === 401
    ) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on saving slotblocker',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'saveSlotBlockerLoading', value: false }),
    );
  }
}

function* getSlotBlocker(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'showSlotBlockerLoading', value: true }),
    );
    const response = yield call(
      Appointments.getSlotBlockerById,
      action.payload,
    );
    if (response.status === 200) {
      yield put(setSelectedSlotBlockerAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'showSlotBlockerLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'showSlotBlockerLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* deleteSlotBlocker() {
  yield put(
    setLoaderStateAction({ key: 'deleteSlotBlockerLoading', value: true }),
  );
  const selectedSlotBlocker = yield select(
    (state) => state.appointments.selectedSlotBlocker,
  );
  try {
    if (selectedSlotBlocker.Repeat.RepeatInterval.RepeatIntervalType) {
      const alertResponse = yield call(showDeleteAlert);
      if (alertResponse) {
        const deleteAll = alertResponse !== null ? `?deleteAll=${alertResponse.deleteAll}` : '';
        const responseDeleteEvents = yield call(
          Appointments.deleteSlotBlocker,
          selectedSlotBlocker.id,
          deleteAll,
        );
        if (responseDeleteEvents.status === 200) {
          yield call(getAppointmentsListByDate);
          NavigatorService.navigate(APPLICATION_ROUTES.DASHBOARD);
          yield put(
            setLoaderStateAction({
              key: 'deleteSlotBlockerLoading',
              value: false,
            }),
          );
        }
      } else {
        yield put(
          setLoaderStateAction({
            key: 'deleteSlotBlockerLoading',
            value: false,
          }),
        );
      }
    } else {
      const responseDelete = yield call(
        Appointments.deleteSlotBlocker,
        selectedSlotBlocker.id,
        '?deleteAll=false',
      );

      if (responseDelete.status === 200) {
        yield call(getAppointmentsListByDate);
        NavigatorService.navigate(APPLICATION_ROUTES.DASHBOARD);
        yield put(
          setLoaderStateAction({
            key: 'deleteSlotBlockerLoading',
            value: false,
          }),
        );
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on deleting slotblocker',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'deleteSlotBlockerLoading', value: false }),
    );
  }
}

function* updateSlotBlocker() {
  yield put(
    setLoaderStateAction({ key: 'updateSlotBlockerLoading', value: true }),
  );
  const newSlotBlocker = yield select(
    (state) => state.appointments.newSlotBlocker,
  );
  const selectedSlotBlocker = yield select(
    (state) => state.appointments.selectedSlotBlocker,
  );
  const userId = yield select((state) => state.user.details.id);

  const slotblocker = {
    id: selectedSlotBlocker.id,
    description: newSlotBlocker.description,
    start: `${newSlotBlocker.date} ${newSlotBlocker.start}`,
    end: `${newSlotBlocker.date} ${newSlotBlocker.end}`,
    userId,
  };

  if (newSlotBlocker.repeatIntervalType) {
    slotblocker.Repeat = {
      RepeatInterval: {
        RepeatIntervalType: newSlotBlocker.repeatIntervalType,
      },
      RepeatUntil: {
        RepeatUntilType: newSlotBlocker.repeatUntilType,
        count: newSlotBlocker.count,
        untilDay: newSlotBlocker.untilDay ? newSlotBlocker.untilDay : null,
      },
    };
  }
  try {
    const responseUpdate = yield call(
      Appointments.updateSlotBlocker,
      slotblocker,
      'true',
    );
    if (responseUpdate.status === 201 || responseUpdate.status === 200) {
      NavigatorService.navigate(APPLICATION_ROUTES.DASHBOARD);
      yield getAppointmentsListByDate();
      yield put(
        setLoaderStateAction({ key: 'updateSlotBlockerLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
      yield put(
        setLoaderStateAction({ key: 'updateSlotBlockerLoading', value: false }),
      );
    } else {
      Alert.alert(
        'Error on saving slotblocker',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'updateSlotBlockerLoading', value: false }),
    );
  }
}

function* checkInWithBalance() {
  yield put(setLoaderStateAction({ key: 'checkInBalances', value: true }));
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );
  try {
    const responseCheckIn = yield call(
      Appointments.checkIn,
      selectedAppointment.id,
      selectedClient?.Client.id,
    );
    if (responseCheckIn.status === 201) {
      const responseApp = yield call(
        Appointments.getAppointmentById,
        selectedAppointment.id,
      );
      if (responseApp.status === 200) {
        yield put(setSelectedAppointmentAction(responseApp.data));
        yield getAppointmentsListByDate();
        const success = {
          title: 'Success',
          message: 'Your client has been checked in.',
        };
        yield put(setSuccessAction(success));
        track('checkin_client');
      }
    }
    yield put(setLoaderStateAction({ key: 'checkInBalances', value: false }));
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on check in client',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(setLoaderStateAction({ key: 'checkInBalances', value: false }));
  }
}

function* checkInWithCashOrCheck() {
  yield put(
    setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: true }),
  );
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const clientsPurchase = yield select((state) => state.clients.clientsPurchase);
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );

  let discount = 0;
  if (clientsPurchase.discount) {
    if (clientsPurchase.discount.type === 'PERCENTAGE') {
      discount = (selectedAppointment.ProductDuration.price
          * clientsPurchase.discount.amount)
        / 100;
    } else {
      discount = clientsPurchase.discount.amount;
    }
  }

  const taxes = ((selectedAppointment.ProductDuration.price - discount)
      * selectedAppointment.ProductDuration.productTax)
    / 100;

  const amountToPay = selectedAppointment.ProductDuration.price - discount + taxes;

  const newClientPurchases = {
    ClientPurchases: [
      {
        amountDue: amountToPay < 0 ? 0 : amountToPay,
        amountPaid: amountToPay < 0 ? 0 : amountToPay,
        cashBalanceToApply: 0,
        checkDate: moment().format('L LT'),
        checkNumber: '',
        clientId: selectedClient?.Client.id,
        clientPurchaseProductDurations: [
          {
            id: parseInt(selectedAppointment.ProductDuration.productId, 10),
            price: selectedAppointment.ProductDuration.price,
            productDurationId: parseInt(
              selectedAppointment.ProductDuration.id,
              10,
            ),
            quantity: 1,
            ProductDuration: selectedAppointment.ProductDuration,
          },
        ],
        discount,
        giftBalanceApplied: 0,
        giftCode: '',
        previousGiftCardId: null,
        purchaseType: 'CASH_OR_CHECK',
        tip: 0,
        taxApplied: taxes,
      },
    ],
  };
  try {
    const responseCheckIn = yield call(
      Clients.purchase,
      selectedClient?.Client.id,
      newClientPurchases,
    );
    if (responseCheckIn.status === 201) {
      const responseBalance = yield call(
        Appointments.checkIn,
        selectedAppointment.id,
        selectedClient?.Client.id,
      );
      if (responseBalance.status === 201) {
        const responseApp = yield call(
          Appointments.getAppointmentById,
          selectedAppointment.id,
        );
        if (responseApp.status === 200) {
          yield put(setSelectedAppointmentAction(responseApp.data));
          yield getAppointmentsListByDate();
          const success = {
            title: 'Success',
            message: 'Your client has been checked in.',
          };
          yield put(setSuccessAction(success));
          track('checkin_client');
        }
      }
    }
    yield put(
      setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on check in client',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: false }),
    );
  }
}

function* checkInFree() {
  yield put(
    setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: true }),
  );
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );

  const newClientPurchases = {
    ClientPurchases: [
      {
        amountDue: 0,
        amountPaid: 0,
        cashBalanceToApply: 0,
        checkDate: moment().format('L LT'),
        checkNumber: '',
        clientId: selectedClient?.Client.id,
        clientPurchaseProductDurations: [
          {
            id: parseInt(selectedAppointment.ProductDuration.productId, 10),
            price: selectedAppointment.ProductDuration.price,
            productDurationId: parseInt(
              selectedAppointment.ProductDuration.id,
              10,
            ),
            quantity: 1,
            ProductDuration: selectedAppointment.ProductDuration,
          },
        ],
        discount: 0,
        giftBalanceApplied: 0,
        giftCode: '',
        previousGiftCardId: null,
        purchaseType: 'CASH_OR_CHECK',
        tip: 0,
        taxApplied: 0,
      },
    ],
  };
  try {
    const responseCheckIn = yield call(
      Clients.purchase,
      selectedClient?.Client.id,
      newClientPurchases,
    );
    if (responseCheckIn.status === 201) {
      const responseBalance = yield call(
        Appointments.checkIn,
        selectedAppointment.id,
        selectedClient?.Client.id,
      );
      if (responseBalance.status === 201) {
        const responseApp = yield call(
          Appointments.getAppointmentById,
          selectedAppointment.id,
        );
        if (responseApp.status === 200) {
          yield put(setSelectedAppointmentAction(responseApp.data));
          yield getAppointmentsListByDate();
          const success = {
            title: 'Success',
            message: 'Your client has been checked in.',
          };
          yield put(setSuccessAction(success));
          track('checkin_client');
        }
      }
    }
    yield put(
      setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on check in client',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: false }),
    );
  }
}

function* saveClass() {
  const newClass = yield select((state) => state.appointments.newClassValues);
  const userId = yield select((state) => state.user.details.id);

  const objClass = {
    ...newClass,
    productId: newClass.service.id,
    selfBookingAddressId:
      newClass.selfBookingAddressId.id === 1
        ? 0
        : newClass.selfBookingAddressId.id,
    startDate: moment(newClass.startDate).format('L [12:00 am]'),
    repeatUntil: newClass.repeatUntil
      ? moment(newClass.repeatUntil).format('L [11:59 pm]')
      : '',
    userId,
  };

  delete objClass.service;
  delete objClass.sendNotifications;
  delete objClass.repeatUntilType;
  const checkOverlapsObject = {
    ...objClass,
  };
  try {
    yield put(
      setLoaderStateAction({ key: 'scheduleClassLoading', value: true }),
    );
    const response = yield call(
      Appointments.checkOverlapsClass,
      checkOverlapsObject,
    );
    if (response.status === 200) {
      if (response.data.length) {
        const alertResponse = yield call(showOverlapsClassAlert);
        if (alertResponse) {
          const responseSaveDouble = yield call(
            Appointments.saveClass,
            objClass,
          );
          if (responseSaveDouble.status === 201) {
            yield getAppointmentsListByDate();
            NavigatorService.navigate(APPLICATION_ROUTES.DASHBOARD);
          }
        }
      } else {
        const responseSave = yield call(Appointments.saveClass, objClass);
        if (responseSave.status === 201) {
          yield getAppointmentsListByDate();
          NavigatorService.navigate(APPLICATION_ROUTES.DASHBOARD);
        }
      }
      track('schedule_class');
    }
    yield put(
      setLoaderStateAction({ key: 'scheduleClassLoading', value: false }),
    );
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'scheduleClassLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on saving appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  }
}

function* getClass(action) {
  try {
    const response = yield call(Appointments.getClassById, action.payload);
    if (response.status === 200) {
      const selectedAppointment = yield select(
        (state) => state.appointments.selectedAppointment,
      );

      let locations = selectedAppointment.onsiteAddress;
      if (selectedAppointment.remotely === 'true') {
        locations = {
          id: 1,
          addressName: 'Remotely',
          checked: true,
        };
      }

      if (selectedAppointment.inapp === 'true') {
        locations = {
          id: 2,
          addressName: 'Video Call',
          checked: true,
        };
      }

      const classObject = {
        ...response.data,
        selfBookingAddressId: locations,
        service: {
          ...selectedAppointment.ProductDuration,
          ProductDuration: [{ ...selectedAppointment.ProductDuration }],
          serviceImage: selectedAppointment.serviceImage,
        },
      };
      yield put(setClassAction(classObject));
      NavigatorService.navigate(APPLICATION_ROUTES.EDIT_CLASS);
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on saving appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  }
}

function* updateClass() {
  yield put(setLoaderStateAction({ key: 'scheduleClassLoading', value: true }));
  const newClass = yield select((state) => state.appointments.newClassValues);
  const userId = yield select((state) => state.user.details.id);

  const objClass = {
    ...newClass,
    productId:
      newClass.service.productId
      || newClass.service.ProductDuration[0].productId,
    maxParticipants: parseInt(newClass.maxParticipants, 10),
    selfBookingAddressId:
      newClass.selfBookingAddressId.id === 1
        ? 0
        : newClass.selfBookingAddressId.id,
    startDate: moment(newClass.startDate).format('L [12:00 am]'),
    repeatUntil: newClass.repeatUntil
      ? moment(newClass.repeatUntil).format('L [11:59 pm]')
      : '',
    userId,
  };

  delete objClass.service;
  delete objClass.sendNotifications;
  delete objClass.repeatUntilType;
  delete objClass.expired;
  delete objClass.futureSpots;
  delete objClass.nextSpotDate;
  delete objClass.product;
  const checkOverlapsObject = {
    ...objClass,
  };
  try {
    const response = yield call(
      Appointments.checkOverlapsClass,
      checkOverlapsObject,
    );
    if (response.status === 200) {
      if (response.data.length) {
        const alertResponse = yield call(showOverlapsClassAlert);
        if (alertResponse) {
          const responseSaveDouble = yield call(
            Appointments.updateClass,
            objClass,
          );
          if (responseSaveDouble.status === 201) {
            yield getAppointmentsListByDate();
            yield put(
              setLoaderStateAction({
                key: 'scheduleClassLoading',
                value: false,
              }),
            );
            NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
          }
        }
      } else {
        const responseSave = yield call(Appointments.updateClass, objClass);
        if (responseSave.status === 201) {
          yield getAppointmentsListByDate();
          yield put(
            setLoaderStateAction({ key: 'scheduleClassLoading', value: false }),
          );
          NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
        }
      }
    }
    yield put(
      setLoaderStateAction({ key: 'scheduleClassLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      yield put(
        setLoaderStateAction({ key: 'scheduleClassLoading', value: false }),
      );
      Alert.alert(
        'Error on saving appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  }
}

function* buyPackageWithCashOrCheck() {
  yield put(
    setLoaderStateAction({ key: 'confirmPaymentLoading', value: true }),
  );
  yield put(setPurchasingAction(true));
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const clientsPurchase = yield select((state) => state.clients.clientsPurchase);
  const selectedPackage = yield select((state) => state.services.selectedPackage);
  const currentUser = yield select((state) => state.currentUser);
  const trainer = currentUser.role === ROLES.TRAINER
    ? currentUser
    : currentUser.client_user.trainer;

  let discount = 0;
  if (clientsPurchase.discount) {
    if (clientsPurchase.discount.type === 'PERCENTAGE') {
      discount = (selectedPackage.price * clientsPurchase.discount.amount) / 100;
    } else {
      discount = clientsPurchase.discount.amount;
    }
  }

  const taxes = selectedPackage.tax;

  const amountToPay = selectedPackage.price - discount + taxes;

  const validTo = selectedPackage.expireDaysCustom === 0
    ? moment().startOf('day').add(10, 'years').format('L LT')
    : moment()
      .startOf('day')
      .add(selectedPackage.expireDaysCustom, 'days')
      .format('L LT');
  const newClientPurchases = {
    ClientPurchases: [
      {
        amountDue: amountToPay < 0 ? 0 : amountToPay,
        amountPaid: amountToPay < 0 ? 0 : amountToPay,
        packageId: selectedPackage.id,
        cashBalanceToApply: 0,
        checkDate: moment().format('L LT'),
        checkNumber: '',
        clientId: selectedClient?.id,
        clientPurchaseProductDurations: [
          {
            price: selectedPackage.packageProducts[0].price,
            productDurationId:
              selectedPackage.packageProducts[0].productDurationId,
            quantity: selectedPackage.packageProducts[0].quantity,
            durationId: selectedPackage.packageProducts[0].durationId,
            productName: selectedPackage.packageProducts[0].productName,
            ProductDuration: {
              productId: selectedPackage.packageProducts[0].productId,
            },
          },
        ],
        discount,
        giftBalanceApplied: 0,
        giftCode: '',
        previousGiftCardId: null,
        purchaseType: 'CASH_OR_CHECK',
        tip: 0,
        taxApplied: taxes,
        validFrom: moment().startOf('day').format('L LT'),
        validTo,
      },
    ],
  };
  try {
    const responseCheckIn = yield call(
      Clients.purchase,
      selectedClient?.id,
      newClientPurchases,
    );
    if (responseCheckIn.status === 201) {
      const success = {
        title: 'Success',
        message: 'Package purchased and applied to the client.',
      };
      yield put(setSuccessAction(success));
      logSuccessfulStripePurchase(
        amountToPay,
        PRODUCT_TYPES.PACKAGE,
        PAYMENT_METHODS.CASH_OR_CHECK,
        trainer?.id,
        trainer?.ua_id || 'UNAVAILABLE',
        selectedClient?.id,
      );
    }
    yield put(
      setLoaderStateAction({ key: 'confirmPaymentLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on package purchase',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'confirmPaymentLoading', value: false }),
    );
    logFailedStripePurchase(
      amountToPay,
      PRODUCT_TYPES.PACKAGE,
      PAYMENT_METHODS.CASH_OR_CHECK,
      trainer?.id,
      trainer?.ua_id || 'UNAVAILABLE',
      selectedClient?.id,
    );
  } finally {
    yield put(setPurchasingAction(false));
  }
}
function* buyPackageWithCreditCard() {
  yield put(
    setLoaderStateAction({ key: 'confirmPaymentLoading', value: true }),
  );
  yield put(setPurchasingAction(true));
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const clientsPurchase = yield select((state) => state.clients.clientsPurchase);
  const selectedPackage = yield select((state) => state.services.selectedPackage);
  const currentUser = yield select((state) => state.currentUser);
  const trainer = currentUser.role === ROLES.TRAINER
    ? currentUser
    : currentUser.client_user.trainer;

  let discount = 0;
  if (clientsPurchase.discount) {
    if (clientsPurchase.discount.type === 'PERCENTAGE') {
      discount = (selectedPackage.price * clientsPurchase.discount.amount) / 100;
    } else {
      discount = clientsPurchase.discount.amount;
    }
  }

  const taxes = selectedPackage.tax;

  const amountToPay = selectedPackage.price - discount + taxes;

  const validTo = selectedPackage.expireDaysCustom === 0
    ? moment().startOf('day').add(10, 'years').format('L LT')
    : moment()
      .startOf('day')
      .add(selectedPackage.expireDaysCustom, 'days')
      .format('L LT');

  const newClientPurchases = {
    ClientPurchases: [
      {
        amountDue: amountToPay < 0 ? 0 : amountToPay,
        amountPaid: amountToPay < 0 ? 0 : amountToPay,
        packageId: selectedPackage.id,
        cashBalanceToApply: 0,
        checkDate: moment().format('L LT'),
        checkNumber: '',
        clientId: selectedClient?.id,
        clientPurchaseProductDurations: [
          {
            price: selectedPackage.packageProducts[0].price,
            productDurationId:
              selectedPackage.packageProducts[0].productDurationId,
            quantity: selectedPackage.packageProducts[0].quantity,
            durationId: selectedPackage.packageProducts[0].durationId,
            productName: selectedPackage.packageProducts[0].productName,
            ProductDuration: {
              productId: selectedPackage.packageProducts[0].productId,
            },
          },
        ],
        discount,
        giftBalanceApplied: 0,
        giftCode: '',
        previousGiftCardId: null,
        purchaseType: 'CREDIT_CARD',
        tip: 0,
        taxApplied: taxes,
        validFrom: moment().startOf('day').format('L LT'),
        validTo,
      },
    ],
  };
  try {
    const responseCheckIn = yield call(
      Clients.purchase,
      selectedClient?.id,
      newClientPurchases,
    );
    if (responseCheckIn.status === 201) {
      Alert.alert(
        'Success',
        'Package purchased and applied to the client.',
        [
          {
            text: 'Continue',
            onPress: () => {
              NavigatorService.navigate(APPLICATION_ROUTES.DASHBOARD);
            },
          },
        ],
        { cancelable: false },
      );
      logSuccessfulStripePurchase(
        amountToPay,
        PRODUCT_TYPES.PACKAGE,
        PAYMENT_METHODS.CARD,
        trainer?.id,
        trainer?.ua_id || 'UNAVAILABLE',
        selectedClient?.id,
      );
    }
    yield put(
      setLoaderStateAction({ key: 'confirmPaymentLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on package purchase',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'confirmPaymentLoading', value: false }),
    );
    logFailedStripePurchase(
      amountToPay,
      PRODUCT_TYPES.PACKAGE,
      PAYMENT_METHODS.CASH_OR_CHECK,
      trainer?.id,
      trainer?.ua_id || 'UNAVAILABLE',
      selectedClient?.id,
    );
  } finally {
    yield put(setPurchasingAction(false));
  }
}

function* checkInWithCreditCard() {
  yield put(
    setLoaderStateAction({ key: 'checkInCreditCardLoading', value: true }),
  );
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const clientsPurchase = yield select((state) => state.clients.clientsPurchase);
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );
  const currentUser = yield select((state) => state.currentUser);
  const trainer = currentUser.role === ROLES.TRAINER
    ? currentUser
    : currentUser.client_user.trainer;

  let discount = 0;
  if (clientsPurchase.discount) {
    if (clientsPurchase.discount.type === 'PERCENTAGE') {
      discount = (selectedAppointment.ProductDuration.price
          * clientsPurchase.discount.amount)
        / 100;
    } else {
      discount = clientsPurchase.discount.amount;
    }
  }

  const taxes = ((selectedAppointment.ProductDuration.price - discount)
      * selectedAppointment.ProductDuration.productTax)
    / 100;

  const amountToPay = selectedAppointment.ProductDuration.price - discount + taxes;

  const newClientPurchases = {
    ClientPurchases: [
      {
        amountDue: amountToPay < 0 ? 0 : amountToPay,
        amountPaid: amountToPay < 0 ? 0 : amountToPay,
        cashBalanceToApply: null,
        checkDate: null,
        checkNumber: null,
        clientId: selectedClient?.Client.id,
        clientPurchaseProductDurations: [
          {
            id: parseInt(selectedAppointment.ProductDuration.productId, 10),
            price: selectedAppointment.ProductDuration.price,
            productDurationId: parseInt(
              selectedAppointment.ProductDuration.id,
              10,
            ),
            quantity: 1,
            ProductDuration: selectedAppointment.ProductDuration,
          },
        ],
        discount,
        giftBalanceApplied: 0,
        giftCode: '',
        previousGiftCardId: null,
        purchaseType: 'CREDIT_CARD',
        tip: 0,
        taxApplied: taxes,
      },
    ],
  };
  try {
    const responseCheckIn = yield call(
      Clients.purchase,
      selectedClient?.Client.id,
      newClientPurchases,
    );
    if (responseCheckIn.status === 201) {
      const responseBalance = yield call(
        Appointments.checkIn,
        selectedAppointment.id,
        selectedClient?.Client.id,
      );
      if (responseBalance.status === 201) {
        const responseApp = yield call(
          Appointments.getAppointmentById,
          selectedAppointment.id,
        );
        if (responseApp.status === 200) {
          yield put(setSelectedAppointmentAction(responseApp.data));
          yield getAppointmentsListByDate();
          Alert.alert(
            'Success',
            'Your client has been checked in.',
            [
              {
                text: 'Continue',
                onPress: async () => {
                  if (selectedAppointment.Invitee.length > 1) {
                    NavigatorService.navigate(
                      APPLICATION_ROUTES.MULTIPLE_CHECK_IN_CLIENT,
                    );
                  } else {
                    NavigatorService.navigate(
                      APPLICATION_ROUTES.INDIVIDUAL_APPOINTMENT,
                    );
                  }
                  await track('checkin_client');
                },
              },
            ],
            { cancelable: false },
          );
          logSuccessfulStripePurchase(
            amountToPay,
            PRODUCT_TYPES.CLASS,
            PAYMENT_METHODS.CARD,
            trainer?.id,
            trainer?.ua_id || 'UNAVAILABLE',
            selectedClient?.id,
          );
        }
      }
    }
    yield put(
      setLoaderStateAction({ key: 'checkInCreditCardLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on check in client',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'checkInCreditCardLoading', value: false }),
    );
    logFailedStripePurchase(
      amountToPay,
      PRODUCT_TYPES.CLASS,
      PAYMENT_METHODS.CARD,
      trainer?.id,
      trainer?.ua_id || 'UNAVAILABLE',
      selectedClient?.id,
    );
  }
}

function* buyServiceWithCashOrCheck() {
  yield put(
    setLoaderStateAction({ key: 'confirmPaymentLoading', value: true }),
  );
  yield put(setPurchasingAction(true));
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const clientsPurchase = yield select((state) => state.clients.clientsPurchase);
  const selectedService = yield select((state) => state.services.selectedService);
  const currentUser = yield select((state) => state.currentUser);
  const trainer = currentUser.role === ROLES.TRAINER
    ? currentUser
    : currentUser.client_user.trainer;

  let discount = 0;
  if (clientsPurchase.discount) {
    if (clientsPurchase.discount.type === 'PERCENTAGE') {
      discount = (parseFloat(selectedService.ProductDuration[0].price)
          * clientsPurchase.discount.amount)
        / 100;
    } else {
      discount = clientsPurchase.discount.amount;
    }
  }

  const taxes = ((parseFloat(selectedService.ProductDuration[0].price) - discount)
      * selectedService.tax)
    / 100;

  const amountToPay = parseFloat(selectedService.ProductDuration[0].price) - discount + taxes;

  const newClientPurchases = {
    ClientPurchases: [
      {
        amountDue: amountToPay < 0 ? 0 : amountToPay,
        amountPaid: amountToPay < 0 ? 0 : amountToPay,
        cashBalanceToApply: 0,
        checkDate: moment().format('L LT'),
        checkNumber: '',
        clientId: selectedClient?.id,
        clientPurchaseProductDurations: [
          {
            id: parseInt(selectedService.ProductDuration[0].productId, 10),
            price: selectedService.ProductDuration[0].price,
            productDurationId: parseInt(
              selectedService.ProductDuration[0].id,
              10,
            ),
            quantity: 1,
            durationId: parseInt(
              selectedService.ProductDuration[0].duration.id,
              10,
            ),
            productName: selectedService.ProductDuration[0].name,
            ProductDuration: selectedService.ProductDuration[0],
          },
        ],
        discount,
        giftBalanceApplied: 0,
        giftCode: '',
        previousGiftCardId: null,
        purchaseType: 'CASH_OR_CHECK',
        tip: 0,
        taxApplied: taxes,
      },
    ],
  };
  try {
    const response = yield call(
      Clients.purchase,
      selectedClient?.id,
      newClientPurchases,
    );
    if (response.status === 201) {
      const success = {
        title: 'Success',
        message: 'Service purchased and applied to the client.',
      };
      yield put(setSuccessAction(success));
      logSuccessfulStripePurchase(
        amountToPay,
        PRODUCT_TYPES.SERVICE,
        PAYMENT_METHODS.CASH_OR_CHECK,
        trainer?.id,
        trainer?.ua_id || 'UNAVAILABLE',
        selectedClient?.id,
      );
    }
    yield put(
      setLoaderStateAction({ key: 'confirmPaymentLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on service purchase',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'confirmPaymentLoading', value: false }),
    );
    logFailedStripePurchase(
      amountToPay,
      PRODUCT_TYPES.SERVICE,
      PAYMENT_METHODS.CASH_OR_CHECK,
      trainer?.id,
      trainer?.ua_id || 'UNAVAILABLE',
      selectedClient?.id,
    );
  } finally {
    yield put(setPurchasingAction(false));
  }
}
function* buyServiceWithCreditCard() {
  yield put(
    setLoaderStateAction({ key: 'confirmPaymentLoading', value: true }),
  );
  yield put(setPurchasingAction(true));
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const clientsPurchase = yield select((state) => state.clients.clientsPurchase);
  const selectedService = yield select((state) => state.services.selectedService);
  const currentUser = yield select((state) => state.currentUser);
  const trainer = currentUser.role === ROLES.TRAINER
    ? currentUser
    : currentUser.client_user.trainer;

  let discount = 0;
  if (clientsPurchase.discount) {
    if (clientsPurchase.discount.type === 'PERCENTAGE') {
      discount = (parseFloat(selectedService.ProductDuration[0].price)
          * clientsPurchase.discount.amount)
        / 100;
    } else {
      discount = clientsPurchase.discount.amount;
    }
  }

  const taxes = ((parseFloat(selectedService.ProductDuration[0].price) - discount)
      * selectedService.tax)
    / 100;

  const amountToPay = parseFloat(selectedService.ProductDuration[0].price) - discount + taxes;

  const newClientPurchases = {
    ClientPurchases: [
      {
        amountDue: amountToPay < 0 ? 0 : amountToPay,
        amountPaid: amountToPay < 0 ? 0 : amountToPay,
        cashBalanceToApply: 0,
        checkDate: moment().format('L LT'),
        checkNumber: '',
        clientId: selectedClient?.id,
        clientPurchaseProductDurations: [
          {
            id: parseInt(selectedService.ProductDuration[0].productId, 10),
            price: selectedService.ProductDuration[0].price,
            productDurationId: parseInt(
              selectedService.ProductDuration[0].id,
              10,
            ),
            quantity: 1,
            durationId: parseInt(
              selectedService.ProductDuration[0].duration.id,
              10,
            ),
            productName: selectedService.ProductDuration[0].name,
            ProductDuration: selectedService.ProductDuration[0],
          },
        ],
        discount,
        giftBalanceApplied: 0,
        giftCode: '',
        previousGiftCardId: null,
        purchaseType: 'CREDIT_CARD',
        tip: 0,
        taxApplied: taxes,
      },
    ],
  };
  try {
    const response = yield call(
      Clients.purchase,
      selectedClient?.id,
      newClientPurchases,
    );
    if (response.status === 201) {
      Alert.alert(
        'Success',
        'Service purchased and applied to the client.',
        [
          {
            text: 'Continue',
            onPress: () => {
              NavigatorService.navigate(APPLICATION_ROUTES.DASHBOARD);
            },
          },
        ],
        { cancelable: false },
      );
      logSuccessfulStripePurchase(
        amountToPay,
        PRODUCT_TYPES.SERVICE,
        PAYMENT_METHODS.CARD,
        trainer?.id,
        trainer?.ua_id || 'UNAVAILABLE',
        selectedClient?.id,
      );
    }
    yield put(
      setLoaderStateAction({ key: 'confirmPaymentLoading', value: false }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on package purchase',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'confirmPaymentLoading', value: false }),
    );
    logFailedStripePurchase(
      amountToPay,
      PRODUCT_TYPES.SERVICE,
      PAYMENT_METHODS.CARD,
      trainer?.id,
      trainer?.ua_id || 'UNAVAILABLE',
      selectedClient?.id,
    );
  } finally {
    yield put(setPurchasingAction(false));
  }
}

function* getClientBookingsAction(action) {
  yield put(
    setLoaderStateAction({ key: 'getClientBookingsLoading', value: true }),
  );
  const startOfMonth = moment().startOf('month').format('YYYY-MM-DD');
  try {
    const response = yield call(
      Appointments.getAppointmentsByDateWithoutSlotBlockers,
      startOfMonth,
      365,
      action.payload,
    );
    if (response.status === 200) {
      yield put(setClientBookingsAction(response.data.appointments));
      yield put(
        setLoaderStateAction({ key: 'getClientBookingsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
      yield put(
        setLoaderStateAction({ key: 'getClientBookingsLoading', value: false }),
      );
    }
  }
}

function* getAppointmentService(action) {
  try {
    const response = yield call(
      Services.getServiceById,
      action.payload.service.id,
    );
    if (response.status === 200) {
      yield put(
        setNewAppointmentValuesAction({ key: 'service', value: response.data }),
      );
      NavigatorService.navigate(action.payload.returnRoute);
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* getClassService(action) {
  try {
    const response = yield call(
      Services.getServiceById,
      action.payload.service.id,
    );
    if (response.status === 200) {
      yield put(
        setNewClassValuesAction({ key: 'service', value: response.data }),
      );
      NavigatorService.navigate(action.payload.returnRoute);
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* endVideoCallSession(action) {
  try {
    const response = yield call(
      Appointments.endVideoCallSession,
      action.payload.appointmentId,
    );
    if (response.status === 200) {
      NavigatorService.goBack();
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* uncheckin(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: true }),
    );
    const selectedAppointment = yield select(
      (state) => state.appointments.selectedAppointment,
    );

    const response = yield call(
      Appointments.uncheckin,
      selectedAppointment.id,
      action.payload.id,
    );
    if (response.status === 200) {
      const responseApp = yield call(
        Appointments.getAppointmentById,
        selectedAppointment.id,
      );
      if (responseApp.status === 200) {
        yield put(setSelectedAppointmentAction(responseApp.data));
        yield getAppointmentsListByDate();
      }
    }
    yield put(
      setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: false }),
    );
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'checkInCashOrCheckLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

// eslint-disable-next-line import/prefer-default-export
export function* appointmentsSaga() {
  yield takeLatest(GET_SERVICE_APPOINTMENTS_ACTION, getAppointmentsList);
  yield takeLatest(GET_APPOINTMENTS_BY_DATE, getAppointmentsListByDate);
  yield takeLatest(
    GET_BLOCKING_APPOINTMENTS_BY_DATE,
    getBlockingAppointmentsListByDate,
  );
  yield takeLatest(SAVE_APPOINTMENT_ACTION, saveAppointment);
  yield takeLatest(UPDATE_APPOINTMENT_ACTION, updateAppointment);
  yield takeLatest(GET_APPOINTMENT_ACTION, getAppointment);
  yield takeLatest(DELETE_APPOINTMENT_ACTION, deleteAppointment);
  yield takeLatest(GET_SLOTBLOCKER_ACTION, getSlotBlocker);
  yield takeLatest(SAVE_SLOTBLOCKER_ACTION, saveSlotBlocker);
  yield takeLatest(DELETE_SLOTBLOCKER_ACTION, deleteSlotBlocker);
  yield takeLatest(UPDATE_SLOTBLOCKER_ACTION, updateSlotBlocker);
  yield takeLatest(CHECKIN_WITH_BALANCE_ACTION, checkInWithBalance);
  yield takeLatest(CHECKIN_WITH_CASH_OR_CHECK_ACTION, checkInWithCashOrCheck);
  yield takeLatest(CHECKIN_FREE_ACTION, checkInFree);
  yield takeLatest(CHECKIN_WITH_CREDIT_CARD_ACTION, checkInWithCreditCard);
  yield takeLatest(SAVE_CLASS_ACTION, saveClass);
  yield takeLatest(UPDATE_CLASS_ACTION, updateClass);
  yield takeLatest(GET_CLASS_ACTION, getClass);
  yield takeLatest(
    BUY_PACKAGE_WITH_CASH_OR_CHECK_ACTION,
    buyPackageWithCashOrCheck,
  );
  yield takeLatest(
    BUY_SERVICE_WITH_CASH_OR_CHECK_ACTION,
    buyServiceWithCashOrCheck,
  );
  yield takeLatest(
    BUY_PACKAGE_WITH_CREDIT_CARD_ACTION,
    buyPackageWithCreditCard,
  );
  yield takeLatest(
    BUY_SERVICE_WITH_CREDIT_CARD_ACTION,
    buyServiceWithCreditCard,
  );
  yield takeLatest(GET_CLIENT_BOOKINGS_ACTION, getClientBookingsAction);
  yield takeLatest(GET_APPOINTMENT_SERVICE_DETAILS, getAppointmentService);
  yield takeLatest(GET_CLASS_SERVICE_DETAILS, getClassService);
  yield takeLatest(END_VIDEO_CALL_SESSION_ACTION, endVideoCallSession);
  yield takeLatest(UNCHECKIN_ACTION, uncheckin);
}
