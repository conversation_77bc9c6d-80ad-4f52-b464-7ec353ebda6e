import {
  call, put, takeLatest, select,
} from 'redux-saga/effects';
import { Alert } from 'react-native';
import * as RNLocalize from 'react-native-localize';
import {
  APPLICATION_INIT_ACTION,
  SetApplicationStateAction,
} from '../actions/artichoke/Application.actions';
import {
  getInitialToken,
  getActiveUser,
  getAccountSettings,
  getTimezones,
  saveAccountSettings,
} from '../api/artichoke/Authentication.api';
import { createSetLoginStateAction } from '../actions/artichoke/Login.actions';
import {
  GET_USER_DATA,
  GET_ACCOUNT_SETTINGS,
  GET_TIMEZONES,
  SAVE_ACCOUNT_SETTINGS,
  setUserDataAction,
  setAccountSettingsAction,
  setTimezonesAction,
  setUpdatedAccountSettingsAction,
} from '../actions/artichoke/User.actions';
import nasm from '../dataManager/apiConfig';
import { setLoaderStateAction } from '../actions/artichoke/Loaders.actions';
import { CURRENCIES } from '../constants';

function* applicationInitWithCookie() {
  try {
    yield put(SetApplicationStateAction(true));
    const isLoggedIn = yield call(nasm.api.getToken);
    if (!isLoggedIn) {
      yield put(SetApplicationStateAction(false));
    } else {
      yield put(createSetLoginStateAction(isLoggedIn));
      const deviceTimeZone = RNLocalize.getTimeZone();
      const responseActiveUser = yield call(getActiveUser, deviceTimeZone);
      yield put(setUserDataAction(responseActiveUser.data));
      yield put(SetApplicationStateAction(false));
    }
  } catch (error) {
    yield put(SetApplicationStateAction(false));
    Alert.alert(
      'Error on init',
      error.message,
      [
        {
          text: 'Ok',
        },
      ],
      { cancelable: false },
    );
  }
}
function* applicationInit() {
  try {
    const response = yield call(getInitialToken);
    if (response.status === 200) {
      const { token } = response;
    }
  } catch (error) {
  }
}

function* getUser() {
  try {
    yield put(setLoaderStateAction({ key: 'userDataLoading', value: true }));
    const deviceTimeZone = RNLocalize.getTimeZone();
    const responseActiveUser = yield call(getActiveUser, deviceTimeZone);
    yield put(setUserDataAction(responseActiveUser.data));
    if (responseActiveUser.status === 200) {
      yield getAccount();
    }
    yield put(setLoaderStateAction({ key: 'userDataLoading', value: false }));
  } catch (error) {
    yield put(setLoaderStateAction({ key: 'userDataLoading', value: false }));
  }
}

function* getAccount() {
  try {
    yield put(setLoaderStateAction({ key: 'accountDataLoading', value: true }));
    const accountId = yield select((state) => state.user.details.account.id);
    const responseAccountSettings = yield call(getAccountSettings, accountId);
    yield put(setAccountSettingsAction(responseAccountSettings.data));
    const selectedCurrency = CURRENCIES.find((item) => item.value === responseAccountSettings.data.currency);
    yield put(
      setUpdatedAccountSettingsAction({
        key: 'symbol',
        value: selectedCurrency.symbol,
      }),
    );
    yield put(
      setLoaderStateAction({ key: 'accountDataLoading', value: false }),
    );
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'accountDataLoading', value: false }),
    );
  }
}

function* saveAccountData() {
  yield put(
    setLoaderStateAction({ key: 'accountDataUpdateLoading', value: true }),
  );
  const accountId = yield select((state) => state.user.details.account.id);
  const oldAccount = yield select((state) => state.user.accountSettings);
  const newAccountData = {
    AccountType: oldAccount.AccountType,
    accountSettings: oldAccount.accountSettings,
    currency: oldAccount.newCurrency
      ? oldAccount.newCurrency
      : oldAccount.currency,
    currencyCountry: oldAccount.newCurrencyCountry
      ? oldAccount.newCurrencyCountry
      : oldAccount.currencyCountry,
    id: oldAccount.id,
    phoneNumberPrefix: oldAccount.phoneNumberPrefix,
    creator: {
      TimeZone: oldAccount.newTimeZone
        ? oldAccount.newTimeZone
        : oldAccount.creator.TimeZone,
      canAddExpense: oldAccount.creator.canAddExpense,
      canAddUsers: oldAccount.creator.canAddUsers,
      canSeeFinancials: oldAccount.creator.canSeeFinancials,
      email: oldAccount.creator.email,
      firstName: oldAccount.creator.firstName,
      id: oldAccount.creator.id,
      lastName: oldAccount.creator.lastName,
      partnerCode: oldAccount.creator.partnerCode,
      phoneNumbers: oldAccount.creator.phoneNumbers,
      seeAllClients: oldAccount.creator.seeAllClients,
      userGroupCode: oldAccount.creator.userGroupCode,
      username: oldAccount.creator.username,
      accountSettings: oldAccount.creator.accountSettings,
    },
  };
  try {
    const responseUpdate = yield call(
      saveAccountSettings,
      accountId,
      newAccountData,
    );
    if (responseUpdate.status === 200) {
      yield getAccount();
    }
    yield put(
      setLoaderStateAction({ key: 'accountDataUpdateLoading', value: false }),
    );
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'accountDataUpdateLoading', value: false }),
    );
  }
}

function* getTimezonesList() {
  try {
    yield put(setLoaderStateAction({ key: 'timezonesLoading', value: true }));
    const responseAvailableTimezones = yield call(getTimezones);
    yield put(setTimezonesAction(responseAvailableTimezones.data.TimeZones));
    yield put(setLoaderStateAction({ key: 'timezonesLoading', value: false }));
  } catch (error) {
    yield put(setLoaderStateAction({ key: 'timezonesLoading', value: false }));
  }
}

export function* applicationInitSaga() {
  yield takeLatest(APPLICATION_INIT_ACTION, applicationInitWithCookie);
  yield takeLatest(GET_USER_DATA, getUser);
  yield takeLatest(GET_ACCOUNT_SETTINGS, getAccount);
  yield takeLatest(GET_TIMEZONES, getTimezonesList);
  yield takeLatest(SAVE_ACCOUNT_SETTINGS, saveAccountData);
}
