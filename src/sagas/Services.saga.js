import {
  call, put, takeLatest, select,
} from 'redux-saga/effects';
import moment from 'moment';
import { Alert } from 'react-native';
import * as Services from '../api/artichoke/Services.api';
import {
  GET_ARCHIVED_SERVICES_ACTION,
  GET_SERVICES_ACTION,
  ARCHIVE_SERVICE_ACTION,
  ARCHIVE_PACKAGE_ACTION,
  RESTORE_SERVICE_ACTION,
  UPDATE_SERVICE_ACTION,
  UPDATE_PACKAGE_ACTION,
  SET_SERVICE_LOCATIONS,
  GET_SERVICE_BY_ID_ACTION,
  GET_PACKAGE_BY_ID_ACTION,
  CREATE_SERVICE_ACTION,
  CREATE_PACKAGE_ACTION,
  UPLOAD_SERVICE_IMAGE,
  GET_ACTIVE_PACKAGES_LIST,
  GET_INACTIVE_PACKAGES_LIST,
  SELECT_OFFERING_SERVICE,
  GET_BUYABLE_SERVICES_ACTION,
  GET_BUYABLE_PACKAGES_ACTION,
  setServicesAction,
  setArchivedServicesAction,
  setSelectedServiceAction,
  setUploadedServiceImageAction,
  setActivePackagesAction,
  setInactivePackagesAction,
  setSelectedOfferingServiceAction,
  setSelectedPackageAction,
  setSelectedPackageForEditValuesAction,
  UNSELECT_OFFERING_SERVICE,
  RESTORE_PACKAGE_ACTION,
} from '../actions/artichoke/Services.actions';

import { setLoaderStateAction } from '../actions/artichoke/Loaders.actions';
import { createSetLoginStateAction } from '../actions/artichoke/Login.actions';
import {
  setSelectedLocationAction,
  setSelectedExtraLocationAction,
} from '../actions/artichoke/Locations.actions';
import { APPLICATION_ROUTES } from '../constants';
import * as NavigatorService from '../util/NavigatorService';

function* getServicesList(action) {
  try {
    yield put(setLoaderStateAction({ key: 'serviceListLoading', value: true }));
    const response = yield call(Services.getActiveServices, action.payload);
    if (response.status === 200) {
      yield put(setServicesAction(response.data.products));
      yield put(
        setLoaderStateAction({ key: 'serviceListLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'serviceListLoading', value: false }),
    );
  }
}

function* getArchivedServicesList() {
  try {
    yield put(
      setLoaderStateAction({ key: 'archivedServicesListLoading', value: true }),
    );
    const response = yield call(Services.getArchivedServices);
    if (response.status === 200) {
      yield put(setArchivedServicesAction(response.data.products));
      yield put(
        setLoaderStateAction({
          key: 'archivedServicesListLoading',
          value: false,
        }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(
      setLoaderStateAction({
        key: 'archivedServicesListLoading',
        value: false,
      }),
    );
  }
}

function* getArchivedPackageList() {
  try {
    yield put(
      setLoaderStateAction({ key: 'archivedPackagesListLoading', value: true }),
    );
    const response = yield call(Services.getInactivePackages);
    if (response.status === 200) {
      yield put(setInactivePackagesAction(response.data));
      yield put(
        setLoaderStateAction({
          key: 'archivedPackagesListLoading',
          value: false,
        }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(
      setLoaderStateAction({
        key: 'archivedPackagesListLoading',
        value: false,
      }),
    );
  }
}

function* archiveServ(action) {
  try {
    yield put(setLoaderStateAction({ key: 'serviceListLoading', value: true }));

    const responseActiveList = yield call(Services.getActivePackages);
    if (responseActiveList.status === 200) {
      yield put(setActivePackagesAction(responseActiveList.data));
      let isInPackage = false;
      responseActiveList.data.find((pack) => pack.packageProducts.find((service) => {
        if (service.productId === action.payload) {
          isInPackage = true;
        }
        return null;
      }));
      if (isInPackage) {
        Alert.alert(
          'Action Required',
          'This service is associated with one or more packages.  Please remove the service from those packages before archiving.',
          [
            {
              text: 'Cancel',
            },
          ],
          { cancelable: false },
        );
      } else {
        const response = yield call(Services.archiveService, action.payload);
        if (response.status === 204) {
          const currentDate = moment().unix() * 1000;
          const responseNewList = yield call(
            Services.getActiveServices,
            currentDate,
          );
          if (responseNewList.status === 200) {
            yield put(setServicesAction(responseNewList.data.products));
            yield put(
              setLoaderStateAction({ key: 'serviceListLoading', value: false }),
            );
            NavigatorService.navigate({
              name: APPLICATION_ROUTES.ARCHIVED_SERVICES,
              params: { isFromPackageOrService: true },
            });
          }
        }
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* archivePack(action) {
  try {
    yield put(setLoaderStateAction({ key: 'packageListLoading', value: true }));
    const response = yield call(Services.archivePackage, action.payload);
    if (response.status === 204) {
      const responseNewList = yield call(Services.getActivePackages);
      const responseInactivePackages = yield call(Services.getInactivePackages);
      if (responseNewList.status === 200) {
        yield put(setActivePackagesAction(responseNewList.data));
      }
      if (responseInactivePackages.status === 200) {
        yield put(setInactivePackagesAction(responseInactivePackages.data));
      }
      yield put(
        setLoaderStateAction({ key: 'packageListLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* restoreServ(action) {
  try {
    const responseGetProduct = yield call(
      Services.getServiceById,
      action.payload.id,
    );
    if (responseGetProduct.status === 200) {
      const newProduct = { ...responseGetProduct.data, active: true };
      delete newProduct.productUserSettings;
      yield put(
        setLoaderStateAction({ key: 'serviceListLoading', value: true }),
      );
      const response = yield call(Services.updateService, newProduct);
      if (response.status === 201) {
        const responseNewList = yield call(Services.getArchivedServices);
        if (responseNewList.status === 200) {
          yield put(setArchivedServicesAction(responseNewList.data.products));
        }

        const currentDate = moment().unix() * 1000;
        const responseActiveList = yield call(
          Services.getActiveServices,
          currentDate,
        );
        if (responseActiveList.status === 200) {
          yield put(setServicesAction(responseActiveList.data.products));
        }
        yield put(
          setLoaderStateAction({ key: 'serviceListLoading', value: false }),
        );
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* updateServ(action) {
  try {
    yield put(setLoaderStateAction({ key: 'serviceListLoading', value: true }));
    yield put(setLoaderStateAction({ key: 'saveServiceLoading', value: true }));
    const response = yield call(Services.updateService, action.payload);
    if (response.status === 201) {
      const responseNewList = yield call(Services.getArchivedServices);
      if (responseNewList.status === 200) {
        yield put(setArchivedServicesAction(responseNewList.data.products));
      }

      const currentDate = moment().unix() * 1000;
      const responseActiveList = yield call(
        Services.getActiveServices,
        currentDate,
      );
      if (responseActiveList.status === 200) {
        yield put(setServicesAction(responseActiveList.data.products));
      }
      yield put(
        setLoaderStateAction({ key: 'serviceListLoading', value: false }),
      );
      yield put(
        setLoaderStateAction({ key: 'saveServiceLoading', value: false }),
      );

      NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
    }
  } catch (error) {
    Alert.alert('Error', 'A server error occurred. Please try again later.');
    yield put(
      setLoaderStateAction({ key: 'saveServiceLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* updatePack(action) {
  try {
    yield put(setLoaderStateAction({ key: 'packageListLoading', value: true }));
    yield put(setLoaderStateAction({ key: 'savePackageLoading', value: true }));
    const response = yield call(Services.updatePackage, action.payload);
    if (response.status === 200) {
      const responseNewList = yield call(Services.getInactivePackages);
      if (responseNewList.status === 200) {
        yield put(setInactivePackagesAction(responseNewList.data));
      }

      const responseActiveList = yield call(Services.getActivePackages);
      if (responseActiveList.status === 200) {
        yield put(setActivePackagesAction(responseActiveList.data));
      }

      NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
      yield put(
        setLoaderStateAction({ key: 'packageListLoading', value: false }),
      );
      yield put(
        setLoaderStateAction({ key: 'savePackageLoading', value: false }),
      );
    }
  } catch (error) {
    Alert.alert('Error', 'A server error occurred. Please try again later.');
    yield put(
      setLoaderStateAction({ key: 'packageListLoading', value: false }),
    );
    yield put(
      setLoaderStateAction({ key: 'savePackageLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* restorePack(action) {
  try {
    const response = yield call(Services.restorePackageById, action.payload);
    if (response.status === 204) {
      const responseNewPackageList = yield call(Services.getInactivePackages);
      if (responseNewPackageList.status === 200) {
        yield put(setInactivePackagesAction(responseNewPackageList.data));
      }
      const responseNewList = yield call(Services.getActivePackages);
      if (responseNewList.status === 200) {
        yield put(setActivePackagesAction(responseNewList.data));
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* createServ(action) {
  try {
    yield put(setLoaderStateAction({ key: 'serviceListLoading', value: true }));
    yield put(setLoaderStateAction({ key: 'saveServiceLoading', value: true }));
    const response = yield call(Services.createService, action.payload.service);
    if (response.status === 201) {
      const responseNewList = yield call(Services.getArchivedServices);
      if (responseNewList.status === 200) {
        yield put(setArchivedServicesAction(responseNewList.data.products));
      }

      const currentDate = moment().unix() * 1000;
      const responseActiveList = yield call(
        Services.getActiveServices,
        currentDate,
      );
      if (responseActiveList.status === 200) {
        yield put(setServicesAction(responseActiveList.data.products));
      }
      NavigatorService.navigate({ name: action.payload.routeName });
      yield put(
        setLoaderStateAction({ key: 'serviceListLoading', value: false }),
      );
      yield put(
        setLoaderStateAction({ key: 'saveServiceLoading', value: false }),
      );
    }
  } catch (error) {
    Alert.alert('Error', 'A server error occurred. Please try again later.');
    yield put(
      setLoaderStateAction({ key: 'serviceListLoading', value: false }),
    );
    yield put(
      setLoaderStateAction({ key: 'saveServiceLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}
function* createPack(action) {
  try {
    yield put(setLoaderStateAction({ key: 'packageListLoading', value: true }));
    yield put(setLoaderStateAction({ key: 'savePackageLoading', value: true }));
    const response = yield call(Services.createPackage, action.payload);
    if (response.status === 200) {
      const responseNewList = yield call(Services.getActivePackages);
      if (responseNewList.status === 200) {
        yield put(setActivePackagesAction(responseNewList.data));
      }
      NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
      yield put(
        setLoaderStateAction({ key: 'packageListLoading', value: false }),
      );
      yield put(
        setLoaderStateAction({ key: 'savePackageLoading', value: false }),
      );
    }
  } catch (error) {
    Alert.alert('Error', 'A server error occurred. Please try again later.');
    yield put(
      setLoaderStateAction({ key: 'packageListLoading', value: false }),
    );
    yield put(
      setLoaderStateAction({ key: 'savePackageLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* setServiceLocations(action) {
  yield put(
    setLoaderStateAction({ key: 'saveServiceLocationsLoading', value: true }),
  );
  const service = yield select((state) => state.services.selectedService);
  const serviceImage = yield select((state) => state.services.serviceImage);
  if (service) {
    const newService = {
      ...service,
      serviceImage,
      clientLocationEnabled: action.payload.clientLocationEnabled,
      offeredOnline: action.payload.offeredOnline,
      selfBookingAddresses: action.payload.locationsList,
    };
    yield put(setSelectedServiceAction(newService));
    yield put(
      setLoaderStateAction({
        key: 'saveServiceLocationsLoading',
        value: false,
      }),
    );
  }
}

function* getService(action) {
  try {
    yield put(setLoaderStateAction({ key: 'getServiceLoading', value: true }));
    const response = yield call(Services.getServiceById, action.payload);
    if (response.status === 200) {
      yield put(setSelectedServiceAction(response.data));
      const productAddresses = response.data.selfBookingAddresses || [];
      const locations = productAddresses.map((location) => ({ id: location.id }));
      const extraLocations = [];
      if (response.data.clientLocationEnabled) {
        extraLocations.push({ id: 0 });
      }
      if (response.data.offeredOnline) {
        extraLocations.push({ id: 1 });
      }
      if (response.data.inappEnabled) {
        extraLocations.push({ id: 2 });
      }
      yield put(setSelectedExtraLocationAction(extraLocations));
      yield put(setSelectedLocationAction(locations));
      yield put(
        setLoaderStateAction({ key: 'getServiceLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(setLoaderStateAction({ key: 'getServiceLoading', value: false }));
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* getPackage(action) {
  try {
    yield put(setLoaderStateAction({ key: 'getPackageLoading', value: true }));
    const response = yield call(Services.getPackageById, action.payload);
    if (response.status === 200) {
      yield put(setSelectedPackageAction(response.data));
      if (
        response.data?.taxType === 'FLATRATE'
        && parseFloat(response.data?.taxRate) === 0
        && parseFloat(response.data?.tax) > 0
      ) {
        yield put(
          setSelectedPackageForEditValuesAction({
            key: 'taxRate',
            value: (
              (parseFloat(response.data.tax)
                / parseFloat(response.data.price))
              * 100
            ).toFixed(2),
          }),
        );
      }
      yield put(
        setLoaderStateAction({ key: 'getPackageLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(setLoaderStateAction({ key: 'getPackageLoading', value: false }));
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* uploadServiceImage(action) {
  try {
    const response = yield call(Services.uploadImage, action.payload);
    if (response.status === 200) {
      yield put(setUploadedServiceImageAction(response.data));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* getPackagesList() {
  try {
    yield put(setLoaderStateAction({ key: 'packageListLoading', value: true }));
    const response = yield call(Services.getActivePackages);
    if (response.status === 200) {
      yield put(setActivePackagesAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'packageListLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'packageListLoading', value: false }),
    );
  }
}

function* getBuyablePackagesList() {
  try {
    yield put(
      setLoaderStateAction({ key: 'productsListLoading', value: true }),
    );
    const user = yield select((state) => state.user.details);
    const response = yield call(Services.getBuyablePackages, user.account.id);
    if (response.status === 200) {
      yield put(setActivePackagesAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'productsListLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'productsListLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* getBuyableServicesList() {
  try {
    yield put(
      setLoaderStateAction({ key: 'productsListLoading', value: true }),
    );
    const user = yield select((state) => state.user.details);
    const response = yield call(Services.getBuyableServices, user.account.id);
    if (response.status === 200) {
      yield put(setServicesAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'productsListLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'productsListLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* selectService(action) {
  const selectedService = yield select(
    (state) => state.services.selectedOfferingServices,
  );
  const newSelectedServices = [
    ...selectedService,
    ...[
      {
        ...{
          productId: action.payload.id,
          productName: action.payload.name,
          duration: action.payload.ProductDuration[0].duration.duration,
          price: action.payload.ProductDuration[0].price,
          tax: action.payload.tax,
          quantity: 1,
          unlimited: false,
        },
      },
    ],
  ];
  yield put(setSelectedOfferingServiceAction(newSelectedServices));
}

function* unselectService(action) {
  const selectedService = yield select(
    (state) => state.services.selectedOfferingServices,
  );
  const newSelectedService = selectedService.filter(
    (item) => item.productId.toString() !== action.payload.id.toString(),
  );
  yield put(setSelectedOfferingServiceAction(newSelectedService));
}

// eslint-disable-next-line import/prefer-default-export
export function* servicesSaga() {
  yield takeLatest(GET_SERVICES_ACTION, getServicesList);
  yield takeLatest(GET_ARCHIVED_SERVICES_ACTION, getArchivedServicesList);
  yield takeLatest(ARCHIVE_SERVICE_ACTION, archiveServ);
  yield takeLatest(ARCHIVE_PACKAGE_ACTION, archivePack);
  yield takeLatest(UPDATE_SERVICE_ACTION, updateServ);
  yield takeLatest(UPDATE_PACKAGE_ACTION, updatePack);
  yield takeLatest(CREATE_SERVICE_ACTION, createServ);
  yield takeLatest(CREATE_PACKAGE_ACTION, createPack);
  yield takeLatest(RESTORE_SERVICE_ACTION, restoreServ);
  yield takeLatest(RESTORE_PACKAGE_ACTION, restorePack);
  yield takeLatest(SET_SERVICE_LOCATIONS, setServiceLocations);
  yield takeLatest(GET_SERVICE_BY_ID_ACTION, getService);
  yield takeLatest(GET_PACKAGE_BY_ID_ACTION, getPackage);
  yield takeLatest(UPLOAD_SERVICE_IMAGE, uploadServiceImage);
  yield takeLatest(GET_ACTIVE_PACKAGES_LIST, getPackagesList);
  yield takeLatest(GET_INACTIVE_PACKAGES_LIST, getArchivedPackageList);
  yield takeLatest(SELECT_OFFERING_SERVICE, selectService);
  yield takeLatest(UNSELECT_OFFERING_SERVICE, unselectService);
  yield takeLatest(GET_BUYABLE_SERVICES_ACTION, getBuyableServicesList);
  yield takeLatest(GET_BUYABLE_PACKAGES_ACTION, getBuyablePackagesList);
}
