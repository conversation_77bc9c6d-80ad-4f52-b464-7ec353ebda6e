import {
  call, put, takeLatest, select,
} from 'redux-saga/effects';
import moment from 'moment';
import { Alert } from 'react-native';
import * as RNLocalize from 'react-native-localize';
import * as ClientAppointments from '../api/artichoke/client/Appointments.api';
import {
  DELETE_CLIENT_APPOINTMENT_ACTION,
  GET_APPOINTMENTS_AVAILABILITY,
  GET_CLIENT_APPOINTMENTS_BY_DATE,
  GET_CLIENT_FUTURE_SCHEDULES_BY_DATE,
  GET_CLIENT_APPOINTMENT_ACTION,
  SAVE_CLIENT_APPOINTMENT_ACTION,
  SAVE_CLIENT_CLASS_APPOINTMENT_ACTION,
  DELETE_MULTIPLE_CLIENT_APPOINTMENT_ACTION,
  setAppointmentsByDateAction,
  setAppointmentsAvailabilityAction,
  setFutureAppointmentsByDateAction,
  setSelectedAppointmentAction,
  setSchedulesAction,
  setSuccessAction,
  JOIN_VIDEO_CALL_ACTION,
} from '../actions/artichoke/Appointments.actions';

import { createSetLoginStateAction } from '../actions/artichoke/Login.actions';
import { APPLICATION_CLIENT_ROUTES, ROLES } from '../constants';
import * as NavigatorService from '../util/NavigatorService';
import { getActiveUser } from '../api/artichoke/Authentication.api';
import { setUserDataAction } from '../actions/artichoke/User.actions';
import * as Appointments from '../api/artichoke/Appointments.api';
import { setLoaderStateAction } from '../actions/artichoke/Loaders.actions';
import * as Clients from '../api/artichoke/Clients.api';

function* getClientAppointmentsListByDate(action) {
  yield put(
    setLoaderStateAction({ key: 'clientBookingsListLoading', value: true }),
  );
  let startOfMonth = moment().startOf('month').format('YYYY-MM-DD');
  if (action?.payload) {
    startOfMonth = moment(action.payload).startOf('month').format('YYYY-MM-DD');
  }
  const currentUser = yield select((state) => state.currentUser);
  let user;
  try {
    user = yield select((state) => state.user.details);
    if (user === true) {
      const deviceTimeZone = RNLocalize.getTimeZone();
      const responseActiveUser = yield call(getActiveUser, deviceTimeZone);
      yield put(setUserDataAction(responseActiveUser.data));
    }
    user = yield select((state) => state.user.details);
    let clientId;
    if (currentUser.role === ROLES.TRAINER) {
      clientId = yield select((state) => state.clients.selectedClient?.id);
    } else {
      clientId = user.clientId;
    }
    const response = yield call(
      ClientAppointments.getAppointmentsByDate,
      startOfMonth,
      31,
      clientId,
    );
    if (response.status === 200) {
      yield put(setAppointmentsByDateAction(response.data.appointments));
      yield put(
        setLoaderStateAction({
          key: 'clientBookingsListLoading',
          value: false,
        }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'clientBookingsListLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function showDeleteAlert() {
  return new Promise((resolve) => {
    Alert.alert(
      'Delete Alert!',
      'This is a repeating event.',
      [
        {
          text: 'Delete This Event Only',
          onPress: () => {
            resolve({ deleteAll: false });
          },
        },
        {
          text: 'Delete All Future Events',
          onPress: () => {
            resolve({ deleteAll: true });
          },
        },
        {
          text: 'Cancel',
          onPress: () => {
            resolve(null);
          },
        },
      ],
      { cancelable: false },
    );
  });
}

function* deleteClientAppointment() {
  yield put(
    setLoaderStateAction({ key: 'cancelAppointmentLoading', value: true }),
  );
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );
  try {
    if (selectedAppointment.Repeat.RepeatInterval.RepeatIntervalType) {
      // const alertResponse = yield call(showDeleteAlert);
      // if(alertResponse) {
      //   const deleteAll = alertResponse !== null ? `?deleteAll=${alertResponse.deleteAll}` : '';
      const deleteAll = '';
      const responseDeleteEvents = yield call(
        ClientAppointments.deleteClientAppointment,
        selectedAppointment.id,
        deleteAll,
      );
      if (responseDeleteEvents.status === 200) {
        yield call(getClientAppointmentsListByDate);
        yield put(
          setLoaderStateAction({
            key: 'cancelAppointmentLoading',
            value: false,
          }),
        );
        NavigatorService.navigate('Home');
      }
      // }
    } else {
      const responseDelete = yield call(
        ClientAppointments.deleteClientAppointment,
        selectedAppointment.id,
        '?deleteAll=false',
      );
      if (responseDelete.status === 200) {
        yield call(getClientAppointmentsListByDate);
        yield put(
          setLoaderStateAction({
            key: 'cancelAppointmentLoading',
            value: false,
          }),
        );
        NavigatorService.navigate('Home');
      }
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'cancelAppointmentLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on deleting appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  }
}

function* saveClientAppointment() {
  yield put(setLoaderStateAction({ key: 'bookServiceLoader', value: true }));
  const startOfMonth = moment().startOf('month').format('YYYY-MM-DD');
  const newAppointment = yield select(
    (state) => state.appointments.newAppointmentValues,
  );
  const service = yield select((state) => state.services.selectedService);
  const user = yield select((state) => state.user.details);

  const appointment = {
    addOnProducts: '',
    bookDate: `${moment(newAppointment.date).format('MM/DD/YYYY')} ${moment(
      newAppointment.time,
      'HH:mm',
    ).format('LT')}`,
    bookSeries: '',
    clientInfo: {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      clientId: user.clientId.toString(),
    },
    productDurationId: service.ProductDuration[0].id,
    productId: service.id,
    selectedUser: 0,
  };

  if (newAppointment.appointmentLocation.id === 0) {
    appointment.clientAddress = 'true';
  }
  if (newAppointment.appointmentLocation.id === 1) {
    appointment.remotely = 'true';
  }
  if (newAppointment.appointmentLocation.id === 2) {
    appointment.inapp = 'true';
  }
  if (newAppointment.appointmentLocation.id > 2) {
    appointment.onsiteAddressId = newAppointment.appointmentLocation.id;
  }

  try {
    const responseSave = yield call(
      ClientAppointments.saveAppointmentByClient,
      appointment,
      user.account.id,
    );
    if (responseSave.status === 200) {
      const selectedAppointment = responseSave.data.appointment;
      let message;
      if (
        newAppointment.appointmentLocation.id === 2
        && parseInt(selectedAppointment.ProductDuration.price, 10) > 0
      ) {
        message = 'You will receive an alert 10 minutes before your video session start time.';
      } else {
        message = 'Your trainer will check you in when you arrive to your class.';
      }
      const responseApp = yield call(
        ClientAppointments.getAppointmentsByDate,
        startOfMonth,
        31,
        user.clientId,
      );
      if (responseApp.status === 200) {
        yield put(setAppointmentsByDateAction(responseApp.data.appointments));
        NavigatorService.navigate('Home');
      }
      const success = {
        title: 'Booked',
        message,
      };
      yield put(setSuccessAction(success));
    }
    yield put(setLoaderStateAction({ key: 'bookServiceLoader', value: false }));
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on saving appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(setLoaderStateAction({ key: 'bookServiceLoader', value: false }));
  }
}

function* getAppointmentsFutureListByDate() {
  yield put(setLoaderStateAction({ key: 'classesListLoading', value: true }));
  const startOfMonth = moment().format('YYYY-MM-DD');
  try {
    const user = yield select((state) => state.user.details);
    const response = yield call(
      ClientAppointments.getSchedulesByDate,
      startOfMonth,
      365,
      user.clientId,
    );
    if (response.status === 200) {
      yield put(setFutureAppointmentsByDateAction(response.data.appointments));
      const newData = {};
      Object.keys(response.data.appointments).map((date) => {
        const filteredAppointments = response.data.appointments[date].filter(
          (item) => {
            const itemDataByName = `${item.name}`;
            const textData = 'Schedule';
            if (itemDataByName.indexOf(textData) > -1) {
              return itemDataByName;
            }
            return null;
          },
        );
        if (filteredAppointments.length) {
          newData[date] = filteredAppointments;
        }
        return null;
      });
      yield put(setSchedulesAction(newData));
      yield put(
        setLoaderStateAction({ key: 'classesListLoading', value: false }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'classesListLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* saveClientClassAppointment() {
  yield put(
    setLoaderStateAction({ key: 'bookClientClassLoader', value: true }),
  );
  const startOfMonth = moment().startOf('month').format('YYYY-MM-DD');
  const schedule = yield select(
    (state) => state.appointments.selectedAppointment,
  );
  const user = yield select((state) => state.user.details);
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );

  const appointment = {
    addOnProducts: '',
    bookDate: schedule.start,
    clientInfo: {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      clientId: user.clientId.toString(),
    },
    // onsiteAddressId: parseInt(schedule.onsiteAddressId),
    productDurationId: schedule.ProductDuration.id,
    productId: schedule.ProductDuration.productId,
    selectedUser: 0,
    scheduleId: schedule.scheduleId,
    // remotely: "true",
  };

  try {
    const responseSave = yield call(
      ClientAppointments.saveAppointmentByClient,
      appointment,
      user.account.id,
    );
    if (responseSave.status === 200) {
      if (
        selectedAppointment.inapp.toString() === 'true'
        && parseInt(selectedAppointment.ProductDuration.price, 10) > 0
      ) {
        const discount = 0;

        const taxes = ((parseFloat(selectedAppointment.ProductDuration.price) - discount)
            * parseFloat(selectedAppointment.ProductDuration.productTax))
          / 100;

        const amountToPay = parseFloat(selectedAppointment.ProductDuration.price)
          - discount
          + taxes;

        const newClientPurchases = {
          ClientPurchases: [
            {
              amountDue: amountToPay < 0 ? 0 : amountToPay,
              amountPaid: amountToPay < 0 ? 0 : amountToPay,
              cashBalanceToApply: null,
              checkDate: null,
              checkNumber: null,
              clientId: user.clientId,
              clientPurchaseProductDurations: [
                {
                  id: parseInt(
                    selectedAppointment.ProductDuration.productId,
                    10,
                  ),
                  price: selectedAppointment.ProductDuration.price,
                  productDurationId: parseInt(
                    selectedAppointment.ProductDuration.id,
                    10,
                  ),
                  quantity: 1,
                  ProductDuration: selectedAppointment.ProductDuration,
                },
              ],
              discount,
              giftBalanceApplied: 0,
              giftCode: '',
              previousGiftCardId: null,
              purchaseType: 'CREDIT_CARD',
              tip: 0,
              taxApplied: taxes,
            },
          ],
        };
        const responsePurchase = yield call(
          Clients.purchase,
          user.clientId,
          newClientPurchases,
          selectedAppointment.id,
        );
        if (responsePurchase.status === 201) {
          const responseApp = yield call(
            ClientAppointments.getAppointmentsByDate,
            startOfMonth,
            31,
            user.clientId,
          );
          yield getAppointmentsFutureListByDate();
          if (responseApp.status === 200) {
            yield put(
              setAppointmentsByDateAction(responseApp.data.appointments),
            );
            NavigatorService.navigate('Home');
          }
        }
      } else {
        const responseApp = yield call(
          ClientAppointments.getAppointmentsByDate,
          startOfMonth,
          31,
          user.clientId,
        );
        yield getAppointmentsFutureListByDate();
        if (responseApp.status === 200) {
          yield put(setAppointmentsByDateAction(responseApp.data.appointments));
          NavigatorService.navigate('Home');
        }
      }
      Alert.alert(
        'Booked',
        'Your trainer will check you in when you arrive to your class.',
        [
          {
            text: 'Continue',
          },
        ],
        { cancelable: false },
      );
      yield put(
        setLoaderStateAction({ key: 'bookClientClassLoader', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on saving appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({ key: 'bookClientClassLoader', value: false }),
    );
  }
}

function* getAppointmentsAvailability(action) {
  const newAppointment = yield select(
    (state) => state.appointments.newAppointmentValues,
  );
  const user = yield select((state) => state.user.details);
  const service = yield select((state) => state.services.selectedService);
  const fromDate = moment(action.payload);

  try {
    const response = yield call(
      ClientAppointments.getAvailability,
      user.account.id,
      service.ProductDuration[0].id,
      newAppointment.appointmentLocation.id,
      fromDate.format('YYYY-MM-DD'),
      fromDate.add(1, 'month').format('YYYY-MM-DD'),
      newAppointment.appointmentLocation.id === 0,
      newAppointment.appointmentLocation.id === 1,
      newAppointment.appointmentLocation.id === 2,
    );
    if (response.status === 200) {
      yield put(setAppointmentsAvailabilityAction(response.data));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on getting availability',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  }
}

function* getClientAppointment(action) {
  try {
    const response = yield call(
      ClientAppointments.getAppointmentById,
      action.payload,
    );
    if (response.status === 200) {
      yield put(setSelectedAppointmentAction(response.data));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* cancelMultipleClientsAppointment() {
  yield put(
    setLoaderStateAction({ key: 'cancelAppointmentLoading', value: true }),
  );
  const newAppointment = yield select(
    (state) => state.appointments.newAppointmentValues,
  );
  const selectedAppointment = yield select(
    (state) => state.appointments.selectedAppointment,
  );
  const isSendNotificationsEnabled = true;
  const clientId = yield select((state) => state.user.details.clientId);
  const arrObjClients = newAppointment.clients.filter(
    (client) => client.Client.id !== clientId,
  );

  const arrClients = arrObjClients.map((client) => ({
    Client: { id: client.Client ? client.Client.id : client.id },
  }));

  const appointment = {
    id: selectedAppointment.id,
    Invitee: arrClients,
    ProductDuration: { id: newAppointment.service.ProductDuration[0].id },
    addOnProducts: null,
    clientAddressId: null, // for the moment
    description: newAppointment.service.name,
    doubleBook: false, // if overlaps
    start: `${moment(newAppointment.date).format('MM/DD/YYYY')} ${
      newAppointment.time
    }`,
    end: moment(
      `${moment(newAppointment.date).format('MM/DD/YYYY')} ${
        newAppointment.time
      }`,
      'MM/DD/YYYY HH:mm A',
    )
      .add(newAppointment.service.ProductDuration[0].duration.duration, 'm')
      .format('MM/DD/YYYY hh:mm A'),
    name:
      newAppointment.clients.length > 1
        ? 'Multiple Clients'
        : `${newAppointment.clients[0].Client.user.firstName} ${newAppointment.clients[0].Client.user.lastName}`,
    onsiteAddressId: newAppointment.appointmentLocation
      ? newAppointment.appointmentLocation.id
      : null,
    remotely: selectedAppointment.remotely, // for the moment
    inapp: selectedAppointment.inapp, // for the moment
    clientAddressEnable: selectedAppointment.clientAddressEnable,
    userId: selectedAppointment.userId,
  };

  if (newAppointment.repeatIntervalType) {
    appointment.Repeat = {
      RepeatInterval: {
        DayAndTimesOfWeek: [
          {
            MinuteOfHour: moment(newAppointment.time, 'hh:mm A').format('m'),
            DaysOfWeek: moment(newAppointment.date, 'MM/DD/YYYY')
              .format('dddd')
              .toUpperCase(),
            hourOfDay: moment(newAppointment.time, 'hh:mm A').format('H'),
          },
        ],
        RepeatIntervalType: newAppointment.repeatIntervalType,
      },
      RepeatUntil: {
        RepeatUntilType: newAppointment.repeatUntilType,
        count: newAppointment.count,
        untilDay: newAppointment.untilDay
          ? moment(newAppointment.untilDay, 'MM/DD/YYYY').format('MM/DD/YYYY')
          : null,
      },
    };
  }

  try {
    if (selectedAppointment.Repeat.RepeatInterval.RepeatIntervalType) {
      const alertResponse = yield call(showDeleteAlert);
      if (alertResponse) {
        const allInSeries = alertResponse !== null
          ? `?allInSeries=${alertResponse.deleteAll}`
          : '?allInSeries=false';
        const responseSaveDouble = yield call(
          Appointments.updateAppointment,
          appointment,
          allInSeries,
          true,
        );
        if (responseSaveDouble.status === 200) {
          yield call(getClientAppointmentsListByDate);
          yield put(
            setLoaderStateAction({
              key: 'cancelAppointmentLoading',
              value: false,
            }),
          );
          NavigatorService.navigate('Home');
        }
      } else {
        yield put(
          setLoaderStateAction({
            key: 'cancelAppointmentLoading',
            value: false,
          }),
        );
      }
    } else {
      const responseSaveDouble = yield call(
        Appointments.updateAppointment,
        appointment,
        '?allInSeries=false',
        isSendNotificationsEnabled,
      );
      if (responseSaveDouble.status === 200) {
        yield call(getClientAppointmentsListByDate);
        yield put(
          setLoaderStateAction({
            key: 'cancelAppointmentLoading',
            value: false,
          }),
        );
        NavigatorService.navigate('Home');
      }
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'cancelAppointmentLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on cancel appointment',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  }
}

function* joinVideoCall(action) {
  try {
    yield put(setLoaderStateAction({ key: 'joinVideoLoading', value: true }));
    yield put(
      setLoaderStateAction({
        key: 'joiningAppointmentId',
        value: action.payload.id,
      }),
    );
    const user = yield select((state) => state.user.details);
    const client = action.payload.Invitee.filter(
      (item) => item.Client.id.toString() === user.clientId.toString(),
    );
    const selectedAppointment = action.payload;
    if (client.length && client[0].checkedIn === 'false') {
      if (parseFloat(selectedAppointment.ProductDuration.price, 10) > 0) {
        const discount = 0;

        const taxes = ((parseFloat(selectedAppointment.ProductDuration.price) - discount)
            * parseFloat(selectedAppointment.ProductDuration.productTax))
          / 100;

        const amountToPay = parseFloat(selectedAppointment.ProductDuration.price)
          - discount
          + taxes;

        const newClientPurchases = {
          ClientPurchases: [
            {
              amountDue: amountToPay < 0 ? 0 : amountToPay,
              amountPaid: amountToPay < 0 ? 0 : amountToPay,
              cashBalanceToApply: null,
              checkDate: null,
              checkNumber: null,
              clientId: user.clientId,
              clientPurchaseProductDurations: [
                {
                  id: parseInt(
                    selectedAppointment.ProductDuration.productId,
                    10,
                  ),
                  price: selectedAppointment.ProductDuration.price,
                  productDurationId: parseInt(
                    selectedAppointment.ProductDuration.id,
                    10,
                  ),
                  quantity: 1,
                  ProductDuration: selectedAppointment.ProductDuration,
                },
              ],
              discount,
              giftBalanceApplied: 0,
              giftCode: '',
              previousGiftCardId: null,
              purchaseType: 'CREDIT_CARD',
              tip: 0,
              taxApplied: taxes,
            },
          ],
        };
        const responsePurchase = yield call(
          Clients.purchase,
          user.clientId,
          newClientPurchases,
          selectedAppointment.id,
        );
        if (responsePurchase.status === 201) {
          const responseCheckin = yield call(
            Appointments.checkIn,
            action.payload.id,
            user.clientId,
          );
          if (responseCheckin.status === 201) {
            const responseApp = yield call(
              Appointments.getAppointmentById,
              action.payload.id,
            );
            if (responseApp.status === 200) {
              yield put(setSelectedAppointmentAction(responseApp.data));
              yield getClientAppointmentsListByDate();
              yield put(
                setLoaderStateAction({ key: 'joinVideoLoading', value: false }),
              );
              yield put(
                setLoaderStateAction({
                  key: 'joiningAppointmentId',
                  value: null,
                }),
              );
              NavigatorService.navigate(
                APPLICATION_CLIENT_ROUTES.CLIENT_VIDEO_SCREEN,
                { channelName: responseApp.data.appointmentToken },
              );
            }
          }
        }
      } else {
        const responseCheckin = yield call(
          Appointments.checkIn,
          action.payload.id,
          user.clientId,
        );
        if (responseCheckin.status === 201) {
          const responseApp = yield call(
            Appointments.getAppointmentById,
            action.payload.id,
          );
          if (responseApp.status === 200) {
            yield put(setSelectedAppointmentAction(responseApp.data));
            yield getClientAppointmentsListByDate();
            yield put(
              setLoaderStateAction({ key: 'joinVideoLoading', value: false }),
            );
            yield put(
              setLoaderStateAction({
                key: 'joiningAppointmentId',
                value: null,
              }),
            );
            NavigatorService.navigate(
              APPLICATION_CLIENT_ROUTES.CLIENT_VIDEO_SCREEN,
              { channelName: responseApp.data.appointmentToken },
            );
          }
        }
      }
    } else {
      const responseApp = yield call(
        Appointments.getAppointmentById,
        action.payload.id,
      );
      if (responseApp.status === 200) {
        yield put(setSelectedAppointmentAction(responseApp.data));
        yield getClientAppointmentsListByDate();
        yield put(
          setLoaderStateAction({ key: 'joinVideoLoading', value: false }),
        );
        yield put(
          setLoaderStateAction({ key: 'joiningAppointmentId', value: null }),
        );
        yield call(
          NavigatorService.navigate,
          APPLICATION_CLIENT_ROUTES.CLIENT_VIDEO_SCREEN,
          { channelName: action.payload.appointmentToken },
        );
      }
    }
  } catch (error) {
    yield put(setLoaderStateAction({ key: 'joinVideoLoading', value: false }));
    yield put(
      setLoaderStateAction({ key: 'joiningAppointmentId', value: null }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on joining video call',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  }
}

// eslint-disable-next-line import/prefer-default-export
export function* clientAppointmentsSaga() {
  yield takeLatest(
    GET_CLIENT_APPOINTMENTS_BY_DATE,
    getClientAppointmentsListByDate,
  );
  yield takeLatest(DELETE_CLIENT_APPOINTMENT_ACTION, deleteClientAppointment);
  yield takeLatest(
    DELETE_MULTIPLE_CLIENT_APPOINTMENT_ACTION,
    cancelMultipleClientsAppointment,
  );
  yield takeLatest(SAVE_CLIENT_APPOINTMENT_ACTION, saveClientAppointment);
  yield takeLatest(
    SAVE_CLIENT_CLASS_APPOINTMENT_ACTION,
    saveClientClassAppointment,
  );
  yield takeLatest(GET_APPOINTMENTS_AVAILABILITY, getAppointmentsAvailability);
  yield takeLatest(
    GET_CLIENT_FUTURE_SCHEDULES_BY_DATE,
    getAppointmentsFutureListByDate,
  );
  yield takeLatest(GET_CLIENT_APPOINTMENT_ACTION, getClientAppointment);
  yield takeLatest(JOIN_VIDEO_CALL_ACTION, joinVideoCall);
}
