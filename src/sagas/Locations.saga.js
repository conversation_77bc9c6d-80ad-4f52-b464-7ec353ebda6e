/* eslint-disable import/prefer-default-export */
import {
  call, put, takeLatest, select,
} from 'redux-saga/effects';
import * as Locations from '../api/artichoke/Locations.api';
import {
  GET_AVAILABLE_LOCATIONS_ACTION,
  SELECT_LOCATION,
  UNSELECT_LOCATION,
  GET_SELF_BOOKING_ACTION,
  DISABLE_CLIENT_LOCATION_ACTION,
  SET_PREP_TIME_ACTION,
  SAVE_SELECTED_LOCATION_FOR_EDIT_ACTION,
  DELETE_SELECTED_LOCATION_FOR_EDIT_ACTION,
  SAVE_CLIENT_LOCATION_ACTION,
  SAVE_NEW_ADDRESS_ACTION,
  GET_SELF_BOOKING_USERS_ACTION,
  DISABLE_LOCATION_ACTION,
  setAvailableLocationsAction,
  setSelectedLocationAction,
  setSelectedExtraLocationAction,
  setSelfBookingAction,
  setSelfBookingUsersAction,
  clearNewLocationValuesAction,
  SAVE_REMOTE_LOCATION_ACTION,
  DISABLE_REMOTE_LOCATION_ACTION,
  DISABLE_IN_APP_VIDEO_LOCATION_ACTION,
  SAVE_IN_APP_VIDEO_LOCATION_ACTION,
} from '../actions/artichoke/Locations.actions';
import { createSetLoginStateAction } from '../actions/artichoke/Login.actions';
import { setLoaderStateAction } from '../actions/artichoke/Loaders.actions';
import { getActiveUser } from '../api/artichoke/Authentication.api';
import { setUserDataAction } from '../actions/artichoke/User.actions';
import { APPLICATION_ROUTES } from '../constants';
import * as NavigatorService from '../util/NavigatorService';

function* setAvailableLocation(arrLocations) {
  yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
  const accountId = yield select((state) => state.user.details.account.id);
  const responseSelfBookingUsers = yield call(
    Locations.getSelfBookingsUsersApi,
    accountId,
  );
  if (responseSelfBookingUsers.status === 200) {
    yield put(setSelfBookingUsersAction(responseSelfBookingUsers.data));
    const availableLocations = [...arrLocations];
    const enabledLocations = responseSelfBookingUsers.data[0].selfBookingAddresses.map(
      (address) => address.id,
    );
    availableLocations.map((item, index) => {
      if (enabledLocations.includes(item.id)) {
        availableLocations[index].checked = true;
      }
      return true;
    });
    yield put(setAvailableLocationsAction(availableLocations));
  }
  yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
}
function* getLocationsList() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const response = yield call(Locations.getAvailableLocations);
    if (response.status === 200) {
      const accountId = yield select((state) => state.user.details.account.id);
      const responseSelfBookingUsers = yield call(
        Locations.getSelfBookingsUsersApi,
        accountId,
      );
      if (
        responseSelfBookingUsers.status === 200
        && responseSelfBookingUsers.data
        && responseSelfBookingUsers.data.length
      ) {
        yield put(setSelfBookingUsersAction(responseSelfBookingUsers.data));
        const availableLocations = response.data;
        const enabledLocations = responseSelfBookingUsers.data[0].selfBookingAddresses.map(
          (address) => address.id,
        );
        availableLocations.map((item, index) => {
          if (enabledLocations.includes(item.id)) {
            availableLocations[index].checked = true;
          }
          return true;
        });
        yield put(setAvailableLocationsAction(availableLocations));
      }
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error && error.response && error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }

    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* selectLocation(action) {
  const selectedLocations = yield select(
    (state) => state.locations.selectedLocations,
  );
  const selectedExtraLocations = yield select(
    (state) => state.locations.selectedExtraLocations,
  );
  if (action.payload.id > 2) {
    const newSelectedLocations = [
      ...selectedLocations,
      ...[{ ...{ id: action.payload.id } }],
    ];
    yield put(setSelectedLocationAction(newSelectedLocations));
  } else if (
    selectedExtraLocations.filter(
      (item) => item.id.toString() === action.payload.id.toString(),
    ).length === 0
  ) {
    const newSelectedExtraLocations = [
      ...selectedExtraLocations,
      ...[{ ...{ id: action.payload.id } }],
    ];
    yield put(setSelectedExtraLocationAction(newSelectedExtraLocations));
  }
}

function* unselectLocation(action) {
  if (action.payload.id > 2) {
    const selectedLocations = yield select(
      (state) => state.locations.selectedLocations,
    );
    const newSelectedLocations = selectedLocations.filter(
      (item) => item.id !== action.payload.id,
    );
    yield put(setSelectedLocationAction(newSelectedLocations));
  } else {
    const selectedExtraLocations = yield select(
      (state) => state.locations.selectedExtraLocations,
    );
    const newSelectedExtraLocations = selectedExtraLocations.filter(
      (item) => item.id !== action.payload.id,
    );
    yield put(setSelectedExtraLocationAction(newSelectedExtraLocations));
  }
}

function* getSelfBookings() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));

    let user;
    user = yield select((state) => state.user.details);
    if (user === true) {
      const responseActiveUser = yield call(getActiveUser);
      user = responseActiveUser.data;
      yield put(setUserDataAction(responseActiveUser.data));
    }
    const accountId = user.account.id;
    const response = yield call(Locations.getSelfBookingsApi, accountId);
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      const responseSelfBookingUsers = yield call(
        Locations.getSelfBookingsUsersApi,
        accountId,
      );
      if (
        responseSelfBookingUsers.status === 200
        && responseSelfBookingUsers.data
        && responseSelfBookingUsers.data.length
      ) {
        yield put(setSelfBookingUsersAction(response.data));
        const selfBooking = response.data;
        const enabledLocations = responseSelfBookingUsers.data[0].selfBookingAddresses.map(
          (address) => address.id,
        );
        const { selfBookingAddresses } = selfBooking;
        selfBookingAddresses.map((item, index) => {
          if (enabledLocations.includes(item.id)) {
            selfBookingAddresses[index].checked = true;
          }
          return true;
        });

        const newSaveBookingsObj = {
          ...selfBooking,
          selfBookingAddresses,
        };
        yield put(setSelfBookingAction(newSaveBookingsObj));
        yield put(
          setLoaderStateAction({ key: 'locationsLoading', value: false }),
        );
      }
    }
  } catch (error) {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* disableClientLocation() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const newSaveBookingsObj = {
      ...selfBooking,
      offsite_services_available: 'false',
      userId,
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* disableLocation(action) {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);

    const selfBookingAddresses = selfBooking.selfBookingAddresses.filter(
      (item) => item.id !== action.payload.id,
    );
    selfBookingAddresses.push({
      ...action.payload,
      checked: false,
    });

    const newSaveBookingsObj = {
      ...selfBooking,
      selfBookingAddresses,
      userId,
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      const responseLocations = yield call(Locations.getAvailableLocations);
      if (responseLocations.status === 200) {
        yield setAvailableLocation(responseLocations.data);
      }
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* setPrepTime(action) {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const newSaveBookingsObj = {
      ...selfBooking,
      prepTime: action.payload,
      userId,
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* saveSelectedLocationForEdit() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const selectedLocationForEdit = yield select(
      (state) => state.locations.selectedLocationForEdit,
    );
    const activeServiceCreation = yield select(
      (state) => state.locations.activeServiceCreation,
    );
    const selfBookingAddresses = selfBooking.selfBookingAddresses.filter(
      (item) => item.id !== selectedLocationForEdit.id,
    );
    selfBookingAddresses.push({
      ...selectedLocationForEdit,
      checked: true,
    });
    const newSaveBookingsObj = {
      ...selfBooking,
      selfBookingAddresses,
      userId,
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      const responseLocations = yield call(Locations.getAvailableLocations);
      if (responseLocations.status === 200) {
        yield setAvailableLocation(responseLocations.data);
      }
      if (!activeServiceCreation) {
        NavigatorService.navigate({ name: APPLICATION_ROUTES.DASHBOARD });
      }
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* deleteSelectedLocationForEdit() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const selectedLocationForEdit = yield select(
      (state) => state.locations.selectedLocationForEdit,
    );

    const checkObj = JSON.parse(selectedLocationForEdit.workHours);
    delete checkObj.new;
    selectedLocationForEdit.workHours = JSON.stringify(checkObj);

    const selfBookingAddresses = selfBooking.selfBookingAddresses.filter(
      (item) => item.id !== selectedLocationForEdit.id,
    );
    selfBookingAddresses.push({
      ...selectedLocationForEdit,
      markedForDelete: true,
    });
    const newSaveBookingsObj = {
      ...selfBooking,
      selfBookingAddresses,
      userId,
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      const responseLocations = yield call(Locations.getAvailableLocations);
      if (responseLocations.status === 200) {
        yield setAvailableLocation(responseLocations.data);
      }
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* saveClientLocation() {
  try {
    yield put(
      setLoaderStateAction({ key: 'clientLocationsLoading', value: true }),
    );
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const clientLocation = yield select(
      (state) => state.locations.clientLocation,
    );
    const activeServiceCreation = yield select(
      (state) => state.locations.activeServiceCreation,
    );

    const checkObj = JSON.parse(clientLocation.workHours);
    delete checkObj.offsite.new;
    clientLocation.workHours = JSON.stringify(checkObj);

    const newSaveBookingsObj = {
      ...selfBooking,
      ...clientLocation,
      userId,
      offsite_services_available: 'true',
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'clientLocationsLoading', value: false }),
      );
      if (!activeServiceCreation) {
        NavigatorService.navigate({ name: APPLICATION_ROUTES.HOURS_LOCATIONS });
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(
      setLoaderStateAction({ key: 'clientLocationsLoading', value: false }),
    );
  }
}

function* saveNewAddress() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const newAddress = yield select(
      (state) => state.locations.newLocationAddress,
    );

    const selfBookingAddresses = [...selfBooking.selfBookingAddresses];
    selfBookingAddresses.push({
      ...newAddress,
      id: 0,
      checked: true,
      isServiceLocation: false,
      markedForDelete: false,
    });
    const newSaveBookingsObj = {
      ...selfBooking,
      selfBookingAddresses,
      userId,
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      const responseLocations = yield call(Locations.getAvailableLocations);
      if (responseLocations.status === 200) {
        yield setAvailableLocation(responseLocations.data);
        yield put(clearNewLocationValuesAction());
      }
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (
      error
      && error.response
      && error.response.status
      && error.response.status === 401
    ) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* getSelfBookingsUsers() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const accountId = yield select((state) => state.user.details.account.id);
    const response = yield call(Locations.getSelfBookingsUsersApi, accountId);
    yield put(setSelfBookingUsersAction(response.data));
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  } catch (error) {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* disableRemoteLocation() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const newSaveBookingsObj = {
      ...selfBooking,
      remote_services_available: 'false',
      userId,
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* disableInAppVideoLocation() {
  try {
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: true }));
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const newSaveBookingsObj = {
      ...selfBooking,
      inapp_services_available: 'false',
      userId,
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'locationsLoading', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(setLoaderStateAction({ key: 'locationsLoading', value: false }));
  }
}

function* saveRemoteLocation() {
  try {
    yield put(
      setLoaderStateAction({ key: 'remoteLocationsLoading', value: true }),
    );
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const remoteLocation = yield select(
      (state) => state.locations.remoteLocation,
    );
    const activeServiceCreation = yield select(
      (state) => state.locations.activeServiceCreation,
    );

    const checkObj = JSON.parse(remoteLocation.workHours);
    delete checkObj.remote.new;
    remoteLocation.workHours = JSON.stringify(checkObj);

    const newSaveBookingsObj = {
      ...selfBooking,
      ...remoteLocation,
      userId,
      remote_services_available: 'true',
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'remoteLocationsLoading', value: false }),
      );
      if (!activeServiceCreation) {
        NavigatorService.navigate({ name: APPLICATION_ROUTES.HOURS_LOCATIONS });
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(
      setLoaderStateAction({ key: 'remoteLocationsLoading', value: false }),
    );
  }
}

function* saveInAppVideoLocation(action) {
  try {
    const returnRoute = action.payload || APPLICATION_ROUTES.HOURS_LOCATIONS;
    yield put(
      setLoaderStateAction({ key: 'remoteLocationsLoading', value: true }),
    );
    const selfBooking = yield select((state) => state.locations.selfBookings);
    const userId = yield select((state) => state.user.details.id);
    const inAppVideoLocation = yield select(
      (state) => state.locations.inAppVideoLocation,
    );
    const activeServiceCreation = yield select(
      (state) => state.locations.activeServiceCreation,
    );

    const checkObj = JSON.parse(inAppVideoLocation.workHours);
    delete checkObj.remote.new;
    inAppVideoLocation.workHours = JSON.stringify(checkObj);

    const newSaveBookingsObj = {
      ...selfBooking,
      ...inAppVideoLocation,
      userId,
      inapp_services_available: 'true',
    };
    const response = yield call(
      Locations.saveSelfBookingsAddrApi,
      newSaveBookingsObj,
    );
    if (response.status === 200) {
      yield put(setSelfBookingAction(response.data));
      yield put(
        setLoaderStateAction({ key: 'remoteLocationsLoading', value: false }),
      );
      if (!activeServiceCreation) {
        NavigatorService.navigate({ name: returnRoute, merge: true });
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(
      setLoaderStateAction({ key: 'remoteLocationsLoading', value: false }),
    );
  }
}

export function* locationsSaga() {
  yield takeLatest(GET_AVAILABLE_LOCATIONS_ACTION, getLocationsList);
  yield takeLatest(SELECT_LOCATION, selectLocation);
  yield takeLatest(UNSELECT_LOCATION, unselectLocation);
  yield takeLatest(GET_SELF_BOOKING_ACTION, getSelfBookings);
  yield takeLatest(GET_SELF_BOOKING_USERS_ACTION, getSelfBookingsUsers);
  yield takeLatest(DISABLE_CLIENT_LOCATION_ACTION, disableClientLocation);
  yield takeLatest(DISABLE_LOCATION_ACTION, disableLocation);
  yield takeLatest(SET_PREP_TIME_ACTION, setPrepTime);
  yield takeLatest(
    SAVE_SELECTED_LOCATION_FOR_EDIT_ACTION,
    saveSelectedLocationForEdit,
  );
  yield takeLatest(
    DELETE_SELECTED_LOCATION_FOR_EDIT_ACTION,
    deleteSelectedLocationForEdit,
  );
  yield takeLatest(SAVE_CLIENT_LOCATION_ACTION, saveClientLocation);
  yield takeLatest(SAVE_NEW_ADDRESS_ACTION, saveNewAddress);
  yield takeLatest(SAVE_REMOTE_LOCATION_ACTION, saveRemoteLocation);
  yield takeLatest(SAVE_IN_APP_VIDEO_LOCATION_ACTION, saveInAppVideoLocation);
  yield takeLatest(DISABLE_REMOTE_LOCATION_ACTION, disableRemoteLocation);
  yield takeLatest(
    DISABLE_IN_APP_VIDEO_LOCATION_ACTION,
    disableInAppVideoLocation,
  );
}
