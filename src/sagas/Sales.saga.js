import { call, put, takeLatest } from 'redux-saga/effects';
import {
  GET_SALES_TAX_ACTION,
  GET_TRANSACTION_HISTORY_ACTION,
  setSalesTaxAction,
  setTransactionHistoryAction,
} from '../actions/artichoke/Sales.actions';
import * as Sales from '../api/artichoke/Sales.api';
import { createSetLoginStateAction } from '../actions/artichoke/Login.actions';

function* getSalesTax(action) {
  try {
    const { startDate } = action.payload;
    const { endDate } = action.payload;

    const response = yield call(Sales.getSalesTax, startDate, endDate);
    if (response.status === 200) {
      yield put(setSalesTaxAction(response.data));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

function* getTransactionHistory(action) {
  const { days, startDate, endDate } = action.payload;
  try {
    const response = yield call(
      Sales.getTransactionHistory,
      days,
      startDate,
      endDate,
    );
    if (response.status === 200) {
      yield put(setTransactionHistoryAction(response.data.Events));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
  }
}

// eslint-disable-next-line import/prefer-default-export
export function* salesSaga() {
  yield takeLatest(GET_SALES_TAX_ACTION, getSalesTax);
  yield takeLatest(GET_TRANSACTION_HISTORY_ACTION, getTransactionHistory);
}
