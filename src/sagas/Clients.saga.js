/* eslint-disable import/prefer-default-export, no-underscore-dangle */
import {
  call, put, takeLatest, select,
} from 'redux-saga/effects';
import { Al<PERSON> } from 'react-native';
import * as RNLocalize from 'react-native-localize';
import * as Clients from '../api/artichoke/Clients.api';
import {
  GET_ACTIVE_CLIENTS,
  GET_DISCOUNTS,
  SAVE_SELECTED_DISCOUNT_FOR_EDIT_ACTION,
  DELETE_SELECTED_DISCOUNT_FOR_EDIT_ACTION,
  CANCEL_SUBSCRIPTION_ACTION,
  SAVE_DISCOUNT_ACTION,
  GET_CLIENT_BALANCE,
  setActiveClientsAction,
  setSelectedClient,
  setDiscountsAction,
  setPurchaseAction,
  setClientBalance,
  SAVE_CLIENT_CARD,
  GET_CLIENT_CREDIT_CARD,
  setClientCreditCard,
  SAVE_CARD_BY_CLIENT,
  REMOVE_CREDIT_CARD,
  GET_CLIENT_TRANSACTIONS,
  GET_CLIENT_SUBSCRIPTIONS,
  setClientTransactions,
  setClientSubscriptions,
  REFUND_TRANSACTION,
  BALANCE_ADJUSTMENT_ACTION,
  setSelectedClientBalance,
  CLIENT_BUY_PACKAGE_ACTION,
  setSavingClientCardAction,
} from '../actions/artichoke/Clients.actions';
import { getActiveUser } from '../api/artichoke/Authentication.api';
import { setUserDataAction } from '../actions/artichoke/User.actions';
import { createSetLoginStateAction } from '../actions/artichoke/Login.actions';
import * as NavigatorService from '../util/NavigatorService';
import { APPLICATION_ROUTES, ROLES } from '../constants';
import { setSuccessAction } from '../actions/artichoke/Appointments.actions';
import { setLoaderStateAction } from '../actions/artichoke/Loaders.actions';
import { track } from '../util/Analytics';

function* getActiveClientsList() {
  try {
    yield put(
      setLoaderStateAction({ key: 'showActiveClientsLoading', value: true }),
    );

    const user = yield select((state) => state.user.details);
    if (user === true) {
      const deviceTimeZone = RNLocalize.getTimeZone();
      const responseActiveUser = yield call(getActiveUser, deviceTimeZone);
      if (responseActiveUser.status === 200) {
        yield put(setUserDataAction(responseActiveUser.data));
        const accountId = yield select((state) => state.user.details.account.id);
        const selectedClient = yield select((state) => state.selectedClient);
        const response = yield call(Clients.getActiveClientsList, accountId);
        if (response.status === 200) {
          yield put(setActiveClientsAction(response.data.Clients));
          const clientsList = response.data.Clients;
          if (selectedClient && selectedClient?.client_user) {
            const artichokeSelectedClientFilter = clientsList.filter(
              (item) => item.user.id === selectedClient?.client_user.id,
            );
            yield put(setSelectedClient(artichokeSelectedClientFilter[0]));
          }
          yield put(
            setLoaderStateAction({
              key: 'showActiveClientsLoading',
              value: false,
            }),
          );
        }
      }
    } else {
      const accountId = yield select((state) => state.user.details.account.id);
      const selectedClient = yield select((state) => state.selectedClient);
      const response = yield call(Clients.getActiveClientsList, accountId);
      if (response.status === 200) {
        yield put(setActiveClientsAction(response.data.Clients));
        // const clientsList = yield select((state) => state.clients.clientsList);
        const clientsList = response.data.Clients;
        if (selectedClient && selectedClient?.client_user) {
          const artichokeSelectedClientFilter = clientsList.filter(
            (item) => item.user.id === selectedClient?.client_user.id,
          );
          yield put(setSelectedClient(artichokeSelectedClientFilter[0]));
        }
        yield put(
          setLoaderStateAction({
            key: 'showActiveClientsLoading',
            value: false,
          }),
        );
      }
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'showActiveClientsLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
  }
}

function* getDiscountsList() {
  yield put(setLoaderStateAction({ key: 'discountLoader', value: true }));
  try {
    const response = yield call(Clients.getDiscounts);
    if (response.status === 200) {
      console.log('clients response', response);
      yield put(setDiscountsAction(response.data));
    }
    yield put(setLoaderStateAction({ key: 'discountLoader', value: false }));
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
    yield put(setLoaderStateAction({ key: 'discountLoader', value: false }));
  }
}

function* saveDiscount() {
  yield put(setLoaderStateAction({ key: 'discountLoader', value: true }));
  try {
    const newDiscount = yield select((state) => state.clients.selectedDiscount);
    const response = yield call(Clients.saveDiscountApi, newDiscount);
    if (response.status === 200) {
      yield put(
        setPurchaseAction({
          key: 'discount',
          value: response.data,
        }),
      );
      const responseDiscounts = yield call(Clients.getDiscounts);
      if (responseDiscounts.status === 200) {
        yield put(setDiscountsAction(responseDiscounts.data));
      }
      yield put(setLoaderStateAction({ key: 'discountLoader', value: false }));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
    yield put(setLoaderStateAction({ key: 'discountLoader', value: false }));
  }
}

function* cancelSubscription() {
  yield put(
    setLoaderStateAction({ key: 'cancelingSubscriptionLoading', value: true }),
  );
  const user = yield select((state) => state.user);
  const selectedClient = yield select((state) => state.clients.selectedClient);
  const selectedClientTransaction = yield select(
    (state) => state.clients.selectedClientTransaction,
  );

  try {
    const response = yield call(
      Clients.cancelSubscription,
      user.accountSettings.id,
      selectedClientTransaction.clientPurchase.id,
      selectedClientTransaction.subscription.id,
    );
    if (response.status === 200) {
      const clientId = selectedClient
        ? selectedClient?.id
        : user.details.clientId;
      const responseSubs = yield call(Clients.getClientSubscriptions, clientId);
      if (responseSubs.status === 200) {
        yield put(setClientSubscriptions(responseSubs.data));
        Alert.alert(
          'Success',
          'Your subscription has been cancelled.',
          [
            {
              text: 'Continue',
              onPress: () => {
                NavigatorService.navigate({
                  name: APPLICATION_ROUTES.CLIENT_SUBSCRIPTIONS,
                  merge: true,
                });
              },
            },
          ],
          { cancelable: false },
        );
      }
      yield put(
        setLoaderStateAction({
          key: 'cancelingSubscriptionLoading',
          value: false,
        }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else {
      Alert.alert(
        'Error on cancelling the subscription.',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    yield put(
      setLoaderStateAction({
        key: 'cancelingSubscriptionLoading',
        value: false,
      }),
    );
  }
}

function* saveSelectedDiscountForEdit() {
  yield put(setLoaderStateAction({ key: 'discountLoader', value: true }));
  try {
    const selectedDiscountForEdit = yield select(
      (state) => state.clients.selectedDiscount,
    );
    const response = yield call(
      Clients.saveDiscountEditApi,
      selectedDiscountForEdit,
    );
    if (response.status === 200) {
      yield put(
        setPurchaseAction({
          key: 'discount',
          value: response.data,
        }),
      );
      const responseDiscounts = yield call(Clients.getDiscounts);
      if (responseDiscounts.status === 200) {
        yield put(setDiscountsAction(responseDiscounts.data));
      }
      yield put(setLoaderStateAction({ key: 'discountLoader', value: false }));
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
    yield put(setLoaderStateAction({ key: 'discountLoader', value: false }));
  }
}

function* getClientTransactions(action) {
  try {
    const response = yield call(Clients.getClientTransactions, action.payload);
    if (response.status === 200) {
      const bookingsPurchases = response.data.Events.filter(
        (event) => event.EventType === 'PURCHASE'
          || event.EventType === 'CHECK_IN'
          || event.EventType === 'BALANCE_ADJUSTMENT',
      );
      yield put(setClientTransactions(bookingsPurchases));
      track('view_history');
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
  }
}

function* getClientSubscriptions(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'subscriptionsLoader', value: true }),
    );
    const response = yield call(Clients.getClientSubscriptions, action.payload);
    if (response.status === 200) {
      yield put(setClientSubscriptions(response.data));
      console.log('subscription answer', response);
      track('view_history');
      yield put(
        setLoaderStateAction({ key: 'subscriptionsLoader', value: false }),
      );
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    yield put(
      setLoaderStateAction({ key: 'subscriptionsLoader', value: false }),
    );
    console.log('error status', error.response);
  }
}

function* getClientBalance(action) {
  try {
    const response = yield call(Clients.getClientBalance, action.payload);
    if (response.status === 200) {
      yield put(setClientBalance(response.data.ClientPurchases));
      track('view_balances');
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
  }
}

function* clientBalanceAdjustment(action) {
  yield put(
    setLoaderStateAction({ key: 'saveBalanceAdjustmentLoader', value: true }),
  );
  try {
    const clientId = yield select((state) => state.clients.selectedClient?.id);
    const productDurationId = yield select(
      (state) => state.clients.selectedClientBalance.ProductDuration.id,
    );
    const response = yield call(
      Clients.saveBalanceAdjustment,
      clientId,
      productDurationId,
      action.payload,
    );
    if (response.status === 201) {
      yield getClientTransactions({ payload: clientId });
      yield getClientBalance({ payload: clientId });
      const clientBalance = yield select((state) => state.clients.clientBalance);
      const productId = yield select(
        (state) => state.clients.selectedClientBalance.ProductDuration.productId,
      );
      const productBalances = [];
      clientBalance.forEach((balance) => balance.clientPurchaseProductDurations.forEach((cppd) => productBalances.push(cppd)));
      const selectedBalance = productBalances.filter(
        (item) => item.ProductDuration.productId === productId,
      );
      if (selectedBalance.length) {
        yield put(setSelectedClientBalance(selectedBalance[0]));
      }
      NavigatorService.goBack();
    }
    yield put(
      setLoaderStateAction({
        key: 'saveBalanceAdjustmentLoader',
        value: false,
      }),
    );
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
    yield put(
      setLoaderStateAction({
        key: 'saveBalanceAdjustmentLoader',
        value: false,
      }),
    );
  }
}

function* deleteSelectedDiscountForEdit() {
  try {
    const selectedDiscountForEdit = yield select(
      (state) => state.clients.selectedDiscount,
    );
    const discountId = selectedDiscountForEdit.id;
    const response = yield call(Clients.deleteDiscountApi, discountId);
    if (response.status === 200) {
      const responseDiscounts = yield call(Clients.getDiscounts);
      if (responseDiscounts.status === 200) {
        yield put(setDiscountsAction(responseDiscounts.data));
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
  }
}

function* getClientCreditCard(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'showBuyProductsLoading', value: true }),
    );
    yield put(
      setLoaderStateAction({ key: 'showCreditCardListLoading', value: true }),
    );
    const response = yield call(Clients.getClientCreditCard, action.payload);
    if (response.status === 200) {
      yield put(setClientCreditCard(response.data));
      yield put(
        setLoaderStateAction({ key: 'showBuyProductsLoading', value: false }),
      );
      yield put(
        setLoaderStateAction({
          key: 'showCreditCardListLoading',
          value: false,
        }),
      );
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'showBuyProductsLoading', value: false }),
    );
    yield put(
      setLoaderStateAction({ key: 'showCreditCardListLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
  }
}

function* saveClientCreditCard(action) {
  yield put(setLoaderStateAction({ key: 'validateCardLoader', value: true }));
  try {
    yield put(setSavingClientCardAction(true));
    let client = yield select((state) => (state.clients.selectedClient && state.clients.selectedClient?.Client
      ? state.clients.selectedClient?.Client
      : state.clients.selectedClient));
    if (!client) {
      client = yield select((state) => state.user.details);
    }
    const payload = {
      Clients: [
        {
          ...client,
          creditCard: {
            stripeToken: action.payload,
          },
        },
      ],
    };
    const response = yield call(Clients.saveClientCrediCard, payload);
    if (response.status === 200) {
      yield put(
        setLoaderStateAction({ key: 'validateCardLoader', value: false }),
      );
      yield call(getClientCreditCard, { payload: client.id });
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'validateCardLoader', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else if (
      error.response.status === 400
      || error.response.status === 500
      || error.response.status === 403
    ) {
      Alert.alert(
        'Error on saving Credit Card',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
  } finally {
    yield put(
      setLoaderStateAction({ key: 'validateCardLoader', value: false }),
    );
    yield put(setSavingClientCardAction(false));
  }
}

function* saveCreditCardByClient(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'ccSaveRemoveLoading', value: true }),
    );
    const currentUser = yield select((state) => state.currentUser);
    const selectedClient = yield select((state) => state.clients.selectedClient);
    const user = yield select((state) => state.user.details);

    let response;
    if (currentUser.role === ROLES.TRAINER) {
      response = yield call(
        Clients.saveCrediCardByClient,
        selectedClient?.id,
        action.payload,
      );
    } else {
      response = yield call(
        Clients.saveCrediCardByClient,
        user.clientId,
        action.payload,
      );
    }

    if (response.status === 201) {
      yield put(
        setLoaderStateAction({ key: 'ccSaveRemoveLoading', value: false }),
      );
      if (currentUser.role === ROLES.TRAINER) {
        yield call(getClientCreditCard, { payload: selectedClient?.id });
      } else {
        yield call(getClientCreditCard, { payload: user.clientId });
      }
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'ccSaveRemoveLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else if (
      error.response.status === 400
      || error.response.status === 500
      || error.response.status === 403
    ) {
      Alert.alert(
        'Error on saving Credit Card',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    console.log('error status', error);
  }
}

function* removeCreditCard() {
  try {
    yield put(
      setLoaderStateAction({ key: 'ccSaveRemoveLoading', value: true }),
    );
    const currentUser = yield select((state) => state.currentUser);
    const selectedClient = yield select((state) => state.clients.selectedClient);
    const user = yield select((state) => state.user.details);

    let response;
    if (currentUser.role === ROLES.TRAINER) {
      response = yield call(Clients.removeCreditCard, selectedClient?.id);
    } else {
      response = yield call(Clients.removeCreditCard, user.clientId);
    }

    if (response.status === 204) {
      yield put(
        setLoaderStateAction({ key: 'ccSaveRemoveLoading', value: false }),
      );
      yield call(getClientCreditCard, { payload: user.clientId });
    }
  } catch (error) {
    yield put(
      setLoaderStateAction({ key: 'ccSaveRemoveLoading', value: false }),
    );
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    } else if (
      error.response.status === 404
      || error.response.status === 400
      || error.response.status === 500
    ) {
      Alert.alert(
        'Error on deleting Credit Card',
        error.response.data.message,
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: false },
      );
    }
    console.log('error status', error);
  }
}

function* refundClientTransaction(action) {
  try {
    yield put(
      setLoaderStateAction({ key: 'refundActionLoading', value: true }),
    );
    const { clientId } = action.payload.purchase;
    const purchaseId = action.payload.purchase.id;
    const response = yield call(
      Clients.refundTransaction,
      clientId,
      purchaseId,
    );
    if (response.status === 200) {
      const responseTransactions = yield call(
        Clients.getClientTransactions,
        clientId,
      );
      if (responseTransactions.status === 200) {
        const bookingsPurchases = responseTransactions.data.Events.filter(
          (event) => event.EventType === 'PURCHASE'
            || event.EventType === 'CHECK_IN'
            || event.EventType === 'BALANCE_ADJUSTMENT',
        );
        yield put(setClientTransactions(bookingsPurchases));
        const success = {
          title: 'Success',
          message: "Your client's refund is complete.",
        };
        yield put(setSuccessAction(success));
        yield put(
          setLoaderStateAction({ key: 'refundActionLoading', value: false }),
        );
      }
    }
  } catch (error) {
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
    yield put(
      setLoaderStateAction({ key: 'refundActionLoading', value: false }),
    );
  }
}

function* clientBuyPackage() {
  try {
    yield put(setLoaderStateAction({ key: 'bookPackageLoader', value: true }));
    const clientId = yield select((state) => state.user.details.clientId);
    const accountId = yield select((state) => state.user.details.account.id);
    const packageId = yield select((state) => state.services.selectedPackage.id);

    const data = { clientId };
    const response = yield call(
      Clients.buyPackageByClient,
      accountId,
      packageId,
      data,
    );
    if (response.status === 200 && response.data.success === 'true') {
      Alert.alert(
        'Success',
        'Package purchased and applied to your account.',
        [
          {
            text: 'Continue',
            onPress: () => {
              NavigatorService.navigate(APPLICATION_ROUTES.SERVICES);
            },
          },
        ],
        { cancelable: false },
      );
    }
    yield put(setLoaderStateAction({ key: 'bookPackageLoader', value: false }));
  } catch (error) {
    yield put(setLoaderStateAction({ key: 'bookPackageLoader', value: false }));
    console.log('error', error);
    if (error.response.status === 401) {
      yield put(createSetLoginStateAction(false));
    }
    console.log('error status', error.response);
  }
}

export function* clientsSaga() {
  yield takeLatest(GET_ACTIVE_CLIENTS, getActiveClientsList);
  yield takeLatest(GET_DISCOUNTS, getDiscountsList);
  yield takeLatest(
    SAVE_SELECTED_DISCOUNT_FOR_EDIT_ACTION,
    saveSelectedDiscountForEdit,
  );
  yield takeLatest(
    DELETE_SELECTED_DISCOUNT_FOR_EDIT_ACTION,
    deleteSelectedDiscountForEdit,
  );
  yield takeLatest(SAVE_DISCOUNT_ACTION, saveDiscount);
  yield takeLatest(GET_CLIENT_BALANCE, getClientBalance);
  yield takeLatest(GET_CLIENT_TRANSACTIONS, getClientTransactions);
  yield takeLatest(GET_CLIENT_SUBSCRIPTIONS, getClientSubscriptions);
  yield takeLatest(SAVE_CLIENT_CARD, saveClientCreditCard);
  yield takeLatest(SAVE_CARD_BY_CLIENT, saveCreditCardByClient);
  yield takeLatest(GET_CLIENT_CREDIT_CARD, getClientCreditCard);
  yield takeLatest(REMOVE_CREDIT_CARD, removeCreditCard);
  yield takeLatest(CANCEL_SUBSCRIPTION_ACTION, cancelSubscription);
  yield takeLatest(REFUND_TRANSACTION, refundClientTransaction);
  yield takeLatest(BALANCE_ADJUSTMENT_ACTION, clientBalanceAdjustment);
  yield takeLatest(CLIENT_BUY_PACKAGE_ACTION, clientBuyPackage);
}
