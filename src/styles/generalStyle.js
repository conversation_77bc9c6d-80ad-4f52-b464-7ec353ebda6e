import { StyleSheet } from 'react-native';
import { colors, shadow } from '../styles';

export const generalStyles = StyleSheet.create({
  title: {
    fontFamily: 'Avenir-Medium',
    fontSize: 24,
    color: colors.black,
  },
  titleService: {
    fontFamily: 'Avenir-Black',
    fontSize: 24,
  },
  titleScreen: {
    fontFamily: 'Avenir-Black',
    fontSize: 17,
  },
  titleSmall: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  container: {
    display: 'flex',
    marginHorizontal: 17,
  },
  priceText: {
    fontFamily: 'Avenir-Heavy',
    color: colors.white,
    fontSize: 14,
  },
  durationText: {
    fontFamily: 'Avenir-Heavy',
    color: colors.white,
    fontSize: 17,
  },
  serviceNameText: {
    fontFamily: 'Avenir-Black',
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
  smallText: {
    fontFamily: 'Avenir-Roman',
    color: colors.subGrey,
    fontSize: 13,
  },
  mVertical10: {
    display: 'flex',
    marginVertical: 10,
  },
  mBottom10: {
    display: 'flex',
    marginBottom: 10,
  },
  fontBold: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
  },
  avenirBlack17: {
    fontFamily: 'Avenir-Black',
    fontSize: 17,
  },
  avenirRoman13: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
  },
  avenirRoman14: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
  },
  avenirRoman17: {
    fontFamily: 'Avenir-Roman',
    fontSize: 17,
  },
  avenirRoman22: {
    fontFamily: 'Avenir-Roman',
    fontSize: 22,
  },
  avenirHeavy14: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 14,
  },
  avenirHeavy17: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
  },
  avenirHeavy24: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 24,
  },
  avenirHeavy80: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 80,
  },
  avenirHeavy13: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 13,
  },
  avenirMedium15: {
    fontFamily: 'Avenir-Medium',
    fontSize: 15,
  },
  avenirMedium17: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
  },
  avenirMedium14: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
  },
  avenirMedium13: {
    fontFamily: 'Avenir-Medium',
    fontSize: 13,
  },
  avenirHeavy30: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 30,
  },
  addButtonContainer: {},
  addButtonSmall: {
    width: 68,
    height: 68,
  },
  addButton: {
    width: 71,
    height: 71,
  },
  addTooltipButtonSmall: {
    width: 33,
    height: 33,
  },

  editButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    marginBottom: 10,
  },
  editButton: {
    width: 55,
    height: 54,
  },
  inputText: {
    fontFamily: 'Avenir-Roman',
    color: colors.black,
    fontSize: 14,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#00000040',
    borderRadius: 20,
  },
  overlayWithoutRadius: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#00000040',
  },
  navigationButtonText: {
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
  },
});

export const locationStyles = StyleSheet.create({
  locationIcon: {
    width: 18,
    height: 26,
  },
  remoteLocationIcon: {
    width: 26,
    height: 26,
  },
  videoCallLocationIcon: {
    width: 23,
    height: 15,
  },
  locationItem: {
    width: '100%',
    borderRadius: 10,
    flexDirection: 'row',
    paddingVertical: 10,
    paddingRight: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    alignItems: 'center',
    justifyContent: 'center',
    height: 110,
  },
  leftBox: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerBox: {
    flex: 5,
    flexDirection: 'column',
  },
  rightBox: {
    flex: 1,
  },
  addressName: {
    ...generalStyles.serviceNameText,
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
  },
  addressLocation: {
    ...generalStyles.smallText,
    lineHeight: 22,
  },
  numberOfServices: {
    ...generalStyles.smallText,
    lineHeight: 22,
  },
  checkbox: {
    borderRadius: 10,
  },
  switch: {
    textAlign: 'right',
    height: 50,
  },
  sectionLabel: {
    ...generalStyles.serviceNameText,
    color: colors.black,
    fontFamily: 'Avenir-Medium',
  },
  startTimesLabel: {
    ...generalStyles.fontBold,
  },
  prepTimeText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
  },
  locationNameForm: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
  },
});

export const servicesStyles = StyleSheet.create({
  uploadPictureText: {
    color: colors.white,
    fontFamily: 'Avenir-Roman',
    fontSize: 24,
  },
  uploadPictureContainer: {
    backgroundColor: 'black',
    height: 300,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  serviceImage: {
    height: 300,
    width: '100%',
    display: 'flex',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    marginBottom: 20,
  },
});
