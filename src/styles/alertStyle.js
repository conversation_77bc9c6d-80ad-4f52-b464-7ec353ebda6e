import { StyleSheet } from 'react-native';
import { colors } from '../styles';

export const successAlertStyles = StyleSheet.create({
  alertTitle: {
    fontSize: 17,
    fontFamily: 'SFProText-Medium',
    color: colors.black,
    marginTop: 5,
  },
  alertText: {
    fontSize: 13,
    fontFamily: 'SFProText-Regular',
    color: colors.subGrey,
    width: 234,
    textAlign: 'center',
    marginBottom: 39,
    marginTop: 12,
    paddingHorizontal: 10,
  },
  alertButton: {
    width: 270,
    height: 44,
    borderTopWidth: 1,
    borderRadius: 5,
    borderTopColor: colors.lightgrey,
    backgroundColor: colors.white,
    margin: 0,
  },
  alertButtonText: {
    fontSize: 17,
    fontFamily: 'Avenir-Heavy',
    color: colors.subGrey,
    textAlign: 'center',
  },
  alertContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: 238,
    width: 270,
    backgroundColor: colors.white,
    borderRadius: 5,
  },
});

export const deleteAlertStyles = StyleSheet.create({
  alertTitle: {
    fontSize: 17,
    fontFamily: 'SFProText-Medium',
    color: colors.black,
    marginTop: 35,
    textAlign: 'center',
  },
  alertText: {
    fontSize: 13,
    fontFamily: 'SFProText-Regular',
    color: colors.subGrey,
    width: 270,
    textAlign: 'center',
    marginBottom: 39,
    padding: 4,
    paddingHorizontal: 18,
  },
  alertButton: {
    width: 270,
    height: 44,
    borderTopWidth: 1,
    borderRadius: 5,
    borderTopColor: colors.lightgrey,
    backgroundColor: colors.white,
    margin: 0,
    alignItems: 'center',
  },
  alertButtonText: {
    fontSize: 17,
    fontFamily: 'Avenir-Heavy',
    color: colors.subGrey,
    textAlign: 'center',
  },
  alertContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 120,
    width: 270,
    backgroundColor: colors.white,
    paddingBottom: 0,
  },
});
