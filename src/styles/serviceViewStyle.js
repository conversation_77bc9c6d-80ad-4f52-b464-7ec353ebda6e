import { StyleSheet } from 'react-native';
import { colors } from '../styles';

export const serviceViewStyles = StyleSheet.create({
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  closeIcon: {
    flex: 1,
    marginTop: 48,
    marginLeft: 20,
    width: 35,
    height: 35,
  },
  headerName: {
    marginLeft: 20,
    width: '100%',
  },
  title: {
    fontFamily: 'Avenir-Black',
    lineHeight: 30,
    fontSize: 24,
    color: colors.white,
    fontWeight: 'bold',
    marginTop: 45,
  },
  headerTimePrice: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 15,
    flexDirection: 'row',
  },
  headerTime: {
    flex: 4,
    fontFamily: 'Avenir-Heavy',
    alignItems: 'flex-start',
    color: colors.white,
    fontSize: 17,
    alignSelf: 'center',
  },
  headerPriceContainer: {
    flex: 1,
    alignItems: 'center',
    textAlign: 'center',
    alignSelf: 'center',
    backgroundColor: colors.goodGreen,
    borderRadius: 30,
    height: 37,
    width: 60,
  },
  headerPrice: {
    fontFamily: 'Avenir-Heavy',
    textAlign: 'center',
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 14,
    marginTop: 9,
  },
});
