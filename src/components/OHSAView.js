import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  View, Text, FlatList, TouchableOpacity, Image,
} from 'react-native';
import moment from 'moment';
import ResultMuscleList from './ResultMuscleList';

// Styles
import { colors } from '../styles';

// Images
const arrowIcon = require('../resources/buttonPlayLight.png');
const check = require('../resources/imgCheckmarkOutline.png');

const propTypes = {
  dataset: PropTypes.shape({}).isRequired,
  correctiveExercises: PropTypes.array.isRequired,
  isTrainer: PropTypes.bool,
  onPressExercise: PropTypes.func.isRequired,
  showUpdatedOn: PropTypes.bool,
};

const defaultProps = {
  isTrainer: false,
  showUpdatedOn: false,
};

class OHSAView extends PureComponent {
  render() {
    return (
      <View style={styles.container}>
        <ResultMuscleList
          overactive
          muscles={
            this.props.dataset.over_active_muscles
              ? this.props.dataset.over_active_muscles
              : []
          }
          Header={(
            <View style={styles.topContainer}>
              <Text style={styles.headerText}>
                Below are the overactive and underactive muscles based on your
                {' '}
                {this.props.isTrainer ? 'client’s ' : ''}
                assessment as well as NASM EDGE’s recommended exercises.
              </Text>
            </View>
          )}
          Footer={() => (
            <ResultMuscleList
              muscles={
                this.props.dataset.under_active_muscles
                  ? this.props.dataset.under_active_muscles
                  : []
              }
              Footer={() => (
                <View style={styles.cardStyle}>
                  <View style={styles.titleContainer}>
                    <Image source={check} />
                    <Text style={styles.recomendedTitle}>
                      {this.props.isTrainer
                        ? 'When creating a program, t'
                        : 'T'}
                      he following exercises are available as recommendations
                      based on your
                      {' '}
                      {this.props.isTrainer ? 'client’s ' : ''}
                      OHSA results.
                    </Text>
                  </View>
                  <FlatList
                    data={this.props.correctiveExercises}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item, index }) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.rowContainer}
                        onPress={() => this.props.onPressExercise(item)}
                      >
                        <Image source={arrowIcon} />
                        <Text style={styles.exerciseName}>{item.name}</Text>
                      </TouchableOpacity>
                    )}
                  />
                  {this.props.showUpdatedOn && (
                    <Text style={styles.date}>
                      Updated on
                      {' '}
                      {moment(this.props.dataset.assessment_date).format(
                        'M/D/YY',
                      )}
                    </Text>
                  )}
                </View>
              )}
            />
          )}
        />
      </View>
    );
  }
}

const styles = {
  container: {
    flex: 1,
  },
  cardStyle: {
    marginTop: 20,
    borderRadius: 3,
    marginBottom: 10,
  },
  rowContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: 'rgba(124, 128, 132, 0.1)',
  },
  exerciseName: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.black,
    marginLeft: 7,
    marginRight: 22,
  },
  emptypropsText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.black,
    marginHorizontal: 20,
    marginVertical: 20,
  },
  titleContainer: {
    backgroundColor: colors.goodGreen,
    paddingHorizontal: 20,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    borderTopRightRadius: 3,
    borderTopLeftRadius: 3,
  },
  topContainer: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 46,
    alignItems: 'center',
  },
  headerText: {
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '300',
    color: colors.cloudyBlue,
    textAlign: 'center',
  },
  recomendedTitle: {
    flex: 1,
    marginHorizontal: 16,
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '800',
    color: colors.white,
  },
  date: {
    marginTop: 37,
    marginBottom: 45,
    marginLeft: 29,
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.cloudyBlue,
  },
};

OHSAView.propTypes = propTypes;
OHSAView.defaultProps = defaultProps;
export default OHSAView;
