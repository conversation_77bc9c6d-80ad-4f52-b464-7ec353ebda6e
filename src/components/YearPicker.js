import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image,
  LayoutAnimation,
  Picker,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { colors } from '../styles';
import { androidSafeLayoutAnimation } from '../constants';

// Helpers
function getYears() {
  const endYear = new Date().getUTCFullYear();
  const startYear = endYear - 50;
  const length = endYear - startYear;
  return Array(length)
    .fill(endYear)
    .map((value, index) => (value - index).toString());
}

// Constants
const years = getYears();
const downIcon = require('../resources/downBlue.png');

// Props
const propTypes = {
  onValueChange: PropTypes.func.isRequired,
  selected: PropTypes.string,
};

const defaultProps = {
  customStyle: null,
  selected: years[0],
};

// Component
class YearPicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  render() {
    return (
      <View>
        {Platform.OS === 'ios' && (
          <TouchableOpacity
            style={[styles.inputContainer, styles.inputUnderline]}
            onPress={() => {
              LayoutAnimation.configureNext(androidSafeLayoutAnimation);
              this.setState({ visible: !this.state.visible });
            }}
          >
            <Text style={styles.inputText}>
              {this.props.selected || years[0]}
            </Text>
            <Image source={downIcon} />
          </TouchableOpacity>
        )}
        {(this.state.visible || Platform.OS === 'android') && (
          <View>
            <Picker
              selectedValue={this.props.selected || years[0]}
              onValueChange={(value) => this.props.onValueChange(value)}
            >
              {years.map((value) => (
                <Picker.Item key={value} label={value} value={value} />
              ))}
            </Picker>
            <View style={styles.inputUnderline} />
          </View>
        )}
      </View>
    );
  }
}

// Exports
YearPicker.propTypes = propTypes;
YearPicker.defaultProps = defaultProps;
export default YearPicker;

// Styles
const styles = StyleSheet.create({
  inputContainer: {
    height: 45,
    paddingTop: 3,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 5,
  },
  inputText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    color: colors.black,
  },
  inputUnderline: {
    borderBottomWidth: 2,
    borderBottomColor: colors.silver,
  },
});
