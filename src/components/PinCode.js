import React, { Component } from 'react';

// Components
import {
  Al<PERSON>, Text, StyleSheet, TextInput, View,
} from 'react-native';
import { PropTypes } from 'prop-types';

// Styles
import { colors } from '../styles';
import { removeAllSpecialCharacters } from '../util/validate';

class PinCode extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentCode: new Array(4).fill(''),
      validPinCode: this.props.validPinCode,
      isInvalid: false,
      isUpdate: this.props.isUpdate ? this.props.isUpdate : false,
      error: 'Error, Invalid Passcode',
    };
    this.textInputRefs = new Array(4).fill(null);
  }

  componentDidMount() {
    this.focus(0);
  }

  clean = () => {
    this.setState({
      currentCode: new Array(4).fill(''),
      isInvalid: true,
    });
    this.focus(0);
  };

  completedClean = () => {
    this.setState({
      currentCode: new Array(4).fill(''),
    });
    this.focus(0);
  };

  focus = (id) => {
    this.textInputRefs[id].focus();
  };

  handleKeyDown = (keyPressed, index) => {
    if (keyPressed.nativeEvent.key === 'Backspace') {
      if (index > 0) {
        this.focus(index - 1);
      } else if (index === 0) {
        this.focus(index);
      }
    }
  };

  updateCode = (value, index) => {
    if (value === '' || this.textInputRefs[index] === null) {
      this.setState((prevState) => {
        const { currentCode } = prevState;
        currentCode[index] = value;
        return { currentCode };
      });
      if (index > 0) {
        this.focus(index - 1);
      } else if (index === 0) {
        this.focus(index);
      }
    } else if (Number.isFinite(value)) {
      this.setState(
        (prevState) => {
          const { currentCode } = prevState;
          currentCode[index] = value;
          return { currentCode };
        },
        () => this.validatingCode(index),
      );
    }
  };

  validatingCode = (index) => {
    if (index < this.textInputRefs.length - 1) {
      this.textInputRefs[index + 1].focus();
    } else {
      const pinCode = this.state.currentCode.join('');

      if (this.state.validPinCode === '') {
        this.setState({
          validPinCode: pinCode,
        });
        this.props.onComplete(pinCode);
        this.completedClean();
      } else if (this.state.isUpdate && pinCode === this.state.validPinCode) {
        this.setState({ validPinCode: '', isUpdate: false });
        this.props.onComplete(pinCode);
        this.completedClean();
      } else if (pinCode !== this.state.validPinCode) {
        Alert.alert(
          'Incorrect pin code',
          '',
          [
            {
              text: 'OK',
              onPress: () => {
                this.completedClean();
                this.props.onComplete(pinCode);
              },
            },
          ],
          { cancelable: false },
        );
      } else {
        this.props.onComplete(pinCode);
        this.completedClean();
      }
    }
  };

  render() {
    return (
      // <PageContainer>
      <View>
        <Text style={styles.headlineText}>{this.props.prompt}</Text>
        <View style={[styles.containerPin, styles.containerPinStyle]}>
          {this.state.currentCode.map((currentValue, index) => (
            <TextInput
              keyboardType="numeric"
              maxLength={1}
              ref={(input) => {
                this.textInputRefs[index] = input;
              }}
              style={styles.pin}
              value={currentValue}
              key={`code${index}`}
              onKeyPress={(keyPressed) => this.handleKeyDown(keyPressed, index)}
              onChangeText={(value) => {
                const filteredValue = removeAllSpecialCharacters(value);
                this.updateVariable(filteredValue, index);
              }}
              underlineColorAndroid="transparent"
              selectionColor={colors.nasmBlue}
            />
          ))}
        </View>
      </View>
      // </PageContainer>
    );
  }
}

PinCode.propTypes = {
  validPinCode: PropTypes.string.isRequired,
  prompt: PropTypes.string.isRequired,
  isUpdate: PropTypes.bool.isRequired,
  onComplete: PropTypes.func,
};

// Export
export default PinCode;

const styles = StyleSheet.create({
  headlineText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 18,
    color: colors.black,
    textAlign: 'center',
    marginTop: 27,
  },
  containerPin: {
    flex: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: 50,
    marginLeft: 75,
    marginRight: 75,
  },
  pin: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 36,
    color: colors.black,
    textAlign: 'center',
    flex: 1,
    marginLeft: 5,
    marginRight: 5,
    borderBottomWidth: 1,
    borderBottomColor: colors.black,
  },
});
