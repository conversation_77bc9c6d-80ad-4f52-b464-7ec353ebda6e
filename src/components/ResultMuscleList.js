import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Text, View, FlatList, Image,
} from 'react-native';

// Styles
import { colors } from '../styles';

// Images
const overactiveImage = require('../resources/imgOveractive.png');
const underactiveImage = require('../resources/imgUnderactive.png');

const propTypes = {
  overactive: PropTypes.bool,
  muscles: PropTypes.array,
};

const defaultProps = {
  overactive: false,
  muscles: [],
};

class ResultMuscleList extends PureComponent {
  render() {
    return (
      <View style={styles.card}>
        <FlatList
          showsVerticalScrollIndicator={false}
          data={this.props.muscles}
          keyExtractor={(item) => item.id}
          renderItem={({ item, index }) => (
            <View
              key={index}
              style={index !== 0 ? styles.rowTopBorder : styles.row}
            >
              <Text style={styles.muscles}>{item.name}</Text>
            </View>
          )}
          ListHeaderComponent={() => (
            <View>
              {this.props.Header}
              <View style={styles.titleContainer}>
                <Text style={styles.title}>
                  {this.props.overactive
                    ? 'Overactive Muscles'
                    : 'Underactive Muscles'}
                </Text>
                <Image
                  source={
                    this.props.overactive ? overactiveImage : underactiveImage
                  }
                />
              </View>
            </View>
          )}
          ListFooterComponent={this.props.Footer}
          ListEmptyComponent={() => (
            <View style={styles.row}>
              <Text style={styles.muscles}>None</Text>
            </View>
          )}
        />
      </View>
    );
  }
}

const styles = {
  card: {
    backgroundColor: colors.white,
    paddingTop: 10,
    borderColor: 'rgba(124, 128, 132, 0.1)',
    borderTopWidth: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    paddingTop: 12,
    paddingBottom: 5,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    flex: 1,
    fontFamily: 'Avenir',
    fontSize: 18,
    color: colors.subGrey,
    fontWeight: '900',
  },
  muscles: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.black,
  },
  rowTopBorder: {
    borderColor: 'rgba(124, 128, 132, 0.1)',
    borderTopWidth: 1,
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  row: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
};

ResultMuscleList.proptTypes = propTypes;
ResultMuscleList.defaultProps = defaultProps;
export default ResultMuscleList;
