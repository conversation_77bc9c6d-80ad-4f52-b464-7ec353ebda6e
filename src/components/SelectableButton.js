import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Text, TouchableOpacity, StyleSheet, Image,
} from 'react-native';

// Styles
import { colors } from '../styles';

// Images
const addImage = require('../resources/exerciseAdd.png');
const addedImage = require('../resources/exerciseAdded.png');

// PropTypes
const propTypes = {
  onPress: PropTypes.func.isRequired,
  imageForSelection: PropTypes.func,
  id: PropTypes.string,
  selected: PropTypes.bool,
  imageButton: PropTypes.bool,
};

const defaultProps = {
  id: 'default',
  selected: false,
  containerStyle: null,
  buttonStyle: null,
  textStyles: null,
  imageButton: false,
  imageForSelection: undefined,
};

// Component definition
export default class SelectableButton extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selected: this.props.selected,
    };
  }

  onPress() {
    this.setState(
      {
        selected: !this.state.selected,
      },
      () => {
        this.props.onPress(this.props.id, this.state.selected);
      },
    );
  }

  getImageAssetForCurrentSelection() {
    if (this.props.imageForSelection !== undefined) {
      return this.props.imageForSelection(this.props.selected);
    }
    return this.props.selected ? addedImage : addImage;
  }

  render() {
    if (this.props.imageButton) {
      return (
        <TouchableOpacity
          style={this.props.containerStyle}
          onPress={() => this.onPress()}
        >
          <Image source={this.getImageAssetForCurrentSelection()} />
        </TouchableOpacity>
      );
    }
    return (
      <TouchableOpacity
        style={[
          this.state.selected ? styles.selectedStyle : styles.unselectedStyle,
          this.props.containerStyle,
        ]}
        onPress={() => this.onPress()}
      >
        <Text style={styles.textStyle}>
          {this.state.selected ? 'YES' : 'NO'}
        </Text>
      </TouchableOpacity>
    );
  }
}

// Export
SelectableButton.propTypes = propTypes;
SelectableButton.defaultProps = defaultProps;

const styles = StyleSheet.create({
  selectedStyle: {
    width: 52,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.nasmRed,
    borderRadius: 16,
  },
  unselectedStyle: {
    width: 52,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.nasmBlue,
    borderRadius: 16,
  },
  textStyle: {
    color: colors.white,
  },
});
