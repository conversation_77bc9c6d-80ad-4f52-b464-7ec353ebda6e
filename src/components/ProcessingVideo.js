import React from 'react';

import { Image, View, Text, StyleSheet, Dimensions } from 'react-native';

import { colors } from '../styles';

const processingIcon = require('../resources/processingIcon.png');

const ProcessingVideo = () => (
  <View style={styles.videoProcessingView}>
    <Image style={styles.imageStyle} source={processingIcon} />
    <Text style={styles.videoTextBig}>We’re processing this video.</Text>
    <Text style={styles.videoTextSmall}>Video will be ready soon.</Text>
  </View>
);

const styles = StyleSheet.create({
  imageStyle: { marginTop: 60 },
  videoProcessingView: {
    backgroundColor: colors.black,
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    height: Dimensions.get('window').width / (16 / 9),
  },
  videoTextSmall: { marginTop: 10, fontSize: 14, color: colors.white },
  videoTextBig: {
    marginTop: 10,
    fontSize: 24,
    color: colors.white,
  },
});

export default ProcessingVideo;
