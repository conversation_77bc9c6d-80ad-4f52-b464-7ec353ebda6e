import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../styles';
import { getAvatarUrl } from '../util/utils';

// Images
const defaultProfile = require('../resources/defaultProfile.png');
const rightArrow = require('../resources/imgRightArrowGray.png');

// PropTypes
const propTypes = {
  profileImage: PropTypes.string,
  onPress: PropTypes.func,
  name: PropTypes.string,
  containerStyle: PropTypes.any,
  active: PropTypes.bool,
  groupInfo: PropTypes.object,
};

const defaultProps = {
  profileImage: null,
  onPress: null,
  name: null,
  date: null,
  containerStyle: null,
  active: true,
  groupInfo: {},
};

class DashboardProfile extends PureComponent {
  renderClient = () => (
    <>
      <View style={styles.avatarContainer(true)}>
        <Image
          style={styles.profileImageStyle(50)}
          source={
            this.props.profileImage
              ? { uri: this.props.profileImage }
              : defaultProfile
          }
        />
      </View>
      <View style={styles.nameView}>
        <Text
          style={styles.nameTextStyle}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {this.props.name}
        </Text>
      </View>
    </>
  );

  renderGroup = (groupInfo) => {
    const { client_group_clients = [] } = groupInfo;
    const filtered_clients = [...client_group_clients];
    if (filtered_clients.length > 4) {
      filtered_clients.splice(4, filtered_clients.length - 4);
    }
    return (
      <>
        <FlatList
          data={filtered_clients}
          numColumns={2}
          keyExtractor={(item) => item.client_user.id}
          renderItem={this.renderUsericon}
          style={styles.avatarContainer(client_group_clients.length === 1)}
        />
        <View style={styles.nameView}>
          <Text
            style={styles.groupNameStyle}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {groupInfo?.title}
          </Text>
          <Text style={styles.members} numberOfLines={1} ellipsizeMode="tail">
            {groupInfo?.client_group_clients?.length}
            {' '}
            Members
          </Text>
        </View>
      </>
    );
  };

  renderUsericon = ({ item, index }) => {
    const profileImg = item?.user?.avatar_url
      ? item?.user?.avatar_url
      : getAvatarUrl(item?.client_user?.user);
    return (
      <View style={styles.profileView(index)}>
        <Image
          style={styles.profileImageStyle(
            !this.props.groupInfo?.id
              || this.props.groupInfo?.client_group_clients?.length === 1
              ? 50
              : 30,
          )}
          source={profileImg ? { uri: profileImg } : defaultProfile}
        />
      </View>
    );
  };

  render() {
    const { groupInfo, active } = this.props;
    const opacity = active ? 1.0 : 0.25;
    return (
      <TouchableOpacity
        style={[{ opacity }, styles.container, this.props.containerStyle]}
        onPress={() => this.props.onPress(this.props.active)}
      >
        <View style={styles.innerContainer}>
          {groupInfo?.id ? this.renderGroup(groupInfo) : this.renderClient()}
        </View>
        <View>
          <Image source={rightArrow} />
        </View>
      </TouchableOpacity>
    );
  }
}

// Export
DashboardProfile.propTypes = propTypes;
DashboardProfile.defaultProps = defaultProps;
export default DashboardProfile;

// Styles
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    borderBottomWidth: 1,
    borderColor: colors.subGreyLight,
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 8,
  },
  nameView: {
    paddingHorizontal: 15,
    flex: 8,
  },
  nameTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 24,
    color: colors.black,
    textTransform: 'capitalize',
  },
  groupNameStyle: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 17,
    lineHeight: 24,
    color: colors.black,
    textTransform: 'capitalize',
  },
  members: {
    fontFamily: 'Avenir-Medium',
    fontSize: 12,
    color: colors.cloudyBlue,
  },
  profileImageStyle: (imageSize) => ({
    width: imageSize,
    height: imageSize,
    borderRadius: imageSize / 2,
  }),
  profileView: (index) => ({
    marginLeft: index % 2 !== 0 ? 5 : 0,
    marginTop: 3,
  }),
  avatarContainer: (shouldAlignCenter = false) => ({
    width: '20%',
    alignItems: shouldAlignCenter ? 'center' : 'flex-start',
  }),
});
