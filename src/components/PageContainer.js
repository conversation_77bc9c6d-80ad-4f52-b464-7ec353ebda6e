import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Dimensions,
  Image,
  View,
  StyleSheet,
  Platform,
  StatusBar,
} from 'react-native';
import KeyboardHandler from './KeyboardHandler';

// Styles
import { colors } from '../styles';

// Images
const bgImage = require('../../assets/bgWhite.png');

// Constants
const { width, height } = Dimensions.get('window');

let statusBarHeight = 0;
if (Platform.OS === 'android') {
  statusBarHeight = StatusBar.currentHeight;
}

// PropTypes
const propTypes = {
  bgImage: PropTypes.bool,
  children: PropTypes.node,
  scrollEnabled: PropTypes.bool,
  testID: PropTypes.string,
  containerStyle: PropTypes.object,
};
const defaultProps = {
  bgImage: false,
  children: null,
  containerStyle: {},
  scrollEnabled: false,
  testID: null,
};

// Main
class PageContainer extends Component {
  render() {
    return (
      <View
        style={[
          styles.screen,
          this.props.containerStyle.backgroundColor && {
            backgroundColor: this.props.containerStyle.backgroundColor,
          },
        ]}
        testID={this.props.testID}>
        {this.props.bgImage && (
          <Image
            source={bgImage}
            resizeMode="cover"
            width={width}
            style={styles.bgImage}
          />
        )}
        <KeyboardHandler
          ref={keyboardHandler => (this.keyboardHandler = keyboardHandler)}
          scrollEnabled={this.props.scrollEnabled}>
          <View
            style={[
              styles.container,
              this.props.containerStyle,
              this.props.bgImage && styles.transparent,
            ]}>
            {this.props.children}
          </View>
        </KeyboardHandler>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: colors.background,
  },
  container: {
    flexGrow: 1,
    paddingHorizontal: 18,
    paddingVertical: 30,
  },
  bgImage: {
    position: 'absolute',
    width: '100%',
    height: height + statusBarHeight,
  },
  transparent: {
    backgroundColor: 'transparent',
  },
});

// Export
PageContainer.propTypes = propTypes;
PageContainer.defaultProps = defaultProps;
export default PageContainer;
