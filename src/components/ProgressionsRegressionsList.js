import React, { memo } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  StyleSheet,
  SectionList,
  View,
  Text,
  Image,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { curvedScale, scaleHeight, scaleWidth } from '../util/responsive';
import ScaledText from './ScaledText';

// Styles
import { colors } from '../styles';
import { EXERCISE_TYPE } from '../constants';
import { getDurationFromSeconds } from '../util/programUtils';
import { editExercise, saveWorkout } from '../reducers/selectedWorkoutReducer';
import { programContexts } from '../reducers/programContextReducer';

// Images
const rightArrow = require('../resources/imgRightArrowGray.png');
const backgroundImage = require('../resources/imgExerciseBlockDefault.png');

const progressionIcon = require('../assets/progressionIcon.png');
const regressionIcon = require('../assets/regressionIcon.png');
const checkMarkGreen = require('../resources/checkMarkGreen.png');

// PropTypes
const propTypes = {
  exercise: PropTypes.obj,
  headerComponent: PropTypes.element,
  programContext: PropTypes.bool,
  comingFrom: PropTypes.string,
  exeOrCompoundExe: PropTypes.object,
};
const defaultProps = {
  exercise: {},
  headerComponent: <></>,
  programContext: false,
  comingFrom: '',
  exeOrCompoundExe: {},
};

const ProgressionsRegressionsList = ({
  exercise,
  headerComponent,
  programContext,
  comingFrom,
  exeOrCompoundExe,
}) => {
  const {
    progressions_regressions,
    progression_enabled = true,
    regression_enabled = true,
  } = exercise;
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const sections = [];
  const isComingFromSummary = comingFrom === 'SummaryCard';
  const isViewOnly = programContext === programContexts.LIBRARY || isComingFromSummary;

  const getSections = () => {
    if (!progressions_regressions?.length) {
      return [];
    }

    const progressionExercises = progressions_regressions.filter(
      (ex) => ex.exerciseType === EXERCISE_TYPE.PROGRESSION,
    );
    const regressionExercises = progressions_regressions.filter(
      (ex) => ex.exerciseType === EXERCISE_TYPE.REGRESSION,
    );

    if (progressionExercises?.length) {
      sections.push({
        id: 0,
        title: isViewOnly ? 'Progressions' : 'Make Progressions Available',
        data: progressionExercises,
        isEnabled: progression_enabled,
      });
    }

    if (regressionExercises?.length) {
      sections.push({
        id: 1,
        title: isViewOnly ? 'Regressions' : 'Make Regressions Available',
        data: regressionExercises,
        isEnabled: regression_enabled,
      });
    }

    return sections;
  };

  const onPressExercise = (exerciseItem) => {
    const name = 'ExerciseDetails';
    const params = {
      exercise: exerciseItem,
      isEditable: false,
      isEditNameIconShow: false,
    };
    navigation.push(name, params);
  };

  const onToggleChanged = (section, isEnabled) => {
    const updatedExercise = {
      ...exercise,
    };
    if (section.id === 0) {
      updatedExercise.progression_enabled = isEnabled;
    } else if (section.id === 1) {
      updatedExercise.regression_enabled = isEnabled;
    }
    if (exeOrCompoundExe?.compound_routine_exercises?.length) {
      const updatedCompoundExe = {
        ...exeOrCompoundExe,
      };
      const compound_exe_arr = updatedCompoundExe.compound_routine_exercises.map((ex) => {
        if (ex.id === exercise?.id) {
          return {
            ...updatedExercise,
          };
        }
        return ex;
      });
      updatedCompoundExe.compound_routine_exercises = compound_exe_arr;
      saveDataLocally(updatedCompoundExe);
      return;
    }
    saveDataLocally(updatedExercise);
  };

  const saveDataLocally = (updatedData) => {
    dispatch(editExercise(updatedData));
    if (programContext === programContexts.SCHEDULING) {
      dispatch(saveWorkout());
    }
  };

  const getRightView = (item) => {
    if (!isComingFromSummary) {
      return (
        <View style={styles.horizontalView}>
          <Text style={styles.timeText}>
            {' '}
            {getDurationFromSeconds(
              item.exercise.total_dur_seconds,
              item.exercise.duration_plus,
            )}
          </Text>
          <Image style={styles.arrow} source={rightArrow} />
        </View>
      );
    }
    if (item.is_complete) {
      return (
        <View style={styles.checkView}>
          <Image style={styles.checked} source={checkMarkGreen} />
        </View>
      );
    }
    return null;
  };

  const renderRow = ({ item }) => (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.cell}
        disabled={isComingFromSummary}
        onPress={() => {
          onPressExercise(item.exercise);
        }}
      >
        <View style={styles.horizontalView}>
          <Image
            style={styles.thumbnail}
            source={{ uri: item.exercise.image_url } || backgroundImage}
          />
          <View>
            <ScaledText style={styles.nameText}>
              {item.exercise.name}
            </ScaledText>
            <View style={styles.variableView}>
              {item.exercise.reps ? (
                <Text style={styles.variableText}>
                  {`Reps: ${item.exercise.reps}`}
                </Text>
              ) : null}
              {item.exercise.sets ? (
                <Text style={styles.variableText}>
                  {`Sets: ${item.exercise.sets}`}
                </Text>
              ) : null}
              {item.exercise.tempo ? (
                <Text style={styles.variableText}>
                  {`Tempo: ${item.exercise.tempo}`}
                </Text>
              ) : null}
              {item.exercise.rest ? (
                <Text style={styles.variableText}>
                  {`Rest: ${item.exercise.rest}`}
                </Text>
              ) : null}
            </View>
          </View>
        </View>
        {getRightView(item)}
      </TouchableOpacity>
      <View style={styles.divider} />
    </View>
  );

  const renderSectionHeader = ({ section }) => {
    if (section.data.length > 0) {
      return (
        <View style={styles.sectionHeaderContainer(isViewOnly)}>
          <View style={styles.sectionHeaderInnerView}>
            {!isViewOnly ? (
              <Image
                source={section.id === 0 ? progressionIcon : regressionIcon}
                style={styles.progressionRegressionIcon}
              />
            ) : null}
            <Text style={styles.sectionHeaderText(isViewOnly)}>
              {section.title}
            </Text>
          </View>
          {!isViewOnly ? (
            <Switch
              trackColor={{ true: colors.medYellow }}
              onValueChange={(isEnabled) => {
                onToggleChanged(section, isEnabled);
              }}
              value={section.isEnabled}
            />
          ) : null}
        </View>
      );
    }
    return null;
  };

  return (
    <View style={styles.parentCont}>
      <SectionList
        keyExtractor={(item, index) => item.id + index}
        sections={getSections()}
        renderItem={renderRow}
        renderSectionHeader={renderSectionHeader}
        initialNumToRender={200}
        maxToRenderPerBatch={200}
        updateCellsBatchingPeriod={75}
        onEndReachedThreshold={0.5}
        windowSize={51}
        ListHeaderComponent={headerComponent}
      />
    </View>
  );
};

ProgressionsRegressionsList.propTypes = propTypes;
ProgressionsRegressionsList.defaultProps = defaultProps;

export default memo(ProgressionsRegressionsList);

const styles = StyleSheet.create({
  parentCont: {
    backgroundColor: colors.white,
    marginBottom: 30,
  },
  container: {
    justifyContent: 'center',
  },
  cell: {
    flex: 1,
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    height: curvedScale(75),
    justifyContent: 'space-between',
    paddingHorizontal: '5%',
  },
  horizontalView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  thumbnail: {
    width: curvedScale(76),
    height: curvedScale(53),
    borderRadius: curvedScale(4),
  },
  arrow: {
    width: curvedScale(20),
    height: curvedScale(20),
  },
  checkView: {
    width: curvedScale(30),
    height: curvedScale(30),
    borderRadius: curvedScale(30) / 2,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.checkGreen,
  },
  checked: {
    width: curvedScale(10),
    height: curvedScale(10),
  },
  nameText: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(15),
    textAlign: 'left',
    width: curvedScale(195),
    marginLeft: scaleWidth(3),
    marginBottom: scaleHeight(0.5),
  },
  variableView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: scaleWidth(3),
  },
  variableText: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(11),
    marginLeft: scaleWidth(1),
    color: colors.subGrey,
  },
  timeText: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(11),
    marginRight: scaleWidth(2),
    color: colors.subGrey,
    textDecorationLine: 'underline',
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: colors.subGreyLight,
  },
  sectionHeaderContainer: (isLibraryContext) => ({
    backgroundColor: isLibraryContext ? colors.paleGray : colors.white,
    height: isLibraryContext ? 34 : 60,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: isLibraryContext ? 0 : 1,
    borderColor: colors.gray_1,
  }),
  sectionHeaderInnerView: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionHeaderText: (isLibraryContext) => ({
    fontFamily: 'Avenir',
    fontSize: isLibraryContext ? 13 : 17,
    fontWeight: isLibraryContext ? '600' : '500',
    color: isLibraryContext ? colors.subGrey : colors.black,
  }),
  progressionRegressionIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
});
