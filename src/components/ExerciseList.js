import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { v4 as uuidV4 } from 'uuid';

// Components
import {
  Alert,
  Text,
  Image,
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native';
import OptionsMenu from './OptionsMenu';
import {
  getTempoStringForExercise,
  getSideStringForExercise,
  getRestTempoStringForExercise,
} from '../util/programUtils';

// Styles
import { colors, shadow } from '../styles';

// Images
const addImage = require('../resources/addLightBlue.png');
const playIcon = require('../resources/buttonPlayLight.png');

// Props
const propTypes = {
  title: PropTypes.string,
  section: PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
    created_at: PropTypes.string,
    updated_at: PropTypes.string,
    exercises: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
        reps: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
        sets: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
        dur_seconds: PropTypes.number,
        muscle_groups: PropTypes.array,
      }),
    ),
  }).isRequired,
  sectionIndex: PropTypes.number,
  onPressAdd: PropTypes.func,
  onPressPlay: PropTypes.func,
  onEditExercise: PropTypes.func.isRequired,
  updateSection: PropTypes.func.isRequired,
  deleteExercise: PropTypes.func.isRequired,
};

const defaultProps = {
  title: 'Test',
  onPressAdd: () => Alert.alert('add exercise', 'need a callback here'),
  onPressPlay: () => Alert.alert('play exercise', 'need a callback here'),
};

class ExerciseList extends Component {
  onSelectExerciseOption = (value, exercise, index) => {
    switch (value) {
    case options.edit: {
      this.props.onEditExercise(exercise, index, this.props.sectionIndex);
      break;
    }
    case options.delete: {
      this.props.deleteExercise(exercise, index, this.props.sectionIndex);
      break;
    }
    case options.moveUp: {
      // test();
      this.moveExercise(index, index - 1);
      break;
    }
    case options.moveDown: {
      // test();
      this.moveExercise(index, index + 1);
      break;
    }
    default: {
      break;
    }
    }
  };

  getOptions = (index, array) => {
    const { moveUp, moveDown, ...rest } = options;
    if (array.length > 1) {
      if (index === 0) return { moveDown, ...rest };
      if (index + 1 === array.length) return { moveUp, ...rest };
      return options;
    }
    return { ...rest };
  };

  moveExercise = (indexFrom, indexTo) => {
    const { section } = this.props;
    const { exercises } = section;
    const movingExercise = exercises.splice(indexFrom, 1)[0];
    exercises.splice(indexTo, 0, movingExercise);
    const updatedExercises = exercises.map((exercise, index) => ({
      ...exercise,
      sequence: index + 1,
    }));
    const updatedSection = { ...section, exercises: updatedExercises };
    this.props.updateSection(updatedSection, this.props.sectionIndex);
  };

  renderExercises = () => this.props.section.exercises.map((exercise, index, array) => {
    if (!exercise.uniqueKeyForComponent) {
      exercise.uniqueKeyForComponent = uuidV4();
    }
    return (
      <View
        key={exercise.uniqueKeyForComponent}
        style={styles.exerciseContainer}
      >
        <TouchableOpacity
          style={styles.exerciseTouchable}
          onPress={() => this.props.onPressPlay(exercise)}
        >
          <Image source={playIcon} style={styles.playButton} />
          <View style={styles.exerciseDetailsContainer}>
            <Text style={styles.exercisesTitleText}>
              {`${exercise.name}`}
            </Text>
            <View style={styles.exerciseVariablesContainer}>
              {parseInt(exercise.sets, 10) > 0 && (
                <Text style={styles.exerciseDetailsText}>
                  {'SETS: '}
                  <Text style={styles.exerciseVariableValue}>
                    {exercise.sets}
                  </Text>
                </Text>
              )}
              {parseInt(exercise.reps, 10) > 0 && (
                <Text style={styles.exerciseDetailsText}>
                  {'REPS: '}
                  <Text style={styles.exerciseVariableValue}>
                    {exercise.reps}
                  </Text>
                </Text>
              )}
              {parseInt(exercise.dur_seconds, 10) > 0 && (
                <Text style={styles.exerciseDetailsText}>
                  {'DURATION: '}
                  <Text style={styles.exerciseVariableValue}>
                    {exercise.dur_seconds}
                  </Text>
                </Text>
              )}
              {!!exercise.tempo && (
                <Text style={styles.exerciseDetailsText}>
                  {'TEMPO: '}
                  <Text style={styles.exerciseVariableValue}>
                    {getTempoStringForExercise(exercise)}
                  </Text>
                </Text>
              )}
              {!!exercise.exercise_sides && (
                <Text style={styles.exerciseDetailsText}>
                  {'SIDE: '}
                  <Text style={styles.exerciseVariableValue}>
                    {getSideStringForExercise(exercise)}
                  </Text>
                </Text>
              )}
              {parseInt(exercise.weight, 10) > 0 && (
                <Text style={styles.exerciseDetailsText}>
                  {'WEIGHT: '}
                  <Text style={styles.exerciseVariableValue}>
                    {exercise.weight}
                  </Text>
                </Text>
              )}
              {!!exercise.rest && (
                <Text style={styles.exerciseDetailsText}>
                  {'REST: '}
                  <Text style={styles.exerciseVariableValue}>
                    {exercise.rest}
                  </Text>
                </Text>
              )}
              {!!exercise.rest_tempo && (
                <Text style={styles.exerciseDetailsText}>
                  {'REST TEMPO: '}
                  <Text style={styles.exerciseVariableValue}>
                    {getRestTempoStringForExercise(exercise)}
                  </Text>
                </Text>
              )}
            </View>
          </View>
        </TouchableOpacity>
        <OptionsMenu
          options={this.getOptions(index, array)}
          onSelect={(value) => this.onSelectExerciseOption(value, exercise, index)}
        />
      </View>
    );
  });

  render() {
    return (
      <View style={styles.cardStyle}>
        <View style={styles.titleContainer}>
          <View style={styles.titleTextContainer}>
            <Text style={styles.titleLabelText}>WORKOUT TYPE</Text>
            <Text style={styles.titleText}>
              {`${this.props.title}`.toUpperCase()}
            </Text>
          </View>
          <View style={styles.titleButtonContainer}>
            <TouchableOpacity
              onPress={() => {
                this.props.onPressAdd(
                  this.props.section,
                  this.props.sectionIndex,
                );
              }}
              style={styles.plusButton}
            >
              <Image source={addImage} />
            </TouchableOpacity>
          </View>
        </View>
        {/* Exercises */}
        {this.props.section.exercises && this.props.section.exercises.length > 0
          ? this.renderExercises()
          : null}
      </View>
    );
  }
}

// Export
ExerciseList.propTypes = propTypes;
ExerciseList.defaultProps = defaultProps;
export default ExerciseList;

// Styles
const styles = StyleSheet.create({
  cardStyle: {
    flex: 1,
    borderRadius: 3,
    backgroundColor: colors.white,
    ...shadow,
    marginTop: 20,
    overflow: 'hidden',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.nasmBlue,
    padding: 19,
  },
  titleTextContainer: {
    flex: 0.7,
  },
  titleLabelText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    textAlign: 'left',
    color: colors.white,
  },
  titleText: {
    fontFamily: 'Avenir',
    fontSize: 16,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.white,
  },
  titleButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flex: 0.3,
  },
  plusButton: {
    marginLeft: 20,
  },
  exerciseContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingRight: 10,
    paddingLeft: 16,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    borderColor: 'rgba(124, 128, 132, 0.1)',
    alignItems: 'center',
  },
  exerciseTouchable: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  playButton: {
    marginRight: 7,
  },
  exerciseDetailsContainer: {
    flex: 1,
  },
  exercisesTitleText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 16,
    textAlign: 'left',
    color: colors.black,
  },
  exerciseDetailsText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    textAlign: 'left',
    color: colors.subGrey,
    marginRight: 16,
  },
  exerciseVariableValue: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    textAlign: 'left',
    color: colors.black,
  },
  exerciseVariablesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});

// Menu Option
const options = {
  moveUp: 'Move Up',
  moveDown: 'Move Down',
  edit: 'Edit',
  delete: 'Delete',
};
