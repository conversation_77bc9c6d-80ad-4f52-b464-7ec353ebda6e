import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import moment from 'moment';

// Components
import {
  Dimensions, View, Text, TouchableOpacity, Alert,
} from 'react-native';
import LoadingSpinner from './LoadingSpinner';
import nasm from '../dataManager/apiConfig';

// Styles
import { colors } from '../styles';

const propTypes = {
  onPress: PropTypes.func.isRequired,
  getRef: PropTypes.func,
};

class WeeklyCompletion extends Component {
  constructor(props) {
    super(props);
    this.state = {
      workoutDays: {},
      isLoading: true,
      showingWorkoutActivity: this.props.showingWorkoutActivity,
    };
  }

  componentDidMount() {
    if (this.props.getRef) {
      this.props.getRef(this);
    }
    this.getWorkoutDays();
  }

  getTotal = () => {
    const workouts = Object.values(this.state.workoutDays);
    if (workouts.length === 0) {
      return '0/0';
    }
    let completeCount = 0;
    workouts.forEach((workoutDay) => {
      if (workoutDay.is_complete) {
        completeCount += 1;
      }
    });
    return `${completeCount}/${workouts.length}`;
  };

  getWorkoutDays = async () => {
    let workoutDays = [];
    try {
      const { id: clientId } = this.props.selectedClient;
      workoutDays = await nasm.api.getLatestWeeklyCompletion(
        clientId,
        moment().format(),
      );
      workoutDays = workoutDays.reduce((days, workoutDay) => {
        let { is_complete } = workoutDay;
        if (
          is_complete
          && days[workoutDay.workout_date]
          && days[workoutDay.workout_date].is_complete === false
        ) {
          is_complete = false;
        }
        return {
          ...days,
          [`${moment.utc(workoutDay.workout_date).weekday()}`]: {
            ...workoutDay,
            is_complete,
          },
        };
      }, {});

      this.setState({ workoutDays });
    } catch (error) {
      Alert.alert('Error retrieving schedule', error.message);
    } finally {
      this.setState({ isLoading: false });
    }
  };

  didFocus() {
    this.getWorkoutDays();
  }

  renderWeekday(weekday) {
    const containerStyle = [styles.weekDay];
    const textStyle = [styles.weekDayText];
    if (this.state.workoutDays[weekday]) {
      textStyle.push({ color: colors.white });
      const workoutDay = this.state.workoutDays[weekday];
      let backgroundColor = 'transparent';
      if (workoutDay.is_complete) {
        backgroundColor = colors.goodGreen;
      } else if (moment(workoutDay.workout_date).isBefore(moment(), 'day')) {
        backgroundColor = colors.macaroniAndCheese;
      } else {
        backgroundColor = colors.cloudyBlue;
      }
      containerStyle.push({ backgroundColor });
    }

    let text = '';
    switch (weekday) {
      case '0':
        text = 'S';
        break;
      case '1':
        text = 'M';
        break;
      case '2':
        text = 'T';
        break;
      case '3':
        text = 'W';
        break;
      case '4':
        text = 'T';
        break;
      case '5':
        text = 'F';
        break;
      case '6':
        text = 'S';
        break;

      default:
        text = 'S';
        break;
    }

    return (
      <View style={containerStyle}>
        <Text style={textStyle}>{text}</Text>
      </View>
    );
  }

  render() {
    return (
      <TouchableOpacity
        onPress={this.props.onPress}
        style={
          this.state.showingWorkoutActivity
            ? styles.activityContainer
            : styles.container
        }
      >
        <Text style={styles.title}>Weekly Workout Completion</Text>
        <View style={styles.body}>
          <View style={styles.weekContainer}>
            {this.renderWeekday('0')}
            {this.renderWeekday('1')}
            {this.renderWeekday('2')}
            {this.renderWeekday('3')}
            {this.renderWeekday('4')}
            {this.renderWeekday('5')}
            {this.renderWeekday('6')}
          </View>
          <View style={styles.totalContainer}>
            <Text style={styles.totalTitle}>Total</Text>
            <Text style={styles.total}>{this.getTotal()}</Text>
          </View>
        </View>
        <LoadingSpinner
          visible={this.state.isLoading}
          backgroundColor="rgba(0, 0, 0, 0.25)"
        />
      </TouchableOpacity>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  container: {
    backgroundColor: colors.white,
    borderRadius: 3 * scale,
    paddingVertical: 15 * scale,
    marginHorizontal: 20 * scale,
    marginTop: 20 * scale,
    borderWidth: 1,
    borderColor: 'rgba(182, 189, 195, 0.2)',
  },
  activityContainer: {
    backgroundColor: colors.white,
    paddingVertical: 15 * scale,
    marginHorizontal: 5 * scale,
    marginTop: 20 * scale,
  },
  title: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18 * scale,
    color: colors.black,
    marginLeft: 15 * scale,
  },
  body: {
    flexDirection: 'row',
  },
  weekContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 14 * scale,
    paddingTop: 12 * scale,
  },
  weekDay: {
    width: 24 * scale,
    height: 22.7 * scale,
    borderRadius: 12 * scale,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  weekDayText: {
    fontFamily: 'Avenir',
    fontSize: 12 * scale,
    fontWeight: '900',
    color: colors.subGrey,
  },
  totalContainer: {
    marginRight: 15 * scale,
    marginLeft: 30 * scale,
    justifyContent: 'space-between',
  },
  totalTitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10 * scale,
    color: colors.subGrey,
    textAlign: 'right',
  },
  total: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12 * scale,
    letterSpacing: 0.69 * scale,
    color: colors.black,
    textAlign: 'right',
  },
};

WeeklyCompletion.propTypes = propTypes;
const mapStateToProps = ({ selectedClient }) => ({ selectedClient });
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(WeeklyCompletion);
