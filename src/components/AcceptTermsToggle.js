import React from 'react';
import PropTypes from 'prop-types';

// Components
import { View, TouchableOpacity, Image } from 'react-native';
import ScaledText from './ScaledText';
import { curvedScale } from '../util/responsive';

// Styles
import { colors } from '../styles';

// Images
const checkedImage = require('../resources/checkmarkSelected.png');
const uncheckedImage = require('../resources/checkMarkGreyUnselected.png');

// PropTypes
const propTypes = {
  textStyle: PropTypes.object,
  linkStyle: PropTypes.object,
  navigation: PropTypes.any.isRequired,
  termsAccepted: PropTypes.bool.isRequired,
  onToggleChanged: PropTypes.func.isRequired,
  containerStyle: PropTypes.object,
};

const defaultProps = {
  textStyle: {},
  linkStyle: {},
  containerStyle: {},
};

function AcceptTermsToggle(props) {
  const checkMarkSource = props.termsAccepted ? checkedImage : uncheckedImage;
  const tintColor = !props.termsAccepted && props.textStyle.color
    ? props.textStyle.color
    : undefined;
  return (
    <View style={[styles.termsContainer, props.containerStyle]}>
      <TouchableOpacity
        onPress={() => props.onToggleChanged(!props.termsAccepted)}
      >
        <Image
          source={checkMarkSource}
          style={[styles.checkMark, { tintColor }]}
        />
      </TouchableOpacity>
      <ScaledText style={[styles.termsText, props.textStyle]}>
        {'Creating an account means you’re okay with our '}
        <ScaledText
          onPress={() => props.navigation.navigate('WebView', {
            title: 'NASM.ORG',
            uri:
                'https://www.nasm.org/policies/terms-and-conditions/nasm-app?mobile=1',
          })}
          style={[styles.urlText, props.linkStyle]}
        >
          Terms and Conditions and Privacy Policy
        </ScaledText>
      </ScaledText>
    </View>
  );
}

const styles = {
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 32,
  },
  termsText: {
    flex: 1,
    fontFamily: 'Avenir-Book',
    fontSize: 6,
    color: colors.subGrey,
  },
  urlText: {
    color: colors.azure,
  },
  checkMark: {
    width: curvedScale(36),
    height: curvedScale(36),
    marginLeft: 8,
    marginRight: 24,
  },
};

AcceptTermsToggle.propTypes = propTypes;
AcceptTermsToggle.defaultProps = defaultProps;
export default AcceptTermsToggle;
