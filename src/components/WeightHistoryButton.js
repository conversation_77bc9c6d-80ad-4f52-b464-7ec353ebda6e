import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { Text, TouchableOpacity, StyleSheet } from 'react-native';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  onPress: PropTypes.func.isRequired,
  id: PropTypes.string,
  selected: PropTypes.bool,
  buttonText: PropTypes.string,
};

const defaultProps = {
  id: 'default',
  selected: false,
  containerStyle: null,
  buttonStyle: null,
  textStyles: null,
  buttonText: '',
};

// Component definition
export default class WeightHistoryButton extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  onPress() {
    this.props.onPress(this.props.id);
  }

  render() {
    return (
      <TouchableOpacity
        style={this.props.selected ? styles.selectedStyle : styles.unselectedStyle}
        onPress={() => this.onPress()}
      >
        <Text
          style={
            this.props.selected ? styles.selectedTextSyle : styles.textStyle
          }
        >
          {this.props.buttonText}
        </Text>
      </TouchableOpacity>
    );
  }
}

// Export
WeightHistoryButton.propTypes = propTypes;
WeightHistoryButton.defaultProps = defaultProps;

const styles = StyleSheet.create({
  selectedStyle: {
    width: 70,
    height: 31,
    borderRadius: 16,
    justifyContent: 'center',
    backgroundColor: colors.nasmBlue,
  },
  unselectedStyle: {
    width: 70,
    height: 31,
    borderRadius: 16,
    borderStyle: 'solid',
    borderWidth: 1,
    justifyContent: 'center',
    borderColor: 'rgba(150, 150, 150, 1)',
  },
  textStyle: {
    fontSize: 12,
    fontWeight: 'bold',
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'center',
    color: colors.nasmBlue,
  },
  selectedTextSyle: {
    fontSize: 12,
    fontWeight: 'bold',
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'center',
    color: colors.white,
  },
});
