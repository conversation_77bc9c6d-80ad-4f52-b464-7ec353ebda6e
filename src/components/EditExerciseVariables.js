import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  KeyboardAvoidingView,
  StyleSheet,
  Text,
  TextInput,
  View,
  TouchableHighlight,
  Platform,
  ScrollView,
} from 'react-native';
import { getTempoStringForExercise } from '../util/programUtils';
import { capitalizeString } from '../constants';

// Styles
import { colors, shadow } from '../styles';
import { removeAllSpecialCharacters } from '../util/validate';

// Props
const propTypes = {
  visible: PropTypes.bool,
  editingData: PropTypes.shape({
    title: PropTypes.string,
    exercise: PropTypes.object,
    variables: PropTypes.shape({
      reps: PropTypes.number,
      sets: PropTypes.number,
      duration: PropTypes.number,
      rest: PropTypes.number,
      tempo: PropTypes.number,
    }),
    exerciseIndex: PropTypes.number,
    sectionIndex: PropTypes.number,
  }),
  onPressCancel: PropTypes.func.isRequired,
  onPressUpdate: PropTypes.func.isRequired,
};

const defaultProps = {
  visible: false,
  editingData: {},
};

// Main Class
class EditExerciseVariables extends Component {
  constructor(props) {
    super(props);
    const editingData = this.props.editingData || { variables: null };
    this.state = {
      variables: editingData.variables,
    };
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.editingData
      && prevProps.editingData !== this.props.editingData
    ) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({ variables: this.props.editingData.variables });
    }
  }

  onInputBlur = (key) => {
    const value = this.state.variables[key];
    if (!parseInt(value, 10)) {
      this.setState((prevState) => ({
        variables: {
          ...prevState.variables,
          [key]: 1,
        },
      }));
    }
  };

  onPressUpdate = () => {
    const updateData = {
      ...this.props.editingData,
      variables: this.state.variables,
    };
    this.props.onPressUpdate(updateData);
  };

  getTitle = () => this.props.editingData.title;

  updateTempoVariable = (toRight, variables) => {
    let { tempo } = variables;
    if (toRight) {
      tempo += 1;
      if (tempo > 4) {
        tempo = 1;
      }
    } else {
      tempo -= 1;
      if (tempo < 1) {
        tempo = 4;
      }
    }
    // update the state if all conditions have passed
    this.setState((prevState) => ({
      variables: {
        ...prevState.variables,
        tempo,
      },
    }));
    return true;
  };

  updateVariable = (key, value) => {
    // must be a positive number
    if (value !== '' && value < 1) return false;
    // don't allow non-numeric values
    const stripCharacters = `${value}`.replace(/\D/g, '');
    // make sure the next value is a number
    const nextValue = stripCharacters === '' ? '' : parseInt(stripCharacters, 10);
    if (Number.isNaN(nextValue)) return false;
    // update the state if all conditions have passed
    this.setState((prevState) => ({
      variables: {
        ...prevState.variables,
        [key]: nextValue,
      },
    }));
    return true;
  };

  renderRegularVariable(key, variables) {
    let measurementUnit = '';
    if (key === 'rest' || key === 'dur_seconds') {
      measurementUnit = 's';
    }
    return (
      <View key={key} style={styles.inputContainer}>
        <Text style={styles.inputTitle}>
          {key === 'dur_seconds' ? 'Duration' : capitalizeString(key)}
        </Text>
        <View style={styles.inputRow}>
          <TouchableHighlight
            style={styles.inputButton}
            onPress={() => this.updateVariable(key, variables[key] - 1)}
            underlayColor="rgba(0, 0, 0, 0.1)"
          >
            <Text style={styles.inputButtonText}>-</Text>
          </TouchableHighlight>
          <TextInput
            value={`${variables[key]}${measurementUnit}`}
            onChangeText={(value) => {
              const filteredValue = removeAllSpecialCharacters(value);
              this.updateVariable(key, filteredValue);
            }}
            onBlur={() => this.onInputBlur(key)}
            style={styles.textInput}
            keyboardType="numeric"
            underlineColorAndroid="transparent"
            selectionColor={colors.nasmBlue}
          />
          <TouchableHighlight
            style={styles.inputButton}
            onPress={() => this.updateVariable(key, variables[key] + 1)}
            underlayColor="rgba(0, 0, 0, 0.1)"
          >
            <Text style={styles.inputButtonText}>+</Text>
          </TouchableHighlight>
        </View>
      </View>
    );
  }

  renderTempoInputVariable(key, variables) {
    return (
      <View key={key} style={styles.inputContainer}>
        <Text style={styles.inputTitle}>{capitalizeString(key)}</Text>
        <View style={styles.inputRow}>
          <TouchableHighlight
            style={styles.inputButton}
            onPress={() => this.updateTempoVariable(false, variables)}
            underlayColor="rgba(0, 0, 0, 0.1)"
          >
            <Text style={styles.inputButtonText}>{'<'}</Text>
          </TouchableHighlight>
          <TextInput
            editable={false}
            value={`${getTempoStringForExercise(variables)}`}
            onBlur={() => this.onInputBlur(key)}
            style={styles.textInput}
            keyboardType="numeric"
            underlineColorAndroid="transparent"
          />
          <TouchableHighlight
            style={styles.inputButton}
            onPress={() => this.updateTempoVariable(true, variables)}
            underlayColor="rgba(0, 0, 0, 0.1)"
          >
            <Text style={styles.inputButtonText}>{'>'}</Text>
          </TouchableHighlight>
        </View>
      </View>
    );
  }

  render() {
    const { variables } = this.state;
    return (
      this.props.visible
      && this.props.editingData && (
        <KeyboardAvoidingView
          style={styles.container}
          behavior={Platform.OS === 'ios' ? 'padding' : null}
        >
          <View style={styles.card}>
            <Text style={styles.titleText}>{this.getTitle()}</Text>
            <ScrollView>
              {/* Variables */}
              {Object.keys(variables).map((key) => {
                if (key === 'tempo') {
                  return this.renderTempoInputVariable(key, variables);
                }
                return this.renderRegularVariable(key, variables);
              })}
            </ScrollView>
            {/* Buttons */}
            <View style={styles.buttonContainer}>
              <TouchableHighlight
                style={[
                  styles.button,
                  {
                    borderRightColor: 'rgba(0, 0, 0, 0.1)',
                    borderRightWidth: 1,
                  },
                ]}
                onPress={this.props.onPressCancel}
                underlayColor="rgba(0, 0, 0, 0.1)"
              >
                <Text style={styles.buttonText}>CANCEL</Text>
              </TouchableHighlight>
              <TouchableHighlight
                style={styles.button}
                onPress={this.onPressUpdate}
                underlayColor="rgba(0, 0, 0, 0.1)"
              >
                <Text style={styles.buttonText}>UPDATE</Text>
              </TouchableHighlight>
            </View>
          </View>
        </KeyboardAvoidingView>
      )
    );
  }
}

// Exports
EditExerciseVariables.propTypes = propTypes;
EditExerciseVariables.defaultProps = defaultProps;
export default EditExerciseVariables;

// Styles
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
  },
  card: {
    borderRadius: 3,
    marginHorizontal: 47,
    // padding: 20,
    backgroundColor: colors.white,
    ...shadow,
  },
  titleText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 19,
    textAlign: 'left',
    color: colors.black,
    marginTop: 20,
    marginHorizontal: 20,
    marginBottom: 10,
  },
  inputContainer: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(124, 128, 132, 0.1)',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputTitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.black,
    textAlign: 'center',
    marginBottom: 5,
  },
  inputButtonText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.nasmBlue,
  },
  inputButton: {
    padding: 10,
    flex: 0.2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textInput: {
    flex: 0.6,
    textAlign: 'center',
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.black,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    padding: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.nasmBlue,
  },
});
