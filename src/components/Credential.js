import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import {
  Image, StyleSheet, TouchableOpacity, View,
} from 'react-native';
import ScaledText from './ScaledText';

// Styles
import { colors } from '../styles';

// Constants
import { CERT_ICONS, CERT_URLS } from '../constants';

const infoIcon = require('../resources/info.png');

// Helpers
function getIcon(name, unlocked) {
  const cert = CERT_ICONS.find((c) => c.name === name);
  if (cert) {
    if (unlocked) {
      return cert.icon;
    }
    return cert.lockedIcon;
  }
  return null;
}

function getUrl(name) {
  const cert = CERT_URLS.find((c) => c.name === name);
  return cert ? cert.url : null;
}

// PropTypes
const propTypes = {
  onPress: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  date: PropTypes.string,
  unlocked: PropTypes.bool.isRequired,
  wide: PropTypes.bool,
};

const defaultProps = {
  date: null,
  wide: false,
};

function Credential(props) {
  const { unlocked, date } = props;
  const momentDate = moment(date, 'YYYY/MM/DD');
  const dateString = momentDate.format('M/D/YY');
  const expired = momentDate.isBefore(moment(), 'day');
  return (
    <View
      style={
        props.wide ? styles.wideCredentialContainer : styles.credentialContainer
      }
    >
      <Image source={getIcon(props.name, unlocked)} />
      {props.wide && (
        <TouchableOpacity onPress={() => props.onPress(getUrl(props.name))}>
          <Image
            style={{ tintColor: colors.subGrey, marginLeft: 10 }}
            source={infoIcon}
          />
        </TouchableOpacity>
      )}
      <View style={props.wide ? { marginLeft: 15, flex: 1 } : {}}>
        {(props.date || !props.wide) && (
          <ScaledText
            style={[
              styles.dateText,
              expired && {
                color: colors.nasmRed,
              },
            ]}
          >
            {props.date ? `Expire${expired ? 'd' : 's'}: ${dateString}` : ''}
          </ScaledText>
        )}
        <ScaledText
          style={[
            styles.titleText,
            { color: unlocked ? colors.black : colors.subGrey },
          ]}
        >
          {props.title}
        </ScaledText>
      </View>
      {!props.wide && (
        <TouchableOpacity
          disabled={unlocked}
          style={[styles.button, unlocked && styles.buttonUnlocked]}
          onPress={() => props.onPress(getUrl(props.name))}
        >
          <ScaledText
            style={[styles.buttonText, unlocked && styles.buttonTextUnlocked]}
          >
            {unlocked ? 'COMPLETE' : 'MORE INFO'}
          </ScaledText>
        </TouchableOpacity>
      )}
    </View>
  );
}

// Export
Credential.propTypes = propTypes;
Credential.defaultProps = defaultProps;
export default Credential;

// Styles
const styles = StyleSheet.create({
  credentialContainer: {
    marginTop: 30,
    marginHorizontal: 15,
    height: 200,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  wideCredentialContainer: {
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    color: colors.subGrey,
  },
  titleText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.black,
  },
  button: {
    height: 32,
    borderRadius: 32 / 2,
    borderWidth: 1,
    borderColor: colors.subGrey,
    padding: 8,
    width: '100%',
    marginTop: 16,
  },
  buttonUnlocked: {
    backgroundColor: colors.goodGreen,
    borderWidth: 0,
  },
  buttonText: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.goodGreen,
  },
  buttonTextUnlocked: {
    color: colors.white,
  },
});
