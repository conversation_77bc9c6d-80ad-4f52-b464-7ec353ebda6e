import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Alert,
  Image,
  LayoutAnimation,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Platform,
  SectionList,
} from 'react-native';
import analytics from '@react-native-firebase/analytics';
import { connect } from 'react-redux';
import nasm from '../dataManager/apiConfig';
import { androidSafeLayoutAnimation, EXERCISE_CONTEXTS } from '../constants';

// Components

import HeaderSearchBar from './HeaderSearchBar';
import LoadingSpinner from './LoadingSpinner';
import FloatingButton from './FloatingButton';
import FilterIcon from './FilterIcon';
import Swipeable from './Swipeable';

// Styles
import { colors } from '../styles';

// Redux
import {
  createNewProgram,
  duplicateProgram,
} from '../reducers/selectedProgramReducer';

const mapState = () => ({});
const mapDispatch = { createNewProgram, duplicateProgram };

const deleteIcon = require('../resources/btnDelete.png');
const duplicateIcon = require('../resources/btnDuplicate.png');
const rightArrow = require('../resources/imgRightArrowGray.png');

const propTypes = {
  emptyStateMessage: PropTypes.string,
  loadingSpinnerMessage: PropTypes.string,
  renderFloatingButton: PropTypes.bool,
  navigation: PropTypes.object.isRequired,
  didSelectProgram: PropTypes.func,
  searchText: PropTypes.string,
  onChangeSearchText: PropTypes.func,
  onRefresh: PropTypes.func,
  isLoading: PropTypes.boolean,
};
const defaultProps = {
  emptyStateMessage: 'Build a Program by\nselecting + below.',
  loadingSpinnerMessage: 'Loading...',
  pageSize: 50,
  renderFloatingButton: false,
  deleteProgram: undefined,
  didSelectProgram: undefined,
  searchText: '',
  onChangeSearchText: () => {},
  onRefresh: () => {},
  isLoading: false,
};

class SearchableProgramListView extends Component {
  constructor(props) {
    super(props);
    props.navigation.setOptions({ headerTitle: 'Choose Program' });
    this.state = {
      programs: [],
      filterOptions: {},
      swiping: false,
      loadingTitle: this.props.loadingSpinnerMessage,
    };
    this.swipeableCells = {};
    this.saveCategories();
  }

  onPressDeleteProgram(program) {
    this.recenterSwipableCell(program);
    const programIndex = this.props.programs.indexOf(program);
    if (programIndex !== -1) {
      Alert.alert(
        'Delete Program',
        `Are you sure you would like to delete '${program.name}'?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => this.deleteProgramAtIndex(programIndex),
          },
        ],
      );
    }
  }

  onPressDuplicateProgram = (program) => {
    this.recenterSwipableCell(program);
    const alertText = program.workout_count > 1
      ? `${program.workout_count} workouts`
      : '1 workout';
    Alert.alert(
      'Copy Program',
      `Copying this program will add ${alertText} to your library`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'OK',
          onPress: () => {
            this.props
              .duplicateProgram(program.id)
              .then((savedProgram) => {
                this.addNewProgram(savedProgram);
                this.props.navigation.navigate('EditProgram', {
                  exerciseContext: EXERCISE_CONTEXTS.WORKOUT_SECTION_EXERCISE,
                });
              })
              .catch((error) => {
                Alert.alert(
                  'Error',
                  error.message || 'Unable to duplicate program.',
                  [{ text: 'OK', style: 'cancel' }],
                );
              });
          },
        },
      ],
    );
  };

  onPressFilter = () => {
    if (this.state.filterOptions) {
      this.props.navigation.navigate('FilterModal', {
        title: 'Filter Programs',
        filters: this.state.filterOptions,
        selectedFilters: this.props.selectedFilters,
        updateFilters: this.updateFilters,
      });
    }
  };

  onProgramSelected = (program) => {
    if (this.props.didSelectProgram !== undefined) {
      this.props.didSelectProgram(program);
    }
  };

  getActiveFilterCount = () => {
    let count = 0;
    if (this.props.selectedFilters.categories) {
      count += this.props.selectedFilters.categories.length;
    }
    if (this.props.selectedFilters.programAuthor) {
      count += this.props.selectedFilters.programAuthor.length;
    }
    return count;
  };

  getSections() {
    const sectionList = [];
    const { programs } = this.props;
    const nasmPrograms = programs.filter((program) => program.owner_id === null);
    const myPrograms = programs.filter((program) => program.owner_id !== null);

    if (myPrograms.length > 0) {
      sectionList.push({
        title: 'My Programs',
        data: myPrograms,
      });
    }
    if (nasmPrograms.length > 0) {
      sectionList.push({
        title: 'NASM Programs',
        data: nasmPrograms,
      });
    }
    return sectionList;
  }

  getSwipeButtonsForProgram = (program) => {
    const buttons = [
      <TouchableOpacity
        style={styles.duplicateButton}
        onPress={() => this.onPressDuplicateProgram(program)}
        key="duplicate"
      >
        <View style={styles.swipeButtonContainer}>
          <Image source={duplicateIcon} />
          <Text style={styles.swipeButtonText}>Copy</Text>
        </View>
      </TouchableOpacity>,
    ];
    if (program.owner_id && program.owner_id.length) {
      buttons.push(
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => this.onPressDeleteProgram(program)}
          key="delete"
        >
          <View style={styles.swipeButtonContainer}>
            <Image source={deleteIcon} />
            <Text style={styles.swipeButtonText}>Delete</Text>
          </View>
        </TouchableOpacity>,
      );
    }
    return buttons;
  };

  addNewProgram = (newProgram) => {
    if (newProgram && newProgram.id) {
      const { programs } = this.state;
      programs.unshift(newProgram);
      LayoutAnimation.configureNext(androidSafeLayoutAnimation);
      this.setState({ programs });
      this.refreshPrograms();
    }
  };

  navigateToAddPrograms = () => {
    const params = {
      onProgramSaved: this.addNewProgram,
      exerciseContext: EXERCISE_CONTEXTS.WORKOUT_SECTION_EXERCISE,
    };
    this.props.createNewProgram();
    this.props.navigation.navigate('EditProgram', params);
  };

  recenterSwipableCell = (program) => {
    if (this.swipeableCells) {
      if (program && this.swipeableCells[program.id]) {
        this.swipeableCells[program.id].recenter();
      } else {
        Object.values(this.swipeableCells).forEach((cell) => {
          if (cell !== null) {
            cell.recenter();
          }
        });
      }
    }
  };

  refreshPrograms = () => {
    this.setState(
      {
        nextPage: 1,
        refreshing: true,
      },
      () => {
        this.props.getPrograms(true, this.props.selectedFilters);
      },
    );
  };

  replaceProgram = (newProgram) => {
    if (newProgram) {
      const { programs } = this.props;
      const indexToBeUpdated = programs.findIndex(
        (program) => program.id === newProgram.id,
      );
      if (indexToBeUpdated !== -1) {
        if (!newProgram.workout_count) {
          newProgram.workout_count = (newProgram.workouts || []).length;
        }
        programs.splice(indexToBeUpdated, 1, newProgram);
      }
      LayoutAnimation.configureNext(androidSafeLayoutAnimation);
      this.setState({ programs });
    }
  };

  showError = (error) => {
    this.setState(
      {
        isGettingPrograms: false,
        refreshing: false,
      },
      () => {
        Alert.alert(
          'Error',
          error.message || 'Internal server error occurred.',
        );
      },
    );
  };

  updateFilters = (selectedFilters) => {
    const selectedOwnership = selectedFilters.programAuthor;
    const selectedCategories = selectedFilters.categories;

    this.setState({
      selectedCategories,
      selectedOwnership,
    });
    this.props.onFiltersChanged(selectedFilters);
  };

  deleteProgramAtIndex(index) {
    if (index >= 0 && index < this.props.programs.length) {
      const { programs } = this.props;
      const program = programs[index];
      nasm.api
        .deleteProgram(program.id)
        .then(() => {
          programs.splice(index, 1);
          LayoutAnimation.configureNext(androidSafeLayoutAnimation);
          this.setState({ programs });
          analytics().logEvent('delete_custom_program', {
            program_id: program.id,
            program_name: program.name,
            program_category: program.program_category.label,
            number_of_workouts: program.workout_count,
          });
        })
        .catch(this.showError);
    }
  }

  saveCategories() {
    return nasm.api
      .getProgramCategories()
      .then((categories) => {
        // Creating filter sections for selecting/finding programs
        const filterOptions = {};
        // Filter through either the user's own programs or all programs
        filterOptions.programAuthor = {
          label: 'Program Author',
          filters: [
            { id: 'trainerOwned', name: 'My Programs' },
            { id: 'nasmOwned', name: 'NASM Programs' },
          ],
          singleSelect: true,
        };
        // Filter through programs that fit under a certain category
        filterOptions.categories = {
          label: 'Program Category',
          filters: categories,
        };

        const sections = Object.keys(filterOptions);
        const { selectedFilters } = this.props;
        for (let i = 0; i < sections.length; i += 1) {
          selectedFilters[sections[i]] = [];
        }
        return this.setState({ filterOptions, selectedFilters });
      })
      .catch(this.showError);
  }

  renderEmptyStateView() {
    let view;
    const hasFiltersEnabled = (this.props.searchText && this.props.searchText.length > 0)
      || this.props.selectedFilters.length > 0;
    if (hasFiltersEnabled) {
      view = (
        <View style={{ width: '65%' }}>
          <Image
            style={styles.emptyStateImage}
            source={require('../resources/magnifyinglassLarge.png')}
          />
          <Text style={styles.emptySearchStateTitleText}>NO RESULTS FOUND</Text>
          <Text style={styles.emptySearchStateSubTitleText}>
            If you have filters active, it might help to deactivate them.
          </Text>
        </View>
      );
    } else {
      view = (
        <View>
          <Text style={styles.emptyStateText}>
            {this.props.emptyStateMessage}
          </Text>
        </View>
      );
    }
    return (
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={styles.emptyStateContainer}
      >
        {view}
        <LoadingSpinner
          backgroundColor={colors.background}
          color={colors.subGrey}
          titleTextStyle={{ color: colors.subGrey }}
          visible={this.props.isLoading}
          title={this.state.loadingTitle}
        />
      </ScrollView>
    );
  }

  renderFilterButton = () => (
    <FilterIcon
      onPressFilter={this.onPressFilter}
      badgeCount={this.getActiveFilterCount()}
    />
  );

  renderFlatList() {
    if (this.props.programs.length === 0) {
      return this.renderEmptyStateView();
    }
    return (
      <SectionList
        testID="SectionListView"
        style={styles.list}
        contentContainerStyle={{
          paddingBottom: Platform.OS === 'ios' ? 100 : 110,
        }}
        data={this.props.programs}
        sections={this.getSections()}
        renderItem={this.renderProgram}
        renderSectionHeader={this.renderSectionHeader}
        keyExtractor={(item) => item.id}
        onEndReachedThreshold={0.7}
        onRefresh={this.props.onRefresh}
        refreshing={this.props.isLoading}
        onEndReached={() => this.props.getPrograms(false, this.props.selectedFilters)}
        onScrollBeginDrag={() => {
          this.setState({ scrolling: true });
        }}
        onScrollEndDrag={() => {
          this.setState({ scrolling: false });
        }}
        scrollEnabled={!this.state.swiping}
        stickySectionHeadersEnabled
      />
    );
  }

  renderFloatingButton() {
    if (this.props.renderFloatingButton) {
      return <FloatingButton onPress={this.navigateToAddPrograms} />;
    }
    return null;
  }

  renderProgram = ({ item }) => (
    <Swipeable
      ref={(ref) => {
        this.swipeableCells[item.id] = ref;
      }}
      rightButtons={this.getSwipeButtonsForProgram(item)}
      onSwipeStart={() => {
        this.setState({ swiping: true });
      }}
      onSwipeRelease={() => {
        this.setState({ swiping: false });
      }}
    >
      <TouchableOpacity
        onPress={() => {
          this.onProgramSelected(item);
        }}
        style={styles.programContainer}
      >
        <View style={styles.programTextContainer}>
          <Text style={styles.programName}>{item.name}</Text>
          <View style={styles.programInfoContainer}>
            <Text style={styles.programInfo}>
              {item.program_category.label}
            </Text>
            <Text style={styles.programInfo}>
              {item.workout_count}
              {' '}
              workout
              {item.workout_count === 1 ? '' : 's'}
            </Text>
          </View>
        </View>
        <View style={styles.arrow}>
          <Image style={{ height: 18, width: 18 }} source={rightArrow} />
        </View>
      </TouchableOpacity>
    </Swipeable>
  );

  renderSectionHeader = ({ section: { title } }) => (
    <View style={styles.sectionHeaderContainer}>
      <Text style={styles.sectionHeaderText}>{title}</Text>
    </View>
  );

  renderTopBar() {
    return (
      <HeaderSearchBar
        searchText={this.props.searchText}
        onChangeText={this.props.onChangeSearchText}
        clearable
        renderActionButton={this.renderFilterButton}
        paddingTop={10}
        light
        shadow={false}
        placeholder="Search for a Program"
      />
    );
  }

  render() {
    return (
      <SafeAreaView style={styles.container}>
        {this.renderTopBar()}
        {this.renderFlatList()}
        {this.renderFloatingButton()}
      </SafeAreaView>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 40,
  },
  emptyStateText: {
    fontFamily: 'Avenir-Light',
    fontSize: 23,
    lineHeight: 32,
    textAlign: 'center',
    color: colors.cloudyBlue,
  },
  emptyStateImage: {
    alignSelf: 'center',
    width: 50,
    height: 50,
  },
  emptySearchStateTitleText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 14,
    color: colors.subGrey,
    textAlign: 'center',
    alignSelf: 'center',
    marginTop: 8,
  },
  emptySearchStateSubTitleText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: colors.subGrey,
    textAlign: 'center',
    alignSelf: 'center',
    marginTop: 10,
  },
  sectionHeaderContainer: {
    backgroundColor: colors.paleGray,
    height: 34,
    paddingHorizontal: 16,
    justifyContent: 'center',
  },
  sectionHeaderText: {
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '600',
    color: colors.subGrey,
  },
  list: {
    backgroundColor: colors.white,
    paddingBottom: 15,
  },
  programContainer: {
    flexDirection: 'row',
    paddingVertical: 13,
    paddingHorizontal: 26,
    borderColor: colors.subGreyLight,
    borderBottomWidth: 1,
  },
  programTextContainer: {
    paddingRight: 5,
    flex: 1,
  },
  programName: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    color: colors.black,
  },
  programInfoContainer: {
    flexDirection: 'row',
  },
  programInfo: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    color: colors.subGreyTwo,
    paddingRight: 8,
  },
  deleteButton: {
    backgroundColor: colors.dustyRed,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  duplicateButton: {
    backgroundColor: colors.medYellow,
    flex: 1,
  },
  swipeButtonContainer: {
    flex: 1,
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  swipeButtonText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
    color: colors.white,
    marginTop: 3,
  },
  arrow: {
    alignItems: 'center',
    justifyContent: 'center',
  },
};

SearchableProgramListView.propTypes = propTypes;
SearchableProgramListView.defaultProps = defaultProps;
export default connect(mapState, mapDispatch, null, { forwardRef: true })(
  SearchableProgramListView,
);
