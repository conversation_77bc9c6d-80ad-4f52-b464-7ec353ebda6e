import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Alert,
  Image,
  StyleSheet,
  TouchableHighlight,
  TouchableOpacity,
  Text,
} from 'react-native';
import Menu, { MenuOptions, MenuOption, MenuTrigger } from 'react-native-menu';

// Styles
import { colors } from '../styles';

const triggerTouchable = (testID) => <TouchableOpacity testID={testID} />;
const optionTouchable = (testID) => (
  <TouchableHighlight
    underlayColor="black"
    activeOpacity={0.8}
    testID={testID}
  />
);

// Images
const optionsIcon = require('../resources/gray.png');

// Props
const propTypes = {
  options: PropTypes.objectOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  ),
  onSelect: PropTypes.func,
  children: PropTypes.node,
  testID: PropTypes.string,
  showCaps: PropTypes.bool,
  optionStyle: PropTypes.object,
  backgroundColor: PropTypes.any,
  menuContainerTopMargin: PropTypes.any,
};

const defaultProps = {
  options: {
    one: 'One',
    two: 'Two',
    three: 'Three',
  },
  onSelect: (value) => {
    Alert.alert(`Selected: ${value}`, 'add a callback here');
  },
  children: null,
  testID: null,
  showCaps: true,
  optionStyle: null,
  backgroundColor: colors.rustyRed,
  menuContainerTopMargin: 0,
};

// Main Class
class OptionsMenu extends PureComponent {
  render() {
    const {
      options,
      showCaps,
      optionStyle,
      backgroundColor,
      menuContainerTopMargin,
    } = this.props;
    return (
      <Menu onSelect={this.props.onSelect} testID={this.props.testID}>
        <MenuTrigger
          renderTouchable={() => triggerTouchable(`${this.props.testID}Trigger`)}
        >
          {this.props.children}
          {!this.props.children && <Image source={optionsIcon} />}
        </MenuTrigger>
        <MenuOptions
          optionsContainerStyle={styles.menuContainer(
            backgroundColor,
            menuContainerTopMargin,
          )}
        >
          {Object.keys(options).map((key) => (
            <MenuOption
              key={key}
              value={options[key]}
              renderTouchable={() => optionTouchable(`${this.props.testID}Item${options[key]}`)}
              style={styles.menuOption(backgroundColor)}
            >
              <Text style={[styles.menuOptionText, optionStyle]}>
                {showCaps ? `${options[key]}`.toUpperCase() : `${options[key]}`}
              </Text>
            </MenuOption>
          ))}
        </MenuOptions>
      </Menu>
    );
  }
}

// Exports
OptionsMenu.propTypes = propTypes;
OptionsMenu.defaultProps = defaultProps;
export default OptionsMenu;

// Styles
const styles = StyleSheet.create({
  menuContainer: (backgroundColor, menuContainerTopMargin) => ({
    backgroundColor,
    marginTop: menuContainerTopMargin,
    marginRight: 1,
  }),
  menuOption: (backgroundColor) => ({
    backgroundColor,
    padding: 15,
    borderBottomWidth: 1,
    borderColor: colors.textPlaceholder,
  }),
  menuOptionText: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.white,
  },
});
