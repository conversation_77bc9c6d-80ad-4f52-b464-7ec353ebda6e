import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { View, Dimensions } from 'react-native';
import ScaledText from './ScaledText';

// Styles
import { colors } from '../styles';

const propTypes = {
  title: PropTypes.string.isRequired,
  backgroundColor: PropTypes.string,
  textColor: PropTypes.string,
};

const defaultProps = {
  backgroundColor: 'rgba(241, 243, 244, 1)',
  textColor: colors.black,
};

class HoverHeader extends Component {
  render() {
    const { backgroundColor, title, textColor } = this.props;
    return (
      <View style={[{ backgroundColor }, styles.container]}>
        <ScaledText style={{ ...styles.text, color: textColor }}>
          {title}
        </ScaledText>
      </View>
    );
  }
}

const { width } = Dimensions.get('window');

const styles = {
  container: {
    width,
    paddingVertical: 20,
  },
  text: {
    fontSize: 16,
    marginHorizontal: '5%',
  },
};

HoverHeader.propTypes = propTypes;
HoverHeader.defaultProps = defaultProps;
export default HoverHeader;
