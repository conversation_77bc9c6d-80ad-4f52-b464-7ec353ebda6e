import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';

// Components
import {
  View, Image, TouchableOpacity, StyleSheet,
} from 'react-native';
import { curvedScale, scaleWidth } from '../util/responsive';

// Styles
import { colors } from '../styles';
import ScaledText from './ScaledText';
import getExerciseName from '../util/exerciseUtils';
import ROUTINE_TYPES from '../types/RoutineTypes';
import { EXERCISE_TYPE, ROLES } from '../constants';

// Images
const backgroundImage = require('../resources/imgExerciseBlockDefault.png');
const rightArrow = require('../resources/imgRightArrowGray.png');

const selectedIcon = require('../resources/checkboxSquare.png');
const unselectedIcon = require('../resources/checkboxSquareUnchecked.png');

const favoriteIcon = require('../assets/favorite.png');
const unfavoriteIcon = require('../assets/unfavorite.png');

const progressionIcon = require('../assets/progressionIcon.png');
const regressionIcon = require('../assets/regressionIcon.png');

// PropTypes
const propTypes = {
  exercise: PropTypes.object.isRequired,
  currentUser: PropTypes.object.isRequired,
  onPressExercise: PropTypes.func.isRequired,
  isSelected: PropTypes.bool,
  showsSelectionImage: PropTypes.bool,
  showsFavoriteImage: PropTypes.bool,
  isFavorite: PropTypes.bool,
  onPressFavoriteIcon: PropTypes.func,
};

const defaultProps = {
  showsSelectionImage: false,
  isSelected: false,
  showsFavoriteImage: false,
  isFavorite: false,
  onPressFavoriteIcon: null,
};

class ExerciseCell extends Component {
  static getImageSource(exercise) {
    const uri = exercise?.exercise_media?.thumbnail_url
      ? exercise.exercise_media.thumbnail_url
      : exercise.image_url;
    if (uri) return { uri };
    return backgroundImage;
  }

  renderSelectionIconImage() {
    if (this.props.showsSelectionImage) {
      return (
        <Image
          style={styles.selectionImage}
          source={this.props.isSelected ? selectedIcon : unselectedIcon}
          pointerEvents="none"
        />
      );
    }
    return null;
  }

  renderProgressionRegressionIconImage = (exercise) => {
    const hasProgression = exercise?.progressions_regressions?.some(
      (ex) => ex.exerciseType === EXERCISE_TYPE.PROGRESSION,
    );
    const hasRegression = exercise?.progressions_regressions?.some(
      (ex) => ex.exerciseType === EXERCISE_TYPE.REGRESSION,
    );

    return (
      <View style={styles.progressionRegressionView}>
        {hasProgression ? (
          <Image
            style={styles.progressionRegressionIcon}
            source={progressionIcon}
            pointerEvents="none"
          />
        ) : null}
        {hasRegression ? (
          <Image
            style={styles.progressionRegressionIcon}
            source={regressionIcon}
            pointerEvents="none"
          />
        ) : null}
      </View>
    );
  };

  renderFavoriteIconImage(exercise, isSuperSet, isCircuit) {
    if (this.props.showsFavoriteImage) {
      return (
        <TouchableOpacity
          key={exercise.id}
          onPress={() => this.props.onPressFavoriteIcon(exercise, isSuperSet, isCircuit)}
        >
          <Image
            style={styles.favoriteImage}
            source={this.props.isFavorite ? favoriteIcon : unfavoriteIcon}
            pointerEvents="none"
          />
        </TouchableOpacity>
      );
    }
    return null;
  }

  render() {
    const { exercise } = this.props;
    const isSuperSet = exercise.routine_type === ROUTINE_TYPES.SUPER_SET;
    const isCircuit = exercise.routine_type === ROUTINE_TYPES.CIRCUIT;
    return (
      <View
        style={[
          styles.container,
          {
            backgroundColor: this.props.isSelected
              ? colors.actionSheetDivider
              : colors.white,
          },
        ]}
      >
        <TouchableOpacity
          key={exercise.id}
          style={styles.cell}
          onPress={() => this.props.onPressExercise(exercise, isSuperSet, isCircuit)}
        >
          <View style={styles.horizontalView}>
            {!isSuperSet && !isCircuit ? (
              <Image
                style={styles.thumbnail}
                source={ExerciseCell.getImageSource(exercise)}
              />
            ) : null}
            {exercise?.progressions_regressions?.length
            && this.props.currentUser.role === ROLES.TRAINER
              ? this.renderProgressionRegressionIconImage(exercise)
              : null}
            <ScaledText style={styles.nameText}>
              {getExerciseName(this.props.currentUser, exercise)}
            </ScaledText>
          </View>
          <View style={styles.horizontalView}>
            {this.renderFavoriteIconImage(exercise, isSuperSet, isCircuit)}
            {!this.props.showsSelectionImage && (
              <Image style={styles.arrow} source={rightArrow} />
            )}
            {this.renderSelectionIconImage()}
          </View>
        </TouchableOpacity>
        <View style={styles.divider} />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
  },
  cell: {
    flex: 1,
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    height: curvedScale(100),
    justifyContent: 'space-between',
    paddingHorizontal: '5%',
  },
  horizontalView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  thumbnail: {
    width: curvedScale(76),
    height: curvedScale(53),
    borderRadius: curvedScale(4),
  },
  nameText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    textAlign: 'left',
    width: curvedScale(180),
    marginLeft: scaleWidth(7),
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: colors.subGreyLight,
  },
  selectionImage: {
    width: curvedScale(30),
    height: curvedScale(30),
  },
  arrow: {
    width: curvedScale(26),
    height: curvedScale(26),
  },
  favoriteImage: {
    width: curvedScale(24),
    height: curvedScale(24),
  },
  progressionRegressionView: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    position: 'absolute',
    width: curvedScale(76),
    height: curvedScale(53),
    justifyContent: 'flex-end',
  },
  progressionRegressionIcon: {
    width: curvedScale(17),
    height: curvedScale(17),
    marginBottom: 3,
    marginRight: 3,
    marginLeft: 2,
  },
});

ExerciseCell.propTypes = propTypes;
ExerciseCell.defaultProps = defaultProps;

const mapStateToProps = ({ currentUser, programContext }) => ({
  currentUser,
  programContext,
});
const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ExerciseCell);
