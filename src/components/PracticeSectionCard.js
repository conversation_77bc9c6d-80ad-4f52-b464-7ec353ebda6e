import React, { Component } from 'react';

// Components
import {
  View, TouchableOpacity, Image, Text,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import AwesomeAlert from 'react-native-awesome-alerts';
import { scaleWidth } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';

class PracticeSectionCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      cardTitle: this.props.cardTitle,
    };
  }

  render() {
    const { showAlert } = this.state;
    const { visible } = this.props;

    if (!visible) return null;

    const handleCancel = () => this.setState({ showAlert: false });
    const handleConfirm = () => {
      this.setState({ showAlert: false });
      // openStore();
    };
    // const openStore = () => {
    //   const iosAppStoreUrl = 'NEW NASM APP store Link';
    //   const androidPlayStoreUrl = 'NEW NASM Playstore store Link';

    //   const storeUrl =
    //     Platform.OS === 'ios' ? iosAppStoreUrl : androidPlayStoreUrl;

    //   Linking.openURL(storeUrl).catch(err => {
    //     console.warn('Failed to open store URL:', err);
    //   });
    // };

    return (
      <View>
        <TouchableOpacity
          onPress={() => {
            this.setState({ showAlert: true });
          }}
          style={{
            ...styles.cardContainer,
            marginRight: this.props.end ? scaleWidth(9) : scaleWidth(2.5),
          }}
        >
          <Image style={styles.bgImage} source={this.props.image} />
          <View style={styles.content}>
            <Text style={styles.titleText}>{this.props.cardTitle}</Text>
          </View>
          <View
            style={{
              position: 'absolute',
              right: 15,
              top: 50,
            }}
          >
            <IconFeader name="chevron-right" size={25} color={colors.white} />
          </View>
        </TouchableOpacity>

        <AwesomeAlert
          show={showAlert}
          showProgress={false}
          useNativeDriver
          title="Exam prep has moved."
          message="You can now access it through the Customer Portal or the new NASM App. It’s no longer available in the Edge app."
          closeOnTouchOutside
          closeOnHardwareBackPress={false}
          showConfirmButton
          showCancelButton
          titleStyle={styles.titleStyle}
          messageStyle={styles.messageStyle}
          confirmText="Download NASM App"
          cancelText="Cancel"
          confirmButtonTextStyle={styles.confirmButtonTextStyle}
          confirmButtonStyle={styles.alertButton}
          cancelButtonTextStyle={styles.confirmButtonTextStyle}
          cancelButtonStyle={styles.alertButton}
          actionContainerStyle={styles.alertActionContainer}
          contentContainerStyle={styles.contentContainerStyle}
          onCancelPressed={handleCancel}
          onConfirmPressed={handleConfirm}
        />
      </View>
    );
  }
}

const styles = {
  cardContainer: {
    height: 120,
    width: scaleWidth(43.8),
    backgroundColor: colors.subGrey,
    borderRadius: 16,
    ...shadow,
  },
  bgImage: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 16,
  },
  content: {
    margin: '8%',
    flexGrow: 1,
    justifyContent: 'space-evenly',
  },
  titleText: {
    fontWeight: 'bold',
    fontSize: 18,
    color: colors.white,
    marginLeft: 15,
  },

  titleStyle: {
    fontSize: 20,
    fontFamily: 'SFProText-Medium',
    color: colors.black,
    textAlign: 'center',
  },
  messageStyle: {
    color: colors.fillDarkGrey,
    fontSize: 14,
    fontFamily: 'Avenir-Roman',
    textAlign: 'center',
    marginVertical: 10,
    marginHorizontal: 16,
  },
  confirmButtonTextStyle: {
    color: colors.fillDarkGrey,
    fontSize: 14,
    fontFamily: 'Avenir-Heavy',
  },
  innerContainerStyle: {
    paddingHorizontal: 25,
  },
  alertContainerStyle: {
    paddingHorizontal: 0,
  },
  contentStyle: {
    borderBottomWidth: 1,
    borderColor: colors.actionSheetDivider,
  },
  contentContainerStyle: {
    paddingHorizontal: 0,
  },
  alertButton: {
    width: '100%',
    height: 36,
    borderTopWidth: 1,
    borderTopColor: colors.lightgrey,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 0,
    marginHorizontal: 0,
  },
  alertActionContainer: {
    alignItems: 'stretch',
    flexDirection: 'column-reverse',
    backgroundColor: colors.white,
    borderRadius: 5,
  },
};

export default PracticeSectionCard;
