import React, { Component } from 'react';

// Components
import { View, Text, StyleSheet } from 'react-native';
import { curvedScale } from '../util/responsive';
import { colors } from '../styles';

class ExamDomainScore extends Component {
  getPercentageFromScore = () => {
    let percentage = (this.props.score / this.props.domainTotal) * 100;
    if (percentage > 100) {
      percentage = 100;
    }
    return `${percentage}%`;
  };

  renderDomainWithScore = () => (
    <View style={styles.textContainer}>
      <Text numberOfLines={2} style={styles.domain}>
        {this.props.domain}
      </Text>
      <Text style={styles.numericalScore}>
        {`${this.props.score}/${this.props.domainTotal}`}
      </Text>
    </View>
  );

  renderLines = () => (
    <View>
      <View style={styles.grayLine} />
      <View
        style={styles.coloredLine(
          this.getPercentageFromScore(),
          this.props.color,
          this.props.score,
        )}
      />
    </View>
  );

  render() {
    return (
      <View style={styles.container}>
        {this.renderDomainWithScore()}
        {this.renderLines()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: curvedScale(18),
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: curvedScale(15),
  },
  domain: {
    fontSize: curvedScale(15),
    fontWeight: '500',
    fontFamily: 'Avenir-Roman',
    color: colors.black,
    width: '85%',
    alignSelf: 'center',
  },
  numericalScore: {
    fontSize: curvedScale(15),
    fontWeight: 'bold',
    fontFamily: 'Avenir-Black',
    color: colors.black,
    textAlign: 'right',
    width: '15%',
  },
  grayLine: {
    width: '100%',
    height: curvedScale(5),
    backgroundColor: colors.lineLightBlue,
    borderRadius: 15,
  },
  coloredLine: (width, backgroundColor, score) => ({
    width,
    height: curvedScale(5),
    backgroundColor,
    borderRadius: 15,
    position: 'absolute',
    opacity: score > 0 ? 1 : 0,
  }),
});

export default ExamDomainScore;
