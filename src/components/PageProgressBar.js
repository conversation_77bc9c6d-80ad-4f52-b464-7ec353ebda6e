import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { StyleSheet, View } from 'react-native';

// Styles
import { colors, shadow } from '../styles';

// PropTypes
const propTypes = {
  currentPage: PropTypes.number.isRequired,
  maxPages: PropTypes.number.isRequired,
};

// Class
class PageProgressBar extends Component {
  getBarStyle(exercise, index, currentPage) {
    if (exercise.is_complete) {
      return styles.complete;
    } if (currentPage - 1 === index) {
      return styles.inProgress;
    }
    return styles.incomplete;
  }

  render() {
    const { guidedWorkout } = this.props;
    const currentPage = guidedWorkout.currentIndex + 1;
    return (
      <View style={styles.barContainer}>
        {guidedWorkout.exercises.map((exercise, index) => (
          <View
            key={`bar${index}`}
            style={this.getBarStyle(exercise, index, currentPage)}
          />
        ))}
      </View>
    );
  }
}

// Export
PageProgressBar.propTypes = propTypes;
export default PageProgressBar;

// Styles
const styles = StyleSheet.create({
  barContainer: {
    flexDirection: 'row',
    backgroundColor: '#0d2b4f',
    alignItems: 'center',
    ...shadow,
  },
  complete: {
    flex: 1,
    height: 11,
    backgroundColor: colors.goodGreen,
  },
  inProgress: {
    flex: 1,
    height: 11,
    backgroundColor: colors.macaroniAndCheese,
  },
  incomplete: {
    flex: 1,
    height: 11,
    backgroundColor: colors.nasmBlue,
  },
});
