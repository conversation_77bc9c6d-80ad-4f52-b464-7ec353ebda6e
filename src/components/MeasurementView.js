import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Dimensions, View, Image, Platform,
} from 'react-native';
import { connect } from 'react-redux';
import moment from 'moment';
import AvatarPicker from './AvatarPicker';
import BubbleInput from './BubbleInput';
import ScaledText from './ScaledText';
import { curvedScale } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';
import { ROLES } from '../constants';

// Images
const plusIcon = require('../resources/plusIcon.png');
const xIcon = require('../resources/xIcon.png');
const cameraIcon = require('../resources/cameraIcon.png');

const cameraIconWidth = Image.resolveAssetSource(cameraIcon).width;
const cameraIconHeight = Image.resolveAssetSource(cameraIcon).height;

const propTypes = {
  data: PropTypes.shape({}).isRequired,
  onDataEdited: PropTypes.func.isRequired,
  frontPicture: PropTypes.shape({ path: PropTypes.string }),
  backPicture: PropTypes.shape({ path: PropTypes.string }),
  sidePicture: PropTypes.shape({ path: PropTypes.string }),
  onFrontPictureChanged: PropTypes.func.isRequired,
  onBackPictureChanged: PropTypes.func.isRequired,
  onSidePictureChanged: PropTypes.func.isRequired,
  onBodyFatPressed: PropTypes.func.isRequired,
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired,
  }).isRequired,
  selectedClient: PropTypes.shape({
    id: PropTypes.string,
  }).isRequired,
  isEditable: PropTypes.bool,
};

const defaultProps = {
  frontPicture: null,
  backPicture: null,
  sidePicture: null,
  isEditable: true,
};

class MeasurementView extends Component {
  onDataEdited(key, text) {
    if (!text.length) return this.props.onDataEdited(key, '');
    let value = text.replace(/-/g, '');
    if (key === 'weight') {
      return this.props.onDataEdited(key, value);
    }
    // Add a leading zero if a dot is present.
    if (/^\./.test(value)) {
      return this.props.onDataEdited(key, `0${value}`);
    }
    const dots = value.match(/\./g);
    if (dots && dots.length > 1) {
      return this.props.onDataEdited(key, value.slice(0, -1));
    }
    const [integers = [], decimals = []] = value.split('.');
    // Prevent the user from inputing more than 2 decimals e.g. 0.987, 1.234
    if (decimals.length >= 3) {
      return this.props.onDataEdited(key, value.slice(0, -1));
    }
    // Format the value values after 3 digits so we
    // only get to 2 integers and 2 decimals
    // this only applies if there is no dot already
    // e.g. 123 gets formatted to 12.3
    if (value.length >= 3 && !value.includes('.')) {
      value = `${value.slice(0, 2)}.${value.slice(-1)}`;
      return this.props.onDataEdited(key, value);
    }
    // Prevent more than 2 integers
    if (integers.length >= 3) {
      const decimalPart = decimals.length ? `.${decimals}` : '';
      const newValue = `${integers.slice(0, -1)}${decimalPart}`;
      return this.props.onDataEdited(key, newValue);
    }
    return this.props.onDataEdited(key, value);
  }

  renderNoImageOverlay = (label) => (
    <View style={styles.overlayContainer}>
      <Image
        source={cameraIcon}
        style={{
          width: curvedScale(cameraIconWidth),
          height: curvedScale(cameraIconHeight),
        }}
      />
      <ScaledText style={styles.overlayLabel}>{label}</ScaledText>
    </View>
  );

  getUpdatedOnDate = () => {
    const isTrainer = this.props.currentUser.role === ROLES.TRAINER;
    return isTrainer
      ? this.props.selectedClient?.client_user.updated_at
      : this.props.currentUser.client_user.updated_at;
  };

  renderInput(label, key) {
    const heightUnit = this.props.currentUser.unit_height === 'cm' ? ' cm' : '"';
    const weightUnit = this.props.currentUser.unit_weight === 'kg' ? 'kgs' : 'lbs';
    let unit = heightUnit;
    let maxDigits = 5;
    if (key === 'weight') {
      maxDigits = 3;
      unit = Platform.OS === 'ios' ? ` ${weightUnit}` : weightUnit;
    } else if (key === 'body_fat_percentage') {
      return this.renderBodyFatInput();
    }
    return (
      <BubbleInput
        onChangeText={(text) => this.onDataEdited(key, text)}
        label={label}
        unit={unit}
        keyboardType="numeric"
        maxLength={maxDigits}
        inputContainerStyle={styles.inputContainer}
        value={`${this.props.data[key] || ''}`}
        placeholder="0"
      />
    );
  }

  renderInputRow(label1, key1, label2, key2) {
    return (
      <View style={styles.inputRowContainer}>
        {this.renderInput(label1, key1)}
        {this.renderInput(label2, key2)}
      </View>
    );
  }

  renderBodyFatInput() {
    let value = this.props.data.body_fat_percentage;
    if (!value || Number.isNaN(value)) {
      value = 0;
    }
    return (
      <BubbleInput
        onPress={this.props.onBodyFatPressed}
        label="Body Fat:"
        value={`${value}`}
        unit="%"
        inputContainerStyle={styles.inputContainer}
      />
    );
  }

  renderPictures() {
    const pickerOptions = { cropperCircleOverlay: false, cropping: false };
    return (
      <View style={styles.picturesContainer}>
        <View>
          {!this.props.frontPicture && this.renderNoImageOverlay('Front')}
          <AvatarPicker
            onSelectImage={this.props.onFrontPictureChanged}
            imageUri={
              this.props.frontPicture ? this.props.frontPicture.path : undefined
            }
            deletable
            containerStyle={styles.imagePicker}
            imageStyle={styles.image}
            shadow={false}
            hidePlaceholderImage
            icon={this.props.frontPicture ? xIcon : plusIcon}
            iconContainerStyle={styles.imageIcon}
            disabled={!this.props.isEditable}
            imagePickerOptions={pickerOptions}
            navigation={this.props.navigation}
          />
        </View>
        <View>
          {!this.props.backPicture && this.renderNoImageOverlay('Back')}
          <AvatarPicker
            onSelectImage={this.props.onBackPictureChanged}
            imageUri={
              this.props.backPicture ? this.props.backPicture.path : undefined
            }
            deletable
            containerStyle={styles.imagePicker}
            imageStyle={styles.image}
            shadow={false}
            hidePlaceholderImage
            icon={this.props.backPicture ? xIcon : plusIcon}
            iconContainerStyle={styles.imageIcon}
            disabled={!this.props.isEditable}
            imagePickerOptions={pickerOptions}
            navigation={this.props.navigation}
          />
        </View>
        <View>
          {!this.props.sidePicture && this.renderNoImageOverlay('Side')}
          <AvatarPicker
            onSelectImage={this.props.onSidePictureChanged}
            imageUri={
              this.props.sidePicture ? this.props.sidePicture.path : undefined
            }
            deletable
            containerStyle={styles.imagePicker}
            imageStyle={styles.image}
            shadow={false}
            hidePlaceholderImage
            icon={this.props.sidePicture ? xIcon : plusIcon}
            iconContainerStyle={styles.imageIcon}
            disabled={!this.props.isEditable}
            imagePickerOptions={pickerOptions}
            navigation={this.props.navigation}
          />
        </View>
      </View>
    );
  }

  renderWeightInputs() {
    return (
      <View style={styles.weightInputsContainer}>
        {this.renderInputRow(
          'Weight:',
          'weight',
          'Body fat',
          'body_fat_percentage',
        )}
      </View>
    );
  }

  render() {
    const updatedOnDate = this.getUpdatedOnDate()
      ? moment(this.getUpdatedOnDate()).format('MM/DD/YY')
      : '';
    return (
      <View
        pointerEvents={this.props.isEditable ? 'auto' : 'none'}
        style={{ backgroundColor: colors.white }}
      >
        {this.renderWeightInputs()}
        {this.renderPictures()}
        <ScaledText style={styles.bodyRegion}>Body Region</ScaledText>
        <View style={styles.sectionHeader}>
          <ScaledText style={styles.sectionHeaderTitle}>Upper</ScaledText>
        </View>
        {this.renderInputRow('Neck:', 'neck', 'Chest:', 'chest')}
        {this.renderInputRow(
          'Bicep (R):',
          'bicep_right',
          'Bicep (L):',
          'bicep_left',
        )}
        {this.renderInputRow(
          'Forearm (R):',
          'forearm_right',
          'Forearm (L):',
          'forearm_left',
        )}
        <View style={styles.sectionHeader}>
          <ScaledText style={styles.sectionHeaderTitle}>Midsection</ScaledText>
        </View>
        {this.renderInputRow('Waist:', 'waist', 'Hips:', 'hips')}
        <View style={styles.sectionHeader}>
          <ScaledText style={styles.sectionHeaderTitle}>Lower</ScaledText>
        </View>
        {this.renderInputRow(
          'Thigh (R):',
          'thigh_right',
          'Thigh (L):',
          'thigh_left',
        )}
        {this.renderInputRow(
          'Calf (R):',
          'calf_right',
          'Calf (L):',
          'calf_left',
        )}

        {updatedOnDate ? (
          <ScaledText style={styles.sectionUpdatedTitle}>
            {`Updated on ${updatedOnDate}`}
          </ScaledText>
        ) : null}

        <View style={{ height: 50 * scale }} />
      </View>
    );
  }
}

MeasurementView.defaultProps = defaultProps;
MeasurementView.propTypes = propTypes;

const scale = Dimensions.get('window').width / 400;

const styles = {
  weightInputsContainer: {
    paddingVertical: 25 * scale,
  },
  inputRowContainer: {
    paddingHorizontal: '4%',
    paddingVertical: 5 * scale,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  label: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.black,
  },
  input: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.subGrey,
    height: '100%',
    textAlign: 'right',
  },
  unit: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.subGrey,
    height: '100%',
    alignSelf: 'center',
  },
  picturesContainer: {
    flexDirection: 'row',
    paddingHorizontal: '4%',
    justifyContent: 'space-between',
  },
  imagePicker: {
    width: undefined,
    height: undefined,
    borderRadius: 0,
    borderWidth: 0,
    alignItems: undefined,
    justifyContent: undefined,
    backgroundColor: 'transparent',
    elevation: 0,
  },
  image: {
    margin: 7 * scale,
    width: 94 * scale,
    height: 159 * scale,
    borderRadius: curvedScale(4),
  },
  imageIcon: {
    height: 33 * scale,
    width: 33 * scale,
    backgroundColor: colors.macaroniAndCheese,
    ...shadow,
  },
  bodyRegion: {
    marginHorizontal: '4%',
    marginTop: 35 * scale,
    fontFamily: 'Avenir-Roman',
    fontSize: 24,
    color: colors.black,
  },
  sectionHeader: {
    paddingHorizontal: '4%',
    borderBottomWidth: 1,
    paddingBottom: 10 * scale,
    marginVertical: 16 * scale,
    borderColor: colors.cloudyBlue,
  },
  sectionHeaderTitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.black,
  },
  overlayContainer: {
    position: 'absolute',
    top: 7 * scale,
    right: 7 * scale,
    left: 7 * scale,
    bottom: 7 * scale,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGreyLight,
    borderRadius: curvedScale(4),
  },
  overlayLabel: {
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '500',
    color: colors.cloudyBlue,
    marginTop: 5 * scale,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: colors.cloudyBlue,
    backgroundColor: colors.white,
    borderRadius: 4,
    paddingLeft: 6,
    alignItems: 'center',
    marginRight: curvedScale(6),
    height: curvedScale(35),
  },
  sectionUpdatedTitle: {
    marginTop: 40,
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.subGrey,
    alignSelf: 'center',
  },
};

const mapStateToProps = ({ currentUser, selectedClient }) => ({
  currentUser,
  selectedClient,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(MeasurementView);
