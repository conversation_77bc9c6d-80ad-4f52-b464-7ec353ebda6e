import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Moment from 'moment';

// Components
import { Dimensions, Text, View } from 'react-native';
import {
  VictoryLine,
  VictoryScatter,
  VictoryAxis,
  VictoryChart,
} from 'victory-native';
import { scaleWidth } from '../util/responsive';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  measurements: PropTypes.array,
  renderEmptyStateView: PropTypes.func,
  centerDate: PropTypes.string,
  showUpdatedText: PropTypes.bool,
  loading: PropTypes.bool,
  weightUnit: PropTypes.string,
};
const defaultProps = {
  measurements: null,
  renderEmptyStateView: null,
  centerDate: null,
  showUpdatedText: true,
  loading: false,
  weightUnit: '',
};

class MeasurementsSection extends Component {
  getBodyFatData = () => {
    const bodyFatData = [];
    const { measurements } = this.props;
    const reversedMeasurements = [].concat(measurements).reverse(0);
    for (let i = 0; i < reversedMeasurements.length; i += 1) {
      const measurement = reversedMeasurements[i];
      if (measurement.body_fat_percentage) {
        bodyFatData.push({ x: i, y: measurement.body_fat_percentage });
      } else if (this.props.centerDate && measurement.avg_body_fat_percentage) {
        bodyFatData.push({
          x: 3 + Moment(measurement.date).diff(this.props.centerDate, 'days'),
          y: measurement.avg_body_fat_percentage,
        });
      }
    }
    return bodyFatData.length === 1
      ? bodyFatData
      : this.normalizeData(bodyFatData);
  };

  getDisplayMeasurement = (measurements) => {
    if (this.props.centerDate) {
      let measurement;
      for (let i = 0; i < measurements.length; i += 1) {
        if (
          !measurement
          || (Moment(measurements[i].date).isSameOrBefore(
            this.props.centerDate,
            'day',
          )
            && Moment(measurements[i].date).isAfter(measurement.date, 'day'))
        ) {
          measurement = measurements[i];
        }
      }
      return measurement;
    }

    return measurements[0];
  };

  getLeanMuscleData = () => {
    const leanMuscleData = [];
    const { measurements } = this.props;
    const reversedMeasurements = [].concat(measurements).reverse(0);
    for (let i = 0; i < reversedMeasurements.length; i += 1) {
      const measurement = reversedMeasurements[i];
      if (measurement.lean_body_mass_percentage) {
        leanMuscleData.push({ x: i, y: measurement.lean_body_mass_percentage });
      } else if (
        this.props.centerDate
        && measurement.avg_lean_body_mass_percentage
      ) {
        leanMuscleData.push({
          x: 3 + Moment(measurement.date).diff(this.props.centerDate, 'days'),
          y: measurement.avg_lean_body_mass_percentage,
        });
      }
    }
    return leanMuscleData.length === 1
      ? leanMuscleData
      : this.normalizeData(leanMuscleData);
  };

  getWeightData = () => {
    const weightData = [];
    const { measurements } = this.props;
    const reversedMeasurements = [].concat(measurements).reverse(0);
    for (let i = 0; i < reversedMeasurements.length; i += 1) {
      const measurement = reversedMeasurements[i];
      if (this.props.centerDate && measurement.avg_weight) {
        const x = 3 + Moment(measurement.date).diff(this.props.centerDate, 'days');
        const y = measurement.avg_weight;
        weightData.push({ x, y });
      } else if (measurement.weight) {
        weightData.push({ x: i, y: measurement.weight });
      }
    }
    return weightData.length === 1
      ? weightData
      : this.normalizeData(weightData);
  };

  normalizeData = (data) => {
    const normalized = [];
    let max = -1;
    data.forEach((element) => {
      if (max === -1) {
        max = element.y;
      }
      if (element.y > max) {
        max = element.y;
      }
    });
    data.forEach((element) => {
      if (max !== 0) {
        normalized.push({ x: element.x, y: element.y / max + 0.1 });
      }
    });
    return normalized;
  };

  roundToOneDecimal = (num) => +`${Math.round(`${num}e+1`)}e-1`;

  renderEmptyStateView() {
    if (typeof this.props.renderEmptyStateView === 'function') {
      return this.props.renderEmptyStateView();
    }
    return (
      <View style={styles.emptyStateView}>
        <Text style={styles.assessInfoLabel}>
          Tap here to record measurement information
        </Text>
      </View>
    );
  }

  renderGraphLine = (data, color) => {
    if (!data?.length) {
      return null;
    }
    return (
      <VictoryLine
        style={{ data: { stroke: color, strokeWidth: 2 } }}
        data={data}
        interpolation="catmullRom"
      />
    );
  };

  renderGraphPoints = (data, color, symbol) => {
    if (!data) {
      return null;
    }
    return (
      <VictoryScatter
        style={{ data: { fill: color } }}
        size={4}
        symbol={symbol}
        data={data}
      />
    );
  };

  renderLoadingState() {
    const weightData = [
      { x: 0, y: 100 },
      { x: 1, y: 95 },
      { x: 2, y: 80 },
      { x: 3, y: 70 },
      { x: 4, y: 50 },
      { x: 5, y: 55 },
      { x: 6, y: 50 },
    ];
    const bodyFatData = [
      { x: 0, y: 50 },
      { x: 1, y: 45 },
      { x: 2, y: 40 },
      { x: 3, y: 30 },
      { x: 4, y: 20 },
      { x: 5, y: 20 },
      { x: 6, y: 10 },
    ];
    const leanMuscleData = [
      { x: 0, y: 10 },
      { x: 1, y: 0 },
      { x: 2, y: 15 },
      { x: 3, y: 40 },
      { x: 4, y: 65 },
      { x: 5, y: 75 },
      { x: 6, y: 80 },
    ];
    return (
      <View>
        <View style={styles.midSection} pointerEvents="none">
          <VictoryChart
            height={100 * scale}
            width={339 * scale}
            domainPadding={10}
            padding={{
              top: 10,
              bottom: 10,
              left: 20,
              right: 20,
            }}
          >
            <VictoryAxis
              key={0}
              style={{
                axis: { stroke: '' },
                tickLabels: { fill: 'rgba(0,0,0,0)' },
                grid: { stroke: colors.loadingStateGray },
              }}
              tickValues={[0, 1, 2, 3, 4, 5, 6]}
            />
            {this.renderGraphLine(leanMuscleData, colors.loadingStateGray)}
            {this.renderGraphPoints(
              leanMuscleData,
              colors.loadingStateGray,
              'triangleUp',
            )}

            {this.renderGraphLine(bodyFatData, colors.loadingStateGray)}
            {this.renderGraphPoints(
              bodyFatData,
              colors.loadingStateGray,
              'square',
            )}

            {this.renderGraphLine(weightData, colors.loadingStateGray)}
            {this.renderGraphPoints(
              weightData,
              colors.loadingStateGray,
              'circle',
            )}
          </VictoryChart>
        </View>
        <View style={styles.textSection}>
          <View
            style={[
              styles.legendDot,
              { backgroundColor: colors.loadingStateGray },
            ]}
          />
          <View
            style={{
              height: 12 * scale,
              width: 80 * scale,
              backgroundColor: colors.loadingStateGray,
              marginLeft: 3 * scale,
              marginRight: 10 * scale,
            }}
          />
          <View
            style={[
              styles.legendSquare,
              { backgroundColor: colors.loadingStateGray },
            ]}
          />
          <View
            style={{
              height: 12 * scale,
              width: 80 * scale,
              backgroundColor: colors.loadingStateGray,
              marginLeft: 3 * scale,
              marginRight: 10 * scale,
            }}
          />
          <View
            style={[
              styles.triangle,
              { borderBottomColor: colors.loadingStateGray },
            ]}
          />
          <View
            style={{
              height: 12 * scale,
              width: 80 * scale,
              backgroundColor: colors.loadingStateGray,
              marginLeft: 3 * scale,
              marginRight: 10 * scale,
            }}
          />
        </View>
      </View>
    );
  }

  renderMeasurementsInformation(measurements, weightUnit) {
    if (!measurements || measurements.length === 0) {
      return this.renderEmptyStateView();
    }
    const measurement = this.getDisplayMeasurement(measurements);
    const weight = measurement.weight
      ? measurement.weight
      : measurement.avg_weight;
    const bodyFat = measurement.body_fat_percentage
      ? measurement.body_fat_percentage
      : measurement.avg_body_fat_percentage;
    const leanMusclePercentage = measurement.lean_body_mass_percentage
      ? measurement.lean_body_mass_percentage
      : measurement.avg_lean_body_mass_percentage;
    const leanMuscle = weight * leanMusclePercentage * 0.01;
    const weightText = weight
      ? `${this.roundToOneDecimal(weight)}${weightUnit}`
      : 'n/a';
    const bodyFatText = bodyFat ? `${this.roundToOneDecimal(bodyFat)}%` : 'n/a';
    const leanMuscleText = leanMuscle
      ? `${this.roundToOneDecimal(leanMuscle)}${weightUnit}`
      : 'n/a';
    const yValues = [0, 1, 2, 3, 4, 5, 6];
    const weightData = this.getWeightData();
    const bodyFatData = this.getBodyFatData();
    const leanMuscleData = this.getLeanMuscleData();
    const alignCenterStyle = {
      alignItems: 'center',
    };
    return (
      <View>
        <View
          style={[styles.midSection, this.props.centerDate && alignCenterStyle]}
          pointerEvents="none"
        >
          {this.props.centerDate && (
            <View style={styles.chartHeaderContainer}>
              <View style={styles.chartHeaderView()} />
              <View style={styles.chartHeaderView(2)} />
              <View style={styles.chartHeaderView()} />
            </View>
          )}
          <VictoryChart
            height={100 * scale}
            width={339 * scale}
            domainPadding={10}
            padding={{
              top: 10,
              bottom: 10,
              left: 20,
              right: 20,
            }}
          >
            <VictoryAxis
              key={0}
              style={{
                axis: { stroke: '' },
                tickLabels: { fill: 'rgba(0,0,0,0)' },
                grid: { stroke: colors.silver },
              }}
              tickValues={yValues}
            />

            {this.renderGraphLine(leanMuscleData, colors.cloudyBlue)}
            {this.renderGraphPoints(
              leanMuscleData,
              colors.cloudyBlue,
              'triangleUp',
            )}

            {this.renderGraphLine(bodyFatData, colors.subGrey)}
            {this.renderGraphPoints(bodyFatData, colors.subGrey, 'square')}

            {this.renderGraphLine(weightData, colors.nasmRed)}
            {this.renderGraphPoints(weightData, colors.nasmRed, 'circle')}
          </VictoryChart>
        </View>
        <View style={styles.textSection}>
          <View style={styles.keyPair}>
            <View
              style={[styles.legendDot, { backgroundColor: colors.nasmRed }]}
            />
            <Text style={styles.underText}>
              weight:
              {weightText}
            </Text>
          </View>
          <View style={styles.keyPair}>
            <View
              style={[styles.legendSquare, { backgroundColor: colors.subGrey }]}
            />
            <Text style={styles.underText}>
              body fat:
              {bodyFatText}
            </Text>
          </View>
          <View style={styles.keyPair}>
            <View style={styles.triangle} />
            <Text style={styles.underText}>
              lean muscle:
              {leanMuscleText}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  render() {
    const { measurements } = this.props;
    const { weightUnit } = this.props;
    const view = this.props.loading
      ? this.renderLoadingState()
      : this.renderMeasurementsInformation(measurements, weightUnit);
    let updated_at = null;
    if (measurements && measurements.length > 0) {
      updated_at = new Moment(measurements[0].date).format('M/D/YY');
    }
    return (
      <View style={styles.cardStyle}>
        <View style={styles.headerSection}>
          <Text style={styles.header}>Measurements</Text>
          {!this.props.loading && this.props.showUpdatedText && updated_at && (
            <Text style={styles.subHeader}>
              Updated
              {updated_at}
            </Text>
          )}
        </View>
        {view}
      </View>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  cardStyle: {
    borderRadius: 5 * scale,
    backgroundColor: '#fff',
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowRadius: 4,
    shadowOpacity: 1,
    elevation: 3,
    marginTop: 20 * scale,
    marginHorizontal: scaleWidth(5),
  },
  headerSection: {
    backgroundColor: 'rgb(216, 44, 68)',
    height: 40 * scale,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopLeftRadius: 5 * scale,
    borderTopRightRadius: 5 * scale,
  },
  header: {
    color: '#fff',
    fontSize: 18 * scale,
    fontFamily: 'Avenir-Roman',
    paddingLeft: 15 * scale,
  },
  subHeader: {
    color: '#fff',
    fontFamily: 'Avenir-Heavy',
    paddingRight: 12 * scale,
    fontSize: 9 * scale,
  },
  midSection: {
    paddingTop: 8 * scale,
    paddingBottom: 8 * scale,
    backgroundColor: 'rgb(248, 249, 251)',
  },
  underText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12 * scale,
    color: 'rgb(124, 128, 132)',
    paddingLeft: 3 * scale,
    paddingRight: 10 * scale,
  },
  legendDot: {
    width: 5,
    height: 5,
    borderRadius: 2.5,
  },
  legendSquare: {
    width: 5,
    height: 5,
  },
  textSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16 * scale,
    height: 34 * scale,
  },
  keyPair: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyStateView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16 * scale,
  },
  triangle: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderLeftWidth: 4,
    borderRightWidth: 4,
    borderBottomWidth: 8,
    borderBottomColor: colors.cloudyBlue,
  },
  assessInfoLabel: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 16 * scale,
    color: colors.subGrey,
    textAlign: 'center',
  },
  chartHeaderContainer: {
    position: 'absolute',
    right: 10 * scale,
    left: 0,
    top: 9 * scale,
    bottom: 0,
    alignItems: 'center',
  },
  chartHeaderView: (width = 10) => ({
    width: width * scale,
    height: 2 * scale,
    backgroundColor: colors.cloudyBlue,
  }),
};

MeasurementsSection.propTypes = propTypes;
MeasurementsSection.defaultProps = defaultProps;

export default MeasurementsSection;
