import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Dimensions,
  LayoutAnimation,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';

import ScaledText from './ScaledText';
import { scaleHeight, scaleWidth } from '../util/responsive';
import { androidSafeLayoutAnimation } from '../constants';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  titles: PropTypes.array,
};

const defaultProps = {
  titles: [],
};

const { width } = Dimensions.get('window');

class Tabs extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedTabIndex: 0,
    };
  }

  onPressTab = (selectedTabIndex) => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.scrollView.scrollTo({
      x: width * selectedTabIndex,
      y: 0,
      animated: true,
    });
    this.setState({ selectedTabIndex });
  };

  renderTabs() {
    return (
      <View style={styles.tabsContainer}>
        {this.props.titles.map((item, index) => {
          const isSelected = this.state.selectedTabIndex === index;
          return (
            <TouchableOpacity
              key={item}
              style={styles.tab}
              onPress={() => this.onPressTab(index)}
            >
              <View style={{ flex: 1, justifyContent: 'center' }}>
                <ScaledText
                  style={
                    isSelected
                      ? styles.selectedTabText
                      : styles.unselectedTabText
                  }
                >
                  {item}
                </ScaledText>
              </View>
              <View
                style={
                  isSelected
                    ? styles.selectedTabIndicator
                    : styles.unselectedTabIndicator
                }
              />
            </TouchableOpacity>
          );
        })}
      </View>
    );
  }

  render() {
    return (
      <View>
        {this.renderTabs()}

        <ScrollView
          horizontal
          scrollEnabled={false}
          ref={(ref) => {
            this.scrollView = ref;
          }}
        >
          {this.props.children.map((item, index) => (
            <View key={index} style={{ width, overflow: 'hidden' }}>
              {item}
            </View>
          ))}
        </ScrollView>
      </View>
    );
  }
}

const styles = {
  tabsContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    borderBottomWidth: 1,
    borderColor: 'rgba(124,128,132,0.1)',
  },
  tab: {
    height: scaleHeight(7),
    width: scaleWidth(35),
    flexDirection: 'column',
  },
  selectedTabText: {
    fontSize: 18,
    textAlign: 'center',
    color: colors.medYellow,
  },
  unselectedTabText: {
    fontSize: 18,
    textAlign: 'center',
    color: colors.cloudyBlue,
  },
  selectedTabIndicator: {
    height: 5,
    backgroundColor: colors.medYellow,
  },
  unselectedTabIndicator: {
    height: 5,
  },
};

Tabs.propTypes = propTypes;
Tabs.defaultProps = defaultProps;

export default Tabs;
