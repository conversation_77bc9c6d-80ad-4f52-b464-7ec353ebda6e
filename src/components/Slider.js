import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { StyleSheet, Text, View } from 'react-native';
import Slider from '@react-native-community/slider';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  ...Slider.propTypes,
  questionText: PropTypes.string,
  minLabel: PropTypes.string,
  maxLabel: PropTypes.string,
  initialValue: PropTypes.number,
};
const defaultProps = {
  // RN Slider props
  ...Slider.defaultProps,
  minimumValue: 0,
  maximumValue: 10,
  step: 1,
  // custom props
  questionText: 'WHAT ARE YOU RATING?',
  minLabel: 'MINIMUM',
  maxLabel: 'MAXIMUM',
  initialValue: 5,
};

// Class
class CustomSlider extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: props.initialValue,
    };
  }

  handleValueChange = (value) => {
    this.setState({ value });
  };

  render() {
    return (
      <View style={styles.container}>
        <Text style={styles.questionText}>{this.props.questionText}</Text>
        <View style={styles.valueContainer}>
          <Text style={styles.valueText}>{this.state.value}</Text>
        </View>
        <Slider
          value={this.state.value}
          minimumValue={this.props.minimumValue}
          maximumValue={this.props.maximumValue}
          step={this.props.step}
          onValueChange={this.handleValueChange}
          minimumTrackTintColor={colors.nasmBlue}
          thumbTintColor={colors.nasmBlue}
          onSlidingComplete={this.props.onSlidingComplete}
        />
        <View style={styles.labelContainer}>
          <Text style={styles.labelText}>{this.props.minLabel}</Text>
          <Text style={styles.labelText}>{this.props.maxLabel}</Text>
        </View>
      </View>
    );
  }
}

// Export
CustomSlider.propTypes = propTypes;
CustomSlider.defaultProps = defaultProps;
export default CustomSlider;

// Styles
const styles = StyleSheet.create({
  container: {},
  questionText: {
    fontFamily: 'Avenir',
    fontSize: 16,
    fontWeight: '900',
    color: colors.black,
    marginBottom: 10,
  },
  valueContainer: {
    alignItems: 'flex-end',
  },
  valueText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    textAlign: 'right',
    color: colors.nasmBlue,
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 13,
  },
  labelText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10,
    color: colors.subGrey,
  },
});
