import React from 'react';
import PropTypes from 'prop-types';

// Components
import { View } from 'react-native';

// Styles
import { colors } from '../styles';

const propTypes = {
  protein: PropTypes.number,
  carbohydrates: PropTypes.number,
  fat: PropTypes.number,
  calories: PropTypes.number,
};
const defaultProps = {
  protein: 0,
  carbohydrates: 0,
  fat: 0,
  calories: 0,
};

class NutritionHorizontalBar extends React.Component {
  render() {
    const totalGrams = this.props.protein + this.props.carbohydrates + this.props.fat;
    const proteinWidth = `${((this.props.protein / totalGrams) * 100).toString()}%`;
    const carbsWidth = `${((this.props.carbohydrates / totalGrams) * 100).toString()}%`;
    const fatWidth = `${((this.props.fat / totalGrams) * 100).toString()}%`;
    return (
      <View
        style={[
          { height: '100%', width: '100%', borderRadius: 2 },
          this.props.style,
          { overflow: 'hidden' },
        ]}
      >
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: colors.loadingStateGray,
            flex: 1,
          }}
        >
          <View style={[styles.proteinBar, { width: proteinWidth }]} />
          <View style={[styles.carbsBar, { width: carbsWidth }]} />
          <View style={[styles.fatBar, { width: fatWidth }]} />
        </View>
      </View>
    );
  }
}

const styles = {
  proteinBar: {
    backgroundColor: colors.macaroniAndCheese,
    height: '100%',
  },
  carbsBar: {
    backgroundColor: colors.pinkishPurple,
    height: '100%',
  },
  fatBar: {
    backgroundColor: colors.nasmBlue,
    height: '100%',
  },
};

NutritionHorizontalBar.propTypes = propTypes;
NutritionHorizontalBar.defaultProps = defaultProps;

export default NutritionHorizontalBar;
