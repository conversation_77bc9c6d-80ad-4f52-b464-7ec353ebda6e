import React, { Component } from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import PropTypes from 'prop-types';
import { colors } from '../styles';

const scale = Dimensions.get('window').width / 400;

const propTypes = {
  percent: PropTypes.number,
  radius: PropTypes.number,
  children: PropTypes.array,
  borderWidth: PropTypes.number,
};

const defaultProps = {
  percent: null,
  radius: null,
  children: [],
  borderWidth: null,
};

const styles = StyleSheet.create({
  main: {
    paddingVertical: 5,
  },
  outerCircle: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerCircle: {
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  leftWrap: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  halfCircle: {
    position: 'absolute',
    top: 0,
    left: 0,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  maxContainer: {
    position: 'absolute',
    zIndex: 3,
    alignSelf: 'center',
    top: 3,
  },
  maxVerticalLine: {
    borderLeftColor: colors.subGrey,
    borderLeftWidth: 2,
    height: 9,
  },
  minContainer: {
    position: 'absolute',
    zIndex: 3,
    transform: [{ rotate: '150deg' }],
    left: '20%',
    top: '21%',
  },
  minVerticalLine: {
    borderLeftColor: colors.subGrey,
    borderLeftWidth: 2,
    height: 8 * scale,
    position: 'absolute',
  },
});

function percentToDegrees(percent) {
  return percent * 3.6;
}

class PercentageCircle extends Component {
  computeDerivedState() {
    const { props } = this;
    const percent = Math.max(Math.min(100, props.percent), 0);
    const needHalfCircle2 = percent > 50;
    let halfCircle1Degree;
    let halfCircle2Degree;
    // degrees indicate the 'end' of the half circle, i.e. they span (degree - 180, degree)
    if (needHalfCircle2) {
      halfCircle1Degree = 180;
      halfCircle2Degree = percentToDegrees(percent);
    } else {
      halfCircle1Degree = percentToDegrees(percent);
      halfCircle2Degree = 0;
    }

    return {
      halfCircle1Degree,
      halfCircle2Degree,
      halfCircle2Styles: {
        // when the second half circle is not needed, we need it to cover
        // the negative degrees of the first circle
        backgroundColor: needHalfCircle2 ? colors.vividBlue : colors.gainsboro,
      },
    };
  }

  renderHalfCircle(rotateDegrees, halfCircleStyles) {
    const { radius } = this.props;
    return (
      <View
        style={[
          styles.leftWrap,
          {
            width: radius,
            height: radius * 2,
          },
        ]}
      >
        <View
          style={[
            styles.halfCircle,
            // eslint-disable-next-line react-native/no-inline-styles
            {
              width: radius,
              height: radius * 2,
              borderRadius: radius,
              overflow: 'hidden',
              transform: [
                { translateX: radius / 2 },
                { rotate: `${rotateDegrees}deg` },
                { translateX: -radius / 2 },
              ],
              backgroundColor: colors.vividBlue,
              ...halfCircleStyles,
            },
          ]}
        />
      </View>
    );
  }

  renderInnerCircle() {
    const radiusMinusBorder = this.props.radius - this.props.borderWidth;
    return (
      <View
        style={[
          styles.innerCircle,
          {
            width: radiusMinusBorder * 2,
            height: radiusMinusBorder * 2,
            borderRadius: radiusMinusBorder,
            backgroundColor: colors.white,
          },
        ]}
      >
        {this.props.children}
      </View>
    );
  }

  render() {
    const {
      halfCircle1Degree,
      halfCircle2Degree,
      halfCircle2Styles,
    } = this.computeDerivedState();

    return (
      <View style={styles.main}>
        <View style={styles.maxContainer}>
          <View style={styles.maxVerticalLine} />
        </View>
        <View style={styles.minContainer}>
          <View style={styles.minVerticalLine} />
        </View>

        <View
          style={[
            styles.outerCircle,
            {
              width: this.props.radius * 2,
              height: this.props.radius * 2,
              borderRadius: this.props.radius,
              backgroundColor: colors.gainsboro,
            },
          ]}
        >
          {this.renderHalfCircle(halfCircle1Degree)}
          {this.renderHalfCircle(halfCircle2Degree, halfCircle2Styles)}
          {this.renderInnerCircle()}
        </View>
      </View>
    );
  }
}

PercentageCircle.propTypes = propTypes;
PercentageCircle.defaultProps = defaultProps;

export default PercentageCircle;
