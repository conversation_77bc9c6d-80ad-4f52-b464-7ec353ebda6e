import React, { Component } from 'react';

// Components
import { View, TouchableOpacity } from 'react-native';
import ScaledText from './ScaledText';
import { baseScale, scaleHeight } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';

class CardDomain extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <TouchableOpacity
        style={styles.cardBody}
        onPress={() => this.props.onPressDomain(this.props.domain)}
      >
        <View
          style={[
            styles.cardHeader,
            { backgroundColor: this.props.color },
          ]}
        />
        <View style={styles.cardContent}>
          <ScaledText style={styles.cardText}>
            {this.props.domain.domainName}
          </ScaledText>
        </View>
      </TouchableOpacity>
    );
  }
}

const styles = {
  cardHeader: {
    borderTopRightRadius: 15,
    borderTopLeftRadius: 15,
    height: scaleHeight(4),
  },
  cardBody: {
    backgroundColor: colors.white,
    minHeight: scaleHeight(20),
    borderRadius: 15,
    width: '48%',
    marginTop: baseScale(13),
    ...shadow,
  },
  cardContent: {
    flex: 1,
    justifyContent: 'flex-end',
    padding: '5%',
  },
  cardText: {
    fontFamily: 'Avenir-Medium',
    color: colors.black,
    fontSize: 17,
  },
};

export default CardDomain;
