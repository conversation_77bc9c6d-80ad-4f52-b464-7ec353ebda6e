import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image,
  LayoutAnimation,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import ScaledText from './ScaledText';
import { scaleHeight } from '../util/responsive';

// Styles
import { colors } from '../styles';

// Constants
import { androidSafeLayoutAnimation } from '../constants';

const downIcon = require('../resources/btnArrowDown.png');

// Props
const propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }),
  ).isRequired,
  autoSelectFirstValue: PropTypes.bool,
  labelKey: PropTypes.string,
  subLabelKey: PropTypes.string,
  onPress: PropTypes.func,
  onPickerVisible: PropTypes.func,
  onValueChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  selected: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  visible: PropTypes.bool,
  containerStyle: PropTypes.object,
  label: PropTypes.string,
  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  labelStyle: PropTypes.object,
  renderSelectedValueText: PropTypes.bool,
  selectedLabelStyle: PropTypes.object,
  textStyle: PropTypes.object,
  subLabelStyle: PropTypes.object,
  testID: PropTypes.string,
  numberOfLines: PropTypes.number,
};

const defaultProps = {
  autoSelectFirstValue: true,
  labelKey: 'label',
  subLabelKey: null,
  onPress: null,
  onPickerVisible: null,
  placeholder: 'SELECT',
  label: null,
  renderSelectedValueText: true,
  selected: null,
  visible: false,
  width: 'auto',
  numberOfLines: undefined,
};

// Component
class DropDownPicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  getLabel() {
    if (!this.props.selected && !this.props.placeholder) {
      return this.props.data[0][this.props.labelKey];
    }
    if (!this.props.selected && this.props.placeholder) {
      return this.props.placeholder;
    }
    const selectedItem = this.props.data.filter(
      (item) => item.id === this.props.selected,
    )[0];
    return selectedItem
      ? selectedItem[this.props.labelKey]
      : this.props.placeholder;
  }

  dropDownItemSelected = (item) => {
    this.props.onValueChange(item);
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ visible: false });
  };

  togglePicker = () => {
    // Set initial date on open so the user doesn't have to scroll to select the default value
    if (this.props.autoSelectFirstValue && !this.props.selected) {
      this.props.onValueChange(this.props.data[0].id);
    }
    // Callback to allow for custom onPress action (e.g. parent wants to control picker state)
    if (this.props.onPress) {
      this.props.onPress();
    } else {
      LayoutAnimation.configureNext(androidSafeLayoutAnimation);
      this.setState({ visible: !this.state.visible }, () => {
        if (this.props.onPickerVisible) {
          setTimeout(() => this.props.onPickerVisible(this.state.visible), 0);
        }
      });
    }
  };

  renderDropDownItem = (item) => {
    const labelStyle = [
      this.props.labelStyle,
      this.props.selected === item.id ? this.props.selectedLabelStyle : null,
    ];
    return (
      <TouchableOpacity
        key={item.id}
        style={styles.itemContainer}
        onPress={() => this.dropDownItemSelected(item)}
      >
        <ScaledText style={labelStyle}>{item[this.props.labelKey]}</ScaledText>
        {this.props.subLabelKey && (
          <ScaledText style={this.props.subLabelStyle}>
            {item[this.props.subLabelKey]}
          </ScaledText>
        )}
      </TouchableOpacity>
    );
  };

  render() {
    const isVisible = this.state.visible || this.props.visible;
    const imageStyle = isVisible ? { transform: [{ rotate: '180deg' }] } : {};
    const containerStyle = [styles.inputContainer, this.props.containerStyle];
    const selectedTextStyle = [
      this.props.selected ? styles.inputText : styles.placeholderText,
      this.props.textStyle ? this.props.textStyle : {},
      { flex: 1 },
    ];
    return (
      <View style={{ width: this.props.width }}>
        {/* Main Container */}
        <TouchableOpacity
          testID={this.props.testID}
          onPress={this.togglePicker}
          style={containerStyle}
        >
          {this.props.label != null && (
            <ScaledText style={this.props.labelStyle}>
              {this.props.label}
            </ScaledText>
          )}
          <View
            style={[
              styles.selectionContainer,
              this.props.label && { flex: 1, justifyContent: 'flex-end' },
            ]}
          >
            {this.props.renderSelectedValueText && (
              <ScaledText
                style={selectedTextStyle}
                numberOfLines={this.props.numberOfLines}
              >
                {this.getLabel()}
              </ScaledText>
            )}
            <Image style={imageStyle} source={downIcon} />
          </View>
        </TouchableOpacity>

        {/* Drop Down Items */}
        {isVisible && (
          <View
            style={[{ paddingBottom: 10 }, this.props.contentContainerStyle]}
          >
            {this.props.data.map((item) => this.renderDropDownItem(item))}
          </View>
        )}
      </View>
    );
  }
}

// Exports
DropDownPicker.propTypes = propTypes;
DropDownPicker.defaultProps = defaultProps;
export default DropDownPicker;

// Styles
const styles = StyleSheet.create({
  inputContainer: {
    height: scaleHeight(4.5),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 5,
  },
  inputText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: colors.black,
    textAlign: 'right',
  },
  placeholderText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: colors.textPlaceholder,
    textAlign: 'right',
  },
  itemContainer: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderColor: colors.subGreyLight,
    borderBottomWidth: 1,
  },
  selectionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
