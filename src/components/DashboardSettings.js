import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Dimensions,
  Image,
  Platform,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { connect } from 'react-redux';
import { isConnectedMode } from '../util/PermissionUtils';

// Styles
import { colors } from '../styles';

// Images
const visibile = require('../../assets/visible.png');
const hidden = require('../../assets/hidden.png');

// PropTypes
const propTypes = {
  visibleGoals: PropTypes.bool,
  visibleDailyReadiness: PropTypes.bool,
  visibleMeasures: PropTypes.bool,
  visibleNutrition: PropTypes.bool,
  visibleOhsa: PropTypes.bool,
  visiblePerfAssess: PropTypes.bool,
  onSettingToggled: PropTypes.func,
};
const defaultProps = {
  visibleGoals: false,
  visibleDailyReadiness: false,
  visibleMeasures: false,
  visibleNutrition: false,
  visibleOhsa: false,
  visiblePerfAssess: false,
  onSettingToggled: null,
};

class DashboardSettings extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visibleGoals: props?.visibleGoals,
      visibleDailyReadiness: props?.visibleDailyReadiness,
      visibleNutrition: props?.visibleNutrition,
      visibleMeasures: props?.visibleMeasures,
      visiblePerfAssess: props?.visiblePerfAssess,
    };
  }

  onSettingToggled = (paramName, newValue) => {
    this.setState({ [paramName]: newValue }, () => {
      if (this.props.onSettingToggled) {
        this.props.onSettingToggled(paramName, newValue);
      }
    });
  };

  render() {
    const connectedMode = isConnectedMode(this.props.currentUser);
    const settings = [
      {
        text: 'Goals',
        paramName: 'visible_goals',
        color: colors.azure,
        visible: this.props.visibleGoals,
        hidden: connectedMode,
      },
      {
        text: 'Readiness Assessment',
        paramName: 'visible_daily_readiness',
        color: colors.redinessLightGrey,
        visible: this.props.visibleDailyReadiness,
        hidden: connectedMode,
      },
      {
        text: 'Nutrition',
        paramName: 'visible_nutrition',
        color: colors.peaGreen,
        visible: this.props.visibleNutrition,
        hidden: this.props.trainerActiveProfile?.ClubId || connectedMode,
      },
      {
        text: 'Measurements',
        paramName: 'visible_measures',
        color: colors.nasmRed,
        visible: this.props.visibleMeasures,
        hidden: this.props.trainerActiveProfile?.ClubId || connectedMode,
      },
      {
        text: 'Performance Assessments',
        paramName: 'visible_perf_assess',
        color: colors.nasmBlue,
        visible: this.props.visiblePerfAssess,
        hidden: connectedMode,
      },
      {
        text: 'Overhead Squat',
        paramName: 'visible_ohsa',
        color: colors.duskBlue,
        visible: this.props.visibleOhsa,
      },
    ];
    return (
      <View>
        <View style={styles.dividerLine} />

        {/* Dashboard Settings Rows */}
        {settings
          .filter((s) => !s.hidden)
          .map((setting) => (
            <View key={setting.paramName}>
              <View style={styles.settingRow}>
                <View
                  style={[
                    styles.coloredBar,
                    { backgroundColor: setting.color },
                  ]}
                />
                <Text style={styles.settingText}>{setting.text}</Text>

                {/* OHSA Row */}
                {setting.paramName === 'visible_ohsa' && (
                  <View style={styles.ohsaRowContainer}>
                    <Text style={styles.ohsaText}>
                      {`${
                        setting.visible
                          ? 'Visible to Client'
                          : 'Hidden from Client'
                      }`}
                    </Text>
                    <TouchableOpacity
                      onPress={() => this.onSettingToggled(
                        setting.paramName,
                        !setting.visible,
                      )}
                    >
                      <Image source={setting.visible ? visibile : hidden} />
                    </TouchableOpacity>
                  </View>
                )}

                {/* All Other Rows */}
                {setting.paramName !== 'visible_ohsa' && (
                <Text style={styles.nonOhsaText}>
                  {setting.visible ? 'on' : 'off'}
                </Text>
                ) && (
                <Switch
                  style={styles.switch}
                  value={this.state[setting.paramName] ?? setting.visible}
                  thumbColor={Platform.OS === 'ios' ? null : colors.white}
                  trackColor={{ true: colors.medYellow }}
                  onValueChange={(newValue) => this.onSettingToggled(setting.paramName, newValue)}
                />
                )}
              </View>
              <View style={styles.dividerLine} />
            </View>
          ))}
      </View>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  descriptionContainer: {
    minHeight: '15%',
    width: '85%',
    marginStart: 28 * scale,
    justifyContent: 'center',
  },
  descriptionText: {
    color: colors.subGreyTwo,
    fontSize: 13 * scale,
  },
  coloredBar: {
    width: 18 * scale,
    height: 55 * scale,
  },
  settingRow: {
    height: 55 * scale,
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    marginStart: 20 * scale,
    fontFamily: 'Avenir-Roman',
    fontSize: 16 * scale,
    marginTop: 0,
    textAlignVertical: 'center',
  },
  ohsaRowContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    position: 'absolute',
    end: 36 * scale,
  },
  ohsaText: {
    color: colors.subGreyTwo,
    marginEnd: 5 * scale,
    fontSize: 13 * scale,
    fontFamily: 'Avenir-Roman',
  },
  nonOhsaText: {
    position: 'absolute',
    color: colors.subGrey,
    end: 98 * scale,
    fontFamily: 'Avenir-Roman',
    fontSize: 12 * scale,
    marginTop: 0,
    textAlignVertical: 'center',
  },
  switch: {
    position: 'absolute',
    end: 36 * scale,
  },
  dividerLine: {
    height: 1,
    backgroundColor: '#f2f3f3',
  },
};

DashboardSettings.propTypes = propTypes;
DashboardSettings.defaultProps = defaultProps;

const mapStateToProps = ({ currentUser, trainerActiveProfile }) => ({
  currentUser,
  trainerActiveProfile,
});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(DashboardSettings);
