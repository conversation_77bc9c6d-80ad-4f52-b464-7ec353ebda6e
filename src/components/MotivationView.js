import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Text,
  View,
  Image,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import TextInput from './TextInput';
import { removeAllSpecialCharacters } from '../util/validate';

// Images
const visible = require('../../assets/visible.png');
const hidden = require('../../assets/hidden.png');

// PropTypes
const propTypes = {
  textChangedCallback: PropTypes.func,
  visibleChangedCallback: PropTypes.func,
  text: PropTypes.string,
  visibleByTrainer: PropTypes.bool,
  editable: PropTypes.bool,
};
const defaultProps = {
  textChangedCallback: () => {},
  visibleChangedCallback: () => {},
  text: '',
  visibleByTrainer: true,
  editable: true,
};

class MotivationView extends Component {
  flipVisibility = () => {
    if (this.props.visibleChangedCallback) {
      this.props.visibleChangedCallback(!this.props.visibleByTrainer);
    }
  };

  textChanged = (text) => {
    if (this.props.textChangedCallback) {
      this.props.textChangedCallback(text);
    }
  };

  render() {
    return (
      <KeyboardAvoidingView
        style={this.props.style || {}}
        behavior={Platform.OS === 'ios' ? 'padding' : null}
      >
        <View style={styles.container}>
          <View style={styles.flexHorizontal}>
            <Text style={styles.motivationText}>Motivation</Text>
            {this.props.editable && (
              <View style={styles.flexHorizontal}>
                <Text
                  style={styles.motivationText}
                  onPress={this.flipVisibility}
                >
                  {this.props.visibleByTrainer
                    ? 'Visible by Trainer'
                    : 'Hidden from Trainer'}
                </Text>
                <TouchableOpacity
                  style={styles.image}
                  onPress={this.flipVisibility}
                >
                  <Image
                    source={this.props.visibleByTrainer ? visible : hidden}
                  />
                </TouchableOpacity>
              </View>
            )}
          </View>
          <TextInput
            onChangeText={(text) => {
              const filteredText = removeAllSpecialCharacters(text);
              this.textChanged(filteredText);
            }}
            value={this.props.text}
            returnKeyType="done"
            editable={this.props.editable}
            showIcon={false}
          />
        </View>
      </KeyboardAvoidingView>
    );
  }
}

MotivationView.propTypes = propTypes;
MotivationView.defaultProps = defaultProps;

export default MotivationView;

const styles = {
  container: {
    paddingTop: 24,
    paddingLeft: 28,
    paddingRight: 28,
  },
  motivationText: {
    fontSize: 13,
    fontFamily: 'Avenir-Roman',
    color: 'rgba(124, 128, 132, 0.8)',
  },
  flexHorizontal: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  image: {
    paddingLeft: 5,
  },
};
