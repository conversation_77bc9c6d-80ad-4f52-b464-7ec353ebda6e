import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { View } from 'react-native';
import DropDownPicker from './DropDownPicker';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  selectedValue: PropTypes.object,
  selectedChangedCallback: PropTypes.func,
  data: PropTypes.array.isRequired,
};
const defaultProps = {
  selectedValue: null,
  selectedChangedCallback: () => {},
};

class ActivityDropdown extends Component {
  selectActivity = (selection) => {
    this.setState(
      {
        selectedValue: selection,
      },
      () => {
        if (this.props.selectedChangedCallback) {
          this.props.selectedChangedCallback(selection);
        }
      },
    );
  };

  render() {
    let label = 'Select Activity Level';
    if (this.props.selectedValue !== null) {
      label = this.props.selectedValue.title;
    }
    return (
      <View style={styles.container}>
        <DropDownPicker
          autoSelectFirstValue={false}
          placeholder={' '}
          data={this.props.data}
          labelKey="title"
          subLabelKey="description"
          selected={
            (this.props.selectedValue && this.props.selectedValue.id) || null
          }
          onValueChange={(selection) => this.selectActivity(selection)}
          outline
          fullWidthValues
          style={styles.picker}
          textStyle={{ ...styles.variableValue, flex: 1, textAlign: 'right' }}
          label={label}
          labelStyle={styles.variableUnselectedLabel}
          renderSelectedValueText={false}
          selectedLabelStyle={styles.variableLabel}
          subLabelStyle={styles.variableSubLabel}
          onPickerVisible={this.pickerChangedVisibility}
        />
      </View>
    );
  }
}

ActivityDropdown.propTypes = propTypes;
ActivityDropdown.defaultProps = defaultProps;

export default ActivityDropdown;

const styles = {
  container: {
    paddingTop: 24,
    paddingLeft: 28,
    paddingRight: 28,
  },
  picker: {
    height: 40,
    borderRadius: 20,
    marginTop: 0,
    marginBottom: 10,
  },
  variableLabel: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18,
    color: colors.black,
  },
  variableUnselectedLabel: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18,
    color: colors.subGrey,
  },
  variableSubLabel: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.subGrey,
  },
  variableValue: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.subGrey,
  },
};
