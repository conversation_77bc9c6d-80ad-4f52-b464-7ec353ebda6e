import React, { Component } from 'react';

// Components
import Svg, { Circle } from 'react-native-svg';
import ScaledSvgText from './ScaledSvgText';
import { curvedScale, scaleWidth } from '../util/responsive';

// Styles
import { colors } from '../styles';

// Constants
const CIRCUMFERENCE = 2 * scaleWidth(23) * Math.PI;
const LOOP = (CIRCUMFERENCE * 3.7) / 5;
const LOOP_POSITION = scaleWidth(50);
const transform = `rotate(137, ${LOOP_POSITION}, ${LOOP_POSITION})`;

class ExamTotalScore extends Component {
  getScorePercentage() {
    return this.props.totalCorrect / this.props.maxQuestions;
  }

  render() {
    return (
      <Svg height={scaleWidth(80)} width={scaleWidth(100)}>
        {/* Underlying gray loop */}
        <Circle
          cx={`${scaleWidth(50)}`}
          cy={`${scaleWidth(50)}`}
          r={`${scaleWidth(23)}`}
          stroke="rgb(210, 215, 219)"
          strokeWidth={scaleWidth(2)}
          strokeLinecap="round"
          strokeDasharray={[LOOP, LOOP]}
          transform={transform.toString()}
          fill="transparent"
        />
        <ScaledSvgText
          scaleFactor={7}
          fill="black"
          x={`${scaleWidth(50)}`}
          y={`${scaleWidth(52)}`}
          style={styles.numericalScore}
          textAnchor="middle"
        >
          {`${this.props.totalCorrect}/${this.props.maxQuestions}`}
        </ScaledSvgText>
        {/* Thick black loop */}
        <Circle
          cx={`${scaleWidth(50)}`}
          cy={`${scaleWidth(50)}`}
          r={`${scaleWidth(23)}`}
          stroke={colors.black}
          strokeWidth={scaleWidth(4)}
          strokeLinecap="round"
          strokeDasharray={[this.getScorePercentage() * LOOP, CIRCUMFERENCE]}
          strokeDashoffset={0}
          fill="transparent"
          transform={transform.toString()}
          style={{
            position: 'absolute',
            opacity: this.props.totalCorrect > 0 ? 1 : 0,
          }}
        />
        <ScaledSvgText
          scaleFactor={10}
          x={`${scaleWidth(50)}`}
          y={`${scaleWidth(70)}`}
          textAnchor="middle"
          fill="black"
          style={styles.yourScore}
        >
          Your Score
        </ScaledSvgText>
      </Svg>
    );
  }
}

const styles = {
  container: {
    flex: 1,
  },
  numericalScore: {
    position: 'absolute',
    fontSize: curvedScale(34),
    fontWeight: 'bold',
    alignSelf: 'center',
  },
  yourScore: {
    position: 'absolute',
    fontSize: curvedScale(22),
  },
};

export default ExamTotalScore;
