import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  ImageBackground,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
} from 'react-native';
import { connect } from 'react-redux';
import moment from 'moment';
import { ROLES } from '../constants';
import {
  getDurationFromSeconds,
  getBackgroundFromProgramCategory,
} from '../util/programUtils';
import { scaleWidth } from '../util/responsive';

// Styles
import { colors } from '../styles';

// Props
const propTypes = {
  workout: PropTypes.object.isRequired,
  role: PropTypes.oneOf([ROLES.CLIENT, ROLES.TRAINER]),
  programName: PropTypes.string,
  programCategory: PropTypes.string,
  isGroup: PropTypes.bool,
  selectedGroup: PropTypes.any,
  scheduleGroupId: PropTypes.string,
};

const defaultProps = {
  role: ROLES.CLIENT,
  programName: '',
  programCategory: '',
  isGroup: false,
  selectedGroup: null,
  scheduleGroupId: '',
};

const defaultProfile = require('../resources/defaultProfile.png');
const checkedImage = require('../resources/checkmarkSelected.png');

// Main Class
class ProgramCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedWorkout: null,
    };
  }

  onPressRow = (id) => {
    if (this.state.selectedWorkout === id) {
      this.setState({ selectedWorkout: null });
    } else {
      this.setState({ selectedWorkout: id });
    }
  };

  isSectionStarted = (section) => {
    const exercises = section.scheduled_exercises;
    for (let i = 0; i < exercises.length; i += 1) {
      if (exercises[i].is_complete) {
        return true;
      }
    }
    return false;
  };

  isSectionComplete = (section) => {
    const exercises = section.scheduled_exercises;
    for (let i = 0; i < exercises.length; i += 1) {
      if (
        exercises[i].is_complete == null
        || exercises[i].is_complete === false
      ) {
        return false;
      }
    }
    return true;
  };

  isWorkoutStarted = () => {
    for (let i = 0; i < this.props.workout.sections.length; i += 1) {
      if (this.isSectionStarted(this.props.workout.sections[i])) {
        return true;
      }
    }
    return false;
  };

  isWorkoutComplete = () => {
    if (this.props.isGroup) {
      return (
        this.props.workout.completed_by.length
        === this.props.selectedGroup?.client_group_clients?.length
      );
    }
    for (let i = 0; i < this.props.workout.sections.length; i += 1) {
      if (!this.isSectionComplete(this.props.workout.sections[i])) {
        return false;
      }
    }
    return true && this.props.completed;
  };

  renderEditButton() {
    return (
      <TouchableOpacity
        style={styles.editButton}
        onPress={this.props.onPressEdit}
      >
        <Text style={styles.viewAllProgramsButtonText}>Edit</Text>
      </TouchableOpacity>
    );
  }

  renderDeleteButton() {
    return (
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={this.props.onPressDelete}
      >
        <Text style={styles.viewAllProgramsButtonText}>Remove</Text>
      </TouchableOpacity>
    );
  }

  renderGroupMemberView = () => {
    let sliced_clients = [];
    let client_group_clients = [];
    const { workout, selectedGroup } = this.props;
    if (selectedGroup?.id) {
      client_group_clients = selectedGroup?.client_group_clients
        .map((user) => ({
          ...user,
          is_complete: workout?.completed_by.includes(user.client_user?.id)
            ? 1
            : 0,
        }))
        .sort((user1, user2) => user2.is_complete - user1.is_complete);
      if (client_group_clients?.length < 6) {
        sliced_clients = [...client_group_clients];
      } else {
        for (let i = 0; i < 6; i += 1) {
          sliced_clients.push(client_group_clients[i]);
        }
      }
    }
    return (
      <View style={styles.groupMemberContainer}>
        <View style={styles.avatarContainer}>
          <View style={styles.clientsContainer}>
            {sliced_clients?.map((client) => (
              <View style={styles.avatarView}>
                <Image
                  source={
                    client.client_user?.user?.avatar_url
                      ? { uri: client.client_user.user.avatar_url }
                      : defaultProfile
                  }
                  style={styles.avatar}
                />
                {client.is_complete ? (
                  <Image
                    style={styles.completeStatusImg}
                    source={checkedImage}
                  />
                ) : null}
              </View>
            ))}
            {client_group_clients.length > 6 ? (
              <Text style={styles.clientCountLabel}>
                {`+${client_group_clients.length - sliced_clients.length}`}
              </Text>
            ) : null}
          </View>
        </View>
        <Text style={styles.clientCountLabel}>
          {`${this.props.workout?.completed_by?.length}/${client_group_clients.length} completed`}
        </Text>
      </View>
    );
  };

  render() {
    const isTrainer = this.props.role === ROLES.TRAINER;
    const isEditable = !(
      this.props.isGroup
      || this.isWorkoutStarted()
      || this.props.isExpired
    );
    const isDeletable = !(
      !this.props.isGroup
      || this.isWorkoutComplete()
      || this.props.isExpired
      || !this.props.scheduleGroupId
    );
    return (
      <View style={styles.card}>
        {/* Card Header */}
        <ImageBackground
          source={getBackgroundFromProgramCategory(this.props.programCategory)}
          style={[styles.programHeaderContainer, isTrainer]}
        >
          {!!this.props.programName && (
            <View style={styles.programNameContainer}>
              <Text numberOfLines={3} style={styles.programName}>
                {this.props.programName}
              </Text>
            </View>
          )}
          {!!this.props.programName && this.props.updated_at ? (
            <View style={styles.dateView}>
              <View style={styles.modifiedView}>
                <Text style={styles.modifiedDate}>
                  {`Modified ${moment(this.props.updated_at).format('M/D/YY')}`}
                </Text>
              </View>
            </View>
          ) : null}
          <Text numberOfLines={3} style={styles.programNameText}>
            {this.props.programWorkoutName}
          </Text>
          <View style={styles.workoutDetailsContainer}>
            <View
              style={{
                paddingRight: 10,
                borderRightWidth: 2,
                borderColor: colors.white,
                textDecorationLine: 'underline',
              }}
            >
              <Text style={styles.programLabelText}>
                {getDurationFromSeconds(
                  this.props.totalSeconds,
                  this.props.durationPlus,
                )}
              </Text>
            </View>
            <Text style={[styles.programLabelText, { marginLeft: 10 }]}>
              {this.props.programCategory}
            </Text>
          </View>
          {this.props.isGroup ? this.renderGroupMemberView() : null}
          {isTrainer && isEditable && this.renderEditButton()}
          {isTrainer && isDeletable && this.renderDeleteButton()}
          {this.isWorkoutComplete() && <View style={styles.completeDot} />}
        </ImageBackground>
      </View>
    );
  }
}

// Styles
const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    marginHorizontal: scaleWidth(5),
    backgroundColor: colors.white,
    marginTop: 10,
    overflow: 'hidden',
    height: 314,
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowRadius: 4,
    shadowOpacity: 1,
  },
  programHeaderContainer: {
    flex: 1,
    padding: 18,
    justifyContent: 'center',
  },
  editButton: {
    position: 'absolute',
    right: 22,
    bottom: 26,
    justifyContent: 'center',
    borderRadius: 18,
    borderStyle: 'solid',
    borderWidth: 2,
    borderColor: colors.white,
    height: 36,
  },
  deleteButton: {
    position: 'absolute',
    right: 22,
    bottom: 26,
    justifyContent: 'center',
    borderRadius: 18,
    borderStyle: 'solid',
    borderWidth: 2,
    borderColor: colors.white,
    height: 36,
  },
  viewAllProgramsButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'center',
    color: colors.white,
    paddingLeft: 24,
    paddingRight: 24,
  },
  programLabelText: {
    fontSize: 17,
    fontFamily: 'Avenir',
    color: colors.white,
    fontWeight: 'bold',
  },
  programNameText: {
    fontSize: 24,
    fontFamily: 'Avenir',
    color: colors.white,
    fontWeight: 'bold',
    lineHeight: 31,
    marginBottom: 12,
  },
  programNameContainer: {
    position: 'absolute',
    left: 0,
    top: 0,
    paddingVertical: 9,
    paddingLeft: 18,
    paddingRight: 13,
    backgroundColor: colors.duskBlue,
    borderBottomRightRadius: 5,
  },
  programName: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.white,
  },
  workoutDetailsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.transparent,
  },
  completeDot: {
    position: 'absolute',
    top: 21,
    right: 21,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgb(33,171,72)',
  },
  dateView: {
    alignItems: 'flex-start',
    marginBottom: 5,
  },
  modifiedView: {
    backgroundColor: colors.black,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    width: 'auto',
  },
  modifiedDate: {
    fontSize: 12,
    fontFamily: 'Avenir-Medium',
    color: colors.white,
    fontWeight: '700',
  },
  groupMemberContainer: {
    flexDirection: 'column',
    height: 70,
  },
  avatarContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 15,
  },
  clientsContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  avatarView: {
    width: 35,
    height: 35,
    borderRadius: 35,
    marginRight: 5,
  },
  avatar: {
    width: '100%',
    height: '100%',
    borderRadius: 35,
  },
  completeStatusImg: {
    width: 15,
    height: 15,
    position: 'absolute',
    right: 0,
    bottom: 0,
  },
  clientCountLabel: {
    fontSize: 15,
    fontFamily: 'Avenir-Medium',
    color: colors.disclaimerGrey,
  },
});

// Exports
ProgramCard.propTypes = propTypes;
ProgramCard.defaultProps = defaultProps;

const mapStateToProps = ({ selectedGroup }) => ({
  selectedGroup,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ProgramCard);
