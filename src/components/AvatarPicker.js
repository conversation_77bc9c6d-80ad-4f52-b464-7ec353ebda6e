import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewPropTypes,
  Platform,
  Alert,
} from 'react-native';
import { curvedScale } from '../util/responsive';
import MediaPicker, { MEDIA_SOURCE_TYPE } from '../util/MediaPicker.utils';

// Styles
import { colors, shadow } from '../styles';

// Constants
const cameraIcon = require('../resources/miniCamera.png');
const defaultProfile = require('../resources/defaultProfile.png');

// PropTypes
const propTypes = {
  onSelectImage: PropTypes.func, // Callback function to return the image
  disabled: PropTypes.bool,
  imageUri: PropTypes.string,
  containerStyle: ViewPropTypes.style,
  imageStyle: ViewPropTypes.style,
  placeholderImage: PropTypes.any,
  shadow: PropTypes.bool,
  resizeMode: PropTypes.string,
  icon: PropTypes.any,
  iconContainerStyle: ViewPropTypes.style,
  hideIconWhenDisabled: PropTypes.bool,
  imagePickerOptions: PropTypes.shape({}),
  hidePlaceholderImage: PropTypes.bool,
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired,
  }).isRequired,
};

const defaultProps = {
  onSelectImage: () => {},
  iconSize: 50,
  disabled: false,
  imageUri: null,
  hideIcon: false,
  containerStyle: null,
  imageStyle: null,
  placeholderImage: null,
  shadow: true,
  resizeMode: 'cover',
  icon: cameraIcon,
  iconContainerStyle: null,
  hideIconWhenDisabled: true,
  imagePickerOptions: {},
  hidePlaceholderImage: false,
};

const imagePickerOptions = {
  cropping: true,
  cropperCircleOverlay: true,
};

class AvatarPicker extends Component {
  onGetMedia(source) {
    const options = { ...imagePickerOptions, ...this.props.imagePickerOptions };
    MediaPicker.getMedia(source, options)
      .then((image) => {
        this.props.onSelectImage(image);
      })
      .catch((error) => Alert.alert('Failed to Change Avatar', error.message));
  }

  // Instance methods
  selectImage = () => {
    this.props.navigation.navigate('ActionSheet', {
      title: 'Edit Picture',
      actions: [
        {
          text: 'Take new picture',
          onPress: () => this.onGetMedia(MEDIA_SOURCE_TYPE.CAMERA),
        },
        {
          text: 'Select image from gallery',
          onPress: () => this.onGetMedia(MEDIA_SOURCE_TYPE.GALLERY),
        },
      ],
    });
  };

  deleteImage = () => {
    this.props.onSelectImage('');
  };

  // Main Render
  render() {
    let defaultImage;
    if (!this.props.hidePlaceholderImage) {
      defaultImage = this.props.placeholderImage
        ? this.props.placeholderImage
        : defaultProfile;
    }
    const isDeletable = this.props.deletable;
    return (
      <TouchableOpacity
        onPress={
          !this.props.imageUri || !isDeletable
            ? this.selectImage
            : this.deleteImage
        }
      >
        <View
          disabled={this.props.disabled || Platform.OS === 'android'}
          style={[
            styles.container,
            this.props.shadow ? { ...shadow } : {},
            this.props.containerStyle,
          ]}
        >
          {this.props.imageUri ? (
            <Image
              source={
                this.props.imageUri
                  ? { uri: this.props.imageUri }
                  : defaultImage
              }
              style={[
                styles.image,
                this.props.containerStyle,
                this.props.imageStyle,
              ]}
              resizeMode={this.props.resizeMode}
            />
          ) : (
            <View
              style={[
                styles.image,
                this.props.containerStyle,
                this.props.imageStyle,
              ]}
            />
          )}
        </View>
        {(!this.props.disabled || !this.props.hideIconWhenDisabled) && (
          <View>
            <View
              style={[styles.iconContainer, this.props.iconContainerStyle]}
              pointerEvents="none"
            >
              <Image style={styles.icon} source={this.props.icon} />
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  }
}

// Exports
AvatarPicker.propTypes = propTypes;
AvatarPicker.defaultProps = defaultProps;
export default AvatarPicker;

const styles = StyleSheet.create({
  container: {
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    height: 132,
    width: 132,
    borderRadius: 132 / 2,
    borderWidth: 3,
    borderColor: colors.nasmBlue,
    backgroundColor: colors.nasmBlue,
    elevation: 12,
    overflow: 'hidden',
  },
  image: {
    height: 132,
    width: 132,
    borderRadius: 132 / 2,
    borderWidth: 3,
    borderColor: colors.nasmBlue,
  },
  iconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: curvedScale(44),
    height: curvedScale(44),
    borderRadius: curvedScale(22),
    backgroundColor: colors.nasmBlue,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 14,
  },
  icon: {
    width: curvedScale(13),
    height: curvedScale(13),
  },
});
