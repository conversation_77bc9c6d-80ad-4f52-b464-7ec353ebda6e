// Libraries
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import {
  Image,
  LayoutAnimation,
  View,
  Text,
  StyleSheet,
  Platform,
  TouchableOpacity,
} from 'react-native';

import DateTimePicker from '@react-native-community/datetimepicker';

// Styles
import { colors } from '../styles';
import { curvedScale } from '../util/responsive';

// Constants
import { androidSafeLayoutAnimation } from '../constants';

const downIcon = require('../resources/btnArrowDown.png');

// Props
const propTypes = {
  value: PropTypes.instanceOf(Date),
  placeholder: PropTypes.string,
  onValueChange: PropTypes.func.isRequired,
  onIosPickerVisible: PropTypes.func,
  minValue: PropTypes.instanceOf(Date),
  maxValue: PropTypes.instanceOf(Date),
  style: PropTypes.shape({}),
  textStyle: PropTypes.shape({}),
  label: PropTypes.string,
  icon: PropTypes.any,
  defaultDate: PropTypes.instanceOf(Date),
  testID: PropTypes.string,
};

const defaultProps = {
  value: null,
  placeholder: 'DATE',
  osPickerVisible: null,
  minValue: null,
  maxValue: null,
  style: {},
  textStyle: {},
  label: undefined,
  icon: downIcon,
  defaultDate: new Date(),
  testID: undefined,
};

// Main Class
class DatePicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  onChange = (value, date) => {
    if (Platform.OS === 'android') {
      this.setState({ visible: false });
    }
    this.props.onValueChange(date);
  };

  displayDate = () => {
    if (this.props.value) {
      const value = moment(this.props.value).format('MMMM DD, YYYY');
      if (this.props.label) {
        return `${this.props.label}: ${value}`;
      }
      return value;
    }
    return this.props.placeholder;
  };

  // Methods
  togglePicker = () => {
    // we use null to show a placeholder description until the user interacts
    if (this.props.value === null) {
      this.props.onValueChange(this.props.defaultDate);
    }
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ visible: !this.state.visible }, () => {
      if (this.props.onIosPickerVisible) {
        setTimeout(() => this.props.onIosPickerVisible(this.state.visible), 0);
      }
    });
  };

  // Main render
  render() {
    return (
      <View>
        <TouchableOpacity
          testID={this.props.testID}
          style={[
            styles.inputContainer,
            styles.inputUnderline,
            this.props.style,
          ]}
          onPress={this.togglePicker}>
          <Text
            style={[
              this.props.value ? styles.inputText : styles.placeholderText,
              this.props.textStyle,
            ]}>
            {this.displayDate()}
          </Text>
          <Image source={this.props.icon} />
        </TouchableOpacity>
        {this.state.visible && (
          <DateTimePicker
            value={
              moment.isMoment(this.props.value)
                ? this.props.value.toDate()
                : this.props.value
            }
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'calendar'}
            minimumDate={this.props.minValue}
            maximumDate={this.props.maxValue}
            onChange={this.onChange}
          />
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 23,
    paddingBottom: 7,
  },
  inputUnderline: {
    borderBottomWidth: 1,
    borderBottomColor: colors.silver,
  },
  inputText: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(14),
    color: colors.black,
    flex: 1,
  },
  placeholderText: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(14),
    color: colors.textPlaceholder,
  },
});

// Exports
DatePicker.propTypes = propTypes;
DatePicker.defaultProps = defaultProps;
export default DatePicker;
