import React from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Slider from '@react-native-community/slider';
import Video from 'react-native-video';
import LoadingSpinner from './LoadingSpinner';

// Constants
import { colors } from '../styles';

const fullScreenIcon = require('../resources/videoPlayerFullscreen.png');
const playButton = require('../resources/videoPlayButton.png');
const thumb = require('../resources/videoScrubber.png');

const { width } = Dimensions.get('window');
const initialWidth = width * 0.9;
const initialHeight = initialWidth / (16 / 9);

// PropTypes
const propTypes = {
  videoUri: PropTypes.string,
  visible: PropTypes.bool,
  onPressOut: PropTypes.func.isRequired,
  videoFile: PropTypes.any,
};
const defaultProps = {
  visible: false,
  videoUri: false,
  videoFile: false,
};

// Helper functions
function secondsToMinutesSeconds(totalSeconds) {
  const minutes = Math.floor(totalSeconds / 60);
  let seconds = Math.floor(totalSeconds - minutes * 60);
  if (seconds < 10) {
    seconds = `0${seconds}`;
  }
  return `${minutes}:${seconds}`;
}

// Class
class VideoPlayer extends React.Component {
  state = {
    paused: false,
    muted: false,
    duration: 0.0,
    currentTime: 0.0,
    showThumbnail: true,
    videoHeight: initialHeight,
  };

  onEnd = () => {
    this.setState({ paused: true });
  };

  onLayout = (event) => {
    const containerWidth = event.nativeEvent.layout.width;
    this.setState({ containerWidth });
  };

  onLoad = (data) => {
    const { containerWidth } = this.state;
    const videoRatio = data.naturalSize.width / data.naturalSize.height;
    const videoHeight = containerWidth / videoRatio;
    this.setState({ videoHeight, duration: data.duration });
  };

  onLoadStart = () => {
    this.setState({ isLoading: true });
  };

  onPressFullscreen = () => {
    this.player.presentFullscreenPlayer();
  };

  onPressOut = () => {
    this.player.seek(0);
    this.setState({ currentTime: 0.0 });
    this.props.onPressOut();
  };

  onProgress = (data) => {
    const remainingTime = secondsToMinutesSeconds(
      this.state.duration - data.currentTime,
    );
    this.setState({
      currentTime: data.currentTime,
      remainingTime,
      isLoading: false,
    });
  };

  onSlidingComplete = (value) => {
    this.player.seek(value);
    this.setState({ paused: false });
  };

  onSlidingStart = () => {
    this.setState({ paused: true });
  };

  onValueChange = (value) => {
    this.player.seek(value);
  };

  togglePlayState = () => {
    this.setState((prevState) => ({ paused: !prevState.paused }));
  };

  render() {
    return (
      this.props.visible
      && (this.props.videoFile || this.props.videoUri) && (
        <TouchableOpacity
          style={styles.modal}
          onPress={this.onPressOut}
          disabled={!this.props.onPressOut}
        >
          <View style={styles.container} onLayout={this.onLayout}>
            <TouchableOpacity
              onPress={this.togglePlayState}
              style={styles.videoContainer}
            >
              <Video
                source={
                  this.props.videoFile
                    ? this.props.videoFile
                    : { uri: this.props.videoUri }
                }
                ref={(ref) => {
                  this.player = ref;
                }}
                style={[styles.video, { height: this.state.videoHeight }]}
                paused={this.state.paused}
                resizeMode="contain"
                onLoadStart={this.onLoadStart}
                onLoad={this.onLoad} // Callback when video loads
                onProgress={this.onProgress} // Callback every ~250ms with currentTime
                onEnd={this.onEnd} // Callback when playback finishes
                controls={false}
                fullscreenOrientation="landscape"
                repeat
              />
              {this.state.paused && (
                <View style={styles.pauseButton}>
                  <Image source={playButton} />
                </View>
              )}
              <LoadingSpinner visible={this.state.isLoading} />
            </TouchableOpacity>
            {/* Video Controls */}
            <View style={styles.videoControlsContainer}>
              <Text style={styles.durationText}>
                {secondsToMinutesSeconds(this.state.currentTime)}
              </Text>
              <Slider
                style={styles.slider}
                step={0}
                minimumValue={0}
                maximumValue={this.state.duration}
                value={this.state.currentTime}
                thumbImage={thumb}
                minimumTrackTintColor={colors.nasmRed}
                onSlidingStart={this.onSlidingStart}
                onSlidingComplete={this.onSlidingComplete}
              />
              <Text style={{ color: colors.subGrey, fontSize: 10 }}>
                {this.state.remainingTime}
              </Text>
              <TouchableOpacity
                style={styles.fullScreenIcon}
                onPress={this.onPressFullscreen}
              >
                <Image source={fullScreenIcon} />
              </TouchableOpacity>
            </View>
            <View style={{ flex: 1 }} />
          </View>
        </TouchableOpacity>
      )
    );
  }
}

// Export
VideoPlayer.propTypes = propTypes;
VideoPlayer.defaultProps = defaultProps;
export default VideoPlayer;

// Styles
const styles = StyleSheet.create({
  modal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.buttonBorderDisabled,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: '95%',
    backgroundColor: colors.white,
  },
  videoContainer: {
    width: '100%',
  },
  video: {
    width: '100%',
  },
  pauseButton: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.transparent,
  },
  videoControlsContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 15,
    paddingVertical: 5,
    backgroundColor: colors.background,
    alignItems: 'center',
  },
  durationText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10,
    color: colors.subGrey,
  },
  slider: {
    flexGrow: 1,
    marginHorizontal: 10,
  },
  fullScreenIcon: {
    marginLeft: 20,
  },
});
