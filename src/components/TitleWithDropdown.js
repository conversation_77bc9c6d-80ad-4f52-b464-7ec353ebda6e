import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Dimensions,
  LayoutAnimation,
  TouchableOpacity,
  View,
  Text,
} from 'react-native';

import ScaledText from './ScaledText';
import { scaleWidth } from '../util/responsive';
import { androidSafeLayoutAnimation } from '../constants';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
};
const defaultProps = {
  title: '',
  description: '',
};

class TitleWithDropdown extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showingDescription: false,
    };
  }

  toggleDescription = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.setState({ showingDescription: !this.state.showingDescription });
  };

  render() {
    return (
      <View
        style={{
          backgroundColor: this.props.color || 'rgba(241, 243, 244, 1)',
        }}
      >
        <View>
          <View style={styles.titleRow}>
            <ScaledText
              style={{
                ...styles.titleTextStyle,
                color: this.props.textColor || colors.black,
              }}
            >
              {this.props.title}
            </ScaledText>
            <TouchableOpacity onPress={this.toggleDescription}>
              <Text>(tap to view details)</Text>
            </TouchableOpacity>
          </View>
          {this.state.showingDescription && (
            <View>
              <ScaledText
                style={{
                  ...styles.descriptionStyle,
                  color: this.props.textColor || colors.black,
                }}
              >
                {this.props.description}
              </ScaledText>
            </View>
          )}
        </View>
      </View>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  titleRow: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  titleTextStyle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 22,
    color: 'black',
    flex: 1,
  },
  arrowStyle: {
    width: 30 * scale,
    height: 30 * scale,
    marginHorizontal: 12 * scale,
  },
  descriptionStyle: {
    fontFamily: 'Avenir-Book',
    fontSize: 13,
    lineHeight: scaleWidth(5),
    color: colors.subGrey,
    padding: 15,
    marginBottom: 16 * scale,
  },
};

TitleWithDropdown.propTypes = propTypes;
TitleWithDropdown.defaultProps = defaultProps;

export default TitleWithDropdown;
