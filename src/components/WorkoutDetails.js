import React from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Alert,
  Image,
  LayoutAnimation,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { connect } from 'react-redux';
import moment from 'moment';
import SortableSectionList, { CELLTYPE } from './SortableSectionList';
import Swipeable from './Swipeable';
import CorrectiveExerciseToggle from './CorrectiveExerciseToggle';
import {
  getExerciseVariables,
  getTempoStringForExercise,
  getSideStringForExercise,
  getRestTempoStringForExercise,
  abbreviateDistanceUnit,
  getDurationForSection,
} from '../util/programUtils';
import { curvedScale } from '../util/responsive';
import { androidSafeLayoutAnimation } from '../constants';

// Styles
import { colors } from '../styles';

// Redux
import {
  editWorkoutName,
  removeExercise,
  sortExercises,
  selectWorkout,
} from '../reducers/selectedWorkoutReducer';
import { programContexts } from '../reducers/programContextReducer';
import { enableCorrectiveExercises } from '../reducers/selectedProgramReducer';
import InformationBox from './InformationBox';
import getExerciseName from '../util/exerciseUtils';
import { removeAllSpecialCharacters } from '../util/validate';
import ROUTINE_TYPES from '../types/RoutineTypes';
import { debounce } from '../util/utils';

const mapStateToProps = ({
  selectedProgram,
  selectedWorkout,
  programContext,
  correctiveExercises,
  selectedClient,
  currentUser,
}) => ({
  // loading: selectedWorkout.loading,
  selectedProgram,
  selectedWorkout,
  program: selectedProgram.program,
  error: selectedWorkout.error,
  editable: selectedWorkout.editable,
  workout: selectedWorkout.workout,
  sections: selectedWorkout.entities?.sections,
  exercises: selectedWorkout.entities?.exercises,
  isWorkoutNameEditable: selectedWorkout.editable,
  programContext,
  correctiveExercises: correctiveExercises.exercises,
  selectedCorrectiveExercises: selectedProgram.correctiveExercises,
  correctiveExercisesEnabled: selectedProgram.correctiveExercisesEnabled,
  selectedClient,
  currentUser,
});
const mapDispatchToProps = {
  editWorkoutName,
  removeExercise,
  sortExercises,
  enableCorrectiveExercises,
  selectWorkout,
};

// Images
const removeIcon = require('../resources/imgClose.png');

// Props
const propTypes = {
  navigation: PropTypes.object.isRequired,
  sortExercises: PropTypes.func.isRequired,
  editable: PropTypes.bool.isRequired,
  sections: PropTypes.object.isRequired,
  exercises: PropTypes.any.isRequired,
  isWorkoutNameEditable: PropTypes.bool.isRequired,
  editWorkoutName: PropTypes.func.isRequired,
  removeExercise: PropTypes.func.isRequired,
  workout: PropTypes.object.isRequired,
  createWorkoutTemplate: PropTypes.func,
  programContext: PropTypes.string,
  selectedClient: PropTypes.object,
  enableCorrectiveExercises: PropTypes.func.isRequired,
  correctiveExercisesEnabled: PropTypes.bool.isRequired,
  correctiveExercises: PropTypes.object,
  currentUser: PropTypes.object.isRequired,
};

const defaultProps = {
  createWorkoutTemplate: null,
  programContext: '',
  selectedClient: null,
};

// Class
class WorkoutDetails extends React.Component {
  // TODO: looks very verbose, can probably be simpler
  static renderExerciseVariable(variable, variables, exercise, isActive) {
    let variableNameText = variable;
    let valueFirst = true;
    if (variableNameText === 'dur_seconds') {
      variableNameText = 'duration';
    }
    let measurementUnit = '';
    if (variable === 'rest' || variable === 'dur_seconds') {
      measurementUnit = 's';
    } else if (variable === 'weight') {
      measurementUnit = exercise.weight_units;
      variableNameText = '';
    } else if (variable === 'pace') {
      if (exercise.pace_units.toLowerCase() === 'watts') {
        variableNameText = 'W';
      } else {
        variableNameText = exercise.pace_units;
      }
    } else if (variable === 'distance') {
      variableNameText = abbreviateDistanceUnit(exercise.distance_units);
    }
    if (variable === 'tempo') {
      variables[variable] = getTempoStringForExercise({
        tempo: variables[variable],
      }).toLowerCase();
      valueFirst = false;
      variableNameText = 'Tempo';
    }
    if (variable === 'exercise_sides') {
      variableNameText = 'side';
      variables[variable] = getSideStringForExercise({
        exercise_sides: variables[variable],
      });
    }
    if (variable === 'rest_tempo') {
      variables[variable] = getRestTempoStringForExercise({
        rest_tempo: variables[variable],
      });
      variableNameText = 'Rest Tempo';
      valueFirst = false;
    }
    if (valueFirst) {
      return (
        <Text
          key={exercise.key + variable}
          style={
            isActive
              ? [styles.exerciseInfo, { fontSize: 13 }]
              : styles.exerciseInfo
          }
        >
          {`${variables[variable]}${measurementUnit} ${variableNameText}`}
        </Text>
      );
    }

    return (
      <Text
        key={exercise.key + variable}
        style={
          isActive
            ? [styles.exerciseInfo, { fontSize: 13 }]
            : styles.exerciseInfo
        }
      >
        {`${variableNameText} ${variables[variable]}${measurementUnit}`}
      </Text>
    );
  }

  constructor(props) {
    super(props);
    this.state = {
      swiping: false,
      sorting: false,
      workoutName: this.props.workout.name,
      isWorkoutEditable:
        this.props.programContext !== programContexts.SCHEDULING,
    };
    this.swipeableCells = {};
    // Use the common debounce function from utils
    this.debouncedEditWorkoutName = debounce(this.props.editWorkoutName, 400);
  }

  onPressAddExercise = (sectionId) => {
    this.props.navigation.navigate('AddExercises', { sectionId });
  };

  onPressDeleteExercise = (exercise, sectionId) => {
    this.recenterSwipableCell(exercise);
    Alert.alert(
      'Remove Exercise',
      `Are you sure you would like to remove ${getExerciseName(
        this.props.currentUser,
        exercise,
      )}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => this.deleteExercise(exercise, sectionId),
        },
      ],
      undefined,
    );
  };

  onPressExercise = (exercise, sectionId, exerciseContext) => {
    if (this.props.editable && this.state.isWorkoutEditable) {
      this.props.navigation.navigate('EditExercise', {
        exercise,
        key: exercise.key,
        sectionId,
        exerciseContext,
        schedule_date: exercise.schedule_date,
      });
    } else {
      this.props.navigation.navigate('ExerciseDetails', {
        exercise,
        key: exercise.key,
        isEditable: this.state.isWorkoutEditable,
        exerciseContext,
        schedule_date: exercise.schedule_date,
      });
    }
  };

  onPressSuperset = (superset, exerciseContext) => {
    this.props.navigation.navigate('CreateSuperSet', {
      isEditable: this.state.isWorkoutEditable,
      isEditMode: true,
      superset,
      exerciseContext,
    });
  };

  onPressCircuit = (circuit, exerciseContext) => {
    this.props.navigation.navigate('CreateCircuit', {
      isEditable: this.state.isWorkoutEditable,
      isEditMode: true,
      circuit,
      exerciseContext,
    });
  };

  onToggleCorrectiveExercises = async (enabled) => {
    if (this.props.selectedCorrectiveExercises?.length > 0) {
      await this.props.enableCorrectiveExercises(enabled);
      this.updateWorkoutFromSelectedProgram();
    } else {
      this.selectCorrectiveExercises();
    }
  };

  getSwipeButtonsForExercise = (exercise, index) => [
    <TouchableOpacity
      style={styles.removeButton}
      onPress={() => this.onPressDeleteExercise(
        exercise,
        this.list.getSectionForIndex(index).id,
      )}
      key="delete"
    >
      <View style={styles.swipeButtonContainer}>
        <Image style={styles.removeIconStyle} source={removeIcon} />
        <Text style={styles.swipeButtonText}>Remove</Text>
      </View>
    </TouchableOpacity>,
  ];

  getInformationBox() {
    const { SCHEDULING, RESCHEDULING, LIBRARY } = programContexts;
    const { workout, programContext, selectedClient } = this.props;
    if (programContext === SCHEDULING) {
      return (
        <InformationBox
          titleComponent={(
            <Text>
              You are viewing a preview of a
              <Text style={styles.infoText}> Workout.</Text>
            </Text>
          )}
          descComponent={(
            <Text>
              Once
              <Text style={styles.infoText}> Scheduled, </Text>
              you will be able to customize it.
            </Text>
          )}
        />
      );
    }
    if (programContext === RESCHEDULING) {
      return (
        <InformationBox
          titleComponent={(
            <Text>
              This is
              <Text style={styles.infoText}>
                {` ${selectedClient?.first_name}’s `}
              </Text>
              customized
              <Text style={styles.infoText}> Workout.</Text>
            </Text>
          )}
          descComponent={(
            <Text>
              Edits saved only apply to
              <Text style={styles.infoText}>
                {` ${selectedClient?.first_name}’s Schedule.`}
              </Text>
            </Text>
          )}
          lastModifiedDate={
            workout && workout.updated_at
              ? moment(workout.updated_at).format('M/D/YY h:mmA')
              : ''
          }
        />
      );
    }
    if (programContext === LIBRARY) {
      return (
        <InformationBox
          titleComponent={(
            <Text>
              This is a
              <Text style={styles.infoText}> Workout Template.</Text>
            </Text>
          )}
          descComponent="Saved changes do not apply to scheduled workouts."
          lastModifiedDate={
            workout && workout.updated_at
              ? moment(workout.updated_at).format('M/D/YY h:mmA')
              : ''
          }
        />
      );
    }
    return null;
  }

  updateWorkoutFromSelectedProgram = async () => {
    if (this.props.selectedWorkout?.workout) {
      const programWorkouts = Object.values(
        this.props.selectedProgram?.entities?.workouts,
      );
      if (programWorkouts) {
        const updatedWorkout = programWorkouts.find(
          (workout) => workout.id === this.props.selectedWorkout.workout.id,
        );
        if (updatedWorkout) {
          await this.props.selectWorkout(
            { ...updatedWorkout, name: this.state.workoutName },
            true,
            false,
          );
        }
      }
    }
  };

  selectCorrectiveExercises = () => {
    this.props.navigation.navigate('CorrectiveExercises', {
      updateWorkout: this.updateWorkoutFromSelectedProgram,
    });
  };

  deleteExercise = (exercise, sectionId) => {
    if (!exercise || !sectionId) {
      return;
    }
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    this.props.removeExercise({ exercise, sectionId });
  };

  recenterSwipableCell = (exercise) => {
    if (exercise && this.swipeableCells[exercise.id]) {
      this.swipeableCells[exercise.id].recenter();
    } else {
      Object.keys(this.swipeableCells).forEach((cell) => {
        cell.recenter();
      });
    }
  };

  sectionsUpdated = (sections) => {
    this.props.sortExercises(unflattenSections(sections));
    this.setState({ sorting: false }, () => {});
  };

  updateWorkoutName = (text) => {
    this.setState({ workoutName: text });
    this.debouncedEditWorkoutName(text);
  };

  createWorkoutTemplate = () => {
    if (this.props.createWorkoutTemplate) {
      this.props.createWorkoutTemplate();
    } else {
      Alert.alert('Error', 'Something went wrong! Please try again later.');
    }
  };

  renderHeader = () => {
    const workoutName = this.props.workout.name;
    if (this.props.isWorkoutNameEditable || this.state.isWorkoutEditable) {
      return (
        <View style={styles.optionsContainer}>
          <Text style={styles.label}>Workout Name</Text>
          <TextInput
            style={styles.nameInput}
            placeholder="Enter Workout Name"
            value={this.state.workoutName}
            onChangeText={(value) => {
              const filteredValue = removeAllSpecialCharacters(value);
              this.updateWorkoutName(filteredValue);
            }}
            returnKeyType="done"
            autoFocus={!workoutName || workoutName.length === 0}
          />
          {this.props.programContext === programContexts.SCHEDULING
            && this.props.correctiveExercises?.length > 0
            && this.props.navFrom !== 'program' && (
              <CorrectiveExerciseToggle
                style={styles.correctiveExerciseToggle}
                onPress={this.selectCorrectiveExercises}
                onToggle={this.onToggleCorrectiveExercises}
                enabled={this.props.correctiveExercisesEnabled}
              />
          )}
          {this.props.programContext === programContexts.RESCHEDULING ? (
            <TouchableOpacity
              onPress={this.createWorkoutTemplate}
              style={styles.btnView}
            >
              <View style={styles.btnInnerView}>
                <View style={styles.btnRow}>
                  <Image
                    source={require('../resources/btnCreate.png')}
                    resizeMode="contain"
                    style={styles.createImg}
                  />
                  <Text style={styles.createText}>
                    Create Template in Library
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ) : null}
          <Text style={styles.label}>Add Exercises</Text>
        </View>
      );
    }
    return (
      <View style={styles.optionsContainer}>
        <Text style={styles.label}>Workout Name</Text>
        <Text style={[styles.nameInput, { color: colors.black }]}>
          {workoutName || ''}
        </Text>
        <Text style={styles.label}>Exercises</Text>
      </View>
    );
  };

  renderRow = ({
    item, getIndex, drag, isActive,
  }) => {
    if (!item) return null;
    const index = getIndex();
    const exercise = this.props.exercises[item.key];
    const { exerciseContext } = this.props;

    if (!exercise) return null;
    let exerciseRoutineType = exercise.routine_type || exercise.compound_routine_type;
    if (!exerciseRoutineType && exercise.compound_routine_exercises?.length) {
      exerciseRoutineType = exercise.compound_routine_exercises[0]?.compound_routine_type;
    }
    const isSuperSet = exerciseRoutineType === ROUTINE_TYPES.SUPER_SET;
    const isCircuit = exerciseRoutineType === ROUTINE_TYPES.CIRCUIT;
    const variables = getExerciseVariables(exercise);
    const keys = Object.keys(variables);
    const mainRowView = (
      <TouchableOpacity
        key={exercise.key}
        onPress={() => {
          if (isSuperSet) {
            this.onPressSuperset(exercise, exerciseContext);
          } else if (isCircuit) {
            this.onPressCircuit(exercise, exerciseContext);
          } else {
            this.onPressExercise(
              exercise,
              this.list.getSectionForIndex(index).id,
              exerciseContext,
            );
          }
        }}
        onLongPress={
          this.props.editable && this.state.isWorkoutEditable ? drag : null
        }
      >
        <View
          style={[
            styles.exerciseRowContainer(isSuperSet, isCircuit),
            isActive ? styles.excerciseView : null,
          ]}
        >
          {/* Sort Gray Image */}
          {this.props.editable && this.state.isWorkoutEditable && (
            <Image
              style={{ tintColor: colors.subGrey }}
              source={require('../resources/sortGray.png')}
            />
          )}

          {/* Text Section */}
          <View style={styles.excerciseText}>
            <Text style={styles.exerciseName}>
              {getExerciseName(this.props.currentUser, exercise)}
            </Text>
            {!isSuperSet && !isCircuit ? (
              <View style={styles.excercise}>
                {keys.map((variable) => WorkoutDetails.renderExerciseVariable(
                  variable,
                  variables,
                  exercise,
                  isActive,
                ))}
              </View>
            ) : null}
            {isCircuit ? (
              <View style={styles.excercise}>
                <Text
                  key={`${exercise.key}rounds`}
                  style={
                    isActive
                      ? [styles.exerciseInfo, { fontSize: 13 }]
                      : styles.exerciseInfo
                  }
                >
                  {`${exercise.reps} rounds`}
                </Text>
              </View>
            ) : null}
          </View>
          {isSuperSet || isCircuit ? (
            <Text style={styles.duration}>
              {getDurationForSection({
                section: exercise,
                isSuperSetOrCircuit: isSuperSet || isCircuit,
              })}
            </Text>
          ) : null}
          {/* Right Arrow Image */}
          {this.props.editable && this.state.isWorkoutEditable && (
            <View style={styles.arrowView}>
              <Image
                style={styles.arrowImg}
                source={require('../resources/imgRightArrowGray.png')}
              />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
    if (!this.props.editable || !this.state.isWorkoutEditable) {
      return mainRowView;
    }
    return (
      <Swipeable
        key={exercise.key}
        ref={(ref) => {
          this.swipeableCells[exercise.id] = ref;
        }}
        rightButtons={this.getSwipeButtonsForExercise(exercise, index)}
        onSwipeStart={() => {
          this.setState({ sorting: false, swiping: true }, () => {
            this.list.setScrollEnabled(false);
          });
        }}
        onSwipeRelease={() => {
          this.setState({ swiping: false }, () => {
            this.list.setScrollEnabled(true);
          });
        }}
      >
        {mainRowView}
      </Swipeable>
    );
  };

  renderSectionHeader = (id) => {
    const section = this.props.sections[id];
    return (
      <View key={section.id} style={styles.sectionHeaderContainer}>
        <View style={styles.headerView}>
          <Text style={styles.sectionHeaderTitle}>{section.name}</Text>
        </View>
        <Text style={styles.duration}>
          {getDurationForSection({ section, exercises: this.props.exercises })}
        </Text>
        {this.props.editable && this.state.isWorkoutEditable && (
          <TouchableOpacity
            onPress={() => {
              this.onPressAddExercise(section.id);
            }}
            style={styles.addBtn}
          >
            <Image
              style={styles.addImg}
              resizeMode="contain"
              source={require('../resources/btnAddwhite.png')}
            />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  render() {
    return (
      <View style={styles.container}>
        {this.getInformationBox()}
        <SortableSectionList
          ref={(ref) => {
            this.list = ref;
          }}
          data={flattenSections(this.props.sections)}
          keyExtractor={(item, index) => `sortable-item-${index}`}
          renderRow={this.renderRow}
          sortingEnabled={this.props.editable && this.state.isWorkoutEditable}
          renderSectionHeader={this.renderSectionHeader}
          onMoveEnd={this.sectionsUpdated}
          renderHeader={this.renderHeader}
          // extraData={this.props}
          extraData={{
            editable: this.props.editable,
            isWorkoutEditable: this.state.isWorkoutEditable,
          }}
          sortExercises={this.props.sortExercises}
          activationDistance={20}
        />
      </View>
    );
  }
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  addImg: {
    width: 25,
    height: 25,
  },
  headerView: {
    flex: 1,
  },
  arrowView: {
    justifyContent: 'center',
  },
  arrowImg: {
    height: 18,
    width: 18,
  },
  excercise: {
    flexDirection: 'row',
    flex: 1,
    flexWrap: 'wrap',
  },
  excerciseText: {
    flex: 1,
    justifyContent: 'center',
  },
  excerciseView: {
    backgroundColor: colors.white,
    borderTopWidth: 2,
    borderBottomWidth: 2,
    borderColor: '#d7d7d7',
  },
  optionsContainer: {
    backgroundColor: colors.white,
    paddingTop: 25,
  },
  label: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    paddingHorizontal: 25,
    color: colors.subGrey,
    paddingBottom: 10,
  },
  nameInput: {
    borderBottomWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    marginBottom: 21,
    marginHorizontal: 20,
    paddingVertical: 7,
  },
  sectionHeaderContainer: {
    height: 60,
    borderColor: colors.subGreyLight,
    borderTopWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 28,
    paddingRight: 18,
    backgroundColor: colors.white,
  },
  sectionHeaderTitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 16,
    color: colors.black,
  },
  duration: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    color: colors.black,
    paddingRight: 12,
    alignSelf: 'center',
  },
  addBtn: {
    width: 33,
    height: 33,
    borderRadius: 16.5,
    backgroundColor: colors.macaroniAndCheese,
    justifyContent: 'center',
    alignItems: 'center',
  },
  exerciseRowContainer: (isSuperSet, isCircuit) => {
    let backgroundColor = colors.white;
    if (isSuperSet) {
      backgroundColor = colors.mildGreenWithOpacity;
    } else if (isCircuit) {
      backgroundColor = colors.mildYellowWithOpacity;
    }
    return {
      backgroundColor,
      borderTopWidth: 1,
      borderColor: colors.subGreyLight,
      paddingLeft: 28,
      paddingRight: 30,
      paddingVertical: 10,
      flexDirection: 'row',
    };
  },
  exerciseName: {
    fontFamily: 'Avenir-Roman',
    fontSize: 16,
    color: 'black',
  },
  exerciseInfo: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    color: colors.subGreyTwo,
    marginRight: 20,
  },
  removeButton: {
    backgroundColor: colors.dustyRed,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeIconStyle: {
    width: 18,
    height: 18,
  },
  swipeButtonContainer: {
    flex: 1,
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  swipeButtonText: {
    fontFamily: 'Avenir-Bold',
    fontSize: 12,
    color: colors.white,
    marginTop: 3,
  },
  deleteButtonContainer: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderColor: colors.subGreyLight,
  },
  deleteButtonStyle: {
    width: '60%',
    marginTop: 15,
    marginBottom: 10,
    height: curvedScale(36),
    borderRadius: curvedScale(18),
    borderWidth: 1,
    borderColor: colors.badRed,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  deleteButtonText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 12,
    fontWeight: '700',
    color: colors.badRed,
    paddingHorizontal: 20,
    paddingVertical: 7,
  },
  btnView: {
    paddingBottom: 25,
    paddingHorizontal: 25,
    alignItems: 'center',
  },
  btnInnerView: {
    borderColor: colors.fillDarkGrey,
    padding: 5,
    paddingHorizontal: 20,
    borderWidth: 2,
    borderRadius: 50,
  },
  btnRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  createImg: {
    height: 15,
    width: 15,
  },
  createText: {
    fontSize: 14,
    fontFamily: 'Avenir-Heavy',
    color: colors.fillDarkGrey,
    marginLeft: 10,
  },
  infoText: {
    fontFamily: 'Avenir-Black',
  },
  correctiveExerciseToggle: {
    marginBottom: 30,
    marginHorizontal: 20,
  },
};

// Export
WorkoutDetails.propTypes = propTypes;
WorkoutDetails.defaultProps = defaultProps;
export default connect(mapStateToProps, mapDispatchToProps)(WorkoutDetails);

// helper functions
const flattenSections = (sectionMap) => {
  const sections = Object.values(sectionMap);
  if (!sections || sections.length === 0) {
    return [];
  }

  const data = [];
  sections.forEach((section) => {
    data.push({
      id: section.id,
      type: CELLTYPE.HEADER,
    });

    if (section.exercises) {
      section.exercises.forEach((item) => {
        data.push({
          key: item,
          type: CELLTYPE.ROW,
        });
      });
    }
  });
  return data;
};

const unflattenSections = (data) => {
  const sections = [];
  data.forEach((cell) => {
    if (cell.type === CELLTYPE.HEADER) {
      sections.push({ id: cell.id, exercises: [] });
    } else {
      sections[sections.length - 1].exercises.push(cell.key);
    }
  });

  return sections;
};
