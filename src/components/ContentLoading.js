import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { ActivityIndicator, Text, View, StyleSheet } from 'react-native';

// Styles
import { colors } from '../styles';



// Class
class ContentLoading extends Component {
  render() {
    if (this.props.visible) {
      return (
        <View style={styles.container}>
          <ActivityIndicator
            animating={true}
            color={colors.subGrey}
            size="large"
          />
        </View>
      );
    }
    return null;
  }
}

export default ContentLoading;

// Styles
const styles = StyleSheet.create({
  container: {
    flex:2,
    justifyContent: 'center',
    alignItems: 'center',
    height: 325,
    display: 'flex',
    backgroundColor: colors.white,
  },
});
