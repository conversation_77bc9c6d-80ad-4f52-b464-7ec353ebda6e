import React from 'react';

// Components
import { View } from 'react-native';
import Video from 'react-native-video';

// Class
class VideoWithErrorHandling extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      videoValid: false,
    };
  }

  componentDidMount() {
    this.checkSource(this.props.source);
  }

  componentDidUpdate(prevProps) {
    if (
      JSON.stringify(this.props.source) !== JSON.stringify(prevProps.source)
    ) {
      this.checkSource(this.props.source);
    }
  }

  checkSource(source) {
    let errorMessage = null;
    if (!source || !source.uri) {
      errorMessage = 'No video source found';
    } else if (source.uri && !this.checkUri(source.uri)) {
      errorMessage = 'Invalid video url format';
    }
    if (errorMessage !== null) {
      this.props.onError(new Error(errorMessage));
    }
    this.setState({ videoValid: errorMessage === null });
  }

  checkUri(uri) {
    const pattern = /(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-/]))?/;
    return pattern.test(uri);
  }

  render() {
    if (this.state.videoValid) {
      return (
        <Video
          source={this.props.source} // Can be a URL or a local file.
          ref={(ref) => {
            this.props.videoRef(ref);
          }} // Store reference
          rate={this.props.rate} // 0 is paused, 1 is normal.
          volume={this.props.volume} // 0 is muted, 1 is normal.
          muted={this.props.muted} // Mutes the audio entirely.
          paused={this.props.paused} // Pauses playback entirely.
          repeat={this.props.repeat} // Repeat forever.
          playInBackground={this.props.playInBackground} // Audio continues to play when app entering background.
          playWhenInactive={this.props.playWhenInactive} // [iOS] Video continues to play when control or notification center are shown.
          disableFocus={this.props.disableFocus} // [Android] Video audio is allowed to play in the background from other apps.
          ignoreSilentSwitch={this.props.ignoreSilentSwitch} // [iOS] "obey" will allow audio to play in the background, "ignore" will stop audio from playing in the background.
          progressUpdateInterval={this.props.progressUpdateInterval} // [iOS] Interval to fire onProgress (default to ~250ms)
          onLoad={this.props.onLoad} // Callback when video loads
          onProgress={this.props.onProgress} // Callback every ~250ms with currentTime
          onEnd={this.props.onEnd} // Callback when playback finishes
          onError={this.props.onError} // Callback when video cannot be loaded
          onBuffer={this.props.onBuffer} // Callback when remote video is buffering
          resizeMode={this.props.resizeMode}
          fullscreen={this.props.fullscreen}
          fullscreenOrientation={this.props.fullscreenOrientation}
          style={this.props.style}
          controls={false}
        />
      );
    }
    return <View style={{ backgroundColor: 'rgb(0,0,0)', height: 207 }} />;
  }
}

// Export
export default VideoWithErrorHandling;
