import React, { Component } from 'react';

// Components
import {
  View,
  TouchableOpacity,
  Image,
  Text,
  ImageBackground,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';

import { scaleHeight, scaleWidth } from '../util/responsive';

// Styles
import { colors } from '../styles';

class AvatarFlashCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      cardTitle: this.props.cardTitle,
    };
  }

  practiceSectionCardFunction = () => {
    if (this.props.visible) {
      return (
        <View style={styles.cardContainer}>
          <TouchableOpacity
            style={{ alignItems: 'center', justifyContent: 'center' }}
            onPress={() => {
              this.props.onClick();
            }}
          >
            <ImageBackground
              source={this.props.image}
              style={styles.connectBgImage}
              resizeMode="stretch"
            >
              <View style={styles.contentCard}>
                <Image
                  source={this.props.logo}
                  style={{ width: 50, height: 50 }}
                />
                <Text style={styles.connectText}>{this.state.cardTitle}</Text>
                <IconFeader
                  name="chevron-right"
                  size={25}
                  color={colors.subGrey}
                />
              </View>
            </ImageBackground>
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  };

  render() {
    return this.practiceSectionCardFunction();
  }
}

const styles = {
  cardContainer: {
    marginHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 50,
  },
  contentCard: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingLeft: scaleWidth(9),
    justifyContent: 'center',
    flex: 1,
    paddingRight: scaleWidth(6),
  },
  connectText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 18,
    color: colors.black,
    paddingLeft: 25,
    flex: 1,
  },
  connectBgImage: {
    width: scaleWidth(85),
    height: scaleHeight(14),
  },
};

export default AvatarFlashCard;
