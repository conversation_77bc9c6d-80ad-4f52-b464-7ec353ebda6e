import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { View } from 'react-native';
import Button from './Button';
import ScaledText from './ScaledText';
import { curvedScale, scaleWidth } from '../util/responsive';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  buttonTitle: PropTypes.string.isRequired,
  enabled: PropTypes.bool,
  buttonDisabled: PropTypes.bool,
  isLoading: PropTypes.bool,
  onPress: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  viewStyle: PropTypes.viewStyle,
};
const defaultProps = {
  enabled: true,
  buttonDisabled: false,
  isLoading: false,
};

class BottomBannerActionView extends Component {
  render() {
    return (
      <View style={[styles.viewStyle, this.props.viewStyle]}>
        <View style={styles.titleContainer}>
          <ScaledText style={styles.textStyle}>{this.props.title}</ScaledText>
        </View>
        <Button
          buttonStyle={styles.buttonStyle}
          containerStyle={styles.buttonContainerStyle}
          isLoading={this.props.isLoading}
          loadingSpinnerSize="small"
          loadingSpinnerBackgroundColor={colors.medYellow}
          onPress={this.props.onPress}
          textStyles={styles.buttonTextStyle}
          title={this.props.buttonTitle}
          disabled={this.props.buttonDisabled}
        />
      </View>
    );
  }
}

BottomBannerActionView.propTypes = propTypes;
BottomBannerActionView.defaultProps = defaultProps;

export default BottomBannerActionView;

const styles = {
  buttonContainerStyle: {
    alignSelf: 'center',
  },
  buttonStyle: {
    backgroundColor: 'transparent',
    borderColor: 'white',
    borderRadius: scaleWidth(7),
    borderWidth: 1,
    elevation: 0,
    shadowOpacity: 0,
    minHeight: '40%',
    minWidth: '30%',
  },
  buttonTextStyle: {
    color: 'white',
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 16,
    marginRight: 16,
  },
  textStyle: {
    color: 'white',
    fontFamily: 'Avenir-Heavy',
    fontSize: 18,
    fontWeight: 'bold',
  },
  viewStyle: {
    backgroundColor: colors.medYellow,
    flexDirection: 'row',
    height: curvedScale(110),
    justifyContent: 'space-around',
    width: '100%',
    padding: 20,
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    marginRight: 5,
  },
};
