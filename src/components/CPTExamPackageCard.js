import React, { Component } from 'react';

// Components
import {
  View, TouchableOpacity, Image, Text,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';

import AwesomeAlert from 'react-native-awesome-alerts';
import ScaledText from './ScaledText';
import { scaleWidth } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';

// Images
const image = require('../resources/imgPackageExamPrep.png');

class CPTExamPackageCard extends Component {
  state = {
    dismissPressed: false,
    showAlert: false,
  };

  // dismissAlertHandler = () => {
  //   this.setState({ dismissPressed: true });
  // };

  handleCancel = () => this.setState({ showAlert: false });

  handleConfirm = () => {
    this.setState({ showAlert: false });
    // openStore();
  };
  // openStore = () => {
  //   const iosAppStoreUrl = 'NEW NASM APP store Link';
  //   const androidPlayStoreUrl = 'NEW NASM Playstore store Link';

  //   const storeUrl =
  //     Platform.OS === 'ios' ? iosAppStoreUrl : androidPlayStoreUrl;

  //   Linking.openURL(storeUrl).catch(err => {
  //     console.warn('Failed to open store URL:', err);
  //   });
  // };

  exampackagefunction = () => {
    if (this.props.visible) {
      return (
        <View>
          <TouchableOpacity
            style={styles.needHelpContainer}
            onPress={() => {
              this.setState({ showAlert: true });
            }}
          >
            <Image source={image} style={styles.bkgImage} />
            <View style={styles.topContainer}>
              <View style={styles.headingBkg}>
                <ScaledText style={styles.headingText}>
                  Exam Prep Package
                </ScaledText>
              </View>
            </View>
            <View style={styles.content}>
              <Text style={styles.needHelpText}>
                {'Everything you need to\nprep for your CPT exam'}
              </Text>
              <View>
                <IconFeader
                  name="chevron-right"
                  size={25}
                  color={colors.white}
                />
              </View>
            </View>
          </TouchableOpacity>
          <AwesomeAlert
            show={this.state.showAlert}
            showProgress={false}
            useNativeDriver
            title="Exam prep has moved."
            message="You can now access it through the Customer Portal or the new NASM App. It’s no longer available in the Edge app."
            closeOnTouchOutside
            closeOnHardwareBackPress={false}
            showConfirmButton
            showCancelButton
            titleStyle={styles.titleStyle}
            messageStyle={styles.messageStyle}
            confirmText="Download NASM App"
            cancelText="Cancel"
            confirmButtonTextStyle={styles.confirmButtonTextStyle}
            confirmButtonStyle={styles.alertButton}
            cancelButtonTextStyle={styles.confirmButtonTextStyle}
            cancelButtonStyle={styles.alertButton}
            actionContainerStyle={styles.alertActionContainer}
            contentContainerStyle={styles.contentContainerStyle}
            onCancelPressed={this.handleCancel}
            onConfirmPressed={this.handleConfirm}
          />
        </View>
      );
    }
    return null;
  };

  render() {
    if (this.state.dismissPressed) {
      return null;
    }

    return this.exampackagefunction();
  }
}

const styles = {
  needHelpContainer: {
    flex: 1,
    width: scaleWidth(85),
    marginRight: scaleWidth(2.5),
    borderRadius: 16,
    ...shadow,
  },
  bkgImage: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    borderRadius: 16,
  },
  topContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  headingBkg: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderTopLeftRadius: 16,
    borderBottomRightRadius: 7,
    backgroundColor: 'rgba(36, 81, 135, 1)',
    justifyContent: 'center',
  },
  headingText: {
    fontFamily: 'Avenir',
    fontWeight: '900',
    fontSize: 14,
    textAlign: 'center',
    color: colors.white,
  },
  dismissBtn: {
    height: 40,
    justifyContent: 'center',
    right: '30%',
    top: '2%',
  },
  dismissBtnText: {
    fontFamily: 'Avenir',
    fontWeight: '900',
    fontSize: 15,
    textAlign: 'right',
    color: colors.white,
  },
  content: {
    marginVertical: '5%',
    marginLeft: '5%',
    marginRight: '3%',
    flexDirection: 'row',
  },
  needHelpText: {
    fontFamily: 'Avenir',
    fontWeight: 'bold',
    fontSize: 18,
    flex: 1,
    color: colors.white,
  },
  learnMoreBtn: {
    height: '16%',
    width: '38%',
    borderRadius: scaleWidth(10),
    marginTop: 15,
    justifyContent: 'center',
    borderColor: colors.white,
    borderWidth: 2,
  },
  learnMoreText: {
    fontFamily: 'Avenir',
    textAlign: 'center',
    fontSize: 16,
    color: colors.white,
  },
  titleText: {
    fontWeight: 'bold',
    fontSize: 18,
    color: colors.white,
    marginLeft: 15,
  },

  titleStyle: {
    fontSize: 20,
    fontFamily: 'SFProText-Medium',
    color: colors.black,
    textAlign: 'center',
  },
  messageStyle: {
    color: colors.fillDarkGrey,
    fontSize: 14,
    fontFamily: 'Avenir-Roman',
    textAlign: 'center',
    marginVertical: 10,
    marginHorizontal: 16,
  },
  confirmButtonTextStyle: {
    color: colors.fillDarkGrey,
    fontSize: 14,
    fontFamily: 'Avenir-Heavy',
  },
  innerContainerStyle: {
    paddingHorizontal: 25,
  },
  alertContainerStyle: {
    paddingHorizontal: 0,
  },
  contentStyle: {
    borderBottomWidth: 1,
    borderColor: colors.actionSheetDivider,
  },
  contentContainerStyle: {
    paddingHorizontal: 0,
  },
  alertButton: {
    width: '100%',
    height: 36,
    borderTopWidth: 1,
    borderTopColor: colors.lightgrey,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 0,
    marginHorizontal: 0,
  },
  alertActionContainer: {
    alignItems: 'stretch',
    flexDirection: 'column-reverse',
    backgroundColor: colors.white,
    borderRadius: 5,
  },
};

export default CPTExamPackageCard;
