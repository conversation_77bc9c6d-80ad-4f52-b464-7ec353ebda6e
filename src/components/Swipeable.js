import React, { PureComponent } from 'react';
import { View, Animated, StyleSheet } from 'react-native';
import { PropTypes } from 'prop-types';
import { Swipeable as SwipeableView } from 'react-native-gesture-handler';

const defaultProps = {
  children: null,
  rightContent: null,
  rightButtons: null,
  onRightActionRelease: null,
  rightButtonContainerStyle: null,
  enabled: true,
};

const propTypes = {
  children: PropTypes.any,
  rightContent: PropTypes.any,
  rightButtons: PropTypes.array,
  onRightActionRelease: PropTypes.func,
  rightButtonContainerStyle: PropTypes.object,
  enabled: PropTypes.boolean,
};

class Swipeable extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
    this.swipeableRef = null;
  }

  recenter = () => {
    this.swipeableRef?.close();
  };

  updateRef = (ref) => {
    this.swipeableRef = ref;
  };

  renderRightActions = (progress) => {
    const { rightContent, rightButtons, rightButtonContainerStyle } = this.props;
    const maxWidth = 80 * rightButtons?.length || 120;
    const trans = progress.interpolate({
      inputRange: [0, 1],
      outputRange: [maxWidth, 0],
    });
    const buttonStyle = [{ width: maxWidth }, rightButtonContainerStyle];
    return (
      <View style={buttonStyle}>
        <Animated.View style={styles.buttonsContainer(trans)}>
          <View style={styles.buttonsView}>{rightContent || rightButtons}</View>
        </Animated.View>
      </View>
    );
  };

  render() {
    const {
      children, rightContent, onRightActionRelease, enabled,
    } = this.props;
    return (
      <SwipeableView
        ref={this.updateRef}
        enabled={enabled}
        enableTrackpadTwoFingerGesture
        renderRightActions={this.renderRightActions}
        onSwipeableOpen={(direction) => {
          if (direction === 'right' && rightContent && onRightActionRelease) {
            onRightActionRelease();
            this.swipeableRef?.close();
          }
        }}
      >
        {children}
      </SwipeableView>
    );
  }
}

Swipeable.propTypes = propTypes;
Swipeable.defaultProps = defaultProps;
export default Swipeable;

const styles = StyleSheet.create({
  buttonsContainer: (trans) => ({
    flex: 1,
    transform: [{ translateX: trans }],
  }),
  buttonsView: {
    flex: 1,
    flexDirection: 'row',
  },
});
