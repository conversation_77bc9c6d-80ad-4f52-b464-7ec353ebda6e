import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { StyleSheet, View } from 'react-native';

// Styles
import { colors, shadow } from '../styles';

// PropTypes
const propTypes = {
  currentPage: PropTypes.number.isRequired,
  maxPages: PropTypes.number.isRequired,
};

// Class
class ExamProgressBar extends Component {
  render() {
    return (
      <View style={styles.barContainer}>
        {[...Array(this.props.maxPages)].map((_, b) => (
          <View
            key={`bar${b}`}
            style={
                b < this.props.currentPage
                  ? styles.completed
                  : styles.incomplete
              }
          />
        ))}
      </View>
    );
  }
}

// Export
ExamProgressBar.propTypes = propTypes;
export default ExamProgressBar;

// Styles
const styles = StyleSheet.create({
  barContainer: {
    height: '1.5%',
    flexDirection: 'row',
    backgroundColor: colors.nasmBlue,
    ...shadow,
  },
  completed: {
    flex: 1,
    height: '100%',
    backgroundColor: colors.macaroniAndCheese,
  },
  incomplete: {
    flex: 2,
    height: '100%',
    backgroundColor: colors.nasmBlue,
  },
});
