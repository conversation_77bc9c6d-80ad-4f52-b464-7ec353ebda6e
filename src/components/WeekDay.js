import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import {
  Text, View, LayoutAnimation, TouchableOpacity,
} from 'react-native';
import styles from 'react-native-calendar-strip/src/Calendar.style';
import { colors } from '../styles';

const propTypes = {
  date: PropTypes.object.isRequired,
  onDateSelected: PropTypes.func.isRequired,
  selected: PropTypes.bool.isRequired,
  enabled: PropTypes.bool.isRequired,

  marking: PropTypes.any,
  markedDates: PropTypes.array,

  showDayName: PropTypes.bool,
  showDayNumber: PropTypes.bool,
  dateNameStyle: PropTypes.any,
  dateNumberStyle: PropTypes.any,
  weekendDateNameStyle: PropTypes.any,
  weekendDateNumberStyle: PropTypes.any,
  highlightDateNameStyle: PropTypes.any,
  highlightDateNumberStyle: PropTypes.any,
  disabledDateNameStyle: PropTypes.any,
  disabledDateNumberStyle: PropTypes.any,
  markedDatesStyle: PropTypes.object,
  disabledDateOpacity: PropTypes.number,
  styleWeekend: PropTypes.bool,
  customStyle: PropTypes.object,

  daySelectionAnimation: PropTypes.object,
  allowDayTextScaling: PropTypes.bool,
  isDateDisabled: PropTypes.bool,
};

// Reference: https://medium.com/@Jpoliachik/react-native-s-layoutanimation-is-awesome-4a4d317afd3e
const defaultProps = {
  daySelectionAnimation: {
    type: '', // animations disabled by default
    duration: 300,
    borderWidth: 1,
    borderHighlightColor: colors.black,
    highlightColor: 'yellow',
    animType: LayoutAnimation.Types.easeInEaseOut,
    animUpdateType: LayoutAnimation.Types.easeInEaseOut,
    animProperty: LayoutAnimation.Properties.opacity,
    animSpringDamping: undefined, // Only applicable for LayoutAnimation.Types.spring,
  },
  styleWeekend: true,
  showDayName: true,
  showDayNumber: true,
  isDateDisabled: false,
};

class WeekDay extends Component {
  constructor(props) {
    super(props);

    this.state = {
      selected: props.selected,
    };
  }

  componentDidUpdate(prevProps) {
    const newState = {};
    let doStateUpdate = false;

    if (this.props.selected !== prevProps.selected) {
      newState.selected = this.props.selected;
      doStateUpdate = true;
    }

    if (doStateUpdate) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState(newState);
    }
  }

  renderDots() {
    const dotStyle = {
      width: 4,
      height: 4,
      marginTop: 1,
      marginHorizontal: 1.5,
      borderRadius: 2,
      opacity: 0,
    };
    if (!this.props.markedDates || this.props.markedDates.length === 0) {
      return <View style={dotStyle} />;
    }
    const marking = this.props.marking || {};
    const baseDotStyle = [dotStyle, styles.visibleDot];
    const markedDatesStyle = this.props.markedDatesStyle || {};
    let validDots = <View style={styles.dot} />; // default empty view for no dots case

    if (
      marking.dots
      && Array.isArray(marking.dots)
      && marking.dots.length > 0
    ) {
      // Filter out dots so that we we process only those items which have key and color property
      validDots = marking.dots
        .filter((d) => d && d.color)
        .map((dot, index) => (
          <View
            key={dot.key ? dot.key : index}
            style={[
              baseDotStyle,
              {
                backgroundColor:
                  marking.selected && dot.selectedDotColor
                    ? dot.selectedDotColor
                    : dot.color,
              },
              markedDatesStyle,
            ]}
          />
        ));
      return <View style={styles.dotsContainer}>{validDots}</View>;
    }

    return <View style={dotStyle} />;
  }

  render() {
    // Defaults for disabled state
    let dateNameStyle = [
      styles.dateName,
      this.props.enabled
        ? this.props.dateNameStyle
        : this.props.disabledDateNameStyle,
    ];
    let dateNumberStyle = [
      styles.dateNumber,
      this.props.enabled
        ? this.props.dateNumberStyle
        : this.props.disabledDateNumberStyle,
    ];
    const dateViewStyle = this.props.enabled
      ? [{ backgroundColor: colors.transparent }]
      : [{ opacity: this.props.disabledDateOpacity }];

    const { customStyle } = this.props;
    if (customStyle) {
      dateNameStyle.push(customStyle.dateNameStyle);
      dateNumberStyle.push(customStyle.dateNumberStyle);
      dateViewStyle.push(customStyle.dateContainerStyle);
    }
    if (this.props.enabled && this.state.selected) {
      // Enabled state
      // The user can disable animation, so that is why I use selection type
      // If it is background, the user have to input colors for animation
      // If it is border, the user has to input color for border animation
      switch (this.props.daySelectionAnimation.type) {
        case 'background':
          dateViewStyle.push({
            backgroundColor: this.props.daySelectionAnimation.highlightColor,
          });
          break;
        case 'border':
          dateViewStyle.push({
            borderColor: this.props.daySelectionAnimation.borderHighlightColor,
            borderWidth: this.props.daySelectionAnimation.borderWidth,
          });
          break;
        default:
          // No animation styling by default
          break;
      }

      dateNameStyle = [styles.dateName, this.props.dateNameStyle];
      dateNumberStyle = [styles.dateNumber, this.props.dateNumberStyle];
      if (
        this.props.styleWeekend
        && (this.props.date.isoWeekday() === 6
          || this.props.date.isoWeekday() === 7)
      ) {
        dateNameStyle = [
          styles.weekendDateName,
          this.props.weekendDateNameStyle,
        ];
        dateNumberStyle = [
          styles.weekendDateNumber,
          this.props.weekendDateNumberStyle,
        ];
      }
      if (this.state.selected) {
        dateNameStyle = [styles.dateName, this.props.highlightDateNameStyle];
        dateNumberStyle = [
          styles.dateNumber,
          this.props.highlightDateNumberStyle,
        ];
      }
    } else if (moment().isSame(this.props.date, 'day')) {
      dateNumberStyle.push({ color: colors.black });
    }

    return (
      <TouchableOpacity
        onPress={() => {
          this.props.onDateSelected(this.props.date);
        }}
        disabled={this.props.isDateDisabled}
      >
        <View key={this.props.date} style={styles.dateContainer}>
          {this.props.showDayName && (
            <Text
              style={[dateNameStyle, { fontSize: 12 }]}
              allowFontScaling={this.props.allowDayTextScaling}
            >
              {this.props.date.format('ddd').toUpperCase()}
            </Text>
          )}
          {this.props.showDayNumber && (
            <View
              style={[
                dateViewStyle,
                {
                  width: 33,
                  height: 33,
                  justifyContent: 'center',
                  borderRadius: 16.5,
                  marginTop: 10,
                  marginBottom: 3,
                },
              ]}
            >
              <Text
                style={[
                  { fontSize: this.state.dateNumberFontSize },
                  dateNumberStyle,
                ]}
                allowFontScaling={this.props.allowDayTextScaling}
              >
                {this.props.date.date()}
              </Text>
            </View>
          )}
          {this.renderDots()}
        </View>
      </TouchableOpacity>
    );
  }
}

WeekDay.propTypes = propTypes;
WeekDay.defaultProps = defaultProps;
export default WeekDay;
