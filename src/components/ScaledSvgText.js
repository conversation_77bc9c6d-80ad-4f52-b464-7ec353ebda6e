import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

// Components
import { Text } from 'react-native-svg';

const defaultProps = { scaleFactor: 0.5 };
const propTypes = { scaleFactor: PropTypes.number };

class ScaledSvgText extends PureComponent {
  render() {
    const { fontSize, children, ...props } = this.props;
    return <Text {...props}>{children}</Text>;
  }
}

ScaledSvgText.propTypes = propTypes;
ScaledSvgText.defaultProps = defaultProps;
export default ScaledSvgText;
