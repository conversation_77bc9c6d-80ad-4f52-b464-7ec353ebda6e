/* eslint-disable react/prop-types */
/* eslint-disable react/no-unused-state */
import React, { Component } from 'react';
import {
  TouchableOpacity,
  Image,
  Text,
  View,
  Alert,
  Dimensions,
  Platform,
  StyleSheet,
} from 'react-native';
import Video from 'react-native-video';
import axios from 'axios';
import addIcon from '../resources/btnAddwhite.png';
import { colors } from '../styles';
import ProgressBar from './ProgressBar';
import nasm from '../dataManager/apiConfig';
import playButton from '../resources/videoPlayButton.png';
import editButtonIcon from '../resources/editButtonIcon.png';
import { logComponentException } from '../util/logging';
import VideoSnapshot from '../util/videoSnapshot';
import VideoCompressor from '../util/videoCompressor'; // eslint-disable-line

import ProcessingVideo from './ProcessingVideo';
import MediaPicker, { MEDIA_SOURCE_TYPE } from '../util/MediaPicker.utils';

const { CancelToken } = axios;
const UploaderState = Object.freeze({
  Empty: 'empty',
  Validate: 'validate',
  Uploading: 'uploading',
  Processing: 'processing',
  Finished: 'finished',
});

export default class MediaUploader extends Component {
  constructor(props) {
    super(props);
    const fileInfo = this.getInitialFileInfo();
    this.state = {
      uploadProgress: 0,
      fileSelected: fileInfo.fileSelected,
      isUploading: false,
      uploaderState: fileInfo.uploaderState,
      videoPaused: true,
      fileType: fileInfo.fileType,
      processingProgress: 0,
    };
    this.cancelToken = null;
  }

  shouldComponentUpdate(nextProps, nextState) {
    if (this.state !== nextState) {
      return true;
    }
    return false;
  }

  handleUploadError(err) {
    if (VideoCompressor.isCancel(err)) return;
    if (axios.isCancel(err)) return;
    this.props.onMediaUploading(false);
    this.resetUploadState();
    err.upload_progress = this.state.uploadProgress;
    logComponentException('MediaUploader', err);
    if (err.message) {
      // Showing more specific error message
      Alert.alert('Upload error', err.message);
    } else {
      Alert.alert(
        'Upload error',
        'Your file could not be uploaded, please try again.',
      );
    }
  }

  async handleImageFile(file, fileType) {
    const isLandscape = await this.isImageLandscape(file);
    if (!isLandscape) {
      this.resetUploadState();
      Alert.alert(
        'Image Error',
        'Only images in landscape orientation are supported at this time.',
      );
      return;
    }
    this.setState(
      {
        fileSelected: file,
        fileType,
      },
      this.startImageUpload,
    );
  }

  onAddMediaPressed = () => {
    this.props.navigation.navigate('ActionSheet', {
      title: 'Edit',
      actions: [
        {
          text: 'Photo',
          onPress: () => this.onSelectFile(true),
        },
        {
          text: 'Video',
          onPress: () => this.onSelectFile(),
        },
      ],
    });
  };

  onEditPressed = () => {
    if (!this.props.navigation) return;
    const actions = [];
    actions.push({
      text: 'Replace with photo',
      onPress: () => this.onSelectFile(true),
    });
    actions.push({
      text: 'Replace with video',
      onPress: () => this.onSelectFile(),
    });
    if (!this.isDisplayingInitialImage()) {
      actions.push({
        text: 'Delete',
        onPress: () => {
          // TODO: Check if we can delete the uploaded file
          this.resetUploadState();
          if (this.props.onMediaDeleted) {
            this.props.onMediaDeleted();
          }
        },
      });
    }
    this.props.navigation.navigate('ActionSheet', {
      title: 'Edit',
      actions,
    });
  };

  onSelectFile = async (photoOnly) => {
    try {
      const file = await this.showPicker(photoOnly);
      const [fileType] = file.type.split('/', 1);
      if (fileType === 'image') {
        this.handleImageFile(file, fileType);
      } else {
        this.setState({
          uploaderState: UploaderState.Validate,
          fileSelected: file,
          fileType,
        });
      }
    } catch (err) {
      this.resetUploadState();
      if (err.code && err.code === 'DOCUMENT_PICKER_CANCELED') return;
      logComponentException('MediaUploader', err);
      Alert.alert(
        'Upload error',
        'Your file could not be uploaded, please try again.',
      );
    }
  };

  onUploadProgress = (progressEvent) => {
    const uploadProgress = Math.round(
      (progressEvent.loaded * 100) / progressEvent.total,
    );
    this.setState({ uploadProgress });
  };

  getInitialFileInfo = () => {
    const fileSelected = {};
    let fileType = '';
    let uploaderState = UploaderState.Empty;

    if (this.props.initialVideoUrl) {
      fileSelected.uri = this.props.initialVideoUrl;
      fileType = 'video';
      uploaderState = UploaderState.Finished;
    } else if (this.props.initialImageUrl) {
      fileSelected.uri = this.props.initialImageUrl;
      fileType = 'image';
      uploaderState = UploaderState.Finished;
    }

    return { fileSelected, fileType, uploaderState };
  };

  isImageLandscape = (file) => new Promise((res, rej) => {
    Image.getSize(
      file.uri,
      (width, height) => {
        res(width > height);
      },
      rej,
    );
  });

  showPicker = async (photoOnly) => {
    try {
      const options = {
        mediaType: photoOnly ? 'photo' : 'video',
      };
      const file = await MediaPicker.getMedia(MEDIA_SOURCE_TYPE.GALLERY, options);
      const data = {
        uri: file.uri,
        name: file.filename,
        type: file.mime,
      };
      return data;
    } catch (error) {
      Alert.alert('Failed to Change Avatar', error.message);
      return null;
    }
  };

  isDisplayingInitialImage = () => {
    if (
      this.props.initialImageUrl === this.state.fileSelected.uri
      || this.props.initialVideoUrl === this.state.fileSelected.uri
    ) {
      return true;
    }
    return false;
  };

  resetUploadState = () => {
    const fileInfo = this.getInitialFileInfo();
    this.props.onMediaUploading(false);
    this.setState({
      fileSelected: fileInfo.fileSelected,
      uploaderState: fileInfo.uploaderState,
      fileType: fileInfo.fileType,
    });
  };

  cancelUpload() {
    if (
      this.state.uploaderState === UploaderState.Uploading
      && this.cancelToken
    ) {
      this.cancelToken.cancel('UPLOAD_CANCELED');
    }
  }

  cancelCompression() {
    if (
      Platform.OS === 'android'
      && this.state.uploaderState === UploaderState.Processing
    ) {
      VideoCompressor.cancelCompression();
    }
  }

  async startImageUpload() {
    try {
      this.setState({ uploaderState: UploaderState.Uploading });
      this.props.onMediaUploading(true);
      this.cancelToken = CancelToken.source();
      const res = await nasm.api.uploadImage(
        this.state.fileSelected,
        this.onUploadProgress,
        this.cancelToken.token,
      );
      this.props.onUploadCompleted(res, this.state.fileType);
      this.setState({
        uploaderState: UploaderState.Finished,
        uploadProgress: 0,
      });
    } catch (err) {
      this.handleUploadError(err);
    }
  }

  async startVideoUpload() {
    try {
      this.props.onMediaUploading(true);
      const thumbnail = await VideoSnapshot.generateSnapshot(
        this.state.fileSelected.uri,
      );
      this.setState({ uploaderState: UploaderState.Uploading });
      this.cancelToken = CancelToken.source();
      const res = await nasm.api.uploadMedia(
        this.state.fileSelected,
        thumbnail,
        this.onUploadProgress,
        this.cancelToken.token,
      );
      this.props.onUploadCompleted(res, this.state.fileType);
      this.setState({
        uploaderState: UploaderState.Processing,
        uploadProgress: 0,
      });
    } catch (err) {
      this.handleUploadError(err);
    }
  }

  renderProcessingProgress = () => <ProcessingVideo />;

  renderTempVideo() {
    // This component is only used to load the video and check
    // the dimensions, sadly there is no API to check for this without
    // mountain it.
    return (
      <Video
        style={{ width: 1, height: 1 }}
        paused
        source={this.state.fileSelected}
        onLoad={({ naturalSize: { orientation } }) => {
          if (orientation === 'portrait') {
            this.resetUploadState();
            Alert.alert(
              'Video Error',
              'Only video content in landscape orientation is supported at this time.',
            );
            return;
          }
          this.startVideoUpload();
        }}
      />
    );
  }

  renderUploadFinished() {
    const isVideo = this.state.fileType === 'video';
    const video = (
      <Video
        style={{ width: '100%', height: '100%' }}
        source={{
          uri: this.state.fileSelected.uri,
        }}
        paused={this.state.videoPaused}
        resizeMode="stretch"
        controls={false}
        repeat
        poster={this.props.initialImageUrl}
      />
    );
    const image = (
      <Image
        resizeMode="contain"
        style={{ width: '100%', height: '100%' }}
        source={this.state.fileSelected}
      />
    );
    const media = isVideo ? video : image;
    return (
      <TouchableOpacity
        onPress={() => this.setState({ videoPaused: !this.state.videoPaused })}
        style={{ width: '100%', height: '100%' }}
      >
        {media}
        {isVideo && this.state.videoPaused && (
          <View style={styles.pauseButton}>
            <Image source={playButton} />
          </View>
        )}
        {(this.state.videoPaused || !isVideo) && (
          <TouchableOpacity
            style={styles.editButton}
            onPress={this.onEditPressed}
          >
            <Image source={editButtonIcon} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  }

  renderUploadProgress() {
    return (
      <>
        <Text
          style={{
            color: 'white',
            fontFamily: 'Avenir-Roman',
            fontSize: 20,
          }}
        >
          Uploading...
        </Text>
        <ProgressBar progress={this.state.uploadProgress} />
        <Text
          style={{
            color: 'white',
            fontFamily: 'Avenir-Roman',
            fontSize: 14,
            textAlign: 'center',
            width: '70%',
          }}
        >
          You can continue to fill out exercise details while this is processing
        </Text>
      </>
    );
  }

  renderUploader() {
    return (
      <>
        <Text
          style={{
            color: 'white',
            fontFamily: 'Avenir-Roman',
            fontSize: 20,
            marginBottom: 15,
          }}
        >
          Upload an Image/Video
        </Text>
        <TouchableOpacity
          style={{
            width: 50,
            height: 50,
            backgroundColor: colors.macaroniAndCheese,
            borderRadius: 50 / 2,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={this.onAddMediaPressed}
        >
          <Image source={addIcon} />
        </TouchableOpacity>
      </>
    );
  }

  render() {
    const currentState = () => {
      switch (this.state.uploaderState) {
        case UploaderState.Empty:
          return this.renderUploader();
        case UploaderState.Uploading:
          return this.renderUploadProgress();
        case UploaderState.Finished:
          return this.renderUploadFinished();
        case UploaderState.Validate:
          return this.renderTempVideo();
        case UploaderState.Processing:
          return this.renderProcessingProgress();
        default:
          return this.renderUploader();
      }
    };
    return (
      <View style={[styles.mediaUploader, this.props.style]}>
        {currentState()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  mediaUploader: {
    flex: 1,
    flexDirection: 'column',
    height: Dimensions.get('window').width / (16 / 9),
    backgroundColor: colors.black,
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pauseButton: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  editButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 40 / 2,
    backgroundColor: '#f7b500',
  },
});
