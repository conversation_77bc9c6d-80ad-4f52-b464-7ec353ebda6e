import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Libraries
import moment from 'moment';
import communications from 'react-native-communications';

// Components
import {
  Dimensions,
  Text,
  TouchableOpacity,
  Image,
  View,
  Linking,
} from 'react-native';
import { scaleWidth } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';

// Images
const callIcon = require('../resources/imgCallIcon.png');
const chatIcon = require('../resources/chatIcon.png');
const emailIcon = require('../resources/emailIcon.png');
const defaultProfileImage = require('../resources/defaultProfile.png');
const linkInactive = require('../resources/link-inactive.png');

const propTypes = {
  name: PropTypes.string,
  phone: PropTypes.string,
  email: PropTypes.string,
  ua_email: PropTypes.string,
  imageUrl: PropTypes.string,
  title: PropTypes.string,
  onBodyPressed: PropTypes.func,
  hideButtons: PropTypes.bool,
  isCard: PropTypes.bool,
  style: PropTypes.shape({}),
  profileBorderColor: PropTypes.string,
  showImageBorder: PropTypes.bool,
  hideContactText: PropTypes.bool,
  startDate: PropTypes.string,
  onPressEditProfile: PropTypes.func,
  iconEdit: PropTypes.node,
  isOwner: PropTypes.bool,
};

const defaultProps = {
  name: undefined,
  phone: undefined,
  email: undefined,
  ua_email: undefined,
  imageUrl: undefined,
  title: undefined,
  onBodyPressed: undefined,
  hideButtons: false,
  isCard: true,
  style: {},
  profileBorderColor: colors.cloudyBlue,
  showImageBorder: true,
  hideContactText: false,
  startDate: undefined,
  onPressEditProfile: null,
  iconEdit: null,
  isOwner: false,
};

class ContactCard extends Component {
  onPressCall = () => {
    if (this.props.phone) {
      communications.phonecall(this.props.phone, false);
    }
  };

  onPressChat = () => {
    if (this.props.phone) {
      communications.text(this.props.phone);
    }
  };

  onPressEmail = () => {
    Linking.openURL(`mailto:${this.props.email}`);
  };

  renderContactButton(type) {
    let icon;
    let action;
    let disabled = false;
    switch (type) {
      case 'Call':
        action = this.onPressCall;
        icon = callIcon;
        disabled = !this.props.phone;
        break;
      case 'Chat':
        action = this.onPressChat;
        icon = chatIcon;
        disabled = !this.props.phone;
        break;
      case 'Email':
        action = this.onPressEmail;
        icon = emailIcon;
        disabled = !this.props.email;
        break;
      default:
        break;
    }
    return (
      <TouchableOpacity
        disabled={disabled}
        onPress={action}
        style={[styles.btn, disabled && { opacity: 0.35 }]}
      >
        <Image source={icon} />
        <Text style={styles.btnText}>{type}</Text>
      </TouchableOpacity>
    );
  }

  renderLink = () => {
    if (this.props.ua_email && !this.props.hideContactText) {
      return (
        <View style={styles.linkContainer}>
          <Image source={linkInactive} style={styles.linkImage} />
          <Text style={styles.info}>
            email:
            {this.props.ua_email}
          </Text>
        </View>
      );
    }
    return null;
  };

  render() {
    return (
      <View style={[this.props.isCard && styles.cardStyle, this.props.style]}>
        {this.props.isOwner && (
          <TouchableOpacity
            style={{ position: 'absolute', right: 18, top: 18 }}
            onPress={this.props.onPressEditProfile}
          >
            <Image source={this.props.iconEdit} />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          disabled={!this.props.onBodyPressed}
          style={[
            styles.bodyContainer,
            !this.props.isCard && {
              width: '100%',
              paddingHorizontal: 36 * scale,
              alignSelf: 'center',
            },
          ]}
          onPress={this.props.onBodyPressed}
        >
          <Image
            style={[
              styles.profileImage,
              { borderColor: this.props.profileBorderColor },
              this.props.showImageBorder && { borderWidth: 3 * scale },
            ]}
            source={
              this.props.imageUrl
                ? { uri: this.props.imageUrl }
                : defaultProfileImage
            }
          />
          <View style={styles.infoContainer}>
            {!!this.props.title && (
              <Text style={styles.title}>{this.props.title}</Text>
            )}
            {!!this.props.name && (
              <Text style={styles.name}>{this.props.name}</Text>
            )}
            {!!this.props.startDate && (
              <Text style={styles.info}>
                start date:
                {' '}
                {moment(this.props.startDate).format('M/D/YY')}
              </Text>
            )}
            {!!this.props.phone && !this.props.hideContactText && (
              <Text style={styles.info}>{`phone: ${this.props.phone}`}</Text>
            )}
            {!!this.props.email && !this.props.hideContactText && (
              <Text style={styles.info}>{`email: ${this.props.email}`}</Text>
            )}
            {!!this.props.isOwner && this.renderLink()}
          </View>
        </TouchableOpacity>
        {!this.props.hideButtons && (
          <View
            style={[
              styles.buttonsContainer,
              !this.props.isCard && {
                marginHorizontal: 36 * scale,
                marginTop: 20 * scale,
              },
            ]}
          >
            {this.renderContactButton('Call')}
            {this.renderContactButton('Email')}
          </View>
        )}
      </View>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  cardStyle: {
    borderRadius: 3 * scale,
    backgroundColor: colors.white,
    ...shadow,
    marginTop: 14 * scale,
    marginHorizontal: '5%',
    padding: 20 * scale,
  },
  title: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12 * scale,
    color: colors.subGrey,
    marginBottom: 3 * scale,
  },
  bodyContainer: {
    flexDirection: 'row',
    marginVertical: 15 * scale,
  },
  profileImage: {
    width: 68 * scale,
    height: 68 * scale,
    borderRadius: 34 * scale,
    borderColor: colors.cloudyBlue,
  },
  name: {
    fontFamily: 'Avenir',
    fontSize: 16 * scale,
    fontWeight: 'bold',
    color: colors.black,
    marginRight: scaleWidth(25),
  },
  infoContainer: {
    marginLeft: 16 * scale,
    alignSelf: 'center',
  },
  info: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10 * scale,
    marginTop: 2 * scale,
    color: colors.subGrey,
  },
  linkContainer: {
    flexDirection: 'row',
  },
  linkImage: {
    height: 14 * scale,
    width: 14 * scale,
    resizeMode: 'contain',
    marginRight: 4,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginLeft: 20,
  },
  btn: {
    height: 27 * scale,
    borderRadius: 13.5 * scale,
    borderWidth: 1,
    borderColor: colors.subGrey,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20 * scale,
    flexDirection: 'row',
    marginHorizontal: 8 * scale,
  },
  btnText: {
    fontFamily: 'Avenir',
    fontSize: 12 * scale,
    fontWeight: '900',
    color: colors.subGrey,
    marginLeft: 7 * scale,
  },
};

ContactCard.propTypes = propTypes;
ContactCard.defaultProps = defaultProps;
export default ContactCard;
