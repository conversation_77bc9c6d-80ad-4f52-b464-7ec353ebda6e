import React, { Component } from 'react';
import {
  Dimensions, View, Text, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import PropTypes from 'prop-types';
import NoteView from '../screens/MainStack/TrainerNotes/NoteView';
import { scaleHeight, scaleWidth } from '../util/responsive';
import { colors, shadow } from '../styles';

const propTypes = {
  notes: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      notes: PropTypes.string,
    }),
  ).isRequired,
  onPress: PropTypes.func,
  onPressNoRecords: PropTypes.func,
  updatedOn: PropTypes.string.isRequired,
  showUpdatedText: PropTypes.bool,
  loading: PropTypes.bool,
};

const defaultProps = {
  onPress: null,
  onPressNoRecords: null,
  showUpdatedText: true,
  loading: false,
};

class NotesSection extends Component {
  renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.title}>Notes</Text>
      {this.props.updatedOn
      && this.props.showUpdatedText
      && !this.props.loading ? (
        <Text style={styles.date}>
          Updated
          {' '}
          {moment(this.props.updatedOn).format('M/DD/YY')}
        </Text>
        ) : null}
    </View>
  );

  renderNotes = (item) => (
    <NoteView numberOfLines={1} item={item} isSwipable={false} />
  );

  renderNoRecords = () => (
    <TouchableOpacity onPress={this.props.onPressNoRecords}>
      <Text style={styles.emptyText}>Tap to add a note</Text>
    </TouchableOpacity>
  );

  render() {
    return (
      <TouchableOpacity
        disabled={!this.props.onPress}
        onPress={this.props.onPress}
        style={styles.container}
      >
        {this.renderHeader()}
        {this.props.loading ? (
          <>
            <View style={styles.loadingStateView} />
            <View style={styles.loadingStateView} />
          </>
        ) : (
          <>
            {this.props.notes.length > 0
              ? this.props.notes.map(this.renderNotes)
              : this.renderNoRecords()}
          </>
        )}
      </TouchableOpacity>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  container: {
    borderRadius: 3 * scale,
    backgroundColor: colors.white,
    marginHorizontal: scaleWidth(5),
    marginVertical: scaleHeight(2),
    marginTop: 20 * scale,
    marginBottom: 0,
    ...shadow,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 15 * scale,
    paddingRight: 12 * scale,
    paddingVertical: 8 * scale,
    backgroundColor: colors.tealBlue,
    borderTopRightRadius: 3 * scale,
    borderTopLeftRadius: 3 * scale,
  },
  loadingStateView: {
    backgroundColor: colors.loadingStateGray,
    marginHorizontal: scaleWidth(5),
    marginVertical: scaleHeight(2),
    height: 14 * scale,
    width: '90%',
  },
  title: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18 * scale,
    color: colors.white,
  },
  date: {
    fontFamily: 'Avenir',
    fontSize: 9 * scale,
    fontWeight: '900',
    color: colors.white,
  },
  emptyText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 16 * scale,
    color: colors.subGrey,
    textAlign: 'center',
    margin: 16 * scale,
  },
};

NotesSection.propTypes = propTypes;
NotesSection.defaultProps = defaultProps;
export default NotesSection;
