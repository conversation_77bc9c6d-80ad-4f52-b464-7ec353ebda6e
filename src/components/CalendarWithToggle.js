import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Libraries
import CalendarStrip from 'react-native-calendar-strip';
import moment from 'moment';

// Components
import {
  View, Text, Image, StyleSheet, TouchableOpacity,
} from 'react-native';
import { Calendar } from 'react-native-calendars';
import DayComponent from './DayComponent';
import LoadingSpinner from './LoadingSpinner';
import WeekDay from './WeekDay';

// Styles
import { colors } from '../styles';
import { curvedScale } from '../util/responsive';

const downIcon = require('../resources/btnArrowDown.png');

// PropTypes
const propTypes = {
  weekView: PropTypes.bool,
  isWalkin: PropTypes.bool,
  isLoading: PropTypes.bool,
  selectedDay: PropTypes.string,
  markedDates: PropTypes.any,
  weeklyMarkedDates: PropTypes.array,
  customDatesStyles: PropTypes.array,
  onDateSelected: PropTypes.func,
  onMonthChange: PropTypes.func,
  onToggle: PropTypes.func,
  isDateDisablingEnabled: PropTypes.bool,
};
const defaultProps = {
  weekView: false,
  isWalkin: false,
  isLoading: true,
  selectedDay: null,
  markedDates: {},
  weeklyMarkedDates: [],
  customDatesStyles: [],
  onDateSelected: null,
  onMonthChange: null,
  onToggle: null,
  isDateDisablingEnabled: false,
};

class CalendarWithToggle extends Component {
  onDateSelected = (day) => {
    if (this.props.onDateSelected) {
      this.props.onDateSelected(day);
    }
  };

  onMonthChange = (date) => {
    if (this.props.onMonthChange) {
      this.props.onMonthChange(date);
    }
  };

  onToggle = () => {
    if (this.props.onToggle) {
      this.props.onToggle();
    }
  };

  getWeekStartingDate() {
    const { selectedDay } = this.props;
    const momentSelectedDay = moment(selectedDay);
    return momentSelectedDay.weekday(0);
  }

  isDateDisabled = (date) => {
    if (!this.props.isDateDisablingEnabled) {
      return false;
    }
    const isDateInFuture = moment(date).isAfter(moment());
    if (isDateInFuture) {
      return true;
    }
    return false;
  };

  renderCalendarToggle({ isExpanded }) {
    const expandOrClose = isExpanded ? 'close' : 'expand';
    return (
      <TouchableOpacity
        onPress={this.props.onToggle}
        style={styles.calendarFooter}
      >
        <View style={styles.calendarToggle}>
          <Text style={styles.expandLabel}>
            {`Tap to ${expandOrClose} calendar`}
          </Text>
          <View style={styles.arrowView}>
            <Image style={styles.arrowIcon(isExpanded)} source={downIcon} />
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  renderMonthView() {
    return (
      <Calendar
        ref={(ref) => {
          this.calendar = ref;
        }}
        onDayPress={(day) => this.onDateSelected(day.dateString)}
        onMonthChange={({ dateString }) => this.onMonthChange(dateString)}
        monthFormat="MMMM yyyy"
        markedDates={this.props.markedDates}
        markingType="multi-dot"
        dayComponent={(props) => (
          <DayComponent
            isDateDisabled={this.isDateDisabled(props.date?.dateString)}
            {...props}
          />
        )}
        theme={styles.calendarTheme}
        current={this.props.selectedDay}
        maxDate={this.props.maxDate}
      />
    );
  }

  renderWeekView() {
    return (
      <CalendarStrip
        style={styles.calendarStrip}
        daySelectionAnimation={styles.daySelectionAnimation}
        showMonth={false}
        calendarColor={colors.white}
        dateNumberStyle={styles.dateNumber}
        dateNameStyle={styles.dateName}
        highlightDateNumberStyle={styles.hightlightDateNumber}
        highlightDateNameStyle={styles.hightlightDateName}
        disabledDateNameStyle={styles.disabledDate}
        disabledDateNumberStyle={styles.disabledDate}
        leftSelector={[]}
        rightSelector={[]}
        selectedDate={this.props.selectedDay}
        onDateSelected={(momentDate) => this.onDateSelected(momentDate.format('YYYY-MM-DD'))}
        markedDates={this.props.weeklyMarkedDates}
        customDatesStyles={this.props.customDatesStyles}
        dayComponent={(props) => (
          <WeekDay
            isDateDisabled={this.isDateDisabled(props.date)}
            {...props}
          />
        )}
        useIsoWeekday={false}
        startingDate={this.getWeekStartingDate()}
        datesBlacklist={(date) => this.props.isDateDisablingEnabled
          && moment(date).isAfter(moment(this.props.maxDate))}
      />
    );
  }

  render() {
    const view = this.props.weekView
      ? this.renderWeekView()
      : this.renderMonthView();
    return (
      <View>
        {view}
        {!this.props.isWalkin
          && this.renderCalendarToggle({ isExpanded: !this.props.weekView })}
        <LoadingSpinner
          visible={this.props.isLoading}
          size="large"
          backgroundColor="rgba(0, 0, 0, 0.25)"
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  calendarStrip: {
    height: 85,
    paddingHorizontal: 15,
    paddingTop: 10,
  },
  calendarTheme: {
    selectedDayBackgroundColor: colors.nasmBlue,
    todayTextColor: colors.nasmBlue,
    arrowColor: colors.cloudyBlue,
    dotColor: colors.nasmBlue,
    textDayFontFamily: 'Avenir',
    textDayFontSize: 12,
    dayTextColor: colors.subGrey,
    'stylesheet.calendar.header': {
      header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingLeft: 10,
        paddingRight: 10,
        alignItems: 'center',
      },
      week: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderTopWidth: 1,
        borderColor: colors.subGreyLight,
        paddingHorizontal: 10,
        paddingVertical: 5,
      },
      monthText: {
        fontSize: 20,
        fontFamily: 'Avenir-Roman',
        color: colors.black,
        margin: 5,
      },
      dayHeader: {
        marginTop: 2,
        width: 32,
        textAlign: 'center',
        fontSize: 11,
        fontFamily: 'Avenir-Roman',
        color: colors.subGrey,
      },
    },
    'stylesheet.calendar.main': {
      week: {
        marginTop: 2,
        marginBottom: 2,
        flexDirection: 'row',
        justifyContent: 'space-around',
      },
    },
  },
  daySelectionAnimation: {
    type: 'background',
    duration: 200,
    highlightColor: colors.black,
  },
  disabledDate: {
    color: colors.gray,
  },
  dateNumber: {
    color: colors.subGrey,
    fontFamily: 'Avenir',
    fontSize: 12,
  },
  dateName: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
  },
  hightlightDateNumber: {
    color: colors.white,
    fontFamily: 'Avenir',
    fontSize: 12,
  },
  hightlightDateName: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
  },
  calendarToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: curvedScale(5),
  },
  calendarFooter: {
    minHeight: curvedScale(30),
    alignItems: 'flex-end',
    justifyContent: 'center',
    borderColor: colors.subGreyLight,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    marginTop: curvedScale(5),
  },
  arrowView: {
    width: curvedScale(20),
    height: curvedScale(20),
  },
  arrowIcon: (isExpanded) => ({
    width: '100%',
    height: '100%',
    transform: [{ rotate: isExpanded ? '180deg' : '0deg' }],
    marginTop: isExpanded ? curvedScale(2) : 0,
  }),
  expandLabel: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(11),
  },
});

CalendarWithToggle.propTypes = propTypes;
CalendarWithToggle.defaultProps = defaultProps;

export default CalendarWithToggle;
