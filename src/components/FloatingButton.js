import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

// Components
import { TouchableOpacity, Image } from 'react-native';

// Styles
import { colors, shadow } from '../styles';

// Images
const defaultIcon = require('../resources/btnAddwhite.png');

const propTypes = {
  onPress: PropTypes.func.isRequired,
  color: PropTypes.string,
  icon: PropTypes.any,
  width: PropTypes.number,
  position: PropTypes.oneOf([
    'top-left',
    'top-right',
    'bottom-left',
    'bottom-right',
  ]),
};

const defaultProps = {
  color: colors.darkOrange,
  icon: defaultIcon,
  width: 65,
  position: 'bottom-right',
};

class FloatingButton extends PureComponent {
  render() {
    const top = this.props.position.includes('top') ? 24 : undefined;
    const bottom = this.props.position.includes('bottom') ? 24 : undefined;
    const right = this.props.position.includes('right') ? 18 : undefined;
    const left = this.props.position.includes('left') ? 18 : undefined;
    return (
      <TouchableOpacity
        {...this.props}
        style={[
          styles.button,
          {
            width: this.props.width,
            height: this.props.width,
            backgroundColor: this.props.color,
            borderRadius: this.props.width / 2,
            top,
            bottom,
            right,
            left,
          },
        ]}
        onPress={this.props.onPress}
      >
        <Image source={this.props.icon} />
      </TouchableOpacity>
    );
  }
}

const styles = {
  button: {
    ...shadow,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
};

FloatingButton.propTypes = propTypes;
FloatingButton.defaultProps = defaultProps;
export default FloatingButton;
