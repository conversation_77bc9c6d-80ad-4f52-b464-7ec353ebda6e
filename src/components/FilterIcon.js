import React from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image, Text, TouchableOpacity, View,
} from 'react-native';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  badgeCount: PropTypes.number,
  onPressFilter: PropTypes.func,
};
const defaultProps = {
  badgeCount: 0,
  onPressFilter: undefined,
};

class FilterIcon extends React.Component {
  render() {
    const filterIcon = require('../resources/allGray.png');
    let badge = null;
    let numberSelected = this.props.badgeCount || 0;
    if (numberSelected > 0) {
      const suffix = numberSelected > 99 ? '+' : '';
      numberSelected = Math.min(Math.max(numberSelected), 99);
      badge = (
        <View style={styles.circularBadge}>
          <Text style={styles.badgeText}>
            {numberSelected}
            {suffix}
          </Text>
        </View>
      );
    }
    return (
      <TouchableOpacity
        style={{ marginRight: 16 }}
        onPress={() => {
          if (typeof this.props.onPressFilter === 'function') {
            this.props.onPressFilter();
          }
        }}
      >
        <Image source={filterIcon} />
        {badge}
      </TouchableOpacity>
    );
  }
}

const styles = {
  badgeText: {
    alignSelf: 'center',
    color: colors.white,
    fontFamily: 'Avenir-Medium',
    fontSize: 8,
    textAlign: 'center',
  },
  circularBadge: {
    alignSelf: 'flex-end',
    backgroundColor: colors.medYellow,
    borderRadius: 6,
    flex: 1,
    height: 12,
    justifyContent: 'center',
    marginTop: 3,
    minWidth: 12,
    position: 'absolute',
    paddingLeft: 4,
    paddingRight: 4,
    right: 2,
    top: 0,
  },
};

FilterIcon.propTypes = propTypes;
FilterIcon.defaultProps = defaultProps;
export default FilterIcon;
