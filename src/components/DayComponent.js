import React, { Component } from 'react';
import moment from 'moment';

// Components
import {
  TouchableOpacity, Text, View, StyleSheet,
} from 'react-native';
import { shouldUpdate } from 'react-native-calendars/src/component-updater';
import { colors } from '../styles';

class DayComponent extends Component {
  constructor(props) {
    super(props);
    this.style = styles;
    this.onDayPress = this.onDayPress.bind(this);
    this.onDayLongPress = this.onDayLongPress.bind(this);
  }

  shouldComponentUpdate(nextProps) {
    return shouldUpdate(this.props, nextProps, [
      'state',
      'children',
      'marking',
      'onPress',
      'onLongPress',
    ]);
  }

  onDayLongPress() {
    this.props.onLongPress(this.props.date);
  }

  onDayPress() {
    this.props.onPress(this.props.date);
  }

  renderDots(marking) {
    if (
      marking.dots
      && Array.isArray(marking.dots)
      && marking.dots.length > 0
    ) {
      const validDots = marking.dots.filter((d) => d && d.color);
      return validDots.map((dot, index) => (
        <View
          key={dot.key ? dot.key : index}
          style={[
            styles.dot,
            styles.visibleDot,
            {
              backgroundColor:
                marking.selected && dot.selectedDotColor
                  ? dot.selectedDotColor
                  : dot.color,
            },
          ]}
        />
      ));
    }
    return null;
  }

  render() {
    const containerStyle = [styles.base];
    const textStyle = [styles.text];

    const isDateToday = moment().format('YYYY-MM-DD') === this.props.date.dateString;
    const isDateInFuture = moment(this.props.date.dateString).isAfter(moment());

    if (isDateToday) {
      textStyle.push({ color: colors.black });
    }

    const marking = this.props.marking || {};
    const dot = this.renderDots(marking);

    if (marking.selected) {
      containerStyle.push(styles.selected);
      if (marking.selectedColor) {
        containerStyle.push({ backgroundColor: marking.selectedColor });
      }
      if (marking.dark) {
        textStyle.push({ color: colors.white });
      }
    }

    if (this.props.isDateDisabled && isDateInFuture) {
      textStyle.push({ color: colors.disabledGrey });
    }

    return (
      <TouchableOpacity
        style={styles.container}
        onPress={this.onDayPress}
        onLongPress={this.onDayLongPress}
        disabled={this.props.isDateDisabled}
      >
        <View style={containerStyle}>
          <Text allowFontScaling={false} style={textStyle}>
            {String(this.props.children)}
          </Text>
        </View>
        <View style={styles.dot}>{dot}</View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    height: 45,
  },
  base: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: 'bold',
    fontFamily: 'Avenir-Heavy',
    fontSize: 12,
    color: colors.fillDarkGrey,
  },
  selected: {
    borderRadius: 16,
  },
  dot: { flexDirection: 'row', marginTop: 3 },
  visibleDot: {
    opacity: 1,
  },
});

export default DayComponent;
