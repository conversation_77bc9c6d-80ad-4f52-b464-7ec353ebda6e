import React, { Component } from 'react';
import { View, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import DraggableFlatList from 'react-native-draggable-flatlist';

export const CELLTYPE = {
  HEADER: 'header',
  ROW: 'row',
};

const propTypes = {
  data: PropTypes.array.isRequired,
  renderRow: PropTypes.func.isRequired,
  renderSectionHeader: PropTypes.func.isRequired,
  onMoveEnd: PropTypes.func.isRequired,
  renderHeader: PropTypes.func,
  renderFooter: PropTypes.func,
};

const defaultProps = {};

class SortableSectionList extends Component {
  // TODO: this is being used by ref
  getSectionForIndex(index) {
    let section = this.props.data[0];

    for (let i = 0; i <= index; i += 1) {
      if (this.props.data[i].type === CELLTYPE.HEADER) {
        section = this.props.data[i];
      }
    }

    return section;
  }

  // TODO: this is being used by ref
  setScrollEnabled(enabled) {
    if (
      this.draggableFlatList.state
      && this.draggableFlatList.state.activeRow >= 0
    ) {
      // list is currently sorting, shouldn't set state
      return;
    }

    if (enabled) {
      this.draggableFlatList.setState({ activeRow: -1 });
    } else {
      this.draggableFlatList.setState({ activeRow: -2 });
    }
  }

  renderHeader = () => (
    <View>
      {this.props.renderHeader && this.props.renderHeader()}
      {this.props.data[0]
        && this.props.renderSectionHeader(this.props.data[0].id)}
    </View>
  );

  renderItem = (itemData) => {
    if (itemData.item.type === CELLTYPE.HEADER) {
      return this.props.renderSectionHeader(itemData.item.id);
    }
    return this.props.renderRow(itemData);
  };

  render() {
    const {
      data, renderFooter, onMoveEnd, ...props
    } = this.props;
    return (
      <View style={styles.container}>
        <DraggableFlatList
          {...props}
          ref={(ref) => {
            this.draggableFlatList = ref;
          }}
          data={data.slice(1)}
          onDragEnd={(params) => onMoveEnd([data[0], ...params.data])}
          renderItem={this.renderItem}
          ListHeaderComponent={this.renderHeader}
          ListFooterComponent={renderFooter}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

SortableSectionList.propTypes = propTypes;
SortableSectionList.defaultProps = defaultProps;

export default SortableSectionList;
