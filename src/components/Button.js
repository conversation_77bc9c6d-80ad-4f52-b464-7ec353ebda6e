/* eslint-disable react-native/no-unused-styles */
// disabling for this file because the "variant" styles are used but not tracked by the linter
import React from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Dimensions,
  TouchableOpacity,
  StyleSheet,
  ViewPropTypes,
  Image,
  View,
} from 'react-native';
import LoadingSpinner from './LoadingSpinner';
import ScaledText from './ScaledText';
import { scaleHeight } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';

// PropTypes
const propTypes = {
  title: PropTypes.string,
  onPress: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  textStyles: PropTypes.any,
  variant: PropTypes.oneOf(['base', 'yellow', 'azure', 'outline', 'text']),
  isLoading: PropTypes.bool,
  containerStyle: ViewPropTypes.style,
  buttonStyle: ViewPropTypes.style,
  testID: PropTypes.string,
  loadingSpinnerBackgroundColor: PropTypes.string,
  loadingSpinnerColor: PropTypes.string,
  loadingSpinnerSize: PropTypes.string,
  icon: PropTypes.any,
};

const defaultProps = {
  disabled: false,
  containerStyle: null,
  buttonStyle: null,
  textStyles: null,
  variant: 'base',
  isLoading: false,
  testID: null,
  loadingSpinnerBackgroundColor: null,
  loadingSpinnerSize: 'large',
  loadingSpinnerColor: colors.white,
};

// Component definition
export default function Button(props) {
  const {
    title,
    onPress,
    containerStyle,
    buttonStyle,
    textStyles,
    disabled,
    variant,
    isLoading,
    testID,
    children,
    loadingSpinnerSize,
    loadingSpinnerColor,
    icon,
  } = props;
  let backgroundColor = props.loadingSpinnerBackgroundColor;

  if (!backgroundColor) {
    backgroundColor = variant === 'yellow' ? colors.macaroniAndCheese : colors.buttonBlue;
    switch (variant) {
      case 'yellow':
        backgroundColor = colors.macaroniAndCheese;
        break;

      case 'red':
        backgroundColor = colors.nasmRed;
        break;

      case 'azure':
        backgroundColor = colors.azure;
        break;

      default:
        backgroundColor = colors.buttonBlue;
        break;
    }
  }
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || isLoading}
      style={[
        styles.container,
        styles[variant],
        disabled && styles.buttonDisabled,
        containerStyle,
        buttonStyle,
      ]}
      testID={testID}
    >
      <View style={styles.contentContainer}>
        {icon && <Image style={styles.icon} source={icon} />}
        {!isLoading && (
          <ScaledText
            style={[
              styles.buttonText,
              disabled && styles.buttonDisabledText,
              variant === 'text' && styles.textButtonText,
              textStyles,
            ]}
          >
            {title}
          </ScaledText>
        )}
      </View>
      {children}
      <LoadingSpinner
        color={loadingSpinnerColor}
        visible={isLoading}
        backgroundColor="transparent"
        size={loadingSpinnerSize}
        viewStyle={[buttonStyle, { borderWidth: 0 }]}
      />
    </TouchableOpacity>
  );
}

const scale = Dimensions.get('window').width / 400;

// Export
Button.propTypes = propTypes;
Button.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    alignSelf: 'stretch',
    borderRadius: 3,
    justifyContent: 'center',
    minHeight: scaleHeight(7),
  },
  contentContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    textAlign: 'center',
    fontFamily: 'Avenir-Heavy',
    color: colors.white,
    backgroundColor: 'transparent',
    fontSize: 14,
  },
  // States
  buttonDisabled: {
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderWidth: 2 * scale,
    borderColor: colors.buttonBorderDisabled,
    elevation: 0,
    shadowOpacity: 0,
  },
  buttonDisabledText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.subGrey,
  },
  // Variants
  base: {
    backgroundColor: colors.buttonBlue,
    ...shadow,
  },
  yellow: {
    backgroundColor: colors.macaroniAndCheese,
    ...shadow,
  },
  red: {
    backgroundColor: colors.nasmRed,
    ...shadow,
  },
  azure: {
    backgroundColor: colors.azure,
    ...shadow,
  },
  outline: {
    backgroundColor: 'transparent',
    borderColor: colors.white,
    borderWidth: 2 * scale,
  },
  text: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    borderRadius: 0,
    alignSelf: 'center',
    paddingHorizontal: 15 * scale,
  },
  textButtonText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14 * scale,
    lineHeight: 21.0 * scale,
    textAlign: 'center',
    color: colors.subGrey,
  },
  icon: {
    marginRight: 10,
  },
});
