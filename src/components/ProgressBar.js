import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '../styles';

export default function ProgressBar(props) {
  const progressColor = props.progressColor || colors.macaroniAndCheese;
  const progress = props.progress || 0;
  const currentProgress = progress <= 100 ? progress : 100;
  const progressBarWidth = `${currentProgress}%`;
  return (
    <View style={[styles.container, props.style]}>
      <View
        style={[
          { backgroundColor: progressColor, width: progressBarWidth },
          styles.progressBar,
          props.barStyle,
        ]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '80%',
    height: 10,
    borderWidth: 1,
    borderColor: colors.subGreyLight,
    borderRadius: 10,
  },
  progressBar: {
    height: '100%',
    borderRadius: 10,
  },
});
