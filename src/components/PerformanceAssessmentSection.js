import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Moment from 'moment';

// Components
import {
  Text,
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {
  VictoryLine,
  VictoryScatter,
  VictoryAxis,
  VictoryChart,
} from 'victory-native';
import { scaleWidth } from '../util/responsive';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  assessments: PropTypes.array,
  renderEmptyStateView: PropTypes.func,
  loading: PropTypes.bool,
  centerDate: PropTypes.string,
};
const defaultProps = {
  assessments: [],
  renderEmptyStateView: null,
  loading: false,
  centerDate: null,
};

const types = {
  CARDIO: 'cardio',
  ENDURANCE: 'endurance',
  STRENGTH: 'strength',
};

const assessmentNames = {
  three_minute_step: 'Step',
  one_minute_jumping_jacks: 'JJ',
  one_mile_run: 'Mile',
  five_hundred_meter_row: 'Row',
  one_minute_pushups: 'Pushups',
  one_minute_squats: 'Squats',
  squat: 'Squat',
  bench: 'Bench',
  seated_row: 'Row',
};

class PerformanceAssessmentSection extends Component {
  constructor(props) {
    super(props);
    this.state = {
      assessmentType: types.CARDIO,
    };
  }

  getColorAndSymbol = (assessment) => {
    if (assessment === 'Pushups') {
      return { color: colors.azure, symbol: 'circle' };
    }
    if (assessment === 'Squats') {
      return { color: colors.nasmBlue, symbol: 'square' };
    }
    if (assessment === 'Squat') {
      return { color: colors.medYellow, symbol: 'triangleUp' };
    }
    if (assessment === 'Bench') {
      return { color: colors.nasmBlue, symbol: 'square' };
    }
    if (assessment === 'Row') {
      if (this.state.assessmentType === types.STRENGTH) {
        return { color: colors.azure, symbol: 'circle' };
      }
      return { color: colors.peaGreen, symbol: 'diamond' };
    }
    if (assessment === 'Step') {
      return { color: colors.azure, symbol: 'circle' };
    }
    if (assessment === 'JJ') {
      return { color: colors.nasmBlue, symbol: 'square' };
    }
    if (assessment === 'Mile') {
      return { color: colors.medYellow, symbol: 'triangleUp' };
    }
    return { color: 'rgba(0, 0, 0, 0)', symbol: null };
  };

  getDataForGraph = () => {
    const data = {};
    const expectedCategories = this.getExpectedAssessmentCategories();
    expectedCategories.forEach((element) => {
      data[element] = {};
    });
    const axesData = [0, 1, 2, 3, 4, 5, 6];
    const reversedAssessments = [].concat(this.props.assessments).reverse(0);
    axesData.forEach((element) => {
      const item = reversedAssessments[element];
      if (item) {
        for (let x = 0; x < item.items.length; x += 1) {
          const assessment = item.items[x];
          const category = assessment.performance_assessment.category.toLowerCase();
          if (this.state.assessmentType === category) {
            const assessmentName = assessment.performance_assessment.name;
            const mappedName = assessmentNames[assessmentName];
            data[mappedName][element] = assessment.value;
          }
        }
      }
    });
    return data;
  };

  getExpectedAssessmentCategories = () => {
    if (this.state.assessmentType === types.CARDIO) {
      return ['Step', 'JJ', 'Mile', 'Row'];
    }
    if (this.state.assessmentType === types.ENDURANCE) {
      return ['Pushups', 'Squats'];
    }
    return ['Squat', 'Bench', 'Row'];
  };

  normalizeData = (data) => {
    const normalized = [];
    let max = -1;
    data.forEach((element) => {
      if (max === -1) {
        max = element.y;
      }
      if (element.y > max) {
        max = element.y;
      }
    });
    data.forEach((element) => {
      if (max !== 0) {
        normalized.push({ x: element.x, y: element.y / max + 0.1 });
      }
    });
    return normalized;
  };

  renderAssessmentTypePicker = () => {
    const pickerStyle = {
      width: 319 * scale,
      borderRadius: 0,
      borderWidth: 0,
      backgroundColor: colors.loadingStateGray,
    };
    if (this.props.loading) {
      return (
        <View style={styles.assessmentTypePickerContainer}>
          <View style={[styles.assessmentTypePicker, pickerStyle]} />
        </View>
      );
    }
    return (
      <View style={styles.assessmentTypePickerContainer}>
        <View style={styles.assessmentTypePicker}>
          <TouchableOpacity
            style={
              this.state.assessmentType === types.CARDIO
                ? styles.selectedContainer
                : styles.unselectedContainer
            }
            onPress={() => {
              this.setState({ assessmentType: types.CARDIO });
            }}
          >
            <Text
              style={
                this.state.assessmentType === types.CARDIO
                  ? styles.selectedPeriod
                  : styles.unselectedPeriod
              }
            >
              Cardio
            </Text>
          </TouchableOpacity>

          <View style={styles.dividerVertical} />

          <TouchableOpacity
            style={
              this.state.assessmentType === types.ENDURANCE
                ? styles.selectedContainer
                : styles.unselectedContainer
            }
            onPress={() => {
              this.setState({ assessmentType: types.ENDURANCE });
            }}
          >
            <Text
              style={
                this.state.assessmentType === types.ENDURANCE
                  ? styles.selectedPeriod
                  : styles.unselectedPeriod
              }
            >
              Endurance
            </Text>
          </TouchableOpacity>

          <View style={styles.dividerVertical} />

          <TouchableOpacity
            style={
              this.state.assessmentType === types.STRENGTH
                ? styles.selectedContainer
                : styles.unselectedContainer
            }
            onPress={() => {
              this.setState({ assessmentType: types.STRENGTH });
            }}
          >
            <Text
              style={
                this.state.assessmentType === types.STRENGTH
                  ? styles.selectedPeriod
                  : styles.unselectedPeriod
              }
            >
              Strength
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderEmptyStateView() {
    if (typeof this.props.renderEmptyStateView === 'function') {
      return this.props.renderEmptyStateView();
    }
    return (
      <View style={styles.emptyStateView}>
        <Text style={styles.assessInfoLabel}>
          Tap here to record assessment information
        </Text>
      </View>
    );
  }

  renderGraph() {
    const data = this.getDataForGraph();
    const axesData = ['0', '1', '2', '3', '4', '5', '6'];
    const emptyPoints = [];
    axesData.forEach((element) => {
      emptyPoints.push({ x: element, y: 0 });
    });

    const keys = Object.keys(data);
    const maps = [];
    for (let i = 0; i < keys.length; i += 1) {
      const key = keys[i];
      const dictionaryForKey = data[key];
      const arrayForKey = [];
      // eslint-disable-next-line no-restricted-syntax, guard-for-in
      for (const item in dictionaryForKey) {
        arrayForKey.push({ x: item, y: dictionaryForKey[item] });
      }
      maps.push({ [key]: arrayForKey });
    }
    return (
      <View key={this.state.assessmentType}>
        <View style={styles.midSection} pointerEvents="none">
          {this.props.centerDate && (
            <View style={styles.chartHeaderContainer}>
              <View style={styles.chartHeaderView()} />
              <View style={styles.chartHeaderView(2)} />
              <View style={styles.chartHeaderView()} />
            </View>
          )}
          <VictoryChart
            height={100 * scale}
            width={339 * scale}
            domainPadding={10}
            padding={{
              top: 10,
              bottom: 10,
              left: 20,
              right: 20,
            }}
          >
            <VictoryAxis
              key={0}
              style={{
                axis: { stroke: '' },
                tickLabels: { fill: 'rgba(0,0,0,0)' },
                grid: { stroke: colors.silver },
              }}
              tickValues={axesData}
            />

            <VictoryScatter
              style={{ data: { fill: 'rgba(0,0,0,0)' } }}
              data={emptyPoints}
            />

            {this.renderGraphLine(maps[0])}
            {this.renderGraphPointsWithMap(maps[0])}

            {this.renderGraphLine(maps[1])}
            {this.renderGraphPointsWithMap(maps[1])}

            {this.renderGraphLine(maps[2])}
            {this.renderGraphPointsWithMap(maps[2])}

            {this.renderGraphLine(maps[3])}
            {this.renderGraphPointsWithMap(maps[3])}
          </VictoryChart>
        </View>
        {this.renderGraphLegend()}
        {this.renderAssessmentTypePicker()}
      </View>
    );
  }

  renderGraphLegend = () => {
    const data = [];
    if (this.props.loading) {
      data.push({
        text: 'Step',
        color: colors.loadingStateGray,
        iconStyle: [
          styles.legendDotStyle,
          { backgroundColor: colors.loadingStateGray },
        ],
      });
      data.push({
        text: 'JJ',
        color: colors.loadingStateGray,
        iconStyle: [
          styles.legendSquareStyle,
          { backgroundColor: colors.loadingStateGray },
        ],
      });
      data.push({
        text: 'Mile',
        color: colors.loadingStateGray,
        iconStyle: [
          styles.triangle,
          {
            borderBottomColor: colors.loadingStateGray,
            borderLeftWidth: 5,
            borderRightWidth: 5,
            borderBottomWidth: 10,
          },
        ],
      });
      data.push({
        text: 'Row',
        color: colors.loadingStateGray,
        iconStyle: [
          styles.legendSquareStyle,
          {
            transform: [{ rotate: '45deg' }],
            backgroundColor: colors.loadingStateGray,
          },
        ],
      });
    } else if (this.state.assessmentType === types.CARDIO) {
      data.push({
        text: 'Step',
        color: colors.azure,
        iconStyle: [styles.legendDotStyle, { backgroundColor: colors.azure }],
      });
      data.push({
        text: 'JJ',
        color: colors.nasmBlue,
        iconStyle: [
          styles.legendSquareStyle,
          { backgroundColor: colors.nasmBlue },
        ],
      });
      data.push({
        text: 'Mile',
        color: colors.medYellow,
        iconStyle: [
          styles.triangle,
          {
            borderBottomColor: colors.medYellow,
            borderLeftWidth: 5,
            borderRightWidth: 5,
            borderBottomWidth: 10,
          },
        ],
      });
      data.push({
        text: 'Row',
        color: colors.peaGreen,
        iconStyle: [
          styles.legendSquareStyle,
          {
            transform: [{ rotate: '45deg' }],
            backgroundColor: colors.peaGreen,
          },
        ],
      });
    } else if (this.state.assessmentType === types.ENDURANCE) {
      data.push({
        text: 'Pushups',
        color: colors.azure,
        iconStyle: [styles.legendDotStyle, { backgroundColor: colors.azure }],
      });
      data.push({
        text: 'Squats',
        color: colors.nasmBlue,
        iconStyle: [
          styles.legendSquareStyle,
          { backgroundColor: colors.nasmBlue },
        ],
      });
    } else {
      data.push({
        text: 'Squat',
        color: colors.medYellow,
        iconStyle: [
          styles.triangle,
          {
            borderBottomColor: colors.medYellow,
            borderLeftWidth: 5,
            borderRightWidth: 5,
            borderBottomWidth: 10,
          },
        ],
      });
      data.push({
        text: 'Bench',
        color: colors.nasmBlue,
        iconStyle: [
          styles.legendSquareStyle,
          { backgroundColor: colors.nasmBlue },
        ],
      });
      data.push({
        text: 'Row',
        color: colors.azure,
        iconStyle: [styles.legendDotStyle, { backgroundColor: colors.azure }],
      });
    }
    return (
      <View style={styles.graphContainer}>
        <View style={styles.textSection}>
          {data.map((item, index) => (
            <View key={`${index + 1}`} style={styles.dotAndLabel}>
              <View style={item.iconStyle} />
              {!this.props.loading && (
                <Text
                  style={[
                    styles.underText,
                    {
                      fontSize: 16 * scale,
                      paddingLeft: 9 * scale,
                      color: item.color,
                    },
                  ]}
                >
                  {item.text}
                </Text>
              )}
              {this.props.loading && (
                <View
                  style={{
                    height: 12 * scale,
                    width: 35 * scale,
                    marginLeft: 3 * scale,
                    marginRight: 10 * scale,
                    backgroundColor: colors.loadingStateGray,
                  }}
                />
              )}
            </View>
          ))}
        </View>
      </View>
    );
  };

  renderGraphLine = (map) => {
    if (!map) {
      return null;
    }
    const key = Object.keys(map)[0];
    const data = map[key];
    if (data && data.length > 1) {
      // need at least 2 points for a line
      const { color } = this.getColorAndSymbol(key);
      const normalizedData = this.normalizeData(data);
      return (
        <VictoryLine
          style={{ data: { stroke: color, strokeWidth: 2 } }}
          data={normalizedData}
          interpolation="catmullRom"
        />
      );
    }
    return null;
  };

  renderGraphPointsWithMap = (map) => {
    if (!map) {
      return null;
    }
    const key = Object.keys(map)[0];
    const data = map[key];
    if (data) {
      const x = this.getColorAndSymbol(key);
      const normalizedData = data.length === 1 ? data : this.normalizeData(data);
      return (
        <VictoryScatter
          style={{ data: { fill: x.color } }}
          size={4}
          symbol={x.symbol}
          data={normalizedData}
        />
      );
    }
    return null;
  };

  renderLoadingState() {
    const data1 = [
      { x: 0, y: 100 },
      { x: 1, y: 95 },
      { x: 2, y: 85 },
      { x: 3, y: 75 },
      { x: 4, y: 55 },
      { x: 5, y: 55 },
      { x: 6, y: 45 },
    ];
    const data2 = [
      { x: 0, y: 75 },
      { x: 1, y: 70 },
      { x: 2, y: 65 },
      { x: 3, y: 60 },
      { x: 4, y: 45 },
      { x: 5, y: 30 },
      { x: 6, y: 30 },
    ];
    const data3 = [
      { x: 0, y: 65 },
      { x: 1, y: 70 },
      { x: 2, y: 75 },
      { x: 3, y: 80 },
      { x: 4, y: 80 },
      { x: 5, y: 77 },
      { x: 6, y: 85 },
    ];
    const data4 = [
      { x: 0, y: 40 },
      { x: 1, y: 35 },
      { x: 2, y: 35 },
      { x: 3, y: 30 },
      { x: 4, y: 25 },
      { x: 5, y: 15 },
      { x: 6, y: 15 },
    ];
    return (
      <View>
        <View style={styles.midSection} pointerEvents="none">
          <VictoryChart
            height={100 * scale}
            width={339 * scale}
            domainPadding={10}
            padding={{
              top: 10,
              bottom: 10,
              left: 20,
              right: 20,
            }}
          >
            <VictoryAxis
              key={0}
              style={{
                axis: { stroke: '' },
                tickLabels: { fill: 'rgba(0,0,0,0)' },
                grid: { stroke: colors.loadingStateGray },
              }}
              tickValues={[0, 1, 2, 3, 4, 5, 6]}
            />

            <VictoryLine
              style={{
                data: { stroke: colors.loadingStateGray, strokeWidth: 2 },
              }}
              data={data1}
              interpolation="catmullRom"
            />
            <VictoryScatter
              style={{ data: { fill: colors.loadingStateGray } }}
              size={4}
              symbol="circle"
              data={data1}
            />

            <VictoryLine
              style={{
                data: { stroke: colors.loadingStateGray, strokeWidth: 2 },
              }}
              data={data2}
              interpolation="catmullRom"
            />
            <VictoryScatter
              style={{ data: { fill: colors.loadingStateGray } }}
              size={4}
              symbol="circle"
              data={data2}
            />

            <VictoryLine
              style={{
                data: { stroke: colors.loadingStateGray, strokeWidth: 2 },
              }}
              data={data3}
              interpolation="catmullRom"
            />
            <VictoryScatter
              style={{ data: { fill: colors.loadingStateGray } }}
              size={4}
              symbol="circle"
              data={data3}
            />

            <VictoryLine
              style={{
                data: { stroke: colors.loadingStateGray, strokeWidth: 2 },
              }}
              data={data4}
              interpolation="catmullRom"
            />
            <VictoryScatter
              style={{ data: { fill: colors.loadingStateGray } }}
              size={4}
              symbol="circle"
              data={data4}
            />
          </VictoryChart>
        </View>
        {this.renderGraphLegend()}
        {this.renderAssessmentTypePicker()}
      </View>
    );
  }

  render() {
    const { assessments } = this.props;
    let view = null;
    if (this.props.loading) {
      view = this.renderLoadingState();
    } else if (!assessments || assessments.length === 0) {
      view = this.renderEmptyStateView();
    } else {
      view = this.renderGraph();
    }
    let updated_at = null;
    if (assessments && assessments.length > 0) {
      updated_at = new Moment(assessments[0].date).format('M/D/YY');
    }
    return (
      <View style={styles.cardStyle}>
        <View style={styles.headerSection}>
          <Text style={styles.header}>Performance Assessments</Text>
          {updated_at && (
            <Text style={styles.subHeader}>
              Updated
              {updated_at}
            </Text>
          )}
        </View>
        {view}
      </View>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = StyleSheet.create({
  assessmentTypePickerContainer: {
    height: 48 * scale,
    width: '100%',
    alignItems: 'center',
  },
  assessmentTypePicker: {
    height: 27 * scale,
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 12 * scale,
    marginEnd: 27 * scale,
    marginStart: 27 * scale,
    borderColor: colors.subGrey,
    borderWidth: 1,
    borderRadius: 4 * scale,
  },
  selectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.subGrey,
  },
  unselectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedPeriod: {
    color: colors.white,
    alignSelf: 'center',
    fontFamily: 'Avenir-Roman',
    fontSize: 13 * scale,
  },
  unselectedPeriod: {
    fontFamily: 'Avenir-Roman',
    alignSelf: 'center',
    color: colors.subGrey,
    fontSize: 13 * scale,
  },
  cardStyle: {
    borderRadius: 5 * scale,
    backgroundColor: colors.white,
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowRadius: 4,
    shadowOpacity: 1,
    elevation: 3,
    marginTop: 20 * scale,
    marginHorizontal: scaleWidth(5),
  },
  headerSection: {
    backgroundColor: colors.nasmBlue,
    height: 40 * scale,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopLeftRadius: 5 * scale,
    borderTopRightRadius: 5 * scale,
  },
  header: {
    color: colors.white,
    fontSize: 18 * scale,
    fontFamily: 'Avenir-Roman',
    paddingLeft: 15 * scale,
  },
  subHeader: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    paddingRight: 12 * scale,
    fontSize: 9 * scale,
  },
  midSection: {
    paddingTop: 8 * scale,
    paddingBottom: 8 * scale,
    backgroundColor: colors.paleGray3,
  },
  underText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12 * scale,
    color: colors.fillDarkGrey,
    paddingLeft: 3 * scale,
    paddingRight: 10 * scale,
  },
  textSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16 * scale,
    height: 34 * scale,
  },
  emptyStateView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: scaleWidth(5),
  },
  triangle: {
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderLeftWidth: 4,
    borderRightWidth: 4,
    borderBottomWidth: 8,
    borderBottomColor: colors.cloudyBlue,
  },
  legendDotStyle: {
    width: 9,
    height: 9,
    borderRadius: 4.5,
  },
  legendSquareStyle: {
    width: 9,
    height: 9,
  },
  dotAndLabel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  dividerVertical: {
    width: 1,
    height: '100%',
    backgroundColor: colors.subGrey,
  },
  assessInfoLabel: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 16 * scale,
    color: colors.subGrey,
    textAlign: 'center',
  },
  graphContainer: {
    height: 28 * scale,
    display: 'flex',
    marginStart: 16 * scale,
    marginEnd: 16 * scale,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  chartHeaderContainer: {
    position: 'absolute',
    right: 10 * scale,
    left: 0,
    top: 9 * scale,
    bottom: 0,
    alignItems: 'center',
  },
  chartHeaderView: (width = 10) => ({
    width: width * scale,
    height: 2 * scale,
    backgroundColor: colors.cloudyBlue,
  }),
});

PerformanceAssessmentSection.propTypes = propTypes;
PerformanceAssessmentSection.defaultProps = defaultProps;

export default PerformanceAssessmentSection;
