import React from 'react';
import {
  Text, Switch, View, TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import { colors } from '../styles';

const propTypes = {
  enabled: PropTypes.bool,
  onPress: PropTypes.func,
  onToggle: PropTypes.func,
  style: PropTypes.object,
};

const defaultProps = {
  enabled: false,
  onPress: () => {},
  onToggle: () => {},
  style: {},
};

export default function CorrectiveExerciseToggle(props) {
  return (
    <View style={[styles.container, props.style]}>
      <TouchableOpacity style={styles.textContainer} onPress={props.onPress}>
        <Text style={styles.title}>Corrective Exercises</Text>
        <Text style={styles.body}>
          Add corrective exercises from your last Overhead Squat Assessment.
        </Text>
      </TouchableOpacity>
      <Switch
        trackColor={{ true: colors.macaroniAndCheese }}
        value={props.enabled}
        onValueChange={props.onToggle}
      />
    </View>
  );
}

CorrectiveExerciseToggle.propTypes = propTypes;
CorrectiveExerciseToggle.defaultProps = defaultProps;

const styles = {
  container: {
    flexDirection: 'row',
  },
  textContainer: {
    flex: 1,
    marginRight: 10,
  },
  title: {
    fontFamily: 'Avenir',
    fontSize: 17,
    fontWeight: 'bold',
    lineHeight: 23,
    letterSpacing: 0,
  },
  body: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    lineHeight: 22,
    letterSpacing: 0,
    color: colors.fillDarkGrey,
  },
};
