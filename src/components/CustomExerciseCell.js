import React from 'react';

import {
  Image, TouchableOpacity, View, StyleSheet,
} from 'react-native';

import Swipeable from './Swipeable';
import ExerciseCell from './ExerciseCell';
import ScaledText from './ScaledText';

import { colors } from '../styles';
import deleteButton from '../resources/btnDelete.png';
import ROUTINE_TYPES from '../types/RoutineTypes';

const styles = StyleSheet.create({
  deleteButtonContainer: {
    backgroundColor: colors.dustyRed,
    flex: 1,
  },
  sideButtonText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
    color: colors.white,
    paddingTop: '5%',
  },
  sideButtonContent: {
    flex: 1,
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
});

export default function CustomExerciseCell(props) {
  const { exercise } = props;
  const isSuperSet = exercise.routine_type === ROUTINE_TYPES.SUPER_SET;
  const isCircuit = exercise.routine_type === ROUTINE_TYPES.CIRCUIT;
  return (
    <Swipeable
      ref={props.swipableRef}
      rightButtons={[
        <TouchableOpacity
          key={1}
          style={styles.deleteButtonContainer}
          onPress={() => props.deleteExercise(exercise, isSuperSet, isCircuit)}
        >
          <View style={styles.sideButtonContent}>
            <Image source={deleteButton} />
            <ScaledText style={styles.sideButtonText}>Delete</ScaledText>
          </View>
        </TouchableOpacity>,
      ]}
    >
      <ExerciseCell {...props} />
    </Swipeable>
  );
}
