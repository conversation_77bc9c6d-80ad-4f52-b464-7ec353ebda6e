import React from 'react';
import PropTypes from 'prop-types';

// Components
import { View, StyleSheet, Platform } from 'react-native';
import PagerView from 'react-native-pager-view';
import { colors } from '../styles';

const propTypes = {
  count: PropTypes.number,
  selectedIndex: PropTypes.number,
  onSelectedIndexChange: PropTypes.func,
  children: PropTypes.any,
};

const defaultProps = {
  count: 0,
  selectedIndex: 0,
  onSelectedIndexChange: null,
  children: null,
};

class CustomViewPager extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      width: 0,
      height: 0,
      selectedIndex: this.props.selectedIndex,
      initialSelectedIndex: this.props.selectedIndex,
      scrollingTo: null,
    };
    this.handleHorizontalScroll = this.handleHorizontalScroll.bind(this);
    this.adjustCardSize = this.adjustCardSize.bind(this);
  }

  componentDidUpdate() {
    if (this.props.selectedIndex !== this.state.selectedIndex) {
      if (Platform.OS === 'ios') {
        this.scrollview.scrollTo({
          x: this.props.selectedIndex * this.state.width,
          animated: true,
        });
        // eslint-disable-next-line react/no-did-update-set-state
        this.setState({ scrollingTo: this.props.selectedIndex });
      } else {
        this.scrollview.setPage(this.props.selectedIndex);
        // eslint-disable-next-line react/no-did-update-set-state
        this.setState({ selectedIndex: this.props.selectedIndex });
      }
    }
  }

  handleHorizontalScroll(e) {
    let selectedIndex = e.nativeEvent.position;
    if (selectedIndex === undefined) {
      selectedIndex = Math.round(
        e.nativeEvent.contentOffset.x / this.state.width,
      );
    }
    if (selectedIndex < 0 || selectedIndex >= this.props.count) {
      return;
    }
    if (
      this.state.scrollingTo !== null
      && this.state.scrollingTo !== selectedIndex
    ) {
      return;
    }
    if (
      this.props.selectedIndex !== selectedIndex
      || this.state.scrollingTo !== null
    ) {
      this.setState({ selectedIndex, scrollingTo: null });
      const { onSelectedIndexChange } = this.props;
      if (onSelectedIndexChange) {
        onSelectedIndexChange(selectedIndex);
      }
    }
  }

  adjustCardSize(e) {
    this.setState({
      width: e.nativeEvent.layout.width,
      height: e.nativeEvent.layout.height,
    });
  }

  renderContent() {
    const { width, height } = this.state;
    const style = Platform.OS === 'ios' && styles.card;
    return React.Children.map(this.props.children, (child, i) => (
      <View style={[style, { width, height }]} key={`r_${i}`}>
        {child}
      </View>
    ));
  }

  render() {
    return (
      <PagerView
        ref={(ref) => {
          this.scrollview = ref;
        }}
        initialPage={this.state.initialSelectedIndex}
        onPageSelected={this.handleHorizontalScroll}
        style={styles.container}
      >
        {this.renderContent()}
      </PagerView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    backgroundColor: colors.transparent,
  },
});

CustomViewPager.propTypes = propTypes;
CustomViewPager.defaultProps = defaultProps;
export default CustomViewPager;
