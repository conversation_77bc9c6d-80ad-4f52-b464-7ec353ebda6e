import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { Image, StyleSheet, TouchableOpacity } from 'react-native';

// PropTypes
const propTypes = {
  isChecked: PropTypes.bool,
  onPress: PropTypes.func.isRequired,
  variant: PropTypes.oneOf(['large', 'small']),
};
const defaultProps = {
  isChecked: false,
  variant: 'small',
};

// Constants
const largeUnchecked = require('../../assets/unchecked.png');
const largeChecked = require('../../assets/checked.png');
const smallUnchecked = require('../../assets/incomplete.png');
const smallChecked = require('../../assets/completed.png');

// Class
class Checkbox extends Component {
  getIcon = () => {
    const { isChecked, variant } = this.props;
    if (variant === 'small' && isChecked) return smallChecked;
    if (variant === 'small' && !isChecked) return smallUnchecked;
    if (variant === 'large' && isChecked) return largeChecked;
    if (variant === 'large' && !isChecked) return largeUnchecked;
    throw new Error('invalid checkbox inputs');
  };

  render() {
    return (
      <TouchableOpacity onPress={this.props.onPress} style={styles.container}>
        <Image source={this.getIcon()} />
      </TouchableOpacity>
    );
  }
}

// Export
Checkbox.propTypes = propTypes;
Checkbox.defaultProps = defaultProps;
export default Checkbox;

// Styles
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
});
