import React, { Component, createRef } from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import Icon from 'react-native-vector-icons/FontAwesome';
import { colors } from '../styles';
import CustomActionSheet from './artichoke/common/CustomActionSheet';

const selectActionSheetRef = createRef();
const cptOptions = [
  { label: 'CPT 6', value: 6 },
  { label: 'CPT 7', value: 7 },
];

export default function CPTVersionSelector(props) {
  const selectedCPT = cptOptions.find((v) => v.value === props.cptVersion)?.label || 'CPT 6';
  const showOptions = () => {
    selectActionSheetRef.current?.setModalVisible();
  };
  const changeCPT = (value) => () => {
    props.changeCPTVersion(value);
    selectActionSheetRef.current?.setModalVisible(false);
  };

  return (
    <>
      <TouchableOpacity style={styles.container} onPress={showOptions}>
        <View style={styles.selector}>
          <Text style={styles.text}>{selectedCPT}</Text>
          <Icon
            style={styles.icon}
            name="angle-down"
            size={20}
            color={colors.white}
          />
        </View>
      </TouchableOpacity>

      <CustomActionSheet
        actionSheetRef={selectActionSheetRef}
        title={props.title}
      >
        <RadioForm>
          {cptOptions.map((obj, i) => (
            <RadioButton
              key={i}
              style={{
                paddingHorizontal: '20%',
                paddingVertical: 30,
                flex: 1,
                borderBottomWidth: 1,
                borderBottomColor: colors.actionSheetDivider,
              }}
            >
              <RadioButtonInput
                obj={obj}
                index={i}
                isSelected={obj.value === props.cptVersion}
                onPress={changeCPT(obj.value)}
                buttonWrapStyle={{ justifyContent: 'center', fontSize: 30 }}
                borderWidth={1}
                buttonSize={10}
                buttonOuterSize={20}
                buttonOuterColor={colors.fillDarkGrey}
              />
              <RadioButtonLabel
                obj={obj}
                index={i}
                labelHorizontal
                onPress={changeCPT(obj.value)}
                labelStyle={{
                  fontSize: 17,
                  fontFamily: 'Avenir-Heavy',
                  color: colors.black,
                  lineHeight: 24,
                  paddingLeft: 16,
                }}
              />
            </RadioButton>
          ))}
        </RadioForm>
      </CustomActionSheet>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: 140,
    height: 40,
  },
  selector: {
    borderColor: colors.white,
    borderWidth: 2,
    borderRadius: 27.5,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
  },
  text: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: colors.white,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    position: 'absolute',
    right: 18,
  },
});
