/* eslint-disable react-native/no-color-literals */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import {
  Dimensions, Text, View, TouchableOpacity,
} from 'react-native';
import { curvedScale, scaleWidth } from '../util/responsive';

// Styles
import DailyReadinessGraphSection from '../screens/MainStack/DailyReadiness/DailyReadinessGraphSection';
import { colors } from '../styles';
import { getReadinessRatingLabel } from '../screens/MainStack/DailyReadiness/DailyReadinessConstants';

// PropTypes
const propTypes = {
  assessments: PropTypes.array,
  loading: PropTypes.bool,
  onPress: PropTypes.func,
};
const defaultProps = {
  assessments: [],
  loading: false,
  onPress: null,
};

class DailyReadinessSection extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  getWeekdays = () => {
    const weekday = [];
    const { assessments } = this.props;
    let updated_date = moment();
    if (assessments && assessments.length > 0) {
      updated_date = assessments[0].date;
    }
    for (let i = 1; i < 8; i += 1) {
      const newDate = moment(updated_date).add(i, 'days');
      const day = moment(newDate).format('ddd');
      weekday.push(day.toUpperCase());
    }
    return weekday;
  };

  getUpdatedDate = () => {
    const { assessments } = this.props;
    let updated_at = null;
    if (assessments && assessments.length > 0) {
      updated_at = moment(assessments[0].date).format('MM/DD/YYYY');
    }
    return updated_at;
  };

  getUpdatedAvgTotal = () => {
    const { assessments } = this.props;
    let avgTotal = 0.0;
    let updatedAvgTotal = 0.0;

    if (assessments && assessments.length > 0) {
      assessments.forEach((obj) => {
        avgTotal = obj.average_of_assessment_levels + avgTotal;
      });
      updatedAvgTotal = avgTotal / assessments.length;
    }
    return updatedAvgTotal.toFixed(1);
  };

  getSortedAvgArray = () => {
    const { assessments } = this.props;
    const arrDate = [];
    let sortedDate = [];
    const sortedAvgArr = [];

    if (assessments && assessments.length > 0) {
      assessments.forEach((obj) => {
        arrDate.push(moment(obj.date).format('MM/DD/YYYY'));
      });
      sortedDate = [...new Set(arrDate)];

      sortedDate.forEach((date) => {
        let avg = 0.0;
        let count = 0.0;
        let day = '';
        assessments.forEach((obj) => {
          if (date === moment(obj.date).format('MM/DD/YYYY')) {
            avg = obj.average_of_assessment_levels + avg;
            day = moment(obj.date).format('ddd').toUpperCase();
            count += 1;
          }
        });
        const avgRate = avg / count;
        sortedAvgArr.push({
          average_of_assessment_levels: Number(avgRate),
          day,
        });
      });
    }
    return sortedAvgArr;
  };

  render() {
    return (
      <TouchableOpacity
        disabled={!this.props.onPress}
        onPress={this.props.onPress}
        style={styles.cardStyle}
      >
        <View style={styles.headerSection}>
          <Text style={styles.header}>Daily Readiness</Text>
          {this.getUpdatedDate() && (
            <Text style={styles.subHeader}>
              Updated
              {` ${this.getUpdatedDate()}`}
            </Text>
          )}
        </View>
        {this.props.loading ? null : (
          <View style={styles.readinessHeaderView}>
            <View style={styles.headerSection}>
              <Text style={styles.statusText}>
                Readiness:
                {' '}
                {getReadinessRatingLabel(this.getUpdatedAvgTotal())}
              </Text>
            </View>
            <View style={styles.avgView}>
              <View style={styles.avgDotView} />
              <Text style={styles.avgRating}>{this.getUpdatedAvgTotal()}</Text>
              <View>
                <Text style={styles.avgText}>Avg.</Text>
              </View>
            </View>
          </View>
        )}
        <DailyReadinessGraphSection
          ref={(ref) => {
            this.graphSection = ref;
          }}
          width={Dimensions.get('window').width / 1.2}
          assessments={this.getSortedAvgArray()}
          axesData={this.getWeekdays()}
        />
      </TouchableOpacity>
    );
  }
}

DailyReadinessSection.propTypes = propTypes;
DailyReadinessSection.defaultProps = defaultProps;

export default DailyReadinessSection;

const scale = Dimensions.get('window').width / 400;

const styles = {
  cardStyle: {
    borderRadius: 5 * scale,
    backgroundColor: '#fff',
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowRadius: 4,
    shadowOpacity: 1,
    elevation: 3,
    marginTop: 20 * scale,
    marginHorizontal: scaleWidth(5),
  },
  headerSection: {
    backgroundColor: colors.white,
    height: 30 * scale,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopLeftRadius: 5 * scale,
    borderTopRightRadius: 5 * scale,
  },
  header: {
    color: colors.black,
    fontSize: 16 * scale,
    fontFamily: 'Avenir-Medium',
    paddingLeft: 15 * scale,
    marginTop: 10,
  },
  subHeader: {
    color: colors.subGrey,
    fontFamily: 'Avenir-Roman',
    paddingRight: 12 * scale,
    fontSize: 11 * scale,
    marginTop: 10,
  },
  statusText: {
    color: colors.black,
    fontSize: 15 * scale,
    fontFamily: 'Avenir',
    paddingLeft: 15 * scale,
    marginTop: 10,
  },
  readinessHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  avgView: {
    flexDirection: 'row',
    paddingRight: 12 * scale,
    alignItems: 'center',
  },
  avgDotView: {
    backgroundColor: colors.midGreen,
    width: curvedScale(10),
    height: curvedScale(10),
    borderRadius: curvedScale(5),
    marginRight: curvedScale(5),
  },
  avgRating: {
    color: colors.black,
    fontSize: 25 * scale,
    fontFamily: 'Avenir-Heavy',
    fontWeight: 'bold',
    marginRight: 2,
  },
  avgText: {
    color: colors.black,
    fontSize: 16 * scale,
    fontFamily: 'Avenir-Roman',
    marginTop: curvedScale(10),
  },
  midSection: {
    paddingTop: 8 * scale,
    backgroundColor: colors.white,
    borderBottomLeftRadius: 5 * scale,
    borderBottomRightRadius: 5 * scale,
  },
  emptyStateView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: scaleWidth(5),
  },
};
