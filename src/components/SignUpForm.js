import React from 'react';
import PropTypes from 'prop-types';

// Components
import { ScrollView, View, Alert } from 'react-native';
import Button from './Button';
import TextInput from './TextInput';
import * as validate from '../util/validate';
import { scaleHeight, scaleWidth, curvedScale } from '../util/responsive';
import ScaledText from './ScaledText';

// Styles
import { colors } from '../styles';

const DeviceInfo = require('react-native-device-info');

const propTypes = {
  onPressCreateAccount: PropTypes.func.isRequired,
  optionalFooterView: PropTypes.func,
  isLoading: PropTypes.bool,
  initialEmail: PropTypes.string,
};
const defaultProps = {
  isLoading: false,
  optionalFooterView: () => {},
  initialEmail: '',
};

class SignUpForm extends React.Component {
  constructor(props) {
    super(props);
    this.inputRefs = {};
    this.state = {
      email: props.initialEmail,
      firstName: '',
      lastName: '',
      password: '',
      passwordSecureEntry: false,
      confirmPasswordSecureEntry: false,
      confirmPassword: '',
      validationPassed: false,
    };
  }

  onPasswordBlur = () => {
    if (!validate.password(this.state.password)) {
      this.setState({ hintTextRed: true });
    }
  };

  onTextBlur = (text, field) => {
    if (text) {
      this.setState({ [field]: text.trim() });
    }
  };

  createAccount = () => {
    const validationPassed = this.validateFields();
    if (validationPassed) {
      if (typeof this.props.onPressCreateAccount === 'function') {
        const user = {
          email: this.state.email,
          first_name: this.state.firstName,
          last_name: this.state.lastName,
          password: this.state.password,
        };
        this.props.onPressCreateAccount(user);
      } else {
        Alert.alert('WARNING', 'onPressCreateAccount is a required prop!');
      }
    }
  };

  focusInput = input => {
    const target = this.inputRefs[input];
    if (target && target.textInput) target.textInput.focus();
  };

  updateInput = nextState => {
    const [field, value] = Object.entries(nextState)[0];
    const newState = { [field]: value };
    if (newState.password) {
      if (validate.password(newState.password)) {
        // make hint text grey
        this.setState({ hintTextRed: false });
      }
    }
    this.setState(newState, this.validateInputs);
  };

  validateInputs = () => {
    const fields = Object.keys(this.inputRefs)
      .filter(key => !!this.inputRefs[key])
      .map(key => {
        const { validation, value } = this.inputRefs[key].props;
        return validation(value);
      });
    const validationPassed = !fields.map(field => !!field).includes(false);
    this.setState({ validationPassed });
    return validationPassed;
  };

  validateFields = () => {
    const { email, firstName, lastName } = this.state;
    if (!validate.email(email) || validate.containsAccentedChars(email)) {
      Alert.alert('Email invalid', 'Please enter a valid email to continue.');
      return false;
    }
    if (
      !validate.name(firstName) ||
      validate.containsAccentedChars(firstName)
    ) {
      Alert.alert(
        'First name invalid',
        'Please enter a valid first name to continue.',
      );
      return false;
    }
    if (!validate.name(lastName) || validate.containsAccentedChars(lastName)) {
      Alert.alert(
        'Last name invalid',
        'Please enter a valid last name to continue.',
      );
      return false;
    }
    return true;
  };

  render() {
    let passwordHintStyle = styles.passwordHint;
    if (this.state.hintTextRed) {
      passwordHintStyle = [passwordHintStyle, { color: colors.badRed }];
    }
    return (
      <ScrollView style={{ paddingHorizontal: 24 }}>
        <TextInput
          ref={ref => {
            this.inputRefs.email = ref;
          }}
          value={this.state.email}
          onChangeText={email => {
            this.updateInput({ email });
          }}
          onBlur={event => this.onTextBlur(event.nativeEvent.text, 'email')}
          placeholder="Email"
          placeholderTextColor={colors.subGrey}
          keyboardType="email-address"
          returnKeyType="next"
          validation={validate.email}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(this.state.email)
              ? validate.accentValidationErrorMsg('email')
              : ''
          }
          testID="SignupEmail"
          onSubmitEditing={() => this.focusInput('firstName')}
          inputText={styles.textInput}
          containerStyle={styles.textInputContainer}
        />
        <TextInput
          ref={ref => {
            this.inputRefs.firstName = ref;
          }}
          value={this.state.firstName}
          onChangeText={(value) => {
            const firstName = validate.removeAllSpecialCharacters(value);
            this.updateInput({ firstName });
          }}
          onBlur={event => this.onTextBlur(event.nativeEvent.text, 'firstName')}
          autoCapitalize="words"
          placeholder="First Name"
          placeholderTextColor={colors.subGrey}
          returnKeyType="next"
          validation={validate.name}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(this.state.firstName)
              ? validate.accentValidationErrorMsg('first name')
              : ''
          }
          testID="SignupFirstName"
          onSubmitEditing={() => this.focusInput('lastName')}
          inputText={styles.textInput}
          containerStyle={styles.textInputContainer}
        />
        <TextInput
          ref={ref => {
            this.inputRefs.lastName = ref;
          }}
          value={this.state.lastName}
          onChangeText={(value) => {
            const lastName = validate.removeAllSpecialCharacters(value);
            this.updateInput({ lastName });
          }}
          onBlur={event => this.onTextBlur(event.nativeEvent.text, 'lastName')}
          autoCapitalize="words"
          placeholder="Last Name"
          placeholderTextColor={colors.subGrey}
          returnKeyType="next"
          validation={validate.name}
          accentValidation={validate.containsAccentedChars}
          validationErrorMsg={
            validate.containsAccentedChars(this.state.lastName)
              ? validate.accentValidationErrorMsg('last name')
              : ''
          }
          testID="SignupLastName"
          onSubmitEditing={() => this.focusInput('password')}
          inputText={styles.textInput}
          containerStyle={styles.textInputContainer}
        />
        <View style={{ paddingTop: 24, paddingBottom: 4 }}>
          <ScaledText style={styles.createPasswordText}>
            Create a Password
          </ScaledText>
          <ScaledText style={passwordHintStyle}>
            At least 8 characters, 1 uppercase, 1 lowercase
          </ScaledText>
          <ScaledText style={passwordHintStyle}>
            1 number and 1 special character.
          </ScaledText>
        </View>
        <TextInput
          ref={ref => {
            this.inputRefs.password = ref;
          }}
          value={this.state.password}
          onChangeText={password => {
            this.updateInput({ password });
          }}
          placeholder="Enter Password"
          placeholderTextColor={colors.subGrey}
          returnKeyType="next"
          validation={validate.password}
          onSubmitEditing={() => this.focusInput('confirmPassword')}
          onFocus={() => {
            this.setState({ passwordSecureEntry: true });
          }}
          testID="SignupPassword"
          secureTextEntry={this.state.passwordSecureEntry}
          onBlur={this.onPasswordBlur}
          inputText={styles.textInput}
          containerStyle={styles.textInputContainer}
        />
        <TextInput
          ref={ref => {
            this.inputRefs.confirmPassword = ref;
          }}
          value={this.state.confirmPassword}
          onChangeText={confirmPassword => {
            this.updateInput({ confirmPassword });
          }}
          onBlur={event =>
            this.onTextBlur(event.nativeEvent.text, 'confirmPassword')
          }
          placeholder="Confirm Password"
          placeholderTextColor={colors.subGrey}
          returnKeyType="done"
          validation={value =>
            validate.confirmPassword(this.state.password, value)
          }
          type="password"
          onFocus={() => {
            this.setState({ confirmPasswordSecureEntry: true });
          }}
          testID="SignupConfirmPassword"
          secureTextEntry={this.state.confirmPasswordSecureEntry}
          inputText={styles.textInput}
          containerStyle={styles.textInputContainer}
        />
        <View style={{ paddingTop: 24, paddingBottom: 0 }}>
          <ScaledText style={styles.dataText}>
            I understand and consent to my data being processed in the US
          </ScaledText>
        </View>
        <View style={styles.buttonContainer}>
          <Button
            textStyles={{ fontFamily: 'Avenir-Heavy', fontSize: 16 }}
            title="Continue"
            variant="yellow"
            buttonStyle={[
              styles.createAccountButton,
              { borderRadius: scaleWidth(7.2) },
            ]}
            testID="SignupContinue"
            onPress={() => this.createAccount()}
            disabled={!this.state.validationPassed}
            isLoading={this.props.isLoading}
          />
          {this.props.optionalFooterView()}
        </View>
      </ScrollView>
    );
  }
}

const styles = {
  title: {
    fontFamily: 'Avenir',
    fontSize: 47,
    fontWeight: '900',
    letterSpacing: 0,
    color: colors.nasmBlue,
    textAlign: 'center',
  },
  createPasswordText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: 'black',
  },
  passwordHint: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    letterSpacing: 0,
    textAlign: 'left',
    color: colors.subGrey,
  },
  dataText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
    letterSpacing: 0,
    textAlign: 'center',
    color: colors.subGrey,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 36,
    padding: 8,
  },
  createAccountButton: {
    alignItems: 'center',
    height: DeviceInfo.isTablet() ? scaleHeight(10) : scaleHeight(8),
    marginBottom: scaleHeight(3),
  },
  textInput: {
    fontFamily: 'Avenir-Heavy',
    fontSize: curvedScale(14),
    fontWeight: 'bold',
    color: colors.black,
  },
  textInputContainer: {
    paddingHorizontal: 0,
  },
};

SignUpForm.propTypes = propTypes;
SignUpForm.defaultProps = defaultProps;

export default SignUpForm;
