import React from 'react';
import PropTypes from 'prop-types';
import _cloneDeep from 'lodash.clonedeep';

// Components
import {
  Alert,
  Image,
  LayoutAnimation,
  // Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { connect } from 'react-redux';
import Moment from 'moment';
import Swipeable from './Swipeable';
import { addCorrectiveExercisesToWorkouts } from '../util/programUtils';
import LoadingSpinner from './LoadingSpinner';
import DropDownPicker from './DropDownPicker';
import ScaledText from './ScaledText';
import { curvedScale } from '../util/responsive';
import WorkoutCell from './WorkoutCell';
import { androidSafeLayoutAnimation, EXERCISE_CONTEXTS } from '../constants';
import InformationBox from './InformationBox';
import CorrectiveExerciseToggle from './CorrectiveExerciseToggle';

// Styles
import { colors } from '../styles';

// Redux
import { selectWorkout } from '../reducers/selectedWorkoutReducer';
import {
  editProgramName,
  editProgramCategory,
  addWorkouts,
  removeWorkout,
  sortWorkouts,
  createNewProgram,
  deleteScheduledProgram,
  enableCorrectiveExercises,
} from '../reducers/selectedProgramReducer';
import { programContexts } from '../reducers/programContextReducer';
import { removeAllSpecialCharacters } from '../util/validate';

const mapStateToProps = ({
  programs,
  selectedProgram,
  programContext,
  correctiveExercises,
  selectedClient,
}) => ({
  categories: programs.programCategories,
  program: selectedProgram.program,
  workouts: selectedProgram.entities?.workouts,
  selectedCategory: selectedProgram.program.program_category,
  editable: selectedProgram.editable,
  newProgram: selectedProgram.newProgram,
  programContext,
  correctiveExercises: correctiveExercises.exercises,
  selectedCorrectiveExercises: selectedProgram.correctiveExercises,
  correctiveExercisesEnabled: selectedProgram.correctiveExercisesEnabled,
  selectedClient,
});
const mapDispatchToProps = {
  editProgramName,
  editProgramCategory,
  addWorkouts,
  removeWorkout,
  selectWorkout,
  sortWorkouts,
  createNewProgram,
  deleteScheduledProgram,
  enableCorrectiveExercises,
};

// Images
const removeIcon = require('../resources/btnRemove.png');

// Prop Types
const propTypes = {
  navigation: PropTypes.object.isRequired,
  isProgramNameEditable: PropTypes.bool,
  correctiveExercises: PropTypes.object,
  sortWorkouts: PropTypes.func.isRequired,
  workouts: PropTypes.object.isRequired,
  program: PropTypes.object.isRequired,
  editable: PropTypes.any.isRequired,
  editProgramName: PropTypes.func.isRequired,
  editProgramCategory: PropTypes.func.isRequired,
  programContext: PropTypes.string.isRequired,
  categories: PropTypes.array.isRequired,
  newProgram: PropTypes.any.isRequired,
  selectWorkout: PropTypes.func.isRequired,
  addWorkouts: PropTypes.func.isRequired,
  removeWorkout: PropTypes.func.isRequired,
  deleteScheduledProgram: PropTypes.func.isRequired,
  createProgramTemplate: PropTypes.func,
  enableCorrectiveExercises: PropTypes.func.isRequired,
  correctiveExercisesEnabled: PropTypes.bool.isRequired,
  exerciseContext: PropTypes.number,
};
const defaultProps = {
  isCategoryEditable: false,
  isProgramNameEditable: false,
  correctiveExercises: undefined,
  createProgramTemplate: undefined,
  exerciseContext: EXERCISE_CONTEXTS.WORKOUT_SECTION_EXERCISE,
};

// Component Class
class ProgramDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      swiping: false,
      sorting: false,
      loadingMessage: null,
      programName: this.props.program.name,
      isWorkoutEditable:
        this.props.programContext !== programContexts.SCHEDULING,
    };
    this.swipeableCells = {};
  }

  onPressAdd = () => {
    const excludeWorkouts = Object.values(this.props.workouts).map(
      (workout) => workout.id,
    );
    this.props.navigation.navigate('AddWorkouts', {
      // TODO: use redux instead of params
      addWorkouts: this.onWorkoutsAdded,
      excludeWorkouts,
    });
  };

  onPressDeleteProgram = async () => {
    await this.props.deleteScheduledProgram();
    this.props.navigation.goBack();
  };

  onPressDeleteWorkout = (workout) => {
    this.recenterSwipableCell(workout);
    Alert.alert(
      'Remove Workout',
      `Are you sure you would like to remove ${workout.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            LayoutAnimation.configureNext(androidSafeLayoutAnimation);
            this.props.removeWorkout(workout);
          },
        },
      ],
      undefined,
    );
  };

  getInformationBox() {
    switch (this.props.programContext) {
      case programContexts.SCHEDULING:
        return (
          <InformationBox
            titleComponent={(
              <Text>
                You are viewing a preview of a
                <Text style={styles.infoText}> Program.</Text>
              </Text>
            )}
            descComponent={(
              <Text>
                Once
                <Text style={styles.infoText}> Scheduled</Text>
                <Text>, you will be able to customize it.</Text>
              </Text>
            )}
          />
        );
      case programContexts.RESCHEDULING:
        return (
          <InformationBox
            titleComponent={(
              <Text>
                This is
                <Text style={styles.infoText}>
                  {` ${this.props.selectedClient?.first_name}’s `}
                </Text>
                customized
                <Text style={styles.infoText}> Program.</Text>
              </Text>
            )}
            descComponent={(
              <Text>
                Edits saved only apply to
                <Text style={styles.infoText}>
                  {` ${this.props.selectedClient?.first_name}’s Schedule.`}
                </Text>
              </Text>
            )}
            lastModifiedDate={
              this.props.program && this.props.program.updated_at
                ? Moment(this.props.program.updated_at).format('M/D/YY h:mmA')
                : ''
            }
          />
        );
      default:
        return (
          <InformationBox
            titleComponent={(
              <Text>
                This is a
                <Text style={styles.infoText}> Program Template.</Text>
              </Text>
            )}
            descComponent="Saved changes do not apply to scheduled programs."
            lastModifiedDate={
              this.props.program && this.props.program.updated_at
                ? Moment(this.props.program.updated_at).format('M/D/YY h:mmA')
                : ''
            }
          />
        );
    }
  }

  onWorkoutsAdded = async (newWorkouts) => {
    if (!Array.isArray(newWorkouts) || newWorkouts.length === 0) {
      return;
    }
    // TODO: this is a temp measure to deal with the unsafe mutation done when adding cor. exercises
    // long term solution is to refactor program utils to not mutate function aruments
    const workouts = _cloneDeep(newWorkouts);
    if (this.props.correctiveExercises) {
      addCorrectiveExercisesToWorkouts(
        workouts,
        this.props.correctiveExercises,
      );
    }
    try {
      await this.props.addWorkouts(workouts);
    } catch (error) {
      Alert.alert(
        'Could not save program',
        error.message || error || 'Please try again later',
      );
    }
  };

  getSwipeButtonsForWorkout = (workout, index) => [
    <TouchableOpacity
      style={styles.removeButton}
      onPress={() => this.onPressDeleteWorkout(workout, index)}
      key="delete"
    >
      <View style={styles.swipeButtonContainer}>
        <Image source={removeIcon} />
        <Text style={styles.swipeButtonText}>Remove</Text>
      </View>
    </TouchableOpacity>,
  ];

  navigateToWorkoutSelectionRoute = (workout) => {
    const { editable, exerciseContext } = this.props;

    this.props.selectWorkout(workout, editable);
    if (workout.uploaded_media_id) {
      return this.props.navigation.navigate('VideoWorkoutDetails', {
        workout,
        isEditable: this.state.isWorkoutEditable,
        exerciseContext,
      });
    }
    return this.props.navigation.navigate(
      'EditWorkout',
      // TODO: use redux instead of params
      {
        workout,
        isEditable: this.state.isWorkoutEditable,
        navFrom: 'program',
        exerciseContext,
        user_schedule_workout_id: this.props.user_schedule_workout_id,
      },
    );
  };

  recenterSwipableCell = (workout) => {
    if (workout && this.swipeableCells[workout.id]) {
      this.swipeableCells[workout.id].recenter();
    } else {
      Object.keys(this.swipeableCells).forEach((cell) => {
        cell.recenter();
      });
    }
  };

  showAlert = (
    error,
    optionalErrorMessage = 'Internal error occurred. Try again later.',
  ) => {
    this.setState(
      {
        loadingMessage: null,
      },
      () => {
        Alert.alert('Error', error.message || optionalErrorMessage, [
          { text: 'Ok', style: 'cancel' },
        ]);
      },
    );
  };

  renderCategorySection = () => {
    if (
      !this.props.categories.length
      || this.props.programContext === programContexts.RESCHEDULING
    ) {
      return null;
    }
    // Program category is only editable for a program in a trainer's personal library
    if (
      this.props.editable
      && (this.props.programContext === programContexts.LIBRARY
        || this.props.newProgram)
    ) {
      return (
        <DropDownPicker
          placeholder="Select Category"
          data={this.props.categories}
          label="Category"
          autoSelectFirstValue={false}
          selected={this.props.program.program_category?.id}
          onValueChange={this.props.editProgramCategory}
          outline
          containerStyle={styles.categoryPicker}
          textStyle={styles.picker}
        />
      );
    }
    const category = this.props.program.program_category;
    return (
      <View style={styles.categoryLabel}>
        <ScaledText style={styles.inputLabelText}>Category: </ScaledText>
        <ScaledText style={styles.inputValueText}>
          {category.label || ''}
        </ScaledText>
      </View>
    );
  };

  renderDeleteProgramButton = () => {
    if (!this.props.program.nasm_program_id) {
      return null;
    }
    return (
      <View style={styles.deleteButtonContainer}>
        <TouchableOpacity
          onPress={() => {
            Alert.alert(
              'Remove programs?',
              "This will remove all future scheduled workouts of this program from you client's calendar",
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Remove',
                  onPress: this.onPressDeleteProgram,
                  style: 'destructive',
                },
              ],
            );
          }}
          style={styles.deleteButtonStyle}
        >
          <Text style={styles.deleteButtonText}>Remove Program</Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderHeader = () => {
    const text = this.props.editable && this.state.isWorkoutEditable
      ? 'Add Workouts'
      : 'Workouts';
    return (
      <View>
        <View>
          {this.renderProgramName()}
          {this.renderCategorySection()}
          {this.props.programContext === programContexts.SCHEDULING
            && this.props.correctiveExercises?.length > 0 && (
              <CorrectiveExerciseToggle
                style={styles.correctiveExerciseToggle}
                onPress={() => this.props.navigation.navigate('CorrectiveExercises')}
                onToggle={(enabled) => {
                  if (this.props.selectedCorrectiveExercises?.length > 0) {
                    this.props.enableCorrectiveExercises(enabled);
                  } else {
                    this.props.navigation.navigate('CorrectiveExercises');
                  }
                }}
                enabled={this.props.correctiveExercisesEnabled}
              />
          )}
        </View>
        <View style={styles.addWorkoutContainer}>
          <View style={styles.headerView}>
            <ScaledText stye={styles.addWorkoutText}>{text}</ScaledText>
          </View>
          {this.props.editable && this.state.isWorkoutEditable && (
            <TouchableOpacity onPress={this.onPressAdd} style={styles.addBtn}>
              <Image
                style={styles.addImg}
                resizeMode="contain"
                source={require('../resources/btnAddwhite.png')}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  updateProgramName = (text) => {
    this.setState({ programName: text });
    this.props.editProgramName(text);
  };

  createProgramTemplate = () => {
    if (this.props.createProgramTemplate) {
      this.props.createProgramTemplate();
    } else {
      Alert.alert('Error', 'Something went wrong! Please try again later.');
    }
  };

  renderProgramName = () => {
    const { program } = this.props;
    const isNameEditable = this.props.isProgramNameEditable;

    // Quick Add Porgrams don't have a name to display
    // The name can be empty when editing so check if the name shouldn't be editable too
    if (!program.name && !isNameEditable) {
      return null;
    }
    return (
      <>
        <View style={styles.programNameContainer}>
          <ScaledText style={styles.inputLabelText}>Program Name</ScaledText>
          {isNameEditable && (
            <TextInput
              style={styles.nameInput}
              placeholder="Enter Program Name"
              value={this.state.programName}
              onChangeText={(value) => {
                const filteredValue = removeAllSpecialCharacters(value);
                this.updateProgramName(filteredValue);
              }}
              returnKeyType="done"
              autoFocus={!program.id}
              editable={isNameEditable}
            />
          )}
          {!isNameEditable && (
            <Text style={styles.inputValueText}>
              {this.props.program.name || ''}
            </Text>
          )}
        </View>
        {this.props.programContext === programContexts.RESCHEDULING ? (
          <TouchableOpacity
            onPress={this.createProgramTemplate}
            style={styles.btnView}
          >
            <View style={styles.btnInnerView}>
              <View style={styles.btnRow}>
                <Image
                  source={require('../resources/btnCreate.png')}
                  resizeMode="contain"
                  style={styles.createImg}
                />
                <Text style={styles.createText}>
                  Create Template in Library
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ) : null}
      </>
    );
  };

  renderWorkout = ({ item, getIndex, drag }) => {
    const workout = this.props.workouts[item];
    const index = getIndex();
    const mainWorkoutView = (
      <TouchableOpacity
        onPress={() => this.navigateToWorkoutSelectionRoute(workout)}
        style={[
          styles.workoutCell,
          this.props.editable ? { backgroundColor: colors.white } : null,
        ]}
        onLongPress={this.state.isWorkoutEditable ? drag : null}
      >
        <TouchableOpacity
          style={styles.workoutItem}
          onPressIn={() => {
            if (!this.state.swiping && this.state.isWorkoutEditable) {
              drag();
              this.setState({ sorting: true });
            }
          }}
          onPressOut={({ nativeEvent }) => {
            if (
              this.state.sorting
              && nativeEvent.touches
              && nativeEvent.touches.length === 0
            ) {
              this.setState({ sorting: false });
            }
          }}
        >
          {this.props.editable && this.state.isWorkoutEditable && (
            <Image
              style={{ tintColor: colors.subGrey }}
              source={require('../resources/sortGray.png')}
            />
          )}
        </TouchableOpacity>
        <WorkoutCell workout={workout} />
        <Image
          style={{ height: curvedScale(18), width: curvedScale(18) }}
          source={require('../resources/imgRightArrowGray.png')}
        />
      </TouchableOpacity>
    );
    if (this.props.editable && this.state.isWorkoutEditable) {
      return (
        <Swipeable
          ref={(ref) => {
            this.swipeableCells[workout.id] = ref;
          }}
          rightButtons={this.getSwipeButtonsForWorkout(workout, index)}
          onSwipeStart={() => {
            this.setState({ sorting: false, swiping: true });
          }}
          onSwipeRelease={() => {
            this.setState({ swiping: false });
          }}
        >
          {mainWorkoutView}
        </Swipeable>
      );
    }
    return mainWorkoutView;
  };

  render() {
    if (typeof this.state.loadingMessage === 'string') {
      return <LoadingSpinner title={this.state.loadingMessage} visible />;
    }
    let count = 0;
    if (this.props.program.workouts) {
      count = this.props.program.workouts.reduce(
        (exerciseCount, workout) => exerciseCount + (workout.exercise_count || 0),
        0,
      );
    } else {
      return null;
    }
    return (
      <View style={styles.container}>
        {this.getInformationBox()}
        <View style={styles.container}>
          <DraggableFlatList
            key={`${count}`}
            data={this.props.program?.workouts}
            extraData={this.props.workouts}
            keyExtractor={(item, index) => `draggable-item-${index}`}
            onDragEnd={({ data }) => {
              this.props.sortWorkouts(data);
            }}
            renderItem={this.renderWorkout}
            ListHeaderComponent={this.renderHeader}
            scrollEnabled={!this.state.swiping}
            activationDistance={20}
          />
          {this.renderDeleteProgramButton()}
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  addBtn: {
    width: 33,
    height: 33,
    borderRadius: 16.5,
    backgroundColor: colors.macaroniAndCheese,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addImg: {
    width: 25,
    height: 25,
  },
  workoutItem: {
    paddingLeft: 28,
  },
  headerView: {
    flex: 1,
  },
  picker: {
    fontWeight: '500',
    fontSize: 13,
    textAlign: 'right',
  },
  addWorkoutContainer: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.veryLightBlue,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    height: 60,
    paddingHorizontal: curvedScale(20),
  },
  addWorkoutText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 16,
    color: colors.black,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  inputLabelText: {
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(14),
    color: colors.subGrey,
    paddingBottom: 10,
  },
  inputValueText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    color: colors.black,
    paddingBottom: curvedScale(5),
  },
  programNameContainer: {
    paddingTop: curvedScale(20),
    paddingHorizontal: curvedScale(20),
    borderBottomWidth: 1,
    borderColor: colors.veryLightBlue,
  },
  nameInput: {
    height: curvedScale(40),
    margin: 0,
    padding: 0,
    fontSize: curvedScale(17),
    borderWidth: 0,
  },
  categoryPicker: {
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    borderColor: colors.veryLightBlue,
    height: curvedScale(60),
    marginTop: 0,
    marginBottom: 0,
    paddingHorizontal: curvedScale(20),
  },
  categoryLabel: {
    paddingTop: curvedScale(20),
    paddingHorizontal: curvedScale(20),
  },
  workoutCell: {
    flexDirection: 'row',
    paddingVertical: 13,
    borderColor: colors.veryLightBlue,
    borderBottomWidth: 1,
  },
  removeButton: {
    backgroundColor: colors.black,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  swipeButtonContainer: {
    flex: 1,
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  swipeButtonText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 11,
    color: colors.white,
    marginTop: 3,
  },
  deleteButtonContainer: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderColor: colors.subGreyLight,
  },
  deleteButtonStyle: {
    width: '60%',
    marginTop: 15,
    marginBottom: 10,
    height: curvedScale(36),
    borderRadius: curvedScale(18),
    borderWidth: 1,
    borderColor: colors.badRed,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  deleteButtonText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 12,
    fontWeight: '700',
    color: colors.badRed,
    paddingHorizontal: 20,
    paddingVertical: 7,
  },
  btnView: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  btnInnerView: {
    borderColor: colors.fillDarkGrey,
    padding: 5,
    paddingHorizontal: 20,
    borderWidth: 2,
    borderRadius: 50,
  },
  btnRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  createImg: {
    height: 15,
    width: 15,
  },
  createText: {
    fontSize: 14,
    fontFamily: 'Avenir-Heavy',
    color: colors.fillDarkGrey,
    marginLeft: 10,
  },
  infoText: {
    fontFamily: 'Avenir-Black',
  },
  correctiveExerciseToggle: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 30,
  },
});

ProgramDetails.propTypes = propTypes;
ProgramDetails.defaultProps = defaultProps;

// Exports
// TODO: We should not use refs when callbacks are more suitable
const options = { forwardRef: true };
export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  options,
)(ProgramDetails);
