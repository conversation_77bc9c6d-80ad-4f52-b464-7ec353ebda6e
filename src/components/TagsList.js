import React from 'react';
import {
  TextInput,
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Text,
  Dimensions,
} from 'react-native';
import { colors } from '../styles';

const closeIcon = require('../assets/iconCloseSmallGrey.png');
// PropTypes
const propTypes = {
  ...TextInput.propTypes,
};
const defaultProps = {
  isEditable: true,
};

// eslint-disable-next-line import/prefer-default-export
export const TagsList = (props) => (
  <View style={styles.textInputContainer}>
    <View style={[styles.selectFieldMainStyle, styles.selectFiledMain]}>
      {props?.value && props.value.length > 0
        ? props.value.map((item) => (
          <TouchableOpacity
            style={[styles.selectFieldMainMultiStyle, { marginBottom: 10 }]}
            disabled={!props.isEditable}
            onPress={() => props.removeBtn(item)}
          >
            <Text style={styles.tagStyle}>{item.tags}</Text>
            {props.isEditable && (
            <View style={styles.imageView}>
              <Image style={styles.closeIcon} source={closeIcon} />
            </View>
            )}
          </TouchableOpacity>
        ))
        : null}
    </View>
  </View>
);

TagsList.propTypes = propTypes;
TagsList.defaultProps = defaultProps;

const scale = Dimensions.get('window').width / 400;

const styles = StyleSheet.create({
  textInputContainer: {
    borderRadius: 10,
  },
  tagStyle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14 * scale,
    fontWeight: '400',
    color: colors.subGrey,
    marginLeft: 5 * scale,
    alignItems: 'center',
  },
  imageView: {
    marginTop: 2,
  },
  selectFieldMainStyle: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 10,
  },
  selectFiledMain: {
    flexWrap: 'wrap',
    width: '100%',
  },
  selectFieldMainMultiStyle: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 7,
    paddingHorizontal: 12,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.boderColor,
    borderRadius: 30,
    marginRight: 7,
  },
  closeIcon: {
    width: 12,
    height: 12,
    marginLeft: 7,
  },
});
