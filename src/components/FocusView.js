import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Text, View, TouchableOpacity, SafeAreaView,
} from 'react-native';
import { KeyboardAwareFlatList } from 'react-native-keyboard-aware-scroll-view';
import ScaledText from './ScaledText';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  selectedChangedCallback: PropTypes.func,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string,
      id: PropTypes.string,
    }),
  ),
  value: PropTypes.string,
  title: PropTypes.string,
  focusCustomStyle: PropTypes.object,
  itemTitleCustomStyle: PropTypes.object,
  itemCustomStyle: PropTypes.object,
  itemDescriptionCustomStyle: PropTypes.object,
  isShowItemSeparator: PropTypes.bool,
  isShowBullet: PropTypes.bool,
};
const defaultProps = {
  selectedChangedCallback: () => {},
  data: [],
  value: undefined,
  title: 'Focus',
  focusCustomStyle: {},
  itemTitleCustomStyle: {},
  itemCustomStyle: {},
  itemDescriptionCustomStyle: {},
  isShowItemSeparator: false,
  isShowBullet: false,
};

class FocusView extends Component {
  onPress = (item) => {
    if (this.props.selectedChangedCallback) {
      this.props.selectedChangedCallback(item);
    }
  };

  renderItem = ({ item }) => {
    let { title, description } = item;
    if (!title && item.name) {
      title = item.name;
    }
    const selected = this.props.value === item.id;
    return (
      <TouchableOpacity
        key={item.id}
        style={[styles.radioRow, this.props.itemCustomStyle]}
        onPress={() => this.onPress(item)}
      >
        <View style={styles.radioCircle}>
          {selected ? <View style={styles.radioCircleSelected} /> : null}
        </View>
        <View style={{ paddingLeft: 10, flex: 1 }}>
          <ScaledText
            style={[styles.itemTitle, this.props.itemTitleCustomStyle]}
          >
            {title}
          </ScaledText>
          {!!description && (
            <View style={styles.bulletPointContainer}>
              {this.props.isShowBullet && (
                <Text style={styles.bulletText}>
                  {'\u2022'}
                  {' '}
                </Text>
              )}
              <Text
                style={[
                  styles.itemDescription,
                  this.props.itemDescriptionCustomStyle,
                ]}
              >
                {description}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  render() {
    return (
      <SafeAreaView style={[styles.container, this.props.style || {}]}>
        <KeyboardAwareFlatList
          testID="keyboardAwareFlatList"
          data={this.props.data}
          extraData={this.props}
          keyExtractor={(item) => item.id}
          renderItem={this.renderItem}
          ListHeaderComponent={(
            <ScaledText style={[styles.focusText, this.props.focusCustomStyle]}>
              {this.props.title}
            </ScaledText>
          )}
          ItemSeparatorComponent={() => (this.props.isShowItemSeparator ? (
            <View style={styles.dividerStyle} />
          ) : null)}
          ListFooterComponent={this.props.Footer}
        />
      </SafeAreaView>
    );
  }
}

FocusView.propTypes = propTypes;
FocusView.defaultProps = defaultProps;

export default FocusView;

const styles = {
  container: {
    flex: 1,
    borderColor: colors.subGreyLight,
    borderBottomWidth: 1,
  },
  focusText: {
    fontSize: 13,
    fontFamily: 'Avenir-Roman',
    color: colors.subGrey,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  radioRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgb(124, 128, 132)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioCircleSelected: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: 'rgb(37, 146, 236)',
  },
  itemTitle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    color: colors.black,
  },
  itemDescription: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.black,
  },
  dividerStyle: {
    height: 1,
    width: '100%',
    backgroundColor: colors.disclaimerGrey,
  },
  bulletText: {
    fontSize: 11,
    fontFamily: 'Avenir-Roman',
    color: colors.subGrey,
    marginRight: 2,
  },
  bulletPointContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
};
