import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { View, TouchableOpacity, TextInput } from 'react-native';
import ScaledText from './ScaledText';
import { curvedScale, scaleHeight } from '../util/responsive';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  onPress: PropTypes.func,
  label: PropTypes.string.isRequired,
  value: PropTypes.string,
  unit: PropTypes.string,
  keyboardType: PropTypes.string,
  onChangeText: PropTypes.func,
  onKeyPress: PropTypes.func,
  placeholder: PropTypes.string,
  editable: PropTypes.bool,
  inputContainerStyle: PropTypes.any,
  textInputStyle: PropTypes.any,
  labelStyle: PropTypes.any,
  maxLength: PropTypes.number,
};

const defaultProps = {
  value: null,
  onPress: null,
  keyboardType: null,
  onChangeText: null,
  onKeyPress: null,
  placeholder: null,
  editable: true,
  inputContainerStyle: null,
  textInputStyle: null,
  labelStyle: null,
  maxLength: null,
  unit: null,
};

class BubbleInput extends Component {
  onPressed = () => {
    if (this.props.onPress) {
      this.props.onPress();
    }
    if (this.props.editable && this.textInput) {
      this.textInput.focus();
    }
  };

  render() {
    return (
      <TouchableOpacity
        onPress={this.onPressed}
        disabled={!this.props.editable}
        style={[styles.inputContainer, this.props.inputContainerStyle]}
      >
        <ScaledText
          style={this.props.labelStyle ? this.props.labelStyle : styles.label}
        >
          {this.props.label}
        </ScaledText>
        {this.props.keyboardType ? (
          <View
            style={{
              flex: 1,
              flexDirection: 'row-reverse',
              alignItems: 'center',
            }}
          >
            {this.props.unit !== null && (
              <ScaledText style={styles.unit}>{this.props.unit}</ScaledText>
            )}
            <TextInput
              pointerEvents="none"
              value={this.props.value}
              onChangeText={this.props.onChangeText}
              onKeyPress={this.props.onKeyPress}
              maxLength={this.props.maxLength}
              keyboardType={this.props.keyboardType}
              selection={this.props.selection}
              style={
                this.props.textInputStyle
                  ? this.props.textInputStyle
                  : styles.input
              }
              placeholder={this.props.placeholder}
              editable={this.props.editable}
              ref={(ref) => {
                this.textInput = ref;
              }}
              placeholderStyle={{ paddingRight: 1, color: colors.badRed }}
            />
          </View>
        ) : (
          <View
            style={{ flex: 1, flexDirection: 'row', alignContent: 'flex-end' }}
          >
            {this.props.unit !== null && (
              <ScaledText style={[styles.input, { flex: 1 }]}>
                {this.props.value}
              </ScaledText>
            )}
            <ScaledText style={styles.unit}>{this.props.unit}</ScaledText>
          </View>
        )}
      </TouchableOpacity>
    );
  }
}

BubbleInput.propTypes = propTypes;
BubbleInput.defaultProps = defaultProps;

export default BubbleInput;

const styles = {
  inputContainer: {
    backgroundColor: '#f8f9f9',
    height: scaleHeight(4.5),
    borderRadius: curvedScale(20),
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 6,
  },
  label: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.black,
  },
  input: {
    fontFamily: 'Avenir',
    fontSize: curvedScale(13),
    fontWeight: '500',
    color: colors.subGrey,
    textAlign: 'right',
    width: 60,
    paddingBottom: 0,
    paddingTop: 0,
  },
  unit: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.subGrey,
    height: '100%',
  },
};
