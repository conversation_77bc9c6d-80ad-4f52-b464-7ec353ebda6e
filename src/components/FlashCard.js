import React, { Component } from 'react';
import { View, TouchableOpacity } from 'react-native';

// Components
import Card<PERSON><PERSON> from './CardFlip';
import { scaleHeight } from '../util/responsive';
import ScaledText from './ScaledText';

// Styles
import { colors, shadow } from '../styles';

class FlashCard extends Component {
  render() {
    return (
      <CardFlip
        style={styles.cardContainer}
        ref={(card) => {
          this.card = card;
        }}
        flipDirection="y"
      >
        <TouchableOpacity
          style={styles.mainCard}
          activeOpacity={1}
          onPress={() => this.props.onCardPress(this.card)}
        >
          <View
            style={[
              styles.mainCardHeader,
              {
                backgroundColor: this.props.color
                  ? this.props.color
                  : colors.white,
                paddingVertical: 30,
              },
            ]}
          >
            <ScaledText style={styles.cardHeaderText}>
              {this.props.domainName}
            </ScaledText>
          </View>
          <View style={styles.cardBody}>
            <ScaledText scaleFactor={1.0} style={styles.cardBodyText}>
              {this.props.cardData.item.front.content.text}
            </ScaledText>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.mainCard}
          activeOpacity={1}
          onPress={() => {
            this.props.onCardPress(this.card);
          }}
        >
          <View
            style={[
              styles.mainCardHeader,
              {
                backgroundColor: this.props.color
                  ? this.props.color
                  : colors.white,
              },
            ]}
          >
            <ScaledText style={styles.cardHeaderText}>
              {this.props.domainName}
            </ScaledText>
          </View>
          <View style={styles.cardBackContainer}>
            <View style={styles.cardBody}>
              <ScaledText scaleFactor={1.0} style={styles.cardBodyText}>
                {this.props.cardData.item.back.content.text}
              </ScaledText>
            </View>
            <ScaledText style={styles.chapter}>
              {this.props.cardData.item.tags[0]}
            </ScaledText>
          </View>
        </TouchableOpacity>
      </CardFlip>
    );
  }
}

const styles = {
  cardContainer: {
    flex: 1,
  },
  mainCard: {
    backgroundColor: colors.white,
    flex: 1,
    borderRadius: 15,
    marginVertical: scaleHeight(5),
    ...shadow,
  },
  mainCardHeader: {
    minHeight: '10%',
    borderTopRightRadius: 15,
    borderTopLeftRadius: 15,
    justifyContent: 'center',
  },
  cardHeaderText: {
    color: colors.white,
    textAlign: 'center',
    marginHorizontal: 25,
  },
  cardBody: {
    flex: 1,
    justifyContent: 'center',
  },
  cardBodyText: {
    fontSize: 18,
    marginHorizontal: '7%',
    color: colors.black,
    textAlign: 'center',
  },
  cardBackContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  chapter: {
    textAlign: 'center',
    color: colors.subGrey,
    margin: '10%',
  },
};

export default FlashCard;
