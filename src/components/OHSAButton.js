import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Text, TouchableOpacity, StyleSheet, View,
} from 'react-native';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  onPress: PropTypes.func.isRequired,
  id: PropTypes.string,
  selected: PropTypes.bool,
};

const defaultProps = {
  id: 'default',
  selected: false,
  containerStyle: null,
  buttonStyle: null,
  textStyles: null,
};

// Component definition
export default class OHSAButton extends Component {
  onPress() {
    this.props.onPress(this.props.id);
  }

  render() {
    return (
      <TouchableOpacity style={styles.container} onPress={() => this.onPress()}>
        <View
          style={
            this.props.selected ? styles.selectedStyle : styles.unselectedStyle
          }
        >
          <Text style={styles.textStyle}>
            {this.props.selected ? 'YES' : 'NO'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }
}

// Export
OHSAButton.propTypes = propTypes;
OHSAButton.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    paddingHorizontal: 6,
    flex: 1,
  },
  selectedStyle: {
    flex: 1,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.goodGreen,
    borderRadius: 16,
  },
  unselectedStyle: {
    flex: 1,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.subGrey,
    borderRadius: 16,
  },
  textStyle: {
    color: colors.white,
  },
});
