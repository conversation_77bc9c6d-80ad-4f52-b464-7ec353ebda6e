import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
// Components
import { View } from 'react-native';
import { KeyboardAwareSectionList } from 'react-native-keyboard-aware-scroll-view';
import BubbleInput from './BubbleInput';
import ScaledText from './ScaledText';
import { curvedScale, scaleHeight } from '../util/responsive';

// Styles
import { colors } from '../styles';

const propTypes = {
  editable: PropTypes.bool,
  assessments: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.number,
      performance_assessment: PropTypes.shape({
        name: PropTypes.string,
        display_name: PropTypes.string,
        category: PropTypes.string,
      }),
    }),
  ),
  onAssessmentChanged: PropTypes.func,
  inputContainer: PropTypes.any,
  input: PropTypes.any,
};

const defaultProps = {
  editable: false,
  assessments: [],
  onAssessmentChanged: () => {},
  inputContainer: null,
  input: null,
};

class PerformanceAssessmentView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      sections: this.formatAssessments(),
      timesText: {},
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.assessments !== this.props.assessments) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({
        sections: this.formatAssessments(),
      });
    }
  }

  onAssessmentChanged = (newText, name, sectionTitle) => {
    const text = newText.replace(/-/g, '');
    let value = 0;
    const { timesText } = this.state;
    if (name === 'one_mile_run' || name === 'five_hundred_meter_row') {
      const timeText = this.formatTime(text);
      timesText[name] = timeText;
      const valueForTime = this.getValueForTime(timeText);
      this.props.onAssessmentChanged(name, valueForTime, sectionTitle);
    } else if (text.length >= 0) {
      value = text.length === 0 ? 0 : parseInt(text, 10);
      timesText[name] = value;
      this.props.onAssessmentChanged(name, value, sectionTitle);
    }
    this.setState({ timesText });
  };

  getTimeForValue = (value) => {
    if (!value) {
      return null;
    }
    let min = `${Math.floor(value / 60)}`;
    let sec = `${value % 60}`;
    if (min.length === 1) {
      min = `0${min}`;
    }
    if (sec.length === 1) {
      sec = `0${sec}`;
    }
    return `${min}:${sec}`;
  };

  getValueForTime = (timeString) => {
    if (timeString) {
      const min = parseInt(timeString.substring(0, timeString.length - 2), 10);
      const sec = parseInt(timeString.substring(timeString.length - 2), 10);
      return min * 60 + sec;
    }
    return null;
  };

  formatAssessments = () => {
    const sections = [
      {
        title: 'Cardio',
        data: [
          {
            name: 'three_minute_step',
            display_name: '3 Minute Step',
            value: 0,
          },
          {
            name: 'one_minute_jumping_jacks',
            display_name: '1 Minute Jumping Jacks',
            value: 0,
          },
          {
            name: 'one_mile_run',
            display_name: '1 Mile Run',
            value: 0,
          },
          {
            name: 'five_hundred_meter_row',
            display_name: '500 Meter Row',
            value: 0,
          },
        ],
      },
      {
        title: 'Endurance',
        data: [
          {
            name: 'one_minute_pushups',
            display_name: '1 Minute Pushups',
            value: 0,
          },
          {
            name: 'one_minute_squats',
            display_name: '1 Minute Squats',
            value: 0,
          },
        ],
      },
      {
        title: 'Strength',
        data: [
          {
            name: 'squat',
            display_name: 'Squat',
            value: 0,
          },
          {
            name: 'bench',
            display_name: 'Bench',
            value: 0,
          },
          {
            name: 'seated_row',
            display_name: 'Seated Row',
            value: 0,
          },
        ],
      },
    ];

    this.props.assessments.forEach((assessment) => {
      for (let i = 0; i < sections.length; i += 1) {
        if (sections[i].title === assessment.performance_assessment.category) {
          const { data } = sections[i];
          for (let j = 0; j < data.length; j += 1) {
            if (data[j].name === assessment.performance_assessment.name) {
              data[j].value = assessment.value;
              break;
            }
          }
          break;
        }
      }
    });

    return sections;
  };

  formatTime = (time) => {
    if (!time.length) return '';
    const parsed = parseInt(time.replace(':', ''), 10);
    const digits = parsed.toString().split('');
    switch (digits.length) {
      case 1:
        return `00:0${digits.join('')}`;
      case 2:
        return `00:${digits.join('')}`;
      case 3:
        return `0${digits.shift()}:${digits.join('')}`;
      case 4: {
        const minutes = digits.slice(0, 2).join('');
        const seconds = digits.slice(2).join('');
        return `${minutes}:${seconds}`;
      }
      default:
        return time.slice(0, -1);
    }
  };

  renderItem = ({ item, section }) => {
    let unit = null;
    let maxDigits = 3;
    if (section.title === 'Strength') {
      unit = ` ${this.props.currentUser.unit_weight}`;
    }
    let value = '';
    let placeholder = '0';
    if (item.name === 'one_minute_jumping_jacks') {
      placeholder = '00';
    }
    if (item.name === 'three_minute_step') {
      placeholder = '000';
      maxDigits = 3;
    }
    if (
      item.name === 'one_mile_run'
      || item.name === 'five_hundred_meter_row'
    ) {
      placeholder = '00:00';
      maxDigits = 6;
      value = this.state.timesText[item.name]
        ? this.state.timesText[item.name]
        : this.getTimeForValue(item.value);
    } else if (this.state.timesText[item.name]) {
      value = this.state.timesText[item.name];
    } else if (item.value !== 0) {
      value = `${item.value}`;
    }
    return (
      <BubbleInput
        onChangeText={(text) => this.onAssessmentChanged(text, item.name, section.title)}
        placeholder={placeholder}
        maxLength={maxDigits}
        editable={this.props.editable}
        label={`${item.display_name}:`}
        value={value}
        unit={unit}
        keyboardType="number-pad"
        inputContainerStyle={[styles.inputContainer, this.props.inputContainer]}
        textInputStyle={[styles.input, this.props.input]}
      />
    );
  };

  renderSectionHeader = ({ section }) => (
    <View
      style={
        section.title === 'Cardio'
          ? styles.sectionHeader
          : styles.sectionHeaderCardio
      }
    >
      <ScaledText style={styles.sectionHeaderTitle}>{section.title}</ScaledText>
    </View>
  );

  render() {
    return (
      <KeyboardAwareSectionList
        renderItem={this.renderItem}
        renderSectionHeader={this.renderSectionHeader}
        sections={this.state.sections}
        extraData={this.state}
        stickySectionHeadersEnabled={false}
        keyExtractor={(item) => item.name}
        initialNumToRender={9}
        style={styles.container}
        contentContainerStyle={styles.contentContainerStyle}
        extraScrollHeight={50}
      />
    );
  }
}

const styles = {
  container: {
    height: '100%',
  },
  contentContainerStyle: {
    paddingBottom: 50,
  },
  sectionHeaderCardio: {
    borderBottomWidth: 1,
    paddingBottom: 10,
    marginTop: 40,
    marginBottom: 12,
    borderColor: colors.subGreyLight,
  },
  sectionHeader: {
    borderBottomWidth: 1,
    paddingBottom: 10,
    marginTop: 14,
    marginBottom: 12,
    borderColor: colors.subGreyLight,
  },
  sectionHeaderTitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.black,
    marginHorizontal: '5%',
  },
  inputContainer: {
    backgroundColor: '#f5f5f5',
    height: scaleHeight(4),
    borderRadius: 3,
    borderColor: '#e8eaec',
    borderWidth: 1,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    marginHorizontal: '5%',
    marginVertical: 5,
  },
  label: {
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    color: colors.black,
  },
  input: {
    fontFamily: 'Avenir',
    fontSize: curvedScale(14),
    width: '100%',
    fontWeight: '500',
    color: colors.subGrey,
    textAlign: 'right',
    paddingBottom: 0,
    paddingTop: 0,
  },
};

PerformanceAssessmentView.propTypes = propTypes;
PerformanceAssessmentView.defaultProps = defaultProps;

const mapStateToProps = (state) => ({ currentUser: state.currentUser });
const mapDispatchToProps = {};
export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(PerformanceAssessmentView);
