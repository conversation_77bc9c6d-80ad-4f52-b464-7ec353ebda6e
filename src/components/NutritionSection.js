import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Moment from 'moment';

// Components
import {
  Dimensions, Text, View, Image,
} from 'react-native';
import NutritionHorizontalBar from './NutritionHorizontalBar';
import { scaleWidth } from '../util/responsive';

// Styles
import { colors } from '../styles';

const avatarLogo = require('../resources/avatarLogo.png');

// PropTypes
const propTypes = {
  nutrition: PropTypes.shape({
    protein: PropTypes.number,
    carbohydrates: PropTypes.number,
    fat: PropTypes.number,
    calories: PropTypes.number,
    updated_at: PropTypes.string,
  }),
  renderEmptyStateView: PropTypes.func,
  showUpdatedText: PropTypes.bool,
  loading: PropTypes.bool,
  avatarConnected: PropTypes.number,
};
const defaultProps = {
  nutrition: null,
  renderEmptyStateView: null,
  showUpdatedText: true,
  loading: false,
  avatarConnected: 0,
};

class NutritionSection extends Component {
  renderEmptyStateView() {
    if (typeof this.props.renderEmptyStateView === 'function') {
      return this.props.renderEmptyStateView();
    }
    return (
      <View>
        <View style={styles.emptyStateView}>
          <Text
            style={{
              fontFamily: 'Avenir-Heavy',
              fontSize: 16 * scale,
              color: colors.subGrey,
              textAlign: 'center',
            }}
          >
            Tap here to record nutrition information
          </Text>
        </View>
        {this.props.avatarConnected ? (
          <View style={styles.emptyStateLogoView}>
            <Image source={avatarLogo} />
          </View>
        ) : null}
      </View>
    );
  }

  renderNutritionInformation(nutrition) {
    if (this.props.loading) {
      return (
        <View>
          <View style={styles.midSection}>
            <View style={[styles.barSection, { width: '70%' }]}>
              <NutritionHorizontalBar
                style={{ paddingVertical: 8 * scale }}
                {...nutrition}
              />
              <View style={styles.dividerBlock}>
                <View
                  style={[
                    styles.dividerHorizontal,
                    { backgroundColor: colors.loadingStateGray },
                  ]}
                />
                <View
                  style={[
                    styles.dividerVertical,
                    { backgroundColor: colors.loadingStateGray },
                  ]}
                />
                <View
                  style={[
                    styles.dividerHorizontal,
                    { backgroundColor: colors.loadingStateGray },
                  ]}
                />
              </View>
              <View
                style={{
                  backgroundColor: colors.loadingStateGray,
                  height: 11 * scale,
                  width: 54 * scale,
                  marginLeft: 24 * scale,
                }}
              />
            </View>
          </View>
          <View style={styles.textSection}>
            <View
              style={[
                styles.legendDot,
                { backgroundColor: colors.loadingStateGray },
              ]}
            />
            <View
              style={{
                height: 12 * scale,
                width: 72 * scale,
                marginLeft: 3 * scale,
                marginRight: 10 * scale,
                backgroundColor: colors.loadingStateGray,
              }}
            />
            <View
              style={[
                styles.legendDot,
                { backgroundColor: colors.loadingStateGray },
              ]}
            />
            <View
              style={{
                height: 12 * scale,
                width: 61 * scale,
                marginLeft: 3 * scale,
                marginRight: 10 * scale,
                backgroundColor: colors.loadingStateGray,
              }}
            />
            <View
              style={[
                styles.legendDot,
                { backgroundColor: colors.loadingStateGray },
              ]}
            />
            <View
              style={{
                height: 12 * scale,
                width: 41 * scale,
                marginLeft: 3 * scale,
                marginRight: 10 * scale,
                backgroundColor: colors.loadingStateGray,
              }}
            />
          </View>
        </View>
      );
    }
    if (!nutrition) {
      return this.renderEmptyStateView();
    }
    return (
      <View>
        <View style={styles.midSection}>
          <View style={[styles.barSection, { width: '70%' }]}>
            <NutritionHorizontalBar
              style={{ paddingVertical: 8 }}
              {...nutrition}
            />
            <View style={styles.dividerBlock}>
              <View
                style={[
                  styles.dividerHorizontal,
                  { backgroundColor: 'rgb(182, 189, 195)' },
                ]}
              />
              <View
                style={[
                  styles.dividerVertical,
                  { backgroundColor: 'rgb(182, 189, 195)' },
                ]}
              />
              <View
                style={[
                  styles.dividerHorizontal,
                  { backgroundColor: 'rgb(182, 189, 195)' },
                ]}
              />
            </View>
            <Text style={styles.kcalText}>
              kcal:
              {Math.round(nutrition.calories)}
            </Text>
          </View>
        </View>
        <View style={styles.textSection}>
          <View style={styles.keyPair}>
            <View
              style={[
                styles.legendDot,
                { backgroundColor: 'rgb(246, 170, 44)' },
              ]}
            />
            <Text style={styles.underText}>
              protein:
              {Math.round(nutrition.protein)}
              g
            </Text>
          </View>
          <View style={styles.keyPair}>
            <View
              style={[
                styles.legendDot,
                { backgroundColor: 'rgb(182, 102, 215)' },
              ]}
            />
            <Text style={styles.underText}>
              carbs:
              {' '}
              {Math.round(nutrition.carbohydrates)}
              g
            </Text>
          </View>
          <View style={styles.keyPair}>
            <View
              style={[styles.legendDot, { backgroundColor: 'rgb(22, 57, 99)' }]}
            />
            <Text style={styles.underText}>
              fat:
              {Math.round(nutrition.fat)}
              g
            </Text>
          </View>
          {this.props.avatarConnected ? (
            <View style={styles.avatarLogoContainer}>
              <Image source={avatarLogo} />
            </View>
          ) : null}
        </View>
      </View>
    );
  }

  render() {
    const { nutrition } = this.props;
    const view = this.renderNutritionInformation(nutrition);
    let updated_at = null;
    if (nutrition && nutrition.updated_at) {
      updated_at = new Moment(nutrition.updated_at).format('M/D/YY');
    }
    return (
      <View style={styles.cardStyle}>
        <View style={styles.headerSection}>
          <Text style={styles.header}>Nutrition</Text>
          {this.props.showUpdatedText && updated_at && (
            <Text style={styles.subHeader}>
              Updated
              {` ${updated_at}`}
            </Text>
          )}
        </View>
        {view}
      </View>
    );
  }
}

NutritionSection.propTypes = propTypes;
NutritionSection.defaultProps = defaultProps;

export default NutritionSection;

const scale = Dimensions.get('window').width / 400;

const styles = {
  cardStyle: {
    borderRadius: 5 * scale,
    backgroundColor: '#fff',
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowRadius: 4,
    shadowOpacity: 1,
    elevation: 3,
    marginTop: 20 * scale,
    marginHorizontal: scaleWidth(5),
  },
  headerSection: {
    backgroundColor: 'rgb(142, 176, 19)',
    height: 40 * scale,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopLeftRadius: 5 * scale,
    borderTopRightRadius: 5 * scale,
  },
  header: {
    color: '#fff',
    fontSize: 18 * scale,
    fontFamily: 'Avenir-Roman',
    paddingLeft: 15 * scale,
  },
  subHeader: {
    color: '#fff',
    fontFamily: 'Avenir-Heavy',
    paddingRight: 12 * scale,
    fontSize: 9 * scale,
  },
  midSection: {
    paddingTop: 8 * scale,
    paddingBottom: 8 * scale,
    paddingLeft: 15 * scale,
    paddingRight: 15 * scale,
    backgroundColor: 'rgb(248, 249, 251)',
  },
  barSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  kcalText: {
    paddingLeft: 24 * scale,
    fontSize: 12 * scale,
    fontFamily: 'Avenir-Roman',
  },
  underText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12 * scale,
    color: 'rgb(124, 128, 132)',
    paddingLeft: 3 * scale,
    paddingRight: 10 * scale,
  },
  legendDot: {
    width: 5,
    height: 5,
    borderRadius: 2.5,
    backgroundColor: '#000',
  },
  dividerBlock: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    right: 5,
  },
  dividerHorizontal: {
    height: 2,
    width: 10,
  },
  dividerVertical: {
    height: 35,
    width: 2,
  },
  textSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16 * scale,
    height: 34 * scale,
  },
  keyPair: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyStateView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16 * scale,
  },
  avatarLogoContainer: {
    flex: 1,
    alignItems: 'flex-end',
    paddingRight: 10,
  },
  emptyStateLogoView: {
    height: '100%',
    width: '100%',
    borderRadius: 2,
    flex: 1,
    paddingRight: 10,
    paddingBottom: 10,
    alignItems: 'flex-end',
  },
};
