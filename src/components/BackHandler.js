import React from 'react';
import PropTypes from 'prop-types';

// Components
import { BackHandler, View } from 'react-native';

// PropTypes
const propTypes = {
  allowed: PropTypes.bool,
  onBackPress: PropTypes.func,
};

const defaultProps = {
  allowed: true,
  onBackPress: null,
};

// Component Class
class BackHandlerComponent extends React.Component {
  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.handleBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.handleBackPress);
  }

  handleBackPress = () => {
    const { allowed, onBackPress } = this.props;
    if (onBackPress) {
      onBackPress();
      return true;
    }
    return !allowed;
  };

  render() {
    return <View />;
  }
}

// Export
BackHandlerComponent.propTypes = propTypes;
BackHandlerComponent.defaultProps = defaultProps;
export default BackHandlerComponent;
