import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  TextInput,
  Platform,
} from 'react-native';
import PropTypes from 'prop-types';

// Styles
import { colors, shadow } from '../styles';
import { removeAllSpecialCharacters } from '../util/validate';

// Images
const searchIcon = require('../resources/searchLightBlue.png');
const searchIconLight = require('../resources/imgSearch.png');
const clearIcon = require('../resources/xCircle.png');

const propTypes = {
  searchText: PropTypes.string.isRequired,
  onChangeText: PropTypes.func.isRequired,
  onPressCancelSearch: PropTypes.func,
  autoFocus: PropTypes.bool,
  clearable: PropTypes.bool,
  renderActionButton: PropTypes.func,
  light: PropTypes.bool,
  paddingTop: PropTypes.number,
  shadow: PropTypes.bool,
  placeholder: PropTypes.string,
};

const defaultProps = {
  shadow: true,
  placeholder: 'SEARCH',
};

class HeaderSearchBar extends Component {
  render() {
    return (
      <View
        style={[
          this.props.light ? styles.searchHeaderLight : styles.searchHeader,
          { paddingTop: this.props.paddingTop },
          this.props.shadow ? shadow : {},
        ]}
      >
        <View
          style={
            this.props.light
              ? styles.inputContainerLight
              : styles.inputContainer
          }
        >
          <Image
            style={
              this.props.light ? styles.searchImageLight : styles.searchImage
            }
            source={this.props.light ? searchIconLight : searchIcon}
          />
          <TextInput
            ref={(ref) => {
              this.searchBar = ref;
            }}
            value={this.props.searchText}
            onChangeText={(text) => {
              const filteredText = removeAllSpecialCharacters(text);
              this.props.onChangeText(filteredText);
            }}
            placeholder={this.props.placeholder}
            placeholderTextColor={
              this.props.light ? colors.silver : 'rgba(255, 255, 255, 0.2)'
            }
            keyboardType={
              Platform.OS === 'ios' ? 'ascii-capable' : 'visible-password'
            }
            returnKeyType="done"
            style={this.props.light ? styles.searchBarLight : styles.searchBar}
            autoFocus={this.props.autoFocus}
            underlineColorAndroid="transparent"
            selectionColor={colors.lightBlue}
            autoCorrect={false}
          />
          {this.props.clearable && this.props.searchText.length > 0 && (
            <TouchableOpacity
              style={styles.clear}
              onPress={() => this.props.onChangeText('')}
            >
              <Image source={clearIcon} />
            </TouchableOpacity>
          )}
        </View>
        {this.props.onPressCancelSearch && (
          <TouchableOpacity onPress={this.props.onPressCancelSearch}>
            <Text style={styles.cancelBtn}>CANCEL</Text>
          </TouchableOpacity>
        )}
        {this.props.renderRightView && this.props.renderRightView()}
        {this.props.renderActionButton && this.props.renderActionButton()}
      </View>
    );
  }
}

const styles = {
  searchHeader: {
    backgroundColor: colors.nasmBlue,
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 10,
  },
  searchHeaderLight: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 10,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderColor: colors.subGreyLight,
  },
  inputContainer: {
    flex: 1,
    justifyContent: 'center',
    marginLeft: 15,
    marginRight: 15,
  },
  inputContainerLight: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(211, 215, 219, 0.17)',
    borderRadius: Platform.OS === 'ios' ? 15 : 20,
    marginLeft: 23,
    marginRight: 15,
    paddingLeft: 36,
    paddingRight: 26,
  },
  searchBar: {
    color: colors.white,
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
  },
  searchBarLight: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    height: 40,
    width: '100%',
  },
  searchImage: {
    marginLeft: 16,
  },
  searchImageLight: {
    position: 'absolute',
    left: 10,
  },
  cancelBtn: {
    color: colors.lightBlue,
    marginRight: 16,
    marginBottom: 3,
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
  },
  clear: {
    position: 'absolute',
    right: 10,
  },
};

HeaderSearchBar.porpTypes = propTypes;
HeaderSearchBar.defaultProps = defaultProps;
export default HeaderSearchBar;
