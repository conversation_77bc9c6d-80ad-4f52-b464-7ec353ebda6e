import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Dimensions, View, Text, Image, TouchableOpacity,
} from 'react-native';
import { scaleHeight, scaleWidth } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';

// Images
const completedImage = require('../resources/completed.png');
const incompleteImage = require('../resources/checkmarkIncomplete.png');

const propTypes = {
  goals: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string,
      status: PropTypes.bool,
    }),
  ).isRequired,
  focus: PropTypes.shape({
    name: PropTypes.string,
  }),
  onPress: PropTypes.func,
  onPressFocus: PropTypes.func,
  onPressGoal: PropTypes.func,
  updatedOn: PropTypes.string.isRequired,
  showUpdatedText: PropTypes.bool,
  loading: PropTypes.bool,
  updating: PropTypes.bool,
};

const defaultProps = {
  focus: {
    name: 'Not Set',
  },
  onPress: null,
  onPressFocus: null,
  onPressGoal: null,
  showUpdatedText: true,
  loading: false,
  updating: false,
};

class GoalsSection extends Component {
  renderGoal = (item) => {
    const source = item.status ? completedImage : incompleteImage;
    return (
      <TouchableOpacity
        key={item.id}
        onPress={() => this.props.onPressGoal(item)}
        disabled={!this.props.onPressGoal}
        activeOpacity={this.props.updating ? 1 : 0.2}
        style={styles.goalContainer}
      >
        <Text style={styles.goalName}>{item.name}</Text>
        <Image style={styles.checkbox} key={`${item.status}`} source={source} />
      </TouchableOpacity>
    );
  };

  renderHeader = () => {
    const { name } = this.props.focus;
    return (
      <View>
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Goals</Text>
          {this.props.updatedOn
            ? this.props.updatedOn.length > 0
              && this.props.showUpdatedText
              && !this.props.loading && (
                <Text style={styles.date}>
                  Updated
                  {` ${this.props.updatedOn}`}
                </Text>
            )
            : null}
        </View>
        {this.props.focus.name !== null && (
          <TouchableOpacity
            disabled={!this.props.onPressFocus}
            onPress={this.props.onPressFocus}
            style={styles.focusContainer}
          >
            {!this.props.loading && (
              <Text style={styles.focus}>
                Focus:
                {name}
              </Text>
            )}
            {this.props.loading && (
              <View
                style={{
                  backgroundColor: colors.loadingStateGray,
                  height: 14 * scale,
                  width: 111 * scale,
                }}
              />
            )}
          </TouchableOpacity>
        )}
      </View>
    );
  };

  renderLoadingGoals = () => {
    const loadingGoals = [];
    for (let i = 0; i < 3; i += 1) {
      loadingGoals.push(
        <View key={`load-${i}`} style={styles.goalContainer}>
          <View
            style={{
              backgroundColor: colors.loadingStateGray,
              height: 18 * scale,
              width: 195 * scale,
            }}
          />
          <Image style={styles.checkbox} source={incompleteImage} />
        </View>,
      );
    }
    return <View>{loadingGoals}</View>;
  };

  render() {
    return (
      <TouchableOpacity
        disabled={!this.props.onPress}
        onPress={this.props.onPress}
        style={styles.container}
      >
        {this.renderHeader()}
        {!this.props.loading && this.props.goals.map(this.renderGoal)}
        {this.props.loading && this.renderLoadingGoals()}
      </TouchableOpacity>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  container: {
    borderRadius: 3 * scale,
    backgroundColor: colors.white,
    marginHorizontal: scaleWidth(5),
    marginVertical: scaleHeight(2),
    marginTop: 20 * scale,
    marginBottom: 0,
    ...shadow,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 15 * scale,
    paddingRight: 12 * scale,
    paddingVertical: 8 * scale,
    backgroundColor: colors.azure,
    borderTopRightRadius: 3 * scale,
    borderTopLeftRadius: 3 * scale,
  },
  title: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18 * scale,
    color: colors.white,
  },
  date: {
    fontFamily: 'Avenir',
    fontSize: 9 * scale,
    fontWeight: '900',
    color: colors.white,
  },
  focus: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12 * scale,
    color: colors.black,
  },
  focusContainer: {
    padding: 15 * scale,
  },
  goalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 30 * scale,
    paddingRight: 20 * scale,
    paddingVertical: 13 * scale,
    borderColor: colors.subGreyLight,
    borderTopWidth: 1,
  },
  goalName: {
    fontFamily: 'Avenir-Roman',
    fontSize: 16 * scale,
    color: colors.subGrey,
    marginEnd: 30 * scale,
  },
  checkbox: {
    position: 'absolute',
    end: 18 * scale,
  },
};

GoalsSection.propTypes = propTypes;
GoalsSection.defaultProps = defaultProps;
export default GoalsSection;
