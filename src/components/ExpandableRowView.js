import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
// Libraries
import {
  Alert,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  LayoutAnimation,
} from 'react-native';
import moment from 'moment';
import nasm from '../dataManager/apiConfig';
import {
  calculateSecondsForExercise,
  getDurationFromSeconds,
  getExerciseVariables,
} from '../util/programUtils';
import { track } from '../util/Analytics';
// Components
import Swipeable from './Swipeable';
import ScaledText from './ScaledText';

// Styles
import { colors } from '../styles';

// Constants
import {
  EXERCISE_TYPE,
  USER_ROLES,
  androidSafeLayoutAnimation,
} from '../constants';
import getExerciseName, { getExerciseImage } from '../util/exerciseUtils';
import ROUTINE_TYPES from '../types/RoutineTypes';
import { curvedScale } from '../util/responsive';

// Images
const completeIcon = require('../resources/imgCheckWhite.png');
const incompleteIcon = require('../resources/imgClose.png');
const defaultExerciseImage = require('../resources/imgExerciseBlockDefault.png');
const superSetIcon = require('../resources/superset.png');
const progressionIcon = require('../assets/progressionIcon.png');
const regressionIcon = require('../assets/regressionIcon.png');

// PropTypes
const propTypes = {
  selected: PropTypes.bool,
  identifier: PropTypes.number.isRequired,
  exercises: PropTypes.array.isRequired,
  onPress: PropTypes.func.isRequired,
  onPressDetails: PropTypes.func.isRequired,
  completed: PropTypes.bool.isRequired,
  selectedClientId: PropTypes.string.isRequired,
  onUpdateExercise: PropTypes.func.isRequired,
  onExerciseCompleted: PropTypes.func.isRequired,
  sectionName: PropTypes.string.isRequired,
  title: PropTypes.string,
  program_start_date: PropTypes.string,
  program_end_date: PropTypes.string,
  showLoading: PropTypes.bool,
};

const defaultProps = {
  selected: false,
  title: '',
  program_start_date: moment().utc().format(),
  program_end_date: moment().utc().format(),
};

class ExpandableRowView extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isGroup: !this.props.selectedClient?.id,
    };
  }

  onPress = (id) => {
    this.setAnimation();
    this.props.onPress(id);
  };

  onPressCompleteExercise(exercise) {
    if (!exercise || exercise.isLoading) {
      return;
    }
    exercise.isLoading = true;
    const clientId = this.props.selectedClientId;
    const exerciseId = exercise.id;
    const request = () => (exercise.is_complete
      ? nasm.api.incompleteClientExercises({
        clientId,
        exerciseIds: [exerciseId],
      })
      : nasm.api.completeClientExercises({
        clientId,
        exerciseIds: [exerciseId],
        program_start_date: this.props.program_start_date,
        program_end_date: this.props.program_end_date,
      }));
    request()
      .then((completedExercises) => {
        const completedExercise = completedExercises[0];
        completedExercise.isLoading = false;
        this.props.onUpdateExercise(completedExercise);
      })
      .catch((error) => {
        Alert.alert(
          'Error',
          error.message
            || 'Failed to mark exercise as complete. Please try again later.',
        );
      });
  }

  onSwipeCompoundRoutine(exeData) {
    if (!exeData) {
      return;
    }
    const clientId = this.props.selectedClientId;
    const iscomplete = !exeData.some(
      (exercise) => exercise.is_complete === false,
    );
    const exerciseIds = exeData.map((exercise) => exercise.id);

    const request = () => (iscomplete
      ? nasm.api.incompleteClientExercises({
        clientId,
        exerciseIds,
      })
      : nasm.api.completeClientExercises({
        clientId,
        exerciseIds,
        program_start_date: this.props.program_start_date,
        program_end_date: this.props.program_end_date,
      }));
    request()
      .then((completedExercises) => {
        const completedExercise = completedExercises[0];
        completedExercise.isLoading = false;
        this.props.onUpdateExercise(completedExercise);
        if (this.props.onExerciseCompleted) {
          this.props.onExerciseCompleted();
        }
      })
      .catch((error) => {
        Alert.alert(
          'Error',
          error.message
            || 'Failed to mark exercise as complete. Please try again later.',
        );
      });
  }

  onPressCompleteSection = async () => {
    try {
      if (this.props.completed) {
        await nasm.api.incompleteClientExercises({
          clientId: this.props.selectedClientId,
          exerciseIds: this.props.exercises.map((exercise) => exercise.id),
          program_start_date: this.props.program_start_date,
          program_end_date: this.props.program_end_date,
        });
      } else {
        const exerciseIds = this.props.exercises
          .filter((exercise) => !exercise.is_complete)
          .map((exercise) => exercise.id);
        await nasm.api.completeClientExercises({
          clientId: this.props.selectedClientId,
          exerciseIds,
          program_start_date: this.props.program_start_date,
          program_end_date: this.props.program_end_date,
        });
        track('workout_completed');
        if (this.props.selected) {
          this.onPress(this.props.sectionName);
        }
      }
      await this.props.onExerciseCompleted();
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  getCompletedExercisesCount() {
    let count = 0;
    for (let i = 0; i < this.props.exercises.length; i += 1) {
      if (this.props.exercises[i].is_complete) {
        count += 1;
      }
    }
    return count;
  }

  getNumberTextStyle() {
    let additionalStyling = {};
    if (this.isWorkoutInProgress() && !this.props.completed) {
      additionalStyling = styles.workoutInProgressTextStyle;
    } else if (this.props.completed) {
      additionalStyling = styles.workoutCompletedTextStyle;
    }
    return additionalStyling;
  }

  getTitleTextStyle() {
    let additionalStyling = {};
    if (this.isWorkoutInProgress() && !this.props.completed) {
      additionalStyling = styles.workoutInProgressTextStyle;
    } else if (this.props.completed) {
      additionalStyling = styles.workoutCompletedTextStyle;
    }
    return [styles.workoutTextStyle, additionalStyling];
  }

  setAnimation = () => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
  };

  getDurationForSection(exeData) {
    let seconds = 0;
    let appendPlus = false;
    exeData.forEach((ex) => {
      const exerciseSeconds = calculateSecondsForExercise(ex);
      seconds += exerciseSeconds;
      if (exerciseSeconds === 0) {
        appendPlus = true;
      }
    });
    return getDurationFromSeconds(seconds, appendPlus);
  }

  getSuperSetView = (exeData, isSuperSet, incomplete) => {
    let setsCount = 0;
    exeData.forEach((ex) => {
      if (Number.isInteger(ex.sets)) {
        setsCount += ex.sets;
      }
    });
    return (
      <Swipeable
        style={styles.workoutContainer}
        enabled={
          !(
            this.state.isGroup
            && this.props.currentUser.nasm_role === USER_ROLES.TRAINER
          )
        }
        rightContent={this.state.isGroup ? null : this.renderSwipe(!incomplete)}
        onRightActionRelease={() => this.onSwipeCompoundRoutine(exeData)}
      >
        <View style={styles.superSet(isSuperSet)}>
          <View style={styles.superSetTitleView(incomplete)}>
            <ScaledText numberOfLines={1} style={styles.superSetTitle}>
              {exeData[0]?.compound_routine_title}
            </ScaledText>
            <View style={styles.superSetVarView}>
              <ScaledText style={styles.superSetVar}>
                {this.getDurationForSection(exeData)}
              </ScaledText>
              <ScaledText style={styles.superSetVar}>
                {`${setsCount} sets`}
              </ScaledText>
            </View>
          </View>
          <View style={styles.superSetTagParent}>
            <View style={styles.superSetTagView(isSuperSet, incomplete)}>
              <ScaledText style={styles.superSetTag}>
                {isSuperSet ? 'Superset' : 'Circuit'}
              </ScaledText>
              <Image source={superSetIcon} style={styles.superSetIcon} />
            </View>
          </View>
        </View>
      </Swipeable>
    );
  };

  getExerView = (isGroup, exercise, isSuperSet) => (
    <Swipeable
      key={exercise.id}
      style={styles.workoutContainer}
      enabled={
        !(
          this.state.isGroup
          && this.props.currentUser.nasm_role === USER_ROLES.TRAINER
        )
      }
      rightContent={isGroup ? null : this.renderSwipe(exercise.is_complete)}
      onRightActionRelease={() => this.onPressCompleteExercise(exercise)}
    >
      <TouchableOpacity
        style={styles.workoutHeaderContainer}
        onPress={() => this.props.onPressDetails(exercise)}
        disabled={isGroup}
      >
        <Image
          opacity={
            (exercise.is_complete && !isGroup) || this.props.showLoading
              ? 0.3
              : 1
          }
          source={
            exercise?.image_url
              ? { uri: getExerciseImage(exercise) }
              : defaultExerciseImage
          }
          style={styles.exerciseImage}
        />
        {exercise?.progressions_regressions?.length
          ? this.renderProgressionRegressionIconImage(exercise)
          : null}
        <View
          opacity={exercise.is_complete && !isGroup ? 0.3 : 1}
          style={styles.exerciseNameContainer}
        >
          <ScaledText
            style={styles.workoutTitleTextStyle}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {getExerciseName(this.props.currentUser, exercise)}
          </ScaledText>
        </View>
        <View
          opacity={exercise.is_complete && !isGroup ? 0.3 : 1}
          style={styles.exerciseVariableContainer(isSuperSet)}
        >
          {exercise.is_complete && !isGroup ? (
            <Image source={completeIcon} style={styles.exerciseCompleteImage} />
          ) : (
            this.renderExerciseVariables(exercise)
          )}
        </View>
      </TouchableOpacity>
    </Swipeable>
  );

  secondsToMinutesSeconds = (totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    let seconds = Math.floor(totalSeconds - minutes * 60);
    if (seconds < 10) {
      seconds = `0${seconds}`;
    }
    return `${minutes}:${seconds}`;
  };

  isWorkoutInProgress() {
    const { exercises } = this.props;
    for (let i = 0; i < exercises.length; i += 1) {
      const exercise = exercises[i];
      if (exercise.is_complete === true) {
        return true;
      }
    }
    return false;
  }

  renderProgressionRegressionIconImage = (exercise) => {
    const hasProgression = exercise?.progressions_regressions?.some(
      (ex) => ex.exerciseType === EXERCISE_TYPE.PROGRESSION,
    );
    const hasRegression = exercise?.progressions_regressions?.some(
      (ex) => ex.exerciseType === EXERCISE_TYPE.REGRESSION,
    );

    return (
      <View style={styles.progressionRegressionView}>
        {hasProgression ? (
          <Image
            style={styles.progressionRegressionIcon}
            source={progressionIcon}
            pointerEvents="none"
            opacity={this.props.showLoading ? 0.3 : 1}
          />
        ) : null}
        {hasRegression ? (
          <Image
            style={styles.progressionRegressionIcon}
            source={regressionIcon}
            pointerEvents="none"
            opacity={this.props.showLoading ? 0.3 : 1}
          />
        ) : null}
      </View>
    );
  };

  renderDropdown() {
    const { isGroup } = this.state;
    const exercises = [];
    const exercisesNew = this.props.exercises.reduce((acc, i) => {
      const { compound_routine_id } = i;
      return {
        ...acc,
        [compound_routine_id]: acc[compound_routine_id]
          ? [...acc[compound_routine_id], i].sort(
            (a, b) => a.ordinal - b.ordinal,
          )
          : [i],
      };
    }, {});
    const keys = Object.keys(exercisesNew);
    keys.forEach((exeKey) => {
      const superSetView = [];
      const exeData = exercisesNew[exeKey];
      exeData.forEach((j) => {
        if (exeKey === 'null') {
          const exerView = this.getExerView(isGroup, j, false);
          exercises.push(exerView);
        } else {
          const exerView = this.getExerView(isGroup, j, true);
          superSetView.push(exerView);
        }
      });
      if (superSetView.length) {
        const isSuperSet = exeData[0]?.compound_routine_type === ROUTINE_TYPES.SUPER_SET;
        const incomplete = exeData.some(
          (exercise) => exercise.is_complete === false,
        );
        exercises.push(
          <View style={styles.superSetParent(isSuperSet, incomplete)}>
            {this.getSuperSetView(exeData, isSuperSet, incomplete)}
            {superSetView}
          </View>,
        );
      }
    });
    return exercises;
  }

  renderExerciseVariable(exercise, variable, variables) {
    switch (variable) {
      case 'sets':
        return (
          <Text key={variable} style={styles.variableNumberText}>
            {variables[variable]}
            {' '}
            sets
          </Text>
        );
      case 'reps':
        return (
          <Text key={variable} style={styles.variableNumberText}>
            {variables[variable]}
            {' '}
            reps
          </Text>
        );
      case 'distance':
        if (exercise.distance_units.toLowerCase() === 'meters') {
          return (
            <Text key={variable} style={styles.variableNumberText}>
              {variables[variable]}
              m
            </Text>
          );
        }
        if (exercise.distance_units.toLowerCase() === 'miles') {
          return (
            <Text key={variable} style={styles.variableNumberText}>
              {variables[variable]}
              mi
            </Text>
          );
        }
        if (exercise.distance_units.toLowerCase() === 'yards') {
          return (
            <Text key={variable} style={styles.variableNumberText}>
              {variables[variable]}
              yd
            </Text>
          );
        }
        return null;
      case 'pace':
        if (exercise.pace_units.toLowerCase() === 'watts') {
          return (
            <Text key={variable} style={styles.variableNumberText}>
              {variables[variable]}
              w
            </Text>
          );
        }
        return (
          <Text key={variable} style={styles.variableNumberText}>
            {variables[variable]}
            {' '}
            {exercise.pace_units.toLowerCase()}
          </Text>
        );

      case 'dur_seconds':
        return (
          <Text key={variable} style={styles.variableNumberText}>
            {this.secondsToMinutesSeconds(variables[variable])}
          </Text>
        );
      default:
        return null;
    }
  }

  renderExerciseVariables = (exercise) => {
    const variables = getExerciseVariables(exercise);
    const keys = Object.keys(variables);
    return keys.map((variable) => this.renderExerciseVariable(exercise, variable, variables));
  };

  renderSwipe = (complete) => (
    <View style={styles.swipeContainer(complete)}>
      <View style={styles.swipeContentContainer}>
        {complete ? (
          <Image source={incompleteIcon} />
        ) : (
          <Image source={completeIcon} />
        )}
        <Text style={styles.swipeText}>
          {complete ? 'Incomplete' : 'Complete'}
        </Text>
      </View>
      <View style={{ flex: 1 }} />
    </View>
  );

  renderCompleteButton = () => (
    <TouchableOpacity
      style={
        this.props.completed
          ? styles.completeButtonDisabled
          : styles.completeButtonEnabled
      }
      onPress={this.onPressCompleteSection}
    >
      <ScaledText
        scaleFactor={2}
        style={{
          ...styles.completeButtonText,
          color: this.props.completed ? colors.white : colors.cloudyBlue,
        }}
      >
        Complete
      </ScaledText>
    </TouchableOpacity>
  );

  render() {
    const { isGroup } = this.state;
    return (
      <View key={this.props.identifier}>
        <TouchableOpacity
          style={styles.cardRowDropdownContainer}
          onPress={() => this.onPress(this.props.sectionName)}
          disabled={isGroup}
        >
          <ScaledText
            style={[
              styles.workoutTextStyle,
              this.props.completed && { color: colors.goodGreen },
            ]}
            numberOfLines={1}
          >
            {this.props.title ? this.props.title : this.props.sectionName}
          </ScaledText>
          {isGroup ? null : this.renderCompleteButton()}
        </TouchableOpacity>
        {this.renderDropdown()}
      </View>
    );
  }
}

// Styles
const styles = StyleSheet.create({
  cardRowDropdownContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 21,
    borderBottomWidth: 1,
    borderColor: colors.cloudyBlue,
  },
  workoutTextStyle: {
    flex: 1,
    fontFamily: 'Avenir-Roman',
    fontSize: 16,
    textAlign: 'left',
    color: colors.black,
    marginRight: 5,
  },
  workoutInProgressTextStyle: {
    color: colors.macaroniAndCheese,
  },
  workoutCompletedTextStyle: {
    color: colors.goodGreen,
  },
  workoutContainer: {
    borderBottomWidth: 1,
    borderColor: colors.cloudyBlue,
  },
  workoutHeaderContainer: {
    flexDirection: 'row',
    marginLeft: 20,
    marginVertical: 15,
    marginRight: 20,
  },
  workoutTitleTextStyle: {
    flex: 1,
    fontSize: 15,
    fontFamily: 'Avenir',
    color: colors.black,
    marginTop: 5,
  },
  variableNumberText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.subGrey,
    lineHeight: 22,
  },
  swipeContainer: (complete) => ({
    flex: 1,
    flexDirection: 'row',
    backgroundColor: complete ? colors.nasmRed : colors.goodGreen,
    justifyContent: 'center',
    paddingHorizontal: 25,
    paddingVertical: 20,
  }),
  swipeContentContainer: {
    alignItems: 'center',
    width: 100,
  },
  swipeText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 15,
    color: colors.white,
  },
  completeButtonDisabled: {
    borderRadius: 20,
    borderWidth: 2,
    borderColor: colors.goodGreen,
    backgroundColor: colors.goodGreen,
    alignItems: 'center',
    justifyContent: 'center',
  },
  completeButtonEnabled: {
    borderRadius: 20,
    borderWidth: 2,
    borderColor: colors.cloudyBlue,
    alignItems: 'center',
    justifyContent: 'center',
  },
  completeButtonText: {
    fontFamily: 'Avenir',
    fontSize: 17,
    fontWeight: 'bold',
    color: colors.cloudyBlue,
    marginHorizontal: 20,
    marginVertical: 5,
  },
  exerciseImage: {
    height: 53,
    width: 76,
    borderRadius: 5,
    alignSelf: 'center',
  },
  exerciseNameContainer: {
    flex: 1,
    margin: 10,
  },
  exerciseVariableContainer: (isSuperSet) => ({
    marginLeft: 10,
    paddingLeft: 10,
    borderLeftWidth: 1,
    borderColor: colors.cloudyBlue,
    justifyContent: 'center',
    width: isSuperSet ? 60 : 64,
  }),
  exerciseCompleteImage: {
    alignSelf: 'center',
    tintColor: 'rgb(136,143,152)',
  },
  superSetParent: (isSuperSet, incomplete) => ({
    borderWidth: 1,
    borderColor: isSuperSet ? colors.mildGreen : colors.macaroniAndCheese,
    marginHorizontal: 5,
    opacity: incomplete ? 1 : 0.7,
  }),
  superSet: (isSuperSet) => ({
    backgroundColor: isSuperSet
      ? colors.mildGreenWithOpacity
      : colors.mildYellowWithOpacity,
    paddingHorizontal: 20,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  }),
  superSetTitle: {
    color: colors.black,
    fontFamily: 'Avenir-Heavy',
    fontSize: 18,
  },
  superSetVarView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  superSetVar: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    marginRight: 10,
  },
  superSetTitleView: (incomplete) => ({
    flex: 7,
    opacity: incomplete ? 1 : 0.5,
  }),
  superSetTagParent: {
    flex: 3,
    alignItems: 'flex-end',
  },
  superSetTagView: (isSuperSet, incomplete) => ({
    backgroundColor: isSuperSet ? colors.peaGreen : colors.macaroniAndCheese,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 5,
    paddingHorizontal: 10,
    borderRadius: 20,
    flexDirection: 'row',
    opacity: incomplete ? 1 : 0.5,
  }),
  superSetTag: {
    color: colors.white,
    fontFamily: 'Avenir-Heavy',
    fontSize: 14,
  },
  superSetIcon: {
    alignSelf: 'center',
    marginLeft: 8,
  },
  progressionRegressionView: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    position: 'absolute',
    width: curvedScale(76),
    height: curvedScale(53),
    justifyContent: 'flex-end',
  },
  progressionRegressionIcon: {
    width: curvedScale(17),
    height: curvedScale(17),
    marginBottom: 3,
    marginRight: 3,
    marginLeft: 2,
  },
});

// Export
ExpandableRowView.propTypes = propTypes;
ExpandableRowView.defaultProps = defaultProps;

const mapStateToProps = ({ currentUser, selectedClient, selectedGroup }) => ({
  currentUser,
  selectedClient,
  selectedGroup,
});
const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ExpandableRowView);
