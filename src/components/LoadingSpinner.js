import React from 'react';
import PropTypes from 'prop-types';

// Components
import {
  ActivityIndicator, Text, View, StyleSheet,
} from 'react-native';

// Styles
import { colors } from '../styles';

// Props
const propTypes = {
  backgroundColor: PropTypes.string,
  color: PropTypes.string,
  size: PropTypes.string,
  title: PropTypes.string,
  titleTextStyle: PropTypes.object,
  visible: PropTypes.bool,
  viewStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};
const defaultProps = {
  backgroundColor: 'rgba(0, 0, 0, 0.25)',
  // backgroundColor: colors.white,
  color: colors.subGrey,
  size: 'large',
  title: null,
  titleTextStyle: null,
  visible: false,
  viewStyle: null,
};

// Class
function LoadingSpinner(props) {
  if (props.visible) {
    return (
      <View
        style={[
          styles.container,
          { backgroundColor: props.backgroundColor },
          props.viewStyle,
        ]}
      >
        {props.title && (
          <Text style={[styles.titleTextStyle, props.titleTextStyle]}>
            {`${props.title}`.toUpperCase()}
          </Text>
        )}
        <ActivityIndicator
          animating={props.visible}
          size={props.size}
          color={props.color}
        />
      </View>
    );
  }
  return null;
}

// Exports
LoadingSpinner.propTypes = propTypes;
LoadingSpinner.defaultProps = defaultProps;
export default LoadingSpinner;

// Styles
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    color: colors.subGrey,
    marginBottom: 14,
  },
});
