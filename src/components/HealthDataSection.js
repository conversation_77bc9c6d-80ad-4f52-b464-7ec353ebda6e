import React, { Component } from 'react';
import {
  Dimensions,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import PropTypes from 'prop-types';
import moment from 'moment';
import { curvedScale, scaleHeight, scaleWidth } from '../util/responsive';
import { colors, shadow } from '../styles';

const propTypes = {
  onPress: PropTypes.func,
  updatedOn: PropTypes.string,
  showUpdatedText: PropTypes.bool,
  loading: PropTypes.bool,
  healthData: PropTypes.object,
  isRookConnected: PropTypes.bool,
};

const defaultProps = {
  onPress: null,
  updatedOn: '',
  onPressNoRecords: null,
  showUpdatedText: true,
  loading: false,
  healthData: {},
  isRookConnected: false,
};

const defaultValue = 'N/A';

const getDurationFromSeconds = (seconds) => {
  if (!seconds) {
    return defaultValue;
  }
  if (seconds > 0 && seconds < 60) {
    const duration = `${seconds} sec`;
    return duration;
  }
  if (seconds >= 60 && seconds < 3600) {
    const minutes = Math.floor(moment.duration(seconds, 'seconds').asMinutes());
    const remainingSeconds = seconds - minutes * 60;
    const formattedSeconds = remainingSeconds > 0 ? ` ${remainingSeconds} sec` : '';
    const duration = `${minutes} min`;
    if (formattedSeconds?.length) {
      duration.concat(formattedSeconds);
    }
    return duration;
  }
  const hours = Math.floor(moment.duration(seconds, 'seconds').asHours());
  const minutes = Math.floor(moment.duration(seconds, 'seconds').asMinutes());
  const remainingMinutes = minutes - hours * 60;
  const formattedMinutes = remainingMinutes > 0 ? ` ${remainingMinutes}min` : '';
  let duration = `${hours}hr`;
  if (formattedMinutes?.length) {
    duration += formattedMinutes;
  }
  return duration;
};

const roundOffPhysicalSummaryValue = (value) => {
  if (!value) {
    return defaultValue;
  }
  return Math.round(value);
};

const healthDetails = (healthData) => {
  const physical_summary = healthData?.physical_summary;
  const sleep_duration = healthData?.sleep_summary?.duration;
  const calories_expenditure = physical_summary?.calories?.calories_expenditure_kcal_float;
  const calories_basal = physical_summary?.calories?.calories_basal_metabolic_rate_kcal_float;

  const cardItem1 = {
    title: 'Physical Activity',
    item: [
      {
        title: 'Steps',
        value: roundOffPhysicalSummaryValue(
          physical_summary?.distance?.steps_int,
        ),
      },
      {
        title: 'Total Calories',
        value: roundOffPhysicalSummaryValue(
          calories_basal ?? calories_expenditure,
        ),
      },
      {
        title: 'Workout Duration',
        value: getDurationFromSeconds(
          physical_summary?.activity?.active_seconds_int,
        ),
      },
    ],
  };

  const cardItem2 = {
    title: 'Sleep Activity',
    item: [
      {
        title: 'Time in bed',
        value: getDurationFromSeconds(sleep_duration?.time_in_bed_seconds_int),
        unit: null,
      },
    ],
  };

  const cardItem3 = {
    title: 'Heart Rate',
    item: [
      {
        title: 'HR min',
        value: physical_summary?.heart_rate?.hr_minimum_bpm_int ?? defaultValue,
        unit: physical_summary?.heart_rate?.hr_minimum_bpm_int ? 'bpm' : null,
      },
      {
        title: 'HR avg',
        value: physical_summary?.heart_rate?.hr_avg_bpm_int ?? defaultValue,
        unit: physical_summary?.heart_rate?.hr_avg_bpm_int ? 'bpm' : null,
      },
    ],
  };
  return [cardItem1, cardItem2, cardItem3];
};

class HealthDataSection extends Component {
  cardChildView = (item) => (
    <View style={styles.innerView}>
      <Text style={styles.cardChildTitle}>{item.title}</Text>
      <Text style={styles.cardChildTitle}>{item.value}</Text>
    </View>
  );

  renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitle}>Health Data</Text>
      {this.props.isRookConnected
      && this.props.updatedOn
      && this.props.showUpdatedText
      && !this.props.loading ? (
        <Text style={styles.date}>
          {'Updated '}
          {this.props.updatedOn}
        </Text>
        ) : null}
    </View>
  );

  renderRookConnectView = () => (
    <View style={styles.emptyStateView}>
      <Text style={styles.assessInfoLabel}>
        Tap here to connect health data
      </Text>
    </View>
  );

  renderSectionValues = (item) => (
    <View style={styles.contentView}>
      <Text style={styles.contentText}>{`${item.title}:`}</Text>
      <View style={styles.valueView}>
        <Text style={styles.contentText}>{item.value}</Text>
        {item.unit ? <Text style={styles.contentText}>{item.unit}</Text> : null}
      </View>
    </View>
  );

  renderItem = (item) => (
    <TouchableOpacity
      style={styles.cardView}
      activeOpacity={0.6}
      onPress={() => {}}
    >
      <View>
        <Text style={styles.sectionLabel}>{item.title}</Text>
        {item.item.map(this.renderSectionValues)}
      </View>
    </TouchableOpacity>
  );

  render() {
    return (
      <TouchableOpacity
        disabled={!this.props.onPress}
        onPress={this.props.onPress}
        style={styles.container}
      >
        {this.renderHeader()}
        {this.props.isRookConnected
          ? healthDetails(this.props.healthData).map(this.renderItem)
          : this.renderRookConnectView()}
      </TouchableOpacity>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = StyleSheet.create({
  container: {
    borderRadius: 3 * scale,
    backgroundColor: colors.white,
    marginHorizontal: scaleWidth(5),
    marginVertical: scaleHeight(2),
    marginBottom: 0 * scale,
    ...shadow,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 15 * scale,
    paddingRight: 12 * scale,
    paddingVertical: 8 * scale,
    backgroundColor: colors.black,
    borderTopRightRadius: 3 * scale,
    borderTopLeftRadius: 3 * scale,
  },
  headerTitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18 * scale,
    color: colors.white,
  },
  date: {
    fontFamily: 'Avenir',
    fontSize: 9 * scale,
    fontWeight: '900',
    color: colors.white,
  },
  cardView: {
    flex: 1,
    marginTop: curvedScale(10),
    paddingHorizontal: curvedScale(20),
  },
  innerView: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderColor: colors.gray_1,
    borderWidth: 1,
    paddingHorizontal: curvedScale(15),
    paddingVertical: curvedScale(7),
    marginBottom: curvedScale(7),
    borderRadius: 5,
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowRadius: 1,
    shadowOpacity: 0.5,
    elevation: 1,
  },
  cardChildTitle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14 * scale,
    color: colors.black,
  },
  contentView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderColor: colors.gray_1,
    borderWidth: 1.5,
    borderBottomWidth: 5,
    borderRadius: 6,
    padding: curvedScale(10),
    paddingHorizontal: curvedScale(15),
    marginBottom: curvedScale(8),
  },
  contentText: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir',
    fontWeight: '400',
  },
  valueView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: curvedScale(3),
  },
  sectionLabel: {
    color: colors.black,
    fontSize: curvedScale(17),
    fontFamily: 'Avenir-Heavy',
    fontWeight: '800',
    marginVertical: curvedScale(10),
  },
  emptyStateView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16 * scale,
  },
  assessInfoLabel: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 16 * scale,
    color: colors.subGrey,
    textAlign: 'center',
  },
});

HealthDataSection.propTypes = propTypes;
HealthDataSection.defaultProps = defaultProps;
export default HealthDataSection;
