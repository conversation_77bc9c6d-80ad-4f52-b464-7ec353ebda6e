import React from 'react';
import PropTypes from 'prop-types';

import { Text, View, StyleSheet } from 'react-native';

import { colors } from '../styles';
import { curvedScale } from '../util/responsive';

// Props
const propTypes = {
  titleComponent: PropTypes.any,
  descComponent: PropTypes.any,
  lastModifiedDate: PropTypes.string,
};
const defaultProps = {
  titleComponent: '',
  descComponent: '',
  lastModifiedDate: '',
};

function InformationBox(props) {
  return (
    <View style={styles.parentContainer}>
      <View style={styles.container}>
        <Text style={styles.title}>{props.titleComponent}</Text>
        {!!props.descComponent && (
          <Text style={styles.description}>{props.descComponent}</Text>
        )}
      </View>
      {props.lastModifiedDate ? (
        <Text style={styles.modified}>
          {`Last Modified ${props.lastModifiedDate}`}
        </Text>
      ) : null}
    </View>
  );
}

// Exports
InformationBox.propTypes = propTypes;
InformationBox.defaultProps = defaultProps;
export default InformationBox;

// Styles
const styles = StyleSheet.create({
  parentContainer: {
    backgroundColor: colors.transparent,
  },
  container: {
    backgroundColor: colors.fillDarkGrey,
    padding: curvedScale(10),
    paddingHorizontal: curvedScale(25),
  },
  title: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(13),
    lineHeight: curvedScale(25),
    color: colors.white,
  },
  description: {
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(13),
    color: colors.white,
  },
  modified: {
    fontFamily: 'Avenir-Roman',
    fontSize: curvedScale(12),
    textAlign: 'center',
    color: colors.fillDarkGrey,
    marginTop: curvedScale(5),
  },
});
