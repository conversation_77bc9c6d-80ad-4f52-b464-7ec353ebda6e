import React from 'react';
import * as screens from '../../../screens/artichoke';
import { APPLICATION_ROUTES } from '../../../constants';

export default (Stack) => [
  <Stack.Screen
    name={APPLICATION_ROUTES.ARCHIVED_SERVICES}
    key={APPLICATION_ROUTES.ARCHIVED_SERVICES}
    component={screens.ArchivedServices}
    options={screens.ArchivedServices.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.EDIT_SERVICES}
    key={APPLICATION_ROUTES.EDIT_SERVICES}
    component={screens.EditService}
    options={screens.EditService.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.EDIT_PACKAGE}
    key={APPLICATION_ROUTES.EDIT_PACKAGE}
    component={screens.EditPackage}
    options={screens.EditPackage.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.LOCATIONS}
    key={APPLICATION_ROUTES.LOCATIONS}
    component={screens.Locations}
    options={screens.Locations.navigationOptions}
  />,
  <Stack.Screen
      name={APPLICATION_ROUTES.QUICK_ENABLE_LOCATION}
      key={APPLICATION_ROUTES.QUICK_ENABLE_LOCATION}
      component={screens.QuickEnableLocation}
      options={screens.QuickEnableLocation.navigationOptions}
    />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIEW_SERVICES_OFFERING}
    key={APPLICATION_ROUTES.VIEW_SERVICES_OFFERING}
    component={screens.ViewServicesOffering}
    options={screens.ViewServicesOffering.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIEW_SERVICES_OFFERING_FOR_EDIT_PACKAGE}
    key={APPLICATION_ROUTES.VIEW_SERVICES_OFFERING_FOR_EDIT_PACKAGE}
    component={screens.ViewServicesOfferingForEditPackage}
    options={screens.ViewServicesOfferingForEditPackage.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CREATE_SERVICE}
    key={APPLICATION_ROUTES.CREATE_SERVICE}
    component={screens.CreateService}
    options={{
      title: 'Service Details',
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CREATE_PACKAGE}
    key={APPLICATION_ROUTES.CREATE_PACKAGE}
    component={screens.CreatePackage}
    options={{
      title: 'Package Details',
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIEW_SERVICE}
    key={APPLICATION_ROUTES.VIEW_SERVICE}
    component={screens.ViewService}
    options={{
      headerShown: false,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIEW_PACKAGE}
    key={APPLICATION_ROUTES.VIEW_PACKAGE}
    component={screens.ViewPackage}
    options={{
      headerShown: false,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.MY_LOCATIONS}
    key={APPLICATION_ROUTES.MY_LOCATIONS}
    component={screens.EditMyLocation}
    options={screens.EditMyLocation.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CLIENTS_LOCATION}
    key={APPLICATION_ROUTES.CLIENTS_LOCATION}
    component={screens.ClientsLocation}
    options={screens.ClientsLocation.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CREATE_NEW_LOCATION}
    key={APPLICATION_ROUTES.CREATE_NEW_LOCATION}
    component={screens.CreateMyLocationScreen}
    options={screens.CreateMyLocationScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.APP_CREATE_SERVICE}
    key={APPLICATION_ROUTES.APP_CREATE_SERVICE}
    component={screens.AddService}
    options={screens.AddService.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.SELECT_LOCATION_SCREEN}
    key={APPLICATION_ROUTES.SELECT_LOCATION_SCREEN}
    component={screens.SelectLocationScreen}
    options={screens.SelectLocationScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.APP_CREATE_CLIENT}
    key={APPLICATION_ROUTES.APP_CREATE_CLIENT}
    component={screens.AddClient}
    options={screens.AddClient.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CLIENTS_LIST}
    key={APPLICATION_ROUTES.CLIENTS_LIST}
    component={screens.ClientsList}
    options={screens.ClientsList.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.RECURRENCE_SCREEN}
    key={APPLICATION_ROUTES.RECURRENCE_SCREEN}
    component={screens.RecurrenceScreen}
    options={screens.RecurrenceScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.INDIVIDUAL_APPOINTMENT}
    key={APPLICATION_ROUTES.INDIVIDUAL_APPOINTMENT}
    component={screens.IndividualAppointment}
    options={screens.IndividualAppointment.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT}
    key={APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT}
    component={screens.CreateNewAppointmentScreen}
    options={screens.CreateNewAppointmentScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.APPOINTMENTS_DATE_AND_TIME}
    key={APPLICATION_ROUTES.APPOINTMENTS_DATE_AND_TIME}
    component={screens.SetAppointmentsDateTimeScreen}
    options={screens.SetAppointmentsDateTimeScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.EDIT_APPOINTMENT}
    key={APPLICATION_ROUTES.EDIT_APPOINTMENT}
    component={screens.EditAppointment}
    options={screens.EditAppointment.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.LIST_CLIENT_BOOKINGS}
    key={APPLICATION_ROUTES.LIST_CLIENT_BOOKINGS}
    component={screens.ListClientsBookingsScreen}
    options={screens.ListClientsBookingsScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.MULTIPLE_CHECK_IN_CLIENT}
    key={APPLICATION_ROUTES.MULTIPLE_CHECK_IN_CLIENT}
    component={screens.MultipleCheckIn}
    options={screens.MultipleCheckIn.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.REMOTE_LOCATION}
    key={APPLICATION_ROUTES.REMOTE_LOCATION}
    component={screens.RemoteLocation}
    options={screens.RemoteLocation.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIDEO_CALL_LOCATION}
    component={screens.VideoCallLocation}
    options={screens.VideoCallLocation.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CREATE_NEW_SLOTBLOCKER}
    key={APPLICATION_ROUTES.CREATE_NEW_SLOTBLOCKER}
    component={screens.CreateNewSlotBlockerScreen}
    options={screens.CreateNewSlotBlockerScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.RECURRENCE_SCREEN_SLOT_BLOCKER}
    key={APPLICATION_ROUTES.RECURRENCE_SCREEN_SLOT_BLOCKER}
    component={screens.RecurrenceScreenSlotBlocker}
    options={screens.RecurrenceScreenSlotBlocker.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.SLOTBLOCKER_DATE_AND_TIME}
    key={APPLICATION_ROUTES.SLOTBLOCKER_DATE_AND_TIME}
    component={screens.SetSlotBlockersDateTimeScreen}
    options={screens.SetSlotBlockersDateTimeScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.EDIT_SLOTBLOCKER}
    key={APPLICATION_ROUTES.EDIT_SLOTBLOCKER}
    component={screens.EditSlotBlocker}
    options={screens.EditSlotBlocker.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.PAYMENTS}
    key={APPLICATION_ROUTES.PAYMENTS}
    component={screens.PaymentsScreen}
    options={screens.PaymentsScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.PACKAGE_PAYMENTS}
    key={APPLICATION_ROUTES.PACKAGE_PAYMENTS}
    component={screens.PackagePaymentsScreen}
    options={screens.PackagePaymentsScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.DISCOUNTS}
    key={APPLICATION_ROUTES.DISCOUNTS}
    component={screens.DiscountScreen}
    options={screens.DiscountScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.EDIT_DISCOUNTS}
    key={APPLICATION_ROUTES.EDIT_DISCOUNTS}
    component={screens.EditDiscount}
    options={screens.EditDiscount.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CREATE_NEW_CLASS}
    key={APPLICATION_ROUTES.CREATE_NEW_CLASS}
    component={screens.CreateClassScreen}
    options={screens.CreateClassScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CLASS_CREATE_SERVICE}
    key={APPLICATION_ROUTES.CLASS_CREATE_SERVICE}
    component={screens.AddClassService}
    options={screens.AddClassService.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CLASS_RECURRENCE_SCREEN}
    key={APPLICATION_ROUTES.CLASS_RECURRENCE_SCREEN}
    component={screens.ClassRecurrenceScreen}
    options={screens.ClassRecurrenceScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.EDIT_CLASS}
    key={APPLICATION_ROUTES.EDIT_CLASS}
    component={screens.EditClassScreen}
    options={screens.EditClassScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CONNECT_WITH_STRIPE}
    key={APPLICATION_ROUTES.CONNECT_WITH_STRIPE}
    component={screens.ConnectWithStripeScreen}
    options={{
      title: APPLICATION_ROUTES.CONNECT_WITH_STRIPE,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.STRIPE_CALLBACK}
    key={APPLICATION_ROUTES.STRIPE_CALLBACK}
    component={screens.StripeCallback}
    options={{
      title: APPLICATION_ROUTES.STRIPE_CALLBACK,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.ACCOUNT_CLIENTS}
    key={APPLICATION_ROUTES.ACCOUNT_CLIENTS}
    component={screens.AccountClientsList}
    options={{
      title: APPLICATION_ROUTES.ACCOUNT_CLIENTS,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CLIENT_BOOKINGS}
    key={APPLICATION_ROUTES.CLIENT_BOOKINGS}
    component={screens.ClientBookings}
    options={{
      title: APPLICATION_ROUTES.CLIENT_BOOKINGS,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CLIENT_SUBSCRIPTIONS}
    key={APPLICATION_ROUTES.CLIENT_SUBSCRIPTIONS}
    component={screens.AccountClientSubscriptions}
    options={screens.AccountClientSubscriptions.navigationOptions}
  />,
  <Stack.Screen
      name={APPLICATION_ROUTES.CLIENT_TRANSACTIONS}
      key={APPLICATION_ROUTES.CLIENT_TRANSACTIONS}
      component={screens.AccountClientsTransactionsAndBalances}
      options={screens.AccountClientsTransactionsAndBalances.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.TRANSACTION_DETAIL}
    key={APPLICATION_ROUTES.TRANSACTION_DETAIL}
    component={screens.TransactionDetailsScreen}
    options={{
      title: APPLICATION_ROUTES.TRANSACTION_DETAIL,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.REFUND_TRANSACTION}
    key={APPLICATION_ROUTES.REFUND_TRANSACTION}
    component={screens.RefundTransactionScreen}
    options={{
      title: APPLICATION_ROUTES.REFUND_TRANSACTION,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.REFUND_DETAILS}
    key={APPLICATION_ROUTES.REFUND_DETAILS}
    component={screens.RefundTransactionDetailsScreen}
    options={{
      title: APPLICATION_ROUTES.REFUND_DETAILS,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CHECKIN_DETAILS}
    key={APPLICATION_ROUTES.CHECKIN_DETAILS}
    component={screens.CheckInDetails}
    options={{
      title: APPLICATION_ROUTES.CHECKIN_DETAILS,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIEW_CLIENT_BOOKING}
    key={APPLICATION_ROUTES.VIEW_CLIENT_BOOKING}
    component={screens.ViewClientBooking}
    options={screens.ViewClientBooking.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIEW_BALANCE_SCREEN}
    key={APPLICATION_ROUTES.VIEW_BALANCE_SCREEN}
    component={screens.ViewBalanceScreen}
    options={screens.ViewBalanceScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIEW_BALANCE_ADJUSTMENT_SCREEN}
    key={APPLICATION_ROUTES.VIEW_BALANCE_ADJUSTMENT_SCREEN}
    component={screens.BalanceAdjustmentScreen}
    options={screens.BalanceAdjustmentScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.TIMEZONE_CURRENCY}
    key={APPLICATION_ROUTES.TIMEZONE_CURRENCY}
    component={screens.TimeZoneCurrencyScreen}
    options={screens.TimeZoneCurrencyScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.CURRENCY_SELECTION}
    key={APPLICATION_ROUTES.CURRENCY_SELECTION}
    component={screens.ListCurrencies}
    options={screens.ListCurrencies.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.INCOME}
    key={APPLICATION_ROUTES.INCOME}
    component={screens.IncomeSalesScreen}
    options={screens.IncomeSalesScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.ADD_CREDIT_CARD}
    key={APPLICATION_ROUTES.ADD_CREDIT_CARD}
    component={screens.PaymentMethodScreen}
    options={screens.PaymentMethodScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.VIDEO_SCREEN}
    component={screens.VideoScreen}
    options={{
      headerShown: false,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_ROUTES.TIMEZONE_SELECTION}
    key={APPLICATION_ROUTES.TIMEZONE_SELECTION}
    component={screens.ListTimeZones}
    options={screens.ListTimeZones.navigationOptions}
  />,
  <Stack.Screen
        name={APPLICATION_ROUTES.SUBSCRIPTION_DETAILS}
        key={APPLICATION_ROUTES.SUBSCRIPTION_DETAILS}
        component={screens.SubscriptionDetails}
        options={{
              title: APPLICATION_ROUTES.SUBSCRIPTION_DETAILS,
            }}
  />,
  <Stack.Screen
          name={APPLICATION_ROUTES.CANCEL_SUBSCRIPTION}
          key={APPLICATION_ROUTES.CANCEL_SUBSCRIPTION}
          component={screens.CancelSubscriptionScreen}
          options={{
                title: APPLICATION_ROUTES.CANCEL_SUBSCRIPTION,
              }}
  />,
];
