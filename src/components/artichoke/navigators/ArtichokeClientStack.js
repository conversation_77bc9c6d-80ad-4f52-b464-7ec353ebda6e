import React from 'react';
import * as screens from '../../../screens/artichoke/client';
import { APPLICATION_CLIENT_ROUTES } from '../../../constants';

export default (Stack) => [
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.CLIENT_DASHBOARD}
    key={APPLICATION_CLIENT_ROUTES.CLIENT_DASHBOARD}
    component={screens.ClientDashboard}
    options={{
      headerShown: false,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.SELECTED_APPOINTMENT}
    key={APPLICATION_CLIENT_ROUTES.SELECTED_APPOINTMENT}
    component={screens.SelectedAppointment}
    options={screens.SelectedAppointment.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.BOOK_SERVICE}
    key={APPLICATION_CLIENT_ROUTES.BOOK_SERVICE}
    component={screens.BookService}
    options={{
      headerShown: false,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.NEW_APPOINTMENT_DATE_AND_TIME}
    key={APPLICATION_CLIENT_ROUTES.NEW_APPOINTMENT_DATE_AND_TIME}
    component={screens.SetBookingDateTimeScreen}
    options={screens.SetBookingDateTimeScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.NEW_APPOINTMENT_LOCATION}
    key={APPLICATION_CLIENT_ROUTES.NEW_APPOINTMENT_LOCATION}
    component={screens.SetBookingLocationScreen}
    options={screens.SetBookingLocationScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.CLASS_VIEW}
    key={APPLICATION_CLIENT_ROUTES.CLASS_VIEW}
    component={screens.ClassView}
    options={{
      headerShown: false,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.CLIENT_PACKAGE_VIEW}
    key={APPLICATION_CLIENT_ROUTES.CLIENT_PACKAGE_VIEW}
    component={screens.ClientViewPackage}
    options={{
      headerShown: false,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.ADD_CREDIT_CARD}
    key={APPLICATION_CLIENT_ROUTES.ADD_CREDIT_CARD}
    component={screens.PaymentMethodScreen}
    options={screens.PaymentMethodScreen.navigationOptions}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.CLIENT_SUBSCRIPTIONS}
    key={APPLICATION_CLIENT_ROUTES.CLIENT_SUBSCRIPTIONS}
    component={screens.AccountClientSubscriptions}
    options={{
      title: APPLICATION_CLIENT_ROUTES.CLIENT_SUBSCRIPTIONS,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.CLIENT_TRANSACTIONS}
    key={APPLICATION_CLIENT_ROUTES.CLIENT_TRANSACTIONS}
    component={screens.TransactionsAndBalances}
    options={{
      title: APPLICATION_CLIENT_ROUTES.CLIENT_TRANSACTIONS,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.CLIENT_VIDEO_SCREEN}
    key={APPLICATION_CLIENT_ROUTES.CLIENT_VIDEO_SCREEN}
    component={screens.VideoScreen}
    options={{
      headerShown: false,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.SUBSCRIPTION_DETAILS}
    key={APPLICATION_CLIENT_ROUTES.SUBSCRIPTION_DETAILS}
    component={screens.ClientSubscriptionDetails}
    options={{
      title: APPLICATION_CLIENT_ROUTES.SUBSCRIPTION_DETAILS,
    }}
  />,
  <Stack.Screen
    name={APPLICATION_CLIENT_ROUTES.CANCEL_CLIENT_SUBSCRIPTION}
    key={APPLICATION_CLIENT_ROUTES.CANCEL_CLIENT_SUBSCRIPTION}
    component={screens.ClientCancelSubscriptionScreen}
    options={{
      title: APPLICATION_CLIENT_ROUTES.CANCEL_CLIENT_SUBSCRIPTION,
    }}
  />,
];
