import React, { useState } from 'react';
import { Formik } from 'formik';
import { View, StyleSheet, ScrollView } from 'react-native';
import FormTextInputUnderline from '../../common/FormTextInputUnderline';
import FormTextInput from '../../common/FormTextInput';
import FormGroup from '../../common/FormGroup';
import FormSubmitButton from '../../common/FormSubmitButton';
import { colors } from '../../../../styles';
import FormDiscountType from '../../common/FormDiscountType';
import { isNumber } from '../../../../util/validate';
import { track } from '../../../../util/Analytics';

const EditDiscountForm = (props) => {
  const disableCreateButton = (initialValues) => {
    let disable = true;
    if (
      initialValues.name
      && initialValues.type
      && initialValues.amount
      && isNumber(initialValues.amount)
    ) {
      disable = false;
    }
    return disable;
  };
  const buttonStyle = disableCreateButton(props.initialValues)
    ? { backgroundColor: colors.lightgrey, borderColor: colors.lightgrey }
    : { backgroundColor: colors.duskBlue, borderColor: colors.duskBlue };
  const buttonLabelStyle = disableCreateButton(props.initialValues)
    ? { color: colors.subGrey }
    : { color: colors.white };
  return (
    <ScrollView
      contentContainerStyle={{
        flexGrow: 1,
        flexDirection: 'column',
        justifyContent: 'space-between',
      }}
      style={styles.container}
    >
      <Formik
        initialValues={props.initialValues}
        onSubmit={(values) => props.onSubmitForm(values)}
        enableReinitialize
      >
        {({
          handleBlur, handleChange, setFieldValue, values, errors,
        }) => (
          <View style={styles.form}>
            <FormGroup>
              <FormTextInputUnderline
                onChangeText={(value) => {
                  props.setSelectedDiscountForEditValuesAction({
                    key: 'name',
                    value,
                  });
                  setFieldValue('name', value);
                }}
                onBlur={handleBlur('name')}
                value={values.name}
                label="Discount Name"
                placeholder="Discount Name"
                name="name"
              />
            </FormGroup>
            <FormGroup>
              <FormDiscountType
                onPressFunc={() => props.onOpenDiscountType()}
                value={values.type}
                label="Type"
              />
            </FormGroup>
            {values.type && values.type !== '' && (
            <FormGroup>
              <FormTextInput
                onChangeText={(value) => {
                  props.setSelectedDiscountForEditValuesAction({
                    key: 'amount',
                    value,
                  });
                  setFieldValue('amount', value);
                }}
                onBlur={handleBlur('amount')}
                label={
                      values.type === 'PERCENTAGE'
                        ? 'Discount %'
                        : `${props.symbol}Discount`
                    }
                value={values.amount && `${values.amount}`}
                placeholder={
                      values.type === 'PERCENTAGE'
                        ? 'Discount %'
                        : `${props.symbol}Discount `
                    }
                name="amount"
                maxLength={values.type === 'PERCENTAGE' ? 4 : 6}
                textAfterInput={values.type === 'PERCENTAGE' ? '%' : ''}
                textBeforeInput={
                      values.type === 'AMOUNT' ? props.symbol : ''
                    }
                keyboardType="numeric"
                txtWidth={
                      values.amount?.toString().length > 0
                        ? values.amount.toString().length * 10
                        : 76
                    }
              />
            </FormGroup>
            )}
          </View>
        )}
      </Formik>
      <FormGroup formStyle={styles.bottomSubmitButton}>
        {props.isInEdit ? (
          <FormSubmitButton
            title="Delete Discount"
            onPressButton={() => {
              props.onDelete();
              track('discount_deleted');
            }}
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.lightgrey,
              borderWidth: 2,
            }}
            buttonLabelStyle={{ color: colors.subGrey }}
          />
        ) : (
          <FormSubmitButton
            title="Create Discount"
            disabled={disableCreateButton(props.initialValues)}
            onPressButton={() => {
              props.onSubmitForm();
              track('discount_created');
            }}
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.lightgrey,
              borderWidth: 2,
            }}
            buttonLabelStyle={{ color: colors.subGrey }}
          />
        )}
      </FormGroup>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
  bottomSubmitButton: {
    display: 'flex',
    marginTop: 50,
    borderRadius: 30,
    marginBottom: 20,
  },
});
export default EditDiscountForm;
