/* eslint-disable array-callback-return */
import React from 'react';
import moment from 'moment';
import {
  View, StyleSheet, Text, FlatList,
} from 'react-native';
import FormGroup from '../../common/FormGroup';
import SelectForm from '../../common/SelectForm';
import ClientLocationDayItem from './ClientLocationDayItem';
import { DAYS, DAYS_SORTER, START_TIMES_OFFSETS } from '../../../../constants';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import AddButton from '../../common/AddButton';

const ClientLocationForm = (props) => {
  const workdays = Object.entries(
    JSON.parse(props.selfBookings.workHours).offsite,
  ).map(([key, value]) => ({
    [key]: typeof value === 'string' ? value.split(', ') : value,
  }));

  const arrStartTimes = props.selfBookings.offsiteStartTimes
    ? props.selfBookings.offsiteStartTimes.split(',')
    : [];

  const renderStartTimes = () => START_TIMES_OFFSETS.map((time) => {
    const title = time === '00' ? 'On the hour :00' : `:${time}`;
    return (
      <SelectForm
        buttonTitle={title}
        width="50%"
        selected={arrStartTimes.includes(time)}
        onChangeValue={() => onStartTimeChange(time)}
      />
    );
  });

  const onStartTimeChange = (value) => {
    // call to change start time;

    const checkIfExist = arrStartTimes.filter(
      (item) => item.toString() === value.toString(),
    );
    let finalArray = arrStartTimes;
    if (!checkIfExist.length) {
      finalArray.push(value);
    } else {
      finalArray = arrStartTimes.filter(
        (item) => item.toString() !== value.toString(),
      );
    }
    props.onChangeClientLocationsProps({
      key: 'offsiteStartTimes',
      value: finalArray.toString(),
    });
  };

  const onChangeStart = (day, newStart, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    const objDay = objWorkHours.offsite[`${day}`] || [];

    objDay[index].from = newStart;
    const workHours = {
      ...objWorkHours,
    };

    props.onChangeClientLocationsProps({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onChangeEnd = (day, newEnd, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    const objDay = objWorkHours.offsite[`${day}`] || [];

    objDay[index].to = newEnd;
    const workHours = {
      ...objWorkHours,
    };

    props.onChangeClientLocationsProps({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onChangeDay = (newDay, oldDay) => {
    if (oldDay !== newDay) {
      const objWorkHours = JSON.parse(props.selfBookings.workHours);
      const workHours = {
        ...objWorkHours,
      };
      const { offsite } = workHours;
      delete Object.assign(offsite, { [newDay]: offsite[oldDay] })[oldDay];
      const sortedWorkHours = sortDays(offsite);
      workHours.offsite = Object.fromEntries(sortedWorkHours);
      props.onChangeClientLocationsProps({
        key: 'workHours',
        value: JSON.stringify(workHours),
      });
    }
  };

  const onRemoveFromDay = (day, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);

    const workHours = {
      ...objWorkHours,
    };
    workHours.offsite[`${day}`].splice(index, 1);

    if (workHours.offsite[`${day}`].length === 0) {
      delete workHours.offsite[`${day}`];
    }
    props.onChangeClientLocationsProps({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onAddNewRow = () => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    const workHours = {
      ...objWorkHours,
    };
    for (let i = 0; i < DAYS.length; i += 1) {
      const day = workdays.find(
        (data) => Object.keys(data).toString() === DAYS[i].key,
      );
      if (!day) {
        const nextDay = DAYS[i].key;
        if (!workHours.offsite) {
          workHours.offsite = {};
          workHours.offsite[nextDay] = [{ from: '9:00am', to: '5:00pm' }];
        }
        if (!workHours.offsite[nextDay]) {
          workHours.offsite[nextDay] = [{ from: '9:00am', to: '5:00pm' }];
        }
        break;
      }
    }
    const sortedWorkHours = sortDays(workHours.offsite);
    workHours.offsite = Object.fromEntries(sortedWorkHours);
    props.onChangeClientLocationsProps({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  // function to calculate total work hours
  const totalWorkHours = (workHours) => {
    let totalMinutes = 0;
    const extractedWorkHours = [];

    workHours.map((day, index) => {
      const key = Object.keys(day).toString();
      extractedWorkHours.push(...workHours[index][key]);
    });

    extractedWorkHours.map((hourInterval) => {
      if (hourInterval.from !== '' && hourInterval.to !== '') {
        const startTime = moment(hourInterval.from, 'HH:mm a');
        const endTime = moment(hourInterval.to, 'HH:mm a');
        const dif = moment(endTime).diff(moment(startTime), 'minutes');
        totalMinutes += dif;
      }
    });

    return (totalMinutes / 60).toFixed(1);
  };

  const sortDays = (offsite) => {
    const daysArray = Object.entries(offsite);
    return daysArray.sort(
      (day1, day2) => DAYS_SORTER[day1[0]] - DAYS_SORTER[day2[0]],
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        keyExtractor={(item) => Object.keys(item)[0]}
        contentContainerStyle={{ flexGrow: 1 }}
        style={styles.hoursContainer}
        removeClippedSubviews
        data={workdays}
        renderItem={({ item, index }) => (
          <ClientLocationDayItem
            item={item}
            index={index * 1000}
            onChangeDay={onChangeDay}
            onChangeStart={onChangeStart}
            onChangeEnd={onChangeEnd}
            onRemoveFromDay={onRemoveFromDay}
            currentDays={workdays.map((workday) => Object.keys(workday)[0])}
          />
        )}
        ListHeaderComponent={(
          <FormGroup formStyle={{ paddingTop: 20 }}>
            <Text style={{ ...generalStyles.titleSmall, color: colors.black }}>
              Work Hours
            </Text>
          </FormGroup>
        )}
        ListFooterComponent={(
          <View>
            <FormGroup
              formStyle={{
                flexDirection: 'row',
                flex: 1,
                marginBottom: 0,
                marginTop: 0,
              }}
            >
              <Text style={styles.addworkHoursTitle}>Add Work Hours</Text>
              <AddButton
                disabled={workdays.length >= 7}
                onPressAction={onAddNewRow}
                small
              />
              <Text style={styles.labeltotal}>
                Total:
                {' '}
                {totalWorkHours(workdays)}
                h
              </Text>
            </FormGroup>
            <FormGroup>
              <Text
                style={{ ...generalStyles.titleSmall, color: colors.black }}
              >
                Appointments Start
              </Text>
              <View style={styles.selectcontainer}>{renderStartTimes()}</View>
            </FormGroup>
          </View>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
    marginBottom: 20,
  },
  hoursContainer: {
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  selectcontainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    flex: 1,
    marginTop: 10,
  },
  addworkHoursTitle: {
    ...generalStyles.titleSmall,
    color: colors.black,
    marginTop: 22,
  },
  labeltotal: {
    ...generalStyles.titleSmall,
    marginTop: 22,
    textAlign: 'right',
    marginLeft: 'auto',
    color: colors.black,
  },
});
export default ClientLocationForm;
