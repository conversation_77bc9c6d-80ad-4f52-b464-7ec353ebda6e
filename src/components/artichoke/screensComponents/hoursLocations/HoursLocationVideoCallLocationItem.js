import React, {useState} from 'react';
import {View, StyleSheet, Text, Switch, TouchableOpacity, Image, Platform} from 'react-native';
import {colors} from '../../../../styles';
import {APPLICATION_ROUTES} from "../../../../constants";
import {generalStyles, locationStyles} from "../../../../styles/generalStyle";
const videoLocationIcon = require('../../../../assets/ic_video_call.png');

const HoursLocationVideoCallLocationItem = (props) => {
  const initialState = props.selfBookings && props.selfBookings.inapp_services_available && props.selfBookings.inapp_services_available === 'true' ? true : false;
  const [isToggled, setToggled] = useState(initialState);
  const toggleTrueFalse = () => props.onChangeVideoCallLocationsSwitch(!isToggled);
  return (

   <TouchableOpacity
      onPress={() =>
          props.navigation.navigate({name: APPLICATION_ROUTES.VIDEO_CALL_LOCATION})
       }
   >
   <View style={{...locationStyles.locationItem, borderRadius: 0}}>
      <View style={locationStyles.leftBox}>
        <Image source={videoLocationIcon} style={locationStyles.videoCallLocationIcon}/>
      </View>
      <View style={locationStyles.centerBox}>
        <Text style={locationStyles.addressName}>Video Call</Text>
        <Text style={locationStyles.addressLocation}>
            Enable this type if you are willing to provide service via video call
        </Text>
        <Text style={locationStyles.numberOfServices}>{props.videoCallOfferedServices} Services</Text>
      </View>
      <View style={locationStyles.rightBox}>
        <Switch
          trackColor={{ true: colors.medYellow }}
          thumbColor={Platform.OS === 'ios' ? null : colors.white}
          style={styles.switch}
          onValueChange={() => toggleTrueFalse()}
          value={props.showDisableAlert ? false : isToggled}
        />
      </View>
   </View>
   </TouchableOpacity>

  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
export default HoursLocationVideoCallLocationItem;
