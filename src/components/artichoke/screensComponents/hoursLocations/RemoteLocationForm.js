/* eslint-disable array-callback-return */
import React from 'react';
import { Formik } from 'formik';
import moment from 'moment';
import {
  View, StyleSheet, Text, ScrollView,
} from 'react-native';
import FormGroup from '../../common/FormGroup';
import SelectForm from '../../common/SelectForm';
import ClientLocationDayItem from './ClientLocationDayItem';
import { DAYS, DAYS_SORTER, START_TIMES_OFFSETS } from '../../../../constants';
import { colors } from '../../../../styles';
import FormMultilineTextInput from '../../common/FormMultilineTextInput';
import AddButton from '../../common/AddButton';
import { generalStyles } from '../../../../styles/generalStyle';

const RemoteLocationForm = (props) => {
  const workdays = Object.entries(
    JSON.parse(props.selfBookings.workHours).remote,
  ).map(([key, value]) => ({
    [key]: typeof value === 'string' ? value.split(', ') : value,
  }));

  const arrStartTimes = props.selfBookings.remoteStartTimes
    ? props.selfBookings.remoteStartTimes.split(',')
    : [];

  const renderStartTimes = () => START_TIMES_OFFSETS.map((time) => {
    const title = time === '00' ? 'On the hour :00' : `:${time}`;
    const valueCheck = time === '00' ? '0' : time;
    return (
      <SelectForm
        buttonTitle={title}
        width="50%"
        selected={arrStartTimes.includes(valueCheck)}
        onChangeValue={() => onStartTimeChange(valueCheck)}
      />
    );
  });

  const onStartTimeChange = (value) => {
    // call to change start time;

    const checkIfExist = arrStartTimes.filter(
      (item) => item.toString() === value.toString(),
    );
    let finalArray = arrStartTimes;
    if (!checkIfExist.length) {
      finalArray.push(value);
    } else {
      finalArray = arrStartTimes.filter(
        (item) => item.toString() !== value.toString(),
      );
    }
    props.onChangeRemoteLocationsProps({
      key: 'remoteStartTimes',
      value: finalArray.toString(),
    });
  };

  const onChangeStart = (day, newStart, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    const objDay = objWorkHours.remote[`${day}`] || [];

    objDay[index].from = newStart;
    const workHours = {
      ...objWorkHours,
    };

    props.onChangeRemoteLocationsProps({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onChangeEnd = (day, newEnd, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    const objDay = objWorkHours.remote[`${day}`] || [];

    objDay[index].to = newEnd;
    const workHours = {
      ...objWorkHours,
    };

    props.onChangeRemoteLocationsProps({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onChangeDay = (newDay, oldDay) => {
    if (oldDay !== newDay) {
      const objWorkHours = JSON.parse(props.selfBookings.workHours);
      const workHours = {
        ...objWorkHours,
      };
      const { remote } = workHours;
      delete Object.assign(remote, { [newDay]: remote[oldDay] })[oldDay];
      const sortedWorkHours = sortDays(remote);
      workHours.remote = Object.fromEntries(sortedWorkHours);
      props.onChangeRemoteLocationsProps({
        key: 'workHours',
        value: JSON.stringify(workHours),
      });
    }
  };

  const onRemoveFromDay = (day, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);

    const workHours = {
      ...objWorkHours,
    };
    workHours.remote[`${day}`].splice(index, 1);

    if (workHours.remote[`${day}`].length === 0) {
      delete workHours.remote[`${day}`];
    }
    props.onChangeRemoteLocationsProps({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onAddNewRow = () => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    const workHours = {
      ...objWorkHours,
    };
    for (let i = 0; i < DAYS.length; i += 1) {
      const day = workdays.find(
        (data) => Object.keys(data).toString() === DAYS[i].key,
      );
      if (!day) {
        const nextDay = DAYS[i].key;
        if (!workHours.remote) {
          workHours.remote = {};
          workHours.remote[nextDay] = [{ from: '9:00am', to: '5:00pm' }];
        }
        if (!workHours.remote[nextDay]) {
          workHours.remote[nextDay] = [{ from: '9:00am', to: '5:00pm' }];
        }
        break;
      }
    }
    const sortedWorkHours = sortDays(workHours.remote);
    workHours.remote = Object.fromEntries(sortedWorkHours);
    props.onChangeRemoteLocationsProps({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  // function to calculate total work hours
  const totalWorkHours = (workHours) => {
    let totalMinutes = 0;
    const extractedWorkHours = [];

    workHours.map((day, index) => {
      const key = Object.keys(day).toString();
      extractedWorkHours.push(...workHours[index][key]);
    });

    extractedWorkHours.map((hourInterval) => {
      if (hourInterval.from !== '' && hourInterval.to !== '') {
        const startTime = moment(hourInterval.from, 'HH:mm a');
        const endTime = moment(hourInterval.to, 'HH:mm a');
        const dif = moment(endTime).diff(moment(startTime), 'minutes');
        totalMinutes += dif;
      }
    });

    return (totalMinutes / 60).toFixed(1);
  };

  const sortDays = (remote) => {
    const daysArray = Object.entries(remote);
    return daysArray.sort(
      (day1, day2) => DAYS_SORTER[day1[0]] - DAYS_SORTER[day2[0]],
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Formik initialValues={props.initialValues}>
        {({ handleBlur, setFieldValue, values }) => (
          <View>
            <FormGroup>
              <FormMultilineTextInput
                style={{ color: colors.black }}
                onChangeText={(value) => {
                  props.onChangeRemoteLocationsProps({
                    key: 'remoteServicesDescription',
                    value,
                  });

                  setFieldValue('remoteServicesDescription', value);
                }}
                onBlur={handleBlur('remoteServicesDescription')}
                placeholder="Provide service via Zoom, Skype, Google, etc. This is normally a URL for others to access."
                name="remoteServicesDescription"
                value={values.remoteServicesDescription}
                maxLength={360}
              />
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <Text
                style={{ ...generalStyles.titleSmall, color: colors.black }}
              >
                Work Hours
              </Text>
            </FormGroup>
            <View style={styles.hoursContainer}>
              {workdays
                && workdays.map((item, index) => (
                  <ClientLocationDayItem
                    item={item}
                    index={index * 1000}
                    onChangeDay={onChangeDay}
                    onChangeStart={onChangeStart}
                    onChangeEnd={onChangeEnd}
                    onRemoveFromDay={onRemoveFromDay}
                    currentDays={workdays.map(
                      (workday) => Object.keys(workday)[0],
                    )}
                  />
                ))}
            </View>
            <FormGroup
              formStyle={{
                flexDirection: 'row',
                flex: 1,
                marginBottom: 0,
                marginTop: 0,
              }}
            >
              <Text style={styles.addworkHoursTitle}>Add Work Hours</Text>
              <AddButton
                disabled={workdays.length >= 7}
                onPressAction={onAddNewRow}
                small
              />
              <Text style={styles.labeltotal}>
                Total:
                {' '}
                {totalWorkHours(workdays)}
                h
              </Text>
            </FormGroup>
            <FormGroup>
              <Text
                style={{ ...generalStyles.titleSmall, color: colors.black }}
              >
                Appointments Start
              </Text>
              <View style={styles.selectcontainer}>{renderStartTimes()}</View>
            </FormGroup>
          </View>
        )}
      </Formik>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
    marginBottom: 20,
  },
  hoursContainer: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  selectcontainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    flex: 1,
    marginTop: 10,
  },
  addworkHoursTitle: {
    ...generalStyles.titleSmall,
    marginTop: 22,
    color: colors.black,
  },
  labeltotal: {
    ...generalStyles.titleSmall,
    marginTop: 22,
    textAlign: 'right',
    marginLeft: 'auto',
    color: colors.black,
  },
});
export default RemoteLocationForm;
