import React, { useState } from 'react';
import {
  View,
  Text,
  Switch,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import { colors } from '../../../../styles';
import { locationStyles } from '../../../../styles/generalStyle';

const locationIcon = require('../../../../assets/locationIcon.png');

const HoursLocationListItem = (props) => {
  const initialState = props.item.checked;
  const [isToggled] = useState(initialState);
  const toggleTrueFalse = () => props.onChangeLocationsSwitch(!isToggled, props.item);

  let addressLine = '';
  if (props.item.address2) {
    addressLine = `${props.item.address2} ${props.item.address1}, ${props.item.city}, ${props.item.state}`;
  } else {
    addressLine = `${props.item.address1}, ${props.item.city}, ${props.item.state}`;
  }

  return (
    <TouchableOpacity
      key={props.item.id}
      onPress={() => props.onGoToEditScreen(props.item)}
    >
      <View style={{ ...locationStyles.locationItem, borderRadius: 0 }}>
        <View style={locationStyles.leftBox}>
          <Image source={locationIcon} style={locationStyles.locationIcon} />
        </View>
        <View style={locationStyles.centerBox}>
          <Text style={locationStyles.addressName}>
            {props.item.addressName}
          </Text>
          <Text style={locationStyles.addressLocation}>
            {!props.item.city && !props.item.state ? '' : addressLine}
          </Text>
          <Text style={locationStyles.numberOfServices}>
            {props.item.offeredServices}
            {' '}
            Services
          </Text>
        </View>
        <View style={locationStyles.rightBox}>
          <Switch
            trackColor={{ true: colors.medYellow }}
            thumbColor={Platform.OS === 'ios' ? null : colors.white}
            onValueChange={toggleTrueFalse}
            value={props.showDisableAlert ? false : isToggled}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default HoursLocationListItem;
