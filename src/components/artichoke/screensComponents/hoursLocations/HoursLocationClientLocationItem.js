import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Text,
  Switch,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';
import { generalStyles, locationStyles } from '../../../../styles/generalStyle';
import { track } from '../../../../util/Analytics';

const locationIcon = require('../../../../assets/locationIcon.png');

const HoursLocationClientLocationItem = (props) => {
  const initialState = !!(props.selfBookings
    && props.selfBookings.offsite_services_available
    && props.selfBookings.offsite_services_available === 'true');
  const [isToggled, setToggled] = useState(initialState);
  const toggleTrueFalse = () => props.onChangeClientLocationsSwitch(!isToggled);
  return (
    <TouchableOpacity
      onPress={() => props.navigation.navigate({ name: APPLICATION_ROUTES.CLIENTS_LOCATION })}
    >
      <View
        style={{
          ...locationStyles.locationItem,
          borderRadius: 0,
          borderTopWidth: 1,
          borderTopColor: colors.lightgrey,
        }}
      >
        <View style={locationStyles.leftBox}>
          <Image source={locationIcon} style={locationStyles.locationIcon} />
        </View>
        <View style={locationStyles.centerBox}>
          <Text style={locationStyles.addressName}>Client's Location</Text>
          <Text style={locationStyles.addressLocation}>
            Enable this type if you are willing to go to your Client’s location
          </Text>
          <Text style={locationStyles.numberOfServices}>
            {props.clientLocationOfferedServices}
            {' '}
            Services
          </Text>
        </View>
        <View style={locationStyles.rightBox}>
          <Switch
            trackColor={{ true: colors.medYellow }}
            thumbColor={Platform.OS === 'ios' ? null : colors.white}
            style={styles.switch}
            onValueChange={() => {
              toggleTrueFalse();
              if (isToggled) track('client_location_disabled');
            }}
            value={props.showDisableAlert ? false : isToggled}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
export default HoursLocationClientLocationItem;
