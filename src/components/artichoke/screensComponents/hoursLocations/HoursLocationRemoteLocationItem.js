import React, {useState} from 'react';
import {View, StyleSheet, Text, Switch, TouchableOpacity, Image, Platform} from 'react-native';
import {colors} from '../../../../styles';
import {APPLICATION_ROUTES} from "../../../../constants";
import {generalStyles, locationStyles} from "../../../../styles/generalStyle";
const remoteIcon = require('../../../../assets/ic_remote.png');
import { track } from '../../../../util/Analytics'

const HoursLocationRemoteLocationItem = (props) => {
  const initialState = props.selfBookings && props.selfBookings.remote_services_available && props.selfBookings.remote_services_available === 'true' ? true : false;
  const [isToggled, setToggled] = useState(initialState);
  const toggleTrueFalse = () => props.onChangeClientLocationsSwitch(!isToggled);
  return (

   <TouchableOpacity
      onPress={() =>
          props.navigation.navigate({name: APPLICATION_ROUTES.REMOTE_LOCATION})
       }
   >
   <View style={{...locationStyles.locationItem, borderRadius: 0}}>
      <View style={locationStyles.leftBox}>
        <Image source={remoteIcon} style={locationStyles.remoteLocationIcon}/>
      </View>
      <View style={locationStyles.centerBox}>
        <Text style={locationStyles.addressName}>Remote Session Link</Text>
        <Text style={locationStyles.addressLocation}>
        Provide service via Zoom, Skype, Google, etc.
        </Text>
        <Text style={locationStyles.numberOfServices}>{props.remoteOfferedServices} Services</Text>
      </View>
      <View style={locationStyles.rightBox}>
        <Switch
          trackColor={{ true: colors.medYellow }}
          thumbColor={Platform.OS === 'ios' ? null : colors.white}
          style={styles.switch}
          onValueChange={() =>{
               toggleTrueFalse()
               if(isToggled)
                   track('remote_location_disabled')
               }}
          value={props.showDisableAlert ? false : isToggled}
        />
      </View>
   </View>
   </TouchableOpacity>

  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
export default HoursLocationRemoteLocationItem;
