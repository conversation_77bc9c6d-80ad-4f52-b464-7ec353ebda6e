import React from 'react';
import {
  View, StyleSheet, Image, TouchableOpacity,
} from 'react-native';
import FormDay from '../../common/FormDay';
import FormTimeInput from '../../common/FormTimeInput';
import { DAYS, HOURS } from '../../../../constants';
import { curvedScale } from '../../../../util/responsive';

const deleteIcon = require('../../../../assets/btnMinus.png');

const ClientLocationDayItem = (props) => {
  const key = Object.keys(props.item)[0];
  return (
    <View key={props.index} style={styles.container}>
      <View style={styles.day}>
        <FormDay
          value={key}
          items={DAYS.filter(
            (day) => day.key === key
              || props.currentDays.findIndex((item) => item === day.key) === -1,
          )}
          onChange={props.onChangeDay}
        />
      </View>
      {props.item[key].map((time, index) => (
        <>
          <View style={styles.timeday}>
            <FormTimeInput
              day={key}
              hourIndex={index}
              value={time.from}
              label="Start"
              items={HOURS}
              onChange={props.onChangeStart}
            />
          </View>
          <View style={styles.timeday}>
            <FormTimeInput
              day={key}
              hourIndex={index}
              value={time.to}
              label="End"
              items={HOURS}
              onChange={props.onChangeEnd}
            />
          </View>
          <TouchableOpacity
            onPress={() => {
              props.onRemoveFromDay(key, index);
            }}
            style={styles.removeButton}
            activeOpacity={0.2}
          >
            <Image
              source={deleteIcon}
              style={{ width: curvedScale(20), resizeMode: 'contain' }}
            />
          </TouchableOpacity>
        </>
      ))}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 13,
    marginBottom: 10,
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  timeday: {
    width: '33%',
  },
  day: {
    width: '20%',
  },
  removeButton: {
    width: '10%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default ClientLocationDayItem;
