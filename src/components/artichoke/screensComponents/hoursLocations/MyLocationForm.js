/* eslint-disable array-callback-return */
import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import moment from 'moment';
import FormGroup from '../../common/FormGroup';
import SelectForm from '../../common/SelectForm';
import ClientLocationDayItem from './ClientLocationDayItem';
import { DAYS, DAYS_SORTER, START_TIMES_OFFSETS } from '../../../../constants';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import AddButton from '../../common/AddButton';

const MyLocationForm = (props) => {
  const workdays = Object.entries(JSON.parse(props.selfBookings.workHours)).map(
    ([key, value]) => ({
      [key]: typeof value === 'string' ? value.split(', ') : value,
    }),
  );

  const arrStartTimes = props.selfBookings.startTimes.split(',');
  const renderStartTimes = () => START_TIMES_OFFSETS.map((time, index) => {
    const title = time === '00' ? 'On the hour :00' : `:${time}`;
    return (
      <SelectForm
        key={`select-start-times-key-${index}`}
        buttonTitle={title}
        width="50%"
        selected={arrStartTimes.includes(time)}
        onChangeValue={() => onStartTimeChange(time)}
      />
    );
  });
  const onStartTimeChange = (value) => {
    const checkIfExist = arrStartTimes.filter(
      (item) => item.toString() === value.toString(),
    );
    let finalArray = arrStartTimes;
    if (!checkIfExist.length) {
      finalArray.push(value);
    } else {
      finalArray = arrStartTimes.filter(
        (item) => item.toString() !== value.toString(),
      );
    }
    props.setSelectedLocationForEditValuesAction({
      key: 'startTimes',
      value: finalArray.toString(),
    });
  };

  const onChangeStart = (day, newStart, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    const objDay = objWorkHours[`${day}`] || [];
    objDay[index].from = newStart;
    const workHours = {
      ...objWorkHours,
    };
    props.setSelectedLocationForEditValuesAction({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onChangeEnd = (day, newEnd, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    const objDay = objWorkHours[`${day}`] || [];
    objDay[index].to = newEnd;
    const workHours = {
      ...objWorkHours,
    };
    props.setSelectedLocationForEditValuesAction({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onChangeDay = (newDay, oldDay) => {
    if (oldDay !== newDay) {
      const objWorkHours = JSON.parse(props.selfBookings.workHours);
      let workHours = {
        ...objWorkHours,
      };
      delete Object.assign(workHours, { [newDay]: workHours[oldDay] })[oldDay];
      const sortedWorkHours = sortDays(workHours);
      workHours = Object.fromEntries(sortedWorkHours);
      props.setSelectedLocationForEditValuesAction({
        key: 'workHours',
        value: JSON.stringify(workHours),
      });
    }
  };

  const onRemoveFromDay = (day, index) => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);

    const workHours = {
      ...objWorkHours,
    };
    workHours[`${day}`].splice(index, 1);

    if (workHours[`${day}`].length === 0) {
      delete workHours[`${day}`];
    }
    props.setSelectedLocationForEditValuesAction({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  const onAddNewRow = () => {
    const objWorkHours = JSON.parse(props.selfBookings.workHours);
    let workHours = {
      ...objWorkHours,
    };
    for (let i = 0; i < DAYS.length; i += 1) {
      const day = workdays.find(
        (data) => Object.keys(data).toString() === DAYS[i].key,
      );
      if (!day) {
        const nextDay = DAYS[i].key;
        if (!workHours) {
          workHours = {};
          workHours[nextDay] = [{ from: '9:00am', to: '5:00pm' }];
        }
        if (!workHours[nextDay]) {
          workHours[nextDay] = [{ from: '9:00am', to: '5:00pm' }];
        }
        break;
      }
    }
    const sortedWorkHours = sortDays(workHours);
    workHours = Object.fromEntries(sortedWorkHours);
    props.setSelectedLocationForEditValuesAction({
      key: 'workHours',
      value: JSON.stringify(workHours),
    });
  };

  // function to calculate total work hours
  const totalWorkHours = (workHours) => {
    let totalMinutes = 0;
    const extractedWorkHours = [];

    workHours.map((day, index) => {
      const key = Object.keys(day).toString();
      extractedWorkHours.push(...workHours[index][key]);
    });

    extractedWorkHours.map((hourInterval) => {
      if (hourInterval.from !== '' && hourInterval.to !== '') {
        const startTime = moment(hourInterval.from, 'HH:mm a');
        const endTime = moment(hourInterval.to, 'HH:mm a');
        const dif = moment(endTime).diff(moment(startTime), 'minutes');
        totalMinutes += dif;
      }
    });

    return (totalMinutes / 60).toFixed(1);
  };

  const sortDays = (workHours) => {
    const daysArray = Object.entries(workHours);
    return daysArray.sort(
      (day1, day2) => DAYS_SORTER[day1[0]] - DAYS_SORTER[day2[0]],
    );
  };

  return (
    <View>
      <FormGroup>
        <Text style={{ ...generalStyles.titleSmall, color: colors.black }}>
          Work Hours
        </Text>
      </FormGroup>
      <View style={styles.container}>
        {workdays
          && workdays.map((item, index) => (
            <ClientLocationDayItem
              key={index + 1}
              item={item}
              index={index * 1000}
              onChangeDay={onChangeDay}
              onChangeStart={onChangeStart}
              onChangeEnd={onChangeEnd}
              onRemoveFromDay={onRemoveFromDay}
              currentDays={workdays.map((workday) => Object.keys(workday)[0])}
            />
          ))}
      </View>
      <FormGroup formStyle={styles.hoursContainer}>
        <View style={styles.workHoursView}>
          <Text style={styles.addworkHoursTitle}>Add Work Hours</Text>
          <AddButton
            disabled={workdays.length >= 7}
            onPressAction={onAddNewRow}
            small
          />
        </View>
        <View style={styles.totalView}>
          <Text style={styles.labeltotal}>
            Total:
            {' '}
            {totalWorkHours(workdays)}
            h
          </Text>
        </View>
      </FormGroup>
      <FormGroup>
        <Text style={{ ...generalStyles.titleSmall, color: colors.black }}>
          Appointments Start
        </Text>
        <View style={styles.selectcontainer}>{renderStartTimes()}</View>
      </FormGroup>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  selectcontainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    flex: 1,
    marginTop: 10,
  },
  addworkHoursTitle: {
    ...generalStyles.titleSmall,
    color: colors.black,
  },
  labeltotal: {
    ...generalStyles.titleSmall,
    color: colors.black,
  },
  hoursContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 0,
    paddingHorizontal: 13,
    marginTop: 0,
  },
  workHoursView: {
    width: '55%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 22,
    paddingVertical: 5,
  },
  totalView: {
    width: '44%',
    paddingVertical: 5,
    justifyContent: 'center',
    alignItems: 'flex-start',
    marginTop: 22,
  },
});
export default MyLocationForm;
