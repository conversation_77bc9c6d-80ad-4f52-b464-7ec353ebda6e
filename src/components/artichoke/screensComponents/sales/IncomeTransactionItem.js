import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const IncomeTransactionItem = (props) => {
  const clientName = props.item.purchase
    ? `${props.item.purchase.clientFirstName} ${props.item.purchase.clientLastName}`
    : '';
  let serviceName = '';
  if (props.item.purchase) {
    if (
      props.item.purchase
      && props.item.purchase.clientPurchaseProductDurations
      && props.item.purchase.clientPurchaseProductDurations.length
      && props.item.purchase.clientPurchaseProductDurations[0].productName
    ) {
      serviceName = props.item.purchase.clientPurchaseProductDurations[0].productName;
    } else if (
      props.item.purchase
      && props.item.purchase.pack
      && props.item.purchase.pack.name
    ) {
      serviceName = props.item.purchase.pack.name;
    }
  } else {
    serviceName = props.item.checkIn.appointment.ProductDuration.name;
  }

  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <View style={styles.leftBox}>
        <Text style={styles.addressName}>{clientName}</Text>
        <Text style={styles.addressLocation}>{serviceName}</Text>
      </View>
      <View style={styles.rightBox}>
        <View style={styles.rightText}>
          <Text
            style={
              props.item.purchase.refunded
                ? styles.priceBlack
                : styles.priceGreen
            }
          >
            {props.item.purchase.refunded
              ? `Refund ${props.symbol}${parseFloat(
                props.item.purchase.amountPaid,
              ).toFixed(2)}`
              : `${props.symbol}${parseFloat(
                props.item.purchase.amountPaid,
              ).toFixed(2)}`}
          </Text>
          <IconFeader name="chevron-right" size={30} color={colors.subGrey} />
        </View>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  item: {
    display: 'flex',
    flex: 1,
    height: 80,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    paddingHorizontal: 20,
  },
  leftBox: {
    display: 'flex',
    flex: 1,
  },
  rightBox: {
    alignSelf: 'center',
    display: 'flex',
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirHeavy17,
    color: colors.black,
  },
  addressLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },

  rightText: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  priceBlack: {
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    paddingRight: 15,
  },
  priceGreen: {
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    color: colors.goodGreen,
    paddingRight: 15,
  },
});
export default IncomeTransactionItem;
