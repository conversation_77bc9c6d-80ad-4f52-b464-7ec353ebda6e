import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  StyleSheet, View, Text, TouchableOpacity, Image,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import moment from 'moment';
import AwesomeAlert from 'react-native-awesome-alerts';
import { colors } from '../../../../styles';
import {
  setFilteredClientTransactions,
  setSelectedClientTransactionAction,
} from '../../../../actions/artichoke/Clients.actions';
import { SALES_PERIODS } from '../../../../constants';
import { generalStyles } from '../../../../styles/generalStyle';
import {
  getSalesTaxAction,
  setPeriodHistoryAction,
} from '../../../../actions/artichoke/Sales.actions';
import { deleteAlertStyles } from '../../../../styles/alertStyle';

const infoIcon = require('../../../../assets/iconsGlobalInfoDarkGray.png');

const dateFormat = 'MM/DD/YYYY hh:mm a';
const displayDateFormat = 'MMM DD, YYYY';

class IncomeDetails extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
      title: '',
      content: '',
    };
  }

  getNewPeriodSales = (period) => {
    let startOfMonth = moment().format('MM-DD-YYYY');
    let endOfMonth = moment().add(1, 'day').format('MM-DD-YYYY');

    switch (period) {
      case SALES_PERIODS[0].value:
        startOfMonth = moment().startOf('month').format('MM-DD-YYYY');
        endOfMonth = moment().endOf('month').isBefore(moment())
          ? moment().endOf('month').format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      case SALES_PERIODS[1].value:
        startOfMonth = moment().startOf('quarter').format('MM-DD-YYYY');
        endOfMonth = moment().endOf('quarter').isBefore(moment())
          ? moment().endOf('quarter').format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      case SALES_PERIODS[2].value:
        startOfMonth = moment().startOf('year').format('MM-DD-YYYY');
        endOfMonth = moment().endOf('year').isBefore(moment())
          ? moment().endOf('year').format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      default:
        break;
    }

    this.props.setPeriodHistoryAction(period);
    this.props.getSalesTaxAction({
      startDate: startOfMonth,
      endDate: endOfMonth,
    });
    this.props.onSelectPeriod(period, startOfMonth, endOfMonth);
    this.setState({
      showAlert: false,
      title: '',
      content: '',
    });
  };

  goBackInCalendar = () => {
    let startOfMonth = moment(this.props.sales.fromDate, dateFormat);
    let endOfMonth = moment(this.props.sales.toDate, dateFormat);

    switch (this.props.period) {
      case SALES_PERIODS[0].value:
        startOfMonth = moment(startOfMonth)
          .subtract(1, 'months')
          .startOf('month')
          .format('MM-DD-YYYY');
        endOfMonth = moment(endOfMonth)
          .subtract(1, 'months')
          .endOf('month')
          .isBefore(moment())
          ? moment(endOfMonth)
            .subtract(1, 'months')
            .endOf('month')
            .format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      case SALES_PERIODS[1].value:
        startOfMonth = moment(startOfMonth)
          .subtract(1, 'quarter')
          .startOf('quarter')
          .format('MM-DD-YYYY');
        endOfMonth = moment(endOfMonth)
          .subtract(1, 'quarter')
          .endOf('quarter')
          .isBefore(moment())
          ? moment(endOfMonth)
            .subtract(1, 'quarter')
            .endOf('quarter')
            .format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      case SALES_PERIODS[2].value:
        startOfMonth = moment(startOfMonth)
          .subtract(1, 'years')
          .startOf('year')
          .format('MM-DD-YYYY');
        endOfMonth = moment(endOfMonth)
          .subtract(1, 'year')
          .endOf('year')
          .isBefore(moment())
          ? moment(endOfMonth)
            .subtract(1, 'year')
            .endOf('year')
            .format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      default:
        break;
    }

    this.props.getSalesTaxAction({
      startDate: startOfMonth,
      endDate: endOfMonth,
    });
    this.props.onSelectPeriod(this.props.period, startOfMonth, endOfMonth);
    this.setState({
      showAlert: false,
      title: '',
      content: '',
    });
  };

  goForwardInCalendar = () => {
    let startOfMonth = moment(this.props.sales.fromDate, dateFormat);
    let endOfMonth = moment(this.props.sales.toDate, dateFormat);

    switch (this.props.period) {
      case SALES_PERIODS[0].value:
        startOfMonth = moment(startOfMonth)
          .add(1, 'months')
          .startOf('month')
          .format('MM-DD-YYYY');

        endOfMonth = moment(endOfMonth)
          .add(1, 'months')
          .endOf('month')
          .isBefore(moment())
          ? moment(endOfMonth)
            .add(1, 'months')
            .endOf('month')
            .format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      case SALES_PERIODS[1].value:
        startOfMonth = moment(startOfMonth)
          .add(1, 'quarter')
          .startOf('quarter')
          .format('MM-DD-YYYY');

        endOfMonth = moment(endOfMonth)
          .add(1, 'quarter')
          .endOf('quarter')
          .isBefore(moment())
          ? moment(endOfMonth)
            .add(1, 'quarter')
            .endOf('quarter')
            .format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      case SALES_PERIODS[2].value:
        startOfMonth = moment(startOfMonth)
          .add(1, 'years')
          .startOf('year')
          .format('MM-DD-YYYY');
        endOfMonth = moment(endOfMonth)
          .add(1, 'year')
          .endOf('year')
          .isBefore(moment())
          ? moment(endOfMonth).add(1, 'year').endOf('year').format('MM-DD-YYYY')
          : moment().format('MM-DD-YYYY');
        break;
      default:
        break;
    }

    this.props.getSalesTaxAction({
      startDate: startOfMonth,
      endDate: endOfMonth,
    });
    this.props.onSelectPeriod(this.props.period, startOfMonth, endOfMonth);
    this.setState({
      showAlert: false,
      title: '',
      content: '',
    });
  };

  hideAlert = () => {
    this.setState({
      showAlert: false,
      title: '',
      content: '',
    });
  };

  showAlert = (title, content) => {
    this.setState({
      showAlert: true,
      title,
      content,
    });
  };

  render() {
    let fromDate;
    let toDate;

    if (this.props.sales) {
      fromDate = moment(this.props.sales.fromDate, dateFormat).format(
        displayDateFormat,
      );
      toDate = moment(this.props.sales.toDate, dateFormat).format(
        displayDateFormat,
      );
    }

    return this.props.sales ? (
      <View style={styles.container}>
        <AwesomeAlert
          show={this.state.showAlert}
          showProgress={false}
          useNativeDriver
          title={this.state.title}
          message={this.state.content}
          closeOnTouchOutside
          closeOnHardwareBackPress={false}
          showConfirmButton={false}
          showCancelButton={false}
          titleStyle={deleteAlertStyles.alertTitle}
          messageStyle={deleteAlertStyles.alertText}
          confirmText="Cancel"
          cancelText="Close"
          confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
          confirmButtonStyle={deleteAlertStyles.alertButton}
          cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
          cancelButtonStyle={deleteAlertStyles.alertButton}
          actionContainerStyle={{
            flexDirection: 'column',
            backgroundColor: colors.white,
            borderRadius: 5,
          }}
          contentContainerStyle={deleteAlertStyles.alertContainer}
          onCancelPressed={() => {
            this.hideAlert();
          }}
          onConfirmPressed={() => {
            this.hideAlert();
          }}
          onDismiss={() => {
            this.hideAlert();
          }}
        />
        <View style={styles.topBox}>
          <View style={styles.periods}>
            {SALES_PERIODS.map((period, index) => (
              <TouchableOpacity
                key={index}
                style={
                  this.props.period === period.value
                    ? styles.periodItemActive
                    : styles.periodItem
                }
                onPress={() => this.getNewPeriodSales(period.value)}
              >
                <Text
                  style={
                    this.props.period === period.value
                      ? styles.periodItemTextActive
                      : styles.periodItemText
                  }
                >
                  {period.value}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        <View style={styles.bottomBox}>
          <View style={styles.dateBox}>
            <View style={{ flex: 0.4 }}>
              <IconFeader
                style={{ textAlign: 'left' }}
                name="chevron-left"
                size={25}
                color={colors.bordergrey}
                onPress={() => this.goBackInCalendar()}
              />
            </View>
            <Text
              style={{
                flex: 1,
                textAlign: 'center',
                ...generalStyles.avenirMedium15,
                color: colors.black,
              }}
            >
              {fromDate}
              {' '}
              -
              {toDate}
            </Text>
            <View style={{ flex: 0.4 }}>
              {!moment(this.props.sales.toDate, dateFormat).isSameOrAfter(
                moment(),
              ) ? (
                <IconFeader
                  style={{ textAlign: 'right' }}
                  name="chevron-right"
                  size={25}
                  color={colors.bordergrey}
                  onPress={() => this.goForwardInCalendar()}
                />
                ) : null}
            </View>
          </View>
          <View style={styles.salesBox}>
            <View style={styles.topSalesBox}>
              <Text style={styles.salesTitle}>Total revenue</Text>
              <Text style={styles.salesPriceBig}>
                {this.props.symbol}
                {parseFloat(this.props.sales.grossSales).toFixed(2)}
              </Text>
            </View>
            <View style={styles.bottomSalesBox}>
              <View style={styles.bottomLeftSalesBox}>
                <View style={styles.salesTitleView}>
                  <Text style={styles.salesTitle}>Taxable Sales</Text>
                  <TouchableOpacity
                    onPress={() => this.showAlert(
                      'Taxable Sales',
                      'Total of all taxable sales EXCLUDING sales tax and tips.',
                    )}
                  >
                    <Image source={infoIcon} />
                  </TouchableOpacity>
                </View>
                <Text style={styles.salesPrice}>
                  {this.props.symbol}
                  {parseFloat(this.props.sales.taxableSales).toFixed(2)}
                </Text>
              </View>
              <View style={styles.bottomRightSalesBox}>
                <View style={styles.salesTitleView}>
                  <Text style={styles.salesTitle}>Sales Tax Collected</Text>
                  <TouchableOpacity
                    onPress={() => this.showAlert(
                      'Sales Tax Collected',
                      'Taxable sales (x) tax rate. This is the amount you need to pay the state taxation authority.',
                    )}
                  >
                    <Image source={infoIcon} />
                  </TouchableOpacity>
                </View>
                <Text style={styles.salesPrice}>
                  {this.props.symbol}
                  {parseFloat(this.props.sales.salesTaxDue).toFixed(2)}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    ) : null;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  topBox: {
    display: 'flex',
    flexDirection: 'row',
  },
  bottomBox: {
    height: 200,
    display: 'flex',
    flexDirection: 'column',
    marginVertical: 20,
  },
  dateBox: {
    display: 'flex',
    flexDirection: 'row',
    height: 45,
    borderTopWidth: 1,
    borderTopColor: colors.lightgrey,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  salesBox: {
    display: 'flex',
    height: 155,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    alignItems: 'center',
    justifyContent: 'center',
  },
  salesTitleView: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  salesTitle: {
    display: 'flex',
    ...generalStyles.avenirMedium14,
    color: colors.subGrey,
    marginRight: 5,
  },
  salesPrice: {
    ...generalStyles.avenirHeavy17,
    color: colors.black,
  },
  salesPriceBig: {
    ...generalStyles.avenirHeavy30,
    color: colors.goodGreen,
  },
  topSalesBox: {
    marginBottom: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomSalesBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomLeftSalesBox: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  bottomRightSalesBox: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 15,
  },
  periods: {
    display: 'flex',
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: 30,
    borderWidth: 0.5,
    borderRadius: 4,
    borderColor: colors.fillDarkGrey,
  },
  periodItem: {
    display: 'flex',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    borderWidth: 0.5,
    borderColor: colors.fillDarkGrey,
  },
  periodItemText: {
    ...generalStyles.avenirHeavy14,
    color: colors.fillDarkGrey,
  },
  periodItemActive: {
    display: 'flex',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    backgroundColor: colors.fillDarkGrey,
  },
  periodItemTextActive: {
    ...generalStyles.avenirHeavy14,
    color: colors.white,
  },
});

const mapStateToProps = (state) => ({
  sales: state.sales.salesTax,
  period: state.sales.period,
  symbol: state.user.accountSettings?.symbol,
});

const mapDispatchToProps = {
  setSelectedClientTransactionAction,
  setFilteredClientTransactions,
  getSalesTaxAction,
  setPeriodHistoryAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(IncomeDetails);
