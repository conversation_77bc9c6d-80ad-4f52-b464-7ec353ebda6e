import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { PAY_METHODS } from '../../../../constants';
import { generalStyles } from '../../../../styles/generalStyle';

const cardActive = require('../../../../assets/ccActive.png');
const cardInactive = require('../../../../assets/ccInactive.png');
const cashActive = require('../../../../assets/cashActive.png');
const cashInactive = require('../../../../assets/cashInactive.png');

const CardCashOptionItem = (props) => (
  <View style={styles.content}>
    {props.payMethod === PAY_METHODS.DEBIT_OR_CC ? (
      <TouchableOpacity
        style={{ ...styles.cashCardButtonActive, ...styles.cardLeft }}
        activeOpacity={1}
        onPress={() => props.onUnselect()}
      >
        <Image source={cardActive} style={styles.cardIconActive} />
        <Text style={styles.labelPayActive}>Debit/CC</Text>
      </TouchableOpacity>
    ) : (
      <TouchableOpacity
        style={{ ...styles.cashCardButtonInactive, ...styles.cardLeft }}
        activeOpacity={1}
        onPress={() => props.onSelect(PAY_METHODS.DEBIT_OR_CC)}
      >
        <Image source={cardInactive} style={styles.cardIconDisable} />
        <Text style={styles.labelPayInactive}>Debit/CC</Text>
      </TouchableOpacity>
    )}
    {props.payMethod === PAY_METHODS.CASH_OR_CHECK ? (
      <TouchableOpacity
        style={{ ...styles.cashCardButtonActive, ...styles.cardRight }}
        activeOpacity={1}
        onPress={() => props.onUnselect()}
      >
        <Image source={cashActive} style={styles.cashImage} />
        <Text style={styles.labelPayActive}>Cash/Check</Text>
      </TouchableOpacity>
    ) : (
      <TouchableOpacity
        style={{ ...styles.cashCardButtonInactive, ...styles.cardRight }}
        activeOpacity={1}
        onPress={() => props.onSelect(PAY_METHODS.CASH_OR_CHECK)}
      >
        <Image source={cashInactive} style={styles.cashImage} />
        <Text style={styles.labelPayInactive}>Cash/Check</Text>
      </TouchableOpacity>
    )}
  </View>
);
const styles = StyleSheet.create({
  content: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 12,
  },
  cashImage: {
    height: 27,
    width: 41,
  },
  cardIconDisable: {
    width: 35,
    height: 25,
    borderWidth: 1,
    borderColor: colors.subGrey,
    borderRadius: 3,
  },
  cardIconActive: {
    width: 35,
    height: 25,
    borderWidth: 1,
    borderRadius: 3,
  },
  cashCardButtonInactive: {
    flex: 1,
    borderRadius: 3,
    backgroundColor: colors.white,
    borderColor: colors.bordergrey,
    borderWidth: 1,
    height: 79,
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cashCardButtonActive: {
    flex: 1,
    borderRadius: 3,
    backgroundColor: colors.goodGreen,
    borderColor: colors.goodGreen,
    borderWidth: 1,
    height: 79,
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  labelPayInactive: {
    textAlign: 'center',
    ...generalStyles.avenirHeavy17,
    paddingTop: 9,
    color: colors.subGrey,
  },
  labelPayActive: {
    textAlign: 'center',
    ...generalStyles.avenirHeavy17,
    paddingTop: 9,
    color: colors.white,
  },
  cardLeft: {
    marginRight: 10,
  },
  cardRight: {
    marginLeft: 10,
  },
});
export default CardCashOptionItem;
