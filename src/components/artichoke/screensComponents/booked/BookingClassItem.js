import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';
import { generalStyles } from '../../../../styles/generalStyle';

const BookingClassItem = (props) => (
  <TouchableOpacity
    activeOpacity={1}
    onPress={() => {
      props.navigation.navigate({
        name: APPLICATION_ROUTES.APP_CREATE_CLIENT,
        params: { returnRoute: props.returnRoute },
      });
    }}
  >
    <View style={{ ...styles.item, ...props.formStyle }}>
      <View style={styles.leftBox}>
        <View style={styles.userImageContainer}>
          <Text style={styles.textInfo}>
            {' '}
            <IconFeader name="user" size={18} color={colors.white} />
            {props.clients.length}
          </Text>
        </View>
      </View>
      <View style={styles.centerBox}>
        <Text style={styles.text}>Class</Text>
        <IconFeader
          style={{ flex: 0.5, textAlign: 'right', paddingRight: 11 }}
          name="chevron-down"
          size={25}
          color={colors.subGrey}
        />
      </View>
    </View>
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  textInfo: {
    color: colors.white,
    display: 'flex',
    fontSize: 16,
  },
  userImageContainer: {
    width: 50,
    height: 50,
    backgroundColor: colors.black,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    flex: 1,
    flexDirection: 'row',
    height: 85,
    borderWidth: 1,
    borderColor: colors.lightgrey,
  },
  leftBox: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingLeft: 10,
  },
  centerBox: {
    flex: 4.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    flex: 1,
    ...generalStyles.fontBold,
  },
});
export default BookingClassItem;
