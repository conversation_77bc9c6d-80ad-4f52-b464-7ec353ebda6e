import React from 'react';
import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';
import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const ClassServiceItem = (props) => {
  const serviceImage = props.service.serviceImage
    ? { uri: props.service.serviceImage }
    : bgImage;
  return (
    <TouchableOpacity
      style={{ ...styles.item, ...props.formStyle }}
      onPress={() => {
        props.navigation.navigate(APPLICATION_ROUTES.CLASS_CREATE_SERVICE, {
          returnRoute: props.returnRoute,
        });
      }}
    >
      <ImageBackground
        source={serviceImage}
        style={styles.image}
        imageStyle={{ borderRadius: 20 }}
      >
        <View style={generalStyles.overlay} />
      </ImageBackground>
      <View style={styles.topBox}>
        <View style={styles.topBoxLeft}>
          <Text
            style={{
              ...generalStyles.priceText,
              color: colors.white,
            }}
          >
            {props.symbol}
            {parseFloat(props.service.ProductDuration[0].price).toFixed(2)}
          </Text>
        </View>
        <Text
          style={
            styles.topBoxRight
          }
        >
          {`${props.service.ProductDuration[0].duration.duration} min`}
        </Text>
      </View>

      <View style={styles.titleBox}>
        <View style={styles.topTitleBox}>
          <Text style={styles.title}>
            {props.service.productName || props.service.name}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 10,
    flexDirection: 'column',
    height: 166,
  },
  topBox: {
    flexDirection: 'row',
  },
  titleBox: {
    height: 106,
    alignItems: 'flex-start',
    justifyContent: 'center',
    marginHorizontal: 30,
  },
  topBoxLeft: {
    backgroundColor: colors.goodGreen,
    alignItems: 'center',
    justifyContent: 'center',
    width: 55,
    height: 31,
    borderTopLeftRadius: 17,
    borderBottomRightRadius: 5,
  },
  topBoxRight: {
    flex: 9,
    backgroundColor: 'transparent',
    color: colors.white,
    ...generalStyles.durationText,
    textAlign: 'right',
    paddingTop: 10,
    marginRight: 20,
  },
  topTitleBox: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    ...generalStyles.titleService,
    color: colors.white,
    marginTop: 10,
  },
});
export default ClassServiceItem;
