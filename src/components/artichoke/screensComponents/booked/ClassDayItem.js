import React from 'react';
import {
  View, StyleSheet, Image, TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { DAYS, HOURS } from '../../../../constants';
import FormClassDay from '../../common/FormClassDay';
import FormClassTimeInput from '../../common/FormClassTimeInput';

const deleteIcon = require('../../../../assets/btnMinus.png');

const ClassDayItem = (props) => {
  const key = props.item.day;
  return (
    <View key={props.index} style={styles.container}>
      <View style={styles.day}>
        <FormClassDay
          value={props.item.day}
          items={DAYS}
          onChange={props.onChangeDay}
          index={props.index}
        />
      </View>
      <View style={styles.timeDays}>
        <View style={{ flex: 1, flexDirection: 'row', marginBottom: '4%' }}>
          <View style={styles.timeday}>
            <FormClassTimeInput
              day={key}
              label="Start"
              items={HOURS}
              index={props.index}
              value={props.item.startTime}
              onChange={props.onChangeStart}
            />
          </View>
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              props.onRemoveFromDay(key, 1);
            }}
            style={styles.removeButton}
          >
            <Image style={styles.addButtonText} source={deleteIcon} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flex: 1,
    width: '100%',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  timeday: {
    flex: 3,
    width: '80%',
    marginRight: 5,
  },
  day: {
    flex: 1,
    width: '20%',
    marginRight: 5,
  },
  timeDays: {
    flex: 3,
    flexDirection: 'column',
  },
  removeButton: {
    width: 34,
    height: 34,
    marginLeft: 5,
    backgroundColor: colors.white,
    color: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addButtonText: {
    fontSize: 19,
    color: colors.subGrey,
    flex: 1,
    fontWeight: 'bold',
  },
});
export default ClassDayItem;
