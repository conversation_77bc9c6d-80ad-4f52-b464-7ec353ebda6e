import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');
const checkMarkOn = require('../../../../assets/imgCheckmarkOnMedium.png');
const checkMarkOff = require('../../../../assets/imgCheckmarkOffMedium.png');

const showAppointmentType = (item) => {
  let name = '';
  if (!item.isScheduled && item.name === 'Multiple Clients') {
    name = 'Group Session';
  } else if (item.isScheduled) {
    name = 'Class';
  } else {
    name = item.name;
  }
  return name;
};

const renderAppointmentStyleAndInfo = (item) => {
  if (!item.isScheduled && item.Invitee.length === 1) {
    return (
      <Image
        source={
          item.Invitee[0].Client.user.avatarUrl
            ? { uri: item.Invitee[0].Client.user.avatarUrl }
            : bgImage
        }
        style={styles.userImage}
      />
    );
  }
  if (!item.isScheduled && item.name === 'Multiple Clients') {
    return (
      <View style={styles.multipleUsersContainer}>
        <Text style={styles.invitee}>
          {' '}
          <IconFeader name="user" size={24} color={colors.black} />
          {item.Invitee.length}
        </Text>
      </View>
    );
  }
  if (item.isScheduled) {
    return (
      <View
        style={[
          styles.multipleUsersContainer,
          { backgroundColor: colors.black },
        ]}
      >
        <Text style={[styles.invitee, { color: colors.white }]}>
          {' '}
          <IconFeader name="user" size={24} color={colors.white} />
          {item.Invitee.length}
        </Text>
      </View>
    );
  }
  return null;
};

const BookedListItem = (props) => {
  const hasInvitees = props.item?.Invitee?.length;
  const totalCount = props.item?.maxParticipantsCount;
  let checkedInCount;
  if (hasInvitees) {
    checkedInCount = props.item.Invitee.filter((inv) => inv.checkedIn === 'true')
      .length;
  } else {
    checkedInCount = props.item.checkedInCount;
  }
  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <View style={styles.topBox}>
        <Text style={styles.startHour}>
          {`${moment(props.item.start, 'MM/DD/YYYY h:mm a').format(
            'h:mm a',
          )} (${props.item.ProductDuration.duration.duration} min)`}
        </Text>
        <Text
          style={
            checkedInCount > 0 ? styles.checkedInTrue : styles.checkedInFalse
          }
        >
          {totalCount > 0
            ? `${checkedInCount} of ${totalCount} Checked In`
            : `${checkedInCount} Checked In`}
        </Text>
        <Image
          source={checkedInCount > 0 ? checkMarkOn : checkMarkOff}
          style={styles.checkMarkImage}
        />
      </View>
      <View style={styles.bottomBox}>
        {renderAppointmentStyleAndInfo(props.item)}
        <View style={styles.centerBox}>
          <Text style={styles.addressName}>
            {props.item.ProductDuration.name}
          </Text>
          <Text style={styles.addressLocation}>
            {showAppointmentType(props.item)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  item: {
    width: '100%',
    borderRadius: 10,
    display: 'flex',
    flexDirection: 'column',
    height: 123,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  topBox: {
    width: '100%',
    paddingHorizontal: 20,
    height: 40,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  startHour: {
    width: '45%',
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    color: colors.lightblue,
  },
  checkedInTrue: {
    width: '50%',
    textAlign: 'right',
    ...generalStyles.avenirHeavy13,
    color: colors.goodGreen,
  },
  checkedInFalse: {
    width: '50%',
    textAlign: 'right',
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  bottomBox: {
    width: '100%',
    marginHorizontal: 20,
    height: 73,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    paddingLeft: 20,
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirHeavy14,
    color: colors.black,
  },
  addressLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  multipleUsersContainer: {
    width: 56,
    height: 56,
    flexDirection: 'row',
    backgroundColor: colors.veryLightBlue,
    borderRadius: 28,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userImage: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  checkMarkImage: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginLeft: 5,
    paddingRight: 20,
  },
  invitee: {
    flex: 1,
    ...generalStyles.fontBold,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 5,
    color: colors.black,
  },
});
export default BookedListItem;
