import React from 'react';
import { Formik } from 'formik';
import {
  View,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Alert,
} from 'react-native';
import moment from 'moment';
import FormTouchableDropdown from '../../common/FormTouchableDropdown';
import FormGroup from '../../common/FormGroup';
import BookingNotification from '../notifications/BookingNotification';
import AddServiceItem from '../services/AddServiceItem';
import AddClientItem from './AddClientItem';
import BookingServiceItem from './BookingServiceItem';
import BookingMultipleClientItem from './BookingMultipleClientItem';
import BookingClientItem from './BookingClientItem';
import { APPLICATION_ROUTES } from '../../../../constants';
import { colors } from '../../../../styles';
import FormSubmitButtonWithSpinner from '../../common/FormSubmitButtonWithSpinner';

const CreateBookingForm = (props) => {
  const showSelectedAddress = (item) => {
    let addressLine = 'Select Location';
    if (item) {
      if (item.id === 2) {
        addressLine = 'Video Call';
      } else if (item.id === 1) {
        addressLine = 'Offered Remotely';
      } else if (item.id === 0) {
        addressLine = "Client's location";
      } else {
        addressLine = item.address2 !== ''
          ? `${item.address1}, ${item.address2}, ${item.city}, ${item.state}`
          : `${item.address1}, ${item.city}, ${item.state}`;
      }
    }
    return addressLine;
  };

  const showRepeatAppointmentInfo = (item) => {
    let repeat = 'Does not repeat';
    if (
      item.repeatIntervalType
      && item.repeatIntervalType !== 'Does not repeat'
    ) {
      repeat = `Repeat ${item.repeatIntervalType.toLowerCase()}`;
      if (item.untilDay) {
        repeat += ` until ${moment(item.untilDay, 'MM/DD/YYYY').format('L')}`;
      } else if (item.count) {
        if (item.count > 1) {
          repeat += ` - ${item.count} times`;
        } else {
          repeat += ` - ${item.count} time`;
        }
      }
    }
    return repeat;
  };

  const displayClient = (appointment) => {
    if (appointment.clients?.length === 1) {
      return (
        <BookingClientItem
          formStyle={{ marginHorizontal: 0 }}
          navigation={props.navigation}
          returnRoute={APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT}
          clients={appointment.clients}
        />
      );
    }
    if (appointment.clients?.length > 1) {
      return (
        <BookingMultipleClientItem
          formStyle={{ marginHorizontal: 0 }}
          navigation={props.navigation}
          returnRoute={APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT}
          clients={appointment.clients}
        />
      );
    }
    return <AddClientItem navigation={props.navigation} />;
  };

  const displayService = (appointment) => {
    if (appointment.service) {
      return (
        <BookingServiceItem
          formStyle={{ marginHorizontal: 0 }}
          navigation={props.navigation}
          returnRoute={APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT}
          service={appointment.service}
          symbol={props.symbol}
        />
      );
    }
    return (
      <AddServiceItem
        navigation={props.navigation}
        returnRoute={APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT}
      />
    );
  };

  const disableBookButton = (initialValues) => {
    let disable = true;
    if (
      initialValues.appointmentLocation != null
      && initialValues.clients.length !== 0
      && initialValues.service != null
      && initialValues.date != null
      && initialValues.time != null
    ) {
      disable = false;
    }
    return disable;
  };

  const showDateAndTimeInfo = (item) => {
    let dateTime = 'Select a Date and Time';
    if (item.date && item.time) {
      dateTime = `${moment(item.date, 'MM/DD/YYYY').format('L')} ${item.time}`;
    }
    return dateTime;
  };

  const onPressSelectDateTime = () => {
    if (!props.initialValues?.service) {
      Alert.alert('Please select a service first', '', [
        { text: 'OK', style: 'cancel' },
      ]);
    } else if (!props.initialValues?.appointmentLocation) {
      Alert.alert('Please select a location first', '', [
        { text: 'OK', style: 'cancel' },
      ]);
    } else {
      props.navigation.navigate({
        name: APPLICATION_ROUTES.APPOINTMENTS_DATE_AND_TIME,
        params: {
          returnRoute: APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT,
          workHours: props.initialValues?.appointmentLocation?.workHours
            ? props.initialValues?.appointmentLocation?.workHours
            : [],
        },
        merge: true,
      });
    }
  };

  const buttonStyle = disableBookButton(props.initialValues)
    ? { backgroundColor: colors.lightgrey, borderColor: colors.bordergrey }
    : { backgroundColor: colors.duskBlue, borderColor: colors.duskBlue };
  const buttonLabelStyle = disableBookButton(props.initialValues)
    ? { color: colors.subGrey }
    : { color: colors.white };
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={{ marginBottom: 30, paddingHorizontal: 20 }}>
        <Formik
          initialValues={props.initialValues}
          onSubmit={(values) => props.onSubmitForm(values)}
        >
          {({ handleBlur, setFieldValue, values }) => (
            <View style={styles.form}>
              {displayService(props.initialValues)}
              {displayClient(props.initialValues)}
              <FormGroup formStyle={{ marginHorizontal: 0 }}>
                {props.initialValues.service ? (
                  <FormTouchableDropdown
                    formInputStyle={{ marginVertical: 10 }}
                    label="Location"
                    value={showSelectedAddress(
                      props.initialValues.appointmentLocation,
                    )}
                    onPressFunc={() => props.navigation.navigate({
                      name: APPLICATION_ROUTES.SELECT_LOCATION_SCREEN,
                      params: {
                        returnRoute:
                            APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT,
                        mode: 'appointment',
                      },
                    })}
                    customStyle={styles.selectValue}
                  />
                ) : null}
                <FormTouchableDropdown
                  label="Date/Time"
                  value={showDateAndTimeInfo(props.initialValues)}
                  onPressFunc={() => onPressSelectDateTime()}
                  customStyle={styles.selectValue}
                />
                <FormTouchableDropdown
                  formInputStyle={{ marginTop: 10 }}
                  label="Repeat"
                  value={showRepeatAppointmentInfo(props.initialValues)}
                  onPressFunc={() => props.repeatActionSheetRef.current?.setModalVisible()}
                  customStyle={styles.selectValue}
                />
                <BookingNotification
                  onChangeValue={(value) => {
                    props.setNewAppointmentValuesAction({
                      key: 'sendNotifications',
                      value,
                    });
                    setFieldValue('sendNotifications', value);
                  }}
                  onBlur={handleBlur('sendNotifications')}
                  value={values.sendNotifications}
                  title="Notifications"
                  subTitle="I want my client to receive reminder notifications through Email for this appointment."
                />
              </FormGroup>
            </View>
          )}
        </Formik>
      </ScrollView>
      <View style={styles.bottom}>
        <FormSubmitButtonWithSpinner
          title="Book Session"
          disabled={disableBookButton(props.initialValues)}
          buttonStyle={buttonStyle}
          buttonLabelStyle={buttonLabelStyle}
          isLoading={props.isLoading}
          onPressButton={() => props.onSaveAppointment()}
        />
      </View>
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
  bottom: {
    flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 16,
    height: 50,
    paddingHorizontal: 20,
  },
  selectValue: {
    color: colors.subGrey,
  },
});
export default CreateBookingForm;
