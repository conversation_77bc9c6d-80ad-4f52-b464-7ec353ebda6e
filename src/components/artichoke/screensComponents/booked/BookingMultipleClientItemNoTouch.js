import React from 'react';
import {
  View, StyleSheet, Text, Platform,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import { APPLICATION_ROUTES } from '../../../../constants';
import FormSubmitButton from '../../common/FormSubmitButton';

const BookingMultipleClientItemNoTouch = (props) => (
  <View style={styles.contentUser}>
    <View style={styles.leftBox}>
      <View style={styles.userImageContainer}>
        <Text style={styles.textInfo}>
          {' '}
          <IconFeader name="user" size={18} color={colors.black} />
          {props.clients.length}
        </Text>
      </View>
      <View style={styles.userDetails}>
        <Text style={styles.userName}>Group Session</Text>
      </View>
    </View>
    <View style={styles.rightBox}>
      {props.inapp === 'true' && (
        <FormSubmitButton
          buttonStyle={{
            backgroundColor: colors.white,
            borderColor: colors.bordergrey,
            borderWidth: 2,
            marginRight: 15,
            height: 35,
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          buttonLabelStyle={{
            color: colors.subGrey,
            ...generalStyles.fontBold,
          }}
          title="View"
          onPressButton={() => props.navigation.navigate({
            name: APPLICATION_ROUTES.MULTIPLE_CHECK_IN_CLIENT,
          })}
        />
      )}
    </View>
  </View>
);
const styles = StyleSheet.create({
  textInfo: {
    color: colors.black,
    display: 'flex',
    fontSize: 16,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftBox: {
    flex: 2.5,
    flexDirection: 'row',
    padding: 15,
    alignItems: 'flex-start',
  },
  rightBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userImageContainer: {
    width: 50,
    height: 50,
    backgroundColor: colors.veryLightBlue,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userDetails: {
    flex: 5,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  userName: {
    display: 'flex',
    ...generalStyles.fontBold,
    marginTop: Platform.OS === 'ios' ? 10 : 5,
    color: colors.black,
  },
});
export default BookingMultipleClientItemNoTouch;
