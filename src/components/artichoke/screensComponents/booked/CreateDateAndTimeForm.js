import React, { useState, Fragment } from 'react';
import { Formik } from 'formik';
import {
  View, StyleSheet, ScrollView, Text,
} from 'react-native';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import FormGroup from '../../common/FormGroup';
import AppointmentDateInput from '../../common/AppointmentDateInput';
import { getIntervals } from '../../../../util/utils';
import SelectTimeForm from '../../common/SelectTimeForm';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const CreateDateAndTimeForm = (props) => {
  const selDate = props.selectedDate
    ? new Date(moment(props.selectedDate, 'L'))
    : new Date();
  const workHours = props.workHours?.length
    ? Object.entries(JSON.parse(props.workHours)).map(([key, value]) => ({
      [key]: typeof value === 'string' ? value.split(', ') : value,
    }))
    : [];

  const [workDays] = useState(workHours);
  const [date, setDate] = useState(selDate);
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);

  const onSelectDate = (selectedDate) => {
    setShow(false);
    setDate(selectedDate);
    props.onChangeDate(selectedDate);
    props.onChangeTime(null);
  };
  const onSelectTime = (time) => {
    props.onChangeTime(time);
  };

  const showDatepicker = () => {
    setShow(true);
    setMode('date');
  };

  const hideDatePicker = () => {
    setShow(false);
  };

  const getDay = () => {
    const day = workDays.find(
      (data) => Object.keys(data).toString()
        === moment(date).format('dddd').toLowerCase(),
    );
    return day ? day[moment(date).format('dddd').toLowerCase()][0] : '';
  };

  let timeSlots = [];
  if (getDay()) {
    if (workDays.length) {
      timeSlots = getIntervals(
        moment(getDay().from, 'HH:mm a').format('HH:mm'),
        moment(getDay().to, 'HH:mm a').format('HH:mm'),
        'LT',
      );
    } else {
      timeSlots = getIntervals('07:00', '20:59', 'LT');
    }
  }

  return (
    <ScrollView style={styles.container}>
      <Formik validateOnMount onSubmit={(values) => props.onSubmitForm(values)}>
        {() => (
          <View style={styles.form}>
            <FormGroup formStyle={styles.formStyle}>
              <Text style={styles.labelDate}>Date</Text>
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <AppointmentDateInput
                selectedValue={moment(props.selectedDate).format('L')}
                onPressFunc={showDatepicker}
              />
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <Text style={styles.label}>Available Times</Text>
              <Text style={{ ...styles.label, ...{ marginTop: 20 } }}>AM</Text>
              <View style={styles.selectcontainer}>
                {timeSlots.map((slot, index) => {
                  const isBlocked = props.blockingAppointments.filter(
                    (appointment) => {
                      const startTime = moment(
                        appointment.start,
                        'MM/DD/YYYY hh:mm A',
                      ).format('LT');

                      const endTime = moment(
                        appointment.end,
                        'MM/DD/YYYY hh:mm A',
                      ).format('LT');
                      return (
                        moment(slot, 'LT').isBefore(moment(endTime, 'LT'))
                        && moment(slot, 'LT').isSameOrAfter(
                          moment(startTime, 'LT'),
                        )
                      );
                    },
                  ).length;
                  if (moment(slot, 'LT').isBefore(moment('12:00 PM', 'LT'))) {
                    return (
                      <SelectTimeForm
                        key={`select-start-times-slot-key-${index}`}
                        buttonTitle={slot.replace('AM', '')}
                        width="25%"
                        isBlocked={isBlocked}
                        selected={props.selectedTime === slot}
                        onChangeValue={() => onSelectTime(slot)}
                      />
                    );
                  }
                  if (moment(slot, 'LT').isSame(moment('12:00 PM', 'LT'))) {
                    return (
                      <>
                        <Text style={styles.label2}>PM</Text>
                        <SelectTimeForm
                          key={`select-start-times-slot-key-${index}`}
                          buttonTitle={slot.replace('PM', '')}
                          width="25%"
                          isBlocked={isBlocked}
                          selected={props.selectedTime === slot}
                          onChangeValue={() => onSelectTime(slot)}
                        />
                      </>
                    );
                  }
                  return (
                    <SelectTimeForm
                      key={`select-start-times-slot-key-${index}`}
                      buttonTitle={slot.replace('PM', '')}
                      width="25%"
                      isBlocked={isBlocked}
                      selected={props.selectedTime === slot}
                      onChangeValue={() => onSelectTime(slot)}
                    />
                  );
                })}
              </View>
            </FormGroup>
            <View>
              <DateTimePickerModal
                date={date}
                mode={mode}
                is24Hour
                display="default"
                isVisible={show}
                onConfirm={onSelectDate}
                onCancel={hideDatePicker}
              />
            </View>
          </View>
        )}
      </Formik>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
  formStyle: {
    flex: 1,
  },
  labelDate: {
    ...generalStyles.avenirRoman22,
    width: '100%',
    color: colors.black,
  },
  label: {
    ...generalStyles.avenirRoman22,
    width: '100%',
    color: colors.black,
    marginTop: 30,
  },
  label2: {
    ...generalStyles.avenirRoman22,
    width: '100%',
    color: colors.black,
    marginTop: 30,
  },
  selectcontainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    flex: 1,
    marginTop: 10,
  },
});
export default CreateDateAndTimeForm;
