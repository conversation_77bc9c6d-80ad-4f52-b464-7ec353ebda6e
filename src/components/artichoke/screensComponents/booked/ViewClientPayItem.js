import React from 'react';
import {
  View, StyleSheet, Text, Image,
} from 'react-native';
import moment from 'moment';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const ViewClientPayItem = (props) => (
  <View style={styles.contentUser}>
    <Image
      source={
        props.clientInfo.Client.user.avatarUrl
          ? { uri: props.clientInfo.Client.user.avatarUrl }
          : bgImage
      }
      style={styles.userImage}
    />
    <View style={styles.userDetails}>
      <Text style={styles.userName}>
        {props.clientInfo.Client.user.firstName}
        {' '}
        {props.clientInfo.Client.user.lastName}
        {' '}
      </Text>
      <Text style={styles.userJob}>
        {props.appointment.ProductDuration.name}
      </Text>
    </View>
    <View style={styles.appData}>
      <Text style={styles.userName}>
        {moment(props.appointment.start, 'MM/DD/YYYY hh:mm A').format(
          'hh:mm A',
        )}
      </Text>
      <Text style={styles.userJob}>
        {`${props.appointment.ProductDuration.duration.duration} ${props.appointment.ProductDuration.duration.granularity.abbreviation}`}
      </Text>
    </View>
  </View>
);
const styles = StyleSheet.create({
  userImage: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userDetails: {
    flex: 6,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    lineHeight: 22,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  appData: {
    flex: 5,
    alignItems: 'flex-end',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
  },
});
export default ViewClientPayItem;
