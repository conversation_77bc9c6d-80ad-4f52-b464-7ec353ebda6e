import React from 'react';
import {
  View, StyleSheet, Text, Alert,
} from 'react-native';
import _cloneDeep from 'lodash.clonedeep';
import moment from 'moment';
import FormGroup from '../../common/FormGroup';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import AddButton from '../../common/AddButton';
import ClassDayItem from './ClassDayItem';

const ClassScheduleForm = (props) => {
  const onChangeStart = (day, newStart, rowIndex) => {
    const scheduleDays = _cloneDeep(props.scheduleDays);
    const alreadyExist = scheduleDays.filter((dayItem, objIndex) => {
      if (
        objIndex !== rowIndex
        && dayItem.day.toString() === day.toString()
        && dayItem.startTime.toString()
          === moment(newStart, 'h:mma').format('HH:mm').toString()
      ) {
        return dayItem;
      }
      return null;
    });
    let objWorkHours = [];
    if (alreadyExist.length) {
      Alert.alert(
        'Error',
        'You cannot have two of the same time for the same day',
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: true },
      );
    } else {
      objWorkHours = scheduleDays.map((dayItem, index) => {
        if (
          (dayItem.day === day || dayItem.day === 'new')
          && index === rowIndex
        ) {
          dayItem.startTime = moment(newStart, 'h:ma')
            .format('HH:mm')
            .toString();
        }
        return dayItem;
      });

      props.setNewClassValuesAction({
        key: 'scheduleDays',
        value: objWorkHours,
      });
    }
  };

  const onChangeDay = (newDay, oldDay, rowIndex) => {
    const scheduleDays = _cloneDeep(props.scheduleDays);
    const alreadyExist = scheduleDays.filter((dayItem, objIndex) => {
      if (
        objIndex !== rowIndex
        && dayItem.day.toString() === newDay.toString()
        && dayItem.startTime.toString()
          === moment(scheduleDays[rowIndex].startTime, 'h:mma')
            .format('HH:mm')
            .toString()
      ) {
        return dayItem;
      }
      return null;
    });

    let objWorkHours = [];
    if (alreadyExist.length) {
      Alert.alert(
        'Error',
        'You cannot have two of the same time for the same day',
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: true },
      );
    } else {
      objWorkHours = scheduleDays.map((dayItem, index) => {
        if (dayItem.day === oldDay && index === rowIndex) {
          dayItem.day = newDay;
        }
        return dayItem;
      });

      props.setNewClassValuesAction({
        key: 'scheduleDays',
        value: objWorkHours,
      });
    }
  };

  const onRemoveFromDay = (day) => {
    const scheduleDays = _cloneDeep(props.scheduleDays);
    const objWorkHours = scheduleDays.filter((dayItem) => dayItem.day !== day);

    props.setNewClassValuesAction({
      key: 'scheduleDays',
      value: objWorkHours,
    });
  };

  const onAddNewRow = () => {
    const objWorkHours = _cloneDeep(props.scheduleDays);
    const alreadyExist = objWorkHours.filter((dayItem) => dayItem.day === 'new');
    if (alreadyExist.length) {
      Alert.alert(
        'Error',
        'You already have an empty day!',
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: true },
      );
    } else {
      objWorkHours.push({
        day: 'new',
        startTime: moment().startOf('hour').format('HH:mm').toString(),
      });

      props.setNewClassValuesAction({
        key: 'scheduleDays',
        value: objWorkHours,
      });
    }
  };

  return (
    <View>
      <View style={styles.container}>
        {props.scheduleDays
          && props.scheduleDays.map((item, index) => (
            <ClassDayItem
              key={index + 1}
              item={item}
              index={index}
              onChangeDay={onChangeDay}
              onChangeStart={onChangeStart}
              onRemoveFromDay={onRemoveFromDay}
            />
          ))}
      </View>
      <FormGroup
        formStyle={{
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          marginTop: 20,
          marginHorizontal: 0,
        }}
      >
        <Text style={styles.addworkHoursTitle}>Add Additional Times</Text>
        <AddButton onPressAction={onAddNewRow} small />
      </FormGroup>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  addworkHoursTitle: {
    ...generalStyles.titleSmall,
    color: colors.black,
  },
});
export default ClassScheduleForm;
