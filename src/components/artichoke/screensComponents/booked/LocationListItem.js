import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles, locationStyles } from '../../../../styles/generalStyle';

const locationIcon = require('../../../../assets/locationIcon.png');
const remoteIcon = require('../../../../assets/ic_remote.png');
const videoLocationIcon = require('../../../../assets/ic_video_call.png');

const LocationListItem = (props) => {
  const getIconAttributes = () => {
    switch (props.item.id) {
      case 1:
        return [remoteIcon, locationStyles.remoteLocationIcon];
      case 2:
        return [videoLocationIcon, locationStyles.videoCallLocationIcon];
      default:
        return [locationIcon, locationStyles.locationIcon];
    }
  };
  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.setNewAppointmentLocation(props.item)}
    >
      <View style={styles.leftBox}>
        <Image source={getIconAttributes()[0]} style={getIconAttributes()[1]} />
      </View>
      <View style={styles.centerBox}>
        <Text style={styles.addrName} numberOfLines={1}>
          {props.item.addressName}
        </Text>
        {props.item.address1 && (
          <Text style={styles.addrDescription}>{props.item.address1}</Text>
        )}
      </View>
      <View style={styles.rightBox}>
        <IconFeader name="chevron-right" size={30} color={colors.subGrey} />
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  item: {
    width: '100%',
    borderRadius: 10,
    flex: 1,
    flexDirection: 'row',
    height: 79,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  leftBox: {
    flex: 1,
    paddingLeft: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerBox: {
    flex: 5,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  rightBox: {
    flex: 1,
    paddingRight: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addrName: {
    ...generalStyles.fontBold,
    width: '100%',
    color: colors.black,
  },
  addrDescription: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
});
export default LocationListItem;
