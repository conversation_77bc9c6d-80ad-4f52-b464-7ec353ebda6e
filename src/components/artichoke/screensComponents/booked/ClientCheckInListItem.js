import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const ClientCheckInListItem = (props) => {
  const checkInStyle = props.isCheckedIn
    ? styles.checkedIn
    : styles.uncheckedIn;

  return (
    <View style={styles.item} key={props.item.Client.user.id}>
      <View style={styles.leftBox}>
        <Image
          source={
            props.item.Client.user.avatarUrl
              ? { uri: props.item.Client.user.avatarUrl }
              : bgImage
          }
          style={styles.userImage}
        />
      </View>
      <View style={styles.centerBox}>
        <Text style={styles.name}>{props.item.Client.user.firstName}</Text>
      </View>
      <View style={styles.rightBox}>
        <TouchableOpacity
          style={checkInStyle}
          onPress={props.isCheckedIn ? props.onPressUnCheckInClient : props.onPressCheckInClient}
        >
          {props.isCheckedIn ? (
            <Text style={styles.labelChecked}>Checked In</Text>
          ) : (
            <Text style={styles.labelUnChecked}>Check In</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    flex: 1,
    flexDirection: 'row',
    height: 80,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftBox: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  centerBox: {
    flex: 3,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  rightBox: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  name: {
    ...generalStyles.avenirHeavy17,
    color: colors.black,
  },
  userImage: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 25,
  },
  uncheckedIn: {
    width: 120,
    borderColor: colors.bordergrey,
    backgroundColor: colors.white,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: 18,
    borderWidth: 2,
    height: 36,
  },
  checkedIn: {
    width: 120,
    backgroundColor: colors.greenselect,
    borderColor: colors.greenselect,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: 18,
    borderWidth: 2,
    height: 36,
  },
  labelChecked: {
    ...generalStyles.avenirHeavy17,
    color: colors.white,
  },
  labelUnChecked: {
    ...generalStyles.avenirHeavy17,
    color: colors.bordergrey,
  },
});
export default ClientCheckInListItem;
