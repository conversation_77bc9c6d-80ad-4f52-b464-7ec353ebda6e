import React, { useState } from 'react';
import { Formik } from 'formik';
import { View, StyleSheet, ScrollView } from 'react-native';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import FormTouchableDropdown from '../../common/FormTouchableDropdown';
import FormGroup from '../../common/FormGroup';
import BookingNotification from '../notifications/BookingNotification';
import { APPLICATION_ROUTES } from '../../../../constants';
import FormTextInput from '../../common/FormTextInput';
import ClassScheduleForm from './ClassScheduleForm';
import ClassServiceItem from './ClassServiceItem';
import AddClassServiceItem from '../services/AddClassServiceItem';

const EditClassForm = (props) => {
  const selDate = props.initialValues.startDate
    ? new Date(moment(props.initialValues.startDate, 'L'))
    : new Date();
  const [date, setDate] = useState(selDate);
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);

  const onSelectDate = (selectedDate) => {
    setShow(false);
    setDate(selectedDate);
    props.setNewClassValuesAction({
      key: 'startDate',
      value: selectedDate.toString(),
    });
  };

  const showDatepicker = () => {
    setShow(true);
    setMode('date');
  };

  const hideDatePicker = () => {
    setShow(false);
  };

  const showSelectedAddress = (item) => {
    let addressLine = 'Select Location';
    if (item) {
      if (item.id === 1) {
        addressLine = 'Offered Remotely';
      } else if (item.id === 0) {
        addressLine = "Client's location";
      } else if (item.id === 2) {
        addressLine = 'Video Call';
      } else {
        addressLine = item.address2 !== ''
          ? `${item.address1}, ${item.address2}, ${item.city}, ${item.state}`
          : `${item.address1}, ${item.city}, ${item.state}`;
      }
    }
    return addressLine;
  };

  const showRepeatAppointmentInfo = (item) => {
    let repeat = 'Does not repeat';
    if (item.repeatFrequency && item.repeatFrequency !== 'Does not repeat') {
      repeat = `Repeat ${item.repeatFrequency.toLowerCase()}`;
      repeat += 'ly';
      if (item.repeatUntil) {
        repeat += ` until ${moment(item.repeatUntil, 'MM/DD/YYYY').format(
          'L',
        )}`;
      } else if (item.repeatCount) {
        if (item.repeatCount > 1) {
          repeat += ` - ${item.repeatCount} times`;
        } else {
          repeat += ` - ${item.repeatCount} time`;
        }
      }
    }
    return repeat;
  };

  const displayService = (appointment) => {
    if (appointment.service) {
      return (
        <ClassServiceItem
          formStyle={{ marginHorizontal: 0 }}
          navigation={props.navigation}
          returnRoute={APPLICATION_ROUTES.EDIT_CLASS}
          service={appointment.service}
          symbol={props.symbol}
        />
      );
    }
    return (
      <AddClassServiceItem
        navigation={props.navigation}
        returnRoute={APPLICATION_ROUTES.EDIT_CLASS}
      />
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Formik
        initialValues={props.initialValues}
        onSubmit={(values) => props.onSubmitForm(values)}
      >
        {({ handleBlur, setFieldValue }) => (
          <View style={styles.form}>
            {displayService(props.initialValues)}
            <FormGroup formStyle={styles.maxParticipantsView}>
              <FormTextInput
                onChangeText={(value) => {
                  props.setNewClassValuesAction({
                    key: 'maxParticipants',
                    value,
                  });
                  setFieldValue('maxParticipants', value);
                }}
                label="Max Participants"
                placeholder="1"
                name="maxParticipants"
                value={props.initialValues.maxParticipants.toString()}
                keyboardType="numeric"
              />
            </FormGroup>
            <ClassScheduleForm
              scheduleDays={props.initialValues.scheduleDays}
              setNewClassValuesAction={props.setNewClassValuesAction}
            />
            <FormGroup formStyle={{ marginHorizontal: 0 }}>
              <FormTouchableDropdown
                label="Date"
                value={
                  props.initialValues.startDate
                    ? moment(props.initialValues.startDate).format('L')
                    : moment().format('L')
                }
                onPressFunc={showDatepicker}
              />

              <FormTouchableDropdown
                formInputStyle={{ marginTop: 10 }}
                label="Repeat"
                value={showRepeatAppointmentInfo(props.initialValues)}
                onPressFunc={() => props.navigation.navigate({
                  name: APPLICATION_ROUTES.CLASS_RECURRENCE_SCREEN,
                  params: {
                    returnRoute: APPLICATION_ROUTES.EDIT_CLASS,
                  },
                })}
              />
              <FormTouchableDropdown
                formInputStyle={{ marginTop: 10 }}
                label="Location"
                value={showSelectedAddress(
                  props.initialValues.selfBookingAddressId,
                )}
                onPressFunc={() => props.navigation.navigate({
                  name: APPLICATION_ROUTES.SELECT_LOCATION_SCREEN,
                  params: {
                    returnRoute: APPLICATION_ROUTES.EDIT_CLASS,
                  },
                })}
              />
              <BookingNotification
                onChangeValue={(value) => {
                  props.setNewClassValuesAction({
                    key: 'sendNotifications',
                    value,
                  });
                  setFieldValue('sendNotifications', value);
                }}
                onBlur={handleBlur('sendNotifications')}
                value={props.initialValues.sendNotifications}
                title="Notifications"
                subTitle="I want my client to receive reminder notifications through Email for this appointment."
              />
            </FormGroup>
            <View>
              <DateTimePickerModal
                date={date}
                mode={mode}
                is24Hour
                display="default"
                isVisible={show}
                onConfirm={onSelectDate}
                onCancel={hideDatePicker}
              />
            </View>
          </View>
        )}
      </Formik>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
    paddingHorizontal: 20,
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
  maxParticipantsView: {
    marginBottom: 30,
    marginHorizontal: 0,
  },
});
export default EditClassForm;
