import React from 'react';
import {ImageBackground, View, StyleSheet, Text} from 'react-native';
import {colors} from '../../../../styles';
import {generalStyles} from "../../../../styles/generalStyle";
const bgImage = require('../../../../assets/imgServiceBackground.png');
const AppointmentServiceView = (props) => {
  const serviceImage = props.service.serviceImage ? {uri: props.service.serviceImage} : bgImage;
  return (
    <View style={styles.item}>
      <ImageBackground
        source={serviceImage}
        style={styles.image}
        imageStyle={{borderRadius: 20}}>
        <View style={generalStyles.overlay}/>
      </ImageBackground>
      <View style={styles.topBox}>
        <View style={styles.topBoxLeft}>
          <Text style={generalStyles.priceText}>
            {props.symbol}{parseFloat(props.service.ProductDuration.price).toFixed(2)}
          </Text>
        </View>
        <Text
          style={
            styles.topBoxRight
          }>{props.service.ProductDuration.duration.duration} {props.service.ProductDuration.duration.granularity.abbreviation}</Text>
      </View>

      <View style={styles.titleBox}>
        <View style={styles.topTitleBox}>
        <Text style={styles.title}>{props.service.ProductDuration.name}</Text>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  image: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 17,
    flexDirection: 'column',
    height: 166,
    flex: 1,
  },
  topBox: {
    flex: 3,
    flexDirection: 'row',
  },
  titleBox: {
    flex: 7,
    textAlign: 'center',
    flexDirection: 'column',
    marginHorizontal: 23,
    marginBottom: 20,
  },
  topBoxLeft: {
    flex: 2,
    backgroundColor: colors.goodGreen,
    color: colors.white,
    fontWeight: 'bold',
    width: 40,
    height: 31,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderBottomRightRadius: 5,
  },
  topBoxRight: {
    ...generalStyles.durationText,
    flex: 9,
    backgroundColor: 'transparent',
    color: colors.white,
    textAlign: 'right',
    paddingTop: 10,
    marginRight: 23,
  },
  topTitleBox: {
    flex: 2,
    alignSelf: 'flex-start',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 5,
  },
  title: {
    ...generalStyles.titleService,
    color: colors.white,
  },
});
export default AppointmentServiceView;
