import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import { APPLICATION_ROUTES } from '../../../../constants';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import FormSubmitButton from '../../common/FormSubmitButton';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const ViewClientItem = (props) => (
  <View style={styles.contentUser}>
    <View style={styles.leftBox}>
      <Image
        source={
          props.clients[0].Client.user.avatarUrl
            ? { uri: props.clients[0].Client.user.avatarUrl }
            : bgImage
        }
        style={styles.userImage}
      />
      <View style={styles.userDetails}>
        <Text style={styles.userName}>
          {props.clients[0].Client.user.firstName}
          {' '}
          {props.clients[0].Client.user.lastName}
          {' '}
        </Text>
        <TouchableOpacity
          onPress={() => {
            props.selectClientCallback(props.clients[0].Client.user.id);
          }}
        >
          <Text style={styles.userJob}>View Client Schedule</Text>
        </TouchableOpacity>
      </View>
    </View>
    <View style={styles.rightBox}>
      {props.inapp === 'true' && (
        <FormSubmitButton
          buttonStyle={{
            backgroundColor: colors.white,
            borderColor: colors.bordergrey,
            borderWidth: 2,
            marginRight: 15,
            height: 35,
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          buttonLabelStyle={{
            color: colors.subGrey,
            ...generalStyles.fontBold,
          }}
          title="View"
          onPressButton={() => props.navigation.navigate({
            name: APPLICATION_ROUTES.MULTIPLE_CHECK_IN_CLIENT,
          })}
        />
      )}
    </View>
  </View>
);
const styles = StyleSheet.create({
  userImage: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 25,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftBox: {
    flex: 2.5,
    flexDirection: 'row',
    padding: 15,
    alignItems: 'flex-start',
  },
  userDetails: {
    flex: 5,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  rightBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default ViewClientItem;
