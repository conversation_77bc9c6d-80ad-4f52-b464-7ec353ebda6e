import React from 'react';
import { Formik } from 'formik';
import {
  View, StyleSheet, ScrollView, Alert,
} from 'react-native';
import moment from 'moment';
import FormTouchableDropdown from '../../common/FormTouchableDropdown';
import FormGroup from '../../common/FormGroup';
import BookingNotification from '../notifications/BookingNotification';
import AddServiceItem from '../services/AddServiceItem';
import AddClientItem from './AddClientItem';
import BookingServiceItem from './BookingServiceItem';
import BookingMultipleClientItem from './BookingMultipleClientItem';
import BookingClassItem from './BookingClassItem';
import BookingClientItem from './BookingClientItem';
import { APPLICATION_ROUTES } from '../../../../constants';

const EditBookingForm = (props) => {
  const showSelectedAddress = (item) => {
    let addressLine = 'Select Location';
    if (item.appointmentLocation) {
      if (item.appointmentLocation.id === 2) {
        addressLine = 'Video Call';
      } else if (item.appointmentLocation.id === 1) {
        addressLine = 'Offered Remotely';
      } else if (item.appointmentLocation.id === 0) {
        addressLine = "Client's location";
      } else {
        addressLine = item.appointmentLocation.address2 !== ''
          ? `${item.appointmentLocation.address1}, ${item.appointmentLocation.address2}, ${item.appointmentLocation.city}, ${item.appointmentLocation.state}`
          : `${item.appointmentLocation.address1}, ${item.appointmentLocation.city}, ${item.appointmentLocation.state}`;
      }
    } else if (item) {
      if (item.remotely.toString() === 'true') {
        addressLine = 'Offered Remotely';
      } else if (item.clientAddressEnable.toString() === 'true') {
        addressLine = "Client's location";
      } else if (item.inapp.toString() === 'true') {
        addressLine = 'Video Call';
      }
    }
    return addressLine;
  };

  const showRepeatAppointmentInfo = (item) => {
    let repeat = 'Does not repeat';
    if (
      item.repeatIntervalType
      && item.repeatIntervalType !== 'Does not repeat'
    ) {
      repeat = `Repeat ${item.repeatIntervalType.toLowerCase()}`;
      if (item.untilDay) {
        repeat += ` until ${moment(item.untilDay, 'MM/DD/YYYY').format('L')}`;
      } else if (item.count) {
        if (item.count > 1) {
          repeat += ` - ${item.count} times`;
        } else {
          repeat += ` - ${item.count} time`;
        }
      }
    }
    return repeat;
  };

  const displayClient = (appointment) => {
    if (!appointment.isScheduled && appointment.clients.length === 1) {
      return (
        <BookingClientItem
          navigation={props.navigation}
          clients={appointment.clients}
        />
      );
    }
    if (!appointment.isScheduled && appointment.clients.length > 1) {
      return (
        <BookingMultipleClientItem
          navigation={props.navigation}
          clients={appointment.clients}
        />
      );
    }
    if (appointment.isScheduled) {
      return (
        <BookingClassItem
          navigation={props.navigation}
          clients={appointment.clients}
        />
      );
    }
    return <AddClientItem navigation={props.navigation} />;
  };

  const displayService = (appointment) => {
    if (appointment.service) {
      return (
        <BookingServiceItem
          navigation={props.navigation}
          service={appointment.service}
          returnRoute={APPLICATION_ROUTES.EDIT_APPOINTMENT}
          symbol={props.symbol}
        />
      );
    }
    return (
      <AddServiceItem
        navigation={props.navigation}
        returnRoute={APPLICATION_ROUTES.CREATE_NEW_APPOINTMENT}
      />
    );
  };

  const onPressSelectDateTime = () => {
    if (!props.initialValues?.service) {
      Alert.alert('Please select a service first', '', [
        { text: 'OK', style: 'cancel' },
      ]);
    } else if (
      !props.initialValues?.appointmentLocation
      && props.initialValues?.remotely.toString() !== 'true'
      && props.initialValues?.clientAddressEnable.toString() !== 'true'
      && props.initialValues?.inapp.toString() !== 'true'
    ) {
      Alert.alert('Please select a location first', '', [
        { text: 'OK', style: 'cancel' },
      ]);
    } else {
      props.navigation.navigate({
        name: APPLICATION_ROUTES.APPOINTMENTS_DATE_AND_TIME,
        params: {
          returnRoute: APPLICATION_ROUTES.EDIT_APPOINTMENT,
          workHours: props.initialValues?.appointmentLocation?.workHours
            ? props.initialValues?.appointmentLocation?.workHours
            : [],
        },
        merge: true,
      });
    }
  };

  const showDateAndTimeInfo = (item) => {
    let dateTime = 'Select a Date and Time';
    if (item.date && item.time) {
      dateTime = `${item.date} ${item.time}`;
    }
    return dateTime;
  };

  return (
    <ScrollView style={styles.container}>
      <Formik
        initialValues={props.initialValues}
        onSubmit={(values) => props.onSubmitForm(values)}
      >
        {({ handleBlur, setFieldValue, values }) => (
          <View style={styles.form}>
            {displayService(props.initialValues)}
            {displayClient(props.initialValues)}
            <FormGroup formStyle={styles.formStyle}>
              <FormTouchableDropdown
                formInputStyle={{ marginVertical: 10 }}
                label="Location"
                value={showSelectedAddress(props.initialValues)}
                onPressFunc={() => props.navigation.navigate(
                  APPLICATION_ROUTES.SELECT_LOCATION_SCREEN,
                  {
                    returnRoute: APPLICATION_ROUTES.EDIT_APPOINTMENT,
                    mode: 'appointment',
                  },
                )}
              />
              <FormTouchableDropdown
                label="Date/Time"
                value={showDateAndTimeInfo(props.initialValues)}
                onPressFunc={() => onPressSelectDateTime()}
              />
              {!props.initialValues.isScheduled ? (
                <FormTouchableDropdown
                  formInputStyle={{ marginTop: 10 }}
                  label="Repeat"
                  value={showRepeatAppointmentInfo(props.initialValues)}
                  onPressFunc={() => props.repeatActionSheetRef.current?.setModalVisible()}
                />
              ) : null}
              <BookingNotification
                onChangeValue={(value) => {
                  props.setNewAppointmentValuesAction({
                    key: 'sendNotifications',
                    value,
                  });
                  setFieldValue('sendNotifications', value);
                }}
                onBlur={handleBlur('sendNotifications')}
                value={values.sendNotifications}
                title="Notifications"
                subTitle="I want my client to receive reminder notifications through Email for this appointment."
              />
            </FormGroup>
          </View>
        )}
      </Formik>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
});
export default EditBookingForm;
