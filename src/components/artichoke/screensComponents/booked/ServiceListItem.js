import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';

import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const ServiceListItem = (props) => {
  const serviceImage = props.item.serviceImage
    ? { uri: props.item.serviceImage }
    : bgImage;
  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => {
        props.setNewAppointmentService(props.item);
      }}
    >
      <View style={styles.leftBox}>
        <View style={styles.serviceImageContainer}>
          <Text style={styles.servicePrice}>
            {props.symbol}
            {parseFloat(props.item.ProductDuration[0].price).toFixed(2)}
          </Text>
        </View>
      </View>
      <View style={styles.centerBox}>
        <Text style={styles.serviceName} numberOfLines={1}>
          {props.item.name}
        </Text>
        <Text style={styles.serviceDuration}>
          {`${props.item.ProductDuration[0].duration.duration} min`}
        </Text>
      </View>
      <View style={styles.rightBox}>
        <IconFeader name="chevron-right" size={30} color={colors.subGrey} />
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    flex: 1,
    flexDirection: 'row',
    height: 80,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  leftBox: {
    width: 60,
    marginRight: 20,
  },
  rightBox: {
    marginLeft: 'auto',
  },
  centerBox: {
    display: 'flex',
    width: '90%',
    flex: 1,
  },
  serviceName: {
    ...generalStyles.fontBold,
    width: '100%',
    color: colors.black,
  },
  serviceDuration: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  serviceImageContainer: {
    width: 60,
    height: 37,
    backgroundColor: colors.goodGreen,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  servicePrice: {
    ...generalStyles.priceText,
    color: colors.white,
  },
});
export default ServiceListItem;
