import React from 'react';
import {
  View, StyleSheet, Text, Image,
} from 'react-native';
import { colors } from '../../../../styles';

const EmptyBookedList = (props) => (
  <View style={styles.container}>
    {props.hasIcon ? (
      <Image source={props.hasIcon} style={styles.image} />
    ) : null}
    <Text style={styles.text}>{props.message}</Text>
  </View>
);
const styles = StyleSheet.create({
  container: {
    marginTop: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    borderColor: colors.veryLightBlue,
  },
  text: {
    color: colors.black,
    fontSize: 22,
    textAlign: 'center',
    fontFamily: 'Avenir-Roman',
    maxWidth: 272,
  },
  image: {
    marginBottom: 10,
  },
});
export default EmptyBookedList;
