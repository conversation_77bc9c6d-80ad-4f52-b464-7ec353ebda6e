import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';
import { generalStyles } from '../../../../styles/generalStyle';
import { curvedScale } from '../../../../util/responsive';

const AddClientItem = (props) => (
  <TouchableOpacity
    activeOpacity={1}
    onPress={() => {
      props.navigation.navigate(APPLICATION_ROUTES.APP_CREATE_CLIENT);
    }}
  >
    <View style={styles.item}>
      <View style={styles.leftBox}>
        <View style={styles.userImageContainer}>
          <Text>
            <IconFeader name="user" size={28} color={colors.subGrey} />
          </Text>
        </View>
      </View>
      <View style={styles.centerBox}>
        <Text style={styles.text}>Select Client(s)</Text>
        <IconFeader
          style={{ flex: 0.5, textAlign: 'right', paddingRight: 11 }}
          name="chevron-down"
          size={25}
          color={colors.subGrey}
        />
      </View>
    </View>
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  item: {
    marginVertical: 8,
    flex: 1,
    flexDirection: 'row',
    height: 83,
    borderWidth: 1,
    borderColor: colors.lightgrey,
    borderRadius: 3,
  },
  leftBox: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingLeft: curvedScale(10),
  },
  centerBox: {
    flex: curvedScale(4.5),
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    flex: 1,
    ...generalStyles.fontBold,
    paddingLeft: 5,
    color: colors.black,
  },
  userImageContainer: {
    width: 56,
    height: 56,
    backgroundColor: colors.veryLightBlue,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default AddClientItem;
