import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import <PERSON><PERSON><PERSON>eader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';
import { generalStyles } from '../../../../styles/generalStyle';
import { curvedScale } from '../../../../util/responsive';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const BookingClientItem = (props) => {
  let userImg = null;
  if (props.clients[0].user) {
    userImg = { uri: props.clients[0].user.avatarUrl };
  } else {
    userImg = { uri: props.clients[0].Client.user.avatarUrl };
  }

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={() => {
        props.navigation.navigate({
          name: APPLICATION_ROUTES.APP_CREATE_CLIENT,
          params: { returnRoute: props.returnRoute },
        });
      }}
    >
      <View style={{ ...styles.item, ...props.formStyle }}>
        <View style={styles.leftBox}>
          <Image
            source={userImg && userImg.uri ? userImg : bgImage}
            style={styles.userImage}
          />
        </View>
        <View style={styles.centerBox}>
          <Text style={styles.text}>
            {`${
              props.clients[0].user
                ? props.clients[0].user.firstName
                : props.clients[0].Client.user.firstName
            } ${
              props.clients[0].user
                ? props.clients[0].user.lastName
                : props.clients[0].Client.user.lastName
            }`}
          </Text>
          <IconFeader
            style={{ flex: 0.5, textAlign: 'right', paddingRight: 11 }}
            name="chevron-down"
            size={25}
            color={colors.subGrey}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    flex: 1,
    flexDirection: 'row',
    height: 85,
    borderWidth: 1,
    borderColor: colors.lightgrey,
  },
  leftBox: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingLeft: curvedScale(10),
  },
  centerBox: {
    flex: curvedScale(4.5),
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    flex: 1,
    ...generalStyles.fontBold,
    color: colors.black,
  },
  userImage: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 25,
  },
});

export default BookingClientItem;
