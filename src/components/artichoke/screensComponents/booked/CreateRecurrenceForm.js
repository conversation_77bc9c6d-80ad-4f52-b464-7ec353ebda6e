import React, { useState, createRef } from 'react';
import { Formik } from 'formik';
import {
  View, StyleSheet, ScrollView, Text,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment from 'moment';
import CustomActionSheet from '../../common/CustomActionSheet';
import FormGroup from '../../common/FormGroup';
import FormFrequency from '../../common/FormFrequency';
import { FREQUENCY } from '../../../../constants';
import RecurrenceEndsOnDateInput from '../../common/RecurrenceEndsOnDateInput';
import FormSimpleTextInput from '../../common/FormSimpleTextInput';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const repeatActionSheetRef = createRef();

const CreateRecurrenceForm = (props) => {
  const selDate = props.untilDay
    ? new Date(moment(props.untilDay, 'L'))
    : new Date();
  const [radio, setRadio] = useState(0);
  const [date, setDate] = useState(selDate);
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);
  const frequencyTypes = props.repeatTypes ? props.repeatTypes : FREQUENCY;

  const radio_props = [
    { label: 'On', value: 0 },
    { label: 'After', value: 1 },
  ];

  const onSelectDate = (selectedDate) => {
    setShow(false);
    setDate(selectedDate);
    props.onChangeRepeatUntilDate(moment(selectedDate));
    props.onChangeRepeatUntilType('DAY');
  };

  const showDatepicker = () => {
    setShow(true);
    setMode('date');
  };

  const setRadioButton = (value) => {
    setRadio(value);
    props.onChangeRepeatUntilDate(null);
    props.onChangeRepeatUntilType(null);
    if (value === 1) {
      props.onChangeRepeatCount(1);
    } else {
      props.onChangeRepeatCount(null);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Formik onSubmit={(values) => props.onSubmitForm(values)}>
        {({ handleBlur }) => (
          <View style={styles.form}>
            <FormGroup formStyle={styles.formStyle}>
              <Text style={styles.label}>Frequency</Text>
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <FormFrequency
                formInputStyle={{ marginTop: 20 }}
                label="Repeat"
                onPressFunc={() => repeatActionSheetRef.current?.setModalVisible()}
                value={frequencyTypes.find(
                  (repeatTypeLabel) => repeatTypeLabel.value === props.repeatIntervalType,
                )}
              />
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <Text style={styles.label}>Ends</Text>
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <RadioForm>
                {radio_props.map((obj, i) => (
                  <RadioButton
                    labelHorizontal
                    key={i}
                    style={{
                      paddingVertical: 10,
                      flex: 1,
                    }}
                  >
                    <RadioButtonInput
                      obj={obj}
                      index={i}
                      isSelected={obj.value === radio}
                      onPress={() => {
                        setRadioButton(obj.value);
                      }}
                      buttonStyle={{}}
                      buttonWrapStyle={{ justifyContent: 'center' }}
                      borderWidth={1}
                      buttonSize={10}
                      buttonOuterSize={20}
                      buttonOuterColor={{ color: colors.subGrey }}
                    />
                    <RadioButtonLabel
                      obj={obj}
                      index={i}
                      labelHorizontal
                      onPress={() => {
                        setRadioButton(obj.value);
                      }}
                      labelStyle={{
                        fontSize: 17,
                        color: colors.black,
                        minWidth: 32,
                      }}
                      labelWrapStyle={{}}
                    />
                    {i === 0 ? (
                      <RecurrenceEndsOnDateInput
                        selectedValue={
                          props.untilDay
                            ? moment(props.untilDay, 'MM/DD/YYYY').format('L')
                            : 'Select Date'
                        }
                        onPressFunc={showDatepicker}
                        radioButtonValue={radio}
                      />
                    ) : (
                      <View
                        style={{
                          flex: 1,
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}
                      >
                        <FormSimpleTextInput
                          formInputStyle={{
                            width: '20%',
                            marginLeft: 15,
                            height: 40,
                          }}
                          onChangeText={(value) => {
                            if (parseInt(value, 10) < 360 || !value) {
                              props.onChangeRepeatCount(value);
                              props.onChangeRepeatUntilType('COUNT');
                            }
                          }}
                          onBlur={handleBlur('count')}
                          placeholder="1"
                          value={props.count ? props.count.toString() : null}
                          keyboardType="numeric"
                          editable={radio === 1}
                        />
                        <Text
                          style={{
                            marginLeft: 15,
                            fontSize: 17,
                            color: colors.black,
                            minWidth: 90,
                          }}
                        >
                          occurrence
                        </Text>
                      </View>
                    )}
                  </RadioButton>
                ))}
              </RadioForm>
              <View>
                <DateTimePickerModal
                  date={date}
                  mode={mode}
                  isVisible={show}
                  is24Hour
                  display="default"
                  onConfirm={onSelectDate}
                  onCancel={() => setShow(false)}
                  minimumDate={props.minimumDate || new Date()}
                />
              </View>
            </FormGroup>
          </View>
        )}
      </Formik>
      <CustomActionSheet actionSheetRef={repeatActionSheetRef} title="Repeat">
        <RadioForm>
          {frequencyTypes.map((obj, i) => (
            <RadioButton
              labelHorizontal
              key={i}
              style={{
                paddingHorizontal: 30,
                paddingVertical: 20,
                flex: 1,
                borderBottomWidth: 1,
                borderBottomColor: colors.lightgrey,
              }}
            >
              {/*  You can set RadioButtonLabel before RadioButtonInput */}
              <RadioButtonInput
                obj={obj}
                index={i}
                isSelected={props.repeatIntervalType === obj.value}
                onPress={() => {
                  props.onChangeRepeatIntervalType(obj.value);
                  repeatActionSheetRef.current?.setModalVisible(false);
                }}
                borderWidth={1}
                buttonSize={10}
                buttonOuterSize={20}
                buttonStyle={{ borderColor: colors.subGrey }}
                buttonWrapStyle={{ marginLeft: 73 }}
              />
              <RadioButtonLabel
                obj={obj}
                index={i}
                labelHorizontal
                onPress={() => {
                  props.onChangeRepeatIntervalType(obj.value);
                  repeatActionSheetRef.current?.setModalVisible(false);
                }}
                labelStyle={{ ...generalStyles.fontBold, color: colors.black }}
                labelWrapStyle={{}}
              />
            </RadioButton>
          ))}
        </RadioForm>
      </CustomActionSheet>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
    marginHorizontal: 10,
  },
  label: {
    width: '100%',
    color: colors.black,
    ...generalStyles.avenirRoman22,
    lineHeight: 30,
  },
});
export default CreateRecurrenceForm;
