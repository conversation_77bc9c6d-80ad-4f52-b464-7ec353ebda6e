import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const DiscountListItem = (props) => {
  const checkboxStyle = props.isSelected
    ? styles.selectedCheckbox
    : styles.unselectedCheckbox;
  return props.item.id === 0 ? (
    <View style={styles.item} key={props.item.id}>
      <View style={styles.leftBox}>
        <Text style={styles.name}>{props.item.name}</Text>
      </View>
      <View style={styles.rightBox}>
        <TouchableOpacity
          style={checkboxStyle}
          activeOpacity={1}
          onPress={() => (props.isSelected
            ? props.unselectItem(props.item)
            : props.selectItem(props.item))}
        >
          {props.isSelected ? (
            <IconFeader
              name="check"
              size={20}
              color={colors.white}
              style={{ paddingTop: 2 }}
            />
          ) : (
            <Text />
          )}
        </TouchableOpacity>
      </View>
    </View>
  ) : (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.onPress(props.item)}
    >
      <View style={styles.leftBox}>
        <Text style={styles.name}>
          {`${
            props.item.type === 'PERCENTAGE' ? '' : props.symbol
          }${props.item.amount}${
            props.item.type === 'PERCENTAGE' ? '%' : ''
          } off - ${props.item.name}`}
        </Text>
      </View>
      <View style={styles.rightBox}>
        <TouchableOpacity
          style={checkboxStyle}
          activeOpacity={1}
          onPress={() => (props.isSelected
            ? props.unselectItem(props.item)
            : props.selectItem(props.item))}
        >
          {props.isSelected ? (
            <IconFeader name="check" size={20} color={colors.white} />
          ) : (
            <Text />
          )}
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    paddingHorizontal: 20,
    flex: 1,
    flexDirection: 'row',
    height: 61,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  leftBox: {
    flex: 3,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  rightBox: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  name: {
    ...generalStyles.avenirMediun17,
    color: colors.black,
  },
  checkbox: {
    borderRadius: 10,
  },
  selectedCheckbox: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.goodGreen,
    width: 26,
    height: 26,
    borderRadius: 13,
  },
  unselectedCheckbox: {
    width: 26,
    height: 26,
    borderRadius: 13,
    borderWidth: 2,
    borderColor: colors.subGrey,
  },
});
export default DiscountListItem;
