import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');
const defaultProfile = require('../../../../resources/defaultProfile.png');

const ClientListItem = (props) => {
  const checkboxStyle = props.isSelected
    ? styles.selectedCheckbox
    : styles.unselectedCheckbox;
  return (
    <View style={styles.item} key={props.item.id}>
      <View style={styles.leftBox}>
        <View style={styles.userImageContainer}>
          <Image
            source={
              props.item.user.avatarUrl
                ? { uri: props.item.user.avatarUrl }
                : defaultProfile
            }
            style={styles.userImage}
          />
        </View>
      </View>
      <View style={styles.centerBox}>
        <Text style={styles.name}>{props.item.user.firstName}</Text>
      </View>
      <View style={styles.rightBox}>
        <TouchableOpacity
          style={checkboxStyle}
          activeOpacity={1}
          onPress={() => (props.isSelected
            ? props.unselectItem(props.item)
            : props.selectItem(props.item))}
        >
          {props.isSelected ? (
            <IconFeader name="check" size={20} color={colors.white} />
          ) : (
            <Text />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    flex: 1,
    flexDirection: 'row',
    height: 80,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  leftBox: {
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  centerBox: {
    paddingLeft: 10,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  rightBox: {
    marginLeft: 'auto',
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  name: {
    ...generalStyles.avenirHeavy17,
    color: colors.black,
  },
  userImage: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 25,
  },
  checkbox: {
    borderRadius: 10,
  },
  selectedCheckbox: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.goodGreen,
    width: 26,
    height: 26,
    borderRadius: 1,
  },
  unselectedCheckbox: {
    width: 26,
    height: 26,
    borderRadius: 1,
    borderWidth: 2,
    borderColor: colors.subGrey,
  },
});
export default ClientListItem;
