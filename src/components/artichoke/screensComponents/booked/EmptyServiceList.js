import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { colors } from '../../../../styles';

const EmptyServiceList = (props) => (
  <View style={styles.container}>
    <Text style={styles.text}>{props.message}</Text>
  </View>
);
const styles = StyleSheet.create({
  container: {
    marginTop: 250,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  text: {
    color: colors.black,
    fontSize: 20,
  },
});
export default EmptyServiceList;
