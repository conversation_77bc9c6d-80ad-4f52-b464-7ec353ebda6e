import React, { useState } from 'react';
import { Formik } from 'formik';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  Alert,
  Platform,
} from 'react-native';
import _cloneDeep from 'lodash.clonedeep';
import FormTextInput from '../../common/FormTextInput';
import FormGroup from '../../common/FormGroup';
import FormTextInputUnderline from '../../common/FormTextInputUnderline';
import FormMultilineTextInput from '../../common/FormMultilineTextInput';
import FormSwitchInput from '../../common/FormSwitchInput';
import FormSubmitButton from '../../common/FormSubmitButton';
import FormLocations from '../../common/FormLocations';
import { APPLICATION_ROUTES } from '../../../../constants';
import {
  createEditServicesFormValidations,
  locationsValidation,
} from '../../../../util/serviceValidations';
import { colors } from '../../../../styles';
import LoadingSpinner from '../../../LoadingSpinner';
import MediaUploaderArtichoke from '../../common/MediaUploaderArtichoke';
import { removeEmojis } from '../../../../util/utils';

const CreateServiceForm = (props) => {
  const removeImage = () => {
    props.setUploadedServiceImageAction(null);
  };

  const renderUploadComponent = () => (
    <MediaUploaderArtichoke
      navigation={props.navigation}
      onUploadCompleted={(res) => {
        props.setUploadedServiceImageAction(res.data);
      }}
      // initialVideoUrl={props.initialValues.video_url}
      initialImageUrl={props.initialValues.serviceImage}
      onMediaDeleted={() => removeImage()}
    />
  );

  let locations = _cloneDeep(props.selectedLocations);
  if (props.selectedExtraLocations.length < 1) {
    if (props.initialValues.offeredOnline) {
      locations.push({ id: 0, addressName: 'Remote' });
    } else if (props.initialValues.clientLocationEnabled) {
      locations.push({ id: 1, addressName: "Client's location" });
    } else if (props.initialValues.inappEnabled) {
      locations.push({ id: 2, addressName: 'Video Call' });
    }
  } else {
    locations = props.selectedLocations.concat([
      ...new Set(props.selectedExtraLocations),
    ]);
  }

  const disableSubmitButton = (values) => {
    let disable = true;
    if (
      locations.length > 0
      && values.name
      && values.duration
      && values.maxParticipants
      && values.description
    ) {
      disable = false;
    }
    return disable;
  };

  /* eslint-disable no-nested-ternary */

  const reduceTwoDecDigit = (digit) => {
    let value = '0.00';
    if (
      digit
      && digit !== ''
      && digit !== '0'
      && !Number.isNaN(parseInt(digit.replace('.', ''), 10))
    ) {
      const digitInt = parseInt(digit.replace('.', ''), 10).toString();
      value = digitInt.length > 3
        ? `${parseInt(
          digitInt.substr(0, digitInt.length - 2),
          10,
        )}.${digitInt.substr(digitInt.length - 2)}`
        : digitInt.length > 2
          ? `${digitInt[0]}.${digitInt.substr(digitInt.length - 2)}`
          : digitInt.length > 1
            ? `0.${digitInt}`
            : `0.0${digitInt}`;
    }
    return value;
  };

  const defaultDigitValue = (digit) => (!digit || digit === '' || digit === '0' ? '0.00' : digit);
  const [validateOnMount, setValidateOnMount] = useState(true);

  return (
    <View style={styles.container}>
      <ScrollView style={StyleSheet.absoluteFill}>
        {renderUploadComponent()}
        <Formik
          validateOnMount={validateOnMount}
          initialValues={props.initialValues}
          onSubmit={(values) => {
            if (values.price > 0 && values.price < 1) {
              Alert.alert(
                'Error',
                'A minimum price of $1 must be set for all items you plan to sell. If you’d like to make this item free you can set the price to $0',
              );
            } else {
              props.onSubmitForm(values);
            }
          }}
          validationSchema={createEditServicesFormValidations}
        >
          {({
            handleChange,
            handleBlur,
            setFieldValue,
            handleSubmit,
            values,
            errors,
          }) => (
            <View style={styles.form}>
              <FormGroup>
                <FormTextInputUnderline
                  onChangeText={handleChange('name')}
                  onBlur={handleBlur('name')}
                  label="Service Name"
                  placeholder="Service Name"
                  keyboardType={
                    Platform.OS === 'ios' ? 'ascii-capable' : 'visible-password'
                  }
                  name="name"
                  errors={errors}
                  value={values.name}
                />
              </FormGroup>
              <FormGroup>
                <FormLocations
                  label="Locations"
                  nLocations={locations.length}
                  onPressFunc={() => {
                    setValidateOnMount(false);
                    props.navigation.navigate({
                      name: APPLICATION_ROUTES.LOCATIONS,
                      params: {
                        previousScreen: 'create-service',
                      },
                    });
                  }}
                />
                {locationsValidation(
                  props.selectedLocations.concat(props.selectedExtraLocations),
                ) && (
                  <Text style={styles.error}>
                    {locationsValidation(
                      props.selectedLocations.concat(
                        props.selectedExtraLocations,
                      ),
                    )}
                  </Text>
                )}
              </FormGroup>
              <FormGroup>
                <FormTextInput
                  onChangeText={handleChange('duration')}
                  onBlur={handleBlur('duration')}
                  label="Duration"
                  placeholder="Min"
                  name="duration"
                  value={values.duration}
                  textAfterInput=" Min"
                  keyboardType="numeric"
                  txtWidth={
                    values.duration?.toString().length > 0
                      ? values.duration?.toString().length * 10
                      : 36
                  }
                  maxLength={3}
                />
                {errors.duration && (
                  <Text style={styles.error}>{errors.duration}</Text>
                )}
              </FormGroup>
              <FormGroup formStyle={{ flexDirection: 'row', flex: 1 }}>
                <View style={{ flex: 1, width: '50%', marginRight: '2%' }}>
                  <FormTextInput
                    onChangeText={(value) => {
                      setFieldValue('price', reduceTwoDecDigit(value));
                      handleChange('price');
                    }}
                    onBlur={handleBlur('price')}
                    label="Price"
                    placeholder="0.00"
                    textBeforeInput={values.price && props.symbol}
                    name="price"
                    maxLength={7}
                    value={defaultDigitValue(values.price)}
                    keyboardType="numeric"
                    txtWidth={
                      values.price?.toString().length > 0
                        ? values.price?.toString().length * 10
                        : 36
                    }
                  />
                  {errors.price && (
                    <Text style={styles.error}>{errors.price}</Text>
                  )}
                </View>
                <View style={{ flex: 1, width: '50%', marginLeft: '2%' }}>
                  <FormTextInput
                    onChangeText={(value) => {
                      setFieldValue('tax', reduceTwoDecDigit(value));
                      handleChange('tax');
                    }}
                    onBlur={handleBlur('tax')}
                    label="Tax Rate"
                    placeholder="Sales Tax"
                    name="tax"
                    maxLength={5}
                    value={defaultDigitValue(values.tax)}
                    textAfterInput="%"
                    keyboardType="numeric"
                    txtWidth={
                      values.tax?.toString().length > 2
                        ? values.tax?.toString().length * 10
                        : 36
                    }
                  />
                  {errors.tax && <Text style={styles.error}>{errors.tax}</Text>}
                </View>
              </FormGroup>
              <FormGroup>
                <FormTextInput
                  onChangeText={handleChange('maxParticipants')}
                  onBlur={handleBlur('maxParticipants')}
                  label="Max Participants"
                  placeholder="1"
                  name="maxParticipants"
                  value={values.maxParticipants}
                  keyboardType="numeric"
                />
              </FormGroup>
              <FormGroup>
                <FormMultilineTextInput
                  style={{ color: colors.black }}
                  onChangeText={handleChange('description')}
                  onBlur={handleBlur('description')}
                  placeholder="Description"
                  name="description"
                  value={removeEmojis(values.description)}
                  maxLength={360}
                />
                {errors.description && (
                  <Text style={styles.error}>{errors.description}</Text>
                )}
              </FormGroup>
              <FormGroup>
                <FormSwitchInput
                  onValueChange={(value) => setFieldValue('bookOnlineEnabled', value)}
                  label="Clients can book this service"
                  value={values.bookOnlineEnabled}
                />
              </FormGroup>
              <FormGroup formStyle={styles.bottomSubmitButton}>
                <FormSubmitButton
                  title="Create Service"
                  disabled={disableSubmitButton(values)}
                  buttonStyle={
                    disableSubmitButton(values)
                      ? styles.submitButtonDisable
                      : styles.submitButtonEnable
                  }
                  buttonLabelStyle={
                    disableSubmitButton(values)
                      ? { color: colors.subGrey }
                      : { color: colors.white }
                  }
                  onPressButton={
                    disableSubmitButton(values) ? null : handleSubmit
                  }
                />
              </FormGroup>
            </View>
          )}
        </Formik>
      </ScrollView>

      {props.saveServiceLoading && (
        <View style={styles.loadingContainer}>
          <LoadingSpinner visible size="large" color={colors.black} />
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.transparentGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
  },
  bottomSubmitButton: {
    display: 'flex',
    flex: 1,
    borderRadius: 30,
    marginTop: 20,
  },
  submitButtonDisable: {
    backgroundColor: colors.lightgrey,
    borderColor: colors.lightgrey,
    marginBottom: 40,
  },
  submitButtonEnable: {
    backgroundColor: colors.duskBlue,
    borderColor: colors.duskBlue,
    marginBottom: 40,
  },
  error: {
    display: 'flex',
    width: '100%',
    fontSize: 10,
    color: colors.nasmRed,
  },
});
export default CreateServiceForm;
