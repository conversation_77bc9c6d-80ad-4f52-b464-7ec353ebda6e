import React from 'react';
import {
  View, StyleSheet, Text, Image,
} from 'react-native';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const ViewPaymentClientItem = (props) => (
  <View style={styles.contentUser}>
    <Image
      source={
          props.clientInfo.user.avatarUrl
            ? { uri: props.clientInfo.user.avatarUrl }
            : bgImage
        }
      style={styles.userImage}
    />
    <View style={styles.userDetails}>
      <Text style={styles.userName}>
        {props.clientInfo.user.firstName}
        {' '}
      </Text>
      <Text style={styles.productName}>{props.product.name}</Text>
    </View>
  </View>
);
const styles = StyleSheet.create({
  userImage: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userDetails: {
    flex: 6,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  productName: {
    display: 'flex',
    ...generalStyles.avenirRoman13,
    lineHeight: 22,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.avenirHeavy14,
    color: colors.black,
  },
});
export default ViewPaymentClientItem;
