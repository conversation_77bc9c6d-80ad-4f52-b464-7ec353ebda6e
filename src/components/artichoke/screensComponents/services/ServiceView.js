/* eslint-disable react/no-did-update-set-state */
import _cloneDeep from 'lodash.clonedeep';
import React, { Component } from 'react';
import {
  ActivityIndicator,
  Image,
  ImageBackground,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import ContentLoading from '../../../ContentLoading';
import FormGroup from '../../common/FormGroup';
import FormLocationsView from '../../common/FormLocationsView';
import FormSubmitButton from '../../common/FormSubmitButton';

const bgImage = require('../../../../assets/imgServiceBackground.png');
const tempProfilePic = require('../../../../resources/defaultProfile.png');
const closeIcon = require('../../../../assets/closeCircle.png');

class ServiceView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      imageLoading: false,
      serviceImage: null,
      locations: null,
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.loader && !this.props.loader) {
      const servImage = this.props.item.serviceImage
        ? { uri: this.props.item.serviceImage }
        : bgImage;
      this.setState({ serviceImage: servImage });
      const locations = _cloneDeep(this.props.selectedLocations);
      if (this.props.item.offeredOnline) {
        locations.push({ id: 0, addressName: 'Remote' });
      } else if (this.props.item.inappEnabled) {
        locations.push({ id: 0, addressName: 'Video Call' });
      } else if (this.props.item.clientLocationEnabled) {
        locations.push({ id: 1, addressName: "Client's location" });
      }
      this.setState({ locations });
    }
  }

  render() {
    return (
      <View style={styles.container}>
        <ScrollView style={{ ...styles.item, paddingTop: 0 }}>
          <View style={styles.header}>
            <ImageBackground
              accessible={!!(this.state.serviceImage && this.props.item)}
              source={this.state.serviceImage}
              style={styles.image}
              onLoadEnd={() => {
                this.setState({ imageLoading: false });
              }}
              onLoadStart={() => {
                this.setState({ imageLoading: true });
              }}
            >
              <View style={generalStyles.overlayWithoutRadius} />
            </ImageBackground>

            <TouchableOpacity
              style={{
                marginTop: 50,
                marginLeft: 20,
                width: 35,
                height: 35,
              }}
              onPress={() => {
                this.props.navigation.goBack();
              }}
            >
              <Image source={closeIcon} style={{ width: 35, height: 35 }} />
            </TouchableOpacity>
            {this.props.loader
            || !this.state.locations
            || this.state.imageLoading ? (
              <ActivityIndicator
                style={{ marginTop: 50 }}
                animating
                color={colors.white}
                size="large"
                backgroundColor={colors.black}
              />
              ) : (
                <>
                  <View style={styles.headerName}>
                    <Text style={styles.title}>{this.props.item.name}</Text>
                  </View>
                  <View style={styles.headerTimePrice}>
                    <Text style={styles.headerTime}>
                      {`${this.props.item.ProductDuration[0].duration.duration} ${this.props.item.ProductDuration[0].duration.granularity.abbreviation}`}
                    </Text>
                    <View style={styles.headerPrice}>
                      <Text style={styles.headerPriceText}>
                        {this.props.symbol}
                        {parseFloat(
                          this.props.item.ProductDuration[0].price,
                        ).toFixed(2)}
                      </Text>
                    </View>
                  </View>
                </>
              )}
          </View>
          {this.props.loader || !this.state.locations ? (
            <ContentLoading visible />
          ) : (
            <View style={styles.content}>
              <View style={styles.contentUser}>
                <View style={styles.userImageContainer}>
                  <Image
                    source={
                      this.props.trainer.avatarUrl
                        ? { uri: this.props.trainer.avatarUrl }
                        : tempProfilePic
                    }
                    style={styles.userImage}
                  />
                </View>
                <View style={styles.userDetails}>
                  <Text style={styles.userName}>
                    {`${this.props.trainer.firstName} ${this.props.trainer.lastName}`}
                  </Text>
                  <Text style={styles.userJob}>Fitness Professional</Text>
                </View>
              </View>
              <View style={styles.serviceDescription}>
                <Text style={styles.description}>
                  {this.props.item.description}
                </Text>
              </View>
              <View style={styles.serviceLocations}>
                <Text style={styles.locationsLabel}>Locations Offering</Text>
                <FormLocationsView
                  label="Locations"
                  nLocations={this.state.locations.length}
                />
              </View>
            </View>
          )}
        </ScrollView>
        <View style={styles.bottomButtonsContainer}>
          {this.props.item?.active === 'true' ? (
            <>
              <FormGroup formStyle={styles.bottomSubmitButton}>
                <FormSubmitButton
                  title="Sell Service"
                  onPressButton={() => this.props.onSellItem(this.props.item)}
                />
              </FormGroup>
              <View style={styles.bottom}>
                <FormSubmitButton
                  buttonStyle={{
                    backgroundColor: colors.white,
                    borderWidth: 2,
                    borderColor: colors.bordergrey,
                    marginRight: 10,

                    flex: 1,
                  }}
                  buttonLabelStyle={{ color: colors.subGrey }}
                  title="Edit"
                  onPressButton={() => this.props.onGoToEditScreen(this.props.item)}
                />
                <FormSubmitButton
                  buttonStyle={{
                    backgroundColor: colors.white,
                    borderWidth: 2,
                    borderColor: colors.bordergrey,
                    marginLeft: 10,
                    flex: 1,
                  }}
                  buttonLabelStyle={{ color: colors.subGrey }}
                  title="Archive"
                  onPressButton={() => this.props.onArchiveService(this.props.item)}
                />
              </View>
            </>
          ) : (
            <FormGroup formStyle={styles.bottomSubmitOnlyEditButton}>
              <FormSubmitButton
                buttonStyle={{
                  backgroundColor: colors.white,
                  borderWidth: 2,
                  borderColor: colors.bordergrey,
                }}
                buttonLabelStyle={{ color: colors.subGrey }}
                title="Restore"
                onPressButton={() => this.props.onRestoreService(this.props.item)}
              />
            </FormGroup>
          )}
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  item: {
    flex: 1,
    flexDirection: 'column',
    width: '100%',
  },
  header: {
    display: 'flex',
    flexDirection: 'column',
    height: 296,
    backgroundColor: colors.black,
  },
  headerName: {
    marginTop: 50,
    width: '100%',
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  headerTimePrice: {
    marginTop: 50,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTime: {
    flex: 4,
    alignItems: 'center',
    color: colors.white,
    ...generalStyles.durationText,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  headerPrice: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.goodGreen,
    borderRadius: 30,
    height: 38,
  },
  headerPriceText: {
    color: colors.white,
    ...generalStyles.durationText,
  },
  content: {
    flex: 1,
    flexDirection: 'column',
  },
  contentUser: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'flex-start',
  },

  userImageContainer: {
    justifyContent: 'center',
    marginRight: 15,
  },
  userImage: {
    width: 50,
    height: 50,
    borderColor: colors.white,
    borderRadius: 25,
  },
  userDetails: {
    flex: 5,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  serviceDescription: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  description: {
    fontSize: 16,
    ...generalStyles.avenirRoman17,
    color: colors.black,
  },
  serviceLocations: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    marginBottom: 20,
  },
  locationsLabel: {
    ...generalStyles.fontBold,
    marginBottom: 10,
    color: colors.black,
  },
  title: {
    ...generalStyles.titleService,
    color: colors.white,
    marginTop: 10,
  },
  bottomButtonsContainer: {
    flex: 0.25,
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 30,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 30,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    bottom: 89,
  },
  bottomSubmitOnlyEditButton: {
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 30,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    bottom: 25,
  },
  bottom: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginHorizontal: 15,
    position: 'absolute',
    margin: 16,
    bottom: 1,
    right: 1,
  },
});
export default ServiceView;
