import React from 'react';
import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';
import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const PackageListItem = (props) => (
  <TouchableOpacity
    key={props.item.id}
    style={styles.item}
    onPress={() => {
      props.navigation.navigate(APPLICATION_ROUTES.VIEW_PACKAGE);
      props.getPackageByIdAction(props.item.id.toString());
    }}
  >
    <ImageBackground
      source={bgImage}
      style={styles.image}
      imageStyle={{ borderRadius: 22 }}
    />
    <View style={styles.topBox}>
      <View style={styles.topBoxLeft}>
        <Text style={generalStyles.priceText}>
          {props.symbol}
          {parseFloat(props.item.price)}
        </Text>
      </View>
    </View>

    <View style={styles.titleBox}>
      <View style={styles.topTitleBox}>
        <Text style={generalStyles.serviceNameText}>{props.item.name}</Text>
      </View>
      <View style={styles.actionBox}>
        <View style={{ ...styles.button, ...styles.rightButton }}>
          <TouchableOpacity onPress={() => props.onGoToEditScreen(props.item)}>
            <Text style={styles.buttonText}>Edit</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  image: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 20,
    flexDirection: 'column',
    minHeight: 120,
  },
  topBox: {
    display: 'flex',
    height: 50,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  titleBox: {
    display: 'flex',
    textAlign: 'center',
    flexDirection: 'row',
    marginHorizontal: 30,
    marginBottom: 20,
  },
  topBoxLeft: {
    backgroundColor: colors.goodGreen,
    color: colors.white,
    width: 80,
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    marginLeft: 1,
    borderTopLeftRadius: 20,
    borderBottomRightRadius: 5,
    ...generalStyles.priceText,
  },
  topTitleBox: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    width: 200,
  },
  actionBox: {
    flex: 0.5,
    flexDirection: 'row',
    display: 'flex',
  },
  button: {
    backgroundColor: 'transparent',
    flex: 1,
    display: 'flex',
  },
  buttonText: {
    color: colors.white,
    height: 36,
    textAlign: 'center',
    minWidth: 85,
    lineHeight: 32,
    borderColor: colors.white,
    borderWidth: 2,
    borderRadius: 18,
    margin: 'auto',
    ...generalStyles.avenirHeavy17,
  },
  rightButton: {
    alignItems: 'flex-end',
  },
});
export default PackageListItem;
