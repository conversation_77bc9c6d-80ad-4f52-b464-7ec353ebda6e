import React from 'react';
import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';
import { generalStyles } from '../../../../styles/generalStyle';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const ServiceListItem = (props) => {
  const serviceImage = props.item.serviceImage
    ? { uri: props.item.serviceImage }
    : bgImage;
  return (
    <TouchableOpacity
      key={props.item.id}
      style={styles.item}
      onPress={() => {
        props.navigation.navigate(APPLICATION_ROUTES.VIEW_SERVICE);
        props.getServiceByIdAction(props.item.id);
      }}
    >
      <ImageBackground
        source={serviceImage}
        style={styles.image}
        imageStyle={{ borderRadius: 22 }}
      >
        <View style={generalStyles.overlay} />
      </ImageBackground>
      <View style={styles.topBox}>
        <View style={styles.topBoxLeft}>
          <Text style={generalStyles.priceText}>
            {props.symbol}
            {parseFloat(props.item.ProductDuration[0].price)?.toFixed(2)}
          </Text>
        </View>
        <Text style={styles.topBoxRight}>
          {`${props.item.ProductDuration[0].duration.duration} ${props.item.ProductDuration[0].duration.granularity.abbreviation}`}
        </Text>
      </View>

      <View style={styles.titleBox}>
        <View style={styles.topTitleBox}>
          <Text style={styles.title}>{props.item.name}</Text>
        </View>
        <View style={styles.actionBox}>
          <View style={{ ...styles.button, ...styles.rightButton }}>
            <TouchableOpacity
              onPress={() => props.onGoToEditScreen(props.item)}
            >
              <Text style={styles.buttonText}>Edit</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  image: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 22,
    flexDirection: 'column',
    height: 201,
  },
  topBox: {
    flex: 3,
    flexDirection: 'row',
  },
  titleBox: {
    flex: 7,
    textAlign: 'center',
    flexDirection: 'column',
    marginHorizontal: 23,
    marginBottom: 20,
  },
  topBoxLeft: {
    flex: 2,
    backgroundColor: colors.goodGreen,
    color: colors.white,
    fontWeight: 'bold',
    width: 40,
    height: 37,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderBottomRightRadius: 5,
  },
  topBoxRight: {
    ...generalStyles.durationText,
    flex: 9,
    backgroundColor: 'transparent',
    color: colors.white,
    textAlign: 'right',
    paddingTop: 10,
    marginRight: 23,
  },
  topTitleBox: {
    flex: 2,
    alignSelf: 'flex-start',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 5,
  },
  title: {
    ...generalStyles.titleService,
    color: colors.white,
  },
  actionBox: {
    flex: 1,
    flexDirection: 'row',
    display: 'flex',
  },
  button: {
    backgroundColor: 'transparent',
    flex: 1,
    display: 'flex',
  },
  buttonText: {
    color: colors.white,
    height: 36,
    textAlign: 'center',
    width: 85,
    lineHeight: 32,
    borderColor: colors.white,
    borderWidth: 2,
    borderRadius: 18,
    margin: 'auto',
    ...generalStyles.avenirHeavy17,
  },
  rightButton: {
    alignItems: 'flex-end',
  },
});
export default ServiceListItem;
