import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles, locationStyles } from '../../../../styles/generalStyle';

const ServiceOfferingListItem = (props) => {
  const checkboxStyle = props.isSelected
    ? styles.selectedCheckbox
    : styles.unselectedCheckbox;
  return (
    <View style={styles.item} key={props.item.id}>
      <View style={styles.centerBox}>
        <Text style={styles.serviceName}>{props.item.name}</Text>
        <Text style={styles.serviceDuration}>
          {props.item.ProductDuration[0].duration.duration}
          {' '}
          min
        </Text>
      </View>
      <View style={styles.rightBox}>
        <TouchableOpacity
          style={checkboxStyle}
          activeOpacity={1}
          onPress={() => (props.isSelected
            ? props.unselectItem(props.item)
            : props.selectItem(props.item))}
        >
          {props.isSelected ? (
            <IconFeader name="check" size={20} color={colors.white} />
          ) : (
            <Text />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    flex: 1,
    flexDirection: 'row',
    height: 79,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  centerBox: {
    flex: 5,
    flexDirection: 'column',
    justifyContent: 'center',
    paddingLeft: 20,
  },
  rightBox: {
    flex: 1,
    paddingRight: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  serviceName: {
    ...generalStyles.fontBold,
    color: colors.black,
  },
  serviceDuration: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  checkbox: {
    borderRadius: 10,
  },
  selectedCheckbox: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.goodGreen,
    width: 26,
    height: 26,
    borderRadius: 1,
  },
  unselectedCheckbox: {
    width: 26,
    height: 26,
    borderRadius: 1,
    borderWidth: 2,
    borderColor: colors.subGrey,
  },
});
export default ServiceOfferingListItem;
