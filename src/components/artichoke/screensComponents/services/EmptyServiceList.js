import React from 'react';
import {
  View, StyleSheet, Text, Image,
} from 'react-native';
import { colors } from '../../../../styles';

const EmptyServiceList = (props) => (
  <View style={styles.container}>
    {props.hasIcon ? (
      <Image source={props.hasIcon} style={styles.image} />
    ) : null}
    <Text style={styles.text}>{props.message}</Text>
  </View>
);
const styles = StyleSheet.create({
  container: {
    minHeight: 100,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  text: {
    color: colors.black,
    fontSize: 20,
    fontSize: 22,
    textAlign: 'center',
    fontFamily: 'Avenir-Roman',
    maxWidth: 272,
  },
  image: {
    marginBottom: 10,
    marginTop: 24,
  },
});
export default EmptyServiceList;
