import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';
import AddButton from '../../common/AddButton';
import { generalStyles } from '../../../../styles/generalStyle';
import { curvedScale } from '../../../../util/responsive';

const AddServiceItem = (props) => (
  <View style={styles.item}>
    <AddButton
      small
      onPressAction={() => {
        props.navigation.navigate(APPLICATION_ROUTES.APP_CREATE_SERVICE, {
          returnRoute: props.returnRoute,
        });
      }}
    />
    <View style={{ marginTop: 10 }}>
      <Text style={styles.text}>Select Service</Text>
    </View>
  </View>
);
const styles = StyleSheet.create({
  item: {
    marginTop: 4,
    marginBottom: 20,
    borderRadius: 17,
    flex: 1,
    flexDirection: 'column',
    height: 166,
    borderWidth: 1,
    borderColor: colors.lightgrey,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    ...generalStyles.fontBold,
    color: colors.black,
    marginTop: curvedScale(15),
  },
});
export default AddServiceItem;
