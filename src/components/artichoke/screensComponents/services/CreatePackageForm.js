import React, { createRef, Fragment } from 'react';
import { Formik } from 'formik';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  Alert,
  Platform,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import _cloneDeep from 'lodash.clonedeep';
import CustomActionSheet from '../../common/CustomActionSheet';
import FormTextInput from '../../common/FormTextInput';
import FormGroup from '../../common/FormGroup';
import FormTextInputUnderline from '../../common/FormTextInputUnderline';
import FormMultilineTextInput from '../../common/FormMultilineTextInput';
import FormSimpleTextInput from '../../common/FormSimpleTextInput';
import FormSwitchInput from '../../common/FormSwitchInput';
import FormSubmitButton from '../../common/FormSubmitButton';
import FormServices from '../../common/FormServices';
import FormGeneral from '../../common/FormGeneral';
import { APPLICATION_ROUTES } from '../../../../constants';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import AddTooltipButton from '../../common/AddTooltipButton';
import LoadingSpinner from '../../../LoadingSpinner';
import { track } from '../../../../util/Analytics';
import { reduceTwoDecDigit, removeEmojis } from '../../../../util/utils';
import * as packageValidations from '../../../../util/packageValidations';

const taxRateActionSheetRef = createRef();
const paymentIntervalActionSheetRef = createRef();
const balanceExpireActionSheetRef = createRef();
const taxType = [
  { label: 'Calculated', value: 'CALCULATED' },
  { label: 'Flat Rate', value: 'FLATRATE' },
];
const paymentInterval = [
  { label: 'Once', value: 'ONETIME' },
  { label: 'Every Week', value: 'WEEKLY' },
  { label: 'Every Other Week', value: 'BIWEEKLY' },
  { label: 'Every Month', value: 'MONTHLY' },
  { label: 'Every Year', value: 'ANNUALLY' },
];
const balanceExpire = [
  { label: 'Never', value: 'NEVER' },
  { label: 'On Next Payment Date', value: 'PERIODEND' },
  { label: 'After 30 Days', value: 'CUSTOM-30' },
  { label: 'After 60 Days', value: 'CUSTOM-60' },
  { label: 'After 90 Days', value: 'CUSTOM-90' },
  { label: 'Custom Days', value: 'CUSTOM' },
];

const CreatePackageForm = (props) => {
  const showTaxInfo = () => {
    Alert.alert(
      'Tax Rate',
      '\nCalculated: Tax calculations are made based on the tax rate of each individual service and prorated based on the total package price.\r\n\nFlat Rate: Apply a flat rate tax to the entire package. This might be the tax rate of the dominant service within the package but depends on local tax code.',
      [
        {
          text: 'Ok',
        },
      ],
      { cancelable: false },
    );
  };

  const showPaymentInfo = () => {
    Alert.alert(
      'Number of payments',
      "\nNever End/Unlimited Payments: Client's will continue to be charged until the subscription is cancelled \r\n\nPayment Count: Client will only be charged for the number of payments specified, unless canceled earlier. Must be a minimum of 2 payments, which include the initial purchase.",
      [
        {
          text: 'Ok',
        },
      ],
      { cancelable: false },
    );
  };

  const onChangeTaxRate = (value) => {
    if (value === 'CALCULATED') {
      const tax = props.calculateTax();
      props.setPackageTaxAction(tax);
    } else {
      props.setPackageTaxAction('0.00');
      props.setPackageTaxRateAction('0.00');
    }
    props.setTaxRateAction(value);
    taxRateActionSheetRef.current?.setModalVisible(false);
  };

  const onChangePaymentInterval = (value) => {
    props.setPaymentIntervalAction(value);
    props.setPaymentCountAction('1');
    paymentIntervalActionSheetRef.current?.setModalVisible(false);
  };

  const onChangeBalanceExpire = (value) => {
    if (
      value === 'CUSTOM-30'
      || value === 'CUSTOM-60'
      || value === 'CUSTOM-90'
    ) {
      const expireType = 'CUSTOM';
      const expireDaysCustom = parseInt(value.split('-').pop(), 10);
      props.setExpireTypeAction(expireType);
      props.setExpireDaysCustomAction(expireDaysCustom);
    } else {
      props.setExpireTypeAction(value);
      props.setExpireDaysCustomAction(null);
    }
    balanceExpireActionSheetRef.current?.setModalVisible(false);
  };

  const showTaxRateType = (value) => {
    let nameShown = 'Select Tax Rate';
    if (value === 'CALCULATED') {
      nameShown = 'Calculated';
    }
    if (value === 'FLATRATE') {
      nameShown = 'Flat Rate';
    }
    return nameShown;
  };

  const showPaymentIntervalValue = (value) => {
    switch (value) {
      case 'ONETIME':
        return 'Once';
      case 'WEEKLY':
        return 'Every Week';
      case 'BIWEEKLY':
        return 'Every Other Week';
      case 'MONTHLY':
        return 'Every Month';
      case 'ANNUALLY':
        return 'Every Year';
      default:
        return 'Select an Interval';
    }
  };

  const showBalanceExpireValue = (expireType, expireDaysCustom) => {
    if (expireType === 'NEVER') {
      return 'Never';
    }
    if (expireType === 'PERIODEND') {
      return 'On Next Payment Date';
    }
    if (expireType === 'CUSTOM' && expireDaysCustom === 30) {
      return 'After 30 Days';
    }
    if (expireType === 'CUSTOM' && expireDaysCustom === 60) {
      return 'After 60 Days';
    }
    if (expireType === 'CUSTOM' && expireDaysCustom === 90) {
      return 'After 90 Days';
    }
    if (expireType === 'CUSTOM') {
      return 'Custom Days';
    }
    return 'Select a Date';
  };

  const showIsSelectedBalanceExpire = (value, expireType, expireDaysCustom) => {
    if (value === expireType && expireType !== 'CUSTOM') {
      return true;
    }
    if (
      expireType === 'CUSTOM'
      && value === expireType
      && ![30, 60, 90].includes(expireDaysCustom)
    ) {
      return true;
    }
    if (
      expireType === 'CUSTOM'
      && value === `${expireType}-${expireDaysCustom}`
    ) {
      return true;
    }
    return false;
  };

  const setQuantityForSelectedService = (item, value) => {
    const services = _cloneDeep(props.newPackage.selectedServices);
    const newServices = services.map((serviceItem) => {
      if (serviceItem.productId === item.productId) {
        serviceItem.quantity = value;
      }
      return serviceItem;
    });
    props.setPackageServicesAction(newServices);
    calculateTotalValue(newServices);
  };

  const setUnlimitedForSelectedService = (item, value) => {
    const services = _cloneDeep(props.newPackage.selectedServices);
    const newServices = services.map((serviceItem) => {
      if (serviceItem.productId === item.productId) {
        serviceItem.unlimited = value;
        if (value) {
          serviceItem.quantity = 1;
        }
      }
      return serviceItem;
    });
    props.setPackageServicesAction(newServices);
    calculateTotalValue(newServices);
  };

  const calculateTotalValue = (services) => {
    let totalPrice = 0.0;
    services.map((serviceItem) => {
      totalPrice += serviceItem.price * serviceItem.quantity;
      return true;
    });
    props.setServicesTotalValueAction(totalPrice.toFixed(2));
    return totalPrice.toFixed(2);
  };

  const disableSubmitButton = (initialValues) => {
    let disable = true;
    if (
      initialValues.packageName !== null
      && initialValues.packageName !== ''
      && initialValues.selectedServices.length > 0
      && initialValues.price !== null
      && initialValues.price !== ''
    ) {
      disable = false;
    }
    return disable;
  };

  const buttonStyle = disableSubmitButton(props.newPackage)
    ? { backgroundColor: colors.lightgrey, borderColor: colors.subGrey }
    : { backgroundColor: colors.duskBlue, borderColor: colors.duskBlue };
  const buttonLabelStyle = disableSubmitButton(props.newPackage)
    ? { color: colors.subGrey }
    : { color: colors.white };

  const TaxRateSelect = React.memo(() => (
    <>
      <View style={{ flex: 9 }}>
        <FormGeneral
          valueStyle={
            props.newPackage.taxRate !== 'NOTAX'
              ? { color: colors.black }
              : { color: colors.subGrey }
          }
          label="Tax Rate"
          name="taxRate"
          value={showTaxRateType(props.newPackage.taxRate)}
          onPressFunc={() => {
            taxRateActionSheetRef.current?.setModalVisible();
          }}
        />
      </View>
      <View
        style={{
          flex: 1,
          marginLeft: '2%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <AddTooltipButton onPressAction={showTaxInfo} />
      </View>
    </>
  ));

  return (
    <View style={styles.container}>
      <ScrollView style={StyleSheet.absoluteFill}>
        <Formik
          validateOnMount
          initialValues={props.newPackage}
          onSubmit={(values) => {
            if (values.price > 0 && values.price < 1) {
              Alert.alert(
                'Error',
                'A minimum price of $1 must be set for all items you plan to sell. If you’d like to make this item free you can set the price to $0',
              );
            } else {
              props.onSubmitForm(values);
            }
          }}
        >
          {({
            handleChange,
            handleBlur,
            setFieldValue,
            handleSubmit,
            values,
          }) => (
            <View style={{ ...generalStyles.avenirMedium17 }}>
              <FormGroup>
                <FormTextInputUnderline
                  frameStyle={{ marginBottom: 0, minHeight: 0 }}
                  onChangeText={(value) => {
                    props.setPackageNameAction(value);
                    setFieldValue('name', value);
                    handleChange('name');
                  }}
                  value={values.name}
                  onBlur={handleBlur('name')}
                  label="Package Name"
                  keyboardType={
                    Platform.OS === 'ios' ? 'ascii-capable' : 'visible-password'
                  }
                  placeholder="Package Name"
                  name="name"
                  errors={packageValidations.nameValidation(
                    props.newPackage.packageName,
                  )}
                />
              </FormGroup>
              <FormGroup>
                <FormServices
                  style={{ ...generalStyles.avenirRoman14 }}
                  label="Services"
                  valueStyle={
                    props.newPackage.selectedServices.length
                      ? { color: colors.black }
                      : { color: colors.subGrey }
                  }
                  selectedServices={props.newPackage.selectedServices.length}
                  onPressFunc={() => {
                    props.navigation.navigate({
                      name: APPLICATION_ROUTES.VIEW_SERVICES_OFFERING,
                    });
                  }}
                />
                {packageValidations.servicesValidation(
                  props.newPackage.selectedServices,
                ) && (
                  <Text style={styles.error}>
                    {packageValidations.servicesValidation(
                      props.newPackage.selectedServices,
                    )}
                  </Text>
                )}
                {props.newPackage.selectedServices.length
                  ? props.newPackage.selectedServices.map((item, index) => (
                    <View key={item.id} style={{ height: 142 }}>
                      <FormGroup
                        formStyle={{
                          flexDirection: 'row',
                          flex: 1,
                          marginHorizontal: 0,
                        }}
                      >
                        <View style={{ flex: 4 }}>
                          <Text
                            style={{
                              ...generalStyles.avenirHeavy17,
                              color: colors.black,
                            }}
                          >
                            {item.productName}
                          </Text>
                          <Text
                            style={{
                              ...generalStyles.avenirRoman13,
                              color: colors.subGrey,
                            }}
                          >
                            {item.duration}
                            {' '}
                            min
                          </Text>
                        </View>
                        <View
                          style={{
                            flex: 1,
                            marginLeft: '2%',
                            alignItems: 'flex-end',
                            justifyContent: 'flex-start',
                            paddingTop: 10,
                          }}
                        >
                          <FormSimpleTextInput
                            editable={!item.unlimited}
                            formInputStyle={{ height: 42, width: 40 }}
                            fieldInputStyle={{ textAlign: 'center' }}
                            onChangeText={(value) => {
                              setQuantityForSelectedService(item, value);
                              setFieldValue(`quantity[${index}]`, value);
                            }}
                            onBlur={handleBlur(`quantity[${index}]`)}
                            placeholder="0"
                            name={`quantity[${index}]`}
                            value={item.quantity.toString()}
                          />
                        </View>
                      </FormGroup>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          borderBottomColor: colors.cloudyBlue,
                        }}
                      >
                        <FormSwitchInput
                          formInputStyle={{
                            paddingHorizontal: 0,
                            marginBottom: 11,
                          }}
                          labelInputStyle={{
                            ...generalStyles.avenirMedium14,
                          }}
                          onValueChange={(value) => {
                            setUnlimitedForSelectedService(item, value);
                            setFieldValue('unlimitedEnable', value);
                          }}
                          label="Unlimited Quantity"
                          value={item.unlimited}
                        />
                      </View>
                    </View>
                  ))
                  : null}
                <Text style={styles.totalValueText}>
                  Total Value:
                  {' '}
                  {props.symbol}
                  {props.newPackage.servicesTotalValue}
                </Text>
              </FormGroup>
              <FormGroup formStyle={styles.formStyle}>
                <Text style={styles.titleText}>Payment</Text>
                <FormTextInput
                  keyboardType="numeric"
                  onChangeText={(value) => {
                    handleChange('price');
                    setFieldValue('price', value);
                  }}
                  onBlur={() => {
                    props.setPackagePriceAction(values.price);
                    if (props.newPackage.taxRate === 'FLATRATE') {
                      props.onChangeFlatRateTax(values.taxPercentage);
                    }
                    handleBlur('price');
                  }}
                  label="Price"
                  placeholder="$0.00"
                  name="price"
                  maxLength={7}
                  value={reduceTwoDecDigit(values.price.toString())}
                  textBeforeInput={values.price !== '' ? props.symbol : ''}
                />
              </FormGroup>
              <FormGroup formStyle={{ flexDirection: 'row', flex: 1 }}>
                <TaxRateSelect />
              </FormGroup>
              {props.newPackage.taxRate !== 'NOTAX'
              && props.newPackage.taxRate === 'FLATRATE' ? (
                <View
                  style={{
                    ...generalStyles.avenirRoman14,
                    flex: 1,
                    marginHorizontal: 15,
                  }}
                >
                  <FormTextInput
                    keyboardType="numeric"
                    onChangeText={(value) => {
                      setFieldValue('taxPercentage', value);
                    }}
                    onBlur={() => {
                      props.onChangeFlatRateTax(values.taxPercentage);
                      handleBlur('tax');
                    }}
                    label={showTaxRateType(props.newPackage.taxPercentage)}
                    placeholder="0.00%"
                    name="tax"
                    maxLength={5}
                    value={reduceTwoDecDigit(values.taxPercentage?.toString())}
                    textAfterInput="%"
                  />
                  {packageValidations.flatRateValidation(
                    props.newPackage.taxPercentage,
                  ).tax && (
                    <Text style={styles.error}>
                      {
                        packageValidations.flatRateValidation(
                          props.newPackage.taxPercentage,
                        ).tax
                      }
                    </Text>
                  )}
                </View>
                ) : null}
              {props.newPackage.taxRate !== 'NOTAX'
              && props.newPackage.taxRate === 'CALCULATED' ? (
                <View
                  pointerEvents="none"
                  style={{
                    ...generalStyles.avenirMedium17,
                    flex: 1,
                    marginHorizontal: 15,
                  }}
                >
                  <FormTextInput
                    keyboardType="numeric"
                    onBlur={handleBlur('tax')}
                    label={showTaxRateType(props.newPackage.taxRate)}
                    placeholder="0.00"
                    name="tax"
                    value={props.newPackage.tax}
                    textBeforeInput={props.newPackage.tax ? props.symbol : ''}
                  />
                </View>
                ) : null}
              <CustomActionSheet
                actionSheetRef={taxRateActionSheetRef}
                title="Select a Tax Rate"
              >
                <RadioForm>
                  {taxType.map((obj, i) => (
                    <RadioButton
                      labelHorizontal
                      key={i}
                      style={{
                        paddingHorizontal: 30,
                        paddingVertical: 20,
                        flex: 1,
                        borderBottomWidth: 1,
                        borderBottomColor: colors.lightgrey,
                      }}
                    >
                      <RadioButtonInput
                        obj={obj}
                        index={i}
                        isSelected={
                          props.newPackage
                          && props.newPackage.taxRate
                          && props.newPackage.taxRate === obj.value
                        }
                        onPress={() => onChangeTaxRate(obj.value)}
                        buttonStyle={{}}
                        buttonWrapStyle={{ justifyContent: 'center' }}
                        borderWidth={1}
                        buttonSize={10}
                        buttonOuterSize={20}
                        buttonOuterColor={{ color: colors.subGrey }}
                      />
                      <RadioButtonLabel
                        obj={obj}
                        index={i}
                        labelHorizontal
                        onPress={() => onChangeTaxRate(obj.value)}
                        labelStyle={generalStyles.fontBold}
                        labelWrapStyle={{}}
                      />
                    </RadioButton>
                  ))}
                </RadioForm>
              </CustomActionSheet>
              <FormGroup>
                <Text style={styles.titleText}>Subscription Options</Text>
                <Text style={styles.infoText}>
                  Set the amount of time between billing intervals.
                </Text>
                <FormGeneral
                  formInputStyle={{ marginBottom: 30 }}
                  valueStyle={
                    props.newPackage.paymentInterval !== null
                      ? { color: colors.black }
                      : { color: colors.subGrey }
                  }
                  label="Payment Interval"
                  value={showPaymentIntervalValue(
                    props.newPackage.paymentInterval,
                  )}
                  onPressFunc={() => {
                    paymentIntervalActionSheetRef.current?.setModalVisible();
                  }}
                />
                <CustomActionSheet
                  actionSheetRef={paymentIntervalActionSheetRef}
                  title="Select a Payment Frequency"
                >
                  <RadioForm>
                    {paymentInterval.map((obj, i) => (
                      <RadioButton
                        labelHorizontal
                        key={i}
                        style={{
                          paddingHorizontal: 30,
                          paddingVertical: 20,
                          flex: 1,
                          borderBottomWidth: 1,
                          borderBottomColor: colors.lightgrey,
                        }}
                      >
                        <RadioButtonInput
                          obj={obj}
                          index={i}
                          isSelected={
                            props.newPackage
                            && props.newPackage.paymentInterval
                            && props.newPackage.paymentInterval === obj.value
                          }
                          onPress={() => onChangePaymentInterval(obj.value)}
                          buttonStyle={{}}
                          buttonWrapStyle={{ justifyContent: 'center' }}
                          borderWidth={1}
                          buttonSize={10}
                          buttonOuterSize={20}
                          buttonOuterColor={{ color: colors.subGrey }}
                        />
                        <RadioButtonLabel
                          obj={obj}
                          index={i}
                          labelHorizontal
                          onPress={() => onChangePaymentInterval(obj.value)}
                          labelStyle={generalStyles.fontBold}
                          labelWrapStyle={{}}
                        />
                      </RadioButton>
                    ))}
                  </RadioForm>
                </CustomActionSheet>
                {props.newPackage.paymentInterval === 'ONETIME' || (
                  <>
                    <View style={{ flexDirection: 'row', flex: 1 }}>
                      <View style={{ flex: 9 }}>
                        <Text style={styles.infoText}>
                          Set when or if the subscription ends.
                        </Text>
                      </View>
                      <View style={{ flex: 1, marginLeft: '2%' }}>
                        <AddTooltipButton onPressAction={showPaymentInfo} />
                      </View>
                    </View>
                    <FormSwitchInput
                      formInputStyle={{
                        paddingHorizontal: 0,
                        marginBottom: 11,
                      }}
                      labelInputStyle={{ ...generalStyles.avenirMedium14 }}
                      onValueChange={(value) => {
                        if (value) {
                          props.setPaymentCountAction(99999);
                        } else {
                          props.setPaymentCountAction(1);
                          setFieldValue('paymentCount', 1);
                        }
                      }}
                      label="Never End/Unlimited Payments"
                      value={props.newPackage.repeatCount === 99999}
                    />
                    {props.newPackage.repeatCount === 99999 || (
                      <FormTextInput
                        keyboardType="numeric"
                        formInputStyle={{ marginBottom: 30 }}
                        onChangeText={(value) => {
                          props.setPaymentCountAction(value);
                          setFieldValue('paymentCount', value);
                        }}
                        onBlur={handleBlur('paymentCount')}
                        label="Payment Count"
                        placeholder="# of Payments"
                        name="paymentCount"
                        value={props.newPackage.repeatCount.toString()}
                        txtWidth={20}
                      />
                    )}
                  </>
                )}
                <Text style={styles.infoText}>
                  Set the time in which the number of services acquired from
                  this package expire.
                </Text>

                <FormGeneral
                  valueStyle={
                    props.newPackage.expireType !== null
                      ? { color: colors.black }
                      : { color: colors.subGrey }
                  }
                  label="Balances Expire"
                  value={showBalanceExpireValue(
                    props.newPackage.expireType,
                    props.newPackage.expireDaysCustom,
                  )}
                  onPressFunc={() => {
                    balanceExpireActionSheetRef.current?.setModalVisible();
                  }}
                />
                {props.newPackage.expireType === 'CUSTOM'
                && ![30, 60, 90].includes(props.newPackage.expireDaysCustom) ? (
                  <View style={{ flex: 1, marginTop: 10 }}>
                    <FormTextInput
                      keyboardType="numeric"
                      onChangeText={(value) => {
                        props.setExpireDaysCustomAction(value);
                        setFieldValue('expireDaysCustom', value);
                      }}
                      onBlur={handleBlur('expireDaysCustom')}
                      label="Number of Days"
                      placeholder="# of days"
                      name="expireDaysCustom"
                    />
                  </View>
                  ) : null}
              </FormGroup>
              <CustomActionSheet
                actionSheetRef={balanceExpireActionSheetRef}
                title="Select When Balances Expire"
              >
                <RadioForm>
                  {balanceExpire.map((obj, i) => (
                    <RadioButton
                      labelHorizontal
                      key={i}
                      style={{
                        paddingHorizontal: 30,
                        paddingVertical: 20,
                        flex: 1,
                        borderBottomWidth: 1,
                        borderBottomColor: colors.lightgrey,
                      }}
                    >
                      <RadioButtonInput
                        obj={obj}
                        index={i}
                        isSelected={showIsSelectedBalanceExpire(
                          obj.value,
                          props.newPackage.expireType,
                          props.newPackage.expireDaysCustom,
                        )}
                        onPress={() => onChangeBalanceExpire(obj.value)}
                        buttonStyle={{}}
                        buttonWrapStyle={{ justifyContent: 'center' }}
                        borderWidth={1}
                        buttonSize={10}
                        buttonOuterSize={20}
                        buttonOuterColor={{ color: colors.subGrey }}
                      />
                      <RadioButtonLabel
                        obj={obj}
                        index={i}
                        labelHorizontal
                        onPress={() => onChangeBalanceExpire(obj.value)}
                        labelStyle={generalStyles.fontBold}
                        labelWrapStyle={{}}
                      />
                    </RadioButton>
                  ))}
                </RadioForm>
              </CustomActionSheet>
              <FormGroup>
                <FormMultilineTextInput
                  style={{ color: colors.black }}
                  onChangeText={(value) => {
                    props.setPackageDescriptionAction(value);
                    setFieldValue('description', value);
                  }}
                  onBlur={handleBlur('description')}
                  placeholder="Description"
                  name="description"
                  maxLength={360}
                  value={removeEmojis(values.description)}
                />
              </FormGroup>
              <FormGroup>
                <FormSwitchInput
                  onValueChange={(value) => {
                    props.setSellPackageOnlineEnableAction(value);
                    setFieldValue('bookOnlineEnabled', value);
                  }}
                  label="Clients can buy this package"
                  value={props.newPackage.sellOnlineEnabled}
                />
              </FormGroup>
              <FormGroup formStyle={styles.bottomSubmitButton}>
                <FormSubmitButton
                  title="Create Package"
                  disabled={disableSubmitButton(props.newPackage)}
                  buttonStyle={buttonStyle}
                  buttonLabelStyle={buttonLabelStyle}
                  onPressButton={() => {
                    handleSubmit();
                    track('package_created');
                  }}
                />
              </FormGroup>
            </View>
          )}
        </Formik>
      </ScrollView>
      {props.savePackageLoading && (
        <View style={styles.loadingContainer}>
          <LoadingSpinner visible size="large" color={colors.black} />
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.transparentGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    ...generalStyles.fontBold,
    height: 26,
    textAlignVertical: 'center',
    marginBottom: 12,
    color: colors.black,
  },
  infoText: {
    ...generalStyles.avenirMedium14,
    minHeight: 20,
    textAlignVertical: 'center',
    marginBottom: 4,
    color: colors.black,
  },
  bottomSubmitButton: {
    display: 'flex',
    flex: 1,
    borderRadius: 30,
    marginTop: 20,
  },
  error: {
    display: 'flex',
    width: '100%',
    fontSize: 10,
    color: colors.nasmRed,
  },
  formStyle: {
    marginBottom: 0,
    marginTop: 0,
  },
  totalValueText: {
    ...generalStyles.avenirMedium17,
    height: 50,
    textAlignVertical: 'center',
    marginTop: 8,
    textAlign: 'right',
    color: colors.black,
  },
});
export default CreatePackageForm;
