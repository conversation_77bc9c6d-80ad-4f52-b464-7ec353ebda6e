import React from 'react';
import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import { track } from '../../../../util/Analytics';

const bgImage = require('../../../../assets/imgServiceBackground.png');

const ArchivedServiceListItem = (props) => {
  const serviceImage = props.item.serviceImage
    ? { uri: props.item.serviceImage }
    : bgImage;
  return props.item.ProductDuration !== null
    && props.item.ProductDuration.length > 0 ? (
      <TouchableOpacity
        style={styles.item}
        key={props.item.id}
        onPress={() => props.onGoToViewServiceScreen(props.item)}
      >
        <ImageBackground
          source={serviceImage}
          style={styles.image}
          imageStyle={{ borderRadius: 20 }}
        >
          <View style={generalStyles.overlay} />
        </ImageBackground>
        <View style={styles.topBox}>
          <View style={styles.topBoxLeft}>
            <Text style={generalStyles.priceText}>
              {props.symbol}
              {Math.round(props.item.ProductDuration[0].price)}
            </Text>
          </View>
          <Text style={styles.topBoxRight}>
            {`${props.item.ProductDuration[0].duration.duration} ${props.item.ProductDuration[0].duration.granularity.abbreviation}`}
          </Text>
        </View>

        <View style={styles.titleBox}>
          <View style={styles.topTitleBox}>
            <Text style={styles.title}>{props.item.name}</Text>
          </View>
          <View style={styles.actionBox}>
            <TouchableOpacity
              style={{ ...styles.button, ...styles.rightButton }}
              onPress={() => {
                props.onRestoreService(props.item);
                track('service_restored');
              }}
            >
              <Text style={styles.buttonLabel}>Restore</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    ) : null;
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 20,
    flexDirection: 'column',
    height: 202,
  },
  topBox: {
    flex: 3,
    flexDirection: 'row',
  },
  titleBox: {
    flex: 7,
    textAlign: 'center',
    flexDirection: 'column',
    marginHorizontal: 23,
    marginBottom: 20,
  },
  topBoxLeft: {
    flex: 2,
    backgroundColor: colors.goodGreen,
    color: colors.white,
    fontWeight: 'bold',
    width: 40,
    height: 37,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderBottomRightRadius: 5,
  },
  topBoxRight: {
    ...generalStyles.durationText,
    flex: 9,
    backgroundColor: 'transparent',
    color: colors.white,
    textAlign: 'right',
    paddingTop: 10,
    marginRight: 23,
  },
  topTitleBox: {
    flex: 2,
    alignSelf: 'flex-start',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 5,
  },
  title: {
    ...generalStyles.titleService,
    color: colors.white,
  },
  actionBox: {
    flex: 1,
    flexDirection: 'row',
    display: 'flex',
  },
  button: {
    backgroundColor: 'transparent',
    flex: 1,
    display: 'flex',
  },
  buttonLabel: {
    color: colors.white,
    textAlign: 'center',
    margin: 'auto',
    height: 36,
    minWidth: 85,
    lineHeight: 32,
    borderColor: colors.white,
    borderWidth: 2,
    borderRadius: 18,
    ...generalStyles.avenirHeavy17,
    paddingHorizontal: 15,
  },
  leftButton: {
    marginRight: 'auto',
    alignItems: 'flex-start',
  },
  rightButton: {
    alignItems: 'flex-end',
  },
});
export default ArchivedServiceListItem;
