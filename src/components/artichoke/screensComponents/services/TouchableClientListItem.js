import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const defaultProfile = require('../../../../resources/defaultProfile.png');

const TouchableClientListItem = (props) => (
  <TouchableOpacity key={props.item.id} onPress={props.onPressSelectClient}>
    <View style={styles.item}>
      <View style={styles.leftBox}>
        <Image
          source={
            props.item.user.avatarUrl
              ? { uri: props.item.user.avatarUrl }
              : defaultProfile
          }
          style={styles.userImage}
        />
        <View style={styles.centerBox}>
          <Text style={styles.name}>{props.item.user.firstName}</Text>
        </View>
      </View>

      <View style={styles.rightBox}>
        <IconFeader
          style={{
            flex: 0.5,
            textAlign: 'right',
            paddingRight: 6,
            textAlignVertical: 'center',
          }}
          name="chevron-right"
          size={25}
          color={colors.subGrey}
        />
      </View>
    </View>
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  item: {
    width: '100%',
    borderRadius: 10,
    flex: 1,
    flexDirection: 'row',
    height: 80,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftBox: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  centerBox: {
    flex: 3,
    alignItems: 'flex-start',
    marginLeft: 20,
  },
  rightBox: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  name: {
    ...generalStyles.avenirHeavy17,
  },
  userImage: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 25,
  },
});
export default TouchableClientListItem;
