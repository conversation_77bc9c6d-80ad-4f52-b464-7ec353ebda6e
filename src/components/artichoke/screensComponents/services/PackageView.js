import React, { Component } from 'react';
import {
  Image,
  ImageBackground,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import FormGroup from '../../common/FormGroup';
import FormSubmitButton from '../../common/FormSubmitButton';

const bgImage = require('../../../../assets/imgServiceBackground.png');
const closeIcon = require('../../../../assets/closeCircle.png');

class PackageView extends Component {
  calculateTotalValue = (packageProducts) => {
    let totalPrice = 0.0;
    packageProducts.map((productItem) => {
      totalPrice += productItem.price * productItem.quantity;
      return null;
    });
    return totalPrice.toFixed(2);
  };

  showOfferedSessions = (packService) => {
    if (packService.unlimited) {
      return 'Unlimited';
    }

    return packService.quantity > 1
      ? `${packService.quantity} sessions`
      : `${packService.quantity} session`;
  };

  showPaymentExpireValue = (expireType, expireDays) => {
    if (expireType === 'NEVER') {
      return 'Never';
    }
    if (expireType === 'PERIODEND') {
      return 'On Next Payment Date';
    }
    if (expireType === 'CUSTOM') {
      return `After ${expireDays} Days`;
    }
    return null;
  };

  showPaymentIntervalValue = (value) => {
    switch (value) {
      case 'ONETIME':
        return 'Once';
      case 'WEEKLY':
        return 'Every Week';
      case 'BIWEEKLY':
        return 'Every Other Week';
      case 'MONTHLY':
        return 'Every Month';
      case 'ANNUALLY':
        return 'Every Year';
      default:
        return '';
    }
  };

  render() {
    const isPackageDisabled = this.props.item?.packageProducts?.length < 1;
    return (
      <View style={styles.container}>
        <ScrollView style={{ ...styles.item, paddingTop: 0 }}>
          <View style={styles.header}>
            <ImageBackground source={bgImage} style={styles.image}>
              <View style={generalStyles.overlayWithoutRadius} />
            </ImageBackground>

            <TouchableOpacity
              style={{
                marginTop: 50,
                marginLeft: 20,
                width: 35,
                height: 35,
              }}
              onPress={() => this.props.navigation.goBack()}
            >
              <Image source={closeIcon} style={{ width: 35, height: 35 }} />
            </TouchableOpacity>
            <View style={styles.headerName}>
              <Text style={styles.title}>{this.props.item.name}</Text>
            </View>
            <View style={styles.headerPrice}>
              <Text style={styles.headerPriceText}>
                {this.props.symbol}
                {Math.round(this.props.item.price)}
              </Text>
            </View>
            <Text style={styles.headerTotalValue}>
              Total Value
              {' '}
              {this.props.symbol}
              {this.calculateTotalValue(this.props.item.packageProducts)}
            </Text>
          </View>
          <View style={styles.content}>
            <View style={styles.contentUser}>
              <View style={styles.userImageContainer}>
                <Image
                  source={
                    this.props.trainer.avatarUrl
                      ? { uri: this.props.trainer.avatarUrl }
                      : bgImage
                  }
                  style={styles.userImage}
                />
              </View>
              <View style={styles.userDetails}>
                <Text style={styles.userName}>
                  {`${this.props.trainer.firstName} ${this.props.trainer.lastName}`}
                </Text>
                <Text style={styles.userJob}>Fitness Professional</Text>
              </View>
            </View>
            {this.props.item.packageDescription ? (
              <View style={styles.packageDescription}>
                <Text style={styles.description}>
                  {this.props.item.packageDescription}
                </Text>
              </View>
            ) : null}
            <View style={styles.packageServices}>
              <Text style={styles.servicesLabel}>Services</Text>
              {this.props.item.packageProducts.length
                ? this.props.item.packageProducts.map((packService) => (
                  <View key={packService.productId}>
                    <FormGroup formStyle={styles.packageServicesStyle}>
                      <View style={{ flex: 3 }}>
                        <Text
                          style={{
                            ...generalStyles.avenirRoman17,
                            color: colors.subGrey,
                          }}
                        >
                          {packService.productName}
                          {' '}
                          (
                          {packService.duration}
                          {' '}
                          min)
                        </Text>
                      </View>
                      <View style={{ flex: 1, alignItems: 'flex-end' }}>
                        <Text
                          style={{
                            ...generalStyles.avenirRoman17,
                            color: colors.subGrey,
                          }}
                        >
                          {this.showOfferedSessions(packService)}
                        </Text>
                      </View>
                    </FormGroup>
                  </View>
                ))
                : null}
            </View>
            <View style={styles.packageServices}>
              <Text style={styles.servicesLabel}>Payment</Text>
              <FormGroup formStyle={styles.packageServicesStyle}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    Payment Interval
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    {this.showPaymentIntervalValue(
                      this.props.item.paymentInterval,
                    )}
                  </Text>
                </View>
              </FormGroup>
              <FormGroup formStyle={styles.packageServicesStyle}>
                <View style={{ flex: 3 }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    Payment Count
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    {this.props.item.repeatCount !== 99999
                      ? this.props.item.repeatCount
                      : 'Unlimited'}
                  </Text>
                </View>
              </FormGroup>
              <FormGroup formStyle={styles.packageServicesStyle}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    Payment Expire
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    {this.showPaymentExpireValue(
                      this.props.item.expireType,
                      this.props.item.expireDaysCustom,
                    )}
                  </Text>
                </View>
              </FormGroup>
            </View>
          </View>
        </ScrollView>
        <View style={styles.bottomButtonsContainer}>
          {this.props.item?.active === 'true' ? (
            <>
              <FormGroup formStyle={styles.bottomSubmitButton}>
                <FormSubmitButton
                  title="Sell Package"
                  buttonStyle={isPackageDisabled && styles.disabledBtn}
                  disabled={isPackageDisabled}
                  buttonLabelStyle={
                    isPackageDisabled && { color: colors.subGrey }
                  }
                  onPressButton={() => this.props.onSellItem(this.props.item)}
                />
              </FormGroup>
              <View style={styles.bottom}>
                <FormSubmitButton
                  buttonStyle={{
                    backgroundColor: colors.white,
                    borderWidth: 2,
                    borderColor: colors.bordergrey,
                    marginRight: 5,
                    flex: 1,
                  }}
                  buttonLabelStyle={{ color: colors.subGrey }}
                  title="Edit"
                  onPressButton={() => this.props.onGoToEditScreen(this.props.item)}
                />
                <FormSubmitButton
                  buttonStyle={{
                    backgroundColor: colors.white,
                    borderWidth: 2,
                    borderColor: colors.bordergrey,
                    marginLeft: 5,
                    flex: 1,
                    marginBottom: 70,
                  }}
                  buttonLabelStyle={{ color: colors.subGrey }}
                  title="Archive"
                  onPressButton={() => this.props.onArchivePackage(this.props.item)}
                />
              </View>
            </>
          ) : (
            <FormGroup formStyle={styles.bottomSubmitOnlyEditButton}>
              <FormSubmitButton
                buttonStyle={{
                  backgroundColor: colors.white,
                  borderColor: colors.bordergrey,
                  marginBottom: 70,
                }}
                buttonLabelStyle={{ color: colors.subGrey }}
                title="Restore"
                onPressButton={() => this.props.onRestorePack(this.props.item)}
              />
            </FormGroup>
          )}
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  item: {
    flex: 1,
    flexDirection: 'column',
    width: '100%',
  },
  header: {
    display: 'flex',
    flexDirection: 'column',
    height: 250,
  },
  headerName: {
    marginTop: 14,
    width: '100%',
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  headerTotalValue: {
    flex: 1,
    alignItems: 'center',
    color: colors.white,
    ...generalStyles.avenirRoman13,
    alignSelf: 'flex-end',
    justifyContent: 'center',
    marginRight: 19,
    marginTop: 8,
    lineHeight: 22,
  },
  headerPrice: {
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.goodGreen,
    borderRadius: 30,
    height: 38,
    width: 82,
    marginRight: 19,
  },
  headerPriceText: {
    color: colors.white,
    ...generalStyles.durationText,
  },
  content: {
    flex: 1,
    flexDirection: 'column',
  },
  contentUser: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'flex-start',
  },

  userImageContainer: {
    justifyContent: 'center',
    marginRight: 15,
  },
  userImage: {
    width: 50,
    height: 50,
    borderColor: colors.white,
    borderRadius: 25,
  },
  userDetails: {
    flex: 5,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.smallText,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.priceText,
    color: colors.black,
  },
  packageDescription: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  description: {
    fontSize: 16,
    ...generalStyles.avenirRoman17,
    color: colors.black,
  },
  packageServices: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  packageServicesStyle: {
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: 0,
    marginBottom: 5,
    marginTop: 0,
  },
  servicesLabel: {
    fontWeight: 'bold',
    ...generalStyles.fontBold,
    marginBottom: 8,
    color: colors.black,
  },
  title: {
    ...generalStyles.titleService,
    color: colors.white,
    marginTop: 10,
  },
  bottomButtonsContainer: {
    flex: 0.25,
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 30,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    marginTop: 20,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  bottom: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
    marginTop: 10,
    marginBottom: 38,
    marginHorizontal: 15,
  },
  bottomSubmitOnlyEditButton: {
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 30,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    bottom: 25,
  },
  disabledBtn: {
    backgroundColor: colors.lightgrey,
    borderColor: colors.lightgrey,
  },
});
export default PackageView;
