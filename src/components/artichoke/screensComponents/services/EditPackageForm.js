import React, { createRef } from 'react';
import { Formik } from 'formik';
import {
  View, StyleSheet, ScrollView, Text, Alert, Platform,
} from 'react-native';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import _cloneDeep from 'lodash.clonedeep';
import CustomActionSheet from '../../common/CustomActionSheet';
import FormTextInput from '../../common/FormTextInput';
import FormGroup from '../../common/FormGroup';
import FormTextInputUnderline from '../../common/FormTextInputUnderline';
import FormMultilineTextInput from '../../common/FormMultilineTextInput';
import FormSimpleTextInput from '../../common/FormSimpleTextInput';
import FormSwitchInput from '../../common/FormSwitchInput';
import FormSubmitButton from '../../common/FormSubmitButton';
import FormServices from '../../common/FormServices';
import FormGeneral from '../../common/FormGeneral';
import { APPLICATION_ROUTES } from '../../../../constants';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import AddTooltipButton from '../../common/AddTooltipButton';
import { track } from '../../../../util/Analytics';
import { reduceTwoDecDigit, removeEmojis } from '../../../../util/utils';
import * as packageValidations from '../../../../util/packageValidations';

const taxRateActionSheetRef = createRef();
const paymentIntervalActionSheetRef = createRef();
const balanceExpireActionSheetRef = createRef();
const taxType = [
  { label: 'Calculated', value: 'CALCULATED' },
  { label: 'Flat Rate', value: 'FLATRATE' },
];
const paymentInterval = [
  { label: 'Once', value: 'ONETIME' },
  { label: 'Every Week', value: 'WEEKLY' },
  { label: 'Every Other Week', value: 'BIWEEKLY' },
  { label: 'Every Month', value: 'MONTHLY' },
  { label: 'Every Year', value: 'ANNUALLY' },
];
const balanceExpire = [
  { label: 'Never', value: 'NEVER' },
  { label: 'On Next Payment Date', value: 'PERIODEND' },
  { label: 'After 30 Days', value: 'CUSTOM-30' },
  { label: 'After 60 Days', value: 'CUSTOM-60' },
  { label: 'After 90 Days', value: 'CUSTOM-90' },
  { label: 'Custom Days', value: 'CUSTOM' },
];

const EditPackageForm = (props) => {
  const initialValues = {
    ...props.selectedPackage,
    price: parseFloat(Number(props?.selectedPackage?.price)).toFixed(2),
    taxRate: parseFloat(Number(props.selectedPackage?.taxRate)).toFixed(2),
  };

  const showTaxInfo = () => {
    Alert.alert(
      'Tax Rate',
      '\nCalculated: Tax calculations are made based on the tax rate of each individual service and prorated based on the total package price.\r\n\nFlat Rate: Apply a flat rate tax to the entire package. This might be the tax rate of the dominant service within the package but depends on local tax code.',
      [
        {
          text: 'Ok',
        },
      ],
      { cancelable: false },
    );
  };

  const onChangeTaxRate = (value) => {
    if (value === 'CALCULATED') {
      const tax = props.calculateTax();
      props.setSelectedPackageForEditValuesAction({ key: 'tax', value: tax });
    } else {
      props.setSelectedPackageForEditValuesAction({
        key: 'tax',
        value: '0.00',
      });
      props.setSelectedPackageForEditValuesAction({
        key: 'taxRate',
        value: '0.00',
      });
    }
    props.setSelectedPackageForEditValuesAction({
      key: 'taxType',
      value,
    });
    taxRateActionSheetRef.current?.setModalVisible(false);
  };

  const onChangePaymentInterval = (value) => {
    props.setSelectedPackageForEditValuesAction({
      key: 'paymentInterval',
      value,
    });
    props.setSelectedPackageForEditValuesAction({
      key: 'repeatCount',
      value: '1',
    });
    paymentIntervalActionSheetRef.current?.setModalVisible(false);
  };

  const onChangeBalanceExpire = (value) => {
    if (
      value === 'CUSTOM-30'
      || value === 'CUSTOM-60'
      || value === 'CUSTOM-90'
    ) {
      const expireType = 'CUSTOM';
      const expireDaysCustom = parseInt(value.split('-').pop(), 10);
      props.setSelectedPackageForEditValuesAction({
        key: 'expireType',
        value: expireType,
      });
      props.setSelectedPackageForEditValuesAction({
        key: 'expireDaysCustom',
        value: expireDaysCustom,
      });
    } else {
      props.setSelectedPackageForEditValuesAction({
        key: 'expireType',
        value,
      });
      props.setSelectedPackageForEditValuesAction({
        key: 'expireDaysCustom',
        value: null,
      });
    }
    balanceExpireActionSheetRef.current?.setModalVisible(false);
  };

  const showTaxRateType = (value) => {
    let nameShown = 'Select Tax Rate';
    if (value === 'CALCULATED') {
      nameShown = 'Calculated';
    }
    if (value === 'FLATRATE') {
      nameShown = 'Flat Rate';
    }
    return nameShown;
  };

  const showPaymentIntervalValue = (value) => {
    switch (value) {
      case 'ONETIME':
        return 'Once';
      case 'WEEKLY':
        return 'Every Week';
      case 'BIWEEKLY':
        return 'Every Other Week';
      case 'MONTHLY':
        return 'Every Month';
      case 'ANNUALLY':
        return 'Every Year';
      default:
        return 'Select an Interval';
    }
  };

  const showBalanceExpireValue = (expireType, expireDaysCustom) => {
    if (expireType === 'NEVER') {
      return 'Never';
    } if (expireType === 'PERIODEND') {
      return 'On Next Payment Date';
    } if (expireType === 'CUSTOM' && expireDaysCustom === 30) {
      return 'After 30 Days';
    } if (expireType === 'CUSTOM' && expireDaysCustom === 60) {
      return 'After 60 Days';
    } if (expireType === 'CUSTOM' && expireDaysCustom === 90) {
      return 'After 90 Days';
    } if (expireType === 'CUSTOM') {
      return 'Custom Days';
    }
    return 'Select a Date';
  };

  const showIsSelectedBalanceExpire = (value, expireType, expireDaysCustom) => {
    if (value === expireType && expireType !== 'CUSTOM') {
      return true;
    } if (
      expireType === 'CUSTOM'
      && value === expireType
      && ![30, 60, 90].includes(expireDaysCustom)
    ) {
      return true;
    } if (
      expireType === 'CUSTOM'
      && value === `${expireType}-${expireDaysCustom}`
    ) {
      return true;
    }
    return false;
  };

  const servicesValidation = (value) => {
    let error;

    if (value.length < 1) {
      error = 'Required';
    }
    return error;
  };

  const setQuantityForSelectedService = (item, value) => {
    const services = _cloneDeep(props.selectedPackage.packageProducts);
    const newServices = services.map((serviceItem) => {
      if (serviceItem.productId === item.productId) {
        serviceItem.quantity = value;
      }
      return serviceItem;
    });
    props.setSelectedPackageForEditValuesAction({
      key: 'packageProducts',
      value: newServices,
    });
    props.calculateTotalValue(newServices);
  };

  const setUnlimitedForSelectedService = (item, value) => {
    const services = _cloneDeep(props.selectedPackage.packageProducts);
    const newServices = services.map((serviceItem) => {
      if (serviceItem.productId === item.productId) {
        serviceItem.unlimited = value;
        if (value) {
          serviceItem.quantity = 1;
        }
      }
      return serviceItem;
    });
    props.setSelectedPackageForEditValuesAction({
      key: 'packageProducts',
      value: newServices,
    });
  };

  const addExistingServices = (services) => {
    props.setSelectedOfferingServiceAction(services);
    props.navigation.navigate({
      name: APPLICATION_ROUTES.VIEW_SERVICES_OFFERING_FOR_EDIT_PACKAGE,
    });
  };

  const disableSubmitButton = (initialValues) => {
    let disable = true;
    if (
      initialValues.name !== null
      && initialValues.name !== ''
      && initialValues.packageProducts.length > 0
      && initialValues.price !== null
      && initialValues.price !== ''
    ) {
      disable = false;
    }
    return disable;
  };

  const buttonStyle = disableSubmitButton(props.selectedPackage)
    ? { backgroundColor: colors.lightgrey, borderColor: colors.lightgrey }
    : { backgroundColor: colors.duskBlue, borderColor: colors.duskBlue };

  const TaxRateSelect = React.memo(() => (
    <>
      <View style={{ flex: 9 }}>
        <FormGeneral
          valueStyle={
            props.selectedPackage.taxType !== 'NOTAX'
              ? { color: colors.black }
              : { color: colors.subGrey }
          }
          label="Tax Rate"
          value={showTaxRateType(props.selectedPackage.taxType)}
          onPressFunc={() => {
            taxRateActionSheetRef.current?.setModalVisible();
          }}
        />
      </View>
      <View
        style={{
          flex: 1,
          marginLeft: '2%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <AddTooltipButton onPressAction={showTaxInfo} />
      </View>
    </>
  ));

  return (
    <ScrollView style={styles.container}>
      <Formik
        validateOnMount
        initialValues={initialValues}
        onSubmit={(values) => {
          if (values.quantity && values.quantity.toString() === '0') {
            Alert.alert('Error', 'Session quantity cannot be 0');
          } else if (values.price > 0 && values.price < 1) {
            Alert.alert('Error', 'A minimum price of $1 must be set for all items you plan to sell. If you’d like to make this item free you can set the price to $0');
          } else {
            props.onSubmitForm(values);
          }
        }}
      >
        {({
          handleBlur,
          setFieldValue,
          handleSubmit,
          values,
        }) => (
          <View style={styles.form}>
            <FormGroup>
              <FormTextInputUnderline
                onChangeText={(value) => {
                  props.setSelectedPackageForEditValuesAction({
                    key: 'name',
                    value,
                  });
                  setFieldValue('name', value);
                }}
                onBlur={handleBlur('name')}
                label="Package Name"
                value={values.name}
                placeholder="Package Name"
                keyboardType={Platform.OS === 'ios' ? 'ascii-capable' : 'visible-password'}
                name="name"
                errors={packageValidations.nameValidation(
                  props.selectedPackage.name,
                )}
              />
            </FormGroup>
            <FormGroup>
              <FormServices
                label="Services"
                selectedServices={
                    props.selectedPackage.packageProducts.length
                  }
                onPressFunc={() => addExistingServices(values.packageProducts)}
              />
              {servicesValidation(props.selectedPackage.packageProducts) && (
              <Text style={styles.error}>
                {servicesValidation(props.selectedPackage.packageProducts)}
              </Text>
              )}
              {props.selectedPackage.packageProducts.length
                ? props.selectedPackage.packageProducts.map((item, index) => (
                  <View key={item.id} style={{ height: 142 }}>
                    <FormGroup
                      formStyle={{
                        flexDirection: 'row',
                        flex: 1,
                        marginHorizontal: 0,
                      }}
                    >
                      <View style={{ flex: 4 }}>
                        <Text
                          style={{
                            ...generalStyles.fontBold,
                            color: colors.black,
                          }}
                        >
                          {item.productName}
                        </Text>
                        <Text
                          style={{
                            ...generalStyles.avenirRoman13,
                            color: colors.subGrey,
                          }}
                        >
                          {item.duration}
                          {' '}
                          min
                        </Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                          marginLeft: '2%',
                          alignItems: 'flex-end',
                          justifyContent: 'flex-start',
                          paddingTop: 10,
                        }}
                      >
                        <FormSimpleTextInput
                          editable={!item.unlimited}
                          formInputStyle={{ height: 42, width: 40 }}
                          fieldInputStyle={{ textAlign: 'center' }}
                          onChangeText={(value) => {
                            setQuantityForSelectedService(item, value);
                            setFieldValue(`quantity[${index}]`, value);
                          }}
                          onBlur={handleBlur(`quantity[${index}]`)}
                          placeholder="0"
                          name={`quantity[${index}]`}
                          value={item.quantity.toString()}
                        />
                      </View>
                    </FormGroup>
                    <View
                      style={{
                        borderBottomWidth: 1,
                        borderBottomColor: colors.cloudyBlue,
                      }}
                    >
                      <FormSwitchInput
                        formInputStyle={{
                          paddingHorizontal: 0,
                          marginBottom: 11,
                        }}
                        labelInputStyle={{
                          ...generalStyles.avenirMedium14,
                        }}
                        onValueChange={(value) => {
                          setUnlimitedForSelectedService(item, value);
                          setFieldValue('unlimitedEnable', value);
                        }}
                        label="Unlimited Quantity"
                        value={item.unlimited}
                      />
                    </View>
                  </View>
                ))
                : null}
              <Text style={styles.totalText}>
                Total Value:
                {' '}
                {props.symbol}
                {props.totalValue}
              </Text>
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <Text style={styles.titleText}>Payment</Text>
              <FormTextInput
                keyboardType="numeric"
                onChangeText={(value) => {
                  setFieldValue('price', reduceTwoDecDigit(value));
                }}
                onBlur={() => {
                  props.setSelectedPackageForEditValuesAction({ key: 'price', value: values.price });
                  handleBlur('price');
                }}
                label="Price"
                placeholder="$0.00"
                name="price"
                maxLength={7}
                value={values.price}
                textBeforeInput={values.price !== '' ? props.symbol : ''}
              />
            </FormGroup>
            <FormGroup formStyle={{ flexDirection: 'row', flex: 1 }}>
              <TaxRateSelect />
            </FormGroup>
            {props.selectedPackage.taxType !== 'NOTAX'
              && props.selectedPackage.taxType === 'FLATRATE' ? (
                <View style={{ flex: 1, marginHorizontal: 15 }}>
                  <FormTextInput
                    onBlur={() => {
                      props.onChangeFlatRateTax(values.taxRate);
                      handleBlur('taxRate');
                    }}
                    onChangeText={(value) => {
                      setFieldValue('taxRate', value);
                    }}
                    label={showTaxRateType(props.selectedPackage.taxType)}
                    placeholder="0.00%"
                    name="taxRate"
                    maxLength={5}
                    value={reduceTwoDecDigit(values.taxRate.toString())}
                    textAfterInput="%"
                  />
                  {packageValidations.flatRateValidation(
                    props.selectedPackage.taxRate,
                  ).tax && (
                    <Text style={styles.error}>
                      {
                        packageValidations.flatRateValidation(
                          props.selectedPackage.taxRate,
                        ).tax
                      }
                    </Text>
                  )}
                </View>
              ) : null}
            {props.selectedPackage.taxType !== 'NOTAX'
              && props.selectedPackage.taxType === 'CALCULATED' ? (
                <View pointerEvents="none" style={{ flex: 1, marginHorizontal: 15 }}>
                  <FormTextInput
                    keyboardType="numeric"
                    onBlur={handleBlur('tax')}
                    label={showTaxRateType(props.selectedPackage.taxType)}
                    placeholder="0.00"
                    name="tax"
                    value={props.selectedPackage.tax.toString()}
                    textBeforeInput={props.selectedPackage.tax ? props.symbol : ''}
                  />
                </View>
              ) : null}
            <CustomActionSheet
              actionSheetRef={taxRateActionSheetRef}
              title="Select a Tax Rate"
            >
              <RadioForm>
                {taxType.map((obj, i) => (
                  <RadioButton
                    labelHorizontal
                    key={i}
                    style={{
                      paddingHorizontal: 30,
                      paddingVertical: 20,
                      flex: 1,
                      borderBottomWidth: 1,
                      borderBottomColor: colors.lightgrey,
                    }}
                  >
                    <RadioButtonInput
                      obj={obj}
                      index={i}
                      isSelected={
                          values
                          && props.selectedPackage.taxType
                          && props.selectedPackage.taxType === obj.value
                        }
                      onPress={() => onChangeTaxRate(obj.value)}
                      buttonStyle={{}}
                      buttonWrapStyle={{ justifyContent: 'center' }}
                      borderWidth={1}
                      buttonSize={10}
                      buttonOuterSize={20}
                      buttonOuterColor={{ color: colors.subGrey }}
                    />
                    <RadioButtonLabel
                      obj={obj}
                      index={i}
                      labelHorizontal
                      onPress={() => onChangeTaxRate(obj.value)}
                      labelStyle={generalStyles.fontBold}
                      labelWrapStyle={{}}
                    />
                  </RadioButton>
                ))}
              </RadioForm>
            </CustomActionSheet>
            <FormGroup>
              <Text style={styles.titleText}>Subscription Options</Text>
              <FormGeneral
                formInputStyle={{ marginBottom: 12 }}
                  // valueStyle = {props.newPackage.paymentInterval !== null ? {color: colors.black} : {color: colors.subGrey}}
                label="Payment Interval"
                value={showPaymentIntervalValue(
                  props.selectedPackage.paymentInterval,
                )}
                onPressFunc={() => {
                  paymentIntervalActionSheetRef.current?.setModalVisible();
                }}
              />
              <CustomActionSheet
                actionSheetRef={paymentIntervalActionSheetRef}
                title="Select a Payment Frequency"
              >
                <RadioForm>
                  {paymentInterval.map((obj, i) => (
                    <RadioButton
                      labelHorizontal
                      key={i}
                      style={{
                        paddingHorizontal: 30,
                        paddingVertical: 20,
                        flex: 1,
                        borderBottomWidth: 1,
                        borderBottomColor: colors.lightgrey,
                      }}
                    >
                      <RadioButtonInput
                        obj={obj}
                        index={i}
                        isSelected={
                            values
                            && props.selectedPackage.paymentInterval
                            && props.selectedPackage.paymentInterval === obj.value
                          }
                        onPress={() => onChangePaymentInterval(obj.value)}
                        buttonStyle={{}}
                        buttonWrapStyle={{ justifyContent: 'center' }}
                        borderWidth={1}
                        buttonSize={10}
                        buttonOuterSize={20}
                        buttonOuterColor={{ color: colors.subGrey }}
                      />
                      <RadioButtonLabel
                        obj={obj}
                        index={i}
                        labelHorizontal
                        onPress={() => onChangePaymentInterval(obj.value)}
                        labelStyle={generalStyles.fontBold}
                        labelWrapStyle={{}}
                      />
                    </RadioButton>
                  ))}
                </RadioForm>
              </CustomActionSheet>
              {props.selectedPackage.paymentInterval === 'ONETIME' || (
                <>
                  <FormSwitchInput
                    formInputStyle={{
                      paddingHorizontal: 0,
                      marginBottom: 11,
                    }}
                    labelInputStyle={{ ...generalStyles.avenirMedium14 }}
                    onValueChange={(value) => {
                      if (value) {
                        props.setSelectedPackageForEditValuesAction({ key: 'repeatCount', value: 99999 });
                      } else {
                        props.setSelectedPackageForEditValuesAction({ key: 'repeatCount', value: 1 });
                        setFieldValue('paymentCount', 1);
                      }
                    }}
                    label="Never/Unlimited Payments"
                    value={props.selectedPackage.repeatCount === 99999}
                  />
                  {props.selectedPackage.repeatCount === 99999 || (
                  <FormTextInput
                    keyboardType="numeric"
                    formInputStyle={{ marginBottom: 12 }}
                    onChangeText={(value) => {
                      props.setSelectedPackageForEditValuesAction({
                        key: 'repeatCount',
                        value,
                      });
                      setFieldValue('paymentCount', value);
                    }}
                    onBlur={handleBlur('paymentCount')}
                    label="Payment Count"
                    placeholder="# of Payments"
                    name="paymentCount"
                    value={props.selectedPackage.repeatCount.toString()}
                    txtWidth={20}
                  />
                  )}
                </>
              )}
              <FormGeneral
                  // valueStyle = {props.newPackage.expireType !== null ? {color: colors.black} : {color: colors.subGrey}}
                label="Balances Expire"
                value={showBalanceExpireValue(
                  props.selectedPackage.expireType,
                  props.selectedPackage.expireDaysCustom,
                )}
                onPressFunc={() => {
                  balanceExpireActionSheetRef.current?.setModalVisible();
                }}
              />
              {props.selectedPackage.expireType === 'CUSTOM'
                && ![30, 60, 90].includes(
                  props.selectedPackage.expireDaysCustom,
                ) ? (
                  <View style={{ flex: 1, marginTop: 10 }}>
                    <FormTextInput
                      keyboardType="numeric"
                      onChangeText={(value) => {
                        props.setSelectedPackageForEditValuesAction({
                          key: 'expireDaysCustom',
                          value,
                        });
                        setFieldValue('expireDaysCustom', value);
                      }}
                      onBlur={handleBlur('expireDaysCustom')}
                      label="Number of Days"
                      placeholder="# of days"
                      name="expireDaysCustom"
                      value={
                        props.selectedPackage.expireDaysCustom !== null
                          ? props.selectedPackage.expireDaysCustom.toString()
                          : '0'
                      }
                      // textAfterInput="%"
                    />
                  </View>
                ) : null}
            </FormGroup>
            <CustomActionSheet
              actionSheetRef={balanceExpireActionSheetRef}
              title="Select When Balances Expire"
            >
              <RadioForm>
                {balanceExpire.map((obj, i) => (
                  <RadioButton
                    labelHorizontal
                    key={i}
                    style={{
                      paddingHorizontal: 30,
                      paddingVertical: 20,
                      flex: 1,
                      borderBottomWidth: 1,
                      borderBottomColor: colors.lightgrey,
                    }}
                  >
                    <RadioButtonInput
                      obj={obj}
                      index={i}
                      isSelected={showIsSelectedBalanceExpire(
                        obj.value,
                        props.selectedPackage.expireType,
                        props.selectedPackage.expireDaysCustom,
                      )}
                      onPress={() => onChangeBalanceExpire(obj.value)}
                      buttonStyle={{}}
                      buttonWrapStyle={{ justifyContent: 'center' }}
                      borderWidth={1}
                      buttonSize={10}
                      buttonOuterSize={20}
                      buttonOuterColor={{ color: colors.subGrey }}
                    />
                    <RadioButtonLabel
                      obj={obj}
                      index={i}
                      labelHorizontal
                      onPress={() => onChangeBalanceExpire(obj.value)}
                      labelStyle={generalStyles.fontBold}
                      labelWrapStyle={{}}
                    />
                  </RadioButton>
                ))}
              </RadioForm>
            </CustomActionSheet>
            <FormGroup>
              <FormMultilineTextInput
                onChangeText={(value) => {
                  props.setSelectedPackageForEditValuesAction({
                    key: 'packageDescription',
                    value,
                  });
                  setFieldValue('description', value);
                }}
                onBlur={handleBlur('description')}
                placeholder="Description"
                name="description"
                maxLength={360}
                value={removeEmojis(props.selectedPackage.packageDescription)}
              />
            </FormGroup>
            <FormGroup>
              <FormSwitchInput
                onValueChange={(value) => {
                  props.setSelectedPackageForEditValuesAction({
                    key: 'sellOnlineEnabled',
                    value,
                  });
                  setFieldValue('bookOnlineEnabled', value);
                }}
                label="Clients can buy this package"
                value={props.selectedPackage.sellOnlineEnabled}
              />
            </FormGroup>
            <FormGroup formStyle={styles.bottomSubmitButton}>
              <FormSubmitButton
                title="Save Package"
                disabled={disableSubmitButton(props.selectedPackage)}
                onPressButton={() => {
                  handleSubmit(props.selectedPackage);
                  track('package_edited');
                }}
                loading={props.savePackageLoading}
                buttonStyle={buttonStyle}
              />
            </FormGroup>
          </View>
        )}
      </Formik>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
  },
  totalText: {
    ...generalStyles.avenirMedium17,
    height: 50,
    textAlignVertical: 'center',
    marginTop: 8,
    textAlign: 'right',
    color: colors.black,
  },
  titleText: {
    ...generalStyles.fontBold,
    height: 26,
    textAlignVertical: 'center',
    marginBottom: 12,
    color: colors.black,
  },
  bottomSubmitButton: {
    display: 'flex',
    flex: 1,
    borderRadius: 30,
    marginTop: 20,
  },
  error: {
    display: 'flex',
    width: '100%',
    fontSize: 10,
    color: 'red',
  },
  formStyle: {
    marginBottom: 0,
    marginTop: 0,
  },
});
export default EditPackageForm;
