import moment from 'moment';
import React, { Component } from 'react';
import {
  ImageBackground,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import IconMaterial from 'react-native-vector-icons/MaterialIcons';
import {
  APPLICATION_CLIENT_ROUTES,
  APPLICATION_ROUTES,
} from '../../../../constants';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';
import { serviceViewStyles } from '../../../../styles/serviceViewStyle';
import { track } from '../../../../util/Analytics';
import { scaleHeight } from '../../../../util/responsive';
import FormCreditCardInfo from '../../common/FormCreditCardInfo';
import FormGeneral from '../../common/FormGeneral';
import FormSubmitButton from '../../common/FormSubmitButton';
import ViewTrainerItem from '../client/bookings/ViewTrainerItem';

const bgImage = require('../../../../assets/imgServiceBackground.png');

class ClassViewForm extends Component {
  renderCreditCard = () => {
    if (
      this.props.user.account.StripeAccount
      && this.props.user.account.StripeAccount.stripeSecretKey
    ) {
      if (
        this.props.clientCreditCard
        && this.props.clientCreditCard.creditCardType
        && this.props.clientCreditCard.lastFour
      ) {
        return (
          <FormCreditCardInfo
            customStyle={generalStyles.mVertical10}
            value={`${this.props.clientCreditCard.creditCardType} ...${this.props.clientCreditCard.lastFour}`}
            onPressAction={() => this.props.navigation.navigate(APPLICATION_ROUTES.ADD_CREDIT_CARD)}
          />
        );
      }
      return (
        <FormGeneral
          label="Add payment method"
          formInputStyle={generalStyles.mVertical10}
          value=""
          onPressFunc={() => this.props.navigation.navigate({
            name: APPLICATION_CLIENT_ROUTES.ADD_CREDIT_CARD,
          })}
        />
      );
    }
    return null;
  };

  isRequiredCard = () => {
    if (parseFloat(this.props.item.ProductDuration.priceWithTax) > 0) {
      if (
        !this.props.clientCreditCard
        || !this.props.clientCreditCard.creditCardType
        || !this.props.clientCreditCard.lastFour
      ) {
        return true;
      }
    }
    return false;
  };

  render() {
    return (
      <View style={styles.container}>
        <ScrollView style={{ ...styles.item, paddingTop: 0 }}>
          <View style={styles.header}>
            <ImageBackground source={bgImage} style={styles.image}>
              <IconMaterial
                name="cancel"
                size={30}
                color={colors.white}
                onPress={() => this.props.navigation.navigate({ name: 'Classes' })}
                style={serviceViewStyles.closeIcon}
              />
              <View style={styles.headerName}>
                <Text style={styles.title}>
                  {this.props.item.ProductDuration.name}
                </Text>
              </View>
              <View style={styles.headerTimePrice}>
                <Text style={styles.headerTime}>
                  {moment(this.props.item.start, 'MM/DD/YYYY hh:mm A').format(
                    'hh:mm a',
                  )}
                  {' '}
                  (
                  {`${this.props.item.ProductDuration.duration.duration} ${this.props.item.ProductDuration.duration.granularity.abbreviation}`}
                  )
                </Text>
                <Text style={styles.headerPrice}>
                  {this.props.symbol}
                  {parseFloat(
                    this.props.item.ProductDuration.priceWithTax,
                  ).toFixed(2)}
                </Text>
              </View>
            </ImageBackground>
          </View>
          <View style={styles.content}>
            <ViewTrainerItem trainer={this.props.item.trainerInfo} />
            <Text style={styles.serviceDescription}>
              {this.props.item.description}
            </Text>
            <View>
              <Text style={styles.dateAndTimeLabel}>Date & Time</Text>
              <View style={styles.dateAndTimeDetails}>
                <Text style={styles.date}>
                  {moment(this.props.item.start, 'MM/DD/YYYY hh:mm A').format(
                    'L',
                  )}
                </Text>
                <Text style={styles.time}>
                  {moment(this.props.item.start, 'MM/DD/YYYY hh:mm A').format(
                    'hh:mm a',
                  )}
                  {' '}
                  (
                  {`${this.props.item.ProductDuration.duration.duration} ${this.props.item.ProductDuration.duration.granularity.abbreviation}`}
                  )
                </Text>
              </View>
              {this.props.item.remotely.toString() === 'true' ? (
                <>
                  <Text style={styles.locationLabel}>Remote</Text>
                  <Text style={styles.address} numberOfLines={2}>
                    Once booked, you will be provided with details on how to
                    join the class.
                  </Text>
                </>
              ) : null}
              {this.props.item.inapp.toString() === 'true' ? (
                <>
                  <Text style={styles.locationLabel}>Video Session</Text>
                  <Text style={styles.address} numberOfLines={2}>
                    This session will be held via video, you will receive an
                    alert to join 10 minutes before the start.
                  </Text>
                </>
              ) : null}
            </View>

            <View style={{ paddingHorizontal: 20 }}>
              {this.isRequiredCard() ? (
                <Text style={styles.required}>* Required</Text>
              ) : null}
              {this.renderCreditCard()}
            </View>
          </View>
        </ScrollView>

        <View style={styles.goAppointments}>
          <View style={styles.totalText}>
            <Text style={styles.textLeft}>Total</Text>
            <Text style={styles.textRight}>
              {this.props.symbol}
              {parseFloat(this.props.item.ProductDuration.priceWithTax).toFixed(
                2,
              )}
            </Text>
          </View>
          <Text style={styles.centerText}>
            You will not be charged until check-in.
          </Text>
          {this.props.booked || this.isRequiredCard() ? (
            <FormSubmitButton
              title="Book Now"
              buttonStyle={{
                borderColor: colors.subGrey,
                backgroundColor: colors.lightgrey,
              }}
              buttonLabelStyle={{ color: colors.subGrey }}
              onPressButton={null}
              disabled
            />
          ) : (
            <FormSubmitButton
              title="Book Now"
              loading={this.props.bookClientClassLoader}
              color={colors.white}
              onPressButton={() => {
                this.props.onSaveAppointment();
                track('class_booked');
              }}
            />
          )}
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    flex: 1,
  },
  item: {
    flexDirection: 'column',
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  header: {
    flex: 1,
    flexDirection: 'row',
  },
  headerName: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTimePrice: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 30,
    flexDirection: 'row',
  },
  headerTime: {
    flex: 4,
    alignItems: 'flex-start',
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    alignSelf: 'center',
  },
  headerPrice: {
    flex: 1,
    alignItems: 'flex-end',
    textAlign: 'center',
    alignSelf: 'center',
    paddingVertical: 5,
    backgroundColor: colors.goodGreen,
    borderRadius: 30,
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 16,
  },
  content: {
    flex: 3,
    flexDirection: 'column',
    paddingBottom: 20,
  },
  serviceDescription: {
    flex: 1,
    ...generalStyles.avenirRoman17,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 25,
    color: colors.white,
    fontWeight: 'bold',
    marginTop: 10,
  },
  goAppointments: {
    paddingHorizontal: 20,
    paddingBottom: scaleHeight(5),
  },
  dateAndTimeLabel: {
    fontWeight: 'bold',
    ...generalStyles.avenirHeavy17,
    paddingLeft: 20,
    paddingTop: 10,
  },
  locationLabel: {
    fontWeight: 'bold',
    ...generalStyles.avenirHeavy17,
    fontSize: 18,
    paddingLeft: 20,
  },
  dateAndTimeDetails: {
    flexDirection: 'row',
    paddingLeft: 20,
    paddingRight: 20,
    paddingBottom: 20,
    marginTop: 5,
  },
  date: {
    flex: 1,
    color: colors.subGrey,
    ...generalStyles.avenirRoman14,
  },
  time: {
    color: colors.subGrey,
    ...generalStyles.avenirRoman14,
  },
  address: {
    color: colors.subGrey,
    ...generalStyles.avenirRoman17,
    paddingHorizontal: 20,
    marginTop: 5,
  },
  totalText: {
    flexDirection: 'row',
    width: '100%',
    marginVertical: 20,
  },
  centerText: {
    textAlign: 'center',
    ...generalStyles.smallText,
    ...generalStyles.mBottom10,
  },
  textLeft: {
    flex: 1,
    fontWeight: 'bold',
    alignSelf: 'flex-start',
    ...generalStyles.fontBold,
  },
  textRight: {
    flex: 1,
    alignSelf: 'flex-end',
    textAlign: 'right',
    ...generalStyles.fontBold,
  },
  required: {
    color: colors.dustyRed,
    marginTop: 10,
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    fontStyle: 'italic',
  },
});
export default ClassViewForm;
