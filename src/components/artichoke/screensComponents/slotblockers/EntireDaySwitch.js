import React, { useState } from 'react';
import {
  View, StyleSheet, Text, Switch, Platform,
} from 'react-native';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

export default function EntireDaySwitch(props) {
  return (
    <View style={{ ...styles.frame, ...props.formInputStyle }}>
      <View style={styles.labelContainer}>
        <Text style={styles.title}>{props.title}</Text>
        <Text style={styles.label}>{props.subTitle}</Text>
      </View>
      <Switch
        trackColor={{ false: colors.chatSelected, true: colors.medYellow }}
        thumbColor={Platform.OS === 'ios' ? null : colors.white}
        style={styles.field}
        onValueChange={() => props.onChangeValue(!props.value)}
        {...props}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  frame: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'center',
    width: '100%',
    marginBottom: 10,
  },
  labelContainer: {
    flex: 5,
    display: 'flex',
    flexDirection: 'column',
  },
  label: {
    fontSize: 14,
    lineHeight: 22,
    color: colors.subGrey,
    ...generalStyles.avenirRoman14,
  },
  title: {
    marginBottom: 10,
    color: colors.black,
    ...generalStyles.titleSmall,
  },
  field: {
    fontSize: 14,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 'auto',
  },
});
