import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import moment from 'moment';
import FormTouchableDropdown from '../../common/FormTouchableDropdown';
import FormGroup from '../../common/FormGroup';
import FormTextInputUnderline from '../../common/FormTextInputUnderline';
import { APPLICATION_ROUTES } from '../../../../constants';
import { colors } from '../../../../styles';

const CreateSlotBlockerForm = (props) => {
  const showRepeatAppointmentInfo = (item) => {
    let repeat = 'Does not repeat';
    if (
      item.repeatIntervalType
      && item.repeatIntervalType !== 'Does not repeat'
    ) {
      repeat = `Repeat ${item.repeatIntervalType.toLowerCase()}`;
      if (item.untilDay) {
        repeat += ` until ${moment(item.untilDay, 'MM/DD/YYYY').format('L')}`;
      } else if (item.count) {
        if (item.count > 1) {
          repeat += ` - ${item.count} times`;
        } else {
          repeat += ` - ${item.count} time`;
        }
      }
    }
    return repeat;
  };

  const showDateAndTimeInfo = (item) => {
    let dateTime = 'Select a Date and Time';
    if (item.date && item.start && item.end) {
      dateTime = `${item.date} ${item.start} - ${item.end}`;
    }
    return dateTime;
  };

  const handleDescription = (text) => {
    props.setNewSlotBlockerValuesAction({ key: 'description', value: text });
  };

  return (
    <View style={styles.container}>
      <View style={styles.form}>
        <View>
          <FormTextInputUnderline
            formlabelStyle={{ marginBottom: 0 }}
            onChangeText={(text) => handleDescription(text)}
            value={props.initialValues.description}
            label="Description"
            placeholder="(optional)"
            name="description"
          />
          <FormTouchableDropdown
            customStyle={
              props.initialValues.date
                ? { color: colors.black }
                : { color: colors.subGrey }
            }
            formInputStyle={{ marginTop: 10 }}
            label="Date/Time"
            value={showDateAndTimeInfo(props.initialValues)}
            onPressFunc={() => props.navigation.navigate({
              name: APPLICATION_ROUTES.SLOTBLOCKER_DATE_AND_TIME,
              merge: true,
              params: {
                returnRoute: APPLICATION_ROUTES.CREATE_NEW_SLOTBLOCKER,
              },
            })}
          />
          <FormTouchableDropdown
            customStyle={
              props.initialValues.repeatIntervalType
                ? { color: colors.black }
                : { color: colors.subGrey }
            }
            formInputStyle={{ marginTop: 24 }}
            label="Repeat"
            value={showRepeatAppointmentInfo(props.initialValues)}
            onPressFunc={() => props.repeatActionSheetRef.current?.setModalVisible()}
          />
        </View>
        {props.keyboardOpen ? null : (
          <View style={styles.bottom}>
            <FormGroup formStyle={{ marginHorizontal: 0 }}>
              <Text style={styles.title}>
                Block off personal time from bookings
              </Text>
              <Text style={styles.label}>
                Block time for things like doctors appointments, meetings,
                lunch, or anything that does not warrant a change to your
                overall work hours.
              </Text>
            </FormGroup>
          </View>
        )}
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    marginHorizontal: 20,
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
  bottom: {
    flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 20,
  },
  title: {
    fontSize: 17,
    color: colors.black,
    fontFamily: 'Avenir-Medium',
  },
  label: {
    fontSize: 14,
    width: '100%',
    marginTop: 10,
    color: colors.subGrey,
    lineHeight: 22,
    fontFamily: 'Avenir-Roman',
  },
});

export default CreateSlotBlockerForm;
