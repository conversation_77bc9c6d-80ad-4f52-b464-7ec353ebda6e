import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const SlotBlockerListItem = (props) => {
  const duration = moment(props.item.end).diff(
    moment(props.item.start),
    'minutes',
  );
  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <View style={styles.topBox}>
        <Text style={styles.startHour}>
          {`${moment(props.item.start, 'MM/DD/YYYY hh:mm a').format(
            'hh:mm a',
          )} - ${moment(props.item.end, 'MM/DD/YYYY hh:mm a').format(
            'hh:mm a',
          )} `}
        </Text>
      </View>

      <View style={styles.bottomBox}>
        <View style={styles.centerBox}>
          <Text style={styles.addressName}>{props.item.description}</Text>
          <Text style={styles.addressLocation}>Slot Blocker</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    display: 'flex',
    flexDirection: 'column',
    height: 123,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  topBox: {
    width: '100%',
    paddingHorizontal: 20,
    height: 40,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  startHour: {
    width: '50%',
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    color: colors.lightblue,
  },
  checkedInTrue: {
    width: '50%',
    textAlign: 'right',
    ...generalStyles.avenirRoman13,
    color: colors.goodGreen,
  },
  checkedInFalse: {
    width: '50%',
    textAlign: 'right',
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  bottomBox: {
    width: '100%',
    marginHorizontal: 20,
    height: 73,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirHeavy14,
    color: colors.black,
  },
  addressLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  userImageContainer: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.peaGreen,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  multipleUsersContainer: {
    width: 56,
    height: 56,
    flexDirection: 'row',
    backgroundColor: colors.lightblue,
    borderRadius: 28,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userImage: {
    width: 54,
    height: 54,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  invitee: {
    flex: 1,
    ...generalStyles.fontBold,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 5,
    color: colors.white,
  },
});
export default SlotBlockerListItem;
