import React, { useState, Fragment } from 'react';
import { Formik } from 'formik';
import {
  View, StyleSheet, ScrollView, Text,
} from 'react-native';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import FormGroup from '../../common/FormGroup';
import AppointmentDateInput from '../../common/AppointmentDateInput';
import { getIntervals } from '../../../../util/utils';
import SelectTimeForm from '../../common/SelectTimeForm';
import EntireDaySwitch from './EntireDaySwitch';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const CreateDateStartEndTimeForm = (props) => {
  const selDate = props.selectedDate
    ? new Date(moment(props.selectedDate, 'L'))
    : new Date();
  const [date, setDate] = useState(selDate);
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);

  const onSelectDate = (selectedDate) => {
    setShow(false);
    setDate(selectedDate);
    props.onChangeDate(moment(selectedDate).format('MM/DD/YYYY'));
  };

  const showDatepicker = () => {
    setShow(true);
    setMode('date');
  };

  const hideDatePicker = () => {
    setShow(false);
  };

  const checkIfSelected = (slot) => {
    if (
      props.selectedStart
      && props.selectedEnd
      && moment(slot, 'h:mma').isSame(
        moment(timeSlots[timeSlots.length - 1], 'h:mma'),
      )
      && moment(slot, 'h:mma').isSame(moment(props.selectedEnd, 'h:mma'))
    ) {
      return true;
    }
    if (
      props.selectedStart
      && props.selectedEnd
      && moment(slot, 'h:mma').isSameOrAfter(
        moment(props.selectedStart, 'h:mma'),
      )
      && moment(slot, 'h:mma').isBefore(moment(props.selectedEnd, 'h:mma'))
    ) {
      return true;
    }
    return false;
  };

  const selWeekDay = moment(selDate).format('dddd').toLowerCase();
  const workHours = JSON.parse(props.workHours);
  let from = '05:00';
  let to = '23:59';
  if (workHours[selWeekDay] && !props.entireDay) {
    from = moment(workHours[selWeekDay][0].from, ['hh:mma']).format('HH:mm');
    to = moment(workHours[selWeekDay][0].to, ['hh:mma'])
      .subtract(1, 'minutes')
      .format('HH:mm');
  }
  const timeSlots = getIntervals(from, to, 'LT');

  const onSelectTime = (time) => {
    props.onChangeTime(time, timeSlots);
  };

  return (
    <ScrollView style={styles.container}>
      <Formik onSubmit={(values) => props.onSubmitForm(values)}>
        {() => (
          <View style={styles.form}>
            <FormGroup formStyle={{ marginTop: 0 }}>
              <Text style={styles.label}>Date</Text>
            </FormGroup>
            <FormGroup>
              <AppointmentDateInput
                formInputStyle={{ marginTop: 0, marginBottom: 28 }}
                selectedValue={moment(props.selectedDate, 'MM/DD/YYYY').format(
                  'L',
                )}
                onPressFunc={showDatepicker}
              />
              <EntireDaySwitch
                onChangeValue={(value) => props.onSwitch(value)}
                value={props.entireDay}
                title="Show Entire Day"
                subTitle="This will show all the available hours outside of your normal work hours"
              />
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <Text style={styles.label}>AM</Text>
              <View style={styles.selectcontainer}>
                {timeSlots.map((slot, index) => {
                  const isBlocked = props.blockingAppointments.filter(
                    (appointment) => {
                      const startTime = moment(
                        appointment.start,
                        'MM/DD/YYYY hh:mm A',
                      ).format('LT');

                      const endTime = moment(
                        appointment.end,
                        'MM/DD/YYYY hh:mm A',
                      ).format('LT');
                      return (
                        moment(slot, 'LT').isBefore(moment(endTime, 'LT'))
                        && moment(slot, 'LT').isSameOrAfter(
                          moment(startTime, 'LT'),
                        )
                      );
                    },
                  ).length;
                  if (moment(slot, 'LT').isBefore(moment('12:00 PM', 'LT'))) {
                    return (
                      <SelectTimeForm
                        key={`select-start-times-slot-key-${index}`}
                        buttonTitle={slot.replace('AM', '')}
                        width="25%"
                        isBlocked={isBlocked}
                        selected={checkIfSelected(slot)}
                        onChangeValue={() => onSelectTime(slot)}
                      />
                    );
                  }
                  if (moment(slot, 'LT').isSame(moment('12:00 PM', 'LT'))) {
                    return (
                      <>
                        <Text style={styles.label2}>PM</Text>
                        <SelectTimeForm
                          key={`select-start-times-slot-key-${index}`}
                          buttonTitle={slot.replace('PM', '')}
                          width="25%"
                          isBlocked={isBlocked}
                          selected={checkIfSelected(slot)}
                          onChangeValue={() => onSelectTime(slot)}
                        />
                      </>
                    );
                  }
                  return (
                    <SelectTimeForm
                      key={`select-start-times-slot-key-${index}`}
                      buttonTitle={slot.replace('PM', '')}
                      width="25%"
                      isBlocked={isBlocked}
                      selected={checkIfSelected(slot)}
                      onChangeValue={() => onSelectTime(slot)}
                    />
                  );
                })}
              </View>
            </FormGroup>
            <View>
              <DateTimePickerModal
                date={date}
                mode={mode}
                is24Hour
                display="default"
                isVisible={show}
                onConfirm={onSelectDate}
                onCancel={hideDatePicker}
              />
            </View>
          </View>
        )}
      </Formik>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
    marginHorizontal: 10,
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 24,
  },
  label: {
    width: '100%',
    color: colors.black,
    ...generalStyles.title,
  },
  label2: {
    color: colors.black,
    ...generalStyles.title,
    width: '100%',
    marginVertical: 15,
  },
  selectcontainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    flex: 1,
  },
});
export default CreateDateStartEndTimeForm;
