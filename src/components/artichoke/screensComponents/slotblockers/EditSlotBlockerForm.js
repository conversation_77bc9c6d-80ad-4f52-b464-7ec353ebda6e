import React from 'react';
import { View, StyleSheet } from 'react-native';
import moment from 'moment';
import FormTouchableDropdown from '../../common/FormTouchableDropdown';
import FormGroup from '../../common/FormGroup';
import FormTextInputUnderline from '../../common/FormTextInputUnderline';
import FormSubmitButton from '../../common/FormSubmitButton';
import { APPLICATION_ROUTES } from '../../../../constants';
import { colors } from '../../../../styles';
import { track } from '../../../../util/Analytics';

const EditSlotBlockerForm = (props) => {
  const showRepeatAppointmentInfo = (item) => {
    let repeat = 'Does not repeat';
    if (
      item.repeatIntervalType
      && item.repeatIntervalType !== 'Does not repeat'
    ) {
      repeat = `Repeat ${item.repeatIntervalType.toLowerCase()}`;
      if (item.untilDay) {
        repeat += ` until ${moment(item.untilDay, 'MM/DD/YYYY').format('L')}`;
      } else if (item.count) {
        if (item.count > 1) {
          repeat += ` - ${item.count} times`;
        } else {
          repeat += ` - ${item.count} time`;
        }
      }
    }
    return repeat;
  };

  const showDateAndTimeInfo = (item) => {
    let dateTime = 'Select a Date and Time';
    if (item.date && item.start && item.end) {
      dateTime = `${item.date} ${item.start} - ${item.end}`;
    }
    return dateTime;
  };

  const handleDescription = (text) => {
    props.setNewSlotBlockerValuesAction({ key: 'description', value: text });
  };

  return (
    <View style={styles.container}>
      <View style={styles.form}>
        <FormGroup formStyle={styles.formStyle}>
          <FormTextInputUnderline
            formlabelStyle={{ marginBottom: 0 }}
            textColor={{ color: colors.black }}
            onChangeText={(text) => handleDescription(text)}
            value={
              props.initialValues.description
                ? props.initialValues.description
                : 'Personal schedule'
            }
            label="Description"
            placeholder="(optional)"
            name="description"
          />
          <FormTouchableDropdown
            formInputStyle={{ marginTop: 10 }}
            label="Date/Time"
            value={showDateAndTimeInfo(props.initialValues)}
            onPressFunc={() => props.navigation.navigate({
              name: APPLICATION_ROUTES.SLOTBLOCKER_DATE_AND_TIME,
              params: {
                returnRoute: APPLICATION_ROUTES.EDIT_SLOTBLOCKER,
              },
            })}
          />
          <FormTouchableDropdown
            formInputStyle={{ marginTop: 24 }}
            label="Repeat"
            value={showRepeatAppointmentInfo(props.initialValues)}
            onPressFunc={() => props.repeatActionSheetRef.current?.setModalVisible()}
          />
        </FormGroup>
        <View style={styles.bottom}>
          <FormSubmitButton
            buttonStyle={{
              backgroundColor: colors.white,
              borderColor: colors.subGrey,
              width: '100%',
            }}
            buttonLabelStyle={{ color: colors.subGrey }}
            title="Delete"
            loading={props.deleteSlotBlockerLoading}
            onPressButton={() => {
              props.deleteSlotBlockerAction();
              track('slotblocker_deleted');
            }}
          />
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    marginHorizontal: 10,
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
  bottom: {
    flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 38,
    marginHorizontal: 20,
  },
});
export default EditSlotBlockerForm;
