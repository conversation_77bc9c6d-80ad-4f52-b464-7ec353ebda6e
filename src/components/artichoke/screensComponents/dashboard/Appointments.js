import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  StyleSheet, View, Text, Platform,
} from 'react-native';
import { Card } from '../../common';
import { colors } from '../../../../styles';

class AppointmentsCard extends Component {
  render() {
    return (
      <View style={styles.container}>
        <Card containerStyle={styles.appointmentsCard}>
          <View style={styles.appointmentsTopContainer}>
            <View style={styles.appointmentsLeftContainer}>
              <Text style={styles.appointmentsTextCard}>Appointments</Text>
              <Text style={styles.appointmentsSubTextCard}>
                Work week is 53.9 hours
              </Text>
            </View>
            <View style={styles.appointmentsRightContainer}>
              <Text style={styles.appointmentsRightText}>3.5</Text>
            </View>
          </View>
          <View style={styles.appointmentsBottomContainer} />
        </Card>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  appointmentsCard: {
    marginTop: 10,
    backgroundColor: colors.goodGreen,
    display: 'flex',
    flexDirection: 'column',
    height: 200,
    padding: 0,
  },
  appointmentsTopContainer: {
    display: 'flex',
    flexDirection: 'row',
    height: 120,
  },
  appointmentsBottomContainer: {
    display: 'flex',
    flexDirection: 'row',
    height: 80,
    backgroundColor: colors.white,
    width: '100%',
  },
  appointmentsLeftContainer: {
    flex: 3,
    display: 'flex',
    flexDirection: 'column',
    marginTop: 20,
    padding: 15,
  },
  appointmentsRightContainer: {
    flex: 1,
    display: 'flex',
    padding: 15,
    marginRight: 30,
  },
  appointmentsRightText: {
    flex: 1,
    display: 'flex',
    color: colors.white,
    fontWeight: 'bold',
    borderColor: colors.white,
    borderWidth: 5,
    borderRadius: 45,
    textAlign: 'center',
    width: 90,
    height: 90,
    fontSize: 45 - 2 * 5, // ... One for top and one for bottom alignment
    lineHeight: 90 - (Platform.OS === 'ios' ? 2 * 5 : 5), // ... One for top and one for bottom alignment
  },
  appointmentsTextCard: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'left',
  },
  appointmentsSubTextCard: {
    color: colors.white,
    fontSize: 14,
    textAlign: 'left',
  },
});

const mapStateToProps = (state) => ({
  appointments: state.appointments.appointmentsListByDate,
});
export default connect(mapStateToProps, {})(AppointmentsCard);
