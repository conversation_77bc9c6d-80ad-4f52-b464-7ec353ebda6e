import React, { Component } from 'react';
import {
  StyleSheet, View, Text, TouchableOpacity,
} from 'react-native';
import { Card } from '../../common';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';

class WorkHoursLocationsCard extends Component {
  render() {
    return (
      <TouchableOpacity
        onPress={() => this.props.navigation.navigate({
          name: APPLICATION_ROUTES.HOURS_LOCATIONS,
        })}
      >
        <View style={styles.container}>
          <Card containerStyle={styles.locationsCard}>
            <View style={styles.locationsTopContainer}>
              <View style={styles.locationsLeftContainer}>
                <Text style={styles.locationsTextCard}>
                  My Work Hours & Locations
                </Text>
              </View>
              <View style={styles.locationsRightContainer}>
                <Text style={styles.locationsRightText}>
                  (
                  {this.props.locationListlength}
                  )
                </Text>
              </View>
            </View>
          </Card>
        </View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  locationsCard: {
    marginTop: 10,
    backgroundColor: colors.peaGreen,
    display: 'flex',
    flexDirection: 'column',
    height: 80,
    padding: 0,
  },
  locationsTopContainer: {
    display: 'flex',
    flexDirection: 'row',
  },
  locationsLeftContainer: {
    flex: 3,
    padding: 15,
  },
  locationsRightContainer: {
    flex: 1,
    padding: 15,
    justifyContent: 'center',
  },
  locationsRightText: {
    flex: 1,
    display: 'flex',
    color: colors.white,
    fontWeight: 'bold',
    textAlign: 'right',
    fontSize: 16,
  },
  locationsTextCard: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'left',
  },
});

export default WorkHoursLocationsCard;
