import React, { Component } from 'react';
import {
  StyleSheet, View, Text, FlatList,
} from 'react-native';
import { connect } from 'react-redux';
import moment from 'moment';
import { Card } from '../../common';
import { colors } from '../../../../styles';
import BookedListItem from '../booked/BookedListItem';
import SlotBlockerListItem from '../slotblockers/SlotBlockerListItem';
import EmptyBookedList from '../booked/EmptyBookedList';

class BookedCard extends Component {
  renderBookedItem = (item) => {
    if (item.name === 'SLOT_BLOCKER') {
      return <SlotBlockerListItem item={item} />;
    }
    return <BookedListItem item={item} />;
  };

  render() {
    const currentDate = moment().format('YYYY-MM-DD');
    const itemList = this.props.appointments && this.props.appointments[currentDate]
      ? this.props.appointments[currentDate]
      : [];
    return (
      <View style={styles.container}>
        <Card containerStyle={styles.bookedCard}>
          <View style={styles.bookedTopContainer}>
            <View style={styles.bookedLeftContainer}>
              <Text style={styles.bookedTextCard}>Booked</Text>
            </View>
            <View style={styles.bookedRightContainer}>
              <Text style={styles.bookedRightText}>
                (
                {itemList.length}
                )
              </Text>
            </View>
          </View>
          <View style={styles.bookedBottomContainer}>
            <FlatList
              data={itemList}
              renderItem={(item) => this.renderBookedItem(item)}
              keyExtractor={(item) => item.id}
              refreshing={this.props.appointmentsListLoading}
              ListEmptyComponent={() => (
                <EmptyBookedList message="No booking found" />
              )}
              ListHeaderComponent={this.renderHeader}
            />
          </View>
        </Card>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  bookedCard: {
    marginTop: 10,
    backgroundColor: colors.medYellow,
    display: 'flex',
    flexDirection: 'column',
    height: 200,
    padding: 0,
  },
  bookedTopContainer: {
    display: 'flex',
    flexDirection: 'row',
    height: 60,
  },
  bookedBottomContainer: {
    display: 'flex',
    flexDirection: 'row',
    height: 140,
    backgroundColor: colors.white,
    width: '100%',
  },
  bookedLeftContainer: {
    flex: 3,
    display: 'flex',
    flexDirection: 'column',
    padding: 15,
  },
  bookedRightContainer: {
    flex: 1,
    display: 'flex',
    padding: 15,
  },
  bookedRightText: {
    flex: 1,
    display: 'flex',
    color: colors.white,
    fontWeight: 'bold',
    textAlign: 'right',
    fontSize: 16,
  },
  bookedTextCard: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'left',
  },
});

const mapStateToProps = (state) => ({
  appointments: state.appointments.appointmentsListByDate,
});
export default connect(mapStateToProps, {})(BookedCard);
