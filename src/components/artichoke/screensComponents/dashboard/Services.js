import React, { Component } from 'react';
import {
  StyleSheet, View, Text, TouchableOpacity,
} from 'react-native';
import { Card } from '../../common';
import { colors } from '../../../../styles';
import { APPLICATION_ROUTES } from '../../../../constants';

class ServicesCard extends Component {
  render() {
    return (
      <TouchableOpacity
        onPress={() => this.props.navigation.navigate({ name: APPLICATION_ROUTES.DASHBOARD })}
      >
        <View style={styles.container}>
          <Card containerStyle={styles.servicesCard}>
            <View style={styles.servicesTopContainer}>
              <View style={styles.servicesLeftContainer}>
                <Text style={styles.servicesTextCard}>Services</Text>
              </View>
              <View style={styles.servicesRightContainer}>
                <Text style={styles.servicesRightText}>
                  (
                  {this.props.serviceListlength}
                  )
                </Text>
              </View>
            </View>
          </Card>
        </View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  servicesCard: {
    marginTop: 10,
    backgroundColor: colors.duskBlue,
    display: 'flex',
    flexDirection: 'column',
    height: 80,
    padding: 0,
  },
  servicesTopContainer: {
    display: 'flex',
    flexDirection: 'row',
  },
  servicesLeftContainer: {
    flex: 3,
    display: 'flex',
    flexDirection: 'column',
    padding: 15,
  },
  servicesRightContainer: {
    flex: 1,
    display: 'flex',
    padding: 15,
  },
  servicesRightText: {
    flex: 1,
    display: 'flex',
    color: colors.white,
    fontWeight: 'bold',
    textAlign: 'right',
    fontSize: 16,
  },
  servicesTextCard: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'left',
  },
});

export default ServicesCard;
