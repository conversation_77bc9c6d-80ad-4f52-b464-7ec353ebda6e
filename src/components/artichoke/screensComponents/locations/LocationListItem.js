import React from 'react';
import {
  View, StyleSheet, Text, Image, TouchableOpacity,
} from 'react-native';
import <PERSON><PERSON><PERSON>eader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles, locationStyles } from '../../../../styles/generalStyle';

const locationIcon = require('../../../../assets/locationIcon.png');
const remoteIcon = require('../../../../assets/ic_remote.png');
const videoLocationIcon = require('../../../../assets/ic_video_call.png');

const LocationListItem = (props) => {
  const addressLine = props.item.address2 !== ''
    ? `${props.item.address2}, ${props.item.city}, ${props.item.state}`
    : `${props.item.city}, ${props.item.state}`;
  const checkboxStyle = props.isSelected
    ? styles.selectedCheckbox
    : styles.unselectedCheckbox;

  const getIconAttributes = () => {
    switch (props.item.id) {
      case 1:
        return [remoteIcon, locationStyles.remoteLocationIcon];
      case 2:
        return [videoLocationIcon, locationStyles.videoCallLocationIcon];
      default:
        return [locationIcon, locationStyles.locationIcon];
    }
  };

  return (
    <TouchableOpacity
      key={props.item.id}
      activeOpacity={0.2}
      onPress={() => (props.isSelected
        ? props.unselectItem(props.item)
        : props.selectItem(props.item))}
    >
      <View style={styles.item}>
        <View style={styles.leftBox}>
          <Image
            source={getIconAttributes()[0]}
            style={getIconAttributes()[1]}
          />
        </View>
        <View style={styles.centerBox}>
          <Text style={styles.addressName}>{props.item.addressName}</Text>
          {addressLine !== '' || (
            <Text style={styles.addressLocation}>
              {!props.item.city && !props.item.state ? '' : addressLine}
            </Text>
          )}
        </View>
        <View style={styles.rightBox}>
          <TouchableOpacity
            style={checkboxStyle}
            activeOpacity={1}
            onPress={() => (props.isSelected
              ? props.unselectItem(props.item)
              : props.selectItem(props.item))}
          >
            {props.isSelected ? (
              <IconFeader name="check" size={20} color={colors.white} />
            ) : (
              <Text />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  item: {
    width: '100%',
    borderRadius: 10,
    flex: 1,
    flexDirection: 'row',
    height: 79,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  leftBox: {
    flex: 1,
    paddingLeft: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerBox: {
    flex: 5,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  rightBox: {
    flex: 1,
    paddingRight: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.fontBold,
    color: colors.black,
  },
  addressLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  selectedCheckbox: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.goodGreen,
    width: 26,
    height: 26,
    borderRadius: 1,
  },
  unselectedCheckbox: {
    width: 26,
    height: 26,
    borderRadius: 1,
    borderWidth: 2,
    borderColor: colors.subGrey,
  },
});
export default LocationListItem;
