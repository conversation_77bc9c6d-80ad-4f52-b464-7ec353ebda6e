import React from 'react';
import {
  View, StyleSheet, Text, Image,
} from 'react-native';
import { colors } from '../../../../styles';

const EmptyLocationList = (props) => (
  <View style={styles.container}>
    {props.hasIcon ? (
      <Image source={props.hasIcon} style={styles.image} />
    ) : null}
    <Text style={styles.text}>{props.message}</Text>
    {props.subTitle ? (
      <Text style={styles.subtitle}>{props.subTitle}</Text>
    ) : null}
  </View>
);
const styles = StyleSheet.create({
  container: {
    marginTop: 200,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  text: {
    color: colors.black,
    fontSize: 22,
    textAlign: 'center',
    fontFamily: 'Avenir-Roman',
  },
  subtitle: {
    color: colors.fillDarkGrey,
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Avenir-Roman',
    maxWidth: 279,
  },
  image: {
    marginBottom: 10,
  },
});
export default EmptyLocationList;
