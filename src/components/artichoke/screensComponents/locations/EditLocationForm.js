import React from 'react';
import { Formik } from 'formik';
import {
  View, StyleSheet, ScrollView, Text,
} from 'react-native';
import FormSimpleTextInput from '../../common/FormSimpleTextInput';
import FormTextInputUnderline from '../../common/FormTextInputUnderline';
import FormGroup from '../../common/FormGroup';
import FormGoogleAutocomplete from '../../common/FormGoogleAutocomplete';
import FormSubmitButton from '../../common/FormSubmitButton';
import * as validations from '../../../../util/validations';
import MyLocationForm from '../hoursLocations/MyLocationForm';
import { colors } from '../../../../styles';

const EditLocationForm = (props) => (
  <ScrollView style={styles.container}>
    <Formik
      validateOnMount
      innerRef={props.refForm}
      initialValues={props.initialValues}
      onSubmit={(values) => props.onSubmitForm(values)}
    >
      {({
        handleBlur, setFieldValue, values, errors,
      }) => (
        <View style={styles.form}>
          <FormGroup>
            <FormTextInputUnderline
              onChangeText={(value) => {
                props.setSelectedLocationForEditValuesAction({
                  key: 'addressName',
                  value,
                });
                setFieldValue('addressName', value);
              }}
              value={values.addressName}
              label="Location Name"
              placeholder="location name"
              name="addressName"
              bottomLabel="Name your location so client can easily recognize where to arrive after booking a session."
              errors={validations.addressNameValidation(values.addressName)}
            />
            <FormGoogleAutocomplete
              onSelectValue={(value) => {
                if (typeof value === 'object') {
                  let street = '';
                  value.address_components.forEach((item) => {
                    if (item.types[0] === 'street_number') {
                      street += item.short_name;
                      street += ' ';
                      props.setSelectedLocationForEditValuesAction({
                        key: 'address1',
                        value: street,
                      });
                      setFieldValue('address1', street);
                    }
                    if (item.types[0] === 'route') {
                      street += item.short_name;
                      props.setSelectedLocationForEditValuesAction({
                        key: 'address1',
                        value: street,
                      });
                      setFieldValue('address1', street);
                    }
                    if (
                      item.types[0] === 'locality'
                          || item.types[0] === 'postal_town'
                    ) {
                      props.setSelectedLocationForEditValuesAction({
                        key: 'city',
                        value: item.short_name,
                      });
                      setFieldValue('city', item.short_name);
                    }
                    if (item.types[0] === 'administrative_area_level_1') {
                      props.setSelectedLocationForEditValuesAction({
                        key: 'state',
                        value: item.short_name,
                      });
                      setFieldValue('state', item.short_name);
                    }
                  });
                } else {
                  props.setSelectedLocationForEditValuesAction({
                    key: 'address1',
                    value,
                  });
                  setFieldValue('address1', value);
                }
              }}
              onBlur={handleBlur('address1')}
              inputValue={values.address1}
              placeholder="Address"
              name="address1"
            />
            {validations.address1Validation(values.address1).address1 && (
              <Text
                style={{
                  display: 'flex',
                  width: '100%',
                  fontSize: 10,
                  color: 'red',
                }}
              >
                {validations.address1Validation(values.address1).address1}
              </Text>
            )}
          </FormGroup>
          <FormGroup>
            <FormSimpleTextInput
              onChangeText={(value) => {
                props.setSelectedLocationForEditValuesAction({
                  key: 'address2',
                  value,
                });
                setFieldValue('address2', value);
              }}
              onBlur={handleBlur('address2')}
              value={values.address2}
              placeholder="Apt, suite or room #"
              name="address2"
            />
          </FormGroup>
          <FormGroup>
            <FormSimpleTextInput
              onChangeText={(value) => {
                props.setSelectedLocationForEditValuesAction({
                  key: 'city',
                  value,
                });
                setFieldValue('city', value);
              }}
              onBlur={handleBlur('city')}
              value={values.city}
              placeholder="city"
              name="city"
            />
            {validations.cityValidation(values.city).city && (
              <Text
                style={{
                  display: 'flex',
                  width: '100%',
                  fontSize: 10,
                  color: 'red',
                }}
              >
                {validations.cityValidation(values.city).city}
              </Text>
            )}
          </FormGroup>
          <FormGroup formStyle={{ flexDirection: 'row', flex: 1 }}>
            <View style={{ flex: 1, width: '50%', marginRight: '2%' }}>
              <FormSimpleTextInput
                onChangeText={(value) => {
                    props.setSelectedLocationForEditValuesAction({
                      key: 'state',
                      value,
                    });
                    setFieldValue('state', value);
                  }}
                onBlur={handleBlur('state')}
                value={values.state}
                placeholder="State"
                name="state"
              />
            </View>
            <View style={{ flex: 1, width: '50%', marginLeft: '2%' }}>
              <FormSimpleTextInput
                onChangeText={(value) => {
                    props.setSelectedLocationForEditValuesAction({
                      key: 'postalCode',
                      value,
                    });
                    setFieldValue('postalCode', value);
                  }}
                onBlur={handleBlur('postalCode')}
                value={values.postalCode}
                placeholder="Zip"
                name="postalCode"
              />
            </View>
          </FormGroup>
          <MyLocationForm
            selfBookings={props.initialValues}
            setSelectedLocationForEditValuesAction={
                  props.setSelectedLocationForEditValuesAction
                }
          />
          <FormGroup formStyle={styles.bottomSubmitButton}>
            <FormSubmitButton
              title="Delete Location"
              onPressButton={() => props.onDeleteLocation()}
              buttonStyle={{
                backgroundColor: colors.white,
                borderColor: colors.subGrey,
              }}
              buttonLabelStyle={{ color: colors.subGrey }}
            />
          </FormGroup>
        </View>
      )}
    </Formik>
  </ScrollView>
);
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
  image: {
    display: 'flex',
    height: 150,
  },
  bottomSubmitButton: {
    display: 'flex',
    marginTop: 50,
    borderRadius: 30,
  },
  error: {
    display: 'flex',
    width: '100%',
    fontSize: 10,
    color: 'red',
  },
  gPlacesContainer: {
    position: 'relative',
    alignSelf: 'stretch',
    margin: 0,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    height: 35,
    shadowOpacity: 0,
    borderColor: colors.lightgrey,
    borderWidth: 1,
    marginBottom: 10,
  },
  gPlacesInput: {
    backgroundColor: colors.white,
    height: 35,
  },
  gPlacesList: {
    borderColor: colors.lightgrey,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    left: 0,
    right: 0,
    opacity: 1,
  },
});
export default EditLocationForm;
