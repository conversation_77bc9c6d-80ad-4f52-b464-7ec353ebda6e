import React, { useState } from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';

const SelectLocationsListItems = (props) => {
  const addressLine = props.item.address2 !== ''
    ? `${props.item.address1}, ${props.item.address2}, ${props.item.city}, ${props.item.state}`
    : `${props.item.address1}, ${props.item.city}, ${props.item.state}`;
  return (
    <TouchableOpacity onPress={() => props.selectItem(props.item)}>
      <View style={styles.item} key={props.item.id}>
        <View style={styles.leftBox}>
          <IconFeader name="map-pin" size={30} color={colors.subGrey} />
        </View>
        <View style={styles.centerBox}>
          <Text style={styles.addressName}>{props.item.addressName}</Text>
          <Text style={styles.addressLocation}>
            {!props.item.city && !props.item.state ? '' : addressLine}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    flexDirection: 'row',
    marginVertical: 10,
    paddingRight: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftBox: {
    flex: 1,
    alignItems: 'center',
  },
  centerBox: {
    flex: 5,
    flexDirection: 'column',
  },
  addressName: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  addressLocation: {
    fontSize: 14,
    marginBottom: 10,
    color: colors.subGrey,
  },
});
export default SelectLocationsListItems;
