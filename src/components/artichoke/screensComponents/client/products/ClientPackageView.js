import React, { Component } from 'react';
import {
  ScrollView,
  ImageBackground,
  View,
  StyleSheet,
  Text,
  Image,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { colors } from '../../../../../styles';
import {
  APPLICATION_CLIENT_ROUTES,
  APPLICATION_ROUTES,
} from '../../../../../constants';
import { generalStyles } from '../../../../../styles/generalStyle';
import FormGroup from '../../../common/FormGroup';
import TotalDetailsItem from '../../../common/TotalDetailsItem';
import FormGeneral from '../../../common/FormGeneral';
import SubTotalDetailsItemPackage from '../../../common/SubTotalDetailsItemPackage';
import FormCreditCardInfo from '../../../common/FormCreditCardInfo';
import FormSubmitButtonWithSpinner from '../../../common/FormSubmitButtonWithSpinner';
import { track } from '../../../../../util/Analytics';

const bgImage = require('../../../../../assets/imgServiceBackground.png');
const closeIcon = require('../../../../../assets/closeCircle.png');
const defaultProfile = require('../../../../../resources/defaultProfile.png');

class ClientPackageView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      overDiscount: false,
    };
  }

  buyPackage = () => {
    this.props.clientBuyPackage();
  };

  overDiscountAlert = (value) => {
    if (!this.state.overDiscount && !value) {
      this.setState({
        overDiscount: true,
      });
      Alert.alert(
        'Info',
        'You are offering a discount greater than the price. The final amount will be 0',
        [
          {
            text: 'Ok',
          },
        ],
        { cancelable: true },
      );
    } else if (this.state.overDiscount && value) {
      this.setState({
        overDiscount: false,
      });
    }
  };

  calculateTotalValue = (packageProducts) => {
    let totalPrice = 0.0;
    packageProducts.map((productItem) => {
      totalPrice += productItem.price * productItem.quantity;
      return totalPrice;
    });
    return totalPrice.toFixed(2);
  };

  showOfferedSessions = (packService) => {
    if (packService.unlimited) {
      return 'Unlimited';
    }

    return packService.quantity > 1
      ? `${packService.quantity} sessions`
      : `${packService.quantity} session`;
  };

  showPaymentExpireValue = (expireType, expireDays) => {
    if (expireType === 'NEVER') {
      return 'Never';
    }
    if (expireType === 'PERIODEND') {
      return 'On Next Payment Date';
    }
    if (expireType === 'CUSTOM') {
      return `After ${expireDays} Days`;
    }
    return '';
  };

  showPaymentIntervalValue = (value) => {
    switch (value) {
      case 'ONETIME':
        return 'Once';
      case 'WEEKLY':
        return 'Every Week';
      case 'BIWEEKLY':
        return 'Every Other Week';
      case 'MONTHLY':
        return 'Every Month';
      case 'ANNUALLY':
        return 'Every Year';
      default:
        return '';
    }
  };

  renderCreditCard = () => {
    if (
      this.props.user.account.StripeAccount
      && this.props.user.account.StripeAccount.stripeSecretKey
    ) {
      if (
        this.props.clientCreditCard
        && this.props.clientCreditCard.creditCardType
        && this.props.clientCreditCard.lastFour
      ) {
        return (
          <FormCreditCardInfo
            customStyle={generalStyles.mVertical10}
            value={`${this.props.clientCreditCard.creditCardType} ...${this.props.clientCreditCard.lastFour}`}
            onPressAction={() => this.props.navigation.navigate(APPLICATION_ROUTES.ADD_CREDIT_CARD)}
          />
        );
      }
      return (
        <FormGeneral
          label="Add payment method"
          formInputStyle={generalStyles.mVertical10}
          value=""
          onPressFunc={() => this.props.navigation.navigate({
            name: APPLICATION_CLIENT_ROUTES.ADD_CREDIT_CARD,
          })}
        />
      );
    }
    return (
      <Text
        style={{
          ...generalStyles.mVertical10,
          ...generalStyles.avenirRoman17,
        }}
      >
        No payment method available.
      </Text>
    );
  };

  disabled = () => {
    if (this.props.item?.packageProducts?.length < 1) {
      return true;
    }
    if (this.props.item.price > 0) {
      if (
        !this.props.user.account?.StripeAccount?.stripeSecretKey
        || (!this.props.clientCreditCard?.creditCardType
          && !this.props.clientCreditCard?.lastFour)
      ) {
        return true;
      }
    }
    return false;
  };

  render() {
    const trainerAvatarUrl = this.props.trainer.avatar_url;
    const trainerImg = trainerAvatarUrl && trainerAvatarUrl.includes('?')
      ? trainerAvatarUrl.split('?')[0]
      : trainerAvatarUrl;

    return (
      <View style={styles.container}>
        <ScrollView style={{ ...styles.item, paddingTop: 0 }}>
          <View style={styles.header}>
            <ImageBackground source={bgImage} style={styles.image}>
              <View style={generalStyles.overlayWithoutRadius} />
            </ImageBackground>

            <TouchableOpacity
              style={{
                marginTop: 50,
                marginLeft: 20,
                width: 35,
                height: 35,
              }}
              onPress={() => this.props.navigation.goBack()}
            >
              <Image source={closeIcon} style={{ width: 35, height: 35 }} />
            </TouchableOpacity>
            <View style={styles.headerName}>
              <Text style={styles.title}>{this.props.item.name}</Text>
            </View>
            <View style={styles.headerPrice}>
              <Text style={styles.headerPriceText}>
                {this.props.symbol}
                {parseFloat(this.props.item.price)}
              </Text>
            </View>
            <Text style={styles.headerTotalValue}>
              Total Value
              {' '}
              {this.props.symbol}
              {this.calculateTotalValue(this.props.item.packageProducts)}
            </Text>
          </View>
          <View style={styles.content}>
            <View style={styles.contentUser}>
              <View style={styles.userImageContainer}>
                <Image
                  source={trainerImg ? { uri: trainerImg } : defaultProfile}
                  style={styles.userImage}
                />
              </View>
              <View style={styles.userDetails}>
                <Text style={styles.userName}>
                  {this.props.trainer.first_name || this.props.trainer.last_name
                    ? `${this.props.trainer.first_name} ${this.props.trainer.last_name}`
                    : ''}
                </Text>
                <Text style={styles.userJob}>Fitness Professional</Text>
              </View>
            </View>
            {this.props.item.packageDescription ? (
              <View style={styles.packageDescription}>
                <Text style={styles.description}>
                  {this.props.item.packageDescription}
                </Text>
              </View>
            ) : null}
            <View style={styles.packageServices}>
              <Text style={styles.servicesLabel}>Services Offering</Text>
              {this.props.item.packageProducts.length
                ? this.props.item.packageProducts.map((packService) => (
                  <View key={packService.productId}>
                    <FormGroup formStyle={styles.packageServicesStyle}>
                      <View style={{ flex: 3 }}>
                        <Text
                          style={{
                            ...generalStyles.avenirRoman17,
                            color: colors.subGrey,
                          }}
                        >
                          {packService.productName}
                          {' '}
                          (
                          {packService.duration}
                          {' '}
                          min)
                        </Text>
                      </View>
                      <View style={{ flex: 1, alignItems: 'flex-end' }}>
                        <Text
                          style={{
                            ...generalStyles.avenirRoman17,
                            color: colors.subGrey,
                          }}
                        >
                          {this.showOfferedSessions(packService)}
                        </Text>
                      </View>
                    </FormGroup>
                  </View>
                ))
                : null}
            </View>
            <View style={styles.packageServices}>
              <Text style={styles.servicesLabel}>Payment</Text>
              <FormGroup formStyle={styles.packageServicesStyle}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    Payment Interval
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    {this.showPaymentIntervalValue(
                      this.props.item.paymentInterval,
                    )}
                  </Text>
                </View>
              </FormGroup>
              <FormGroup formStyle={styles.packageServicesStyle}>
                <View style={{ flex: 3 }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    Payment Count
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    {this.props.item.repeatCount !== 99999
                      ? this.props.item.repeatCount
                      : 'Unlimited'}
                  </Text>
                </View>
              </FormGroup>
              <FormGroup formStyle={styles.packageServicesStyle}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    Payment Expire
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                  <Text
                    style={{
                      ...generalStyles.avenirRoman17,
                      color: colors.subGrey,
                    }}
                  >
                    {this.showPaymentExpireValue(
                      this.props.item.expireType,
                      this.props.item.expireDaysCustom,
                    )}
                  </Text>
                </View>
              </FormGroup>
              <View style={{ marginTop: 12 }}>
                {this.disabled() ? (
                  <Text style={styles.required}>* Required</Text>
                ) : null}
                {this.renderCreditCard()}
              </View>
              <SubTotalDetailsItemPackage
                price={this.props.item.price}
                tax={this.props.item.tax}
                priceWithTax={this.props.item.priceWithTax}
                discount={0}
                balance={0}
                useBalance={false}
                usageStyle={{ paddingHorizontal: 0 }}
                symbol={this.props.symbol}
              />
            </View>
          </View>
        </ScrollView>
        <View formStyle={styles.bottomSubmitButton}>
          <TotalDetailsItem
            price={this.props.item.price}
            tax={this.props.item.tax}
            priceWithTax={this.props.item.priceWithTax}
            discount={0}
            balance={0}
            useBalance={false}
            styleLabelTotal={{ marginLeft: 21 }}
            styleValueTotal={{ marginRight: 20 }}
            symbol={this.props.symbol}
            overDiscountAlert={this.overDiscountAlert}
          />
          <View style={styles.packageServices}>
            <FormSubmitButtonWithSpinner
              disabled={this.disabled()}
              style={{ width: '100%', marginHorizontal: 0, marginTop: 20 }}
              title="Buy Package"
              onPressButton={() => {
                this.buyPackage();
                track('package_purchase');
              }}
              isLoading={this.props.bookPackageLoader}
              buttonStyle={this.disabled() && styles.disabledButton}
            />
          </View>
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  item: {
    flex: 1,
    flexDirection: 'column',
    width: '100%',
  },
  header: {
    display: 'flex',
    flexDirection: 'column',
    height: 250,
  },
  headerName: {
    ...generalStyles.avenirBlack24,
    marginTop: 14,
    width: '100%',
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  headerTotalValue: {
    flex: 1,
    alignItems: 'center',
    color: colors.white,
    ...generalStyles.avenirRoman13,
    alignSelf: 'flex-end',
    justifyContent: 'center',
    marginRight: 19,
    marginTop: 8,
    lineHeight: 22,
  },
  headerPrice: {
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.goodGreen,
    borderRadius: 30,
    height: 38,
    width: 62,
    marginRight: 19,
  },
  headerPriceText: {
    color: colors.white,
    ...generalStyles.durationText,
  },
  content: {
    flex: 1,
    flexDirection: 'column',
  },
  contentUser: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'flex-start',
  },

  userImageContainer: {
    justifyContent: 'center',
    marginRight: 15,
  },
  userImage: {
    width: 50,
    height: 50,
    borderColor: colors.white,
    borderRadius: 25,
  },
  userDetails: {
    flex: 5,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
  },
  userJob: {
    display: 'flex',
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    ...generalStyles.avenirHeavy14,
    color: colors.black,
  },
  packageDescription: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  description: {
    fontSize: 16,
    ...generalStyles.avenirRoman17,
  },
  packageServices: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  packageServicesStyle: {
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: 0,
    marginBottom: 5,
    marginTop: 0,
  },
  servicesLabel: {
    ...generalStyles.avenirHeavy17,
    marginBottom: 8,
    color: 'black',
  },
  title: {
    ...generalStyles.titleService,
    color: colors.white,
    marginTop: 10,
  },
  bottomSubmitButton: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    marginTop: 55,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    borderColor: colors.bordergrey,
    backgroundColor: colors.lightgrey,
  },
  required: {
    color: colors.dustyRed,
    marginTop: 10,
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    fontStyle: 'italic',
  },
});
export default ClientPackageView;
