import React from 'react';
import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../../styles';
import { APPLICATION_CLIENT_ROUTES } from '../../../../../constants';
import { generalStyles } from '../../../../../styles/generalStyle';

const bgImage = require('../../../../../assets/imgServiceBackground.png');

const PackageListItem = (props) => (
  <TouchableOpacity
    key={props.item.id}
    style={styles.item}
    onPress={() => props.navigation.navigate(APPLICATION_CLIENT_ROUTES.CLIENT_PACKAGE_VIEW, {
      packageId: props.item.id.toString(),
    })}
  >
    <ImageBackground
      source={bgImage}
      style={styles.image}
      imageStyle={{ borderRadius: 20 }}
    >
      <View style={styles.topBox}>
        <View style={styles.topBoxLeft}>
          <Text style={generalStyles.priceText}>
            {props.symbol}
            {parseFloat(props.item.price)}
          </Text>
        </View>
      </View>

      <View style={styles.titleBox}>
        <View style={styles.topTitleBox}>
          <Text style={generalStyles.serviceNameText}>{props.item.name}</Text>
        </View>
        <View style={styles.actionBox}>
          <View style={{ ...styles.button, ...styles.rightButton }}>
            <Text
              style={styles.buttonText}
              onPress={() => props.navigation.navigate(
                APPLICATION_CLIENT_ROUTES.CLIENT_PACKAGE_VIEW,
                {
                  packageId: props.item.id.toString(),
                },
              )}
            >
              Buy
            </Text>
          </View>
        </View>
      </View>
    </ImageBackground>
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  image: {
    flex: 1,
  },
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 20,
    flexDirection: 'column',
    minHeight: 120,
  },
  topBox: {
    display: 'flex',
    height: 50,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  titleBox: {
    display: 'flex',
    textAlign: 'center',
    flexDirection: 'row',
    marginHorizontal: 30,
    marginBottom: 20,
  },
  topBoxLeft: {
    backgroundColor: colors.goodGreen,
    color: colors.white,
    fontWeight: 'bold',
    width: 80,
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 15,
    paddingRight: 15,
    borderTopLeftRadius: 20,
    borderBottomRightRadius: 5,
  },
  topTitleBox: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    width: 200,
  },
  actionBox: {
    flex: 0.5,
    flexDirection: 'row',
    display: 'flex',
  },
  button: {
    backgroundColor: 'transparent',
    flex: 1,
    display: 'flex',
  },
  buttonText: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 14,
    borderColor: colors.white,
    borderWidth: 2,
    borderRadius: 18,
    paddingHorizontal: 25,
    paddingTop: 7,
    height: 36,
    margin: 'auto',
  },
  rightButton: {
    alignItems: 'flex-end',
  },
});
export default PackageListItem;
