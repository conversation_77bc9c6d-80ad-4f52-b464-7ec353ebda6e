import React from 'react';
import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../../../../styles';
import { generalStyles } from '../../../../../styles/generalStyle';
import { track } from '../../../../../util/Analytics';

const bgImage = require('../../../../../assets/imgServiceBackground.png');

const ArchivedPackageListItem = (props) => (
  <TouchableOpacity
    style={styles.item}
    key={props.item.id}
    onPress={() => props.onGoToViewPackageScreen(props.item)}
  >
    <ImageBackground
      source={bgImage}
      style={styles.image}
      imageStyle={{ borderRadius: 20 }}
    >
      <View style={generalStyles.overlay} />
    </ImageBackground>
    <View style={styles.topBox}>
      <View style={styles.topBoxLeft}>
        <Text style={generalStyles.priceText}>
          {props.symbol}
          {Math.round(props.item.price)}
        </Text>
      </View>
    </View>

    <View style={styles.titleBox}>
      <View style={styles.topTitleBox}>
        <Text style={generalStyles.serviceNameText}>{props.item.name}</Text>
      </View>
      <View style={styles.actionBox}>
        <TouchableOpacity
          style={{ ...styles.button, ...styles.rightButton }}
          onPress={() => {
            props.onRestorePackage(props.item);
            track('package_restored');
          }}
        >
          <Text style={styles.buttonLabel}>Restore</Text>
        </TouchableOpacity>
      </View>
    </View>
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  image: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  item: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 20,
    flexDirection: 'column',
    minHeight: 118,
  },
  topBox: {
    display: 'flex',
    height: 50,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  titleBox: {
    display: 'flex',
    textAlign: 'center',
    flexDirection: 'row',
    marginHorizontal: 30,
    marginBottom: 20,
  },
  topBoxLeft: {
    backgroundColor: colors.goodGreen,
    color: colors.white,
    fontWeight: 'bold',
    width: 65,
    height: 37,
    alignItems: 'center',
    justifyContent: 'center',
    borderTopLeftRadius: 20,
    borderBottomRightRadius: 5,
  },
  topTitleBox: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    width: 200,
  },
  actionBox: {
    flex: 0.5,
    flexDirection: 'row',
    display: 'flex',
  },
  button: {
    backgroundColor: 'transparent',
    flex: 1,
    display: 'flex',
  },
  buttonLabel: {
    color: colors.white,
    textAlign: 'center',
    margin: 'auto',
    height: 36,
    minWidth: 85,
    lineHeight: 32,
    borderColor: colors.white,
    borderWidth: 2,
    borderRadius: 18,
    ...generalStyles.avenirHeavy17,
    paddingHorizontal: 15,
  },
  rightButton: {
    alignItems: 'flex-end',
  },
});
export default ArchivedPackageListItem;
