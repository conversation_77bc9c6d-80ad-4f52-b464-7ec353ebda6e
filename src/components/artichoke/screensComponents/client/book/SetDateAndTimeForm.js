import React, { useState } from 'react';
import { Formik } from 'formik';
import {
  View,
  StyleSheet,
  Text,
  FlatList,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import FormGroup from '../../../common/FormGroup';
import SelectBookingTimeForm from '../../../common/SelectBookingTimeForm';
import DateTimeSuggestionForm from '../../../common/DateTimeSuggestionForm';
import FormTouchableDropdown from '../../../common/FormTouchableDropdown';
import { generalStyles } from '../../../../../styles/generalStyle';
import { colors } from '../../../../../styles';

const SetDateAndTimeForm = (props) => {
  const selDate = props.selectedDate
    ? new Date(props.selectedDate)
    : new Date();
  const [date, setDate] = useState(selDate);
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);

  const onSelectDate = (selectedDate) => {
    setShow(false);
    setDate(selectedDate);
    props.onChangeDate(selectedDate);
  };
  const onSelectTime = (time) => {
    props.onChangeTime(time);
  };

  const showDatepicker = () => {
    setShow(true);
    setMode('date');
  };

  const hideDatePicker = () => {
    setShow(false);
  };
  const availability = props.availability.filter(
    (availableSlot) => availableSlot.date === moment(props.selectedDate).format('YYYY-M-D'),
  );

  const renderFooter = () => (props.sliceAvailableSlots.length === props.availability.length ? null : (
    <View style={styles.footerStyle}>
      <TouchableOpacity onPress={() => props.paginateData()}>
        <Text style={styles.textShowMore}>Show more</Text>
      </TouchableOpacity>
    </View>
  ));

  const renderTimeSlots = (amSlots, pmSlots) => (
    <ScrollView>
      <FormGroup formStyle={styles.availableTimes}>
        <Text style={styles.label}>Available Times</Text>
        <Text style={{ ...styles.label, ...{ marginTop: 15 } }}>AM</Text>
        <View style={styles.selectcontainer}>
          {amSlots.length ? (
            amSlots.map((slot, index) => {
              const isBlocked = props.blockingAppointments.filter(
                (appointment) => {
                  const startTime = moment(
                    appointment.start,
                    'MM/DD/YYYY hh:mm A',
                  ).format('HH:mm');

                  const endTime = moment(
                    appointment.end,
                    'MM/DD/YYYY hh:mm A',
                  ).format('HH:mm');
                  return (
                    moment(slot, 'HH:mm').isBefore(moment(endTime, 'HH:mm'))
                    && moment(slot, 'HH:mm').isSameOrAfter(
                      moment(startTime, 'HH:mm'),
                    )
                  );
                },
              ).length;
              return (
                <SelectBookingTimeForm
                  key={`select-start-times-slot-key-${index}`}
                  buttonTitle={moment(slot, 'HH:mm')
                    .format('hh:mm A')
                    .replace('AM', '')}
                  width="25%"
                  isBlocked={isBlocked}
                  selected={props.selectedTime === slot}
                  onChangeValue={() => onSelectTime(slot)}
                />
              );
            })
          ) : (
            <Text style={generalStyles.smallText}>No available times</Text>
          )}
          <Text style={{ ...styles.label, marginTop: 40 }}>PM</Text>
          {pmSlots.length ? (
            pmSlots.map((slot, index) => {
              const isBlocked = props.blockingAppointments.filter(
                (appointment) => {
                  const startTime = moment(
                    appointment.start,
                    'MM/DD/YYYY hh:mm A',
                  ).format('HH:mm');

                  const endTime = moment(
                    appointment.end,
                    'MM/DD/YYYY hh:mm A',
                  ).format('HH:mm');
                  return (
                    moment(slot, 'HH:mm').isBefore(moment(endTime, 'HH:mm'))
                    && moment(slot, 'HH:mm').isSameOrAfter(
                      moment(startTime, 'HH:mm'),
                    )
                  );
                },
              ).length;
              return (
                <SelectBookingTimeForm
                  key={`select-start-times-slot-key-${index}`}
                  buttonTitle={moment(slot, 'HH:mm')
                    .format('hh:mm A')
                    .replace('PM', '')}
                  width="25%"
                  isBlocked={isBlocked}
                  selected={props.selectedTime === slot}
                  onChangeValue={() => onSelectTime(slot)}
                />
              );
            })
          ) : (
            <Text style={generalStyles.smallText}>No available times</Text>
          )}
        </View>
      </FormGroup>
    </ScrollView>
  );

  const renderSugestionSlots = (availableSlot) => (
    <View style={styles.form}>
      <FormGroup>
        <FlatList
          style={{ width: '100%' }}
          numColumns={2}
          keyExtractor={(item, index) => index}
          data={availableSlot}
          renderItem={({ item, index }) => (
            <DateTimeSuggestionForm
              key={`select-date-slot-key-${index}`}
              buttonTitle={moment(item.date, 'YYYY-M-D').format('ddd, MMM Do')}
              width="50%"
              onChangeValue={() => onSelectDate(new Date(moment(item.date, 'YYYY-M-D')))}
            />
          )}
          ListHeaderComponent={(
            <View>
              <Text style={styles.info}>
                Sorry, It seems that there are no available time slots for the
                selected date.
              </Text>
              <Text style={[styles.label, { marginBottom: 20 }]}>
                Days with available slots:
              </Text>
            </View>
          )}
          ListFooterComponent={renderFooter}
        />
      </FormGroup>
    </View>
  );

  const timeSlots = availability.length ? availability[0].availableSlots : [];
  const amSlots = timeSlots.filter((slot) => moment(slot, 'HH:mm').isBefore(moment('12:00', 'HH:mm')));
  const pmSlots = timeSlots.filter((slot) => moment(slot, 'HH:mm').isSameOrAfter(moment('12:00', 'HH:mm')));
  return (
    <View style={styles.container}>
      <Formik onSubmit={(values) => props.onSubmitForm(values)}>
        {() => (
          <View style={styles.form}>
            <FormGroup formStyle={styles.formStyle}>
              <Text style={styles.label}>What Day</Text>
            </FormGroup>
            <FormGroup formStyle={styles.formStyle}>
              <FormTouchableDropdown
                label="Date"
                value={moment(props.selectedDate).format('L')}
                onPressFunc={showDatepicker}
              />
            </FormGroup>
            {!amSlots.length && !pmSlots.length
              ? renderSugestionSlots(props.sliceAvailableSlots)
              : renderTimeSlots(amSlots, pmSlots)}
            <View>
              <DateTimePickerModal
                date={date}
                mode={mode}
                is24Hour
                display="default"
                isVisible={show}
                onConfirm={onSelectDate}
                onCancel={hideDatePicker}
              />
            </View>
          </View>
        )}
      </Formik>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    margin: 0,
    flexDirection: 'column',
  },
  availableTimes: {
    marginTop: '5%',
  },
  form: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 20,
  },
  label: {
    fontWeight: 'bold',
    fontSize: 25,
    width: '100%',
    color: colors.black,
  },
  info: {
    fontSize: 14,
    color: colors.bordergrey,
    marginBottom: 30,
    marginTop: 0,
  },
  selectcontainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    flex: 1,
    marginTop: 20,
  },
  footerStyle: {
    padding: 7,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textShowMore: {
    color: colors.nasmBlue,
    textDecorationLine: 'underline',
    fontSize: 16,
  },
});
export default SetDateAndTimeForm;
