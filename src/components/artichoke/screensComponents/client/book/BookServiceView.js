import React, { Component, Fragment } from 'react';
import {
  ScrollView,
  ImageBackground,
  View,
  StyleSheet,
  Text,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import _cloneDeep from 'lodash.clonedeep';
import moment from 'moment';
import AwesomeAlert from 'react-native-awesome-alerts';
import {
  APPLICATION_CLIENT_ROUTES,
  APPLICATION_ROUTES,
} from '../../../../../constants';
import FormSubmitButton from '../../../common/FormSubmitButton';
import { serviceViewStyles } from '../../../../../styles/serviceViewStyle';
import FormTouchableDropdown from '../../../common/FormTouchableDropdown';
import { generalStyles } from '../../../../../styles/generalStyle';
import { deleteAlertStyles } from '../../../../../styles/alertStyle';
import FormCreditCardInfo from '../../../common/FormCreditCardInfo';
import FormGeneral from '../../../common/FormGeneral';
import { colors } from '../../../../../styles';
import FormSubmitButtonWithSpinner from '../../../common/FormSubmitButtonWithSpinner';
import ContentLoading from '../../../../ContentLoading';
import SubTotalDetailsItem from '../../../common/SubTotalDetailsItem';

const bgImage = require('../../../../../assets/imgServiceBackground.png');
const infoIcon = require('../../../../../assets/darkGray.png');
const closeIcon = require('../../../../../assets/closeCircle.png');
const defaultProfile = require('../../../../../resources/defaultProfile.png');

class BookServiceView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
      imageLoading: true,
      serviceImage: null,
    };
  }

  componentDidMount() {
    this.unsubscribeFocus = this.props.navigation.addListener('focus', () => {
      this.setState({ serviceImage: null });
    });
  }

  componentDidUpdate(prevProps) {
    if (prevProps.loader && !this.props.loader) {
      this.setServiceImage();
      this.preselectOneLocation();
    }
  }

  preselectOneLocation = () => {
    const items = this.props.item.selfBookingAddresses
      ? _cloneDeep(this.props.item.selfBookingAddresses)
      : [];
    if (this.props.item.clientLocationEnabled) {
      items.push({
        id: 0,
        addressName: "Client's location",
        checked: true,
      });
    }
    if (this.props.item.offeredOnline) {
      items.push({
        id: 1,
        addressName: 'Remotely',
        checked: true,
      });
    }
    if (this.props.item.inappEnabled) {
      items.push({
        id: 2,
        addressName: 'Video Call',
        checked: true,
      });
    }
    if (items.length === 1) {
      this.props.setNewAppointmentValuesAction({
        key: 'appointmentLocation',
        value: items[0],
      });
      const currentDate = moment().toString();
      this.props.getAppointmentsAvailabilityAction(currentDate);
    }
  };

  setServiceImage = () => {
    const servImage = this.props.item.serviceImage
      ? { uri: this.props.item.serviceImage }
      : bgImage;
    this.setState({ serviceImage: servImage });
  };

  showAlert = () => {
    this.setState({
      showAlert: true,
    });
  };

  hideAlert = () => {
    this.setState({
      showAlert: false,
    });
  };

  showDateAndTimeInfo = (item) => {
    let dateTime = 'Choose a Time';
    if (item.date && item.time) {
      dateTime = `${moment(item.date).format('L')} ${moment(item.time, [
        'hh:mm',
      ]).format('hh:mm a')}`;
    }
    return dateTime;
  };

  checkAvailableBalance = () => {
    let availableBalance = null;
    if (
      this.props.clientBalance !== 'undefined'
      && this.props.clientBalance.length !== 0
    ) {
      for (const item in this.props.clientBalance) {
        if (
          item.clientPurchaseProductDurations
          && item.clientPurchaseProductDurations.length
        ) {
          if (
            item.clientPurchaseProductDurations[0].quantity !== '0'
            && item.clientPurchaseProductDurations[0].ProductDuration.id
              === this.props.item.ProductDuration[0].id
          ) {
            [availableBalance] = item.clientPurchaseProductDurations;
          }
        }
      }
    }
    return availableBalance;
  };

  renderBalance = () => {
    if (this.checkAvailableBalance() !== null) {
      return (
        <View styles={generalStyles.mVertical10}>
          <Text style={styles.locationsLabel}>Available Balance</Text>
          <View style={styles.balanceBox}>
            <Text style={styles.balanceText}>
              Service covered under available balances
            </Text>
            <TouchableOpacity
              style={styles.infoIcon}
              onPress={() => this.showAlert()}
            >
              <Image source={infoIcon} />
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return null;
  };

  renderCreditCard = () => {
    if (
      this.props.user.account.StripeAccount
      && this.props.user.account.StripeAccount.stripeSecretKey
    ) {
      if (
        this.props.clientCreditCard
        && this.props.clientCreditCard.creditCardType
        && this.props.clientCreditCard.lastFour
      ) {
        return (
          <FormCreditCardInfo
            customStyle={generalStyles.mVertical10}
            value={`${this.props.clientCreditCard.creditCardType} ...${this.props.clientCreditCard.lastFour}`}
            onPressAction={() => this.props.navigation.navigate(APPLICATION_ROUTES.ADD_CREDIT_CARD)}
          />
        );
      }
      return (
        <FormGeneral
          label="Add payment method"
          formInputStyle={generalStyles.mVertical10}
          value=""
          onPressFunc={() => this.props.navigation.navigate({
            name: APPLICATION_CLIENT_ROUTES.ADD_CREDIT_CARD,
          })}
        />
      );
    }
    return null;
  };

  showTimeAndDurationInfo = (item) => {
    if (this.props.item?.ProductDuration) {
      let durationTime = `${this.props.item.ProductDuration[0].duration.duration} ${this.props.item.ProductDuration[0].duration.granularity.abbreviation}`;
      if (item.date && item.time) {
        durationTime = `${moment(item.time, ['hh:mm']).format('hh:mm a')} (${
          this.props.item.ProductDuration[0].duration.duration
        } ${
          this.props.item.ProductDuration[0].duration.granularity.abbreviation
        })`;
      }
      return durationTime;
    }
    return null;
  };

  canProceed = () => {
    if (this.props.item.ProductDuration[0].price === '0.0') {
      if (
        !this.props.newAppointment.appointmentLocation
        || !this.props.newAppointment.date
      ) {
        return false;
      }
      return true;
    }
    if (
      !this.props.newAppointment.appointmentLocation
      || !this.props.newAppointment.date
      || !(
        this.props.clientCreditCard
        && this.props.clientCreditCard.creditCardType
        && this.props.clientCreditCard.lastFour
      )
    ) {
      return false;
    }
    return true;
  };

  isRequiredCard = () => {
    if (this.props.item.ProductDuration[0].price === '0.0') {
      return true;
    }
    if (
      !(
        this.props.clientCreditCard
        && this.props.clientCreditCard.creditCardType
        && this.props.clientCreditCard.lastFour
      )
    ) {
      return false;
    }
    return true;
  };

  getPriceWithTax = () => {
    let priceWithTax;
    if (this.props.item && this.props.item.ProductDuration) {
      const product = this.props.item;
      const productPrice = parseFloat(product?.ProductDuration[0]?.price);
      priceWithTax = product?.ProductDuration[0]?.priceWithTax || productPrice;
      if (product?.tax) {
        const taxValue = product.tax ? (productPrice * product.tax) / 100 : 0;
        priceWithTax = productPrice + taxValue;
      }
    }
    return parseFloat(priceWithTax).toFixed(2);
  };

  render() {
    const priceWithTax = this.getPriceWithTax();
    return (
      <View style={styles.container}>
        <ScrollView style={styles.item}>
          <View style={styles.header}>
            <ImageBackground
              accessible={!!(this.state.serviceImage && this.props.item)}
              source={this.state.serviceImage}
              style={serviceViewStyles.image}
              onLoadStart={() => {
                this.setState({ imageLoading: true });
              }}
              onLoadEnd={() => {
                this.setState({ imageLoading: false });
              }}
            >
              <TouchableOpacity
                style={{
                  marginTop: 50,
                  marginLeft: 20,
                  width: 35,
                  height: 35,
                }}
                onPress={() => this.props.navigation.goBack()}
              >
                <Image source={closeIcon} style={{ width: 35, height: 35 }} />
              </TouchableOpacity>
              {this.props.loader
              || !this.props.item
              || this.state.imageLoading
              || !this.props.item.ProductDuration ? (
                <ActivityIndicator
                  style={{ marginTop: 50 }}
                  animating
                  color={colors.white}
                  size="large"
                />
                ) : (
                  <>
                    <View style={styles.headerName}>
                      <Text style={serviceViewStyles.title}>
                        {this.props.item.name}
                      </Text>
                    </View>
                    <View style={serviceViewStyles.headerTimePrice}>
                      <Text style={serviceViewStyles.headerTime}>
                        {this.showTimeAndDurationInfo(this.props.newAppointment)}
                      </Text>
                      <View style={serviceViewStyles.headerPriceContainer}>
                        <Text style={serviceViewStyles.headerPrice}>
                          {this.props.symbol}
                          {priceWithTax}
                        </Text>
                      </View>
                    </View>
                  </>
                )}
            </ImageBackground>
          </View>
          {this.props.loader
          || !this.props.item
          || !this.props.item?.ProductDuration ? (
            <ContentLoading visible />
            ) : (
              <>
                <View style={styles.content}>
                  <View style={styles.contentUser}>
                    <View style={styles.userImageContainer}>
                      <Image
                        source={
                        this.props.item.productUserSettings[0].trainerAvatarUrl
                          ? {
                            uri: this.props.item.productUserSettings[0]
                              .trainerAvatarUrl,
                          }
                          : defaultProfile
                      }
                        style={styles.userImage}
                      />
                    </View>
                    <View style={styles.userDetails}>
                      <Text style={styles.userName}>
                        {`${this.props.item.productUserSettings[0].trainerFirstName} ${this.props.item.productUserSettings[0].trainerLastName}`}
                      </Text>
                      <Text style={styles.userJob}>Fitness Professional</Text>
                    </View>
                  </View>
                  <View style={styles.serviceDescription}>
                    <Text style={styles.description}>
                      {this.props.item.description}
                    </Text>
                  </View>
                  <View style={styles.serviceLocations}>
                    {this.renderBalance()}
                    <Text style={styles.locationsLabel1}>
                      Make Your Appointment
                    </Text>
                    <FormTouchableDropdown
                      formInputStyle={generalStyles.mVertical10}
                      label="Location"
                      value={
                      this.props.newAppointment.appointmentLocation
                        ? this.props.newAppointment.appointmentLocation
                          .addressName
                        : 'Select a Location'
                    }
                      onPressFunc={() => this.props.navigation.navigate({
                        name:
                          APPLICATION_CLIENT_ROUTES.NEW_APPOINTMENT_LOCATION,
                        params: {
                          returnRoute: APPLICATION_CLIENT_ROUTES.BOOK_SERVICE,
                        },
                      })}
                    />
                    <View styles={generalStyles.mVertical10}>
                      {this.props.newAppointment.appointmentLocation ? (
                        <View style={{ display: 'flex' }}>
                          <FormTouchableDropdown
                            label="Date/Time"
                            value={this.showDateAndTimeInfo(
                              this.props.newAppointment,
                            )}
                            onPressFunc={() => this.props.navigation.navigate({
                              name:
                                APPLICATION_CLIENT_ROUTES.NEW_APPOINTMENT_DATE_AND_TIME,
                              params: {
                                returnRoute:
                                  APPLICATION_CLIENT_ROUTES.BOOK_SERVICE,
                              },
                              merge: true,
                            })}
                          />
                          {!this.isRequiredCard() ? (
                            <Text style={styles.required}>* Required</Text>
                          ) : null}
                          {this.renderCreditCard()}
                        </View>
                      ) : (
                        <Text style={generalStyles.smallText}>
                          Date/time availability varies by location. Please select
                          your location first.
                        </Text>
                      )}
                    </View>
                  </View>
                </View>
                <AwesomeAlert
                  show={this.state.showAlert}
                  showProgress={false}
                  useNativeDriver
                  title="Available Balance"
                  message={
                  '1 booking = 1 balance \r\n \r\n Any available balances in your account will be automatically applied to relevant services. \r\n\nPlease keep in mind how many balances you have left for each service, because balances are not applied until check-in.'
                }
                  closeOnTouchOutside
                  closeOnHardwareBackPress={false}
                  showConfirmButton={false}
                  showCancelButton
                  titleStyle={deleteAlertStyles.alertTitle}
                  messageStyle={deleteAlertStyles.alertText}
                  confirmText="Cancel"
                  cancelText="Close"
                  confirmButtonTextStyle={deleteAlertStyles.alertButtonText}
                  confirmButtonStyle={deleteAlertStyles.alertButton}
                  cancelButtonTextStyle={deleteAlertStyles.alertButtonText}
                  cancelButtonStyle={deleteAlertStyles.alertButton}
                  actionContainerStyle={{
                    flexDirection: 'column',
                    backgroundColor: colors.white,
                    borderRadius: 5,
                  }}
                  contentContainerStyle={deleteAlertStyles.alertContainer}
                  onCancelPressed={() => {
                    this.hideAlert();
                  }}
                  onConfirmPressed={() => {
                    this.hideAlert();
                  }}
                />
              </>
            )}
        </ScrollView>
        {this.props.loader
        || !this.props.item
        || !this.props.item?.ProductDuration ? null : (
          <View style={styles.goAppointments}>
            <SubTotalDetailsItem
              price={this.props.item?.ProductDuration[0]?.price}
              tax={this.props.item?.tax}
              symbol={this.props.symbol}
            />
            <View style={styles.totalText}>
              <Text style={styles.textLeft}>Total</Text>
              <Text style={styles.textRight}>
                $
                {priceWithTax}
              </Text>
            </View>
            <View>
              <Text style={styles.centerText}>
                You will not be charged until check-in.
              </Text>
              {!this.canProceed() ? (
                <FormSubmitButton
                  title="Book Now"
                  buttonStyle={{
                    backgroundColor: colors.lightgrey,
                    borderWidth: 1,
                    borderColor: colors.bordergrey,
                    marginHorizontal: 20,
                  }}
                  buttonLabelStyle={{ color: colors.subGrey }}
                  onPressButton={() => false}
                />
              ) : (
                <FormSubmitButtonWithSpinner
                  title="Book Now"
                  onPressButton={() => this.props.onSaveAppointment()}
                  isLoading={this.props.bookServiceLoader}
                />
              )}
            </View>
          </View>
          )}
      </View>
    );
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    display: 'flex',
    flexDirection: 'column',
    height: 296,
    backgroundColor: colors.black,
  },
  item: {
    flexDirection: 'column',
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  content: {
    flex: 3,
    flexDirection: 'column',
  },
  contentUser: {
    flex: 1,
    flexDirection: 'row',
    padding: 20,
    alignItems: 'flex-start',
  },

  userImageContainer: {
    justifyContent: 'center',
    marginRight: 15,
  },
  userImage: {
    width: 50,
    height: 50,
    borderColor: colors.white,
    borderRadius: 25,
  },
  userDetails: {
    ...generalStyles.avenirHeavy14,
    flex: 5,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
  },
  userJob: {
    ...generalStyles.avenirRoman13,
    display: 'flex',
    color: colors.subGrey,
  },
  userName: {
    display: 'flex',
    fontWeight: 'bold',
    fontSize: 16,
    color: colors.black,
  },
  serviceDescription: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  description: {
    fontSize: 16,
  },
  serviceLocations: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  locationsLabel: {
    ...generalStyles.avenirHeavy17,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  locationsLabel1: {
    ...generalStyles.avenirHeavy17,
    fontWeight: 'bold',
    marginBottom: 4,
    marginTop: 30,
  },
  goAppointments: {
    justifyContent: 'flex-end',
    paddingTop: 20,
    paddingBottom: 30,
  },
  totalText: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 20,
    ...generalStyles.mVertical10,
  },
  centerText: {
    textAlign: 'center',
    ...generalStyles.smallText,
    ...generalStyles.mBottom10,
  },
  textLeft: {
    flex: 1,
    fontWeight: 'bold',
    alignSelf: 'flex-start',
    ...generalStyles.fontBold,
  },
  textRight: {
    flex: 1,
    alignSelf: 'flex-end',
    textAlign: 'right',
    ...generalStyles.fontBold,
  },
  balanceText: {
    ...generalStyles.avenirRoman17,
    color: colors.subGrey,
    display: 'flex',
    marginRight: 50,
    marginTop: 0,
  },
  infoIcon: {
    display: 'flex',
    width: 33,
    height: 33,
    marginLeft: 'auto',
  },
  balanceBox: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerName: {
    marginTop: 14,
    width: '100%',
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  required: {
    color: colors.dustyRed,
    marginTop: 10,
    fontFamily: 'Avenir-Roman',
    fontSize: 13,
    fontStyle: 'italic',
  },
});
export default BookServiceView;
