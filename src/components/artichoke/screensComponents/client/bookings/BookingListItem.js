import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import { colors } from '../../../../../styles';
import { generalStyles } from '../../../../../styles/generalStyle';

const BookingListItem = (props) => {
  let checkedIn = 0;
  if (props.item.Invitee) {
    checkedIn = props.item.Invitee.filter(
      (inv) => inv.checkedIn == 'true' && inv.Client.user.id === props.userDetails.id,
    ).length;
  }
  let address = '';
  if (props.item.remotely.toString() === 'true') {
    address = 'Remote';
  } else if (props.item.clientAddressEnable.toString() === 'true') {
    address = "Client's Location";
  } else if (props.item.onsiteAddress) {
    address = props.item.onsiteAddress && props.item.onsiteAddress.addressName
      ? props.item.onsiteAddress.addressName
      : props.item.onsiteAddress.address1;
  } else {
    address = 'Unknown Address';
  }
  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <View style={styles.topBox}>
        <Text style={styles.startHour}>
          {`${moment(props.item.start, 'MM/DD/YYYY hh:mm a').format(
            'hh:mm a',
          )} (${props.item.ProductDuration.duration.duration} min)`}
        </Text>

        <Text
          style={checkedIn > 0 ? styles.checkedInTrue : styles.checkedInFalse}
        >
          {checkedIn > 0 ? 'Checked In' : 'Not Checked In'}
        </Text>
      </View>

      <View style={styles.bottomBox}>
        <View style={styles.centerBox}>
          <Text style={styles.addressName}>{address}</Text>
          <Text style={styles.addressLocation}>{props.item.description}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    display: 'flex',
    flexDirection: 'column',
    height: 104,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  topBox: {
    width: '100%',
    paddingHorizontal: 20,
    height: 40,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  startHour: {
    width: '50%',
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    color: colors.lightblue,
  },
  checkedInTrue: {
    width: '50%',
    textAlign: 'right',
    ...generalStyles.avenirRoman13,
    color: colors.goodGreen,
  },
  checkedInFalse: {
    width: '50%',
    textAlign: 'right',
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  bottomBox: {
    width: '100%',
    marginHorizontal: 20,
    height: 53,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirHeavy14,
  },
  addressLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  userImageContainer: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderColor: colors.peaGreen,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  multipleUsersContainer: {
    width: 56,
    height: 56,
    flexDirection: 'row',
    backgroundColor: colors.lightblue,
    borderRadius: 28,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userImage: {
    width: 54,
    height: 54,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 28,
  },
  invitee: {
    flex: 1,
    ...generalStyles.fontBold,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 5,
    color: colors.white,
  },
});
export default BookingListItem;
