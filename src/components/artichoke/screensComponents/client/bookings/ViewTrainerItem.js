import React from 'react';
import {
  View, StyleSheet, Text, Image,
} from 'react-native';
import { colors } from '../../../../../styles';

const bgImage = require('../../../../../assets/imgServiceBackground.png');

const ViewTrainerItem = (props) => {
  const trainerImage = props.trainer.trainerAvatarUrl
    ? { uri: props.trainer.trainerAvatarUrl }
    : bgImage;
  return (
    <View style={styles.contentTrainer}>
      <Image source={trainerImage} style={styles.trainerImage} />
      <View style={styles.trainerDetails}>
        <Text style={styles.trainerJob}>Fitness Professional </Text>
        <Text
          style={
            styles.trainerName
          }
        >
          {`${props.trainer.trainerFirstName} ${props.trainer.trainerLastName}`}
        </Text>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  trainerImage: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: 25,
  },
  contentTrainer: {
    flex: 1,
    flexDirection: 'row',
    padding: 15,
    alignItems: 'flex-start',
  },
  trainerDetails: {
    flex: 5,
    alignItems: 'flex-start',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: 5,
    marginLeft: 10,
  },
  trainerName: {
    display: 'flex',
    fontWeight: 'bold',
    fontSize: 16,
  },
  trainerJob: {
    display: 'flex',
    color: colors.subGrey,
  },
});
export default ViewTrainerItem;
