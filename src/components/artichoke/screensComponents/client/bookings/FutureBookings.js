import * as React from 'react';
import {
  FlatList, View, StyleSheet, Text,
} from 'react-native';
import moment from 'moment';
import EmptyBookedList from '../../booked/EmptyBookedList';
import BookingCardItem from './BookingCardItem';
import { colors } from '../../../../../styles';

const styles = StyleSheet.create({
  container: {},
  labelday: {
    fontSize: 17,
    width: '100%',
    textAlign: 'left',
  },
  formday: {
    backgroundColor: colors.veryLightBlue,
    height: 58,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingHorizontal: 21,
  },
});

export default function FutureBookings(props) {
  return (
    <View style={styles.container} key={props.index}>
      <View style={styles.formday}>
        <Text style={styles.labelday}>
          {moment(props.date, 'YYYY-MM-DD').format('dddd, MMMM Do')}
        </Text>
      </View>

      <FlatList
        data={props.appointments}
        renderItem={({ item }) => (
          <BookingCardItem
            item={item}
            onPressItem={props.onPressItem}
            userDetails={props.userDetails}
          />
        )}
        keyExtractor={(item) => `booked-list-previous-date-item-${item.id}`}
        refreshing={props.refreshing}
        ListEmptyComponent={() => (
          <EmptyBookedList message="No booking found" />
        )}
      />
    </View>
  );
}
