import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../../styles';
import { generalStyles } from '../../../../../styles/generalStyle';
import { curvedScale } from '../../../../../util/responsive';

const ClientBookingListItem = (props) => (
  <TouchableOpacity
    style={styles.item}
    key={props.item.id}
    onPress={() => props.onPressItem(props.item)}
  >
    <View style={styles.bottomBox}>
      <View style={styles.centerBox}>
        <Text style={styles.bookingName}>{props.item.description}</Text>
        <Text style={styles.bookingLocation}>{props.item.start}</Text>
      </View>
    </View>
    <View style={styles.rightBox}>
      <IconFeader name="chevron-right" size={20} color={colors.subGrey} />
    </View>
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  item: {
    width: '100%',
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  bottomBox: {
    marginHorizontal: 20,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginBottom: 10,
  },
  centerBox: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  bookingName: {
    ...generalStyles.avenirHeavy14,
    marginVertical: 5,
  },
  bookingLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  rightBox: {
    alignItems: 'center',
    marginHorizontal: curvedScale(20),
  },
});
export default ClientBookingListItem;
