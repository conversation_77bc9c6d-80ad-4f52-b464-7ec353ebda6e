import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import moment from 'moment';
import { colors } from '../../../../../styles';
import { APPLICATION_CLIENT_ROUTES } from '../../../../../constants';
import { generalStyles } from "../../../../../styles/generalStyle";

const ClassesListItem = (props) => {

  let address = ""
  if (props.item.remotely.toString() === "true") {
    address = "Remote"
  } else if (props.item.clientAddressEnable.toString() === "true") {
    address = "Client's Location"
  } else if (props.item.inapp.toString() === "true") {
    address = "Video Call"
  } else if (props.item.onsiteAddress) {
    address = props.item.onsiteAddress && props.item.onsiteAddress.addressName ? props.item.onsiteAddress.addressName : props.item.onsiteAddress.address1;
  } else {
    address = "Unknown"
  }

  const checkBooked = () => {
    const bookedInv = props.item.Invitee.filter((client) => client.Client.user.id === props.userDetails.id);
    if (bookedInv.length > 0) {
      return true;
    } else {
      return false;
    }
  };

  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.navigation.navigate(APPLICATION_CLIENT_ROUTES.CLASS_VIEW, {
        scheduleId: props.item.id,
      })}
    >
      <View style={styles.leftBox}>
        <View style={styles.topBox}>
          <Text style={styles.startHour}>
            {`${moment(props.item.start, 'MM/DD/YYYY hh:mm a').format(
              'hh:mm a',
            )} (${props.item.ProductDuration.duration.duration} min)`}
          </Text>

        </View>

        <View style={styles.bottomBox}>
          <Text style={styles.addressName} numberOfLines={1}>{props.item.ProductDuration.name}</Text>
          <Text style={styles.addressLocation}>{address}</Text>
        </View>
      </View>
      <View style={styles.rightBox}>
        {checkBooked() ?
          <TouchableOpacity
            style={styles.booked}
            activeOpacity={1}>
            <Text style={styles.labelBooked}>Book</Text>
          </TouchableOpacity>
          :
          <TouchableOpacity
            style={styles.unBooked}
            activeOpacity={1}
            onPress={() => props.navigation.navigate(APPLICATION_CLIENT_ROUTES.CLASS_VIEW, {
              scheduleId: props.item.id,
            })}>
            <Text style={styles.labelUnBooked}>Book</Text>
          </TouchableOpacity>
        }
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    display: 'flex',
    flexDirection: 'row',
    height: 104,
    marginTop: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  topBox: {
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    height: 20,
    marginHorizontal: 20,
    width: '100%',
  },
  bottomBox: {
    display: 'flex',
    flexDirection: 'column',
    width: '70%',
    marginHorizontal: 20,
  },
  addressName: {
    ...generalStyles.avenirHeavy14,
    width: '100%',
    display: 'flex',
    marginTop: 10,
    height: 25,
  },
  addressLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
    height: 20,
    marginTop: 10,
    width: '100%',
    display: 'flex',
  },
  startHour: {
    flex: 1,
    fontSize: 13,
    fontWeight: 'bold',
    color: colors.lightblue,
    height: 22,
    width: '100%',
  },
  duration: {
    flex: 1,
    fontSize: 13,
    color: 'black',
    height: 22,
    width: 40,
  },
  booked: {
    width: 92,
    borderColor: colors.bordergrey,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    display: 'flex',
    borderRadius: 18,
    borderWidth: 2,
    height: 36,
  },
  labelBooked: {
    ...generalStyles.avenirMedium17,
    color: colors.bordergrey,
  },
  unBooked: {
    width: 92,
    backgroundColor: colors.nasmBlue,
    borderColor: colors.nasmBlue,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    display: 'flex',
    borderRadius: 18,
    borderWidth: 2,
    height: 36,
  },
  labelUnBooked: {
    ...generalStyles.avenirHeavy17,
    color: colors.white,
  },
  rightBox: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-end',
    justifyContent: 'center',
    marginRight: 10,
  },
  leftBox: {
    flex: 2,
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginRight: 10,
    height: '100%',
  },
});
export default ClassesListItem;
