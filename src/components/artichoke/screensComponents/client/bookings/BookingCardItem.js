import React from 'react';
import {
  Image,
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
} from 'react-native';
import moment from 'moment';
import { generalStyles } from '../../../../../styles/generalStyle';
import { scaleWidth } from '../../../../../util/responsive';
import { track } from '../../../../../util/Analytics';
import { colors } from '../../../../../styles';

const bgImage = require('../../../../../assets/imgServiceBackground.png');
const checkMarkOn = require('../../../../../assets/imgCheckmarkOnMedium.png');
const checkMarkOff = require('../../../../../assets/imgCheckmarkOffMedium.png');
const videoBtnIcon = require('../../../../../assets/video_btn_icon.png');

const BookingCardItem = (props) => {
  const serviceImage = props.item.serviceImage
    ? { uri: props.item.serviceImage }
    : bgImage;
  let checkedIn = 0;
  if (props.item.Invitee) {
    checkedIn = props.item.Invitee.filter(
      (inv) => inv.checkedIn === 'true' && inv.Client.user.id === props.userDetails.id,
    ).length;
  }
  let address = '';
  if (props.item.remotely.toString() === 'true') {
    address = 'Join Remotely';
  } else if (props.item.inapp.toString() === 'true') {
    address = 'Video Session';
  } else if (props.item.clientAddressEnable.toString() === 'true') {
    address = "Client's Location";
  } else if (props.item.onsiteAddress) {
    address = props.item.onsiteAddress && props.item.onsiteAddress.addressName
      ? props.item.onsiteAddress.addressName
      : props.item.onsiteAddress.address1;
  } else {
    address = 'Unknown Address';
  }

  const renderStartSessionButton = (!props.item.endSession
      && moment(props.item.end, 'MM/DD/YYYY hh:mm A').isSameOrAfter(moment()))
    || (props.item.endSession
      && moment(props.item.endSessionTime, 'MM/DD/YYYY hh:mm A')
        .add(10, 'minute')
        .isSameOrAfter(moment())) ? (
          <View style={styles.topTitleBox}>
            <View style={{ ...styles.button, ...styles.rightButton }}>
              {props.joinVideoLoading
          && props.joiningAppointmentId === props.item.id ? (
            <ActivityIndicator color={colors.white} />
                ) : (
                  <TouchableOpacity
                    onPress={() => {
                      props.joinVideoCallAction(props.item);
                      track('video_call_started', {
                        channel: props.item.appointmentToken,
                      });
                    }}
                  >
                    <View style={styles.buttonContainer}>
                      <Image
                        source={videoBtnIcon}
                        style={{
                          width: 50,
                          height: 36,
                        }}
                      />
                      <Text style={styles.buttonText}>Join</Text>
                    </View>
                  </TouchableOpacity>
                )}
            </View>
          </View>
    ) : null;

  return (
    <TouchableOpacity
      style={{
        ...styles.item,
        height: props.item.inapp.toString() === 'true' ? 251 : 201,
      }}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <ImageBackground
        source={serviceImage}
        style={styles.image}
        imageStyle={{ borderRadius: 20 }}
      >
        <View style={generalStyles.overlay} />
      </ImageBackground>
      <View style={styles.topBox}>
        <View style={styles.topBoxLeft}>
          {props.item.isScheduled ? (
            <Text style={generalStyles.priceText}>Class</Text>
          ) : (
            <Text style={generalStyles.priceText}>Session</Text>
          )}
        </View>
        <Text style={styles.topBoxRight}>
          {checkedIn > 0 ? (
            <Image source={checkMarkOn} style={styles.checkMark} />
          ) : (
            <Image source={checkMarkOff} style={styles.checkMark} />
          )}
        </Text>
      </View>
      <View style={styles.titleBox}>
        <View style={styles.topTitleBox}>
          <Text style={styles.title}>{props.item.ProductDuration.name}</Text>
          {props.item.inapp.toString() === 'true' ? (
            <Text style={styles.subTitle}>
              {`${moment(props.item.start, 'MM/DD/YYYY hh:mm a').format(
                'hh:mm a',
              )} (${props.item.ProductDuration.duration.duration} min)`}
              {' '}
              |
              {' '}
              {address}
            </Text>
          ) : (
            <Text style={styles.subTitle}>
              {address}
              {' '}
              |
              {' '}
              {`${moment(props.item.start, 'MM/DD/YYYY hh:mm a').format(
                'hh:mm a',
              )} (${props.item.ProductDuration.duration.duration} min)`}
            </Text>
          )}
        </View>
        {props.item.inapp.toString() === 'true'
          ? renderStartSessionButton
          : null}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  image: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  item: {
    marginTop: 9,
    marginHorizontal: scaleWidth(5),
    borderRadius: 20,
    flexDirection: 'column',
    height: 201,
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowRadius: 4,
    shadowOpacity: 1,
    backgroundColor: colors.white,
    elevation: 10,
  },
  topBox: {
    flex: 3,
    flexDirection: 'row',
    marginLeft: 1,
  },
  titleBox: {
    flex: 7,
    textAlign: 'center',
    flexDirection: 'column',
    marginHorizontal: 23,
    marginBottom: 20,
  },
  topBoxLeft: {
    color: colors.white,
    fontWeight: 'bold',
    justifyContent: 'center',
    borderTopLeftRadius: 20,
    position: 'absolute',
    left: 0,
    top: 0,
    paddingVertical: 9,
    paddingLeft: 18,
    paddingRight: 13,
    backgroundColor: colors.duskBlue,
    borderBottomRightRadius: 5,
    flex: 1,
  },
  topBoxRight: {
    ...generalStyles.durationText,
    backgroundColor: colors.transparent,
    color: colors.white,
    alignSelf: 'flex-end',
    textAlign: 'right',
    marginRight: 23,
    marginLeft: 'auto',
    height: Platform.OS === 'ios' ? 37 : 41,
  },
  topTitleBox: {
    flex: 2,
    alignSelf: 'flex-start',
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingBottom: 5,
  },
  title: {
    ...generalStyles.titleService,
    color: colors.white,
  },
  subTitle: {
    ...generalStyles.avenirHeavy17,
    color: colors.white,
    marginTop: 13,
  },
  button: {
    backgroundColor: colors.transparent,
    flex: 1,
    display: 'flex',
    marginTop: 40,
  },
  buttonContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: colors.white,
    borderWidth: 2,
    borderRadius: 18,
    height: 36,
    lineHeight: 32,
    paddingHorizontal: 20,
  },
  buttonText: {
    display: 'flex',
    color: colors.white,
    textAlign: 'center',
    margin: 'auto',
    ...generalStyles.avenirHeavy17,
  },
  checkMark: {
    width: 20,
    height: 20,
  },
});
export default BookingCardItem;
