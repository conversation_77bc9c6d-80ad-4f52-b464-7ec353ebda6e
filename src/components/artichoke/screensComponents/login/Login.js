import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
  StyleSheet, Text, TextInput, View, Button, Alert,
} from 'react-native';
import {
  createLoginAction,
  createSetLoginStateAction,
} from '../../../actions/Login.actions';
import nasm from '../../../../dataManager/apiConfig';
import { applicationInitAction } from '../../../actions/Application.actions';

class LoginComponent extends Component {
  constructor(props) {
    super(props);

    this.state = {
      username: '',
      password: '',
    };
  }

  onPressClientLogin = () => {
    const { username, password } = this.state;
    nasm.auth
      .login({ username, password }, 'v1.7')
      .then((success) => {
        this.props.applicationInitAction();
      })
      .catch((error) => {
        Alert.alert('Error', error.message);
      });
  };

  onPressTrainerLogin = () => {
    const { username, password } = this.state;
    nasm.auth
      .login({ username, password }, 'v2.0')
      .then((success) => {
        this.props.applicationInitAction();
      })
      .catch((error) => {
        Alert.alert('Error', error.message);
      });
  };

  render() {
    return (
      <View style={styles.container}>
        <Text style={styles.headText}>Login</Text>
        <TextInput
          keyboardType="email-address"
          autoCapitalize="none"
          value={this.state.username}
          onChangeText={(username) => this.setState({ username })}
          label="Email"
          style={styles.input}
        />
        <TextInput
          value={this.state.password}
          onChangeText={(password) => this.setState({ password })}
          label="Password"
          secureTextEntry
          style={styles.input}
        />
        <Button
          title="Trainer Login"
          style={styles.input}
          onPress={this.onPressTrainerLogin}
        />
        <Button
          title="Client Login"
          style={styles.input}
          onPress={this.onPressClientLogin}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  input: {
    width: 200,
    height: 44,
    padding: 10,
    borderWidth: 1,
    borderColor: '#cccccc',
    borderRadius: 10,
    marginBottom: 10,
  },
  headText: {
    fontSize: 20,
    padding: 20,
  },
});
export const LoginScreen = connect(
  (state) => ({
    form: state.form,
  }),
  {
    createLoginAction,
    createSetLoginStateAction,
    applicationInitAction,
  },
)(LoginComponent);
