import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const CurrencyCountryListItem = (props) => {
  const checkboxStyle = props.isSelected
    ? styles.selectedCheckbox
    : styles.unselectedCheckbox;
  return (
    <View style={styles.item} key={props.item.key}>
      <View style={styles.leftBox}>
        <Text style={styles.name}>
          {props.item.key}
          {' '}
          {props.item.symbol}
        </Text>
      </View>
      <View style={styles.rightBox}>
        <TouchableOpacity
          style={checkboxStyle}
          activeOpacity={1}
          onPress={() => props.onSelectItem(props.item)}
        >
          {props.isSelected ? (
            <IconFeader
              name="check"
              size={20}
              color={colors.white}
              style={{ paddingTop: 2 }}
            />
          ) : (
            <Text />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    paddingHorizontal: 20,
    borderRadius: 10,
    flex: 1,
    flexDirection: 'row',
    height: 61,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  leftBox: {
    flex: 3,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  rightBox: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  name: {
    ...generalStyles.avenirRoman17,
  },
  checkbox: {
    borderRadius: 10,
  },
  selectedCheckbox: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.goodGreen,
    width: 26,
    height: 26,
    borderRadius: 13,
  },
  unselectedCheckbox: {
    width: 26,
    height: 26,
    borderRadius: 13,
    borderWidth: 2,
    borderColor: colors.subGrey,
  },
});
export default CurrencyCountryListItem;
