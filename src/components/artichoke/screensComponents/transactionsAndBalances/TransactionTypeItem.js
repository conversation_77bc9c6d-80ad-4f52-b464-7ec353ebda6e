import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const TransactionTypeItem = (props) => (
  <View style={styles.item}>
    <View style={styles.box}>
      <View style={styles.centerBox}>
        <Text style={styles.addressName}>{props.name}</Text>
      </View>
    </View>
    <View style={styles.rightBox}>
      <Text style={styles.addressLocation}>
        {props.quantity}
        {' '}
      </Text>
      {props.collapsed ? (
        <IconFeader name="chevron-up" size={20} color={colors.subGrey} />
      ) : (
        <IconFeader name="chevron-down" size={20} color={colors.subGrey} />
      )}
    </View>
  </View>
);
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    width: '100%',
    borderRadius: 10,
    display: 'flex',
    flexDirection: 'row',
    height: 65,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  box: {
    width: '75%',
    marginHorizontal: 20,
    height: 65,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirBlack17,
    color: 'black',
    fontWeight: 'bold',
  },
  addressLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  rightBox: {
    flex: 1,
    marginLeft: 'auto',
    paddingRight: 19,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
});
export default TransactionTypeItem;
