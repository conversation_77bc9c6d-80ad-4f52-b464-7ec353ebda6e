import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const BalanceItem = (props) => (
  <TouchableOpacity
    style={styles.item}
    key={props.item.id}
    onPress={() => props.onPressItem(props.item)}
  >
    <View style={styles.box}>
      <View style={styles.centerBox}>
        <Text style={styles.addressName}>{props.item.productName}</Text>
        <Text
          style={
              styles.addressLocation
            }
        >
          {`${props.item.ProductDuration.duration.duration} ${props.item.ProductDuration.duration.granularity.abbreviation}`}
        </Text>
      </View>
    </View>
    <View style={styles.rightBox}>
      <Text style={styles.addressLocation}>
        {props.item.quantity > 100
          ? 'Unlimited'
          : `${props.item.quantity} left`}
      </Text>
      <IconFeader name="chevron-right" size={30} color={colors.subGrey} />
    </View>
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  item: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    height: 65,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
  },
  box: {
    width: '75%',
    marginHorizontal: 20,
    height: 65,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirHeavy17,
    color: 'black',
  },
  addressLocation: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  rightBox: {
    display: 'flex',
    marginLeft: 'auto',
    paddingRight: 19,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
});
export default BalanceItem;
