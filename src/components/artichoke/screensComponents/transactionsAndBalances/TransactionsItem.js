import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const dateFormat = 'MM/DD/YYYY hh:mm a';

const TransactionItem = (props) => {
  let purchaseType = '';
  if (props.item.purchase) {
    if (props.item.purchase.refunded === true) {
      purchaseType = 'Refund';
    } else if (props.item.purchase.purchaseType === 'CREDIT_CARD') {
      purchaseType = `${props.symbol}${props.item.purchase.amountPaid} CC Purchase`;
    } else if (props.item.purchase.purchaseType === 'CASH_OR_CHECK') {
      purchaseType = `${props.symbol}${props.item.purchase.amountPaid} Cash/Check Purchase`;
    }
  } else {
    purchaseType = 'Check in';
  }

  let serviceName = '';
  if (props.item.purchase) {
    if (
      props.item.purchase
      && props.item.purchase.clientPurchaseProductDurations
      && props.item.purchase.clientPurchaseProductDurations.length
      && props.item.purchase.clientPurchaseProductDurations[0].productName
    ) {
      serviceName = props.item.purchase.clientPurchaseProductDurations[0].productName;
    } else if (
      props.item.purchase
      && props.item.purchase.pack
      && props.item.purchase.pack.name
    ) {
      serviceName = props.item.purchase.pack.name;
    }
  } else {
    serviceName = props.item.checkIn.appointment.ProductDuration.name;
  }

  const assignStyle = props.item.purchase && props.item.purchase.refunded === true
    ? styles.startHourGreen
    : styles.startHour;

  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <View style={styles.leftBox}>
        <View style={styles.topBox}>
          <Text
            style={
              purchaseType === 'Check in' ? styles.checkInStyle : assignStyle
            }
          >
            {purchaseType}
          </Text>
        </View>

        <View style={styles.bottomBox}>
          <View style={styles.centerBox}>
            <Text style={styles.addressName}>{serviceName}</Text>
            <Text style={styles.addressLocation}>
              {moment(props.item.created, dateFormat).format(
                'MM/DD/YYYY h:mm a',
              )}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.rightBox}>
        <IconFeader name="chevron-right" size={30} color={colors.subGrey} />
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  item: {
    display: 'flex',
    width: '100%',
    height: 123,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    paddingHorizontal: 20,
  },
  topBox: {
    display: 'flex',
    marginTop: 18,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  startHour: {
    width: '100%',
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    color: colors.lightblue,
  },
  startHourGreen: {
    width: '100%',
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    color: colors.goodGreen,
  },
  checkInStyle: {
    width: '100%',
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
  },
  bottomBox: {
    width: '100%',
    marginBottom: 24,
    marginTop: 14,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
    marginBottom: 10,
  },
  addressName: {
    ...generalStyles.avenirHeavy17,
    color: colors.black,
  },
  addressLocation: {
    ...generalStyles.avenirRoman14,
    color: colors.subGrey,
  },
  leftBox: {
    width: '95%',
    display: 'flex',
  },
  rightBox: {
    display: 'flex',
    alignItems: 'flex-start',
  },
});
export default TransactionItem;
