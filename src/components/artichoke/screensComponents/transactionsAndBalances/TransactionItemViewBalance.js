import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const dateFormat = 'MM/DD/YYYY hh:mm a';

const TransactionItemViewBalance = (props) => {
  let purchaseType = '';
  if (props.item.purchase) {
    if (props.item.purchase.refunded === true) {
      purchaseType = 'Refund';
    } else if (props.item.purchase.purchaseType === 'CREDIT_CARD') {
      purchaseType = `${props.symbol}${props.item.purchase.amountPaid} CC Purchase`;
    } else if (props.item.purchase.purchaseType === 'CASH_OR_CHECK') {
      purchaseType = `${props.symbol}${props.item.purchase.amountPaid} Cash/Check Purchase`;
    }
  } else {
    purchaseType = 'Check in';
  }

  let serviceName = '';
  if (props.item.purchase) {
    if (
      props.item.purchase
      && props.item.purchase.clientPurchaseProductDurations
      && props.item.purchase.clientPurchaseProductDurations.length
      && props.item.purchase.clientPurchaseProductDurations[0].productName
    ) {
      serviceName = props.item.purchase.clientPurchaseProductDurations[0].productName;
    } else if (
      props.item.purchase
      && props.item.purchase.pack
      && props.item.purchase.pack.name
    ) {
      serviceName = props.item.purchase.pack.name;
    }
  } else {
    serviceName = props.item.checkIn.appointment.ProductDuration.name;
  }

  const quantityByProductInList = (
    productId,
    clientPurchaseProductDurationList,
  ) => {
    const clientPurchaseProductDurationMatch = clientPurchaseProductDurationList.filter(
      (cppd) => cppd.ProductDuration.productId === productId,
    );
    return clientPurchaseProductDurationMatch[0].quantity;
  };

  const quantityByProductInPack = (productId, productList) => {
    const productMatch = productList.filter(
      (p) => p.productId.toString() === productId,
    );
    return productMatch[0].unlimited ? 'Unlimited' : productMatch[0].quantity;
  };

  let quantity = 0;
  if (purchaseType !== 'Check in') {
    if (props.item.purchase.packageId) {
      quantity = quantityByProductInPack(
        props.productId,
        props.item.purchase.pack.packageProducts,
      );
    } else {
      quantity = quantityByProductInList(
        props.productId,
        props.item.purchase.clientPurchaseProductDurations,
      );
    }
  }

  return purchaseType === 'Check in' ? (
    <TouchableOpacity
      style={styles.ckitem}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <View style={styles.leftBox}>
        <View style={styles.bottomBox}>
          <View style={styles.centerBox}>
            <Text style={styles.addressName}>
              {moment(props.item.created, dateFormat).format('DD/MM/YYYY')}
            </Text>
            <Text style={styles.addressLocation}>
              {moment(props.item.created, dateFormat).format('h:mm a')}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.rightBox}>
        <IconFeader name="chevron-right" size={20} color={colors.subGrey} />
      </View>
    </TouchableOpacity>
  ) : (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <View style={styles.leftBox}>
        <View style={styles.topBox}>
          <Text
            style={
              props.item.purchase && props.item.purchase.refunded === true
                ? styles.startHourGreen
                : styles.startHour
            }
          >
            {purchaseType}
          </Text>
        </View>

        <View style={styles.bottomBox}>
          <View style={styles.centerBox}>
            <Text style={styles.addressName}>
              {serviceName}
              {' '}
              -
              {quantity}
              {' '}
              {quantity === 1 ? 'session' : 'sessions'}
            </Text>
            <Text style={styles.addressLocation}>
              {moment(props.item.created, dateFormat).format(
                'MM/DD/YYYY h:mm a',
              )}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.rightBox}>
        <IconFeader name="chevron-right" size={20} color={colors.subGrey} />
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    display: 'flex',
    width: '100%',
    height: 123,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    paddingHorizontal: 20,
  },
  ckitem: {
    display: 'flex',
    width: '100%',
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    paddingHorizontal: 20,
  },
  topBox: {
    display: 'flex',
    marginTop: 19,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  startHour: {
    width: '100%',
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    color: colors.lightblue,
  },
  startHourGreen: {
    width: '100%',
    textAlign: 'left',
    ...generalStyles.avenirHeavy13,
    color: colors.goodGreen,
  },
  bottomBox: {
    width: '100%',
    marginBottom: 19,
    marginTop: 19,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirHeavy17,
    color: 'black',
  },
  addressLocation: {
    ...generalStyles.avenirRoman14,
    color: colors.subGrey,
  },
  leftBox: {
    width: '95%',
    display: 'flex',
  },
  rightBox: {
    display: 'flex',
    alignItems: 'flex-start',
  },
});
export default TransactionItemViewBalance;
