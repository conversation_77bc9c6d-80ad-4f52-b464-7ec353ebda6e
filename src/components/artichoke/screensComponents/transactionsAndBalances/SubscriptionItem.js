import React from 'react';
import {
  View, StyleSheet, Text, TouchableOpacity,
} from 'react-native';
import moment from 'moment';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const SubscriptionItem = (props) => {

  let serviceName = props.item.clientPurchase.pack.name;

  return (
    <TouchableOpacity
      style={styles.item}
      key={props.item.id}
      onPress={() => props.onPressItem(props.item)}
    >
      <View style={styles.leftBox}>
        <View style={styles.bottomBox}>
          <View style={styles.centerBox}>
            <Text style={styles.addressName}>{serviceName}</Text>
            <Text style={styles.addressLocation}>
              {moment(props.item.created).format('MM/DD/YYYY h:mm a')}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.rightBox}>
        <IconFeader name="chevron-right" size={30} color={colors.subGrey} />
      </View>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  item: {
    display: 'flex',
    width: '100%',
    height: 80,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    paddingHorizontal: 20,
  },
  bottomBox: {
    width: '100%',
    marginBottom: 10,
    marginTop: 14,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirHeavy17,
    color: colors.black,
  },
  addressLocation: {
    ...generalStyles.avenirRoman14,
    color: colors.subGrey,
  },
  leftBox: {
    width: '95%',
    display: 'flex',
  },
  rightBox: {
    display: 'flex',
    alignItems: 'flex-start',
  },
});
export default SubscriptionItem;
