import React from 'react';
import {
  View, StyleSheet, Text,
} from 'react-native';
import moment from 'moment';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

const BalanceAdjustmentItem = (props) => (
  <View style={styles.item} key={props.item.id}>
    <View style={styles.leftBox}>
      <View style={styles.bottomBox}>
        <View style={styles.centerBox}>
          <Text style={styles.addressName}>
            {moment(props.item.created, 'MM/DD/YYYY hh:mm a').format(
              'MM/DD/YYYY',
            )}
          </Text>
        </View>
      </View>
    </View>
    <View style={styles.rightBox}>
      <Text style={styles.quantity}>
        {props.item.purchase.clientPurchaseProductDurations[0].quantity}
        {' '}
        sessions
      </Text>
    </View>
  </View>
);
const styles = StyleSheet.create({
  item: {
    display: 'flex',
    width: '100%',
    height: 65,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    paddingHorizontal: 20,
  },
  bottomBox: {
    width: '60%',
    marginBottom: 19,
    marginTop: 19,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  addressName: {
    ...generalStyles.avenirHeavy17,
  },
  quantity: {
    ...generalStyles.avenirRoman13,
    color: colors.subGrey,
  },
  leftBox: {
    width: '85%',
    display: 'flex',
  },
  rightBox: {
    display: 'flex',
    alignItems: 'flex-start',
  },
});
export default BalanceAdjustmentItem;
