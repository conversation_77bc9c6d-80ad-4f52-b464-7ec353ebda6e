import React from 'react';
import {
  View, StyleSheet, Text, Switch, Platform,
} from 'react-native';
import { colors } from '../../../../styles';
import { generalStyles } from '../../../../styles/generalStyle';

export default function BookingNotification(props) {
  return (
    <View style={{ ...styles.frame, ...props.formInputStyle }}>
      <View style={styles.labelContainer}>
        <Text style={styles.title}>{props.title}</Text>
        <Text style={styles.label}>{props.subTitle}</Text>
      </View>
      <Switch
        trackColor={{ false: colors.chatSelected, true: colors.medYellow }}
        thumbColor={Platform.OS === 'ios' ? null : colors.white}
        style={styles.field}
        onValueChange={() => props.onChangeValue(!props.value)}
        {...props}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  frame: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'center',
    width: '100%',
    marginBottom: 20,
  },
  labelContainer: {
    paddingTop: 20,
    flex: 5,
    display: 'flex',
    flexDirection: 'column',
  },
  label: {
    textAlign: 'left',
    ...generalStyles.avenirRoman14,
    color: colors.subGrey,
    lineHeight: 22,
    fontFamily: 'Avenir-Roman',
  },
  title: {
    ...generalStyles.titleSmall,
    marginBottom: 5,
    color: colors.black,
    fontFamily: 'Avenir-Medium',
  },
  field: {
    fontSize: 14,
    height: 30,
    marginTop: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 'auto',
  },
});
