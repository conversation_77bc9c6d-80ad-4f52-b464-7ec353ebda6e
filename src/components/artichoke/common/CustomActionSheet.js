import * as React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import ActionSheet from 'react-native-actions-sheet';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';

export default function CustomActionSheet(props) {
  return (
    <ActionSheet
      headerAlwaysVisible
      footerAlwaysVisible
      gestureEnabled
      ref={props.actionSheetRef}
      containerStyle={styles.container}
    >
      <View
        style={[styles.contentContainer, { paddingTop: props.title ? 24 : 0 }]}
      >
        {props.title && (
          <View style={styles.title}>
            <Text style={generalStyles.title}>{props.title}</Text>
          </View>
        )}
        <View style={styles.content}>{props.children}</View>
      </View>
    </ActionSheet>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.veryLightBlue,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  contentContainer: {
    paddingTop: 24,
    paddingBottom: 0,
    flex: 1,
    flexDirection: 'column',
  },
  title: {
    flex: 1,
    alignItems: 'center',
    borderBottomWidth: 1,
    paddingBottom: 20,
    paddingHorizontal: 27,
    borderBottomColor: colors.actionSheetDivider,
  },
  content: {
    flex: 3,
    flexDirection: 'column',
  },
});
