import React from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../styles';
import { locationStyles } from '../../../styles/generalStyle';

export default function FormPrepTime(props) {
  return (
    <TouchableOpacity activeOpacity={1} onPress={props.onPressFunc}>
      <View style={styles.frame}>
        <Text style={styles.label}>
          {!props.value || props.value === 0
            ? 'No prep time'
            : `${props.value} min`}
        </Text>
        <Text style={styles.value} />
        <IconFeader
          style={{
            width: '8%',
            textAlign: 'right',
            paddingRight: 10,
            marginRight: '1.5%',
          }}
          name="chevron-down"
          size={25}
          color={colors.subGrey}
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    display: 'flex',
    flexDirection: 'row',
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  label: {
    ...locationStyles.prepTimeText,
    textAlign: 'left',
    width: '70%',
    color: colors.black,
    borderWidth: 0,
    paddingLeft: 8,
    marginLeft: '3%',
  },
  value: {
    ...locationStyles.prepTimeText,
    textAlign: 'right',
    width: '21%',
    color: colors.black,
    borderWidth: 0,
  },
});
