import React from 'react';
import { View, Text, Platform } from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import RNPickerSelect from 'react-native-picker-select';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';

export default function FormDay(props) {
  return (
    <View style={{ flex: 1 }}>
      <RNPickerSelect
        style={{
          inputIOS: {
            fontSize: 14,
            borderColor: colors.lightgrey,
            borderWidth: 1,
            paddingTop: 5,
            paddingBottom: 3,
            paddingLeft: 11,
            color: colors.black,
            height: 35, // to ensure the text is never behind the icon
            borderRadius: 3,
          },
          inputAndroid: {
            ...generalStyles.inputText,
            borderColor: colors.lightgrey,
            borderWidth: 1,
            paddingTop: 3,
            paddingBottom: 3,
            paddingLeft: 11,
            color: colors.black,
            height: 35, // to ensure the text is never behind the icon
            borderRadius: 3,
          },
          iconContainer: {
            top: 5,
            left: 5,
          },
        }}
        items={props.items}
        multiple
        max={7}
        placeholder={{}}
        useNativeAndroidPickerStyle={false}
        itemKey={props.value}
        value={props.value}
        onValueChange={(item) => props.onChange(item, props.value)}
        Icon={() => (
          <IconFeader
            style={{ flex: 1, textAlign: 'right' }}
            name="chevron-down"
            size={25}
            color={colors.subGrey}
          />
        )}
      />
      {Platform.OS !== 'ios' ? (
        <Text
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            bottom: 0,
            left: 0,
          }}
        >
          {' '}
        </Text>
      ) : null}
    </View>
  );
}
