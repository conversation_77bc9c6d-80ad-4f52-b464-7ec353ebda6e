import { Image, TouchableOpacity } from 'react-native';
import React from 'react';
import { generalStyles } from '../../../styles/generalStyle';

const addTooltipButtonSmallIcon = require('../../../assets/darkGrayTooltipSmall.png');

const AddTooltipButton = (props) => (
  <TouchableOpacity
    activeOpacity={0.2}
    onPress={() => props.onPressAction()}
    style={generalStyles.addButtonContainer}
  >
    <Image
      source={addTooltipButtonSmallIcon}
      style={generalStyles.addTooltipButtonSmall}
    />
  </TouchableOpacity>
);
export default AddTooltipButton;
