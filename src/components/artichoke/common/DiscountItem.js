import React from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../styles';

export default function DiscountItem(props) {
  let value = 'Select Discount';
  if (props.selectedDiscount.discount) {
    value = `${props.selectedDiscount.discount.amount}${
      props.selectedDiscount.discount.type === 'PERCENTAGE' ? '%' : props.symbol
    } off - ${props.selectedDiscount.discount.name}`;
  }
  return (
    <TouchableOpacity activeOpacity={1} onPress={() => props.selectDiscount()}>
      <View style={styles.frame}>
        <Text style={styles.label}>Discount</Text>
        <Text
          style={{
            ...styles.value,
            color: value === 'Select Discount' ? colors.subGrey : colors.black,
          }}
        >
          {value.length > 25 ? `${value.slice(0, 25)}...` : value}
        </Text>
        <IconFeader
          style={{ flex: 0.5, textAlign: 'right', paddingRight: 6 }}
          name="chevron-right"
          size={25}
          color={colors.subGrey}
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    display: 'flex',
    flexDirection: 'row',
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginVertical: 20,
  },
  label: {
    textAlign: 'left',
    fontSize: 14,
    flex: 2,
    color: colors.black,
    borderWidth: 0,
    paddingLeft: 8,
  },
  value: {
    textAlign: 'right',
    fontSize: 14,
    flex: 4,
    color: colors.black,
    borderWidth: 0,
  },
});
