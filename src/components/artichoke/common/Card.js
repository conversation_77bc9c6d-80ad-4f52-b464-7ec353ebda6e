import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { shadow, colors } from '../../../styles';

export default function Card(props) {
  const textStyle = props.textStyle || {};
  const containerStyle = props.containerStyle || {};
  return (
    <View style={{ ...styles.container, ...containerStyle }}>
      {props.title ? (
        <Text style={{ ...styles.titleText, ...textStyle }}>{props.title}</Text>
      ) : null}
      {props.children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.goodGreen,
    height: 75,
    width: '94%',
    padding: 15,
    marginLeft: '3%',
    marginRight: '3%',
    justifyContent: 'center',
    borderRadius: 3,
    ...shadow,
  },
  titleText: {
    textAlign: 'center',
    fontSize: 14,
  },
});
