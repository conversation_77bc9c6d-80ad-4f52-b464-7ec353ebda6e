import React from 'react';
import Textarea from 'react-native-textarea';
import {
  StyleSheet, Text, TextInput, View,
} from 'react-native';
import { generalStyles } from '../../../styles/generalStyle';
import { colors } from '../../../styles';
import { removeAllSpecialCharacters } from '../../../util/validate';

export default class CustomTextArea extends Textarea {
  constructor(props) {
    super(props);
    this.textarea = null;
  }

  renderCount() {
    const { maxLength, value } = this.props;
    const count = value?.length || 0;

    if (!maxLength) return null;

    return (
      <Text style={styles.count}>{`${maxLength - count} characters left`}</Text>
    );
  }

  render() {
    const { containerStyle, maxLength, ...rest } = this.props;
    if (maxLength) {
      rest.maxLength = maxLength;
    }
    return (
      <View style={[styles.container, containerStyle]}>
        <TextInput
          style={{ height: '100%', textAlignVertical: 'top' }}
          multiline
          {...rest}
          // eslint-disable-next-line no-underscore-dangle
          onChangeText={(text) => {
            const filteredValue = removeAllSpecialCharacters(text);
            // eslint-disable-next-line no-underscore-dangle
            this._onChangeText(filteredValue);
          }}
          ref={(ref) => {
            this.textarea = ref;
          }}
        />
        {this.renderCount()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  count: {
    position: 'absolute',
    bottom: -8,
    right: 2,
    ...generalStyles.avenirRoman14,
    color: colors.subGrey,
  },
  container: {
    width: '100%',
    height: 170,
  },
});
