import React from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../styles';

export default function FormCreditCardInfo(props) {
  return (
    <TouchableOpacity
      style={{ ...styles.frame, ...props.customStyle }}
      activeOpacity={1}
      onPress={() => (props.onPressAction ? props.onPressAction() : null)}
    >
      <View style={styles.frame}>
        <Text style={styles.label}>Payment method</Text>
        <Text style={styles.value}>{props.value}</Text>
        <IconFeader
          style={{ flex: 0.5, textAlign: 'right', paddingRight: 11 }}
          name="chevron-down"
          size={25}
          color={colors.subGrey}
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    display: 'flex',
    flexDirection: 'row',
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  label: {
    textAlign: 'left',
    fontSize: 14,
    flex: 4,
    color: colors.black,
    borderWidth: 0,
    paddingLeft: 8,
  },
  value: {
    textAlign: 'right',
    fontSize: 14,
    flex: 2.5,
    color: colors.black,
    borderWidth: 0,
  },
});
