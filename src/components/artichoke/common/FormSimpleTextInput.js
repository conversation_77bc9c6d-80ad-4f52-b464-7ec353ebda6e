import React from 'react';
import { StyleSheet, View, TextInput } from 'react-native';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';
import { removeAllSpecialCharacters } from '../../../util/validate';

export default function FormSimpleTextInput(props) {
  const customProps = { ...props };
  delete customProps.onChangeText;
  return (
    <View style={{ ...styles.frame, ...props.formInputStyle }}>
      <TextInput
        style={{ ...styles.field, ...props.fieldInputStyle }}
        onChangeText={(text) => {
          const filteredText = removeAllSpecialCharacters(text);
          props.onChangeText(filteredText);
        }}
        {...customProps}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    paddingHorizontal: 5,
    display: 'flex',
    flexDirection: 'row',
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  field: {
    ...generalStyles.inputText,
    flex: 1,
    textAlign: 'left',
    height: 50,
  },
});
