import { Image, TouchableOpacity } from 'react-native';
import React from 'react';
import { generalStyles } from '../../../styles/generalStyle';

const editButtonIcon = require('../../../assets/nounEdit1072351.png');

const EditButton = (props) => (
  <TouchableOpacity
    activeOpacity={1}
    onPress={() => props.onPressAction()}
    style={generalStyles.editButtonContainer}
  >
    <Image source={editButtonIcon} style={generalStyles.editButton} />
  </TouchableOpacity>
);
export default EditButton;
