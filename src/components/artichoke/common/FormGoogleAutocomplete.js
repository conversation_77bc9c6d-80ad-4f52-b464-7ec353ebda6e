import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { GoogleAutoComplete } from 'react-native-google-autocomplete';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';
import { GOOGLE_API_KEY } from '../../../apiConstants';
import { removeAllSpecialCharacters } from '../../../util/validate';

export default function FormGoogleAutocomplete(props) {
  return (
    <GoogleAutoComplete apiKey={GOOGLE_API_KEY} debounce={300}>
      {({
        handleTextChange, locationResults, fetchDetails, clearSearch,
      }) => (
        <View style={{ ...styles.frame, ...props.formInputStyle }}>
          <TextInput
            style={styles.field}
            onChangeText={(value) => {
              const filteredValue = removeAllSpecialCharacters(value);
              props.onSelectValue(filteredValue);
              handleTextChange(filteredValue);
            }}
            value={props.inputValue}
            {...props}
          />
          <ScrollView style={styles.scrollView}>
            {locationResults.map((el, i) => (
              <TouchableOpacity
                {...el}
                activeOpacity={1}
                style={styles.autocompleteOpacity}
                key={String(i)}
                onPress={() => fetchDetails(el.place_id).then((detailedResult) => {
                  props.onSelectValue(detailedResult);
                  clearSearch();
                })}
              >
                <Text
                  numberOfLines={2}
                  ellipsizeMode="tail"
                  style={styles.autocompleteCell}
                >
                  {el.description}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </GoogleAutoComplete>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    paddingHorizontal: 5,
    display: 'flex',
    flexDirection: 'column',
    minHeight: 35,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  field: {
    ...generalStyles.inputText,
    color: colors.black,
    flex: 1,
    minHeight: 35,
    width: '100%',
    borderColor: colors.lightgrey,
  },
  scrollView: {
    flex: 1,
    top: 2,
    width: '100%',
    backgroundColor: colors.lightgrey,
  },

  autocompleteOpacity: {
    flex: 1,
    height: 50,
    width: '100%',
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightgrey,
    justifyContent: 'center',
    alignItems: 'center',
  },

  autocompleteCell: {
    width: '100%',
    backgroundColor: colors.white,
    fontSize: 14,
    fontWeight: 'normal',
    marginBottom: 3,
  },
});
