import * as React from 'react';
import {
  FlatList, View, StyleSheet, Text,
} from 'react-native';
import moment from 'moment';
import EmptyBookedList from '../screensComponents/booked/EmptyBookedList';
import BookedListItem from '../screensComponents/booked/BookedListItem';
import SlotBlockerListItem from '../screensComponents/slotblockers/SlotBlockerListItem';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';

const styles = StyleSheet.create({
  container: {
    paddingTop: 0,
  },
  labelday: {
    ...generalStyles.titleSmall,
    width: '100%',
    color: colors.black,
  },
  formday: {
    backgroundColor: colors.veryLightBlue,
    height: 58,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingHorizontal: 21,
  },
});

export default function FormBooking(props) {
  const renderBookedItem = (item) => {
    if (item.name === 'SLOT_BLOCKER') {
      return (
        <SlotBlockerListItem
          item={item}
          onPressItem={props.onPressSlotBlocker}
        />
      );
    }
    return (
      <BookedListItem item={item} onPressItem={props.onPressAppointment} />
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.formday}>
        <Text style={styles.labelday}>
          {moment(props.date, 'YYYY-MM-DD').format('dddd, MMMM Do')}
        </Text>
      </View>

      <FlatList
        data={props.appointments}
        renderItem={({ item }) => renderBookedItem(item)}
        keyExtractor={(item) => `booked-list-previous-date-item-${item.id}`}
        refreshing={props.refreshing}
        ListEmptyComponent={() => (
          <EmptyBookedList message="No booking found" />
        )}
      />
    </View>
  );
}
