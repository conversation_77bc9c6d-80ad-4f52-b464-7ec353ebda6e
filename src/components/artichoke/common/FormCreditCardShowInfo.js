import React from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../styles';

export default function FormCreditCardShowInfo(props) {
  return (
    <TouchableOpacity
      style={{ ...styles.frame, ...props.customStyle }}
      activeOpacity={1}
      onPress={() => (props.onPressAction ? props.onPressAction() : null)}
    >
      <View style={styles.insideFrame}>
        <Text style={styles.label}>Payment method</Text>
        <Text style={styles.value}>{props.value}</Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    display: 'flex',
    flexDirection: 'row',
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  insideFrame: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 10,
  },
  label: {
    textAlign: 'left',
    fontSize: 14,
    flex: 3,
    color: colors.black,
    borderWidth: 0,
  },
  value: {
    textAlign: 'right',
    fontSize: 14,
    flex: 5,
    color: colors.black,
    borderWidth: 0,
  },
});
