import React from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';

export default function FormLocations(props) {
  const pluralSuffix = props.nLocations > 1 ? 's' : '';
  return (
    <TouchableOpacity
      style={{ ...props.formInputStyle }}
      activeOpacity={1}
      onPress={props.onPressFunc}
    >
      <View style={styles.frame}>
        <Text style={styles.label}>{props.label}</Text>
        <Text style={styles.value}>
          {props.nLocations
            ? `${props.nLocations} Location${pluralSuffix}`
            : 'Select Location(s)'}
        </Text>
        <IconFeader
          style={{ flex: 0.5, textAlign: 'right', paddingRight: 6 }}
          name="chevron-right"
          size={25}
          color={colors.subGrey}
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    display: 'flex',
    flexDirection: 'row',
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  label: {
    textAlign: 'left',
    ...generalStyles.inputText,
    flex: 4,
    color: colors.black,
    borderWidth: 0,
    paddingLeft: 8,
  },
  value: {
    textAlign: 'right',
    ...generalStyles.inputText,
    display: 'flex',
    color: colors.black,
    borderWidth: 0,
  },
});
