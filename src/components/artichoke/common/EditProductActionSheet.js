import * as React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import ActionSheet from 'react-native-actions-sheet';
import { colors } from '../../../styles';

export default function EditProductActionSheet(props) {
  return (
    <ActionSheet
      headerAlwaysVisible
      footerAlwaysVisible
      gestureEnabled
      ref={props.actionSheetRef}
      containerStyle={styles.container}
    >
      <View style={styles.contentContainer}>
        <View style={styles.title}>
          <Text style={styles.titleText}>Edit Service</Text>
        </View>
        <View style={styles.content}>
          <Text style={styles.contentDetails} onPress={props.onGoToEditScreen}>
            Details
          </Text>
          <Text style={styles.contentArchive} onPress={props.onArchiveService}>
            Archive
          </Text>
        </View>
      </View>
    </ActionSheet>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.veryLightBlue,
  },
  contentContainer: {
    padding: 30,
    flex: 1,
    flexDirection: 'column',
  },
  title: {
    flex: 1,
    alignItems: 'center',
  },
  titleText: { fontSize: 30 },
  content: { padding: 30, flex: 3, flexDirection: 'column' },
  contentDetails: {
    flex: 1,
    color: colors.azure,
    marginBottom: 10,
    paddingHorizontal: 30,
    paddingVertical: 10,
    backgroundColor: colors.white,
    textAlign: 'center',
    fontSize: 20,
    borderRadius: 30,
  },
  contentArchive: {
    flex: 1,
    color: colors.azure,
    marginBottom: 10,
    paddingHorizontal: 30,
    paddingVertical: 10,
    backgroundColor: colors.white,
    textAlign: 'center',
    fontSize: 20,
    borderRadius: 30,
  },
});
