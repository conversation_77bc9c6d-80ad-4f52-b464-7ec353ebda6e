import React from 'react';
import {
  StyleSheet, Text, View, Platform,
} from 'react-native';
import RNPickerSelect from 'react-native-picker-select';
import moment from 'moment';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';

export default function FormClassTimeInput(props) {
  return (
    <View style={{ ...styles.frame, ...props.formInputStyle }}>
      <Text style={styles.label}>{props.label}</Text>
      <View style={{ flex: 1 }}>
        <RNPickerSelect
          style={{
            inputIOS: {
              ...generalStyles.inputText,
              borderWidth: 0,
              paddingTop: 5,
              paddingBottom: 2,
              textAlign: 'right',
              color: colors.black,
              height: 35, // to ensure the text is never behind the icon
            },
            iconContainer: {
              top: 5,
              right: 5,
            },
            placeholder: {
              ...generalStyles.inputText,
              paddingTop: 3,
              paddingBottom: 2,
              textAlign: 'right',
              height: 35,
              color: colors.subGrey,
              width: 60,
            },
            inputAndroid: {
              ...generalStyles.inputText,
              paddingTop: 3,
              paddingBottom: 2,
              paddingRight: 10,
              textAlign: 'right',
              height: 35,
              width: 70,
            },
          }}
          placeholder={{
            label: 'Time',
            value: 'new',
          }}
          items={props.items}
          multiple
          useNativeAndroidPickerStyle={false}
          itemKey={moment(props.value, 'HH:mm').format('h:mma').toString()}
          value={moment(props.value, 'HH:mm').format('h:mma').toString()}
          onValueChange={(item) => props.onChange(props.day, item, props.index)}
          Icon={() => null}
        />
        {Platform.OS !== 'ios' ? (
          <Text
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
              bottom: 0,
              left: 0,
            }}
          >
            {' '}
          </Text>
        ) : null}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    marginRight: 5,
    display: 'flex',
    paddingHorizontal: 5,
    flexDirection: 'row',
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    borderRadius: 3,
  },
  label: {
    ...generalStyles.inputText,
    width: '70%',
    textAlign: 'left',
    color: colors.black,
    paddingTop: Platform.OS === 'ios' ? 4 : 1.5,
    paddingLeft: 5,
  },
});
