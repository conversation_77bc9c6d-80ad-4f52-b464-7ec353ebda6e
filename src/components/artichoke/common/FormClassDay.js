import React from 'react';
import { Text, View, Platform } from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import RNPickerSelect from 'react-native-picker-select';
import moment from 'moment';
import { generalStyles } from '../../../styles/generalStyle';
import { colors } from '../../../styles';

export default function FormClassDay(props) {
  return (
    <View style={{ flex: 1 }}>
      <RNPickerSelect
        style={{
          inputIOS: {
            fontSize: 14,
            borderColor: colors.lightgrey,
            borderWidth: 1,
            paddingTop: 5,
            paddingBottom: 3,
            paddingLeft: 8,
            color: colors.black,
            height: 35, // to ensure the text is never behind the icon
            borderRadius: 3,
          },
          inputAndroid: {
            ...generalStyles.inputText,
            borderColor: colors.lightgrey,
            borderWidth: 1,
            paddingTop: 3,
            paddingBottom: 3,
            paddingHorizontal: 10,
            color: colors.black,
            height: 35, // to ensure the text is never behind the icon
            borderRadius: 3,
          },
          iconContainer: {
            top: 5,
            left: 5,
          },
        }}
        items={props.items}
        multiple
        max={7}
        placeholder={{ label: 'Day', value: 'new' }}
        useNativeAndroidPickerStyle={false}
        itemKey={moment(props.value, 'e').format('dddd').toLowerCase()}
        value={moment(props.value, 'e').format('dddd').toLowerCase()}
        onValueChange={(item) => {
          if (item !== 'new') {
            props.onChange(
              moment(item, 'dddd').format('e'),
              props.value,
              props.index,
            );
          }
        }}
        Icon={() => (
          <IconFeader
            style={{ flex: 1, textAlign: 'right' }}
            name="chevron-down"
            size={25}
            color={colors.subGrey}
          />
        )}
      />
      {Platform.OS !== 'ios' ? (
        <Text
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            bottom: 0,
            left: 0,
          }}
        >
          {' '}
        </Text>
      ) : null}
    </View>
  );
}
