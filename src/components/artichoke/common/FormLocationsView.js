import React from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';

export default function FormLocationsView(props) {
  return (
    <View style={{ ...props.formInputStyle }}>
      <View style={styles.frame}>
        <Text style={styles.label}>{props.label}</Text>
        <Text style={styles.value}>
          {props.nLocations
            ? props.nLocations > 1
              ? `(${props.nLocations}) Locations`
              : `${props.nLocations} Location`
            : 'Select Location(s)'}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    display: 'flex',
    flexDirection: 'row',
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  label: {
    textAlign: 'left',
    ...generalStyles.inputText,
    flex: 4,
    color: colors.black,
    borderWidth: 0,
    paddingLeft: 8,
  },
  value: {
    textAlign: 'right',
    ...generalStyles.inputText,
    display: 'flex',
    color: colors.black,
    borderWidth: 0,
    marginRight: 5,
  },
});
