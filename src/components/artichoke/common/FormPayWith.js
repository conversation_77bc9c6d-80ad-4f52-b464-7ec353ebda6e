import { CardField } from '@stripe/stripe-react-native';
import React, { PureComponent } from 'react';
import { StyleSheet } from 'react-native';
import { connect } from 'react-redux';
import { saveClientStripeCreditCard } from '../../../actions/artichoke/Clients.actions';
import { colors } from '../../../styles';

const cardNumberPlaceholder = '4242 4242 4242 4242';
const cardExpirationPlaceholder = 'MM/YY';
const cardCVCPlaceholder = 'CVC';
const zipCodePlaceholder = 'ZIP';

class CardTextFieldScreen extends PureComponent {
  handleFieldParamsChange = (valid, params) => {
    this.props.onChangeCreditCardInfo(valid, params);
  };

  render() {
    return (
      <CardField
        placeholder={{
          number: cardNumberPlaceholder,
          expiration: cardExpirationPlaceholder,
          cvc: cardCVCPlaceholder,
          postalCode: zipCodePlaceholder,
        }}
        cardStyle={styles.cardStyle}
        style={styles.field}
        onCardChange={(cardDetails) => {
          if (cardDetails && cardDetails.complete) {
            this.handleFieldParamsChange(true, {});
          }
        }}
      />
    );
  }
}

const styles = StyleSheet.create({
  cardStyle: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    fontSize: 14,
    backgroundColor: colors.white,
    textColor: colors.black,
  },
  field: {
    width: '100%',
    height: 50,
    marginVertical: 30,
  },
});

const mapStateToProps = (state) => ({
  user: state.user.details,
});

const mapDispatchToProps = {
  saveClientStripeCreditCard,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(CardTextFieldScreen);
