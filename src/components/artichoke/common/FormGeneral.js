import React from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';

export default function FormGeneral(props) {
  return (
    <TouchableOpacity onPress={props.onPressFunc} activeOpacity={1}>
      <View style={{ ...styles.frame, ...props.formInputStyle }}>
        <Text style={styles.label}>{props.label}</Text>
        <Text style={{ ...styles.value, ...props.valueStyle }}>
          {props.value}
        </Text>
        <IconFeader
          style={{ flex: 0.5, textAlign: 'right', paddingRight: 6 }}
          name="chevron-down"
          size={25}
          color={colors.subGrey}
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    display: 'flex',
    flexDirection: 'row',
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  label: {
    textAlign: 'left',
    ...generalStyles.inputText,
    flex: 4,
    color: colors.black,
    borderWidth: 0,
    paddingLeft: 8,
  },
  value: {
    textAlign: 'right',
    ...generalStyles.inputText,
    flex: 3.5,
    color: colors.black,
    borderWidth: 0,
    paddingRight: 10,
  },
});
