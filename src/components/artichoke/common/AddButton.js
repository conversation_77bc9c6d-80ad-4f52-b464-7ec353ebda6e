import {
  Image, StyleSheet, TouchableOpacity, View,
} from 'react-native';
import React from 'react';
import { generalStyles } from '../../../styles/generalStyle';
import { FloatingButton } from '../../index';
import { colors, shadow } from '../../../styles';
import { curvedScale } from '../../../util/responsive';

const btnAddGray = require('../../../assets/btnAddGray.png');

const AddButton = (props) => (props.small ? (
  <TouchableOpacity
    {...props}
    activeOpacity={0.2}
    onPress={() => props.onPressAction()}
    style={[
      generalStyles.addButtonContainer,
      props.disabled && { opacity: 0.7 },
    ]}
  >
    <View style={styles.btnView}>
      <Image source={btnAddGray} style={styles.plusIcon} />
    </View>
  </TouchableOpacity>
) : (
  <FloatingButton {...props} onPress={() => props.onPressAction()} />
));
const styles = StyleSheet.create({
  plusIcon: {
    tintColor: colors.white,
    height: curvedScale(30),
    width: curvedScale(30),
    resizeMode: 'contain',
  },
  btnView: {
    width: curvedScale(34),
    height: curvedScale(34),
    borderRadius: curvedScale(34) / 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.macaroniAndCheese,
    marginRight: curvedScale(10),
    marginLeft: curvedScale(13),
    ...shadow,
  },
});

export default AddButton;
