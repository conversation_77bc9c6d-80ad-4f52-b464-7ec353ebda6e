import React from 'react';
import {
  StyleSheet, Text, View, TouchableOpacity,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';

export default function FormServices(props) {
  return (
    <TouchableOpacity
      style={{ ...styles.frame, ...props.formInputStyle }}
      activeOpacity={1}
      onPress={props.onPressFunc}
    >
      <Text style={styles.label}>{props.label}</Text>
      <Text style={{ ...styles.value, ...props.valueStyle }}>
        {props.selectedServices
          ? `(${props.selectedServices}) Services`
          : 'Select Service(s)'}
      </Text>
      <IconFeader
        style={{ flex: 0.5, textAlign: 'right', paddingRight: 6 }}
        name="chevron-right"
        size={25}
        color={colors.subGrey}
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    display: 'flex',
    flexDirection: 'row',
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  label: {
    textAlign: 'left',
    ...generalStyles.inputText,
    flex: 4,
    color: colors.black,
    borderWidth: 0,
    paddingLeft: 8,
  },
  value: {
    textAlign: 'right',
    ...generalStyles.inputText,
    flex: 2.5,
    color: colors.subGrey,
    borderWidth: 0,
    paddingRight: 10,
  },
});
