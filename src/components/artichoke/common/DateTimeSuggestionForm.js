import React, { Component } from 'react';
import {
  StyleSheet, View, Text, TouchableOpacity,
} from 'react-native';
import { colors } from '../../../styles';

export default function DateTimeSuggestionForm(props) {
  const selectedStyle = styles.unselected;
  return (
    <View style={{ ...styles.frame, flexBasis: props.width }} key={props.key}>
      <TouchableOpacity
        activeOpacity={1}
        style={selectedStyle}
        onPress={props.onChangeValue}
      >
        <Text style={styles.label}>{props.buttonTitle}</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  frame: {
    flexGrow: 0,
    flexBasis: '50%',
    height: 55,
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
    paddingRight: 10,
    paddingBottom: 10,
    paddingLeft: 0,
  },
  label: {
    textAlign: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    display: 'flex',
    fontSize: 14,
    flex: 1,
    height: 20,
    fontWeight: 'bold',
    color: colors.bordergrey,
  },
  unselected: {
    flex: 1,
    width: '100%',
    borderColor: colors.bordergrey,
    textAlign: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: 27,
    borderWidth: 2,
  },
});
