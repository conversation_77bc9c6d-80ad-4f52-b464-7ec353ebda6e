import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { colors } from '../../../styles';
import { generalStyles } from '../../../styles/generalStyle';
import CustomTextArea from './CustomTextArea';

export default function FormMultilineTextInput(props) {
  return (
    <View style={{ ...styles.frame, ...props.formInputStyle }}>
      {props.label ? <Text style={styles.label}>{props.label}</Text> : null}
      <CustomTextArea
        {...props}
        containerStyle={styles.field}
        multiline
        numberOfLines={50}
        textAlignVertical="top"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  frame: {
    borderColor: colors.lightgrey,
    borderWidth: 1,
    borderRadius: 3,
    paddingTop: 10,
    paddingHorizontal: 5,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    width: '100%',
    height: 200,
  },
  label: {
    ...generalStyles.avenirRoman14,
    textAlign: 'left',
    display: 'flex',
    height: 20,
    color: colors.black,
    width: '100%',
    marginLeft: 5,
    marginTop: 14,
  },
  field: {
    ...generalStyles.inputText,
    height: 177,
    width: '100%',
    paddingHorizontal: 5,
  },
});
