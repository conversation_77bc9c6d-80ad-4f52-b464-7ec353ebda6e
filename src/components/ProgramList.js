import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import moment from 'moment';

// Components
import {
  Image,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import LoadingSpinner from './LoadingSpinner';
import ProgramCard from './ProgramCard';
import BookingCardItem from './artichoke/screensComponents/client/bookings/BookingCardItem';
import {
  ROLES,
  APPLICATION_CLIENT_ROUTES,
  APPLICATION_ROUTES,
} from '../constants';
import { scaleWidth } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';

import {
  getAppointmentAction,
  clearSelectedAppointment,
  joinVideoCallAction,
} from '../actions/artichoke/Appointments.actions';

// Assets
const restDayBackground = require('../resources/placeholderBgRest.png');
const restDay = require('../resources/imgRestDay.png');
const noProgramScheduled = require('../resources/imgNoProgram.png');

// PropTypes
const propTypes = {
  workoutsDay: PropTypes.arrayOf(
    PropTypes.shape({
      workout: PropTypes.object,
      workout_date: PropTypes.string,
    }),
  ),
  scheduleDays: PropTypes.arrayOf(
    PropTypes.shape({
      workout: PropTypes.object,
      workout_date: PropTypes.string,
    }),
  ),
  selectedDay: PropTypes.string,
  currentUser: PropTypes.shape({
    role: PropTypes.string,
  }).isRequired,
  isLoading: PropTypes.bool,
  onPressAllPrograms: PropTypes.func.isRequired,
  onPressNextAvailableDay: PropTypes.func.isRequired,
};
const defaultProps = {
  scheduleDay: null,
  isLoading: true,
};

// Main Class
class ProgramList extends React.Component {
  onSelectAppointment = (appointment) => {
    this.props.clearSelectedAppointment();
    this.props.getAppointmentAction(appointment.id);
    if (this.props.currentUser.role === ROLES.TRAINER) {
      this.props.navigation.navigate({
        name: APPLICATION_ROUTES.VIEW_CLIENT_BOOKING,
      });
    } else {
      this.props.navigation.navigate({
        name: APPLICATION_CLIENT_ROUTES.SELECTED_APPOINTMENT,
      });
    }
  };

  getNextAvailableWorkout = () => {
    const { scheduleDays } = this.props;
    const workouts = scheduleDays
      .filter((day) => !!day.scheduled_workout)
      .map((day) => day.scheduled_workout);
    if (workouts.length) {
      const sortedWorkouts = workouts.sort(
        (a, b) => moment(a.workout_date) - moment(b.workout_date),
      );
      const today = moment(this.props.selectedDay);
      const nextWorkout = sortedWorkouts.find((scheduleDay) => moment(scheduleDay.workout_date).isAfter(today));
      return nextWorkout;
    }
    return null;
  };

  getNextScheduledProgramDate = () => {
    const nextWorkout = this.getNextAvailableWorkout();
    if (nextWorkout) {
      return moment.utc(nextWorkout.workout_date).format('dddd, MMM D');
    }
    return 'No Programs Available';
  };

  nextProgramName = () => {
    const nextWorkout = this.getNextAvailableWorkout();
    if (nextWorkout) {
      return nextWorkout.workout.name;
    }
    return 'No Programs Available';
  };

  renderWorkouts = (isClient) => this.props.workoutsDay.map((programDay) => {
    const isExpired = new Date(programDay.program_end_date) < new Date();
    return (
      <TouchableOpacity
        key={programDay.id}
        onPress={() => this.props.onSelectWorkout(programDay)}
      >
        <ProgramCard
          key={programDay.user_schedule_id}
          workout={programDay.workout}
          onPressEdit={() => this.props.onPressEdit(programDay)}
          role={this.props.currentUser.role}
          isGuidedWorkout={isClient}
          programName={programDay.program_name}
          programWorkoutName={programDay.workout_name}
          programCategory={programDay.program_category_name}
          totalSeconds={programDay.total_dur_seconds}
          durationPlus={programDay.duration_plus}
          isExpired={isExpired}
          completed={programDay.is_complete}
          updated_at={programDay.updated_at}
        />
      </TouchableOpacity>
    );
  });

  renderAppointments = () => this.props.appointments
    && this.props.appointments[this.props.selectedDay]
    && this.props.appointments[this.props.selectedDay].map((appointment) => (
      <BookingCardItem
        key={appointment.id}
        item={appointment}
        onPressItem={this.onSelectAppointment}
        userDetails={this.props.userDetails}
        navigation={this.props.navigation}
        joinVideoCallAction={this.props.joinVideoCallAction}
        joinVideoLoading={this.props.joinVideoLoading}
        joiningAppointmentId={this.props.joiningAppointmentId}
      />
    ));

  render() {
    const isClient = this.props.currentUser.role === ROLES.CLIENT;
    if (this.props.isLoading || this.props.clientBookingsListLoading) {
      return (
        <View style={styles.programLoading}>
          <LoadingSpinner
            visible
            size="large"
            backgroundColor={colors.white}
            title="Loading Workouts"
          />
        </View>
      );
    }
    if (
      this.props.workoutsDay.length
      || (this.props.appointments
        && this.props.appointments[this.props.selectedDay]
        && this.props.appointments[this.props.selectedDay].length)
    ) {
      return (
        <View>
          {this.renderAppointments()}
          {this.renderWorkouts(isClient)}
        </View>
      );
    }
    // no scheduled program for the given day
    const nextWorkout = this.getNextAvailableWorkout();
    if (this.props.currentUser.role === ROLES.TRAINER) {
      if (this.nextProgramName() === 'No Programs Available') {
        return (
          <View>
            <View style={styles.container2}>
              <Image style={styles.stretch} source={noProgramScheduled} />
              <Text style={styles.headerText}>No Workout Scheduled</Text>
            </View>
          </View>
        );
      }
      return (
        <View>
          <View style={styles.container2}>
            <Image style={styles.stretch} source={restDay} />
            <Text style={styles.headerText}>Rest Day</Text>
            <View style={styles.MainContainer}>
              <TouchableOpacity
                style={styles.EditScheduleButtonStyle}
                activeOpacity={0.5}
                onPress={this.props.onPressAllPrograms}
              >
                <Text
                  style={{
                    fontFamily: 'Avenir-Heavy',
                    color: colors.white,
                    position: 'relative',
                    fontSize: 17,
                  }}
                >
                  Edit Schedule
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      );
    }
    return (
      <TouchableOpacity
        disabled={!nextWorkout}
        onPress={() => this.props.onPressNextAvailableDay(nextWorkout.workout_date)}
        style={styles.emptyStateContainer}
      >
        <View style={styles.restDayCard}>
          <ImageBackground
            style={styles.restDaySectionContainer}
            imageStyle={{ borderTopLeftRadius: 6, borderTopRightRadius: 6 }}
            source={restDayBackground}
          >
            <Text style={styles.noSessionsText}>No Sessions</Text>
            <Text style={styles.restDayText}>Rest Day</Text>
            <TouchableOpacity
              style={styles.viewAllProgramsButton}
              onPress={this.props.onPressAllPrograms}
            >
              <Text style={styles.viewAllProgramsButtonText}>
                View Assigned Programs
              </Text>
            </TouchableOpacity>
          </ImageBackground>
          <View style={styles.restDaySectionContainer}>
            <Text style={styles.nextProgramDaysText}>
              {`${this.getNextScheduledProgramDate()}`}
            </Text>
            <Text style={styles.upcomingProgramText}>Upcoming Program</Text>
            <Text style={styles.programNameText}>{this.nextProgramName()}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}

// Exports
ProgramList.propTypes = propTypes;
ProgramList.defaultProps = defaultProps;

const mapStateToProps = (state) => ({
  userDetails: state.user.details,
  clientBookingsListLoading: state.loadingComponents.clientBookingsListLoading,
  joinVideoLoading: state.loadingComponents.joinVideoLoading,
  joiningAppointmentId: state.loadingComponents.joiningAppointmentId,
});

export default connect(mapStateToProps, {
  clearSelectedAppointment,
  getAppointmentAction,
  joinVideoCallAction,
})(ProgramList);

// Styles
const styles = StyleSheet.create({
  emptyStateContainer: {
    paddingHorizontal: scaleWidth(5),
    backgroundColor: 'rgba(0,0,0,0)',
  },
  container2: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 100,
  },
  EditScheduleButtonStyle: {
    marginTop: 20,
    paddingVertical: 16,
    paddingHorizontal: 50,
    backgroundColor: colors.macaroniAndCheese,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 28,
  },
  restDayCard: {
    backgroundColor: colors.white,
    borderRadius: 6,
    marginTop: 14,
    marginBottom: 5,
    ...shadow,
  },
  headerText: {
    fontFamily: 'Avenir-Roman',
    color: colors.black,
    position: 'relative',
    fontSize: 22,
    marginTop: 24,
  },
  restDaySectionContainer: {
    flex: 1,
    padding: 18,
    justifyContent: 'space-between',
    backgroundColor: colors.transparent,
  },
  viewAllProgramsButton: {
    justifyContent: 'center',
    borderRadius: 16,
    borderStyle: 'solid',
    borderWidth: 2,
    borderColor: 'rgba(150, 150, 150, 1)',
    alignSelf: 'center',
    height: 36,
    marginTop: 10,
  },
  viewAllProgramsButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'center',
    color: colors.white,
    paddingLeft: 24,
    paddingRight: 24,
  },
  noSessionsText: {
    fontSize: 12,
    textAlign: 'left',
    color: colors.white,
    backgroundColor: 'transparent',
  },
  restDayText: {
    fontSize: 16,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.white,
  },
  upcomingProgramText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    textAlign: 'left',
    color: colors.subGrey,
  },
  programNameText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    textAlign: 'left',
    color: colors.black,
  },
  nextProgramDaysText: {
    fontFamily: 'Avenir',
    fontSize: 28,
    fontWeight: '900',
    color: colors.nasmBlue,
  },
  programLoading: {
    marginHorizontal: scaleWidth(5),
    marginTop: 14,
    height: 113,
  },
});
