import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image,
  LayoutAnimation,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import ScaledText from './ScaledText';
import { scaleHeight } from '../util/responsive';

// Styles
import { colors } from '../styles';

// Constants
import { androidSafeLayoutAnimation } from '../constants';

const downIcon = require('../resources/btnArrowDown.png');
const check = require('../../assets/imgCheckmarkOnMedium.png');

// Props
const propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
    }),
  ).isRequired,
  autoSelectFirstValue: PropTypes.bool,
  labelKey: PropTypes.string,
  subLabelKey: PropTypes.string,
  onPress: PropTypes.func,
  onPickerVisible: PropTypes.func,
  onValueChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  selected: PropTypes.string,
  visible: PropTypes.bool,
  containerStyle: PropTypes.object,
  label: PropTypes.string,
  width: PropTypes.number,
  labelStyle: PropTypes.object,
  renderSelectedValueText: PropTypes.bool,
  textStyle: PropTypes.object,
  subLabelStyle: PropTypes.object,
  selectedItems: PropTypes.array,
};

const defaultProps = {
  autoSelectFirstValue: true,
  labelKey: 'label',
  subLabelKey: null,
  onPress: null,
  onPickerVisible: null,
  placeholder: 'SELECT',
  label: null,
  renderSelectedValueText: true,
  selected: null,
  visible: false,
  selectedItems: [],
};

// Component
class DropDownMultiPicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  getLabel() {
    if (!this.props.selectedItems.length && this.props.placeholder) return this.props.placeholder;
    return this.props.selectedItems.length;
  }

  dropDownItemSelected = (item) => {
    LayoutAnimation.configureNext(androidSafeLayoutAnimation);
    const exists = this.props.selectedItems.find((i) => i.id === item.id) != null;
    const selected = exists
      ? this.props.selectedItems.filter((i) => i.id !== item.id)
      : this.props.selectedItems.concat(item);
    this.props.onValueChange(selected);
  };

  togglePicker = () => {
    // Set initial date on open so the user doesn't have to scroll to select the default value
    if (this.props.autoSelectFirstValue && !this.props.selected) this.props.onValueChange(this.props.data[0].id);
    // Callback to allow for custom onPress action (e.g. parent wants to control picker state)
    if (this.props.onPress) {
      this.props.onPress();
    } else {
      LayoutAnimation.configureNext(androidSafeLayoutAnimation);
      this.setState({ visible: !this.state.visible }, () => {
        if (this.props.onPickerVisible) {
          setTimeout(() => this.props.onPickerVisible(this.state.visible), 0);
        }
      });
    }
  };

  renderDropDownItem = (item) => {
    const labelStyle = [this.props.labelStyle];
    const selectedBackgroundColor = this.props.selectedBackgroundColor || colors.veryLightBlue;
    const isSelected = this.props.selectedItems.find((i) => i.id === item.id) != null;
    const background = isSelected ? selectedBackgroundColor : 'transparent';

    return (
      <TouchableOpacity
        key={item.id}
        style={[styles.itemContainer, { backgroundColor: background }]}
        onPress={() => this.dropDownItemSelected(item)}
      >
        <View style={styles.itemRow}>
          <ScaledText style={labelStyle}>
            {item[this.props.labelKey]}
          </ScaledText>
          {isSelected && <Image source={check} style={styles.checkMark} />}
        </View>
        {this.props.subLabelKey && (
          <ScaledText style={this.props.subLabelStyle}>
            {item[this.props.subLabelKey]}
          </ScaledText>
        )}
      </TouchableOpacity>
    );
  };

  render() {
    const isVisible = this.state.visible || this.props.visible;
    const imageStyle = isVisible ? { transform: [{ rotate: '180deg' }] } : {};
    const containerStyle = [styles.inputContainer, this.props.containerStyle];
    const selectedTextStyle = [
      this.props.selected ? styles.inputText : styles.placeholderText,
      this.props.textStyle ? this.props.textStyle : {},
    ];
    return (
      <View style={{ width: this.props.width }}>
        {/* Main Container */}
        <TouchableOpacity onPress={this.togglePicker} style={containerStyle}>
          {this.props.label != null && (
            <ScaledText style={this.props.labelStyle}>
              {this.props.label}
            </ScaledText>
          )}
          <View
            style={[
              styles.selectionContainer,
              this.props.label && { flex: 1, justifyContent: 'flex-end' },
            ]}
          >
            {this.props.renderSelectedValueText && (
              <ScaledText style={selectedTextStyle}>
                {this.getLabel()}
              </ScaledText>
            )}
            <Image style={imageStyle} source={downIcon} />
          </View>
        </TouchableOpacity>
        {/* Drop Down Items */}
        {isVisible && (
          <View style={{ paddingBottom: 10 }}>
            {this.props.data.map((item) => this.renderDropDownItem(item))}
          </View>
        )}
      </View>
    );
  }
}

// Exports
DropDownMultiPicker.propTypes = propTypes;
DropDownMultiPicker.defaultProps = defaultProps;
export default DropDownMultiPicker;

// Styles
const styles = StyleSheet.create({
  inputContainer: {
    height: scaleHeight(4.5),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: colors.black,
  },
  placeholderText: {
    fontFamily: 'Avenir-Medium',
    fontSize: 14,
    color: colors.subGrey,
  },
  itemContainer: {
    paddingHorizontal: 20,
    borderColor: 'rgba(182, 189, 195, 0.5)',
    borderBottomWidth: 1,
    height: 50,
    justifyContent: 'center',
  },
  selectionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  checkMark: {
    width: 26,
    height: 26,
  },
});
