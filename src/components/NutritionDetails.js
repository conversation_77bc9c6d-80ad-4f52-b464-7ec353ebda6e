import React from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Keyboard, Text, TextInput, View,
} from 'react-native';
import { calculateCalories } from '../constants';

// Styles
import { colors } from '../styles';
import { removeAllSpecialCharacters } from '../util/validate';

const propTypes = {
  nutrition: PropTypes.shape({
    protein: PropTypes.number,
    carbohydrates: PropTypes.number,
    fat: PropTypes.number,
    calories: PropTypes.number,
    created_at: PropTypes.string,
    updated_at: PropTypes.string,
  }),
  onChangeNutritionProperty: PropTypes.func,
};
const defaultProps = {
  nutrition: {
    protein: 0,
    carbohydrates: 0,
    fat: 0,
    calories: 0,
  },
  onChangeNutritionProperty: null,
};

let nutritionData = [];

class NutritionDetails extends React.Component {
  constructor(props) {
    super(props);

    this.inputRefs = {};
    this.updateNutritionData(props);
    this.state = {
      activeTextField: null,
    };
  }

  componentDidMount() {
    this.keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        this.setState({ focusedTextField: null });
      },
    );
  }

  componentDidUpdate(prevProps) {
    if (
      JSON.stringify(prevProps.nutrition)
      !== JSON.stringify(this.props.nutrition)
    ) {
      this.updateNutritionData(this.props);
    }
  }

  componentWillUnmount() {
    this.keyboardDidHideListener.remove();
  }

  parseIntOrZero = (input) => {
    let number = parseInt(input, 10);
    if (Number.isNaN(number)) {
      number = 0;
    }
    return number;
  };

  updateCalories() {
    // Formula for calculating calories: Calories = (Protein x 4) + (Carbs x 4) + (Fat x 9)
    const protein = nutritionData[0].value;
    const carbs = nutritionData[1].value;
    const fat = nutritionData[2].value;
    const toUpdate = nutritionData[3];
    toUpdate.value = calculateCalories(protein, carbs, fat);
    if (typeof this.props.onChangeNutritionProperty === 'function') {
      this.props.onChangeNutritionProperty(toUpdate.key, toUpdate.value);
    }
  }

  updateNutritionData(props) {
    const nutrition = props.nutrition || defaultProps.nutrition;
    nutritionData = [
      {
        key: 'protein',
        value: this.parseIntOrZero(nutrition.protein),
        label: 'protein',
        units: '(g)',
        isEditable: true,
      },
      {
        key: 'carbohydrates',
        value: this.parseIntOrZero(nutrition.carbohydrates),
        label: 'carbs',
        units: '(g)',
        isEditable: true,
      },
      {
        key: 'fat',
        value: this.parseIntOrZero(nutrition.fat),
        label: 'fat',
        units: '(g)',
        isEditable: true,
      },
      {
        key: 'calories',
        value: this.parseIntOrZero(nutrition.calories),
        label: 'kcal',
        isEditable: false,
      },
    ];
  }

  renderInputView(id, value = 0, label = '', units = null, isEditable = true) {
    let inputComponent = (
      <Text
        style={{
          fontFamily: 'Avenir-Roman',
          fontSize: 27,
          color: colors.white,
        }}
      >
        {value}
      </Text>
    );
    if (isEditable) {
      let focusedTextFieldStyle = {};
      if (this.state.focusedTextField === id) {
        focusedTextFieldStyle = { color: colors.black };
      }
      inputComponent = (
        <View style={{ flexDirection: 'row' }}>
          <TextInput
            ref={(ref) => {
              if (id) {
                this.inputRefs.id = ref;
              }
            }}
            onFocus={() => this.setState({ focusedTextField: id })}
            onChangeText={(text) => {
              const filteredText = removeAllSpecialCharacters(text).replace(
                /\D/gm,
                '',
              );
              const toUpdate = nutritionData[id];
              let number = parseInt(filteredText, 10);
              if (Number.isNaN(number)) {
                number = 0;
              }
              toUpdate.value = number;
              this.updateCalories();
              if (typeof this.props.onChangeNutritionProperty === 'function') {
                this.props.onChangeNutritionProperty(
                  toUpdate.key,
                  toUpdate.value,
                );
              }
            }}
            maxLength={3}
            textAlign="center"
            keyboardType="numeric"
            style={[styles.textInput, focusedTextFieldStyle]}
            value={
              nutritionData[id].value === 0
              || Number.isNaN(nutritionData[id].value)
                ? ''
                : `${nutritionData[id].value}`
            }
          />
        </View>
      );
    }
    return (
      <View key={`${id}`} style={styles.nutritionRecordContainer}>
        <View
          style={[
            styles.nutritionRecordSubContainer,
            { flexDirection: 'row' },
          ]}
        >
          <Text style={styles.nutritionLabel}>{label}</Text>
          {!!units && <Text style={styles.nutritionUnitsLabel}>{units}</Text>}
        </View>
        <View style={styles.nutritionRecordSubContainer}>
          <View
            style={[
              styles.inputContainer,
              isEditable ? {} : { backgroundColor: colors.cloudyBlue },
            ]}
          >
            {inputComponent}
          </View>
        </View>
      </View>
    );
  }

  render() {
    return (
      <View style={this.props.style}>
        {nutritionData.map((data, index) => this.renderInputView(
          index,
          data.value,
          data.label,
          data.units,
          data.isEditable,
        ))}
      </View>
    );
  }
}

const styles = {
  nutritionRecordContainer: {
    height: 61,
    flexDirection: 'row',
    marginBottom: 16,
  },
  nutritionRecordSubContainer: {
    flex: 0.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    flex: 1,
    width: '70%',
    borderRadius: 58.5,
    backgroundColor: colors.paleGray3,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  nutritionLabel: {
    fontFamily: 'Avenir-Medium',
    fontSize: 16,
    color: 'black',
  },
  nutritionUnitsLabel: {
    fontFamily: 'Avenir-Medium',
    fontSize: 16,
    color: colors.subGrey,
    marginLeft: 4,
  },
  textInput: {
    flex: 1,
    width: '100%',
    fontFamily: 'Avenir-Roman',
    fontSize: 27,
    color: colors.subGrey,
  },
};

NutritionDetails.propTypes = propTypes;
NutritionDetails.defaultProps = defaultProps;

export default NutritionDetails;
