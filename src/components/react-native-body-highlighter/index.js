import React, { memo, useState, useCallback } from 'react';
import {
  View,
  TouchableWithoutFeedback,
  Modal,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import PropTypes from 'prop-types';
import Svg, { Polygon, Path } from 'react-native-svg';
// eslint-disable-next-line import/no-extraneous-dependencies
import _ from 'lodash';
import bodyFront from './assets/bodyFront';
import bodyBack from './assets/bodyBack';
import bodyFrontCombined from './assets/bodyFrontCombined';
import bodyBackCombined from './assets/bodyBackCombined';

const propTypes = {
  data: PropTypes.array.isRequired,
  colors: PropTypes.array,
  scale: PropTypes.number,
  onMusclePress: PropTypes.func,
  zoomOnPress: PropTypes.bool,
  frontOnly: PropTypes.bool,
  backOnly: PropTypes.bool,
  isBodyMap: PropTypes.bool,
};

const defaultProps = {
  colors: [],
  scale: 1,
  onMusclePress: null,
  zoomOnPress: false,
  frontOnly: false,
  backOnly: false,
  isBodyMap: false,
};

const comparison = (a, b) => a.slug === b.slug;

const Body = ({
  onMusclePress,
  zoomOnPress,
  colors,
  data,
  scale,
  frontOnly,
  backOnly,
  isBodyMap,
}) => {
  const [openInModal, setOpenInModal] = useState(false);

  /**
   * Function to retrieve the data of the selected muscles from body parts JSON.
    @param bodyMuscles Array containing the front or back body muscles, whichever is selected currently.
    @param data Array that contains the selected muscles, received from parent component.
    @param selectedMusclesData Array that contains the selected muscles with their slug, color and pointsArray.
  */
  const mergedMuscles = useCallback(
    (bodyMuscles) => {
      const selectedMusclesData = data
        .map((d) => bodyMuscles.find((t) => t.slug === d.slug))
        .filter((val) => !!val);
      const coloredMuscles = selectedMusclesData.map((d) => {
        const muscle = data.find((e) => e.slug === d.slug);
        let colorIntensity = 1;
        if (muscle && muscle.intensity) {
          colorIntensity = muscle.intensity;
        }
        return { ...d, color: colors[colorIntensity - 1] };
      });
      const formattedMuscles = _.differenceWith(bodyMuscles, data, comparison);
      return [...formattedMuscles, ...coloredMuscles];
    },
    [data, colors],
  );

  const getColorToFill = (muscle) => {
    let color;
    if (muscle.intensity) {
      color = colors[muscle.intensity];
    } else {
      color = muscle.color;
    }

    return color;
  };

  const handleMusclePress = (muscle) => {
    if (onMusclePress && !zoomOnPress) {
      onMusclePress(muscle);
    }

    if (zoomOnPress && !openInModal) {
      setOpenInModal(!openInModal);
    }
  };

  const renderBodySvg = (muscleData) => (
    <Svg height={200 * scale} width={100 * scale}>
      {mergedMuscles(muscleData).map((muscle) => {
        if (!muscle.pointsArray) {
          return null;
        }
        const newPointsArray = muscle.pointsArray.map((points) => points
          .split(' ')
          .map((p) => `${parseFloat(p) * scale}`)
          .join(' '));

        return newPointsArray.map((points) => (
          <Polygon
            key={points}
            onPress={() => handleMusclePress(muscle)}
            id={muscle.slug}
            fill={getColorToFill(muscle)}
            points={points}
          />
        ));
      })}
    </Svg>
  );

  return (
    <TouchableWithoutFeedback
      onPress={() => zoomOnPress && setOpenInModal(!openInModal)}
    >
      <View>
        <View style={styles.bodyContainer}>
          {frontOnly
            && renderBodySvg(isBodyMap ? bodyFrontCombined : bodyFront)}
          {backOnly && renderBodySvg(isBodyMap ? bodyBackCombined : bodyBack)}
        </View>
        <Modal
          hardwareAccelerated
          presentationStyle="fullScreen"
          animationType="none"
          transparent={false}
          visible={openInModal}
        >
          <View style={styles.modalContainer}>
            <TouchableOpacity
              style={styles.closeModal}
              onPress={() => setOpenInModal(!openInModal)}
            >
              <Svg height="24" width="24">
                <Path
                  d="M24 20.188l-8.315-8.209 8.2-8.282-3.697-3.697-8.212 8.318-8.31-8.203-3.666 3.666 8.321 8.24-8.206 8.313 3.666 3.666 8.237-8.318 8.285 8.203z"
                  fill="black"
                />
              </Svg>
            </TouchableOpacity>
            <View style={styles.modalContent}>
              {frontOnly
                && renderBodySvg(isBodyMap ? bodyFrontCombined : bodyFront)}
              {backOnly
                && renderBodySvg(isBodyMap ? bodyBackCombined : bodyBack)}
            </View>
          </View>
        </Modal>
      </View>
    </TouchableWithoutFeedback>
  );
};

Body.defaultProps = {
  scale: 1,
  colors: ['#0984e3', '#74b9ff'],
  backOnly: false,
  frontOnly: false,
  zoomOnPress: false,
};

const styles = StyleSheet.create({
  bodyContainer: {
    flexDirection: 'row',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    transform: [{ scale: 2 }],
    flexDirection: 'row',
  },
  closeModal: {
    position: 'absolute',
    top: 30,
    right: 10,
  },
});

Body.propTypes = propTypes;
Body.defaultProps = defaultProps;

export default memo(Body);
