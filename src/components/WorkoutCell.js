import React, { Component } from 'react';
import PropTypes from 'prop-types';

import {
  View, Image, StyleSheet, Text,
} from 'react-native';
import { curvedScale, scaleWidth } from '../util/responsive';

import { getDurationFromSeconds } from '../util/programUtils';

import { colors } from '../styles';

const backgroundImage = require('../resources/imgExerciseBlockDefault.png');

const propTypes = {
  workout: PropTypes.object.isRequired,
};

class WorkoutCell extends Component {
  getImageSource = (workout) => {
    if (workout?.uploaded_media?.thumbnail_url) {
      return { uri: workout.uploaded_media.thumbnail_url };
    }
    return backgroundImage;
  };

  renderExerciseWorkout = (workout) => (
    <View style={styles.workoutTextContainer}>
      <Text style={styles.workoutName}>
        {workout.workout_name || workout.name}
      </Text>
      <View style={styles.rowContainer}>
        <View style={{ flex: 1 }}>
          <Text style={styles.workoutInfo}>
            {workout.exercise_count}
            {' '}
            exercise
            {workout.exercise_count === 1 ? '' : 's'}
          </Text>
        </View>
        <Text style={styles.workoutInfo}>
          {getDurationFromSeconds(
            workout.total_dur_seconds,
            workout.duration_plus,
          )}
        </Text>
      </View>
    </View>
  );

  renderVideoWorkout = (workout) => (
    <>
      <View style={styles.imageContainer}>
        <Image style={styles.thumbnail} source={this.getImageSource(workout)} />
      </View>
      <View style={styles.workoutTextContainer}>
        <Text style={{ ...styles.workoutName, ...styles.marginLeft }}>
          {workout.name}
        </Text>
        <View style={styles.revRowContainer}>
          <Text style={styles.workoutInfo}>
            {workout.uploaded_media?.duration
              ? getDurationFromSeconds(workout.uploaded_media?.duration, 0)
              : getDurationFromSeconds(0, 0)}
          </Text>
        </View>
      </View>
    </>
  );

  render() {
    return this.props.workout?.uploaded_media_id
      ? this.renderVideoWorkout(this.props.workout)
      : this.renderExerciseWorkout(this.props.workout);
  }
}

const styles = StyleSheet.create({
  workoutTextContainer: {
    paddingRight: 5,
    flex: 1,
    justifyContent: 'center',
  },
  imageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  workoutName: {
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    color: colors.black,
  },
  marginLeft: {
    marginLeft: scaleWidth(7),
  },
  workoutInfo: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    color: colors.subGreyTwo,
  },
  thumbnail: {
    width: curvedScale(76),
    height: curvedScale(53),
    borderRadius: curvedScale(4),
  },
  rowContainer: {
    flexDirection: 'row',
  },
  revRowContainer: {
    flexDirection: 'row-reverse',
  },
});

WorkoutCell.propTypes = propTypes;
export default WorkoutCell;
