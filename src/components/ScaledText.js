import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { Text } from 'react-native';
import { curvedScale } from '../util/responsive';

const defaultProps = { scaleFactor: 0.5, baseWidth: 375 };
const propTypes = {
  scaleFactor: PropTypes.number,
  baseWidth: PropTypes.number,
};

class ScaledText extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    const {
      style, children, scaleFactor, ...props
    } = this.props;
    // Handling case where no styles are defined
    // TODO: this is unreliable, sometimes style can be an array of style objects which will cause it to default to 14
    let fontSize;
    if (style) {
      fontSize = style.fontSize ? style.fontSize : 14;
    } else {
      fontSize = 14;
    }
    return (
      <Text
        style={[style, { fontSize: curvedScale(fontSize, scaleFactor) }]}
        {...props}
      >
        {children}
      </Text>
    );
  }
}

ScaledText.propTypes = propTypes;
ScaledText.defaultProps = defaultProps;
export default ScaledText;
