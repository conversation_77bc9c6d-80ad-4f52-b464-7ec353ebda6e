import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Alert,
} from 'react-native';

// Styles
import { colors, shadow } from '../styles';

// Images
const unchecked = require('../../assets/incomplete.png');
const checked = require('../../assets/completed.png');

// Props
const propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string,
  isChecked: PropTypes.bool,
  onPress: PropTypes.func.isRequired,
  variant: PropTypes.oneOf(['default', 'condensed']),
  exclusive: PropTypes.bool,
  testID: PropTypes.string,
  disabled: PropTypes.bool,
  infoUrl: PropTypes.string,
  pressedInfo: PropTypes.func,
};

const defaultProps = {
  isChecked: false,
  variant: 'default',
  subtitle: null,
  exclusive: false,
  testID: null,
  disabled: false,
  infoUrl: null,
  pressedInfo: () => Alert.alert('pressed info function not defined'),
};

// Compoenent
class CheckListItem extends Component {
  render() {
    const {
      title,
      subtitle,
      isChecked,
      exclusive,
      onPress,
      variant,
      disabled,
      infoUrl,
    } = this.props;
    return (
      <View>
        <TouchableOpacity
          key={title}
          style={[
            !disabled && styles.checkItemContainer,
            !disabled && isChecked && styles.checked,
            !disabled && exclusive && !isChecked && styles.unchecked,
            disabled && styles.disabledContainer,
          ]}
          onPress={onPress}
          testID={this.props.testID}
          disabled={disabled}
        >
          <View style={styles.checkItemTextContainer}>
            <Text
              style={[
                !disabled && styles.checkTitleText,
                !disabled && variant === 'condensed' && styles.condensedText,
                disabled && styles.disabledText,
              ]}
            >
              {title}
            </Text>
            {subtitle && !disabled && (
              <Text style={styles.checkSubtitleText}>{subtitle}</Text>
            )}
          </View>
          {!disabled && <Image source={isChecked ? checked : unchecked} />}
        </TouchableOpacity>
        {!!infoUrl && (
          <TouchableOpacity onPress={() => this.props.pressedInfo(infoUrl)}>
            <Text style={styles.learnMore}>LEARN MORE</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }
}

// Exports
CheckListItem.propTypes = propTypes;
CheckListItem.defaultProps = defaultProps;
export default CheckListItem;

// Styles
const styles = StyleSheet.create({
  checkItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 7.5,
    paddingVertical: 15,
    paddingHorizontal: 19,
    borderRadius: 3,
    backgroundColor: colors.buttonBlue,
    ...shadow,
  },
  disabledContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 7.5,
    paddingVertical: 15,
    paddingHorizontal: 19,
    borderRadius: 3,
    borderWidth: 2,
    borderColor: colors.subGrey,
  },
  checkItemTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  checked: {
    backgroundColor: colors.goodGreen,
  },
  unchecked: {
    backgroundColor: colors.subGrey,
  },
  checkTitleText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 38,
    color: colors.white,
  },
  checkSubtitleText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    color: colors.lightBlue,
  },
  // variants
  condensedText: {
    fontFamily: 'Avenir',
    fontSize: 15,
    fontWeight: '900',
    color: colors.white,
    marginVertical: 6,
  },
  disabledText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.subGrey,
  },
  learnMore: {
    fontFamily: 'Avenir',
    fontSize: 13,
    fontWeight: '900',
    letterSpacing: 0,
    textAlign: 'right',
    color: colors.nasmBlue,
    alignSelf: 'flex-end',
    marginTop: 15,
    marginBottom: 10,
  },
});
