import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import { Text, StyleSheet, TouchableOpacity } from 'react-native';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  selected: PropTypes.bool,
  title: PropTypes.string.isRequired,
  onPress: PropTypes.func.isRequired,
  selectedStyle: PropTypes.shape({}),
  deselectedStyle: PropTypes.shape({}),
};
const defaultProps = {
  selected: false,
  selectedStyle: {},
  deselectedStyle: {},
};

// Class
class TextToggle extends Component {
  render() {
    return (
      <TouchableOpacity
        onPress={this.props.onPress}
        style={
          this.props.selected
            ? [styles.containerSelected, this.props.selectedStyle]
            : [styles.container, this.props.deselectedStyle]
        }
      >
        <Text
          style={
            this.props.selected ? styles.selectedTextStyle : styles.textStyle
          }
        >
          {this.props.title}
        </Text>
      </TouchableOpacity>
    );
  }
}

// Export
TextToggle.propTypes = propTypes;
TextToggle.defaultProps = defaultProps;
export default TextToggle;

// Styles
const styles = StyleSheet.create({
  container: {
    width: 33,
    height: 33,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderRadius: 17,
    borderColor: colors.silver,
  },
  textStyle: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    textAlign: 'center',
    color: colors.subGrey,
  },
  containerSelected: {
    width: 33,
    height: 33,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 17,
    backgroundColor: colors.goodGreen,
  },
  selectedTextStyle: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.white,
  },
});
