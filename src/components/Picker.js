import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image,
  LayoutAnimation,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import { Picker } from '@react-native-picker/picker';

// Styles
import { colors } from '../styles';

// Constants
import { androidSafeLayoutAnimation } from '../constants';

const downIcon = require('../resources/btnArrowDown.png');
const downIconOutline = require('../resources/btnArrowDown.png');

// Styles
const styles = StyleSheet.create({
  inputContainer: {
    height: 45,
    marginTop: 15,
    paddingTop: 3,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 5,
  },
  inputText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    color: colors.black,
  },
  placeholderText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    color: colors.textPlaceholder,
  },
  inputUnderline: {
    borderBottomWidth: 1,
    borderBottomColor: colors.silver,
  },
  inputOutline: {
    borderWidth: 1,
    borderColor: colors.silver,
    paddingTop: 0,
    paddingRight: 12,
  },
  pickerStyle: {
    marginTop: Platform.OS === 'android' ? 10 : 0,
    backgroundColor: colors.white,
  },
  androidDownIcon: {
    position: 'absolute',
    right: 20,
    top: 23,
  },
});

// Props
const propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.string,
    }),
  ).isRequired,
  onPress: PropTypes.func,
  onIosPickerVisible: PropTypes.func,
  onValueChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  selected: PropTypes.string,
  visible: PropTypes.bool,
  outline: PropTypes.bool,
  outlineIcon: PropTypes.bool,
  style: PropTypes.object,
  label: PropTypes.string,
  labelStyle: PropTypes.object,
  textStyle: PropTypes.object,
  testID: PropTypes.string,
};

const defaultProps = {
  onPress: null,
  osPickerVisible: null,
  placeholder: 'SELECT',
  selected: null,
  visible: false,
  outline: false,
  testID: undefined,
};

// Component
class CustomPicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  getLabel() {
    if (!this.props.selected && !this.props.placeholder)
      return this.props.data[0].label;
    if (!this.props.selected && this.props.placeholder)
      return this.props.placeholder;
    const selectedItem = this.props.data.filter(
      item => item.value === this.props.selected,
    )[0];
    return selectedItem ? selectedItem.label : this.props.placeholder;
  }

  togglePicker = () => {
    // Set initial date on open so the user doesn't have to scroll to select the default value
    if (!this.props.selected)
      this.props.onValueChange(this.props.data[0].value);
    // Callback to allow for custom onPress action (e.g. parent wants to control picker state)
    if (this.props.onPress) {
      this.props.onPress();
    } else {
      LayoutAnimation.configureNext(androidSafeLayoutAnimation);
      this.setState({ visible: !this.state.visible }, () => {
        if (this.props.onIosPickerVisible) {
          setTimeout(
            () => this.props.onIosPickerVisible(this.state.visible),
            0,
          );
        }
      });
    }
  };

  render() {
    return (
      <View style={{ flex: 1 }}>
        {Platform.OS === 'ios' && (
          <TouchableOpacity
            testID={this.testID}
            style={[
              styles.inputContainer,
              this.props.outline ? styles.inputOutline : styles.inputUnderline,
              this.props.style ? this.props.style : {},
            ]}
            onPress={this.togglePicker}>
            {this.props.label && (
              <Text style={this.props.labelStyle}>{this.props.label}</Text>
            )}
            <Text
              style={[
                this.props.selected ? styles.inputText : styles.placeholderText,
                this.props.textStyle ? this.props.textStyle : {},
              ]}>
              {this.getLabel()}
            </Text>
            <Image
              style={
                this.state.visible || this.props.visible
                  ? { transform: [{ rotate: '180deg' }] }
                  : {}
              }
              source={
                this.props.outline || this.props.outlineIcon
                  ? downIconOutline
                  : downIcon
              }
            />
          </TouchableOpacity>
        )}
        {(this.state.visible ||
          this.props.visible ||
          Platform.OS === 'android') && (
          <View
            style={[
              this.props.outline ? styles.inputOutline : styles.inputUnderline,
              this.props.style ? this.props.style : {},
            ]}>
            <Picker
              selectedValue={this.props.selected || this.props.data[0].value}
              onValueChange={value => this.props.onValueChange(value)}
              style={this.props.outline ? { height: 46 } : styles.pickerStyle}>
              {this.props.data.map(item => (
                <Picker.Item
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </Picker>
            <Image
              style={styles.androidDownIcon}
              source={
                this.props.outline || this.props.outlineIcon
                  ? downIconOutline
                  : downIcon
              }
            />
            <Text
              style={{
                width: '100%',
                height: 60,
                position: 'absolute',
                bottom: 0,
                left: 0,
              }}>
              {' '}
            </Text>
          </View>
        )}
      </View>
    );
  }
}

// Exports
CustomPicker.propTypes = propTypes;
CustomPicker.defaultProps = defaultProps;
export default CustomPicker;
