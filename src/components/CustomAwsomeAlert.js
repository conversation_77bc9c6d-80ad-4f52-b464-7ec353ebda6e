import React, { Component } from 'react';
import {
  Text,
  Animated,
  View,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ActivityIndicator,
  BackHandler,
  Modal,
  Platform,
  StyleSheet,
  Dimensions,
} from 'react-native';

import PropTypes from 'prop-types';
import { colors } from '../styles';

const { height, width } = Dimensions.get('window');

const HwBackHandler = BackHandler;
const HW_BACK_EVENT = 'hardwareBackPress';

const { OS } = Platform;

export default class CustomAwsomeAlert extends Component {
  constructor(props) {
    super(props);
    const { show } = this.props;
    this.springValue = new Animated.Value(0.3);

    this.state = {
      showSelf: false,
    };

    if (show) this.springShow(true);
  }

  componentDidMount() {
    HwBackHandler.addEventListener(HW_BACK_EVENT, this.handleHwBackEvent);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { show } = nextProps;
    const { showSelf } = this.state;

    if (show && !showSelf) this.springShow();
    else if (show === false && showSelf) this.springHide();
  }

  componentWillUnmount() {
    HwBackHandler.removeEventListener(HW_BACK_EVENT, this.handleHwBackEvent);
  }

  springShow = (fromConstructor) => {
    const { useNativeDriver = false } = this.props;

    this.toggleAlert(fromConstructor);
    Animated.spring(this.springValue, {
      toValue: 1,
      bounciness: 10,
      useNativeDriver,
    }).start();
  };

  springHide = () => {
    const { useNativeDriver = false } = this.props;

    if (this.state.showSelf === true) {
      Animated.spring(this.springValue, {
        toValue: 0,
        tension: 10,
        useNativeDriver,
      }).start();

      setTimeout(() => {
        this.toggleAlert();
        this.onDismiss();
      }, 70);
    }
  };

  toggleAlert = (fromConstructor) => {
    if (fromConstructor) this.state = { showSelf: true };
    else this.setState((prevState) => ({ showSelf: !prevState.showSelf }));
  };

  handleHwBackEvent = () => {
    const { closeOnHardwareBackPress } = this.props;
    if (this.state.showSelf && closeOnHardwareBackPress) {
      this.springHide();
      return true;
    }
    if (!closeOnHardwareBackPress && this.state.showSelf) {
      return true;
    }

    return false;
  };

  onTapOutside = () => {
    const { closeOnTouchOutside } = this.props;
    if (closeOnTouchOutside) this.springHide();
  };

  onDismiss = () => {
    const { onDismiss } = this.props;
    if (onDismiss) {
      onDismiss();
    }
  };

  renderButton = (data) => {
    const {
      text,
      backgroundColor,
      buttonStyle,
      buttonTextStyle,
      onPress,
    } = data;

    return (
      <TouchableOpacity onPress={onPress}>
        <View style={[styles.button, { backgroundColor }, buttonStyle]}>
          <Text style={[styles.buttonText, buttonTextStyle]}>{text}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderAlert = () => {
    const animation = { transform: [{ scale: this.springValue }] };

    const { showProgress } = this.props;
    const {
      title,
      message,
      customView = null,
      headerCustomView = null,
    } = this.props;

    const {
      showCancelButton,
      cancelText,
      cancelButtonColor,
      cancelButtonStyle,
      cancelButtonTextStyle,
      onCancelPressed,
    } = this.props;
    const {
      showConfirmButton,
      confirmText,
      confirmButtonColor,
      confirmButtonStyle,
      confirmButtonTextStyle,
      onConfirmPressed,
    } = this.props;

    const {
      alertContainerStyle,
      overlayStyle,
      progressSize,
      progressColor,
      contentContainerStyle,
      contentStyle,
      titleStyle,
      messageStyle,
      actionContainerStyle,
    } = this.props;

    const cancelButtonData = {
      text: cancelText,
      backgroundColor: cancelButtonColor,
      buttonStyle: cancelButtonStyle,
      buttonTextStyle: cancelButtonTextStyle,
      onPress: onCancelPressed,
    };

    const confirmButtonData = {
      text: confirmText,
      backgroundColor: confirmButtonColor,
      buttonStyle: confirmButtonStyle,
      buttonTextStyle: confirmButtonTextStyle,
      onPress: onConfirmPressed,
    };

    return (
      <View style={[styles.container, alertContainerStyle]}>
        <TouchableWithoutFeedback onPress={this.onTapOutside}>
          <View style={[styles.overlay, overlayStyle]} />
        </TouchableWithoutFeedback>
        <Animated.View
          style={[styles.contentContainer, animation, contentContainerStyle]}
        >
          <View style={[styles.content, contentStyle]}>
            {showProgress ? (
              <ActivityIndicator size={progressSize} color={progressColor} />
            ) : null}
            {headerCustomView}
            {title ? (
              <Text style={[styles.title, titleStyle]}>{title}</Text>
            ) : null}
            {message ? (
              <Text style={[styles.message, messageStyle]}>{message}</Text>
            ) : null}
            {customView}
          </View>
          <View style={[styles.action, actionContainerStyle]}>
            {showCancelButton ? this.renderButton(cancelButtonData) : null}
            {showConfirmButton ? this.renderButton(confirmButtonData) : null}
          </View>
        </Animated.View>
      </View>
    );
  };

  render() {
    const { show, showSelf } = this.state;
    const { modalProps = {}, closeOnHardwareBackPress } = this.props;

    const wrapInModal = OS === 'android' || OS === 'ios';

    // eslint-disable-next-line no-nested-ternary
    return showSelf ? (
      wrapInModal ? (
        <Modal
          animationType="none"
          transparent
          visible={show}
          onRequestClose={() => {
            if (showSelf && closeOnHardwareBackPress) {
              this.springHide();
            }
          }}
          {...modalProps}
        >
          {this.renderAlert()}
        </Modal>
      ) : (
        this.renderAlert()
      )
    ) : null;
  }
}

CustomAwsomeAlert.propTypes = {
  show: PropTypes.bool,
  useNativeDriver: PropTypes.bool,
  showProgress: PropTypes.bool,
  title: PropTypes.string,
  message: PropTypes.string,
  closeOnTouchOutside: PropTypes.bool,
  closeOnHardwareBackPress: PropTypes.bool,
  showCancelButton: PropTypes.bool,
  showConfirmButton: PropTypes.bool,
  cancelText: PropTypes.string,
  confirmText: PropTypes.string,
  cancelButtonColor: PropTypes.string,
  confirmButtonColor: PropTypes.string,
  onCancelPressed: PropTypes.func,
  onConfirmPressed: PropTypes.func,
  onDismiss: PropTypes.func,
  cancelButtonStyle: PropTypes.object,
  cancelButtonTextStyle: PropTypes.object,
  confirmButtonStyle: PropTypes.object,
  confirmButtonTextStyle: PropTypes.object,
  alertContainerStyle: PropTypes.object,
  overlayStyle: PropTypes.object,
  contentContainerStyle: PropTypes.object,
  contentStyle: PropTypes.object,
  titleStyle: PropTypes.object,
  messageStyle: PropTypes.object,
  actionContainerStyle: PropTypes.object,
  progressSize: PropTypes.string,
  progressColor: PropTypes.string,
  customView: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.node,
    PropTypes.func,
  ]),
  headerCustomView: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.node,
    PropTypes.func,
  ]),
  modalProps: PropTypes.object,
};

CustomAwsomeAlert.defaultProps = {
  show: false,
  useNativeDriver: false,
  showProgress: false,
  title: '',
  message: '',
  closeOnTouchOutside: true,
  closeOnHardwareBackPress: true,
  showCancelButton: false,
  showConfirmButton: false,
  cancelText: 'Cancel',
  confirmText: 'Confirm',
  progressSize: 'small',
  cancelButtonColor: '#D0D0D0',
  confirmButtonColor: '#AEDEF4',
  progressColor: '#000000',
  customView: null,
  headerCustomView: null,
  modalProps: {},
  onCancelPressed: null,
  onConfirmPressed: null,
  onDismiss: null,
  cancelButtonStyle: {},
  cancelButtonTextStyle: {},
  confirmButtonStyle: {},
  confirmButtonTextStyle: {},
  alertContainerStyle: {},
  overlayStyle: {},
  contentContainerStyle: {},
  contentStyle: {},
  titleStyle: {},
  messageStyle: {},
  actionContainerStyle: {},
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
  },
  overlay: {
    width,
    height,
    position: 'absolute',
    backgroundColor: colors.pickerOverlayBg,
  },
  contentContainer: {
    maxWidth: '80%',
    borderRadius: 5,
    backgroundColor: colors.white,
    padding: 10,
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
  action: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginTop: 5,
  },
  title: {
    paddingVertical: 5,
    paddingHorizontal: 15,
    color: colors.title,
    fontSize: 18,
  },
  message: {
    paddingTop: 5,
    color: colors.subGrey,
    fontSize: 14,
  },
  button: {
    paddingHorizontal: 10,
    paddingVertical: 7,
    margin: 5,
    borderRadius: 5,
  },
  buttonText: {
    color: colors.white,
    fontSize: 13,
  },
});
