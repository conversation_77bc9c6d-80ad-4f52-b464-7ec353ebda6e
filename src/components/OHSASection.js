import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import {
  Dimensions, View, TouchableOpacity, Text, Image,
} from 'react-native';

// Styles
import { colors, shadow } from '../styles';

// Images
const overactiveImage = require('../resources/imgOveractive.png');
const underactiveImage = require('../resources/imgUnderactive.png');

const propTypes = {
  overactiveMuscles: PropTypes.array,
  underactiveMuscles: PropTypes.array,
  onPress: PropTypes.func,
  updatedOn: PropTypes.string,
  loading: PropTypes.bool,
};

const defaultProps = {
  overactiveMuscles: [],
  underactiveMuscles: [],
  onPress: null,
  updatedOn: null,
  loading: false,
};

class OHSASection extends Component {
  renderAssessmentInfo() {
    if (this.props.loading) {
      const loadingBoxes = [];
      for (let i = 0; i < 4; i += 1) {
        loadingBoxes.push(
          <View
            key={`box-${i}`}
            style={{
              backgroundColor: colors.loadingStateGray,
              width: 111 * scale,
              height: 12 * scale,
              marginBottom: 4 * scale,
            }}
          />,
        );
      }
      return (
        <View style={styles.bottomContainer}>
          <View style={styles.muscleGroupContainer}>
            <View
              style={[
                styles.image,
                {
                  backgroundColor: colors.loadingStateGray,
                  borderRadius: 8.5 * scale,
                },
              ]}
            />
            <View>{loadingBoxes}</View>
          </View>
          <View style={styles.muscleGroupContainer}>
            <View
              style={[
                styles.image,
                {
                  backgroundColor: colors.loadingStateGray,
                  borderRadius: 8.5 * scale,
                },
              ]}
            />
            <View>{loadingBoxes}</View>
          </View>
        </View>
      );
    }
    if (!this.props.updatedOn) {
      return (
        <Text style={styles.emptyText}>
          {this.props.isTrainer
            ? 'Tap here to record OHSA'
            : 'No OHSA recorded'}
        </Text>
      );
    }
    return (
      <View style={styles.bottomContainer}>
        <View style={styles.muscleGroupContainer}>
          <Image style={styles.image} source={overactiveImage} />
          <View>
            <Text style={styles.muscleText}>Overactive muscles</Text>
            {this.props.overactiveMuscles.map((muscle) => (
              <Text key={muscle.id} style={styles.muscleText}>
                •
                {' '}
                {muscle.name}
              </Text>
            ))}
          </View>
        </View>
        <View style={styles.muscleGroupContainer}>
          <Image style={styles.image} source={underactiveImage} />
          <View>
            <Text style={styles.muscleText}>Underactive muscles</Text>
            {this.props.underactiveMuscles.map((muscle) => (
              <Text key={muscle.id} style={styles.muscleText}>
                •
                {' '}
                {muscle.name}
              </Text>
            ))}
          </View>
        </View>
      </View>
    );
  }

  render() {
    return (
      <TouchableOpacity
        disabled={!this.props.onPress}
        onPress={this.props.onPress}
        style={styles.container}
      >
        <View style={styles.topContainer}>
          <Text style={styles.title}>Overhead Squat</Text>
          {!this.props.loading && !!this.props.updatedOn && (
            <Text style={styles.date}>
              Updated
              {' '}
              {moment(this.props.updatedOn).format('M/D/YY')}
            </Text>
          )}
        </View>
        {this.renderAssessmentInfo()}
      </TouchableOpacity>
    );
  }
}

const scale = Dimensions.get('window').width / 400;

const styles = {
  container: {
    borderRadius: 3 * scale,
    backgroundColor: colors.white,
    marginHorizontal: 18 * scale,
    marginVertical: 10 * scale,
    marginTop: 20 * scale,
    ...shadow,
  },
  topContainer: {
    backgroundColor: colors.duskBlue,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15 * scale,
    paddingVertical: 8 * scale,
    justifyContent: 'space-between',
    borderTopRightRadius: 3 * scale,
    borderTopLeftRadius: 3 * scale,
  },
  title: {
    fontFamily: 'Avenir-Roman',
    fontSize: 18 * scale,
    color: colors.white,
  },
  date: {
    fontFamily: 'Avenir',
    fontSize: 9 * scale,
    fontWeight: '900',
    color: colors.white,
  },
  bottomContainer: {
    paddingHorizontal: 13 * scale,
    paddingVertical: 19 * scale,
    flexDirection: 'row',
  },
  muscleGroupContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  muscleText: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10 * scale,
    color: colors.subGrey,
  },
  image: {
    width: 17 * scale,
    height: 17 * scale,
    marginRight: 10 * scale,
  },
  emptyText: {
    fontFamily: 'Avenir-Heavy',
    fontSize: 16 * scale,
    color: colors.subGrey,
    textAlign: 'center',
    margin: 16 * scale,
  },
};

OHSASection.propTypes = propTypes;
OHSASection.defaultProps = defaultProps;
export default OHSASection;
