import React from 'react';
import {
  Image,
  Text,
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import DashboardProfile from './DashboardProfile';
import nasm from '../dataManager/apiConfig';
import { colors } from '../styles';
import { curvedScale } from '../util/responsive';
import { setAppointmentsByDateAction } from '../actions/artichoke/Appointments.actions';
import { deselectClient, selectClient } from '../actions';
import { removeAllSpecialCharacters } from '../util/validate';
import { getAvatarUrl } from '../util/utils';

const plusIcon = require('../assets/btnAddGray.png');
const editIcon = require('../assets/nounEdit1072351.png');

const propTypes = {
  groupInfo: PropTypes.object,
  setValues: PropTypes.func,
  onPressSelectMembers: PropTypes.func,
  navigation: PropTypes.any.isRequired,
};
const defaultProps = {
  groupInfo: {},
  setValues: null,
  onPressSelectMembers: null,
};

const GroupDetails = ({
  groupInfo,
  setValues,
  navigation,
  onPressSelectMembers,
}) => {
  const stateSelector = (state) => state;
  const dispatch = useDispatch();
  const { currentUser } = useSelector(stateSelector);

  const onPressUser = async (client) => {
    nasm.api
      .getUserById(client.user.id)
      .then((user) => {
        if (user) {
          dispatch(setAppointmentsByDateAction([]));
          dispatch(deselectClient());
          dispatch(selectClient(user));
          navigation.navigate('tabClient', {
            role: currentUser.role,
          });
        }
      })
      .catch((error) => error);
  };

  const renderUserName = (item) => {
    if (item?.user?.full_name) return item?.user?.full_name;
    if (item?.user?.first_name && item?.user?.last_name) return `${item?.user?.first_name} ${item?.user?.last_name}`;
    if (item?.user?.first_name) return `${item?.user?.first_name}`;
    if (item?.user?.last_name) return `${item?.user?.last_name}`;
    return '';
  };

  const renderRow = (item, index) => (
    <DashboardProfile
      key={index}
      profileImage={
        item.user.avatar_url ? item.user.avatar_url : getAvatarUrl(item?.user)
      }
      name={renderUserName(item)}
      onPress={() => onPressUser(item)}
    />
  );
  const RenderGroupName = () => (
    <View style={styles.grpInfoContainer}>
      <Text style={styles.grpNameLabel}>Group Name</Text>
      <TextInput
        style={styles.grpNameInput}
        placeholder="Enter Group Name"
        value={groupInfo?.groupName ?? ''}
        onChangeText={(updateText) => setValues('groupName', removeAllSpecialCharacters(updateText))}
      />
    </View>
  );

  const renderGroupMembersCount = () => (
    <TouchableOpacity
      style={styles.grpMembersContainer}
      onPress={onPressSelectMembers}
    >
      <Text style={styles.grpMembersLabel}>
        {`${!groupInfo?.members?.length ? 'Add ' : ''}Group Members`}
        {groupInfo?.members?.length ? (
          <Text style={styles.grpMembersCount}>
            {' '}
            {groupInfo?.members?.length}
          </Text>
        ) : null}
      </Text>
      <Image source={groupInfo?.members?.length ? editIcon : plusIcon} />
    </TouchableOpacity>
  );

  const renderDivider = () => <View style={styles.divider} />;

  return (
    <ScrollView contentContainerStyle={{ paddingBottom: 20 }}>
      {RenderGroupName()}
      {renderGroupMembersCount()}
      {renderDivider()}
      {groupInfo?.members?.map((item, index) => renderRow(item, index))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  grpInfoContainer: {
    marginTop: curvedScale(20),
    marginHorizontal: curvedScale(20),
  },
  grpNameLabel: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
  },
  grpNameInput: {
    color: colors.eerieBlack,
    fontSize: curvedScale(17),
    fontFamily: 'Avenir-Medium',
    paddingBottom: curvedScale(10),
    marginTop: curvedScale(20),
    borderBottomWidth: 1,
    borderColor: colors.bordergrey,
  },
  grpMembersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    margin: curvedScale(20),
  },
  grpMembersLabel: {
    color: colors.black,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
  },
  grpMembersCount: {
    color: colors.fillDarkGrey,
    fontSize: curvedScale(14),
    fontFamily: 'Avenir-Medium',
  },
  divider: {
    height: 1,
    backgroundColor: colors.bordergrey,
    marginHorizontal: curvedScale(20),
  },
});

GroupDetails.propTypes = propTypes;
GroupDetails.defaultProps = defaultProps;

export default GroupDetails;
