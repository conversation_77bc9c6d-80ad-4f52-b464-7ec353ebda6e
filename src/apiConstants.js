import { NativeModules } from 'react-native';

const buildEnv = NativeModules.ENVConfig.buildEnvironment;

let artichokeEnvUrl = '';
let stripeApiKey = '';
let stripeReturnUri = '';
let stripePublishableKey = '';
let stripeAndroidPayMode = '';
let stripeCallbackUrl = '';
let mixpanelToken = '';
let agoraIoApiKey = '';
let agoraAppCertificate = '';
switch (buildEnv) {
  case 'debug':
    artichokeEnvUrl = 'https://trainerpro-api-dev.nasm.org/';
    stripeApiKey = 'ca_H6tl1jgCeltwJfbcCP1pb2S57pqhAAS4';
    stripeReturnUri = 'https://trainerpro-api-dev.nasm.org/api/accounts/stripeSetupRedirect';
    stripePublishableKey = 'pk_test_FSnnRFkn6CJULVVOQxC314YT00RDq3YGub';
    stripeAndroidPayMode = 'test';
    stripeCallbackUrl = 'nasm://test_stripecallback';
    mixpanelToken = '********************************';
    agoraIoApiKey = '********************************';
    agoraAppCertificate = '********************************';
    break;
  case 'staging':
    artichokeEnvUrl = 'https://trainerpro-api-stg.nasm.org/';
    stripeApiKey = 'ca_RW366c43w4ZcE69mNxpGziSPD5zehBAZ';
    stripeReturnUri = 'https://trainerpro-api-stg.nasm.org/api/accounts/stripeSetupRedirect';
    stripePublishableKey = 'pk_test_51Qd0zfFKcm6v57xBULsGxq74fzqfBOyvWV4P77Wrvn1xrjH5mxANxmpXs2Pw4A7klogV0AvK7ImEVi8agVB7KH7r00cJR2V2Ft';
    stripeAndroidPayMode = 'test';
    stripeCallbackUrl = 'nasm://test_stripecallback';
    mixpanelToken = '********************************';
    agoraIoApiKey = '********************************';
    agoraAppCertificate = '********************************';
    break;
  case 'release':
    artichokeEnvUrl = 'https://trainerpro-api.nasm.org/';
    stripeApiKey = 'ca_H6tlKBEjrBHvg2o1OFCMHGJqJIo48hAL';
    stripeReturnUri = 'https://trainerpro-api.nasm.org/api/accounts/stripeSetupRedirect';
    stripePublishableKey = 'pk_live_B6OmGb5icxWjkpsCj4EVDcx100IavBGjxt';
    stripeAndroidPayMode = 'production';
    stripeCallbackUrl = 'nasm://prod_stripecallback';
    mixpanelToken = '********************************';
    agoraIoApiKey = '********************************';
    agoraAppCertificate = '********************************';
    break;
  default:
    artichokeEnvUrl = 'https://trainerpro-api-dev.nasm.org/';
    stripeApiKey = 'ca_H6tl1jgCeltwJfbcCP1pb2S57pqhAAS4';
    stripeReturnUri = 'https://trainerpro-api-dev.nasm.org/api/accounts/stripeSetupRedirect';
    stripePublishableKey = 'pk_test_FSnnRFkn6CJULVVOQxC314YT00RDq3YGub';
    stripeAndroidPayMode = 'test';
    stripeCallbackUrl = 'nasm://test_stripecallback';
    mixpanelToken = '********************************';
    agoraIoApiKey = '********************************';
    agoraAppCertificate = '********************************';
    break;
}

export const BASE_URL = artichokeEnvUrl;
export const BASE_OAUTH_URL = artichokeEnvUrl;

export const AUTH_USER = 'apiUser';
export const AUTH_PASSWORD = 'apid3vich0kepass';
export const GOOGLE_API_KEY = 'AIzaSyDqL8kLKM8oMx9pXMKd9feg8CocqcK7G1o';

export const STRIPE_KEY = stripeApiKey;
export const STRIPE_RETURN_URI = stripeReturnUri;
export const STRIPE_PUBLISHABLE_KEY = stripePublishableKey;
export const STRIPE_ANDROID_PAY_MODE = stripeAndroidPayMode;
export const STRIPE_CALLBACK_URLS = stripeCallbackUrl;
export const MIXPANEL_TOKEN = mixpanelToken;
export const AGORA_IO_API_KEY = agoraIoApiKey;
export const AGORA_IO_APP_CERTIFICATE = agoraAppCertificate;
