import {
  CLEAR_STATE_LOGOUT,
  SET_APPLICATION_LOADING_STATE,
} from '../../actions/artichoke/Application.actions';

const initialState = {
  applicationLoading: true,
};

const ApplicationReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_APPLICATION_LOADING_STATE:
      return {
        ...state,
        applicationLoading: action.payload,
      };
    case CLEAR_STATE_LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default ApplicationReducer;
