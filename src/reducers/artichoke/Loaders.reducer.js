import { SET_LOADER_ACTION } from '../../actions/artichoke/Loaders.actions';
import { CLEAR_STATE_LOGOUT } from '../../actions/artichoke/Application.actions';

const initialState = {
  serviceListLoading: false,
  packageListLoading: false,
  appointmentsListLoading: false,
  locationsLoading: false,
  remoteLocationsLoading: false,
  clientLocationsLoading: false,
  getServiceLoading: false,
  getPackageLoading: false,
  productsListLoading: false,
  classesListLoading: false,
  clientBookingsListLoading: false,
  saveServiceLoading: false,
  savePackageLoading: false,
  scheduleClassLoading: false,
  saveSessionLoading: false,
  checkInCreditCardLoading: false,
  checkInCashOrCheckLoading: false,
  checkInBalances: false,
  showAppointmentLoading: false,
  showSlotBlockerLoading: false,
  showActiveClientsLoading: false,
  showBuyProductsLoading: false,
  confirmPaymentLoading: false,
  cancelingSubscriptionLoading: false,
  getClientBookingsLoading: false,
  validateCardLoader: false,
  bookServiceLoader: false,
  saveBalanceAdjustmentLoader: false,
  discountLoader: false,
  subscriptionsLoader: false,
  showCreditCardListLoading: false,
  saveSlotBlockerLoading: false,
  deleteSlotBlockerLoading: false,
  updateSlotBlockerLoading: false,
  bookClientClassLoader: false,
  updateAppointmentLoading: false,
  archivedPackagesListLoading: false,
  archivedServicesListLoading: false,
  saveServiceLocationsLoading: false,
  userDataLoading: false,
  accountDataLoading: false,
  accountDataUpdateLoading: false,
  timezonesLoading: false,
  ccSaveRemoveLoading: false,
  bookPackageLoader: false,
  refundActionLoading: false,
  joinVideoLoading: false,
  joiningAppointmentId: null,
  cancelAppointmentLoading: false,
};

const LoadersReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_LOADER_ACTION:
      return {
        ...state,
        [`${action.payload.key}`]: action.payload.value,
      };
    case CLEAR_STATE_LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default LoadersReducer;
