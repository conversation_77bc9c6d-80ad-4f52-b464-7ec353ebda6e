import { CLEAR_STATE_LOGOUT } from '../../actions/artichoke/Application.actions';
import {
  SET_PERIOD_HISTORY_ACTION,
  SET_SALES_TAX_ACTION,
  SET_TRANSACTION_HISTORY_ACTION,
} from '../../actions/artichoke/Sales.actions';
import { SALES_PERIODS } from '../../constants';

const initialState = {
  salesTax: null,
  transactions: [],
  period: SALES_PERIODS[0].value,
};

const SalesReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_SALES_TAX_ACTION:
      return {
        ...state,
        salesTax: action.payload,
      };
    case SET_TRANSACTION_HISTORY_ACTION:
      return {
        ...state,
        transactions: [...action.payload],
      };
    case SET_PERIOD_HISTORY_ACTION:
      return {
        ...state,
        period: action.payload,
      };
    case CLEAR_STATE_LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default SalesReducer;
