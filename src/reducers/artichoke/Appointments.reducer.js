import moment from 'moment';
import {
  GET_SERVICE_APPOINTMENTS_SUCCEED_ACTION,
  SET_FILTERED_APPOINTMENTS,
  SET_FILTERED_INVITEE,
  CLEAR_SERVICE_APPOINTMENTS,
  SET_APPOINTMENTS_BY_DATE,
  SET_CLIENT_BOOKINGS_ACTION,
  SET_FILTERED_BOOKINGS_BY_CLIENT,
  SET_FUTURE_APPOINTMENTS_BY_DATE,
  SET_SCHEDULES,
  SET_NEW_APPOINTMENT_VALUES_ACTION,
  SET_BLOCKING_APPOINTMENTS_BY_DATE,
  SET_SELECTED_APPOINTMENT_ACTION,
  <PERSON><PERSON><PERSON>_NEW_APPOINTMENT,
  <PERSON><PERSON><PERSON>_SELECTED_APPOINTMENT,
  SET_NEW_SLOTBLOCKER_VALUES_ACTION,
  SET_SELECTED_SLOTBLOCKER_ACTION,
  C<PERSON>AR_SELECTED_SLOTBLOCKER,
  SET_APPOINTMENTS_AVAILABILITY,
  C<PERSON>AR_APPOINTMENTS_AVAILABILITY,
  SET_NEW_CLASS_VALUES_ACTION,
  <PERSON><PERSON><PERSON>_SELECTED_CLASS,
  SET_SELECTED_CLASS_ACTION,
  CLEAR_NEW_CLASS,
  CLEAR_SUCCESS_ACTION,
  CLEAR_ERROR_ACTION,
  SET_SUCCESS_ACTION,
  SET_ERROR_ACTION,
  SET_PURCHASING_ACTION,
} from '../../actions/artichoke/Appointments.actions';
import { CLEAR_STATE_LOGOUT } from '../../actions/artichoke/Application.actions';

const dateFormat = 'MM/DD/YYYY hh:mm a';
let payload;
let payloadEdit;

const initialState = {
  appointmentsList: [],
  filteredAppointments: {},
  schedules: {},
  filteredInvitee: [],
  appointmentsListByDate: {},
  bookingsListByClient: {},
  filteredBookingsByClient: {},
  futureAppointmentsListByDate: {},
  blockingAppointments: [],
  newAppointmentValues: {
    appointmentLocation: null,
    sendNotifications: true,
    repeatIntervalType: null,
    repeatUntilType: null,
    count: null,
    untilDay: null,
    clients: [],
    service: null,
    date: null,
    time: null,
    remotely: false,
    clientAddressEnable: false,
    inapp: false,
    isScheduled: false,
    isEdit: false,
  },
  newClassValues: {
    bookOnlineEnabled: false,
    bookSeriesEnabled: false,
    sendNotifications: true,
    hideRule: '',
    maxParticipants: 1,
    productId: null,
    repeatCount: 1,
    repeatFrequency: 'week',
    repeatUntil: null,
    scheduleDays: [
      {
        day: moment().day().toString(),
        startTime: moment().startOf('hour').format('HH:mm').toString(),
      },
    ],
    selfBookingAddressId: null,
    startDate: null,
    userId: null,
  },
  selectedAppointment: null,
  selectedSlotBlocker: null,
  editAppointmentValues: {
    appointmentLocation: null,
    sendNotifications: true,
    repeatIntervalType: null,
    repeatUntilType: null,
    count: null,
    untilDay: null,
    clients: [],
    service: null,
    date: null,
    time: null,
  },
  newSlotBlocker: {
    description: '',
    date: null,
    start: null,
    end: null,
    repeatIntervalType: null,
    repeatUntilType: 'Day',
    count: '0',
    untilDay: null,
  },
  availability: [],
  selectedClass: null,
  error: null,
  success: null,
  purchasing: false,
};

const AppointmentsReducer = (state = initialState, action) => {
  switch (action.type) {
    case GET_SERVICE_APPOINTMENTS_SUCCEED_ACTION:
      return {
        ...state,
        appointmentsList: action.payload,
        filteredAppointments: action.payload,
      };
    case SET_FILTERED_APPOINTMENTS:
      return {
        ...state,
        filteredAppointments: action.payload,
      };
    case SET_FILTERED_INVITEE:
      return {
        ...state,
        filteredInvitee: action.payload,
      };
    case SET_APPOINTMENTS_BY_DATE:
      return {
        ...state,
        appointmentsListByDate: { ...action.payload },
        filteredAppointments: { ...action.payload },
      };
    case SET_CLIENT_BOOKINGS_ACTION:
      return {
        ...state,
        bookingsListByClient: { ...action.payload },
        filteredBookingsByClient: { ...action.payload },
      };
    case SET_FILTERED_BOOKINGS_BY_CLIENT:
      return {
        ...state,
        filteredBookingsByClient: { ...action.payload },
      };
    case SET_FUTURE_APPOINTMENTS_BY_DATE:
      return {
        ...state,
        futureAppointmentsListByDate: { ...action.payload },
      };
    case SET_SCHEDULES:
      return {
        ...state,
        schedules: { ...action.payload },
      };
    case CLEAR_SERVICE_APPOINTMENTS:
      return {
        ...state,
        appointmentsList: [],
        filteredAppointments: [],
      };
    case SET_NEW_APPOINTMENT_VALUES_ACTION:
      return {
        ...state,
        newAppointmentValues: {
          ...state.newAppointmentValues,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case SET_BLOCKING_APPOINTMENTS_BY_DATE:
      return {
        ...state,
        blockingAppointments: action.payload,
      };
    case SET_SELECTED_APPOINTMENT_ACTION:
      return {
        ...state,
        selectedAppointment: action.payload,
        filteredInvitee: action.payload.Invitee,
        newAppointmentValues: {
          appointmentLocation: action.payload.onsiteAddress,
          sendNotifications: !action.payload.disableNotifications,
          repeatIntervalType: action.payload.Repeat
            ? action.payload.Repeat.RepeatInterval.RepeatIntervalType
            : null,
          repeatUntilType: action.payload.Repeat
            ? action.payload.Repeat.RepeatUntil.RepeatUntilType
            : null,
          count: action.payload.Repeat
            ? action.payload.Repeat.RepeatUntil.count
            : null,
          untilDay: action.payload.Repeat
            ? action.payload.Repeat.RepeatUntil.untilDay
            : null,
          clients: action.payload.Invitee,
          service: {
            name: action.payload.ProductDuration.name,
            ProductDuration: [action.payload.ProductDuration],
            serviceImage: action.payload.serviceImage,
            selfBookingAddresses: action.payload.selfBookingAddresses,
          },
          date: moment(action.payload.start, dateFormat).format('L'),
          time: moment(action.payload.start, dateFormat).format('LT'),
          remotely: action.payload.remotely,
          inapp: action.payload.inapp,
          clientAddressEnable: action.payload.clientAddressEnable,
          isScheduled: action.payload.isScheduled,
        },
      };
    case CLEAR_NEW_APPOINTMENT:
      payload = { ...initialState.newAppointmentValues };
      payload.clients = [];
      return {
        ...state,
        newAppointmentValues: payload,
        availability: [],
      };
    case CLEAR_SELECTED_APPOINTMENT:
      payloadEdit = { ...initialState.editAppointmentValues };
      payloadEdit.clients = [];
      return {
        ...state,
        selectedAppointment: null,
        selectedSlotBlocker: null,
        newAppointmentValues: payloadEdit,
        availability: [],
      };
    case SET_NEW_SLOTBLOCKER_VALUES_ACTION:
      return {
        ...state,
        newSlotBlocker: {
          ...state.newSlotBlocker,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case SET_SELECTED_SLOTBLOCKER_ACTION:
      return {
        ...state,
        selectedSlotBlocker: action.payload,
        newSlotBlocker: {
          repeatIntervalType: action.payload.Repeat
            ? action.payload.Repeat.RepeatInterval.RepeatIntervalType
            : null,
          repeatUntilType: action.payload.Repeat
            ? action.payload.Repeat.RepeatUntil.RepeatUntilType
            : null,
          count: action.payload.Repeat
            ? action.payload.Repeat.RepeatUntil.count
            : null,
          untilDay: action.payload.Repeat
            ? action.payload.Repeat.RepeatUntil.untilDay
            : null,
          date: moment(action.payload.start, 'MM/DD/YYYY HH:mm A').format('L'),
          start: moment(action.payload.start, 'MM/DD/YYYY HH:mm A').format(
            'LT',
          ),
          end: moment(action.payload.end, 'MM/DD/YYYY HH:mm A').format('LT'),
          description: action.payload.description
            ? action.payload.description
            : '',
        },
      };
    case CLEAR_SELECTED_SLOTBLOCKER:
      return {
        ...state,
        selectedSlotBlocker: null,
        newSlotBlocker: {
          description: '',
          date: null,
          start: null,
          end: null,
          repeatIntervalType: null,
          repeatUntilType: null,
          count: null,
          untilDay: null,
        },
      };
    case SET_APPOINTMENTS_AVAILABILITY:
      return {
        ...state,
        availability: [...action.payload],
      };

    case CLEAR_APPOINTMENTS_AVAILABILITY:
      return {
        ...state,
        availability: [],
      };
    case SET_NEW_CLASS_VALUES_ACTION:
      return {
        ...state,
        newClassValues: {
          ...state.newClassValues,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case CLEAR_SELECTED_CLASS:
      return {
        ...state,
        selectedClass: null,
        newClassValues: initialState.newClassValues,
      };
    case CLEAR_NEW_CLASS:
      return {
        ...state,
        newClassValues: initialState.newClassValues,
      };
    case SET_SELECTED_CLASS_ACTION:
      return {
        ...state,
        selectedClass: action.payload,
        newClassValues: {
          ...action.payload,
          sendNotifications: initialState.newClassValues.sendNotifications,
        },
      };
    case CLEAR_SUCCESS_ACTION:
      return {
        ...state,
        success: initialState.success,
      };
    case CLEAR_ERROR_ACTION:
      return {
        ...state,
        error: initialState.error,
      };
    case SET_SUCCESS_ACTION:
      return {
        ...state,
        success: action.payload,
      };
    case SET_PURCHASING_ACTION:
      return {
        ...state,
        purchasing: action.payload,
      };
    case SET_ERROR_ACTION:
      return {
        ...state,
        error: action.payload,
      };
    case CLEAR_STATE_LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default AppointmentsReducer;
