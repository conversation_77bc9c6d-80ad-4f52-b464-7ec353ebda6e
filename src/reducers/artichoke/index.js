import { combineReducers } from 'redux';
import application from './Application.reducer';
import services from './Services.reducer';
import login from './Login.reducer';
import loadingComponents from './Loaders.reducer';
import locations from './Locations.reducer';
import appointments from './Appointments.reducer';
import user from './User.reducer';
import clients from './Clients.reducer';
import sales from './Sales.reducer';

const rootReducer = {
  application,
  appointments,
  loadingComponents,
  locations,
  login,
  services,
  user,
  clients,
  sales,
};

export default rootReducer;
