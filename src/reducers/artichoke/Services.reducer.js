import {
  GET_SERVICES_SUCCEED_ACTION,
  GET_ARCHIVED_SERVICES_SUCCEED_ACTION,
  SET_SELECTED_SERVICE,
  SET_SELECTED_PACKAGE,
  SET_FILTERED_SERVICES,
  CLEAR_SELECTED_SERVICE,
  SET_UPLOADED_IMAGE,
  SET_ACTIVE_PACKAGES_LIST,
  SET_INACTIVE_PACKAGES_LIST,
  SET_SELECTED_OFFERING_SERVICE,
  CLEAR_NEW_PACKAGE_ACTION,
  SET_PACKAGE_SERVICES,
  SET_PACKAGE_SERVICES_FOR_EDIT,
  SET_TAX_RATE,
  SET_PAYMENT_INTERVAL,
  SET_EXPIRE_TYPE,
  SET_EXPIRE_DAYS_CUSTOM,
  SET_PACKAGE_NAME,
  SET_PACKAGE_PRICE,
  SET_PAYMENT_COUNT,
  SET_SELL_PACKAGE_ONLINE_ENABLE,
  SET_PACKAGE_TAX,
  SET_PACKAGE_TAX_RATE,
  SET_PACKAGE_DESCRIPTION,
  SET_SERVICES_TOTAL_VALUE,
  SET_SELECTED_PACKAGE_FOR_EDIT_VALUES_ACTION,
  CLEAR_SELECTED_PACKAGE,
} from '../../actions/artichoke/Services.actions';
import { CLEAR_STATE_LOGOUT } from '../../actions/artichoke/Application.actions';

const initialState = {
  servicesList: [],
  archivedServicesList: [],
  filteredServices: [],
  selectedService: {
    name: '',
    duration: '',
    price: '0.00',
    tax: '0.00',
    maxParticipants: '1',
    description: '',
    bookOnlineEnabled: true,
    serviceImage: null,
  },
  serviceImage: null,
  packagesList: [],
  archivedPackageList: [],
  newPackage: {
    selectedServices: [],
    taxRate: 'NOTAX',
    tax: '0.00',
    taxPercentage: '0.00',
    paymentInterval: 'ONETIME',
    expireType: 'NEVER',
    expireDaysCustom: null,
    packageName: '',
    price: '0',
    repeatCount: 1,
    sellOnlineEnabled: false,
    packageDescription: null,
    servicesTotalValue: 0.0,
  },
  selectedOfferingServices: [],
  selectedPackage: null,
};

const ServicesReducer = (state = initialState, action) => {
  switch (action.type) {
    case GET_SERVICES_SUCCEED_ACTION:
      return {
        ...state,
        servicesList: action.payload,
        filteredServices: action.payload,
      };
    case SET_SELECTED_OFFERING_SERVICE:
      return {
        ...state,
        selectedOfferingServices: action.payload,
      };
    case CLEAR_NEW_PACKAGE_ACTION:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          selectedServices: [],
          taxRate: 'NOTAX',
          tax: '0.00',
          taxPercentage: '0.00',
          paymentInterval: 'ONETIME',
          expireType: 'NEVER',
          expireDaysCustom: null,
          packageName: '',
          price: '0',
          repeatCount: 1,
          sellOnlineEnabled: false,
          packageDescription: null,
          servicesTotalValue: 0.0,
        },
        selectedOfferingServices: [],
      };
    case SET_PACKAGE_SERVICES:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          selectedServices: action.payload,
        },
      };
    case SET_FILTERED_SERVICES:
      return {
        ...state,
        filteredServices: action.payload,
      };
    case GET_ARCHIVED_SERVICES_SUCCEED_ACTION:
      return {
        ...state,
        archivedServicesList: action.payload,
      };
    case SET_INACTIVE_PACKAGES_LIST:
      return {
        ...state,
        archivedPackageList: action.payload,
      };
    case SET_SELECTED_SERVICE:
      return {
        ...state,
        selectedService: action.payload,
        serviceImage: action.payload.serviceImage,
      };
    case SET_SELECTED_PACKAGE:
      return {
        ...state,
        selectedPackage: action.payload,
      };

    case SET_UPLOADED_IMAGE:
      return {
        ...state,
        serviceImage: action.payload,
      };
    case CLEAR_SELECTED_SERVICE:
      return {
        ...state,
        selectedService: initialState.selectedService,
        serviceImage: null,
        filteredServices: state.servicesList,
      };
    case CLEAR_SELECTED_PACKAGE:
      return {
        ...state,
        selectedPackage: null,
      };
    case SET_ACTIVE_PACKAGES_LIST:
      return {
        ...state,
        packagesList: action.payload,
      };
    case SET_TAX_RATE:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          taxRate: action.payload,
        },
      };
    case SET_PAYMENT_INTERVAL:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          paymentInterval: action.payload,
        },
      };
    case SET_EXPIRE_TYPE:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          expireType: action.payload,
        },
      };
    case SET_EXPIRE_DAYS_CUSTOM:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          expireDaysCustom: action.payload,
        },
      };
    case SET_PACKAGE_NAME:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          packageName: action.payload,
        },
      };
    case SET_PACKAGE_PRICE:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          price: action.payload,
        },
      };
    case SET_PAYMENT_COUNT:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          repeatCount: action.payload,
        },
      };
    case SET_SELL_PACKAGE_ONLINE_ENABLE:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          sellOnlineEnabled: action.payload,
        },
      };
    case SET_PACKAGE_TAX:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          tax: action.payload,
        },
      };
    case SET_PACKAGE_TAX_RATE:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          taxPercentage: action.payload,
        },
      };
    case SET_PACKAGE_DESCRIPTION:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          packageDescription: action.payload,
        },
      };
    case SET_SERVICES_TOTAL_VALUE:
      return {
        ...state,
        newPackage: {
          ...state.newPackage,
          servicesTotalValue: action.payload,
        },
      };
    case SET_SELECTED_PACKAGE_FOR_EDIT_VALUES_ACTION:
      return {
        ...state,
        selectedPackage: {
          ...state.selectedPackage,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case SET_PACKAGE_SERVICES_FOR_EDIT:
      return {
        ...state,
        selectedPackage: {
          ...state.selectedPackage,
          packageProducts: action.payload,
        },
      };
    case CLEAR_STATE_LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default ServicesReducer;
