import {
  SET_ACTIVE_CLIENTS,
  SET_FILTERED_CLIENTS_LIST,
  SET_DISCOUNTS,
  SET_PURCHASE_ACTION,
  CLEAR_PURCHASE,
  SET_SELECTED_DISCOUNT_FOR_EDIT_VALUES_ACTION,
  C<PERSON>AR_SELECTED_DISCOUNT_FOR_EDIT_VALUES_ACTION,
  SET_CLIENT_BALANCE,
  SET_SELECTED_CLIENT,
  SET_CLIENT_CREDIT_CARD,
  SET_CLIENT_TRANSACTIONS,
  SET_CLIENT_SUBSCRIPTIONS,
  SET_SELECTED_TRANSACTION,
  SET_FILTERED_CLIENT_TRANSACTIONS,
  SET_SELECTED_CLIENT_BALANCE,
  SET_SAVING_CLIENT_CARD_ACTION,
  CLEAR_CLIENT_CREDIT_CARD,
} from '../../actions/artichoke/Clients.actions';
import { <PERSON><PERSON>AR_STATE_LOGOUT } from '../../actions/artichoke/Application.actions';

const initialState = {
  clientsList: [],
  filteredClientsList: [],
  discountsList: [],
  clientsPurchase: {
    amountDue: null,
    amountPaid: null,
    cashBalanceToApply: 0,
    checkDate: null,
    checkNumber: '',
    clientId: null,
    clientPurchaseProductDurations: {},
    packageId: null,
    discount: null,
    previousGiftCardId: null,
    purchaseType: null,
    tip: 0,
  },
  selectedDiscount: {
    name: null,
    amount: null,
    type: null,
    accountId: null,
    id: null,
  },
  clientBalance: [],
  clientTransactions: [],
  clientSubscriptions: [],
  filteredClientTransactions: [],
  selectedClientTransaction: null,
  selectedClient: null,
  selectedClientCreditCard: null,
  selectedClientBalance: null,
  savingClientCard: false,
};

const ClientsReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_ACTIVE_CLIENTS:
      return {
        ...state,
        clientsList: action.payload,
        filteredClientsList: action.payload,
      };
    case SET_FILTERED_CLIENTS_LIST:
      return {
        ...state,
        filteredClientsList: action.payload,
      };
    case SET_DISCOUNTS:
      return {
        ...state,
        discountsList: [
          { id: 0, amount: 0, name: 'No discount' },
          ...action.payload,
        ],
      };
    case SET_PURCHASE_ACTION:
      return {
        ...state,
        clientsPurchase: {
          ...state.clientsPurchase,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case CLEAR_PURCHASE:
      const payload = { ...initialState.clientsPurchase };
      return {
        ...state,
        clientsPurchase: payload,
      };
    case SET_SELECTED_DISCOUNT_FOR_EDIT_VALUES_ACTION:
      return {
        ...state,
        selectedDiscount: {
          ...state.selectedDiscount,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case CLEAR_SELECTED_DISCOUNT_FOR_EDIT_VALUES_ACTION:
      return {
        ...state,
        selectedDiscount: initialState.selectedDiscount,
      };
    case SET_CLIENT_BALANCE:
      return {
        ...state,
        clientBalance: action.payload,
      };
    case SET_CLIENT_TRANSACTIONS:
      return {
        ...state,
        clientTransactions: action.payload,
        filteredClientTransactions: action.payload,
      };
    case SET_CLIENT_SUBSCRIPTIONS:
      return {
        ...state,
        clientSubscriptions: action.payload,
      };
    case SET_FILTERED_CLIENT_TRANSACTIONS:
      return {
        ...state,
        filteredClientTransactions: action.payload,
      };
    case SET_SELECTED_CLIENT:
      return {
        ...state,
        selectedClient: action.payload,
      };
    case SET_CLIENT_CREDIT_CARD:
      return {
        ...state,
        selectedClientCreditCard: action.payload,
      };
    case CLEAR_CLIENT_CREDIT_CARD:
      return {
        ...state,
        selectedClientCreditCard: null,
      };
    case SET_SELECTED_TRANSACTION:
      return {
        ...state,
        selectedClientTransaction: action.payload,
      };
    case SET_SELECTED_CLIENT_BALANCE:
      return {
        ...state,
        selectedClientBalance: action.payload,
      };
    case SET_SAVING_CLIENT_CARD_ACTION:
      return {
        ...state,
        savingClientCard: action.payload,
      };
    case CLEAR_STATE_LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default ClientsReducer;
