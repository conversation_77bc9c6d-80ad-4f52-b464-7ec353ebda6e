import {
  SET_USER_DATA,
  SET_ACCOUNT_SETTINGS,
  SET_TIMEZONES,
  SET_UPDATED_ACCOUNT_SETTINGS,
} from '../../actions/artichoke/User.actions';
import { CLEAR_STATE_LOGOUT } from '../../actions/artichoke/Application.actions';

const initialState = {
  details: true,
  accountSettings: null,
  timezones: null,
  newAccountSettings: null,
};

const UserReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_USER_DATA:
      return {
        ...state,
        details: action.payload,
      };
    case SET_ACCOUNT_SETTINGS:
      return {
        ...state,
        accountSettings: action.payload,
      };
    case SET_UPDATED_ACCOUNT_SETTINGS:
      return {
        ...state,
        accountSettings: {
          ...state.accountSettings,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case SET_TIMEZONES:
      return {
        ...state,
        timezones: action.payload,
      };
    case CLEAR_STATE_LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default UserReducer;
