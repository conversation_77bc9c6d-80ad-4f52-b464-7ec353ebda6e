import {
  GET_A<PERSON><PERSON>ABLE_LOCATIONS_SUCCEED_ACTION,
  SET_SELECTED_LOCATION,
  SET_FILTERED_LOCATIONS,
  SET_ACTIVE_SERVICE_CREATION,
  CLEAR_SELECTED_LOCATION,
  SET_SELECTED_EXTRA_LOCATION,
  CLEAR_SELECTED_EXTRA_LOCATION,
  SET_SELF_BOOKING_ACTION,
  SET_SELECTED_LOCATION_FOR_EDIT_ACTION,
  CLEAR_SELECTED_LOCATION_FOR_EDIT,
  SET_CLIENT_LOCATION_VALUES_ACTION,
  SET_SELECTED_LOCATION_FOR_EDIT_VALUES_ACTION,
  SET_NEW_LOCATION_VALUES_ACTION,
  C<PERSON>AR_NEW_LOCATION_VALUES,
  SET_SELF_BOOKING_USERS_ACTION,
  SET_REMOTE_LOCATION_VALUES_ACTION,
  SET_IN_APP_VIDEO_LOCATION_VALUES_ACTION,
} from '../../actions/artichoke/Locations.actions';
import { CLEAR_STATE_LOGOUT } from '../../actions/artichoke/Application.actions';

const initialState = {
  locationsList: [],
  filteredLocations: [],
  selectedLocations: [],
  selectedExtraLocations: [],
  selfBookings: null,
  selfBookingsUsers: null,
  clientLocation: {
    workHours: '{ "offsite": {}, "remote":{}, "inapp":{}}',
    offsiteStartTimes: '',
    travelTime: 0,
    travelRadius: 0,
    travelOrigin: '',
  },
  remoteLocation: {
    workHours: '{ "offsite": {}, "remote":{}, "inapp":{}}',
    remoteStartTimes: '',
    remoteServicesDescription: '',
  },
  inAppVideoLocation: {
    workHours: '{ "offsite": {}, "remote":{}, "inapp":{}}',
    inappStartTimes: '',
  },
  newLocationAddress: {
    addressName: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    postalCode: '',
    hideLocation: false,
    workHours:
      '{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}],"saturday":[{"from":"9:00am","to":"5:00pm"}], "sunday":[{"from":"9:00am","to":"5:00pm"}]}',
    startTimes: '',
  },
  selectedLocationForEdit: null,
  activeServiceCreation: false,
};

const LocationsReducer = (state = initialState, action) => {
  switch (action.type) {
    case GET_AVAILABLE_LOCATIONS_SUCCEED_ACTION:
      return {
        ...state,
        locationsList: action.payload,
        filteredLocations: action.payload,
      };
    case SET_SELECTED_LOCATION:
      return {
        ...state,
        selectedLocations: action.payload,
      };
    case SET_SELECTED_EXTRA_LOCATION:
      return {
        ...state,
        selectedExtraLocations: action.payload,
      };
    case SET_SELF_BOOKING_USERS_ACTION:
      return {
        ...state,
        selfBookingsUsers: action.payload,
      };
    case SET_SELF_BOOKING_ACTION:
      return {
        ...state,
        selfBookings: {
          ...action.payload,
          workHours: action.payload.workHours
            ? action.payload.workHours
            : '{"offsite":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]},"remote":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]},"inapp":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]}}',
        },
        clientLocation: {
          workHours: action.payload.workHours
            ? action.payload.workHours
            : '{"offsite":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]},"remote":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]},"inapp":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]}}',
          offsiteStartTimes: action.payload.offsiteStartTimes
            ? action.payload.offsiteStartTimes
            : '',
          travelTime: action.payload.travelTime,
          travelRadius: action.payload.travelRadius,
          travelOrigin: action.payload.travelOrigin,
        },
        remoteLocation: {
          workHours: action.payload.workHours
            ? action.payload.workHours
            : '{"offsite":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]},"remote":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]},"inapp":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]}}',
          remoteStartTimes: action.payload.remoteStartTimes
            ? action.payload.remoteStartTimes
            : '',
          remoteServicesDescription: action.payload.remoteServicesDescription,
        },
        inAppVideoLocation: {
          workHours: action.payload.workHours
            ? action.payload.workHours
            : '{"offsite":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]},"remote":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]},"inapp":{"monday":[{"from":"9:00am","to":"5:00pm"}],"tuesday":[{"from":"9:00am","to":"5:00pm"}],"wednesday":[{"from":"9:00am","to":"5:00pm"}],"thursday":[{"from":"9:00am","to":"5:00pm"}],"friday":[{"from":"9:00am","to":"5:00pm"}]}}',
          inappStartTimes: action.payload.inappStartTimes
            ? action.payload.inappStartTimes
            : '',
        },
      };
    case CLEAR_SELECTED_LOCATION:
      return {
        ...state,
        selectedLocations: [],
        filteredLocations: [],
      };
    case CLEAR_SELECTED_EXTRA_LOCATION:
      return {
        ...state,
        selectedExtraLocations: [],
        filteredLocations: [],
      };
    case SET_FILTERED_LOCATIONS:
      return {
        ...state,
        filteredLocations: action.payload,
      };
    case SET_CLIENT_LOCATION_VALUES_ACTION:
      return {
        ...state,
        clientLocation: {
          ...state.clientLocation,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case SET_SELECTED_LOCATION_FOR_EDIT_ACTION:
      return {
        ...state,
        selectedLocationForEdit: action.payload,
      };
    case CLEAR_SELECTED_LOCATION_FOR_EDIT:
      return {
        ...state,
        selectedLocationForEdit: null,
      };
    case SET_SELECTED_LOCATION_FOR_EDIT_VALUES_ACTION:
      return {
        ...state,
        selectedLocationForEdit: {
          ...state.selectedLocationForEdit,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case SET_NEW_LOCATION_VALUES_ACTION:
      return {
        ...state,
        newLocationAddress: {
          ...state.newLocationAddress,
          [`${action.payload.key}`]: action.payload.value,
        },
      };

    case CLEAR_NEW_LOCATION_VALUES:
      return {
        ...state,
        newLocationAddress: initialState.newLocationAddress,
      };
    case SET_REMOTE_LOCATION_VALUES_ACTION:
      return {
        ...state,
        remoteLocation: {
          ...state.remoteLocation,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case SET_IN_APP_VIDEO_LOCATION_VALUES_ACTION:
      return {
        ...state,
        inAppVideoLocation: {
          ...state.inAppVideoLocation,
          [`${action.payload.key}`]: action.payload.value,
        },
      };
    case SET_ACTIVE_SERVICE_CREATION:
      return {
        ...state,
        activeServiceCreation: action.payload,
      };
    case CLEAR_STATE_LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default LocationsReducer;
