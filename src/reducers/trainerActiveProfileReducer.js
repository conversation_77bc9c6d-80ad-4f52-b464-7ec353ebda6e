import { createSlice } from '@reduxjs/toolkit';

const initialState = null;

export const trainerActiveProfileSlice = createSlice({
  name: 'trainerActiveProfile',
  initialState,
  reducers: {
    setTrainerActiveProfile: (state, action) => action.payload,
    resetTrainerActiveProfile: () => null,
  },
});

// Extract the action creators object and the reducer from the slice
export const { actions, reducer } = trainerActiveProfileSlice;
export default reducer;

// Extract and export each action creator by name
export const { setTrainerActiveProfile, resetTrainerActiveProfile } = actions;
