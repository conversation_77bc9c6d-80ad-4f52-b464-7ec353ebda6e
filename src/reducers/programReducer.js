import { createSlice } from '@reduxjs/toolkit';
import nasm from '../dataManager/apiConfig';
import { LOGOUT } from '../actions/currentUserActions';
import { logException } from '../util/logging';
// import querystring from 'querystring'
// import { Alert } from 'react-native';

const initialState = {
  loading: false,
  error: false,
  nextPage: null,
  isRefreshing: false,
  byId: {},
  programCategories: [],
};

export const programsSlice = createSlice({
  name: 'programs',
  initialState,
  reducers: {
    // GET DEFAULT SECTIONS
    programCategoriesRequested: () => {
      // should put some kind of loading state here
    },
    programCategoriesReceived: (state, action) => {
      state.programCategories = action.payload;
    },
    programCategoriesError: (state, action) => {
      state.error = action.payload;
    },
  },
  extraReducers: {
    [LOGOUT]: () => initialState,
  },
});

// Extract the action creators object and the reducer from the slice
export const { actions, reducer } = programsSlice;
export default reducer;

// Extract and export each action creator by name
export const {
  programCategoriesRequested,
  programCategoriesReceived,
  programCategoriesError,
} = actions;

// *****
// ACTION CREATORS
// *****

// -----
// GET PROGRAM CATEGORIES
export const getProgramCategories = () => async (dispatch) => {
  dispatch(actions.programCategoriesRequested());
  const response = await nasm.api
    .getProgramCategories()
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.programCategoriesError(response.error.message));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.programCategoriesReceived(response));
  return Promise.resolve(response);
};
