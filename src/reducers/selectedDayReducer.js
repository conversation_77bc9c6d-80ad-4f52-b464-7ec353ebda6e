import { createSlice } from '@reduxjs/toolkit';
import moment from 'moment';
import { LOGOUT } from '../actions/currentUserActions';

const initialState = {
  day: moment().format('YYYY-MM-DD'),
};

export const selectedDaySlice = createSlice({
  name: 'selectedDay',
  initialState,
  reducers: {
    clearDay: () => initialState,
    selectDay: (state, action) => {
      const day = action.payload;
      state.day = day;
    },
  },
  extraReducers: {
    [LOGOUT]: (state) => initialState,
  },
});

export const { actions, reducer } = selectedDaySlice;

export const { clearDay } = actions;

export default reducer;

export const selectDay = (day) => (dispatch) => {
  dispatch(actions.selectDay(day));
  return Promise.resolve(day);
};
