import React, { useReducer } from 'react';
import {
  getKeyByValue,
  convert_to_kilograms,
  convert_from_kilograms,
  get_height_from_centimeters,
  convert_height_to_centimeters_from_inches,
} from '../util/CalculatorUtils';
import {
  UNIT_TYPE,
  GENDER_TYPE,
  CALCULATION_TYPE,
  MethodKeysAndLabel,
  BODY_FAT_ACTION_TYPES,
} from '../types/BodyFatTypes';
import { calculateBodyFat } from '../screens/MainStack/Calculators/BodyFatCalculator/BodyFatCalculations';

const initialState = {
  unitType: UNIT_TYPE.IMPERIAL,
  methodType: CALCULATION_TYPE.US_NAVY_METHOD,
  weight: '',
  sex: GENDER_TYPE.MALE,
  age: '',
  height_cm: '',
  height_ft: '',
  height_in: '',
  bodyfat_score: null,
};

function reducer(state, action) {
  let newWeight = state.weight;
  let newMaxWeight = state.maxWeight;
  const unitType = action.payload?.unitType;
  const bodyfat_score = calculateBodyFat(state);
  const skinfoldObject = Object.values(
    MethodKeysAndLabel[getKeyByValue(CALCULATION_TYPE, state.methodType)],
  ).filter((field) => !field.gender || field.gender === state.sex);
  const skinfoldKeys = skinfoldObject.map((field) => field.key);
  switch (action.type) {
    case BODY_FAT_ACTION_TYPES.RESET:
      return { ...initialState };
    case BODY_FAT_ACTION_TYPES.EDIT:
      return {
        ...state,
        ...action.payload,
      };
    case BODY_FAT_ACTION_TYPES.CHANGE_UNITS:
      // convert units if previous weight unit is different from new weight unit
      if (state.unitType !== unitType) {
        if (state.unitType === UNIT_TYPE.METRIC) {
          if (state.weight) {
            newWeight = convert_from_kilograms(state.weight);
            newMaxWeight = convert_from_kilograms(state.maxWeight);
          }
          if (state.height_cm) {
            state.height_ft = get_height_from_centimeters(state.height_cm).feet;
            state.height_in = get_height_from_centimeters(
              state.height_cm,
            ).inches;
          }
          skinfoldKeys.forEach((field) => {
            if (Number(state[field])) {
              state[field] = get_height_from_centimeters(
                state[field],
              ).totalInches.toFixed(2);
            }
          });
        } else {
          if (state.weight) {
            newWeight = convert_to_kilograms(state.weight);
            newMaxWeight = convert_to_kilograms(state.maxWeight);
          }
          if (state.height_ft) {
            state.height_cm = convert_height_to_centimeters_from_inches(
              Number(state.height_ft) * 12 + Number(state.height_in),
            );
          }
          skinfoldKeys.forEach((field) => {
            if (Number(state[field])) {
              state[field] = convert_height_to_centimeters_from_inches(
                state[field],
              );
            }
          });
        }
      }

      return {
        ...state,
        unitType,
        weight: newWeight,
        maxWeight: newMaxWeight,
      };
    case BODY_FAT_ACTION_TYPES.CALCULATE:
      return {
        ...state,
        bodyfat_score,
      };
    default:
      return state;
  }
}

export const BodyFatStateContext = React.createContext();
export const BodyFatDispatchContext = React.createContext();

export function BodyFatContextProvider({ children }) {
  const [state, dispatch] = useReducer(reducer, { ...initialState });

  return (
    <BodyFatDispatchContext.Provider value={dispatch}>
      <BodyFatStateContext.Provider value={state}>
        {children}
      </BodyFatStateContext.Provider>
    </BodyFatDispatchContext.Provider>
  );
}
