/* eslint-disable react/prop-types */
/* eslint-disable no-case-declarations */
import React, { useReducer } from 'react';
import {
  convert_to_kilograms,
  convert_from_kilograms,
  get_height_from_centimeters,
  convert_height_to_centimeters_from_inches,
} from '../util/CalculatorUtils';
import { BMI_ACTION_TYPES, UNIT_TYPE } from '../types/BMItypes';

const initialState = {
  unitType: UNIT_TYPE.US,
  weight: '',
  height_cm: '',
  height_ft: '',
  height_in: '',
  bmiScore: 0,
};

function reducer(state, action) {
  switch (action.type) {
    case BMI_ACTION_TYPES.RESET:
      return { ...initialState };
    case BMI_ACTION_TYPES.EDIT:
      return {
        ...state,
        ...action.payload,
      };
    case BMI_ACTION_TYPES.CHANGE_UNITS:
      let newWeight = state.weight;
      const { unitType } = action.payload;
      // convert units if previous weight unit is different from new weight unit
      if (state.unitType !== unitType) {
        if (state.unitType === UNIT_TYPE.METRIC) {
          if (state.weight) {
            newWeight = convert_from_kilograms(state.weight);
          }
          if (state.height_cm) {
            state.height_ft = get_height_from_centimeters(state.height_cm).feet;
            state.height_in = get_height_from_centimeters(
              state.height_cm,
            ).inches;
          }
        } else {
          if (state.weight) {
            newWeight = convert_to_kilograms(state.weight);
          }
          if (state.height_ft) {
            state.height_cm = convert_height_to_centimeters_from_inches(
              Number(state.height_ft) * 12 + Number(state.height_in),
            );
          }
        }
      }

      return {
        ...state,
        unitType,
        weight: newWeight,
      };
    case BMI_ACTION_TYPES.CALCULATE:
      let weight = 0;
      let height = 0;
      if (state.unitType === UNIT_TYPE.METRIC) {
        height = state.height_cm;
        weight = state.weight;
      } else {
        height = convert_height_to_centimeters_from_inches(
          Number(state.height_ft) * 12 + Number(state.height_in),
        );
        weight = convert_to_kilograms(state.weight);
      }
      const height_m = height / 100;
      const heightSq = height_m * height_m;
      return {
        ...state,
        bmiScore: weight / heightSq,
      };
    default:
      return state;
  }
}

export const BMIStateContext = React.createContext();
export const BMIDispatchContext = React.createContext();

export function BMIContextProvider({ children }) {
  const [state, dispatch] = useReducer(reducer, { ...initialState });

  return (
    <BMIDispatchContext.Provider value={dispatch}>
      <BMIStateContext.Provider value={state}>
        {children}
      </BMIStateContext.Provider>
    </BMIDispatchContext.Provider>
  );
}
