// @flow
import type { LoginPreferenceAction } from '../actions/loginPreferenceActions';
import { REMEMBER_ME, FORGET_ME } from '../actions/loginPreferenceActions';
import { LOGOUT } from '../actions/currentUserActions';

type State = {
  +rememberMe: boolean,
  +rememberedEmail: ?string,
  +touchIdEnabled: boolean,
  +passcode: ?string,
};

const initialState: State = {
  rememberMe: false,
  rememberedEmail: null,
  touchIdEnabled: false,
  passcode: null,
};

export default function (
  state: State = initialState,
  action: LoginPreferenceAction,
): State {
  switch (action.type) {
    case REMEMBER_ME: {
      return { ...state, rememberMe: true, rememberedEmail: action.payload };
    }
    case FORGET_ME: {
      return { ...state, rememberMe: false, rememberedEmail: null };
    }
    case LOGOUT: {
      return initialState;
    }
    default: {
      return state;
    }
  }
}
