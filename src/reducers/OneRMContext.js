/* eslint-disable react/prop-types */
/* eslint-disable no-case-declarations */
import React, { useReducer } from 'react';
import {
  convert_to_kilograms,
  convert_from_kilograms,
} from '../util/CalculatorUtils';
import { calculateMaxLiftWeight } from '../screens/MainStack/Calculators/OneRMCalculator/OneRMCalculations';
import { UNIT_TYPE, LIFT_TYPE, ONE_RM_ACTION_TYPES } from '../types/RMtypes';

const initialState = {
  unitType: UNIT_TYPE.US,
  liftType: LIFT_TYPE.NOT_SPECIFIED,
  weight: '',
  reps: '',
  maxWeight: 0,
};

function reducer(state, action) {
  switch (action.type) {
    case ONE_RM_ACTION_TYPES.RESET:
      return { ...initialState };
    case ONE_RM_ACTION_TYPES.EDIT:
      const { weight, reps, liftType } = action.payload;
      return {
        ...state,
        weight: weight != null ? weight : state.weight,
        reps: reps != null ? reps : state.reps,
        liftType: liftType != null ? liftType : state.liftType,
      };
    case ONE_RM_ACTION_TYPES.CHANGE_UNITS:
      let newWeight = state.weight;
      let newMaxWeight = state.maxWeight;
      const { unitType } = action.payload;
      // convert units if previous weight unit is different from new weight unit
      if (state.unitType !== unitType) {
        if (state.unitType === UNIT_TYPE.METRIC) {
          newWeight = convert_from_kilograms(state.weight);
          newMaxWeight = convert_from_kilograms(state.maxWeight);
        } else {
          newWeight = convert_to_kilograms(state.weight);
          newMaxWeight = convert_to_kilograms(state.maxWeight);
        }
      }

      return {
        ...state,
        unitType,
        weight: newWeight,
        maxWeight: newMaxWeight,
      };
    case ONE_RM_ACTION_TYPES.CALCULATE:
      return {
        ...state,
        maxWeight:
          state.liftType === LIFT_TYPE.NOT_SPECIFIED && state.reps > 1
            ? calculateMaxLiftWeight(state.weight, state.reps)
            : state.weight,
      };
    default:
      return state;
  }
}

export const OneRmStateContext = React.createContext();
export const OneRmDispatchContext = React.createContext();

export function OneRmContextProvider({ children }) {
  const [state, dispatch] = useReducer(reducer, { ...initialState });

  return (
    <OneRmDispatchContext.Provider value={dispatch}>
      <OneRmStateContext.Provider value={state}>
        {children}
      </OneRmStateContext.Provider>
    </OneRmDispatchContext.Provider>
  );
}
