import { createSlice } from '@reduxjs/toolkit';

const initialState = {};

export const selectedGroupSlice = createSlice({
  name: 'selectedGroup',
  initialState,
  reducers: {
    selectGroup: (state, action) => action.payload,
    deselectGroup: () => null,
  },
});

// Extract the action creators object and the reducer from the slice
export const { actions, reducer } = selectedGroupSlice;
export default reducer;

// Extract and export each action creator by name
export const { selectGroup, deselectGroup } = actions;
