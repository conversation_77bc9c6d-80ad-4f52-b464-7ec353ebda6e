// @flow
import PropTypes from 'prop-types';
import type {
  AssessmentAction,
  AssessmentName,
} from '../actions/assessmentActions';
import {
  START_ASSESSMENT,
  ASSESSMENT_NAMES,
} from '../actions/assessmentActions';
import { LOGOUT } from '../actions/currentUserActions';
// Reducer state
type State = {
  +currentAssessment: AssessmentName,
};

const initialState: State = {
  currentAssessment: null,
};

// Export prop types for react component
export const assessmentPropTypes = PropTypes.shape({
  currentAssessment: PropTypes.oneOf(Object.values(ASSESSMENT_NAMES)),
});

// Main reducer function
export default function (
  state: State = initialState,
  action: AssessmentAction,
): State {
  switch (action.type) {
    case START_ASSESSMENT: {
      return { ...state, currentAssessment: action.payload };
    }
    case LOGOUT: {
      return initialState;
    }
    default: {
      return state;
    }
  }
}
