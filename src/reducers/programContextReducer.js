import { createSlice } from '@reduxjs/toolkit';
import { LOGOUT } from '../actions/currentUserActions';

export const programContexts = {
  LIBRARY: 'LIBRARY',
  SCHEDULING: 'SCHEDULING',
  RESCHEDULING: 'RESCHEDULING',
};

const initialState = null;

export const programContextSlice = createSlice({
  name: 'programContext',
  initialState,
  reducers: {
    programLibrary: () => programContexts.LIBRARY,
    scheduleProgram: () => programContexts.SCHEDULING,
    rescheduleProgram: () => programContexts.RESCHEDULING,
    clearProgramContext: () => null,
  },
  extraReducers: {
    [LOGOUT]: (state) => initialState,
  },
});

// Extract the action creators object and the reducer from the slice
export const { actions, reducer } = programContextSlice;
export default reducer;

// Extract and export each action creator by name
export const {
  programLibrary,
  scheduleProgram,
  rescheduleProgram,
  clearProgramContext,
} = actions;
