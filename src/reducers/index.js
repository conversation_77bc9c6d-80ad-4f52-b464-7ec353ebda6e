import assessments from './assessmentReducer';
import correctiveExercises from './correctiveExercisesReducer';
import currentUser from './currentUserReducer';
import loginPreferences from './loginPreferenceReducer';
import programContext from './programContextReducer';
import programs from './programReducer';
import selectedClient from './selectedClientReducer';
import selectedGroup from './selectedGroupReducer';
import selectedProgram from './selectedProgramReducer';
import selectedWorkout from './selectedWorkoutReducer';
import workouts from './workoutsReducer';
import deepLink from './deepLinkReducer';
import selectedDay from './selectedDayReducer';
import bwpCalculator from './bwpReducer';
import calculatorContext from './calculatorContextReducer';
import trainerActiveProfile from './trainerActiveProfileReducer';

export default {
  assessments,
  correctiveExercises,
  currentUser,
  loginPreferences,
  programContext,
  programs,
  selectedClient,
  selectedGroup,
  selectedProgram,
  selectedWorkout,
  workouts,
  deepLink,
  selectedDay,
  bwpCalculator,
  calculatorContext,
  trainerActiveProfile,
};
