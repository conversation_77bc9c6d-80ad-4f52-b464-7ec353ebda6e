import { createSlice } from '@reduxjs/toolkit';
import nasm from '../dataManager/apiConfig';
import { deselectClient } from './selectedClientReducer';
import { LOGOUT } from '../actions/currentUserActions';
import { logException } from '../util/logging';

const initialState = {
  loading: false,
  exercises: null,
  error: false,
};

export const correctiveExercisesSlice = createSlice({
  name: 'correctiveExercises',
  initialState,
  reducers: {
    // Async Reducers
    requestCorrectiveExercises: (state) => {
      state.loading = true;
      state.exercises = null;
      state.error = false;
    },
    receiveCorrectiveExercises: (state, action) => {
      state.loading = false;
      state.error = false;
      const exercises = flattenExercises(action.payload);
      state.exercises = exercises;
    },
    errorCorrectiveExercises: (state, action) => {
      state.loading = false;
      state.exercises = null;
      state.error = action.payload;
    },
  },
  extraReducers: {
    [deselectClient]: (state) => {
      state.loading = false;
      state.exercises = null;
      state.error = false;
    },
    [LOGOUT]: () => initialState,
  },
});

// Extract the action creators object and the reducer from the slice
export const { actions, reducer } = correctiveExercisesSlice;
export default reducer;

// *****
// ASYNC ACTION CREATORS
// *****
export const getCorrectiveExercises = () => async (dispatch, getState) => {
  const { selectedClient } = getState();

  if (!selectedClient || !selectedClient?.id) {
    return null;
  }

  dispatch(
    actions.requestCorrectiveExercises({
      clientId: selectedClient?.id,
      assessmentName: 'ohsa',
    }),
  );
  const response = await nasm.api
    .getCorrectiveExercises(selectedClient?.id)
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.errorCorrectiveExercises(response.error.message));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.receiveCorrectiveExercises(response));
  return Promise.resolve(response);
};

// Helper Functions
const flattenExercises = (response) => response.reduce((exercises, section) => {
  const sectionExercises = [];
  Object.keys(section).forEach((key) => {
    section[key].forEach((exercise) => {
      sectionExercises.push({ sectionId: key, ...exercise });
    });
  });
  return exercises.concat(sectionExercises);
}, []);
