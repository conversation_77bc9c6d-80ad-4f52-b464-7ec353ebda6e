import { createSlice } from '@reduxjs/toolkit';
import { LOGOUT } from '../actions/currentUserActions';
// import nasm from '../dataManager/apiConfig'

const initialState = {};

export const selectedClientSlice = createSlice({
  name: 'currentUser',
  initialState,
  reducers: {
    selectClient: (state, action) => action.payload,
    deselectClient: () => null,
  },
  extraReducers: {
    [LOGOUT]: () => initialState,
  },
});

// Extract the action creators object and the reducer from the slice
export const { actions, reducer } = selectedClientSlice;
export default reducer;

// Extract and export each action creator by name
export const { selectClient, deselectClient } = actions;
