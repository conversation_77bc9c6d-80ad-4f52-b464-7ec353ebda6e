import { createSlice } from '@reduxjs/toolkit';
import { v4 as uuidV4 } from 'uuid';
import { normalize, schema, denormalize } from 'normalizr';
import analytics from '@react-native-firebase/analytics';
import moment from 'moment';
import { Alert } from 'react-native';
import { actions as workoutActions } from './workoutsReducer';
import {
  actions as selectedProgramActions,
  saveProgram,
} from './selectedProgramReducer';

import nasm from '../dataManager/apiConfig';
import { programContexts } from './programContextReducer';
import { LOGOUT } from '../actions/currentUserActions';
import { track } from '../util/Analytics';
import { logException } from '../util/logging';
import { formatSuperSets, unformatSuperSets } from '../util/programUtils';

const initialState = {
  loading: false,
  loadingDetail: false,
  error: false,
  workoutChanged: false,
  duplicate: false,
  editable: false,
  workout: null,
  entities: null,
};

export const selectedWorkoutSlice = createSlice({
  name: 'selectedWorkout',
  initialState,
  reducers: {
    // -----
    // CREATE WORKOUT TEMPLATE
    createWorkoutTemplateRequested: (state) => {
      state.loading = true;
    },
    createWorkoutTemplateSuccess: (state) => {
      state.loading = false;
    },
    createWorkoutTemplateFailed: (state) => {
      state.loading = false;
    },
    // ERROR RECEIVED
    workoutErrorReceived: (state, action) => {
      const error = action.payload;
      state.loadingDetail = false;
      state.loading = false;
      if (error) {
        state.error = error;
      }
    },
    // -----
    // GET WORKOUT DETAILS
    workoutDetailsRequested: (state, action) => {
      const { id } = action.payload;
      return {
        ...initialState,
        loadingDetail: true,
        id,
      };
    },
    workoutDetailsReceived: (state, action) => {
      const { workout, editable } = action.payload;
      state.loadingDetail = false;
      state.editable = editable;
      const [newWorkout] = formatSuperSets([workout]);
      const keyedWorkout = addExerciseKeys(newWorkout);
      const { result, entities } = normalize(keyedWorkout, workoutSchema);
      state.workoutChanged = false;
      // Default to the scheduled workout name (NE-2399)
      result.name = result.workout_name || result.name;
      state.workout = result;
      state.entities = {
        sections: entities.sections || {},
        exercises: entities.exercises || {},
      };
    },
    // -----
    // SAVE WORKOUT
    showSaveLoading: (state) => {
      state.loading = true;
    },
    saveWorkoutRequested: (state, action) => {
      state.loading = true;
      state.workout.name = action.payload.name;
    },
    saveWorkoutRecevied: (state, action) => {
      const response = action.payload;
      state.loading = false;
      state.workout = {
        // Start with current state
        ...state.workout,
        // Add any updated base properties from the API
        ...action.payload,
        // We want to overwrite API sections to keep our normalized sections
        sections: state.workout.sections,
      };
      if (response.error) {
        state.error = response.error;
      } else {
        state.workoutChanged = false;
      }
    },
    // -----
    // LOCAL STATE UPDATES
    editWorkout: (state, action) => {
      const keyedWorkout = addExerciseKeys(action.paylod);
      state.workout = keyedWorkout;
      state.workoutChanged = true;
    },
    editWorkoutName: (state, action) => {
      const name = action.payload;
      state.workout.name = name;
      state.workoutChanged = true;
    },
    addExercises: (state, action) => {
      const { exercises, sectionId } = action.payload;
      exercises.forEach((exercise) => {
        const key = uuidV4();
        state.entities.exercises[key] = { key, ...exercise };
        state.entities.sections[sectionId].exercises.push(key);
      });
      state.workoutChanged = true;
    },
    editExercise: (state, action) => {
      const exercise = action.payload;
      state.entities.exercises[exercise.key] = exercise;
      state.workoutChanged = true;
    },
    removeExercise: (state, action) => {
      const { exercise, sectionId, flagChanges = true } = action.payload;
      const { exercises } = state.entities.sections[sectionId];
      const index = exercises.findIndex((item) => item === exercise.key);
      exercises.splice(index, 1);
      delete state.entities.exercises[exercise.key];
      if (flagChanges) {
        state.workoutChanged = true;
      }
    },
    sortExercises: (state, action) => {
      const newSections = action.payload;
      newSections.forEach((newSection) => {
        state.entities.sections[newSection.id].exercises = newSection.exercises;
      });
      state.workoutChanged = true;
    },
    clearWorkout: () => initialState,
    createNewWorkout: (state, action) => {
      const { result, entities } = normalize(action.payload, workoutSchema);
      state.editable = true;
      state.workout = result;
      state.entities = {
        sections: entities.sections || {},
        exercises: entities.exercises || {},
      };
    },
    duplicateWorkout: (state) => {
      state.duplicate = true;
      state.editable = true;
      state.workoutChanged = true;
      state.workout.name = `${state.workout.name} copy`;

      Alert.alert(
        'Copy Workout',
        'This workout has been successfully copied.\nPlease save to add to your library',
        [{ text: 'Ok', style: 'cancel' }],
      );
    },
  },
  extraReducers: {
    [selectedProgramActions.assignExercises]: (state, action) => {
      const { result, entities } = normalize(action.payload, workoutSchema);
      state.workout = result;
      state.entities = {
        sections: entities.sections || {},
        exercises: entities.exercises || {},
      };
    },
    [LOGOUT]: () => initialState,
  },
});

// Extract the action creators object and the reducer
export const { actions, reducer } = selectedWorkoutSlice;
// Extract and export each action creator by name
export const {
  editWorkout,
  editWorkoutName,
  removeExercise,
  sortExercises,
  clearWorkout,
  duplicateWorkout,
} = actions;

// Export the reducer, either as a default or named export
export default reducer;

// *****
// ACTION CREATORS
// *****

// -----
// SELECT WORKOUT
export const selectWorkout = (workout, editable = false, fetch = true) => async (dispatch, getState) => {
  const { id, key } = workout;
  const { selectedProgram, programContext, currentUser } = getState();
  const { SCHEDULING, RESCHEDULING } = programContexts;

  // This is a special case for quick-add workouts which must be plucked from schedule data
  if (!fetch) {
    dispatch(actions.workoutDetailsReceived({ workout, editable }));
    return Promise.resolve(workout);
  }

  // See if this workout already exists as part of a currently selected program
  const programWorkouts = selectedProgram?.entities?.workouts ?? {};
  if (
    key
      && programWorkouts[key]
      && [SCHEDULING, RESCHEDULING].includes(programContext)
  ) {
    const isEditable = programContext === SCHEDULING
        || programContext === RESCHEDULING
        || selectedProgram.editable;
    dispatch(
      actions.workoutDetailsReceived({
        workout: programWorkouts[key],
        editable: isEditable,
      }),
    );
    return Promise.resolve(programWorkouts[key]);
  }

  // Else fetch the workout
  dispatch(actions.workoutDetailsRequested({ id }));
  const response = await nasm.api.getWorkout(id).catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.workoutErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(
    actions.workoutDetailsReceived({
      workout: response,
      editable: response.owner_id === currentUser.id,
    }),
  );
  return Promise.resolve(response);
};
// -----
// DUPLICATE WORKOUT
export const fetchAndCopyWorkout = (id) => async (dispatch) => {
  dispatch(actions.workoutDetailsRequested(id));
  const response = await nasm.api.getWorkout(id).catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.workoutErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.workoutDetailsReceived({ workout: response }));
  dispatch(actions.duplicateWorkout());
  return Promise.resolve(response);
};
// -----
// CREATE WORKOUT
export const createNewWorkout = () => (dispatch, getState) => {
  const { defaultSections } = getState().workouts;
  const blankSections = defaultSections.map((section) => ({
    ...section,
    exercises: [],
  }));
  const workout = {
    name: '',
    sections: blankSections,
  };
  dispatch(actions.createNewWorkout(workout));
};
// -----
// SAVE WORKOUT
export const saveWorkout = () => async (dispatch, getState) => {
  const { programContext, selectedProgram, selectedWorkout } = getState();
  const {
    workout, entities, duplicate, loading,
  } = selectedWorkout;
  const { SCHEDULING, RESCHEDULING } = programContexts;
  const normalizedWorkout = denormalize(workout, workoutSchema, entities);
  const [newWorkout] = unformatSuperSets([normalizedWorkout]);

  dispatch(actions.showSaveLoading());
  // If the workout is part of program being edited, update the local program copy
  if (selectedProgram.program) {
    // Find the key on the parent workout if it doesn't exist
    if (!newWorkout.key) {
      const key = Object.values(selectedProgram.entities.workouts).find(
        (w) => w.id === newWorkout.id,
      )?.key;
      if (!key) throw new Error('Current program does not have a matching workout');
      newWorkout.key = key || uuidV4();
    }
    dispatch(selectedProgramActions.editWorkout(newWorkout));
  }

  // Program can't be saved until a schedule is created
  if (programContext === SCHEDULING) {
    dispatch(actions.saveWorkoutRecevied({ error: false }));
    return Promise.resolve(newWorkout);
  }

  // When editing a schedule, we can update the schedule after every workout edit
  if (programContext === RESCHEDULING) {
    dispatch(actions.saveWorkoutRecevied({ error: false }));
    if (
      selectedProgram.program.name === null
      || selectedProgram.program.name === undefined
    ) {
      // is quick add workout
      // save program as well
      if (workout.days_of_week) {
        const days = workout.days_of_week.map((day) => day.toString());
        dispatch(
          selectedProgramActions.setWorkoutDays({
            workoutKey: selectedProgram.program.workouts[0],
            days,
          }),
        );
      }
      return dispatch(saveProgram());
    }
    return Promise.resolve(newWorkout);
  }

  // Else make an API call to udpate the workout directly
  if (loading) return null;
  if (!newWorkout.name) {
    newWorkout.name = `New Workout ${moment().format('MM-DD')}`;
  }
  // TODO: saveWorkout requested/received seems redundant to update/create here
  dispatch(actions.saveWorkoutRequested(newWorkout));
  if (workout.id && !duplicate) {
    dispatch(workoutActions.updateWorkoutRequested(newWorkout));
    const response = await nasm.api
      .updateWorkout(newWorkout)
      .catch((error) => ({ error }));
    if (response.error) {
      dispatch(actions.workoutErrorReceived(response.error));
      logException(response.error);
      return Promise.reject(response.error);
    }
    dispatch(workoutActions.updateWorkoutReceived(response));
    dispatch(actions.saveWorkoutRecevied(response));
    analytics().logEvent('edit_custom_workout');
    return Promise.resolve(response);
  }

  dispatch(workoutActions.createWorkoutRequested(newWorkout));
  const response = await nasm.api
    .createWorkout(newWorkout)
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.workoutErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(workoutActions.createWorkoutReceived(response));
  dispatch(actions.saveWorkoutRecevied(response));
  analytics().logEvent('create_custom_workout');
  await track('workout_created');
  return Promise.resolve(response);
};

// ----
// SAVE WORKOUT AS A TEMPLATE IN LIBRARY
export const createWorkoutTemplateInLibrary = () => async (dispatch, getState) => {
  const { selectedWorkout } = getState();
  const { workout, entities, loading } = selectedWorkout;
  const normalizedWorkout = denormalize(workout, workoutSchema, entities);

  if (loading) return null;

  const [newWorkout] = unformatSuperSets([normalizedWorkout]);

  if (!newWorkout.name) {
    newWorkout.name = `New Workout ${moment().format('MM-DD')}`;
  }
  const requestBody = {
    name: newWorkout.name,
    description: null,
    is_visible: true,
    uploaded_media_id:
        newWorkout.uploaded_media && newWorkout.uploaded_media.id
          ? newWorkout.uploaded_media.id
          : null,
    sections: newWorkout.sections,
  };

  dispatch(actions.createWorkoutTemplateRequested());

  // Create Workout Template
  const request = nasm.api.createWorkout(requestBody);

  // Handle response/error
  const response = await request.catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.createWorkoutTemplateFailed());
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.createWorkoutTemplateSuccess());
  return Promise.resolve(response);
};

// -----
// AUTO SAVE ACTIONS
export const addExercises = ({ exercises, sectionId }) => (dispatch, getState) => {
  if (!exercises || (Array.isArray(exercises) && exercises.length < 1)) {
    throw new Error('addExercises: No exercises were provided');
  }
  const state = getState();
  const workoutSections = state.selectedWorkout.entities.sections;
  const defaultSection = Object.values(workoutSections).find(
    (section) => section.name === 'Warm-Up',
  );
  const id = sectionId || defaultSection?.id;
  if (!id) {
    throw new Error('addExercises: SectionId is required');
  }

  dispatch(actions.addExercises({ exercises, sectionId: id }));
  return dispatch(saveWorkout());
};

export const editExercise = (exercise) => (dispatch) => {
  dispatch(actions.editExercise(exercise));
};

// *****
// HELPER FUNCTIONS
// *****
const addExerciseKeys = (workout) => ({
  ...workout,
  sections: workout.sections.map((section) => ({
    ...section,
    exercises: (section.exercises || section.scheduled_exercises || []).map(
      (exercise) => ({
        ...exercise,
        key: exercise.key ? exercise.key : uuidV4(),
      }),
    ),
  })),
});

// *****
// Normalizr
// *****
const exerciseSchema = new schema.Entity(
  'exercises',
  {},
  { idAttribute: 'key' },
);
const sectionSchema = new schema.Entity('sections', {
  exercises: [exerciseSchema],
});
const workoutSchema = { sections: [sectionSchema] };
