import { createReducer } from '@reduxjs/toolkit';
import moment from 'moment';
import {
  ActionTypes,
  ActivityLevel,
  ActivityLevelStrings,
  initialModels,
  UnitType,
} from '../types/BWPTypes';
import {
  convert_from_kilograms,
  convert_height_to_centimeters_from_inches,
  convert_to_kilograms,
  get_height_from_centimeters,
  get_height_from_inches,
} from '../util/CalculatorUtils';

// The intial state of the steps
const getInitialStepsState = () => [
  {
    name: 'BMR',
    active: true,
    completed: false,
  },
  {
    name: 'TDEE',
    active: false,
    completed: false,
  },
  {
    name: 'Goal Weight',
    active: false,
    completed: false,
  },
  {
    name: 'Macros',
    active: false,
    completed: false,
  },
  {
    name: 'Client Results',
    active: false,
    completed: false,
  },
];

const initialState = {
  currentStep: 0,
  steps: getInitialStepsState(),
  prevUnits: initialModels.units,
  data: initialModels,
};

const bwpReducer = createReducer(initialState, {
  [ActionTypes.CHANGE_STEP]: (state, action) => {
    state.steps[action.payload].active = true;
    state.currentStep = action.payload;
  },
  [ActionTypes.COMPLETE_STEP]: (state, action) => {
    state.steps[action.payload].completed = true;
  },
  [ActionTypes.CHANGE_UNITS]: (state, action) => {
    const newUnits = action.payload;
    state.prevUnits = state.data.units;
    state.data.units = newUnits;

    if (newUnits === UnitType.US) {
      state.data.weight = convert_from_kilograms(state.data.weight);
      state.data.goalWeight = convert_from_kilograms(state.data.goalWeight);
      state.data.height_ft = get_height_from_centimeters(
        state.data.height_cm,
      ).feet;
      state.data.height_in = get_height_from_centimeters(
        state.data.height_cm,
      ).inches;
    } else {
      state.data.weight = convert_to_kilograms(state.data.weight);
      state.data.goalWeight = convert_to_kilograms(state.data.goalWeight);
      state.data.height_cm = convert_height_to_centimeters_from_inches(
        Number(state.data.height_ft) * 12 + Number(state.data.height_in),
      );
    }
  },
  [ActionTypes.UPDATE_FORM_DATA]: (state, action) => {
    state.data = {
      ...state.data,
      ...action.payload,
    };
  },
  [ActionTypes.CLEAR_FORM_DATA]: (state) => {
    // Get next form iteration value
    const nextFormIteration = state.data.formIteration + 1;

    // Reset form data back to initial values
    state.data = {
      ...initialModels,
      formIteration: nextFormIteration,
    };

    state.prevUnits = initialModels.units;
    state.currentStep = 0;
    state.steps = getInitialStepsState();
  },
  [ActionTypes.INITIALIZE_FORM_DATA]: (state, action) => {
    const newState = {
      ...state.data,
      units:
        action.payload.unit_weight === 'lb'
        || action.payload.unit_height === 'in'
          ? UnitType.US
          : UnitType.METRIC,
      clientName: `${action.payload.first_name} ${action.payload.last_name}`,
      sex:
        action.payload.gender?.toLowerCase() === 'female'
        || action.payload.gender_type === 2
          ? 'Female'
          : 'Male',
      weight: action.payload.client_user.weight || 0,
      height_ft: get_height_from_inches(action.payload.client_user.height || 0)
        .feet,
      height_in: get_height_from_inches(action.payload.client_user.height || 0)
        .inches,
      height_cm: convert_height_to_centimeters_from_inches(
        action.payload.client_user.height || 0,
      ),
      age: calculateAge(action.payload.birth_date),
      currentActivityLevel: getActivityLevel(
        action.payload.activityLevel?.title,
      ),
      currentActivityLevelId: action.payload.activityLevel?.id,
      allActivityLevels: action.payload.allActivityLevels,
    };

    state.data = { ...newState };
    state.prevUnits = newState.units;
  },
  [ActionTypes.SYNC_UNITS]: (state) => {
    state.prevUnits = state.data.units;
  },
});

// HELPER FUNCTIONS
const calculateAge = (birthDate) => moment().diff(moment(birthDate), 'years') || '';

const getActivityLevel = (activityLevel) => {
  if (!activityLevel) {
    return ActivityLevel.SEDENTARY;
  }
  const mapping = {
    [ActivityLevelStrings.SEDENTARY]: ActivityLevel.SEDENTARY,
    [ActivityLevelStrings.LIGHTLY_ACTIVE]: ActivityLevel.LIGHTLY_ACTIVE,
    [ActivityLevelStrings.MODERATELY_ACTIVE]: ActivityLevel.MODERATELY_ACTIVE,
    [ActivityLevelStrings.VERY_ACTIVE]: ActivityLevel.VERY_ACTIVE,
    [ActivityLevelStrings.EXTREMELY_ACTIVE]: ActivityLevel.EXTREMELY_ACTIVE,
  };

  return mapping[activityLevel];
};

export default bwpReducer;
