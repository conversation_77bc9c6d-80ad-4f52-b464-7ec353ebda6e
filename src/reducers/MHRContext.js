import React, { useReducer } from 'react';
import { MHR_ACTION_TYPES } from '../types/MHRTypes';

const initialState = {
  age: 0,
  selectedZone: null,
  HRmax: 0,
};

function reducer(state, action) {
  switch (action.type) {
    case MHR_ACTION_TYPES.RESET:
      return { ...initialState };
    case MHR_ACTION_TYPES.EDIT:
      return {
        ...state,
        ...action.payload,
      };
    case MHR_ACTION_TYPES.CALCULATE:
      return {
        ...state,
        HRmax: 208 - 0.7 * Number(state.age),
      };
    default:
      return state;
  }
}

export const MHRStateContext = React.createContext();
export const MHRDispatchContext = React.createContext();

export function MHRContextProvider({ children }) {
  const [state, dispatch] = useReducer(reducer, { ...initialState });

  return (
    <MHRDispatchContext.Provider value={dispatch}>
      <MHRStateContext.Provider value={state}>
        {children}
      </MHRStateContext.Provider>
    </MHRDispatchContext.Provider>
  );
}
