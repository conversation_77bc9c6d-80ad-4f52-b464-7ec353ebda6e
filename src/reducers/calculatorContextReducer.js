import { createSlice } from '@reduxjs/toolkit';

export const CALCULATOR_CONTEXTS = {
  CLIENT: 'CLIENT',
  SELECTED_CLIENT: 'SELECTED_CLIENT',
  TRAINER_ACCOUNT: 'TRAINER_ACCOUNT',
};

const initialState = {
  type: null,
};

export const calculatorContextSlice = createSlice({
  name: 'calculatorContext',
  initialState,
  reducers: {
    clearCalculatorContext: () => null,
    setCalculatorContext: (state, { payload }) => {
      state.type = payload;
    },
  },
});

// Extract the action creators object and the reducer from the slice
export const { actions, reducer } = calculatorContextSlice;
export default reducer;

// Extract and export each action creator by name
export const { clearCalculatorContext, setCalculatorContext } = actions;
