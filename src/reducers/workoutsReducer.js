import { createSlice } from '@reduxjs/toolkit';
import querystring from 'querystring';
import nasm from '../dataManager/apiConfig';
import { LOGOUT } from '../actions/currentUserActions';
import { logException } from '../util/logging';

const initialState = {
  loading: false,
  error: false,
  nextPage: null,
  isRefreshing: false,
  byId: {},
  defaultSections: [],
  loadingSections: false,
};

export const workoutsSlice = createSlice({
  name: 'workouts',
  initialState,
  reducers: {
    // -----
    // GET ALL WORKOUTS
    workoutsRequested: (state, action) => {
      const { reload, query } = action.payload;
      state.loading = true;
      state.query = query;
      if (reload) {
        state.byId = {};
      }
    },
    workoutsReceived: (state, action) => {
      const { error } = action.payload;
      state.loading = false;
      state.error = error;

      if (error) {
        state.byId = {};
        return;
      }

      const { workouts, nextPage } = action.payload;
      state.byId = {
        ...state.byId,
        ...normalizeWorkouts(workouts),
      };
      state.nextPage = parseNextPage(nextPage);
      state.nextPageQuery = nextPage;
    },
    // -----
    // CREATE WORKOUT
    createWorkoutRequested: (state) => {
      state.loading = true;
    },
    createWorkoutReceived: (state, action) => {
      const response = action.payload;
      state.loading = false;
      state.byId[response.id] = response;
    },
    // -----
    // UPDATE WORKOUT
    updateWorkoutRequested: (state) => {
      state.loading = true;
    },
    updateWorkoutReceived: (state, action) => {
      const response = action.payload;
      state.loading = false;
      state.byId[response.id] = response;
    },
    // EDIT WORKOUT
    editWorkoutRequested: (state, action) => {
      const workout = action.payload;
      state.byId[workout.id].loading = true;
    },
    editWorkoutReceived: (state, action) => {
      const { id, response } = action.payload;
      state.byId[id] = response.workout;
    },
    // -----
    // DELETE WORKOUT
    deleteWorkoutRequested: (state, action) => {
      const id = action.payload;
      state.byId[id].loading = true;
    },
    deleteWorkoutReceived: (state, action) => {
      const { id } = action.payload;
      delete state.byId[id];
    },
    // -----
    // GET DEFAULT SECTIONS
    workoutSectionsRequested: (state) => {
      state.loadingSections = true;
    },
    workoutSectionsRecevied: (state, action) => {
      state.loadingSections = false;
      state.defaultSections = action.payload;
    },
    workoutErrorReceved: (state, action) => {
      state.error = action.payload;
    },
  },
  extraReducers: {
    [LOGOUT]: () => initialState,
  },
});

// Extract the action creators object and the reducer from the slice
export const { actions, reducer } = workoutsSlice;
export default reducer;

// Extract and export each action creator by name
export const {
  workoutsRequested,
  workoutsReceived,
  deleteWorkoutRequested,
  deleteWorkoutReceived,
  workoutSectionsRequested,
  workoutSectionsRecevied,
  workoutErrorReceved,
} = actions;

// *****
// ACTION CREATORS
// *****

// -----
// GET ALL WORKOUTS
const defaultOptions = {
  page: 1,
  size: 100,
  search: '',
  workoutFilter: [],
  sortField: 'name',
  sortOrder: 'asc',
  getPublic: false,
  getPrivate: false,
  reload: false,
};

export const fetchWorkouts = (options) => async (dispatch, getState) => {
  const { reload, ...query } = { ...defaultOptions, ...options };
  const state = getState();
  if (state.workouts.loading && !reload) return null;
  // API request
  dispatch(workoutsRequested({ reload, query }));
  // API response
  const response = await nasm.api
    .getWorkouts(query)
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(workoutErrorReceved(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(workoutsReceived(response));
  return Promise.resolve(response);
};

// -----
// DELETE WORKOUT
export const deleteWorkout = (id) => async (dispatch, getState) => {
  const state = getState();
  const workout = state.workouts.byId[id];
  if (!workout || workout?.loading) return null;

  dispatch(deleteWorkoutRequested(id));
  const response = await nasm.api.deleteWorkout(id).catch((error) => ({ error }));
  if (response.error) {
    dispatch(workoutErrorReceved(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(deleteWorkoutReceived({ id, response }));
  return Promise.resolve(response);
};

// -----
// GET DEFAULT SECTIONS
export const getWorkoutSections = () => async (dispatch) => {
  dispatch(actions.workoutSectionsRequested());
  const response = await nasm.api
    .getWorkoutSections()
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(workoutErrorReceved(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.workoutSectionsRecevied(response));
  return Promise.resolve(response);
};

// *****
// HELPER FUNCTIONS
// *****
const normalizeWorkouts = (workouts) => workouts.reduce((acc, val) => {
  acc[val.id] = val;
  return acc;
}, {});

const parseNextPage = (q) => {
  if (!q) return null;
  const string = q.replace(/\?/, '');
  const params = querystring.parse(string);
  return params.page;
};
