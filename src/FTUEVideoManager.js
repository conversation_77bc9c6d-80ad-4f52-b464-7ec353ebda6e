import AsyncStorage from '@react-native-async-storage/async-storage';

const homeVideo = require('./resources/ClientOverview.mp4');
const addClientVideo = require('./resources/HowToAddClient.mp4');
const assessmentVideo = require('./resources/HowToMakeAssessment.mp4');
const schedulingVideo = require('./resources/HowToCreateProgram.mp4');

const painVideoUrl = 'https://production.smedia.lvp.llnw.net/c795395f130d4737be848be06f52ea62/0r/lA_7W-2LvfdAUnSa06tcv4D1fPeeodGGCSGhkeJtk/nasm_edge_tips_clients_health_issues.mp4?x=0&h=a6de6e6243a6c0337dd43e215aa2d80c';
const addClientVideoThumbnail = require('./resources/addClientVideoThumbnail.png');
const assessmentVideoThumbnail = require('./resources/assessmentVideoThumbnail.png');
const scheduleProgramVideoThumbnail = require('./resources/scheduleProgramVideoThumbnail.png');
const painVideoThumbnail = require('./resources/painVideoThumbnail.png');

const videosEnabled = true;

export function showAddClientVideoIfAble(trainerId, navigation) {
  return async () => {
    if (!videosEnabled) {
      return false;
    }
    return setTimeout(async () => {
      const videoShown = await AsyncStorage.getItem(
        `${trainerId}_add_client_video_shown`,
      );
      if (!videoShown) {
        navigation.navigate('FTUEVideoModal', {
          video: addClientVideo,
          videoThumbnail: addClientVideoThumbnail,
          fullScreen: true,
          body: {
            title: 'How to manage clients',
            description:
              'Add clients and track their progress effortlessly wherever you are.',
          },
        });
        AsyncStorage.setItem(`${trainerId}_add_client_video_shown`, 'true');
        return true;
      }
      return false;
    }, 1000);
  };
}

export function showAssessmentVideoIfAble(trainerId, navigation) {
  return async () => {
    if (!videosEnabled) {
      return false;
    }
    return setTimeout(async () => {
      const videoShown = await AsyncStorage.getItem(
        `${trainerId}_assessment_video_shown`,
      );
      if (!videoShown) {
        navigation.navigate('FTUEVideoModal', {
          video: assessmentVideo,
          videoThumbnail: assessmentVideoThumbnail,
          fullScreen: true,
          body: {
            title: 'How to perform an OHSA',
            description:
              'Evaluate clients and easily design the right exercise program based on their Overhead Squat Assessment results.',
          },
        });
        AsyncStorage.setItem(`${trainerId}_assessment_video_shown`, 'true');
        return true;
      }
      return false;
    }, 1000);
  };
}

export function showScheduleProgramVideoIfAble(trainerId, navigation) {
  return async () => {
    if (!videosEnabled) {
      return false;
    }
    return setTimeout(async () => {
      const videoShown = await AsyncStorage.getItem(
        `${trainerId}_schedule_video_shown`,
      );
      if (!videoShown) {
        navigation.navigate('FTUEVideoModal', {
          video: schedulingVideo,
          videoThumbnail: scheduleProgramVideoThumbnail,
          fullScreen: true,
          body: {
            title: 'How to schedule programs',
            description:
              'Take training to a whole new level with easy programming you can customize and share with your clients.',
          },
        });
        AsyncStorage.setItem(`${trainerId}_schedule_video_shown`, 'true');
        return true;
      }
      return false;
    }, 1000);
  };
}

export function showHomeVideoIfAble(clientId, navigation) {
  return async () => {
    if (!videosEnabled) {
      return false;
    }
    return setTimeout(async () => {
      const videoShown = await AsyncStorage.getItem(
        `${clientId}_home_video_shown`,
      );
      if (!videoShown) {
        navigation.navigate('FTUEVideoModal', {
          video: homeVideo,
          fullScreen: true,
          body: {
            title: 'Instant training',
            description:
              'Reach your fitness goals faster than ever with exceptional exercise programming and resources.',
          },
        });
        AsyncStorage.setItem(`${clientId}_home_video_shown`, 'true');
        return true;
      }
      return false;
    }, 1000);
  };
}

export function showPainVideoIfAble(trainerId, navigation) {
  return async () => {
    if (!videosEnabled) {
      return false;
    }
    return setTimeout(async () => {
      const videoShown = await AsyncStorage.getItem(
        `${trainerId}_pain_video_shown`,
      );
      if (!videoShown) {
        navigation.navigate('FTUEVideoModal', {
          uri: painVideoUrl,
          videoThumbnail: painVideoThumbnail,
          fullScreen: false,
          body: {
            title: 'Tips for Training Clients with Health Issues',
            description: '',
          },
        });
        AsyncStorage.setItem(`${trainerId}_pain_video_shown`, 'true');
        return true;
      }
      return false;
    }, 1000);
  };
}
