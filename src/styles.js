import {
  Platform,
  StyleSheet,
  Keyboard,
  StatusBar,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

export const colors = {
  // Zeplin exported colors
  nasmBlue: '#163963',
  subGrey: '#7c8084',
  lightBlue: '#b3d7ed',
  white: '#ffffff',
  black: '#000000',
  goodGreen: '#469a1e',
  medYellow: '#dc8c00',
  macaroniAndCheese: '#f6aa2c',
  badRed: '#9b221c',
  silver: '#d3d7db',
  silver2: '#e6e7e8',
  silver51: 'rgba(210, 215, 219, 0.51)',
  nasmRed: '#ef0b2b',
  buttonBlue: '#245186',
  peaGreen: '#8eb013',
  azure: '#2592ec',
  colorsFillLight2: 'rgba(232, 234, 236, 1.0)',
  // Colors we sampled from design but not named in Zeplin
  background: '#f3f4f5',
  linkBlue: 'rgb(82, 168, 237)',
  pinkishPurple: '#b666d7',
  buttonBorderDisabled: 'rgba(0, 0, 0, 0.2)',
  gray: '#808080',
  subGreyLight: '#e5e6e6',
  transparentBlack: 'rgba(0, 0, 0, 0.9)',
  transparentGray: 'rgba(0, 0, 0, 0.3)',
  subGreyTwo: 'rgba(124, 128, 132, 0.8)',
  textPlaceholder: 'rgba(124, 128, 132, 0.3)',
  loadingStateGray: 'rgba(210, 215, 219, 0.3)',
  paleGray: 'rgba(238, 239, 240, 1.0)',
  paleGray2: 'rgba(243, 243, 244, 1.0)',
  paleGray3: '#f8f9fb',
  duskBlue: 'rgba(36, 81, 134, 1.0)',
  cloudyBlue: 'rgba(182, 189, 195, 1.0)',
  dustyRed: 'rgba(216, 44, 68, 1.0)',
  veryLightBlue: '#e8eaec',
  fillDarkGrey: 'rgb(124, 128, 132)',
  chatSelected: 'rgb(182, 189, 195)',
  transparent: 'transparent',
  // Artichoke colors
  lightishPurple: '#b666d7',
  lightgrey: '#DEE1E4',
  bordergrey: 'rgba(0, 0, 0, 0.15)',
  greenselect: '#469a1e',
  dustyWhite: '#f2f2f2',
  lightblue: '#007aff',
  actionSheetDivider: 'rgba(182, 189, 195, 0.5)',
  lineLightBlue: 'rgba(182, 189, 195, 0.5)',
  axisLabelBlue: '#b3bac0',
  avatarBlue: '#080b27',
  avatarGreen: '#459a1e',
  avatarOrange: '#f6a92c',
  lightRed: 'rgba(216, 44, 68, 0.15)',
  rustyRed: '#D82C44',
  cyanBlue: '#B6BDC3',
  vividBlue: '#3399FF',
  gainsboro: '#DADDE1',
  scheduleDayComplete: 'rgba(70, 154, 30, 0.2)',
  missedWorkout: 'rgba(246, 170, 44, 0.2)',
  scheduledFutureWorkout: 'rgba(182, 189, 195, 0.2)',
  outerProgress: '#DADFE1',
  opacityDark: 'rgba(30, 30, 30, 0.85)',
  lightGrey: 'rgba(89, 89, 89, 89)',
  darkGrey: '#414242',
  inactiveWhite: '#AAb8b8b8',
  pickerOverlayBg: 'rgba(0, 0, 0, 0.4)',
  pickerBg: '#f7f7fa',
  overlayBackground: '#999999',
  darkOrange: '#ce8209',
  darkGreen: '#2c775c',
  darkPurple: '#812fa3',
  disclaimerGrey: '#ced3d7',
  tealBlue: '#3B7387',
  offWhite: '#f5f5f5',
  orange: '#f4a42c',
  brickOrange: '#fc7e1e',
  pink: '#e8354d',
  blue: '#2e94e9',
  mildGreen: '#a6d315',
  midGreen: '#4ecc58',
  mildGreenWithOpacity: '#f4f7e8',
  checkGreen: '#cbe4c2',
  mildYellowWithOpacity: '#f7f2e7',
  redinessLightGrey: '#CFD3D7',
  lightYellow: '#F4C528',
  eerieBlack: '#16161D',
  selectionColor: '#504a55',
  shadowColor: 'rgba(31, 33, 36, 0.25)',
  royalBlue: '#26359e',
  gray_1: '#F0F2F3',
  disabledGrey: '#DEDEDE',
};

// Function to get color scale for score based UI elements
export function getColorForScore(score) {
  if (!score || score <= 99) return colors.macaroniAndCheese;
  return colors.goodGreen;
}

export const headerTitleStyle = {
  fontFamily: 'Avenir-Heavy',
  fontSize: 15,
  fontWeight: '700',
  textAlign: 'center',
  color: colors.white,
  marginHorizontal: '5%',
};

const headerTitleContainerStyle = {
  width: '70%',
};

export const androidSafeAreaStyle = {
  paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
};

// Header Styles for React-Navigation headers
export const header = {
  default: {
    headerStyle: {
      backgroundColor: colors.duskBlue,
      borderBottomWidth: 0,
      shadowOffset: { height: 0, width: 0 },
    },
    headerTintColor: colors.lightBlue,
    headerTitleStyle,
    headerTitleContainerStyle,
    headerTitleAlign: 'center',
  },
  blackHeader: {
    headerStyle: {
      backgroundColor: colors.black,
      borderBottomWidth: 0,
      shadowOffset: { height: 0, width: 0 },
    },
    headerTintColor: colors.lightBlue,
    headerTitleStyle,
    headerTitleContainerStyle,
    headerTitleAlign: 'center',
  },
  noShadow: {
    headerStyle: {
      backgroundColor: colors.duskBlue,
      borderBottomWidth: 0,
      elevation: 0,
    },
    headerTintColor: colors.lightBlue,
    headerTitleStyle,
  },
  transparent: {
    headerTransparent: true,
    headerStyle: {
      backgroundColor: 'transparent',
      borderBottomWidth: 0,
      elevation: 0,
    },
    headerTintColor: colors.subGrey,
    headerTitleStyle: [headerTitleStyle, { color: colors.subGrey }],
  },
};

export const materialTabBarOptions = {
  tabBarOptions: {
    tabBarAllowFontScaling: false,
    tabBarActiveTintColor: colors.white,
    tabBarInactiveTintColor: '#8aa1bd',
    tabBarStyle: {
      backgroundColor: colors.duskBlue,
      elevation: 0,
    },
    tabBarIndicatorStyle: {
      backgroundColor: colors.medYellow,
      height: 5,
    },
    tabBarLabelStyle: {
      fontFamily: 'Avenir-Roman',
      fontSize: width <= 640 ? 16 : 18,
      textTransform: 'none',
    },
    swipeEnabled: false,
    lazy: true,
  },
  animationEnabled: false,
  defaultNavigationOptions: {
    tabBarOnPress: ({ defaultHandler }) => {
      Keyboard.dismiss();
      defaultHandler();
    },
  },
};

// Custom component styles
export const headline = StyleSheet.create({
  container: {
    justifyContent: 'flex-end',
    width: 338,
    height: 87,
  },
  text: {
    fontFamily: 'Avenir',
    fontSize: 46,
    fontWeight: '900',
    color: colors.nasmBlue,
  },
});

export const fonts = {
  h1: {
    fontSize: 20,
  },
  h2: {
    fontSize: 18,
  },
  h3: {
    fontSize: 16,
  },
  body: {
    fontSize: 14,
  },
};

export const shadow = {
  shadowColor: colors.shadowColor,
  shadowOffset: {
    width: 0,
    height: 3,
  },
  shadowRadius: 4,
  shadowOpacity: 1,
  elevation: 3,
};

export const textStyles = StyleSheet.create({
  heavySignup: {
    fontFamily: 'Avenir',
    fontSize: 46,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.white,
  },
  romanSignup: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    textAlign: 'left',
    color: colors.lightBlue,
  },
  heavyBlue: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.nasmBlue,
  },
  heavyGrayLa: {
    fontFamily: 'Avenir',
    fontSize: 24,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.subGrey,
  },
  heavyGreen: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.goodGreen,
  },
  heavyBlack: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    lineHeight: 16,
    textAlign: 'center',
    color: colors.black,
  },
  heavyWhite: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.white,
  },
  heavyButtonWhite: {
    fontFamily: 'Avenir',
    fontSize: 15,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.white,
  },
  heavyTextFieldSignup: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.white,
  },
  heavyGray: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.subGrey,
  },
  heavyTextFieldSignupFilled: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.white,
  },
  romanBlack: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    textAlign: 'left',
    color: colors.black,
  },
  romanWhite: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    textAlign: 'center',
    color: colors.white,
  },
  romanGreen: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10,
    textAlign: 'center',
    color: colors.goodGreen,
  },
  romanGray: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10,
    textAlign: 'center',
    color: colors.subGrey,
  },
  romanMa: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    lineHeight: 21,
    textAlign: 'center',
    color: colors.subGrey,
  },
  romanSignupGray: {
    fontFamily: 'Avenir-Roman',
    fontSize: 14,
    lineHeight: 21,
    textAlign: 'left',
    color: colors.subGrey,
  },
  navbar1Blue: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.nasmBlue,
  },
  heavyCross: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.subGrey,
  },
  navbar1White: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'left',
    color: colors.white,
  },
  navbar1Gray: {
    fontFamily: 'Avenir',
    fontSize: 12,
    fontWeight: '900',
    textAlign: 'center',
    color: colors.subGrey,
  },
  romanGrayLeftAlign: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    textAlign: 'left',
    color: colors.subGrey,
  },
  navbar2White: {
    fontFamily: 'Avenir-Roman',
    fontSize: 12,
    textAlign: 'left',
    color: colors.white,
  },
  romanGrayLa: {
    fontFamily: 'Avenir-Roman',
    fontSize: 10,
    textAlign: 'left',
    color: colors.subGrey,
  },
});
