/*  eslint-disable global-require */
import React from 'react';
import Moment from 'moment';
import {
  Platform,
  NativeModules,
  LayoutAnimation,
  Linking,
  I18nManager,
} from 'react-native';

// eslint-disable-next-line import/no-extraneous-dependencies
import { StreamChat } from 'stream-chat';

/// ARTICHOKE /////

import {
  BASE_URL,
  BASE_OAUTH_URL,
  AUTH_USER,
  AUTH_PASSWORD,
} from './apiConstants';

const buildEnv = NativeModules.ENVConfig.buildEnvironment;

export const AVATAR_BASE_URL = buildEnv === 'staging' || buildEnv === 'debug'
  ? 'https://nasmstaging.blob.core.windows.net'
  : 'https://nasmprod.blob.core.windows.net';

export const ROLES = {
  TRAINER: 'PRACTITIONER',
  CLIENT: 'ATHLETE',
};

export const USER_ROLES = {
  TRAINER: 'TRAINER',
  CLIENT: 'CLIENT',
};

export const CERT_CATEGORIES = {
  CERT: 'CERT',
  MASTER: 'MASTER',
  SPECIAL: 'SPECIAL',
};

export const EXERCISE_CATEGORIES = {
  INHIBIT: 'INHIBIT',
  LENGTHEN: 'LENGTHEN',
  ACTIVATE: 'ACTIVATE',
  INTEGRATE: 'INTEGRATE',
};

export const EXERCISE_FILTER_CATEGORIES = {
  BODY_REGION: 'bodyRegion',
  FITNESS_COMPONENTS: 'fitnessComponents',
  SOURCE: 'source',
};

export const EXERCISE_TYPE = {
  PROGRESSION: 'PROGRESSION',
  REGRESSION: 'REGRESSION',
};

export const CERT_ICONS = [
  {
    name: 'CNC',
    icon: require('./resources/credentialsCnc.png'),
    lockedIcon: require('./resources/credentialsCncOff.png'),
  },
  {
    name: 'CPT',
    icon: require('./resources/credentialsNasmCpt.png'),
    lockedIcon: require('./resources/credentialsNasmCptOff.png'),
  },
  {
    name: 'CES',
    icon: require('./resources/credentialsCes.png'),
    lockedIcon: require('./resources/credentialsCesOff.png'),
  },
  {
    name: 'PES',
    icon: require('./resources/credentialsPes.png'),
    lockedIcon: require('./resources/credentialsPesOff.png'),
  },
  {
    name: 'NASM MASTER TRAINER',
    icon: require('./resources/masterTrainerOn.png'),
    lockedIcon: require('./resources/masterTrainerOff.png'),
  },
  {
    name: 'NASM MASTER INSTRUCTOR',
    icon: require('./resources/masterInstructorOn.png'),
    lockedIcon: require('./resources/masterInstructorOff.png'),
  },
  {
    name: 'FNS',
    icon: require('./resources/credentialsFns.png'),
    lockedIcon: require('./resources/credentialsFnsOff.png'),
  },
  {
    name: 'BCS',
    icon: require('./resources/credentialsBcs.png'),
    lockedIcon: require('./resources/credentialsBcsOff.png'),
  },
  {
    name: 'WLS',
    icon: require('./resources/credentialsWls.png'),
    lockedIcon: require('./resources/credentialsWlsOff.png'),
  },
  {
    name: 'GPT',
    icon: require('./resources/credentialsGpt.png'),
    lockedIcon: require('./resources/credentialsGptOff.png'),
  },
  {
    name: 'WFS',
    icon: require('./resources/credentialsWfs.png'),
    lockedIcon: require('./resources/credentialsWfsOff.png'),
  },
  {
    name: 'YES',
    icon: require('./resources/credentialsYes.png'),
    lockedIcon: require('./resources/credentialsYesOff.png'),
  },
  {
    name: 'SFS',
    icon: require('./resources/credentialsSfs.png'),
    lockedIcon: require('./resources/credentialsSfsOff.png'),
  },
  {
    name: 'GFS',
    icon: require('./resources/credentialsGfs.png'),
    lockedIcon: require('./resources/credentialsGfsOff.png'),
  },
  {
    name: 'MMACS',
    icon: require('./resources/credentialsMma.png'),
    lockedIcon: require('./resources/credentialsMmaOff.png'),
  },
];

export const CERT_URLS = [
  {
    name: 'CNC',
    url: 'https://www.nasm.org/products/CNC301K',
  },
  {
    name: 'CPT',
    url: 'https://www.nasm.org/how-to-become-a-personal-trainer',
  },
  {
    name: 'CES',
    url: 'https://www.nasm.org/injury-prevention/why-corrective-exercise',
  },
  {
    name: 'PES',
    url: 'https://www.nasm.org/performance-training/sports-performance-training',
  },
  {
    name: 'NASM MASTER TRAINER',
    url: 'https://www.nasm.org/continuing-education/master-trainer',
  },
  {
    name: 'NASM MASTER INSTRUCTOR',
    url: 'https://www.nasm.org/continuing-education/master-instructors',
  },
  {
    name: 'FNS',
    url: 'https://www.nasm.org/products/FNS105K',
  },
  {
    name: 'BCS',
    url: 'https://www.nasm.org/products/CEU151K',
  },
  {
    name: 'WLS',
    url: 'https://www.nasm.org/products/CEU161K',
  },
  {
    name: 'GPT',
    url: 'https://www.nasm.org/products/CEU152K',
  },
  {
    name: 'WFS',
    url: 'https://www.nasm.org/products/CEU162K',
  },
  {
    name: 'YES',
    url: 'https://www.nasm.org/products/CEU142K',
  },
  {
    name: 'SFS',
    url: 'https://www.nasm.org/products/CEU140K',
  },
  {
    name: 'GFS',
    url: 'https://www.nasm.org/products/CEU139K',
  },
  {
    name: 'MMACS',
    url: 'https://www.nasm.org/products/CEU143K',
  },
];

export const CREATE_USER_FLOW = {
  CREATE: 'CREATE',
  INVITE: 'INVITE',
  ERROR: 'ERROR',
};

export function getAppStoreURLForPlatform(platform) {
  if (platform.toLowerCase() === 'android') {
    return 'https://play.google.com/store/apps/details?id=com.nasm.edge';
  }
  return 'https://itunes.apple.com/us/app/nasm-edge/id1353245169?mt=8';
}

export function getAvatarAppStoreURLForPlatform(platform) {
  if (platform.toLowerCase() === 'ios') {
    return 'https://apps.apple.com/in/app/avatar-nutrition/id1507989271';
  }
  return 'https://play.google.com/store/apps/details?id=com.avatarnutrition.avatarnutritionappb';
}

export const AvatarDeepLink = 'avatarnutrition://';

export function launchAvatar(platform) {
  Linking.canOpenURL(AvatarDeepLink).then((canOpen) => {
    if (canOpen) {
      Linking.openURL(AvatarDeepLink);
    } else {
      Linking.openURL(getAvatarAppStoreURLForPlatform(platform));
    }
  });
}

export const ProgramStatuses = {
  ACTIVE: 1,
  COMPLETED: 2,
};

export function capitalizeString(string) {
  if (!string) {
    return '';
  }
  return string.replace(
    /\b[\w']+\b/g,
    (text) => text.charAt(0).toUpperCase() + text.substr(1).toLowerCase(),
  );
}

export function isDateInThePast(date) {
  if (!date) {
    return false;
  }
  return new Moment().utc().isAfter(new Moment(date));
}

// As documented here: https://v1.reactnavigation.org/docs/screen-tracking.html
export function getCurrentRouteName(navigationState) {
  try {
    if (!navigationState) {
      return null;
    }
    const route = navigationState.routes[navigationState.index];
    // dive into nested navigators
    if (route.routes) {
      return getCurrentRouteName(route);
    }
    return route.routeName;
  } catch (error) {
    return null;
  }
}

export function isCurrentRouteNameEqualTo(routeName, navigationState) {
  if (!routeName || !navigationState) {
    return false;
  }
  return getCurrentRouteName(navigationState) === routeName;
}

// -------- In-App Purchase Related Constants -------- //

export const defaultPrice = '$19.99';
export const defaultProductTitle = 'Trainer Monthly Subscription';
export const defaultExamPrepPrice = '$16.99';

export const TrainerPlusMonthlyId = Platform.select({
  ios: 'subscription.trainer.monthly',
  android: 'subscription.trainer_plus.monthly',
});

export const TrainerPlusYearlyId = Platform.select({
  ios: 'subscription.trainer.yearly',
  android: 'subscription.trainer_plus.yearly',
});

export const TrainerProMonthlyId = 'subscription.trainer_pro.monthly';

export const TrainerProYearlyId = 'subscription.trainer_pro.yearly';

export const TrainerProSubscriptionIds = [
  TrainerProMonthlyId,
  TrainerProYearlyId,
];

export const SubscriptionItemSKUs = [
  TrainerPlusMonthlyId,
  TrainerPlusYearlyId,
  TrainerProMonthlyId,
  TrainerProYearlyId,
];

export const StudentSubscription = Platform.select({
  ios: ['cpt_exam_prep'],
  android: ['cpt_exam_prep'],
});
const TransactionIdentifierKey = Platform.select({
  ios: 'originalTransactionIdentifierIOS',
  android: 'purchaseToken',
});

// 3001 -> user cannot redeem this purchase
// 3004 -> user doesn't have a valid subscription
// 3005 -> user subscription has expired
// 3006 -> transaction id doesn't match with user
export const SubscriptionStatuses = {
  OK: 1111,
  INVALID: 3004,
  EXPIRED: 3005,
  UNAUTHORIZED: 3006,
  UNREGISTERED: 3007, // When a user receipt has failed to be uploaded for a brand new subscription
};

function filterPurchaseHistoryByProductIdentifiers(
  purchaseHistory,
  sortByTransactionDate = false,
) {
  const filteredArray = purchaseHistory.filter(
    (receipt) => SubscriptionItemSKUs.indexOf(receipt.productId) !== -1,
  );
  if (Platform.OS === 'android' || sortByTransactionDate) {
    filteredArray.sort(
      (purchase1, purchase2) => parseInt(purchase1.transactionDate, 10)
        < parseInt(purchase2.transactionDate, 10),
    );
  }
  return filteredArray;
}

export function getTransactionIdFromPurchaseHistory(purchaseHistory) {
  if (!purchaseHistory) {
    return null;
  }
  let originalTransactionId = null;
  const originalTransactionIdentifiers = filterPurchaseHistoryByProductIdentifiers(purchaseHistory).map(
    (receipt) => receipt[TransactionIdentifierKey],
  );
  if (originalTransactionIdentifiers.length) {
    // Because the original transaction id is going to be unique for a subscription from the same iTunes account.
    [originalTransactionId] = originalTransactionIdentifiers;
  }
  return originalTransactionId;
}

export function getExamPrepPurchase(purchaseHistory) {
  if (!purchaseHistory) {
    return null;
  }
  for (let i = 0; i < purchaseHistory.length; i += 1) {
    if (purchaseHistory[i].productId === 'cpt_exam_prep') {
      return purchaseHistory[i];
    }
  }
  return null;
}

export function getLatestPurchase(purchaseHistory) {
  if (!purchaseHistory) {
    return null;
  }
  return (
    filterPurchaseHistoryByProductIdentifiers(purchaseHistory, true)[0] || null
  );
}

export function getTransactionIdForFirebaseAnalytics(purchase) {
  if (!purchase) {
    return 'not_defined';
  }
  const transactionId = Platform.select({
    ios: purchase.transactionId || 'not_defined',
    android: purchase.transactionReceipt || 'not_defined',
  });
  return transactionId.substring(0, 100);
}

export function getPrice(product) {
  if (!product) {
    return defaultPrice;
  }
  return `${
    Platform.OS === 'ios'
      ? product.localizedPrice
      : product?.subscriptionOfferDetails[0]?.pricingPhases?.pricingPhaseList[0]
        ?.formattedPrice
  }`;
}

export function getProductTitle(product) {
  if (!product) {
    return defaultProductTitle;
  }
  const titleKey = Platform.select({
    ios: ['localizedTitle'],
    android: ['title'],
  });
  return this.state.products[0][titleKey];
}

export function getSubscriptionPeriod(product, defaultPeriod = 'month') {
  let subscriptionPeriod = defaultPeriod;
  if (product) {
    if (Platform.OS === 'android') {
      const period = product.subscriptionPeriodAndroid;
      switch (period) {
        case 'P1W':
          subscriptionPeriod = 'Week';
          break;
        case 'P1M':
          subscriptionPeriod = 'Month';
          break;
        case 'P3M':
          subscriptionPeriod = '3 Months';
          break;
        case 'P6M':
          subscriptionPeriod = '6 Months';
          break;
        case 'P1Y':
          subscriptionPeriod = 'Year';
          break;
        default:
          break;
      }
    } else if (Platform.OS === 'ios') {
      subscriptionPeriod = product.subscriptionPeriod || subscriptionPeriod;
    }
  }
  return subscriptionPeriod;
}

export function formatPurchaseForAndroid(purchase) {
  if (purchase.dataAndroid) {
    purchase.data = purchase.dataAndroid;
  }
  if (purchase.signatureAndroid) {
    purchase.signature = purchase.signatureAndroid;
  }
  if (purchase.autoRenewingAndroid) {
    purchase.autoRenewing = purchase.autoRenewingAndroid;
  }
  return purchase;
}

export function sortCorrectiveExercises(correctiveExercises) {
  if (!correctiveExercises || correctiveExercises.length === 0) {
    return [];
  }

  const inhibit = [];
  const lengthen = [];
  const activate = [];
  const integrate = [];

  correctiveExercises.map((section) => {
    Object.values(section).map((exercises) => {
      exercises.map((exercise) => {
        switch (exercise.exercise_category) {
          case EXERCISE_CATEGORIES.INHIBIT:
            inhibit.push(exercise);
            break;

          case EXERCISE_CATEGORIES.LENGTHEN:
            lengthen.push(exercise);
            break;

          case EXERCISE_CATEGORIES.ACTIVATE:
            activate.push(exercise);
            break;

          case EXERCISE_CATEGORIES.INTEGRATE:
            integrate.push(exercise);
            break;

          default:
            break;
        }
        return exercise;
      });
      return exercises;
    });
    return section;
  });

  return inhibit.concat(lengthen).concat(activate).concat(integrate);
}

export function calculateCalories(protein = 0, carbs = 0, fat = 0) {
  return protein * 4 + carbs * 4 + fat * 9;
}

export function debounce(func, wait, immediate) {
  let timeout;
  return function (...args) {
    const context = this;
    const later = function () {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(context, args);
  };
}

export async function passwordRecoveryUAURL() {
  let envUrl = '';
  switch (buildEnv) {
    case 'debug':
      envUrl = 'https://dev2-auth.nasm.org/reset_password?service=http://www.nasm.org';
      break;
    case 'staging':
      envUrl = 'https://dev2-auth.nasm.org/reset_password?service=http://www.nasm.org';
      break;
    case 'release':
      envUrl = 'https://auth.nasm.org/reset_password?service=http://www.nasm.org';
      break;
    default:
      envUrl = 'https://auth.nasm.org/reset_password?service=http://www.nasm.org';
      break;
  }
  return envUrl;
}

export const VARIABLE_DESCRIPTIONS = {
  tempo: {
    slow: 'Slow tempo reps can be performed for 6-7 seconds. For example, they can be performed 3/2/1 or 4/2/1.',
    medium:
      'Medium tempo reps are to be performed from 4-6 seconds i.e. 2/0/2.',
    fast: 'Fast tempo reps are to be performed from 2-4 seconds. Fast reps should also be performed under control.',
    explosive:
      'Explosive reps are performed as fast as possible or AFAP. Explosive reps should also be performed under control.',
  },
  rest_tempo: {
    slow: 'This is an active recovery in which the exerciser is still in motion. This may be used during the rest interval of any interval training program.',
    rest: 'This is a “complete” recovery period in which the exerciser is not required to move in between sets or exercises.',
    'n/a': 'N/A',
  },
  exercise_sides: {
    right_only:
      'The exerciser should perform reps only on the right side in a set.',
    left_only:
      'The exerciser should perform reps only on the left side in a set.',
    alternate:
      'The exerciser should alternate reps on each side for the duration of a set i.e. in a Step-Up exercise, one would step up with the right foot first and on the following rep step up with the left.',
    each_side:
      'The exerciser should perform all the reps of an exercise on one side and then immediately perform the same amount of reps on the opposite side.',
  },
};

let chatKey = '';
switch (buildEnv) {
  case 'debug':
    chatKey = '2m9gzx2nstyc';
    break;
  case 'staging':
    chatKey = '2m9gzx2nstyc';
    break;
  case 'release':
    chatKey = 'tebwm67d4kb9';
    break;
  default:
    chatKey = '2m9gzx2nstyc';
    break;
}

export const chatClient = StreamChat.getInstance(chatKey);

export const FEATURE_FLAGS = {
  CHAT_ENABLED: true,
  TRAINER_PRO_ENABLED: true,
  CPT7_ENABLED: true,
  NASM_CONNECTED_ENABLED: true,
  CLUB_CONNECT_MULTI_LOCATION_ENABLED: false,
};

export function getScreensFromRoutes(Stack, routes, params) {
  return Object.keys(routes).map((key) => {
    const screen = routes[key];
    return (
      <Stack.Screen
        key={key}
        name={key}
        component={screen}
        options={screen.navigationOptions}
        initialParams={params || null}
      />
    );
  });
}

export const DEVICE_LOCALE = Platform.OS === 'ios'
  ? NativeModules.SettingsManager.settings.AppleLocale
      || NativeModules.SettingsManager.settings.AppleLanguages[0]
  : I18nManager.localeIdentifier;

// This file exports constants used throughout the app
export const STORAGE_ITEMS = {
  AUTH_TOKEN: 'authToken',
};

export const API_AUTH = {
  AUTH_USER,
  AUTH_PASSWORD,
};

export const API_URLS = {
  BASE_URL,
  BASE_OAUTH_URL,
  OAUTH_TOKEN: 'ngapp/login',
  LOGIN: 'api/users/login',
  SERVICES: {
    LIST_ACTIVE: 'api/products?returnAll=true&active=true',
    LIST_BUYABLE: 'api/products/selfBookingProducts',
    LIST_ARCHIVED: 'api/products?returnAll=true&active=false',
    ARCHIVE_PRODUCT: 'api/products/',
    RESTORE_PRODUCT: 'api/products',
    GET_SERVICE: 'api/products/',
    UPLOAD_IMAGE: 'api/products/uploadServiceImage',
  },
  PACKAGES: {
    LIST_ACTIVE_PACKAGES: 'api/products/packages',
    LIST_BUYABLE_PACKAGES: 'api/products/onlinePackages',
    LIST_INACTIVE_PACKAGES: 'api/products/packagesInactive',
  },
  LOCATIONS: {
    AVAILABLE_LOCATIONS: 'api/selfBookings/selfBookingAddresses',
    SELF_BOOKINGS: 'api/selfBookings/selfBooking',
    SAVE_SELF_BOOKINGS: 'api/selfBookings/saveSelfBooking',
    SELF_BOOKINGS_USER: 'api/selfBookings/selfBookingUsers',
  },
  APPOINTMENTS: {
    SERVICE_APPOINTMENTS: 'api/appointments/byservice',
    BY_DATE: 'api/appointments/ondate?with_slot_blocker=true',
    BY_DATE_WITHOUT_SLOT_BLOCKER:
      'api/appointments/ondate?with_slot_blocker=false',
    BY_DATE_WITHOUT_SLOT_BLOCKER_SCHEDULES:
      'api/appointments/ondateSchedules?with_slot_blocker=false',
    BOOKED_BY_DATE: 'api/appointments?with_slot_blocker=true',
    SAVE_APPOINTMENT: 'api/appointments',
    OVERLAPS_APPOINTMENTS: 'api/appointments/overlaps',
    UPDATE_APPOINTMENT: 'api/appointments/',
    DELETE_APPOINTMENT_BY_CLIENT: 'api/appointments/client/',
    GET_APPOINTMENT: 'api/appointments/',
    SAVE_SLOTBLOCKER: 'api/appointments/slotblockers',
    GET_SLOTBLOCKER: 'api/appointments/slotblockers/',
    UPDATE_SLOTBLOCKER: 'api/appointments/slotblockers/',
    SAVE_APPOINTMENT_BY_CLIENT: 'api/selfBookings/clientBooking?accountId=',
    GET_AVAILABILITY: 'api/selfBookings/getAvailability?',
    CHECK_IN: 'api/appointments',
    UNCHECKIN: 'api/appointments',
    OVERLAPS_CLASS: 'api/appointments/schedule/overlaps',
    SAVE_CLASS: 'api/appointments/schedule',
    GET_CLASS: 'api/appointments/schedule/',
  },
  USER: {
    ACTIVE_USER: 'api/users/activeUser',
    ACCOUNT_SETTINGS: 'api/accounts',
    TIMEZONES: 'api/lookups/timezones',
    CANCEL_SUB: 'api/accounts/cancelpacksubscription',
  },
  CLIENTS: {
    ACTIVE_CLIENTS: 'api/clients?active=true',
    DISCOUNTS: 'api/clients/discounts',
    CLIENT_BALANCE: 'api/clients/',
    CLIENT_REPORTS: 'api/reports/clients/',
    BALANCES: '/balances?onlyUserId=0',
    HISTORY: '/history?lookback_days=3650&onlyUserId=0',
    PURCHASES: 'api/clients',
    BUY_PACKAGE: 'api/selfBookings/buyPackage',
    CLIENT_SUBSCRIPTIONS: 'api/reports/clients/',
    SUBSCRIPTIONS: '/subscriptions',
  },
  DASHBOARD: {
    SALES_TAX: 'api/dashboard/salesTax?',
    TRANSACTION_HISTORY: 'api/reports/account/history?event_type=PURCHASE',
  },
};

export const APPLICATION_ROUTES = {
  DASHBOARD: 'ServicesTab',
  MY_CLIENTS: 'My Clients',
  LIBRARIES: 'Libraries',
  LEARN_MORE: 'Learn More',
  SERVICES: 'Services',
  ARCHIVED_SERVICES: 'Archived Services',
  EDIT_SERVICES: 'Edit Service',
  EDIT_PACKAGE: 'Edit Package',
  LOCATIONS: 'Locations',
  QUICK_ENABLE_LOCATION: 'Quick Enable Locations',
  BOOKED_APPOINTMENTS: 'Booked',
  CREATE_SERVICE: 'Create Service',
  VIEW_SERVICE: 'View Service',
  VIEW_PACKAGE: 'View Package',
  HOURS_LOCATIONS: 'Hours',
  CLIENTS_LOCATION: "Client's Location",
  REMOTE_LOCATION: 'Remote',
  VIDEO_CALL_LOCATION: 'Video call',
  MY_LOCATIONS: 'My Location',
  CREATE_NEW_LOCATION: 'Create Location',
  CREATE_NEW_APPOINTMENT: 'Create Booking',
  APP_CREATE_SERVICE: 'Add a service',
  SELECT_LOCATION_SCREEN: 'Select Location',
  APP_CREATE_CLIENT: 'Add a client',
  CLIENTS_LIST: 'Clients List',
  RECURRENCE_SCREEN: 'Recurrence Screen',
  INDIVIDUAL_APPOINTMENT: 'Individual Appointment',
  APPOINTMENTS_DATE_AND_TIME: 'Select Time',
  EDIT_APPOINTMENT: 'Edit Appointment',
  LIST_CLIENT_BOOKINGS: 'Bookings',
  MULTIPLE_CHECK_IN_CLIENT: 'Check In',
  CREATE_NEW_SLOTBLOCKER: 'Create Slot Blocker',
  RECURRENCE_SCREEN_SLOT_BLOCKER: 'Recurrence Screen Slot Blocker',
  SLOTBLOCKER_DATE_AND_TIME: 'Select Time Slot Blockers',
  EDIT_SLOTBLOCKER: 'Edit Slotblocker',
  PAYMENTS: 'Payments',
  PACKAGE_PAYMENTS: 'Sell Package',
  DISCOUNTS: 'Select a discount',
  EDIT_DISCOUNTS: 'Edit discount',
  CREATE_NEW_CLASS: 'Create Class',
  CLASS_CREATE_SERVICE: 'Add class service',
  CLASS_RECURRENCE_SCREEN: 'Class Recurrence Screen',
  EDIT_CLASS: 'Edit Class',
  CREATE_PACKAGE: 'Create Package',
  VIEW_SERVICES_OFFERING: 'View Services Offering',
  VIEW_SERVICES_OFFERING_FOR_EDIT_PACKAGE:
    'View Services Offering For Edit Package',
  CONNECT_WITH_STRIPE: 'Connect with Stripe',
  STRIPE_CALLBACK: 'stripecallback',
  CLIENT_BOOKINGS: 'Client Bookings',
  ACCOUNT_CLIENTS: 'Account Clients',
  CLIENT_TRANSACTIONS: 'Client Transactions',
  CLIENT_SUBSCRIPTIONS: 'Subscriptions',
  TRANSACTION_DETAIL: 'Purchase Details',
  SUBSCRIPTION_DETAILS: 'Subscription Details',
  CANCEL_SUBSCRIPTION: 'Cancel Subscription',
  REFUND_TRANSACTION: 'Refund',
  REFUND_DETAILS: 'Refund Details',
  CHECKIN_DETAILS: 'Balance Details',
  VIEW_CLIENT_BOOKING: 'Client Booking',
  VIEW_BALANCE_SCREEN: 'View Balance',
  VIEW_BALANCE_ADJUSTMENT_SCREEN: 'Balance Adjustment',
  INCOME: 'Income',
  TIMEZONE_CURRENCY: 'Timezone & Currency',
  CURRENCY_SELECTION: 'Select Currency',
  ADD_CREDIT_CARD: 'Payment Method',
  TIMEZONE_SELECTION: 'Select Timezone',
  VIDEO_SCREEN: 'Video Screen',
};

export const APPLICATION_CLIENT_ROUTES = {
  CLIENT_DASHBOARD: 'Dashboard',
  SELECTED_APPOINTMENT: 'Selected Appointment',
  BOOK_SERVICE: 'Book Service',
  NEW_APPOINTMENT_DATE_AND_TIME: 'Select Time',
  NEW_APPOINTMENT_LOCATION: 'Select a Location',
  CLASS_VIEW: 'Class View',
  CLIENT_PACKAGE_VIEW: 'Package View',
  ADD_CREDIT_CARD: 'Payment Method',
  CLIENT_TRANSACTIONS: 'Transactions & Balances',
  CLIENT_SUBSCRIPTIONS: 'Subscriptions',
  CLIENT_VIDEO_SCREEN: 'Video Screen',
  SUBSCRIPTION_DETAILS: 'Subscription Details',
  CANCEL_CLIENT_SUBSCRIPTION: 'Cancel Subscription',
};

export const START_TIMES_OFFSETS = ['00', '15', '30', '45'];

export const FREQUENCY = [
  { label: 'Day', value: 'DAILY' },
  { label: 'Week', value: 'WEEKLY' },
  { label: 'Every Other Week', value: 'BIWEEKLY' },
  { label: 'Every Three Weeks', value: 'TRIWEEKLY' },
  { label: 'Every Four Weeks', value: 'FOURWEEKLY' },
  { label: 'Month ', value: 'MONTHLY' },
];

export const FREQUENCY_SHORT = [
  { label: 'Does not repeat', value: null },
  { label: 'Custom', value: 'Custom' },
];

export const DAYS = [
  { key: 'monday', label: 'Mon', value: 'monday' },
  { key: 'tuesday', label: 'Tues', value: 'tuesday' },
  { key: 'wednesday', label: 'Wed', value: 'wednesday' },
  { key: 'thursday', label: 'Thu', value: 'thursday' },
  { key: 'friday', label: 'Fri', value: 'friday' },
  { key: 'saturday', label: 'Sat', value: 'saturday' },
  { key: 'sunday', label: 'Sun', value: 'sunday' },
];

export const DAYS_SORTER = {
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6,
  sunday: 7,
};

export const HOURS = [
  { label: '12:00 am', value: '0:00am', key: '12:00am' },
  { label: '12:30 am', value: '0:30am', key: '12:30am' },
  { label: '1:00 am', value: '1:00am', key: '1:00am' },
  { label: '1:30 am', value: '1:30am', key: '1:30am' },
  { label: '2:00 am', value: '2:00am', key: '2:00am' },
  { label: '2:30 am', value: '2:30am', key: '2:30am' },
  { label: '3:00 am', value: '3:00am', key: '3:00am' },
  { label: '3:30 am', value: '3:30am', key: '3:30am' },
  { label: '4:00 am', value: '4:00am', key: '4:00am' },
  { label: '4:30 am', value: '4:30am', key: '4:30am' },
  { label: '5:00 am', value: '5:00am', key: '5:00am' },
  { label: '5:30 am', value: '5:30am', key: '5:30am' },
  { label: '6:00 am', value: '6:00am', key: '6:00am' },
  { label: '6:30 am', value: '6:30am', key: '6:30am' },
  { label: '7:00 am', value: '7:00am', key: '7:00am' },
  { label: '7:30 am', value: '7:30am', key: '7:30am' },
  { label: '8:00 am', value: '8:00am', key: '8:00am' },
  { label: '8:30 am', value: '8:30am', key: '8:30am' },
  { label: '9:00 am', value: '9:00am', key: '9:00am' },
  { label: '9:30 am', value: '9:30am', key: '9:30am' },
  { label: '10:00 am', value: '10:00am', key: '10:00am' },
  { label: '10:30 am', value: '10:30am', key: '10:30am' },
  { label: '11:00 am', value: '11:00am', key: '11:00am' },
  { label: '11:30 am', value: '11:30am', key: '11:30am' },
  { label: '12:00 pm', value: '12:00pm', key: '12:00pm' },
  { label: '12:30 pm', value: '12:30pm', key: '12:30pm' },
  { label: '1:00 pm', value: '1:00pm', key: '1:00pm' },
  { label: '1:30 pm', value: '1:30pm', key: '1:30pm' },
  { label: '2:00 pm', value: '2:00pm', key: '2:00pm' },
  { label: '2:30 pm', value: '2:30pm', key: '2:30pm' },
  { label: '3:00 pm', value: '3:00pm', key: '3:00pm' },
  { label: '3:30 pm', value: '3:30pm', key: '3:30pm' },
  { label: '4:00 pm', value: '4:00pm', key: '4:00pm' },
  { label: '4:30 pm', value: '4:30pm', key: '4:30pm' },
  { label: '5:00 pm', value: '5:00pm', key: '5:00pm' },
  { label: '5:30 pm', value: '5:30pm', key: '5:30pm' },
  { label: '6:00 pm', value: '6:00pm', key: '6:00pm' },
  { label: '6:30 pm', value: '6:30pm', key: '6:30pm' },
  { label: '7:00 pm', value: '7:00pm', key: '7:00pm' },
  { label: '7:30 pm', value: '7:30pm', key: '7:30pm' },
  { label: '8:00 pm', value: '8:00pm', key: '8:00pm' },
  { label: '8:30 pm', value: '8:30pm', key: '8:30pm' },
  { label: '9:00 pm', value: '9:00pm', key: '9:00pm' },
  { label: '9:30 pm', value: '9:30pm', key: '9:30pm' },
  { label: '10:00 pm', value: '10:00pm', key: '10:00pm' },
  { label: '10:30 pm', value: '10:30pm', key: '10:30pm' },
  { label: '11:00 pm', value: '11:00pm', key: '11:00pm' },
  { label: '11:30 pm', value: '11:30pm', key: '11:30pm' },
];

export const CURRENCY_COUNTRIES = [
  { label: 'United Kingdom' },
  { label: 'United States' },
];

export const CURRENCIES = [
  {
    value: 'CAD',
    key: 'CAD',
    label: 'CAD (Canadian Dollar)',
    country: 'Canada',
    symbol: 'C$',
  },
  {
    value: 'EUR',
    key: 'EUR',
    label: 'EUR (Euro)',
    country: 'United Kingdom',
    symbol: '€',
  },
  {
    value: 'GBP',
    key: 'GBP',
    label: 'GBP (British Pound)',
    country: 'United Kingdom',
    symbol: '£',
  },
  {
    value: 'DKK',
    key: 'DKK',
    label: 'DKK (Danish Krone)',
    country: 'United Kingdom',
    symbol: 'kr',
  },
  {
    value: 'NOK',
    key: 'NOK',
    label: 'NOK (Norwegian Krone)',
    country: 'United Kingdom',
    symbol: 'kr',
  },
  {
    value: 'SEK',
    key: 'SEK',
    label: 'SEK (Swedish Krona)',
    country: 'United Kingdom',
    symbol: 'kr',
  },
  {
    value: 'USD',
    key: 'USD',
    label: 'USD (United States Dollar)',
    country: 'United States',
    symbol: '$',
  },
];

export const discount_type_props = [
  { label: 'Percentage', value: 'PERCENTAGE' },
  { label: 'Amount', value: 'AMOUNT' },
];

export const PAY_METHODS = {
  DEBIT_OR_CC: 'DEBIT_OR_CC',
  CASH_OR_CHECK: 'CASH_OR_CHECK',
};

export const CLASS_REPEAT = [
  { label: 'Every week', value: 'week' },
  { label: 'Every other week', value: 'biweek' },
  { label: 'Every 3 weeks', value: 'triweek' },
  { label: 'Every 4 weeks', value: 'fourweek' },
];

export const ONESIGNAL_APP_ID = '************************************';

export const SALES_PERIODS = [
  { value: 'Month' },
  { value: 'Quarter' },
  { value: 'Year' },
];

export const OUTPUT_DEVICE = {
  BLUETOOTH: 'bluetooth',
  SPEAKER: 'speaker',
};

export const androidSafeLayoutAnimation = {
  duration: 200,
  create: {
    type: LayoutAnimation.Types.easeInEaseOut,
    property: LayoutAnimation.Properties.opacity,
  },
  update: {
    type: LayoutAnimation.Types.easeInEaseOut,
  },
};

export const EXERCISE_CONTEXTS = {
  NASM_EXERCISE: 1,
  WORKOUT_SECTION_EXERCISE: 2,
  SCHEDULED_EXERCISE: 3,
};
