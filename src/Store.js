import {
  combineReducers,
  configureStore,
  getDefaultMiddleware,
} from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';

import rootReducer from './reducers';
import artichokeReducers from './reducers/artichoke';
import rootSaga from './sagas';

export default function configureAppStore() {
  const sagaMiddleware = createSagaMiddleware();

  const middleware = [sagaMiddleware].concat(getDefaultMiddleware());

  if (__DEV__) {
    const createDebugger = require('redux-flipper').default;
    middleware.push(createDebugger());
  }

  const store = configureStore({
    reducer: combineReducers({ ...rootReducer, ...artichokeReducers }),
    middleware,
  });

  sagaMiddleware.run(rootSaga);

  if (process.env.NODE_ENV !== 'production' && module.hot) {
    module.hot.accept('./reducers', () => store.replaceReducer(rootReducer));
  }

  return store;
}
