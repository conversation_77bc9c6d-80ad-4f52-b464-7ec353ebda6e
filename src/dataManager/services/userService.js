import AsyncStorage from '@react-native-async-storage/async-storage';
import nasm, { deleteAccessToken } from '../apiConfig';
import { CREATE_USER_FLOW, FEATURE_FLAGS } from '../../constants';
import { API_VERSION } from '../../api/nasm_sdk/constants';

// ==============================================================================
// User Functions
const LOGIN_PREFERENCES = 'LOGIN_PREFERENCES';
const DeviceInfo = require('react-native-device-info');

export async function relogin(version = API_VERSION) {
  return nasm.api.getMyUser(version);
}

/**
 * Log a user in with email and password
 * @param {Object} user - User login credentials
 * @param user.email {String} Email of user to login
 * @param user.password {String} Password of user to login
 */
export async function login({ email, password }, version) {
  const loginPayload = {
    username: email,
    password,
  };
  try {
    await nasm.auth.login(loginPayload, version);
  } catch (error) {
    if (error.response && error.response.status === 403) {
      error.message = 'Incorrect username or password';
    }
    throw error;
  }
  return relogin(version);
}

export async function logout() {
  return deleteAccessToken();
}

export function updateLoginPreferences(rememberMe = false, email = null) {
  let preferences = [];
  if (rememberMe && email) {
    preferences = { rememberMe, email };
  }
  return AsyncStorage.setItem(LOGIN_PREFERENCES, JSON.stringify(preferences));
}

export async function getLoginPreferences() {
  return AsyncStorage.getItem(LOGIN_PREFERENCES)
    .then((preferencesString) => {
      if (preferencesString !== null) {
        return JSON.parse(preferencesString);
      }
      return { rememberMe: false, email: null };
    })
    .catch(() => ({ rememberMe: false, email: null }));
}

export async function validateEmail(email, club_id, location_id) {
  const result = {};
  try {
    const emailObj = {
      email,
      club_id,
    };
    if (FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED && location_id) {
      emailObj.location_id = location_id;
    }
    await nasm.api.trainerValidateClientEmail(emailObj);
    result.status = CREATE_USER_FLOW.CREATE;
  } catch (error) {
    if (error.code === 4005) {
      throw error;
    }

    result.message = error.message;
    if (error.code === 2001) {
      result.status = CREATE_USER_FLOW.INVITE;
    } else {
      result.status = CREATE_USER_FLOW.ERROR;
    }
  }

  return result;
}

export async function forgotPassword(email) {
  const result = await nasm.api.createPasswordResetRequest(email);
  return result.success;
}

export function checkAppVersion() {
  const tempVersionData = {
    currentVersion: DeviceInfo.getVersion(),
  };
  return nasm.api.getAppVersion(tempVersionData);
}

export async function hasToken() {
  try {
    const tokenInfo = await nasm.api.manageAuthentication();
    if (tokenInfo) {
      return true;
    }
  } catch (error) {
    return false;
  }

  return false;
}
