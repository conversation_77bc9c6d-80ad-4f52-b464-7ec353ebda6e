import nasm from '../apiConfig';

export function scheduleOrRescheduleProgram(
  program,
  isNew,
  name,
  clientId,
  fromDate,
  toDate,
) {
  const programBody = {};
  programBody.name = name;
  programBody.nasmProgramId = isNew ? program.id : program.nasm_program_id;
  programBody.startDate = fromDate;
  programBody.endDate = toDate;
  programBody.workouts = program.workouts;
  if (isNew) {
    return nasm.api.scheduleClientProgram(clientId, programBody);
  }
  programBody.id = program.id;
  return nasm.api.rescheduleClientProgram(clientId, program.id, programBody);
}
