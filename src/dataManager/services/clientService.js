import { FEATURE_FLAGS } from '../../constants';
import nasm from '../apiConfig';

// ==============================================================================
// Client Functions

export async function getUserById(id) {
  const userRes = await nasm.api.getUserById(id);
  const user = userRes.result;
  return user;
}

export async function createNewClient(
  firstName,
  lastName,
  email,
  club_id,
  location_id,
) {
  const newClient = {
    first_name: firstName,
    last_name: lastName,
    email,
    club_id,
  };

  if (FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED && location_id) {
    newClient.location_id = location_id;
  }

  return nasm.api.createClientUser(newClient);
}

export async function getAllGoals() {
  const allGoalsRes = await nasm.api.getAllGoals();
  const allGoals = allGoalsRes.result;
  return allGoals;
}

export async function assignClientGoal(userId, goalIds) {
  const goalData = { userId, goals: goalIds };
  return nasm.api.assignGoalToClient(goalData);
}

export async function deleteClientGoal(id, goalIds) {
  const goalData = { id, goalIds };
  return nasm.api.deleteClientGoals(goalData);
}

export async function getAllOHSAResults(userId) {
  const ohsaResultsRes = await nasm.api.getAllOHSAResults(userId);
  ohsaResultsRes.map((result) => result);
}

export async function getAssessmentResult(userId) {
  const ohsaResultsRes = await nasm.api.getAssessmentResult(userId);
  return ohsaResultsRes.result;
}

export async function getAllMeResults(userId) {
  const meResultsRes = await nasm.api.getAllMeResults(userId);
  meResultsRes.map((result) => result);
}
