import * as IAP from 'react-native-iap';
import moment from 'moment';
import { Platform } from 'react-native';
import {
  getTransactionIdFromPurchaseHistory,
  SubscriptionStatuses,
} from '../../constants';
import nasm from '../apiConfig';

// ==============================================================================
// Trainer Functions

export async function getTrainer(id) {
  const { result: trainer } = await nasm.api.getUserById(id);
  return trainer;
}

export async function uploadReceipt(receipt) {
  if (!receipt) {
    return Promise.reject(new Error('receipt is undefined.'));
  }
  return nasm.api.uploadReceipt(receipt);
}

// helper
export function isSubscriptionStatusCode(code) {
  if (code) {
    return code >= 3002 && code <= 3007;
  }
  return false;
}

function checkSubscriptionWithTransactionId() {
  // Ask IAP for receipt ID and try checking subscription again.
  return IAP.initConnection().then(() => IAP.getPurchaseHistory().then((purchaseHistory) => {
    const originalTransactionId = getTransactionIdFromPurchaseHistory(
      purchaseHistory,
    );
    if (!originalTransactionId) {
      return Promise.resolve({
        status: SubscriptionStatuses.INVALID,
      });
    }
    return nasm.api
      .checkSubscription(originalTransactionId)
      .then(() => {
        if (Platform.OS === 'android') {
          IAP.endConnection();
        }
        return Promise.resolve({
          status: SubscriptionStatuses.OK,
        });
      })
      .catch((error) => {
        if (Platform.OS === 'android') {
          IAP.endConnection();
        }
        if (error.code) {
          const statusCode = error.code;
          if (isSubscriptionStatusCode(statusCode)) {
            return Promise.resolve({
              status: statusCode,
              message: error.message,
            });
          }
        }
        return Promise.reject(error);
      });
  }));
}

export async function checkSubscription(user) {
  if (user.bypass_subscription) {
    // user is excempt from purchases
    return Promise.resolve({
      status: SubscriptionStatuses.OK,
    });
  }

  const expirationDate = user.subscription_expiration_date;
  if (expirationDate) {
    if (moment().isSameOrBefore(expirationDate)) {
      // user has valid subscription on database
      return Promise.resolve({
        status: SubscriptionStatuses.OK,
      });
    }
    // user has expired subscription on database
    // call check enpoint to make sure we are up to date
    return nasm.api
      .checkSubscription()
      .then(() => Promise.resolve({
        status: SubscriptionStatuses.OK,
      }))
      .catch((error) => {
        if (error.code) {
          const statusCode = error.code;
          if (statusCode === SubscriptionStatuses.INVALID) {
            // Query IAP store and then hit the check subscription endpoint again
            return checkSubscriptionWithTransactionId();
          }
          if (statusCode === SubscriptionStatuses.EXPIRED) {
            return Promise.resolve({
              status: statusCode,
              message: error.message,
            });
          }
        }
        return Promise.reject(error);
      });
  }
  // user has never had a subscription
  return Promise.resolve({
    status: SubscriptionStatuses.UNREGISTERED,
    message: 'User has not purchased a subscription',
  });
}
