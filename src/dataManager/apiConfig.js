import AsyncStorage from '@react-native-async-storage/async-storage';
import Nasm from '../api/nasm_sdk';

const AUTH_TOKEN = 'AUTH_TOKEN';

async function saveToken(tokenData) {
  if (tokenData && typeof tokenData === 'object') {
    return AsyncStorage.setItem(AUTH_TOKEN, JSON.stringify(tokenData))
      .then(() => true)
      .catch(() => false);
  }
  return false;
}

async function getToken() {
  return AsyncStorage.getItem(AUTH_TOKEN)
    .then((tokenString) => {
      if (tokenString !== null) {
        return JSON.parse(tokenString);
      }
      return false;
    })
    .catch(() => false);
}

export async function deleteAccessToken() {
  return AsyncStorage.removeItem(AUTH_TOKEN)
    .then(() => true)
    .catch(() => false);
}

// Create api instance
export default new Nasm(saveToken, getToken, deleteAccessToken);
