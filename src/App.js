import { GestureHandlerRootView } from 'react-native-gesture-handler';
import React, { Component } from 'react';
import {
  Platform,
  UIManager,
  AppState,
  NativeModules,
  LogBox,
  StyleSheet,
  View,
  Text,
  Image,
} from 'react-native';
import { Provider } from 'react-redux';
import Jai<PERSON><PERSON><PERSON><PERSON> from 'jail-monkey';
import SplashScreen from 'react-native-splash-screen';

import { MenuContext } from 'react-native-menu';
import Orientation from 'react-native-orientation-locker';
import * as Sentry from '@sentry/react-native';

import { enableScreens } from 'react-native-screens';
import IconFeather from 'react-native-vector-icons/Feather';
import IconMaterial from 'react-native-vector-icons/MaterialIcons';
import IconFontAwesome from 'react-native-vector-icons/FontAwesome';
import IconIoni from 'react-native-vector-icons/Ionicons';
import IconFoundation from 'react-native-vector-icons/Foundation';
import IconMaterialCommunity from 'react-native-vector-icons/MaterialCommunityIcons';
import { initStripe } from '@stripe/stripe-react-native';
import moment from 'moment';
import {
  requestTrackingPermission,
  getTrackingStatus,
} from 'react-native-tracking-transparency';
import * as PurchaseManager from './util/PurchaseManager';
import Router from './Router';
import configureAppStore from './Store';
import { STRIPE_PUBLISHABLE_KEY } from './apiConstants';

import { track, flushMixpanel } from './util/Analytics';
import { FEATURE_FLAGS } from './constants';
import { colors } from './styles';
import { curvedScale } from './util/responsive';

const logo = require('../assets/logo.png');

const buildEnv = NativeModules.ENVConfig.buildEnvironment;
const rootedMessage = Platform.OS === 'android'
  ? 'We do not support rooted devices. Please unroot your device to start using our app.'
  : 'We do not support jailbroken devices. Please switch back to stock iOS to start using our app.';

if (FEATURE_FLAGS.TRAINER_PRO_ENABLED) {
  initStripe({
    publishableKey: STRIPE_PUBLISHABLE_KEY,
  });
}

enableScreens();

// constants
const store = configureAppStore();

// TODO: debug this yellow box error, may be related to RN update
// ignoring "Accessing the 'state' property..." because it is necessary to
// manage navigation headers based on the state of child screens
LogBox.ignoreLogs([
  'Could not locate shadow view',
  'Warning: isMounted(...) is deprecated',
  "Accessing the 'state' property of the 'route' object is not supported.",
]);

IconFeather.loadFont();
IconMaterial.loadFont();
IconFontAwesome.loadFont();
IconIoni.loadFont();
IconFoundation.loadFont();
IconMaterialCommunity.loadFont();

export default class App extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startSessionTime: null,
    };
  }

  componentDidMount() {
    track('app_launch');
    track('session_has_started');
    this.setState({
      startSessionTime: moment(),
    });
    flushMixpanel();
    // initialize sentry for crash reporting
    Sentry.init({
      dsn: 'https://<EMAIL>/4403249',
      environment: buildEnv,
      enableAutoSessionTracking: true,
    });
    // Enable LayoutAnimation api for Android
    if (
      Platform.OS === 'android'
      && UIManager.setLayoutAnimationEnabledExperimental
    ) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
    Orientation.lockToPortrait();
    PurchaseManager.setPurchaseUpdateSubscriptionAndroid((purchase) => {
      if (purchase.productId) {
        PurchaseManager.finishTransaction(purchase, false);
      }
    }).then((subscription) => {
      this.purchaseUpdateSubscriptionAndroid = subscription;
    });
    AppState.addEventListener('change', this.handleAppStateChange);
  }

  componentWillUnmount() {
    if (this.purchaseUpdateSubscriptionAndroid) {
      this.purchaseUpdateSubscriptionAndroid.remove();
      this.purchaseUpdateSubscriptionAndroid = null;
    }
    AppState.removeEventListener('change', this.handleAppStateChange);
  }

  handleAppStateChange = async (nextAppState) => {
    if (nextAppState === 'active') {
      this.handleTrackingPermission();
      await track('session_has_started');
      this.setState({
        startSessionTime: moment(),
      });
    } else if (nextAppState === 'background') {
      await track('session_has_stopped');
      const sessionDuration = moment().diff(
        this.state.startSessionTime,
        'seconds',
      );
      await track('session_duration', { duration: sessionDuration });
      this.setState({
        startSessionTime: moment(),
      });
    }
  };

  handleTrackingPermission = async () => {
    const trackingStatus = await getTrackingStatus();
    if (trackingStatus !== 'authorized' && trackingStatus !== 'unavailable') {
      requestTrackingPermission();
    }
  };

  isDeviceRooted = () => {
    if (JailMonkey.isJailBroken()) {
      SplashScreen.hide();
      return true;
    }
    return false;
  };

  render() {
    if (this.isDeviceRooted() && buildEnv === 'release') {
      return (
        <View style={styles.container}>
          <Image source={logo} />
          <Text style={styles.message}>{rootedMessage}</Text>
        </View>
      );
    }
    return (
      <Provider store={store}>
        <MenuContext style={{ flex: 1 }}>
          <GestureHandlerRootView style={{ flex: 1 }}>
            <Router />
          </GestureHandlerRootView>
        </MenuContext>
      </Provider>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.nasmBlue,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: curvedScale(50),
  },
  message: {
    fontSize: curvedScale(14),
    color: colors.white,
    textAlign: 'center',
    letterSpacing: 1,
    lineHeight: 25,
    marginTop: curvedScale(30),
  },
});
