import moment from 'moment';

// Take a number and format it to have commas
export const format_thousands = (number, includeDecimals) => {
  const roundedNumber = includeDecimals
    ? number.toFixed(2)
    : Math.round(number).toString();

  return roundedNumber.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
};

export const calculate_BMR = (fieldValues, adjustRate) => {
  const {
    sex, weight, height, age, currentActivityLevel,
  } = fieldValues;
  if (sex && weight && weight > 0 && height && height > 0 && age && age > 10) {
    // BMR = 9.99 * weight (kg) + 6.25 * height (cm) - 4.92 * age (years) + sexFactor (kcal / day)
    // sexFactor is +5 for males and -161 for females

    let sexFactor = 5;
    if (sex?.toLowerCase() === 'female') {
      sexFactor = -161;
    }

    let result = 9.99 * parseInt(weight, 10)
      + 6.25 * parseInt(height, 10)
      - 4.92 * parseInt(age, 10)
      + sexFactor;

    // If the BMR is being adjusted, include the corresponding activity level rate
    if (adjustRate === true) {
      result *= currentActivityLevel;
    }

    return result;
  }

  return 0;
};

/**
 * Calculate the adjusted caloric requirement for the given end goal
 * @param currentTDEE
 * @param currentWeight
 * @param endGoalWeight
 * @param days
 */
export const calculate_adjusted_caloric_requirement = (
  currentTDEE,
  currentWeight,
  endGoalWeight,
  days,
) => {
  // Adjusted Daily Caloric Requirement = Current TDEE - [(current weight - end goal weight) * 3500) / days]
  const adjustment = ((currentWeight - endGoalWeight) * 3500) / days;
  return currentTDEE - adjustment;
};

/**
 * Calculate macro allocations using the percent, adjusted daily caloric requirement, and calories per gram.
 * Returns an object containing the allocated calories and grams
 * @param percent
 * @param adjusted_daily_caloric_requirement
 * @param calories_per_gram
 */
export const calculate_macro_allocations = (
  percent,
  adjusted_daily_caloric_requirement,
  calories_per_gram,
) => {
  // If percent is not already in decimal format, convert it now
  // Assuming there will never be 100% allocation to a specific macro
  let calculatedPercent = percent;
  if (percent >= 1) {
    calculatedPercent = percent / 100;
  }

  // Grams per macro = Percent * Adjusted Daily Caloric Requirement * g/cal conversion
  const calories = calculatedPercent * adjusted_daily_caloric_requirement;
  const grams = calories * (1 / calories_per_gram);
  return {
    calories,
    grams,
  };
};

/**
 * Calculate macro allocations percent, using the grams, adjusted daily caloric requirement, and calories per gram.
 * Returns calculated percentage according to input values
 * @param grams
 * @param adjusted_daily_caloric_requirement
 * @param calories_per_gram
 */
export const calculate_macro_percentage = (
  grams,
  adjusted_daily_caloric_requirement,
  calories_per_gram,
) => {
  const calories = grams * calories_per_gram;
  const percentage = (calories / adjusted_daily_caloric_requirement) * 100;
  return Math.round(percentage);
};

/**
 * Find date that is the specified number of days from today
 * @param number_of_days
 */
export const calculate_date_from_today = (number_of_days) => moment()
  .add(+number_of_days, 'days')
  .toDate();

/**
 * Find the nearest value equal to or greater than value in an array of possible values
 * @param value
 * @param possible_values
 */
export const calculate_nearest_to_value = (value, possible_values) => possible_values.find((element) => element >= value);

/** *** WEIGHT **** */
// Take kilograms weight and converts it to pounds
export const convert_from_kilograms = (weight) => (weight * 2.20462262).toFixed(1);

// Takes pounds weight and converts it to kilograms
export const convert_to_kilograms = (weight) => (weight * 0.45359237).toFixed(2);

/** *** HEIGHT **** */
const INCHES_PER_FEET = 12;
const CM_PER_INCH = 2.54;

// Take centimeters and converts it into an object containing feet, inches, and total inches
export const get_height_from_centimeters = (height = 0) => {
  const totalInches = height / CM_PER_INCH;
  const feet = Math.floor(totalInches / INCHES_PER_FEET);
  const inches = Math.round(totalInches) % INCHES_PER_FEET;
  return {
    feet,
    inches,
    totalInches,
  };
};

export const get_height_from_inches = (height) => {
  const feet = Math.floor(height / INCHES_PER_FEET);
  const inches = Math.round(height) % INCHES_PER_FEET;
  return {
    feet,
    inches,
    totalInches: height,
  };
};

export const feetAndInchesToInches = (height_ft, height_in) => {
  const totalInches = +height_ft * INCHES_PER_FEET + +height_in;
  return totalInches;
};

// Take feet and inches and convert it to centimeters
export const convert_height_to_centimeters = (inches) => (inches * CM_PER_INCH).toFixed(2);

export const convert_height_to_inches = (centimeters) => {
  const totalInches = centimeters / CM_PER_INCH;
  return totalInches.toFixed(2);
};

export const convert_height_to_centimeters_from_inches = (inches = 0) => (inches * CM_PER_INCH).toFixed(2);

export function getKeyByValue(object, value) {
  return Object.keys(object).find((key) => object[key] === value);
}
