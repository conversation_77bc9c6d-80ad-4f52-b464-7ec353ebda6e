import { Platform, Alert } from 'react-native';
import {
  initConnection as initIAPConnection,
  endConnection as endIAPConnection,
  getProducts as getIAPProducts,
  getSubscriptions as getIAPSubscriptions,
  getPurchaseHistory as getIAPPurchaseHistory,
  finishTransaction as finishIAPTransaction,
  requestPurchase,
  requestSubscription,
  clearTransactionIOS,
  getAvailablePurchases,
  purchaseUpdatedListener,
  acknowledgePurchaseAndroid,
  flushFailedPurchasesCachedAsPendingAndroid,
} from 'react-native-iap';
import moment from 'moment';
import { getLatestPurchase } from '../constants';

export const defaultFreeTrialPeriod = Platform.OS === 'ios' ? '1 week' : '7 day';

export async function initConnection() {
  // According to their Github page, we need to call initConnection() for
  // purchases to work Android. This method does nothing on iOS.
  if (Platform.OS === 'android') {
    return initIAPConnection().then(async (conn) => {
      if (conn) {
        await flushFailedPurchasesCachedAsPendingAndroid();
      }
      return conn;
    });
  }
  return true;
}

export function endConnection() {
  // endConnection() needs to be called only for Android.
  // Calling this method wouldn't have any effect on iOS.
  if (Platform.OS === 'android') {
    endIAPConnection();
  }
}

export function getProducts(productIds) {
  return getIAPProducts({ skus: productIds });
}
export function getSubscriptions(productIds) {
  return getIAPSubscriptions({ skus: productIds });
}

export function getFreeTrialPeriod(products) {
  if (Platform.OS === 'ios') {
    if (
      products != null
      && products.length > 0
      && products[0].freeTrialPeriod
      && products[0].freeTrialPeriod !== ''
    ) {
      return products[0].freeTrialPeriod;
    }
  } else if (products != null && products.length > 0) {
    return `${moment.duration(products[0].freeTrialPeriodAndroid).days()} day`;
  }
  return this.defaultFreeTrialPeriod;
}

async function getCurrentAndroidSubscriptionToken() {
  const purchases = await getAvailablePurchases();
  if (purchases.length !== 0) {
    const latestPurchase = getLatestPurchase(purchases);
    if (latestPurchase) {
      return latestPurchase.purchaseToken;
    }
  }
  return undefined;
}

async function getOfferTokenForAndroid(subscriptionId) {
  let offerToken = '';
  try {
    if (Platform.OS === 'android') {
      const subscriptions = await getSubscriptions([subscriptionId]);
      if (subscriptions?.length) {
        const selectedSubscription = subscriptions.find(
          (item) => item.productId === subscriptionId,
        );
        if (
          selectedSubscription
          && selectedSubscription.subscriptionOfferDetails?.length
        ) {
          offerToken = selectedSubscription?.subscriptionOfferDetails[0]?.offerToken;
        }
      }
    }
  } catch (error) {
    offerToken = '';
  }
  return offerToken;
}

export async function purchaseProduct(subscriptionId, isProduct) {
  if (isProduct) {
    let purchaseRequest = {
      sku: subscriptionId,
      andDangerouslyFinishTransactionAutomaticallyIOS: false,
    };
    if (Platform.OS === 'android') {
      purchaseRequest = {
        skus: [subscriptionId],
      };
    }
    const purchase = await requestPurchase(purchaseRequest);
    if (purchase) {
      if (Platform.OS === 'android' && purchase[0]) {
        return purchase[0];
      }
      return purchase;
    }
    return new Error('Failed');
  }
  const currentToken = await getCurrentAndroidSubscriptionToken();
  const offerToken = await getOfferTokenForAndroid(subscriptionId);
  const subscriptionRequest = {
    sku: subscriptionId,
    andDangerouslyFinishTransactionAutomaticallyIOS: false,
    subscriptionOffers: [{ sku: subscriptionId, offerToken }],
  };
  if (currentToken) {
    subscriptionRequest.purchaseTokenAndroid = currentToken;
  }
  if (Platform.OS === 'ios') {
    await clearTransactionIOS();
  }
  const purchase = await requestSubscription(subscriptionRequest);
  if (purchase) {
    if (Platform.OS === 'android' && purchase[0]) {
      return purchase[0];
    }
    return purchase;
  }
  return new Error('Something went wrong! Please try again later.');
}

export async function restorePurchases() {
  return getAvailablePurchases()
    .then((purchases) => {
      const purchase = purchases[0];
      return purchase;
    })
    .catch((err) => {
      if (
        err.code === 'E_RECEIPT_FINISHED_FAILED'
        || err.code === 'E_UNKNOWN'
      ) {
        return;
      }
      Alert.alert(err?.message ?? err ?? '');
    });
}

export function getPurchaseHistory() {
  return getIAPPurchaseHistory();
}

export async function finishTransaction(purchase, consumable) {
  if (Platform.OS === 'android') {
    await initConnection();
    const currentPurchase = JSON.parse(purchase.transactionReceipt);
    const token = currentPurchase?.purchaseToken;
    const developerPayload = purchase?.developerPayloadAndroid;
    if (token) {
      await acknowledgePurchaseAndroid({
        token,
        developerPayload,
      })
        .then(async () => {
          handleFinishTransaction({ purchase, consumable, isAndroid: true });
        })
        .catch((error) => error);
    } else {
      throw new Error('Something went wrong! Please try again later.');
    }
  } else {
    handleFinishTransaction({ purchase, consumable });
  }
}

const handleFinishTransaction = async ({ purchase, consumable, isAndroid }) => {
  const finishPayload = {
    purchase,
    isConsumable: consumable,
  };
  if (isAndroid) {
    finishPayload.developerPayloadAndroid = purchase.developerPayloadAndroid;
  }
  await finishIAPTransaction(finishPayload);
};

export async function setPurchaseUpdateSubscriptionAndroid(
  purchaseHandler = () => {},
) {
  if (Platform.OS === 'android') {
    await initConnection();
    return purchaseUpdatedListener(purchaseHandler);
  }
  return null;
}
