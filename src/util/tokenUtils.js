import AsyncStorage from '@react-native-async-storage/async-storage';

export const AUTH_TOKEN = 'AUTH_TOKEN';

export async function saveAccessToken(tokenData) {
  if (tokenData && typeof tokenData === 'object') {
    return AsyncStorage.setItem(AUTH_TOKEN, JSON.stringify(tokenData))
      .then(() => true)
      .catch(() => false);
  }
  return false;
}

export async function getAccessToken() {
  return AsyncStorage.getItem(AUTH_TOKEN)
    .then((tokenString) => {
      if (tokenString !== null) {
        return JSON.parse(tokenString);
      }
      return false;
    })
    .catch(() => false);
}

export async function deleteAccessToken() {
  return AsyncStorage.removeItem(AUTH_TOKEN)
    .then(() => true)
    .catch(() => false);
}
