import * as axios from 'axios';
import { BASE_URL } from '../apiConstants';

const request = axios.create({ baseURL: BASE_URL, timeout: 60000 });

export default request;
//
// export const setAuthorizationHeaders = (instance: any, token: string) => {
//   instance.defaults.headers.common.Authorization = `Bearer ${token}`;
// };
//
// export const setCookieHeaders = (instance: any, cookie: string) => {
//   instance.defaults.headers.common.Cookie = cookie;
// };
//
// export const setLanguageHeaders = (instance: any, lang: string) => {
//   instance.defaults.headers.common['Accept-Language'] = lang;
// };
//
// export const removeAuthorizationHeaderForInstance = (instance: any) => {
//   delete instance.defaults.headers.common.Authorization;
// };
