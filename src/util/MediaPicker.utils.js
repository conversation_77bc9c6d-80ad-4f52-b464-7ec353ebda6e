/* eslint-disable consistent-return */
/* eslint-disable import/prefer-default-export */
/* eslint-disable no-console */
/* eslint-disable react-native/split-platform-components */
import ImagePicker from 'react-native-image-crop-picker';
import { Alert, Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import RNPermissions from 'react-native-permissions';

export const MEDIA_SOURCE_TYPE = {
  CAMERA: 'camera',
  GALLERY: 'gallery',
};

// CAMERA permission for Android / IOS
const CAMERA_PERMISSION = Platform.select({
  ios: RNPermissions.PERMISSIONS.IOS.CAMERA,
  android: RNPermissions.PERMISSIONS.ANDROID.CAMERA,
  default: '',
});

// Setting alert
const showAlert = (title, message) => {
  Alert.alert(
    title || 'Permission Blocked',
    message
      || 'You have blocked camera permission. Please enable it in the settings.',
    [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Go to Settings',
        onPress: () => {
          RNPermissions.openSettings();
        },
      },
    ],
  );
};

// handle permission status
const handlePermissionStatus = (result, resolve) => {
  switch (result) {
    case RNPermissions.RESULTS.GRANTED:
      return resolve(true);
    case RNPermissions.RESULTS.LIMITED:
      return resolve(true);
    case RNPermissions.RESULTS.DENIED:
      return resolve(false);
    case RNPermissions.RESULTS.BLOCKED:
      showAlert();
      return resolve(false);
    case RNPermissions.RESULTS.UNAVAILABLE:
      showAlert();
      return resolve(false);
    default:
      return resolve(false);
  }
};

// handle media
const handleMedia = (media) => {
  const uri = Platform.OS === 'android' ? media.path : media.path;
  const filename = uri?.split('/').pop();
  const updatedMedia = {
    ...media,
    uri,
    filename,
  };
  return updatedMedia;
};

// Request Camera Permission
const requestCameraPermission = () => new Promise((resolve, reject) => {
  (async () => {
    try {
      const permissionStatus = await RNPermissions.check(CAMERA_PERMISSION);
      if (
        permissionStatus === RNPermissions.RESULTS.GRANTED
          || permissionStatus === RNPermissions.RESULTS.LIMITED
      ) {
        return resolve(true);
      }

      const result = await RNPermissions.request(CAMERA_PERMISSION);
      handlePermissionStatus(result, resolve);
    } catch (err) {
      console.log('Error requesting camera permission', err.code);
      return reject(err);
    }
  })();
});

// Request Storage Permission
const requestStoragePermission = () => new Promise((resolve, reject) => {
  (async () => {
    try {
      if (Platform.OS === 'ios') {
        const permissionStatus = await RNPermissions.check(
          RNPermissions.PERMISSIONS.IOS.PHOTO_LIBRARY,
        );
        if (
          permissionStatus === RNPermissions.RESULTS.GRANTED
            || permissionStatus === RNPermissions.RESULTS.LIMITED
        ) {
          return resolve(true);
        }

        const result = await RNPermissions.request(
          RNPermissions.PERMISSIONS.IOS.PHOTO_LIBRARY,
        );
        handlePermissionStatus(result, resolve);
      }

      if (Platform.OS === 'android') {
        const apiLevel = await DeviceInfo.getApiLevel();
        let permissions = [
          RNPermissions.PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
        ];
        if (apiLevel >= 33) {
          permissions = [
            RNPermissions.PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
            RNPermissions.PERMISSIONS.ANDROID.READ_MEDIA_VIDEO,
          ];
          if (apiLevel >= 34) {
            permissions.push(
              RNPermissions.PERMISSIONS.ANDROID
                .READ_MEDIA_VISUAL_USER_SELECTED,
            );
          }
        }
        const permissionStatus = await RNPermissions.checkMultiple(
          permissions,
        );

        if (
          Object.values(permissionStatus).every(
            (r) => r === RNPermissions.RESULTS.GRANTED,
          )
            || Object.values(permissionStatus).every(
              (r) => r === RNPermissions.RESULTS.LIMITED,
            )
        ) {
          return resolve(true);
        }

        const results = await RNPermissions.requestMultiple(permissions);
        if (
          Object.values(permissionStatus).every(
            (r) => r === RNPermissions.RESULTS.GRANTED,
          )
            || Object.values(permissionStatus).every(
              (r) => r === RNPermissions.RESULTS.LIMITED,
            )
        ) {
          return resolve(true);
        }

        if (
          Object.values(results).every(
            (r) => r === RNPermissions.RESULTS.BLOCKED,
          )
            || Object.values(results).every(
              (r) => r === RNPermissions.RESULTS.UNAVAILABLE,
            )
        ) {
          showAlert(
            'Permission Blocked',
            'You have blocked gallery permission. Please enable it in the settings.',
          );
          return resolve(false);
        }

        if (
          Object.values(results).every(
            (r) => r === RNPermissions.RESULTS.DENIED,
          )
        ) {
          return resolve(false);
        }
      }
    } catch (err) {
      console.log('Error requesting storage permission', err.code);
      return reject(err);
    }
  })();
});

const MediaPicker = {
  // Function to handle camera or gallery media selection
  getMedia: async (source = 'camera', option = {}) => {
    const options = {
      mediaType: 'photo', // 'photo' | 'video' | 'any'
      width: 1080,
      height: 1080,
      compressImageMaxWidth: 1080,
      compressImageMaxHeight: 1080,
      compressImageQuality: 0.7,
      ...option,
    };

    try {
      if (source === MEDIA_SOURCE_TYPE.CAMERA) {
        // Check and Request camera permission
        const granted = await requestCameraPermission();
        if (!granted) {
          return false;
        }

        // Open camera and handle result
        const media = await ImagePicker.openCamera(options);
        if (media) {
          return handleMedia(media);
        }
      } else if (source === MEDIA_SOURCE_TYPE.GALLERY) {
        // Check and Request storage permission
        const granted = await requestStoragePermission();
        if (!granted) {
          return false;
        }

        // Open gallery and handle result
        const media = await ImagePicker.openPicker(options);
        if (media) {
          return handleMedia(media);
        }
      }
      return false; // If no media was selected or permissions denied
    } catch (error) {
      console.error('Error while picking media:', JSON.stringify(error));
      return false;
    }
  },
};

export default MediaPicker;
