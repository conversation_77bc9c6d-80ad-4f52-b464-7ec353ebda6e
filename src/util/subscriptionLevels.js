import  {USER_ROLES} from '../constants'
export default function subscriptionLevels(currentUser) {

    let role;

    const isTrainerFree = currentUser.permissions?.filter(item => item.name === "edge_trainer_access").length;
    if(isTrainerFree && currentUser.nasm_role === USER_ROLES.TRAINER)
    {
        role="Trainer Free"
    }

    const isTrainerStudent = currentUser.permissions?.filter(item => item.name === "edge_exam_prep_package_cpt").length;
    if(isTrainerStudent && currentUser.nasm_role === USER_ROLES.TRAINER)
    {
        role="Trainer Student"
    }

    const isTrainerFoundation = currentUser.permissions?.filter(item => item.name === "edge_trainer_foundation").length;
    if(isTrainerFoundation && currentUser.nasm_role === USER_ROLES.TRAINER)
    {
       role="Trainer Foundation"
    }

    const isTrainerPlus = currentUser.permissions?.filter(item => item.name === "edge_trainer_plus").length;
    if(isTrainerPlus && currentUser.nasm_role === USER_ROLES.TRAINER)
    {
        role="Trainer Plus"
    }

    const isTrainerPro = currentUser.permissions?.filter(item => item.name === "edge_trainer_pro").length;
    if(isTrainerPro && currentUser.nasm_role === USER_ROLES.TRAINER)
    {
        role="Trainer Pro"
    }

    if (currentUser.nasm_role === USER_ROLES.CLIENT && currentUser.client_user.trainer)
    {
        role="Client"
    }

    if (currentUser.nasm_role === USER_ROLES.CLIENT && !currentUser.client_user.trainer)
    {
        role="Walk In"
    }

    return role;

}