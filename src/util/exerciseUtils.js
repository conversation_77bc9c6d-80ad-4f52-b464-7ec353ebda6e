import { ROLES } from '../constants';

export default function getExerciseName(currentUser, exercise) {
  let exerciseName = exercise?.name;
  if (currentUser && exercise) {
    const isSuperSetOrCircuit = 'compound_routine_exercises' in exercise;
    if (isSuperSetOrCircuit) {
      return exercise?.title;
    }
    if (
      exercise.alias_name
      && exercise.alias_name instanceof Array
      && exercise.alias_name.length > 0
    ) {
      let creator_id;
      if (currentUser.role === ROLES.TRAINER) {
        creator_id = currentUser.id;
      } else {
        creator_id = currentUser.client_user.trainer.user_id;
      }
      exercise.alias_name.map((aliasData) => {
        if (aliasData.alias_name) {
          if (aliasData.user_id === creator_id) {
            exerciseName = aliasData.alias_name;
          }
        }
        return null;
      });
    }

    if (
      exercise?.is_complete
      && exercise?.progressions_regressions?.length > 0
    ) {
      exercise.progressions_regressions.map((exeData) => {
        if (exeData?.is_complete) {
          exerciseName = exeData.name;
        }
        return null;
      });
    }
  }
  return exerciseName;
}

export const getExerciseImage = (exercise) => {
  let exerciseImage = exercise?.image_url;
  if (exercise?.is_complete && exercise?.progressions_regressions?.length > 0) {
    exercise.progressions_regressions.map((exeData) => {
      if (exeData?.is_complete && exeData?.image_url) {
        exerciseImage = exeData.image_url;
      }
      return null;
    });
  }
  return exerciseImage;
};
