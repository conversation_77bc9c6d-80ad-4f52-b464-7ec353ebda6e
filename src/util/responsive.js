import { Dimensions, PixelRatio } from 'react-native';

const screenWidth = Dimensions.get('window').width;
const screenHeight = Dimensions.get('window').height;
const baseWidth = 375; // Standard screen width (in pixels) for an iPhone 6/7/8

// Returns a new integer pixel value that represents a percentage of the device width
export const scaleWidth = (widthPercent) => {
  try {
    const elementWidth = parseFloat(widthPercent);
    return PixelRatio.roundToNearestPixel((screenWidth * elementWidth) / 100);
  } catch {
    throw new Error(
      `Could not scale property ${widthPercent}. Please make sure to use an integer or float.`,
    );
  }
};

// Returns a new integer pixel value that represents a percentage of the device height
export const scaleHeight = (heightPercent) => {
  try {
    const elementHeight = parseFloat(heightPercent);
    return PixelRatio.roundToNearestPixel((screenHeight * elementHeight) / 100);
  } catch {
    throw new Error(
      `Could not scale property ${heightPercent}. Please make sure to use an integer or float.`,
    );
  }
};

// Base linear scaling function
export const baseScale = (size) => (screenWidth / baseWidth) * size;

// Returns a new integer pixel value scaled non-linearly for text
export const curvedScale = (size, factor = 0.5) => PixelRatio.roundToNearestPixel(
  size + (baseScale(size) - size) * factor,
);
