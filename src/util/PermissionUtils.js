import { NativeModules } from 'react-native';
import moment from 'moment';
import { ROLES, FEATURE_FLAGS } from '../constants';

const buildEnv = NativeModules.ENVConfig.buildEnvironment;

export const cpt7PermissionId = buildEnv === 'release' ? 731 : 40;
export const connectedPermissionId = buildEnv === 'release' ? 740 : 41;

export function userHasCpt7Permission(user) {
  if (!user || user.role !== ROLES.TRAINER) {
    return false;
  }

  return user.permissions.some((p) => p.permission_id === cpt7PermissionId);
}

export function userHasConnectedPermission(user) {
  if (
    !FEATURE_FLAGS.NASM_CONNECTED_ENABLED
    || !user
    || user.role !== ROLES.TRAINER
    || !user?.permissions
  ) {
    return false;
  }

  return user.permissions.some(
    (p) => p.permission_id === connectedPermissionId
      && moment(p.expiration_at).isAfter(moment()),
  );
}

export function isConnectedMode(user) {
  const isConnected = userHasConnectedPermission(user);
  if (isConnected) {
    const activeSubscription = user.bypass_subscription
      || (user.subscription_expiration_date
        && moment().isSameOrBefore(user.subscription_expiration_date));
    return !activeSubscription;
  }
  return false;
}
