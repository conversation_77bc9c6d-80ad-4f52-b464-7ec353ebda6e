export function servicesValidation(value) {
  let error;

  if (value.length < 1) {
    error = 'Required';
  }
  return error;
}

export function nameValidation(value) {
  const error = {};

  if (!value || value === '') {
    error.name = 'Required';
  }
  return error;
}

export function priceValidation(value) {
  const error = {};

  if (!value || value === '' || parseFloat(value) < parseFloat('0.01')) {
    error.price = 'Required';
  }
  return error;
}

export function flatRateValidation(value) {
  const error = {};

  if (value && parseFloat(value) > parseFloat('100')) {
    error.tax = 'Max Flatrate is 100%';
  }
  return error;
}
