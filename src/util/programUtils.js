// TODO: all utils should be purely functional and return new values, many mutate their arguments
// This is especially concerning as we're receiving reports of data loss when editing programs

import moment from 'moment';

/**
 * Get a list of the weeks available in a program
 * @param {Object} Program - Complete program data from get details endpoint
 */
export function getProgramWeeks(program) {
  return program.groups
    .map((group) => group.programGroupWeek)
    .reduce((prev, curr) => ({ ...prev, [curr]: curr }), {});
}

/**
 * Get a list of the days available in a program
 * @param {Object} Program - Complete program data from get details endpoint
 */
export function getProgramDays(program) {
  return program.groups
    .map((group) => group.programGroupDay)
    .reduce((prev, curr) => ({ ...prev, [curr]: curr }), {});
}

/**
 * Get an array of the acute variable names for a given exercise
 * @param {Object} exercise - Complete exercise data
 */
export function getExerciseVariables(exercise, includeZeroValues) {
  // exercise.duration = exercise.dur_seconds;
  const {
    sets,
    reps,
    dur_seconds,
    rest,
    tempo,
    rest_tempo,
    distance,
    distance_units,
    pace,
    pace_units,
    weight,
    weight_units,
    exercise_sides,
    allow_unilateral,
  } = exercise;
  const variables = {
    reps,
    sets,
    dur_seconds,
    weight,
    distance,
    distance_units,
    pace,
    pace_units,
    tempo,
    exercise_sides,
    rest,
    rest_tempo,
    weight_units,
  };

  if (!includeZeroValues) {
    delete variables.distance_units;
    delete variables.pace_units;
    if (reps) {
      delete variables.dur_seconds;
    }
  }

  if (!allow_unilateral) {
    delete variables.exercise_sides;
  }

  return Object.keys(variables)
    .map((key) => {
      if (key === 'exercise_sides') {
        if (variables[key]) {
          return { [key]: variables[key] };
        }
        return { [key]: 'alternate' };
      }
      if (key === 'pace_units') {
        if (variables[key]) {
          return { [key]: variables[key] };
        }
        return { [key]: 'mph' };
      }
      if (key === 'distance_units') {
        if (variables[key]) {
          return { [key]: variables[key] };
        }
        return { [key]: 'miles' };
      }
      const number = parseInt(variables[key], 10);
      if (number > 0 || (includeZeroValues && number >= 0)) return { [key]: number };
      if (includeZeroValues && Number.isNaN(number)) {
        return { [key]: 0 };
      }
      return false;
    })
    .filter((variable) => !!variable)
    .reduce((acc, val) => Object.assign(acc, val), {});
}

export function getTempoStringForExercise(exercise) {
  switch (exercise.tempo) {
    case 1:
      return 'Slow';
    case 2:
      return 'Medium';
    case 3:
      return 'Fast';
    case 4:
      return 'Explosive';
    default:
      return 'Slow';
  }
}

export function getRestTempoStringForExercise(exercise) {
  switch (exercise.rest_tempo) {
    case 1:
      return 'Rest';
    case 2:
      return 'Slow';
    default:
      return 'n/a';
  }
}

export function getSideStringForExercise(exercise) {
  switch (exercise.exercise_sides) {
    case 'right_only':
      return 'Right';
    case 'left_only':
      return 'Left';
    case 'alternate':
      return 'Alternating';
    case 'each_side':
      return 'Each';
    default:
      return 'Right';
  }
}

export function abbreviateDistanceUnit(unit) {
  switch (unit.toLowerCase()) {
    case 'miles':
      return 'mi';
    case 'yards':
      return 'yd';
    case 'meters':
      return 'm';
    case 'kilometers':
      return 'km';
    default:
      return unit;
  }
}

function distanceInMiles(distance, unit) {
  switch (unit) {
    case 'miles':
      return distance;
    case 'yards':
      return distance / 1760;
    case 'meters':
      return distance / 1609.344;
    case 'inches':
      return distance / 63360;
    default:
      return distance;
  }
}

function distanceInMeters(distance, unit) {
  switch (unit) {
    case 'miles':
      return distance * 1609.344;
    case 'yards':
      return distance / 1.094;
    case 'meters':
      return distance;
    case 'kilometers':
      return distance / 1000;
    default:
      return distance;
  }
}

/**
 * Get an array of the acute variable names for all exercises in a technique
 * @param {Object} technique - Complete technique data
 */
export function getTechniqueVariables(technique) {
  return technique.exercises
    .map((exercise) => getExerciseVariables(exercise))
    .reduce((acc, val) => Object.assign(acc, val), {});
}

/**
 * Returns all techniques across all groups of a program flattened to a single array
 * @param {Object} programGroups - The groups array from a program
 * @returns {Array} techniques
 */
export function getTechniques(programGroups) {
  return programGroups
    .map((group) => ({ groupId: group.identifier, ...group.techniques }))
    .reduce((a, b) => a.concat(b));
}

/**
 * Returns array of program groups that match the provided day and week id
 * @param {Object} program - Complete program object
 * @param {number} dayId
 * @param {number} weekId
 * @returns {Array} group
 */
export function filterProgramGroups(program, dayId, weekId) {
  return program.groups.filter(
    (group) => dayId === group.programGroupDay && weekId === group.programGroupWeek,
  );
}

/**
 * Returns a program group that match the provided day and week id
 * @param {Object} program - Complete program object
 * @param {number} dayId
 * @param {number} weekId
 * @returns {Object} group
 */
export function findGroupByDayWeek(program, dayId, weekId) {
  return program.groups.find(
    (group) => dayId === group.programGroupDay && weekId === group.programGroupWeek,
  );
}

/**
 * Returns the index of a program group that matches the provided day and week id
 * @param {Object} program - Complete program object
 * @param {number} dayId
 * @param {number} weekId
 * @returns {number} index
 */
export function findProgramGroupIndex(program, dayId, weekId) {
  return program.groups.findIndex(
    (group) => dayId === group.programGroupDay && weekId === group.programGroupWeek,
  );
}

export function removeDuplicateWorkouts(program) {
  const workouts = {};
  if (program.workouts) {
    program.workouts = program.workouts.filter((workout) => {
      if (workouts[workout.id]) {
        return false;
      }
      workouts[workout.id] = workout;
      return true;
    });
  }
  return workouts;
}

export function getDurationFromSeconds(seconds, appendPlus) {
  if (!seconds) {
    return `0${appendPlus ? '+' : ''} min`;
  }
  if (seconds > 0 && seconds < 30) {
    return `${seconds}${appendPlus ? '+' : ''} sec`;
  }
  const duration = moment.duration(seconds, 'seconds');
  let minutes = Math.floor(duration.asMinutes());
  const remainingSeconds = duration.seconds();
  if (remainingSeconds >= 30) {
    minutes += 1;
  }
  return `${minutes}${appendPlus ? '+' : ''} min`;
}

export function calculateSecondsForExercise(exercise) {
  let {
    dur_seconds = 0,
    reps = 1,
    sets = 1,
    tempo = 0,
    rest = 0,
    distance = 0,
    distance_units = 'miles',
    pace = 0,
    pace_units = 'mph',
    exercise_sides,
    allow_unilateral,
  } = exercise;
  let seconds = 0;

  if (reps === 0) {
    reps = 1;
  }
  if (sets === 0) {
    sets = 1;
  }

  let side = 1;
  if (allow_unilateral && exercise_sides === 'each_side') {
    side = 2;
  }

  if (dur_seconds !== 0 && dur_seconds !== null) {
    seconds = (dur_seconds * side + rest) * sets;
  } else if (distance !== 0 && distance != null) {
    if (pace) {
      switch (pace_units) {
        case 'mph':
          seconds = ((distanceInMiles(distance, distance_units) / pace) * 3600 * side
              + rest)
            * sets;
          break;
        case 'rpm':
          seconds = 0;
          // We are not calculating duration for RPM as a proper formula would require more variables than we would like to keep track of
          // this would be the most accurate formula with our current variables:
          //
          // seconds = (((distanceInMiles(distance, distance_units) / ((distanceInMiles(27.5 * 3.14, 'inches') * pace) / 60)) * side) + rest) * sets;
          // note: 27.5 inches is the average diameter of a road bike tire              ^^^
          //
          // this is still incomplete. This formula doesn't take into account the gear ratio which will vary depending on the bike and the user
          // as the rpm measured is the rotation of the bike pedals, not the tire
          // the formula currently assumes a 1:1 gear ratio
          break;
        case 'watts':
          seconds = (Math.cbrt(2.8 / pace)
              * distanceInMeters(distance, distance_units)
              * side
              + rest)
            * sets;
          break;
        case 'kph':
          seconds = ((distanceInMeters(distance, distance_units) / pace) * 3.6 * side
              + rest)
            * sets;
          break;
        default:
          break;
      }
    }
  } else {
    switch (tempo) {
      case 1:
        seconds = 5;
        break;
      case 2:
        seconds = 3;
        break;
      case 3:
        seconds = 2;
        break;
      case 4:
        seconds = 1;
        break;
      default:
        seconds = 3;
        break;
    }
    seconds = (reps * seconds * side + rest) * sets;
  }

  return seconds;
}

export function getDurationForSection({
  section,
  isSuperSetOrCircuit,
  exercises = [],
}) {
  let seconds = 0;
  let appendPlus = false;
  if (isSuperSetOrCircuit && section?.compound_routine_exercises?.length) {
    section.compound_routine_exercises.forEach((superset_ex) => {
      const exerciseSeconds = calculateSecondsForExercise(superset_ex);
      seconds += exerciseSeconds;
      if (exerciseSeconds === 0) {
        appendPlus = true;
      }
    });
  } else if (section.exercises) {
    section.exercises.forEach((exerciseId) => {
      const exercise = exercises[exerciseId];
      let exerciseSeconds = 0;
      if ('compound_routine_exercises' in exercise) {
        exercise.compound_routine_exercises?.forEach((superset_ex) => {
          exerciseSeconds = calculateSecondsForExercise(superset_ex);
          seconds += exerciseSeconds;
        });
      } else {
        exerciseSeconds = calculateSecondsForExercise(exercise);
        seconds += exerciseSeconds;
      }
      if (exerciseSeconds === 0) {
        appendPlus = true;
      }
    });
  }

  return getDurationFromSeconds(seconds, appendPlus);
}

export function calculateSecondsForWorkout(workout) {
  if (!workout) return 0;
  let seconds = 0;
  const sections = workout.sections || [];
  sections.forEach((section) => {
    const exercises = section.exercises || [];
    exercises.forEach((exercise) => {
      seconds += calculateSecondsForExercise(exercise);
    });
  });
  return seconds;
}

function removeDuplicateExercises(exercises) {
  if (!Array.isArray(exercises)) {
    return;
  }

  const exerciseIds = {};
  for (let i = 0; i < exercises.length; i += 1) {
    const { id } = exercises[i];
    if (exerciseIds[id]) {
      exercises.splice(i, 1);
    } else {
      exerciseIds[id] = true;
    }
  }
}

// TODO: this is dangerous because it mutates the function argument, all utils should be purely functional
export function addCorrectiveExercisesToWorkouts(
  workouts,
  correctiveExercisesDictionary,
) {
  if (
    !Array.isArray(workouts)
    || !correctiveExercisesDictionary
    || typeof correctiveExercisesDictionary !== 'object'
  ) {
    return;
  }
  workouts.forEach((workout) => {
    let exerciseCount = 0;
    if (workout.sections) {
      workout.sections.forEach((section) => {
        const toAdd = correctiveExercisesDictionary[section.id] || [];
        if (Array.isArray(section.exercises)) {
          section.exercises = toAdd.concat(section.exercises);
        } else {
          section.exercises = toAdd;
        }
        removeDuplicateExercises(section.exercises);
        exerciseCount += section.exercises.length;
      });
    }
    workout.exercise_count = exerciseCount;
  });
}

export function addCorrectiveExercisesToProgram(
  program,
  correctiveExercisesDictionary,
) {
  if (!program) {
    return;
  }
  addCorrectiveExercisesToWorkouts(
    program.workouts,
    correctiveExercisesDictionary,
  );
}

export function addCorrectiveExercisesToWorkout(
  workout,
  correctiveExercisesDictionary,
) {
  addCorrectiveExercisesToWorkouts([workout], correctiveExercisesDictionary);
}

export function getBackgroundFromProgramCategory(programCategory) {
  switch (programCategory.replace(/\s/g, '').toLowerCase()) {
    case 'stabilizationendurance':
      return require('../resources/program-card-image-Stabilization-Endurance.png');
    case 'musclegain':
      return require('../resources/program-card-image-Muscle-Gain.png');
    case 'maximalstrength':
      return require('../resources/program-card-image-Maximal-Strength.png');
    case 'power':
      return require('../resources/program-card-image-Power.png');
    case 'beginners':
      return require('../resources/program-card-image-Beginners.png');
    case 'generalfitness':
      return require('../resources/program-card-image-General-Fitness.png');
    case 'lifestyle':
      return require('../resources/program-card-Lifestyle.png');
    case 'intervaltraining':
      return require('../resources/program-card-image-General-Fitness-Interval-Training.png');
    case 'wakeupworkout':
      return require('../resources/program-card-image-WakeUpWorkout.png');
    default:
      return require('../resources/placeholderBgProgramBanner.png');
  }
}

export const formatSuperSets = (workouts = []) => {
  const getUpdatedWorkouts = workouts.map((workout) => {
    const getUpdatedSections = workout.sections.map((sec) => {
      const sectionExercises = combineExercisesAndSuperSets(sec.exercises);
      return {
        ...sec,
        exercises: sectionExercises,
      };
    });
    return {
      ...workout,
      sections: getUpdatedSections,
    };
  });
  return getUpdatedWorkouts;
};

const combineExercisesAndSuperSets = (originalExercises = []) => {
  const isAlreadyFormatted = originalExercises.some(
    (ex) => 'compound_routine_exercises' in ex,
  );
  if (isAlreadyFormatted) {
    return originalExercises;
  }
  const exercises = originalExercises.reduce((acc, i) => {
    const { compound_routine_id } = i;
    return {
      ...acc,
      [compound_routine_id]: acc[compound_routine_id]
        ? [...acc[compound_routine_id], i].sort((a, b) => a.ordinal - b.ordinal)
        : [i],
    };
  }, {});

  let output = [];
  const keys = Object.keys(exercises);
  keys.forEach((i, index) => {
    const exercise = [...exercises[i]].map((j) => {
      const qq = { ...j };
      delete qq.compound_routine;
      return qq;
    });
    if (i === 'null') {
      output.push(...exercise);
    } else {
      const compound_routine = {
        ...exercises[i][0].compound_routine,
        compound_routine_id: exercises[i][0].compound_routine_id,
        ordinal: exercises[i][0].ordinal ?? index,
        compound_routine_exercises: [...exercise],
      };
      if (!compound_routine.title && exercises[i][0].compound_routine_title) {
        compound_routine.title = exercises[i][0].compound_routine_title;
      }
      if (!compound_routine.reps && exercises[i][0].compound_routine_reps) {
        compound_routine.reps = exercises[i][0].compound_routine_reps;
      }
      if (!compound_routine.rest && exercises[i][0].compound_routine_rest) {
        compound_routine.rest = exercises[i][0].compound_routine_rest;
      }
      if (
        !compound_routine.routine_type
        && exercises[i][0].compound_routine_type
      ) {
        compound_routine.routine_type = exercises[i][0].compound_routine_type;
      }
      output.push(compound_routine);
    }
  });
  output = output.sort((a, b) => a.ordinal - b.ordinal);
  return output;
};

export const unformatSuperSets = (workouts = []) => {
  const getUpdatedWorkouts = workouts.map((workout) => {
    const getUpdatedSections = workout.sections.map((sec) => {
      const sectionExercises = uncombineExercisesAndSuperSets(sec.exercises);
      return {
        ...sec,
        exercises: sectionExercises,
      };
    });
    return {
      ...workout,
      sections: getUpdatedSections,
    };
  });
  return getUpdatedWorkouts;
};

const uncombineExercisesAndSuperSets = (originalExercises = []) => {
  const exercises = [];
  originalExercises.forEach((ex) => {
    if ('compound_routine_exercises' in ex) {
      ex.compound_routine_exercises.forEach((superset_ex) => {
        const cr = {
          ...superset_ex,
          compound_routine_id: ex.compound_routine_id || ex.id,
          compound_routine_title: ex.title,
          compound_routine_reps: ex.reps,
          compound_routine_rest: ex.rest,
          compound_routine_type: ex.routine_type,
        };
        exercises.push(cr);
      });
    } else {
      exercises.push(ex);
    }
  });
  return exercises.sort((a, b) => a.ordinal - b.ordinal);
};
