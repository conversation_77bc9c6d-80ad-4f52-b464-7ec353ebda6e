import * as Sentry from '@sentry/react-native';
import { NativeModules } from 'react-native';

const buildEnv = NativeModules.ENVConfig.buildEnvironment;

const shouldLogError = (error) => {
  if (!error || buildEnv !== 'debug') {
    return false;
  }
  let errorMessage = '';
  if (error.message) {
    errorMessage = error.message.toLowerCase();
  } else if (typeof error === 'string') {
    errorMessage = error.toLowerCase();
  } else if (typeof error === 'object' && !Object.keys(error).length) {
    return false;
  }
  const isNetworkError = errorMessage.includes('network error');
  if (isNetworkError) {
    return false;
  }
  return true;
};

/**
 * Uploads error data to sentry with a component:name tag.
 * @param {string} name The name of the component.
 * @param {Error} error
 */
export function logComponentException(name, error, isScreenException) {
  if (shouldLogError(error)) {
    let tag = 'component';
    if (isScreenException) {
      tag = 'screen';
    }
    Sentry.withScope((scope) => {
      scope.setTag(tag, name);
      scope.setLevel('error');
      Sentry.captureException(error);
    });
  }
}

/**
 * Uploads error data to sentry with a screen:name tag.
 * @param {string} name The name of the screen.
 * @param {Error} error
 */
export function logScreenException(name, error) {
  logComponentException(name, error, true);
}

/**
 * Uploads only error data to sentry with no tags.
 * @param {Error} error
 */
export function logException(error) {
  if (shouldLogError(error)) {
    const exception = error.message || error;
    Sentry.captureException(new Error(exception));
  }
}
