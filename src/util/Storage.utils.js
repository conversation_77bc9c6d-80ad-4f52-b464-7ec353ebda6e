import { call } from 'redux-saga/effects';
import AsyncStorage from '@react-native-async-storage/async-storage';

export async function saveToStorage(itemKey, itemValue) {
  try {
    await AsyncStorage.setItem(itemKey, JSON.stringify(itemValue));
  } catch (error) {
    return false;
  }
  return null;
}

export async function readFromStorage(itemKey) {
  try {
    const value = await AsyncStorage.getItem(itemKey).then(
      (response) => response,
    );
    return JSON.parse(value);
  } catch (error) {
    return null;
  }
}

export function* removeFromStorage(itemKey) {
  return yield call(AsyncStorage.removeItem, itemKey);
}
