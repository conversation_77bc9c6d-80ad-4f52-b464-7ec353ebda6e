import { NativeEventEmitter, NativeModules } from 'react-native';

const { VideoCompressor } = NativeModules;
const eventEmitter = new NativeEventEmitter(VideoCompressor);

export default {
  async compressVideo(uri, onProgress) {
    let eventListener = null;
    if (onProgress) {
      eventListener = eventEmitter.addListener('progress', onProgress);
    }
    try {
      const compresedVideoPath = await VideoCompressor.compressVideo(uri);
      return compresedVideoPath;
    } finally {
      if (eventListener != null) eventListener.remove();
    }
  },
  cancelCompression: VideoCompressor.cancelCompression,
  isCancel: (error) => error.code === 'COMPRESSOR_ERROR' && error.message === 'CANCELLED',
};
