import moment from 'moment';
import * as lookup from 'country-code-lookup';
import { Linking, Alert } from 'react-native';
import { AVATAR_BASE_URL } from '../constants';

export const getIntervals = (startString, endString, format) => {
  const start = moment(startString, 'HH:mm');
  const end = moment(endString, 'HH:mm');

  start.minutes(Math.ceil(start.minutes() / 15) * 15);

  const result = [];

  const current = moment(start);

  while (current <= end) {
    result.push(current.format(format));
    current.add(15, 'minutes');
  }

  return result;
};

export const removeEmojis = (string) => {
  const basicRegex = /\p{Extended_Pictographic}|[\uFE0F]/gu;
  const extRegex = /\p{Emoji_Presentation}\p{Emoji_Modifier}*/gu;
  const extraRegex = /(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])/g;
  const filteredString = string
    ?.replace(basicRegex, '')
    ?.replace(extRegex, '')
    ?.replace(extraRegex, '');
  return filteredString;
};

export const removeAccentedChars = (string) => {
  const regex = /([^\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u024Fa-zA-Z0-9 ])/g;
  return string ? string.replace(regex, '') : string;
};

export function getCountryCode(currentCountry) {
  let countryObj = lookup.byInternet(currentCountry);
  if (!countryObj) {
    countryObj = lookup.byIso(currentCountry);
  }

  return !countryObj ? null : countryObj.country;
}

export function getFocusedRouteName(navState) {
  const route = navState.routes[navState.index];

  if (route.state) {
    // routes containing their own state have deeper nested routes
    return getFocusedRouteName(route.state);
  }

  // no state means we are looking at our focused route
  return route.name;
}

export const onPressExternalLink = (link) => {
  Linking.canOpenURL(link)
    .then((supported) => {
      if (supported) {
        Linking.openURL(link);
      }
    })
    .catch(() => Alert.alert('unable to open this URL'));
};

export const areAllArraysEmpty = (obj) => Object.values(obj).every((array) => array.length === 0);

export function getAvatarUrl(userData) {
  const { avatar } = userData;

  // Check if avatar exists
  if (avatar && avatar?.blob_name && avatar?.user_id) {
    const avatarUrl = `${AVATAR_BASE_URL}/useravatars/${avatar?.user_id}/${avatar?.blob_name}`;
    return avatarUrl;
  }
  return '';
}

export const reduceTwoDecDigit = (digit) => {
  if (!digit || digit === '' || digit === '0' || digit === '0.0') {
    return '';
  }

  const digitInt = parseInt(digit.replace('.', ''), 10);
  if (Number.isNaN(digitInt)) {
    return '';
  }

  const digitStr = digitInt.toString();
  if (digitStr.length === 1) {
    return `0.0${digitStr}`;
  }
  if (digitStr.length === 2) {
    return `0.${digitStr}`;
  }
  if (digitStr.length === 3) {
    return `${digitStr[0]}.${digitStr.slice(-2)}`;
  }
  return `${parseInt(digitStr.slice(0, -2), 10)}.${digitStr.slice(-2)}`;
};

export function debounce(func, wait = 300) {
  let timeout;
  return function (...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}
