import * as Yup from 'yup';

export function locationsValidation(value) {
  let error;

  if (value.length < 1) {
    error = 'Required';
  }
  return error;
}

export const createEditServicesFormValidations = Yup.object({
  name: Yup.string().required('Required'),
  duration: Yup.number().required('Required'),
  price: Yup.string().required('Required'),
  tax: Yup.number().max(99, 'Max Tax is 99'),
  description: Yup.string().required('Required'),
});
