// @flow

import type { User } from '../types/User';

export const client: User = {
  Identifier: '123',
  FirstName: '<PERSON>',
  LastName: '<PERSON>',
  DateOfBirth: '01011999',
  ImageURI: 'http://www.placehold.it/40x40',
  FullName: '<PERSON>',
  UserType: 'client',
};

export const trainer: User = {
  Identifier: '234',
  FirstName: 'Trainer',
  LastName: 'Person',
  DateOfBirth: '02011999',
  ImageURI: 'http://www.placehold.it/40x40',
  FullName: 'Trainer Guy',
  UserType: 'trainer',
};
