export const BODY_FAT_ACTION_TYPES = {
  RESET: 'reset',
  EDIT: 'edit',
  CALCULATE: 'calculate',
  CHANGE_UNITS: 'change units',
};

export const CALCULATION_TYPE = {
  US_NAVY_METHOD: 'U.S. Navy Method',
  JACKSON_POLLOCK_3_SITE: 'Jackson and Pollock 3 Site Inputs',
  JACKSON_POLLOCK_7_SITE: 'Jackson and Pollock 7 Site Inputs',
  DURNIN_WOMERSLEY_4_SITE: 'Durnin-Womersley 4 Site Inputs',
};

export const UNIT_TYPE = {
  IMPERIAL: 'U.S. Units',
  METRIC: 'Metric',
};

export const GENDER_TYPE = {
  MALE: 'Male',
  FEMALE: 'Female',
};

export const BODY_MEASUREMENT_TYPE = {
  NECK_CIRCUMFERENCE: 'Neck Circumference',
  WAIST_CIRCUMFERENCE: 'Waist Circumference',
  HIP_CIRCUMFERENCE: 'Hip Circumference',
  CHEST: 'Chest',
  ABDOMEN: 'Abdomen',
  THIGH: 'Thigh',
  TRICEPS: 'Triceps',
  SUPRAILIAC: 'Suprailiac',
  MID_AXILLARY: 'Mid-axillary',
  SUBSCAPULAR: 'Subscapular',
  BICEPS: 'Biceps',
};

export const MethodKeysAndLabel = {
  US_NAVY_METHOD: {
    neckCircumference: {
      key: 'neck_circumference',
      label: 'Neck Circumference',
    },
    waistCircumference: {
      key: 'waist_circumference',
      label: 'Waist Circumference',
    },
    hipCircumference: {
      key: 'hip_circumference',
      label: 'Hip Circumference',
      gender: GENDER_TYPE.FEMALE,
    },
  },
  JACKSON_POLLOCK_3_SITE: {
    chest: {
      key: 'chest',
      label: 'Chest',
      gender: GENDER_TYPE.MALE,
    },
    abdomen: {
      key: 'abdomen',
      label: 'Abdomen',
      gender: GENDER_TYPE.MALE,
    },
    triceps: {
      key: 'triceps',
      label: 'Triceps',
      gender: GENDER_TYPE.FEMALE,
    },
    suprailiac: {
      key: 'suprailiac',
      label: 'Suprailiac',
      gender: GENDER_TYPE.FEMALE,
    },
    thigh: {
      key: 'thigh',
      label: 'Thigh',
    },
  },
  JACKSON_POLLOCK_7_SITE: {
    chest: {
      key: 'chest',
      label: 'Chest',
    },
    midAxillary: {
      key: 'mid_axillary',
      label: 'Mid-axillary',
    },
    subscapular: {
      key: 'subscapular',
      label: 'Subscapular',
    },
    triceps: {
      key: 'triceps',
      label: 'Triceps',
    },
    abdomen: {
      key: 'abdomen',
      label: 'Abdomen',
    },
    suprailiac: {
      key: 'suprailiac',
      label: 'Suprailiac',
    },
    thigh: {
      key: 'thigh',
      label: 'Thigh',
    },
  },
  DURNIN_WOMERSLEY_4_SITE: {
    biceps: {
      key: 'biceps',
      label: 'Biceps',
    },
    subscapular: {
      key: 'subscapular',
      label: 'Subscapular',
    },
    triceps: {
      key: 'triceps',
      label: 'Triceps',
    },
    suprailiac: {
      key: 'suprailiac',
      label: 'Suprailiac',
    },
  },
};

export const initialBodyFatModel = {
  methodType: MethodKeysAndLabel.US_NAVY_METHOD.US_NAVY_METHOD,
  units: UNIT_TYPE.IMPERIAL,
  weight: 0,
  sex: GENDER_TYPE.MALE,
  age: 0,
  height_cm: 0,
  height_ft: 0,
  height_in: 0,
  bodyfat_score: null,
};

export const initialModels = {
  ...initialBodyFatModel,
};
