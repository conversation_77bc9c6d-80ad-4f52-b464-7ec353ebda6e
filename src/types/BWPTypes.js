import { calculate_date_from_today } from '../util/CalculatorUtils';

export const ActionTypes = {
  CHANGE_STEP: 'CHANGE_STEP',
  COMPLETE_STEP: 'COMPLETE_STEP',
  CHANGE_UNITS: 'CHANGE_UNITS',
  SYNC_UNITS: 'SYNC_UNITS',
  UPDATE_FORM_DATA: 'UPDATE_FORM_DATA',
  CLEAR_FORM_DATA: 'CLEAR_FORM_DATA',
  INITIALIZE_FORM_DATA: 'INITIALIZE_FORM_DATA',
};

export const UnitType = {
  US: 'U.S.',
  METRIC: 'Metric',
};

export const GenderType = {
  male: 'Male',
  female: 'Female',
};

export const ActivityLevel = {
  SEDENTARY: 1.2,
  LIGHTLY_ACTIVE: 1.375,
  MODERATELY_ACTIVE: 1.55,
  VERY_ACTIVE: 1.725,
  EXTREMELY_ACTIVE: 1.9,
};

export const ActivityLevelStrings = {
  SEDENTARY: 'Sedentary',
  LIGHTLY_ACTIVE: 'Lightly Active',
  MODERATELY_ACTIVE: 'Moderately Active',
  VERY_ACTIVE: 'Very Active',
  EXTREMELY_ACTIVE: 'Extremely Active',
};

export const DietTypes = {
  DEFAULT: 'Default',
  KETOGENIC: 'Ketogenic',
  LOW_FAT: 'Low-Fat',
  LOW_CARBOHYDRATE: 'Low-Carbohydrate',
  HIGH_PROTEIN: 'High-Protein',
};

export const MessageTypes = {
  UNREACHABLE_GOAL: {
    message: 'Your goal weight is currently unreachable.',
    messageType: 'Error',
  },
  LOW_BMI: {
    message: 'Your goal weight will result in a low BMI.',
    messageType: 'Warning',
  },
  HIGH_BMI: {
    message: 'Your goal weight will result in a high BMI.',
    messageType: 'Warning',
  },
  NEGATIVE_CALORIES: {
    message:
      'Your goal weight will result in a negative maintenance calories amount.',
    messageType: 'Error',
  },
  NO_WEIGHT_CHANGE: {
    message: 'Your goal weight is the same as your current weight.',
    messageType: 'Warning',
  },
};

export const initialFormModel = {
  units: UnitType.US,
  formIteration: 1,
};

export const initialStartModel = {
  clientName: '',
};

export const initialBMRModel = {
  sex: 'Male',
  weight: '',
  height: '',
  height_ft: '',
  height_in: '',
  age: '',
};

export const initialTDEEModel = {
  currentActivityLevel: ActivityLevel.SEDENTARY,
};

export const initialGoalModel = {
  goalWeight: '',
  numberOfDays: 180,
  goalDate: String(calculate_date_from_today(180)),
};

export const initialMacroModel = {
  diet: DietTypes.DEFAULT,
  proteinPercent: 30,
  carbohydratesPercent: 40,
  fatsPercent: 30,
  totalPercent: 100,
};

// Get the preset macronutrient percents based on the selected diet
export const getDietPercents = (diet) => {
  switch (diet) {
    case DietTypes.HIGH_PROTEIN:
      return [
        { x: 'Proteins', y: 50 },
        { x: 'Carbs', y: 40 },
        { x: 'Fats', y: 10 },
      ];
    case DietTypes.LOW_CARBOHYDRATE:
      return [
        { x: 'Proteins', y: 25 },
        { x: 'Carbs', y: 20 },
        { x: 'Fats', y: 55 },
      ];
    case DietTypes.LOW_FAT:
      return [
        { x: 'Proteins', y: 20 },
        { x: 'Carbs', y: 60 },
        { x: 'Fats', y: 20 },
      ];
    case DietTypes.KETOGENIC:
      return [
        { x: 'Proteins', y: 20 },
        { x: 'Carbs', y: 10 },
        { x: 'Fats', y: 70 },
      ];
    case DietTypes.DEFAULT:
    default:
      return [
        { x: 'Proteins', y: 30 },
        { x: 'Carbs', y: 40 },
        { x: 'Fats', y: 30 },
      ];
  }
};

export const initialModels = {
  ...initialFormModel,
  ...initialStartModel,
  ...initialBMRModel,
  ...initialTDEEModel,
  ...initialGoalModel,
  ...initialMacroModel,
};
