// @flow

//= ============================================================================
// Program Schedule Data
// from atheleteSchedule.json
export type ScheduleItem = {
  Id: number,
  ProgramId: number,
  ProgramTypeId: number,
  Completed: boolean,
  Name: string,
  MobileName: string,
  Title: string,
  Description: ?string, // null in example data
  ScheduleDate: string,
  ProgramStartDate: string,
  ProgramTitle: string,
  ProgramType: string,
  TotalDays: number,
  TotalDaysComplete: number,
  DaysScheduledInFuture: number,
  LastCompletedDate: string,
};

//= ============================================================================
// Program Exercise Data
// from athleteProgram.json
export type Program = {
  ProgramTitle: string,
  ScheduleDate: string,
  Strategies: Array<Strategy>,
};

export type Strategy = {
  StrategyName: string,
  Techniques: Array<Technique>,
};

export type Technique = {
  TechniqueName: string,
  exercises: Array<Exercise>,
};

export type Exercise = {
  BodySide: string,
  Sequence: number,
  ExerciseName: string,
  ExerciseInstructions: string,
  IsRightLeft: boolean,
  AcuteVariables: AcuteVariables,
  ExerciseMedia: Array<ExerciseMedia>,
};

export type AcuteVariables = {
  Week: number,
  Sets: string,
  Reps: string,
  Duration: number,
  RestDuration: number,
  Tempo: string,
  Intensity: string,
  DurationType: string,
  RestDurationType: string,
};

export type ExerciseMedia = {
  MediaUri: string,
  ThumbnailUri: string,
  StartUri: string,
  StopUri: string,
};

//= ============================================================================
// Test Report
// from testReport.json
export type TestReport = {
  DateTaken: string,
  GeneratedTest: ?any, // null in example data
  GeneratableTest: Object,
  GeneratedProgramId: number,
  UnlinkedPreviousTest: ?number, // null in example data
  CanGenerateProgram: boolean,
  CanAddReport: boolean,
  GivenBy: string,
  ClinicalReport: ?number, // null in example data
  TestResults: Array<TestResult>,
};

export type GeneratableTest = {
  ID: number,
  Abbreviations: string,
  Name: string,
  ShortName: string,
  Description: string,
  route: string,
  DisplayOrder: number,
  TestType: TestType,
  DefaultTest: boolean,
  Incomplete: boolean,
  CanTakeTest: boolean,
};

export type TestType = {
  TestTypeID: number,
  TestTypeName: string,
  Category: string,
};

export type TestResult = {
  RiskLevel: RiskLevel,
  BodyRegionResult: BodyRegionResult,
  ExerciseResults: Array<ExerciseResult>,
  BodyAreaMap: Object,
  Id: number,
  DateTaken: string,
  Score: number,
  Test: Object,
  TestTitle: string,
  IsCurrentTest: boolean,
  TestNotes: ?string, // null in example data
  GivenBy: string,
  CompletedDate: string,
};

export type RiskLevel = {
  ID: number,
  Name: string,
  Color: string,
  LowerBound: ?number, // null in example data
  UpperBound: ?number, // null in example data
};

export type BodyRegionResult = {
  UpperRightScore: number,
  UpperLeftScore: number,
  LowerRightScore: number,
  LowerLeftScore: number,
  RightScore: number,
  LeftScore: number,
  UpperDifferenceScore: number,
  LowerDifferenceScore: number,
  TotalDifferenceScore: number,
  UpperRightRiskLevel: RiskLevel,
  UpperLeftRiskLevel: RiskLevel,
  LowerRightRiskLevel: RiskLevel,
  LowerLeftRiskLevel: RiskLevel,
  RightRiskLevel: RiskLevel,
  LeftRiskLevel: RiskLevel,
  UpperDifferenceRiskLevel: RiskLevel,
  LowerDifferenceRiskLevel: RiskLevel,
  TotalDifferenceRiskLevel: RiskLevel,
};

export type ExerciseResult = {
  TestExerciseID: number,
  TestExerciseName: string,
  Score: number,
  Pain: boolean,
  ImageId: string,
  RiskLevel: RiskLevel,
  TestCheckPoints: Array<TestCheckPoint>,
};

export type TestCheckPoint = {
  Name: string,
  Measurements: Array<Measurement>,
};

export type Measurement = {
  ID: number,
  Name: string,
  WrittenInstructions: ?string,
  Media: ?any,
  IncludeInScore: boolean,
  ScoreWeight: 0,
  CheckForSymmetry: boolean,
  SymmetryWeight: ?number,
  Optimal: ?any,
  Inputs: Array<TestInput>,
  Score: ?number,
  IsRequired: ?boolean,
  IsRecommended: ?boolean,
  MeasurementGroup: boolean,
  PerformTemplate: ?any,
};

export type TestInput = {
  ID: number,
  MeasurementType: ?any,
  BodySide: string,
  SideLabel: ?string,
  GroupLabel: ?string,
  MovementDetected: boolean,
  MeasuredValue: ?number,
  Score: ?number,
  PairedInputId: ?number,
  MetricLabel: ?string,
  ImperialLabel: ?string,
  Placeholder: ?string,
};
