export const MHR_ACTION_TYPES = {
  RESET: 'reset',
  EDIT: 'edit',
  CALCULATE: 'calculate',
};

export const MHR_TRAINING_ZONES = [
  {
    id: '9384tgfiub3ru',
    title_short: 'Zone 1',
    title_long: 'Training Zone 1: A little intense',
    description: [
      'Light to moderate',
      'Starting to sweat but can still carry on a conversation effortlessly',
    ],
    intensity_level: 1,
    intensity_range_start: 30,
    intensity_range_end: 40,
    RPE: '3-4',
  },
  {
    id: 'fc2894gf9g24e',
    title_short: 'Zone 2',
    title_long: 'Training Zone 2: Moderately intense',
    description: [
      'Challenging to hard',
      'Noticeable sweating and using larger volumes of breath',
      'Continual talking is becoming challenging',
    ],
    intensity_level: 2,
    intensity_range_start: 50,
    intensity_range_end: 60,
    RPE: '5-6',
  },
  {
    id: 'b2evb29fcv92w',
    title_short: 'Zone 3',
    title_long: 'Training Zone 3: Intense',
    description: [
      'Vigorous to very hard',
      'Profuse sweating',
      'Vigorous breathing and ability to talk is limited to short phrases',
    ],
    intensity_level: 3,
    intensity_range_start: 70,
    intensity_range_end: 80,
    RPE: '7-8',
  },
  {
    id: 'qcoivbwvucbr3',
    title_short: 'Zone 4',
    title_long: 'Training Zone 4: Very intense',
    description: [
      'Very hard to maximum effort',
      'Breathing as hard as possible',
      'Speaking is impossible or limited to grunts of single words',
    ],
    intensity_level: 4,
    intensity_range_start: 90,
    intensity_range_end: 100,
    RPE: '9-10',
  },
];
