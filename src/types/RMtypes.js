export const ONE_RM_ACTION_TYPES = {
  RESET: 'reset',
  EDIT: 'edit',
  CHANGE_UNITS: 'change units',
  CALCULATE: 'calculate',
};

export const UNIT_TYPE = {
  US: 'U.S.',
  METRIC: 'Metric',
};

export const LIFT_TYPE = {
  NOT_SPECIFIED: 'Lifts',
  DEADLIFT: 'Deadlifts',
  SQUAT: 'Squats',
  BENCH_PRESS: 'Bench Press',
};

export const initialRMModel = {
  units: UNIT_TYPE.US,
  type: LIFT_TYPE.NOT_SPECIFIED,
  weight: 0,
  reps: 0,
};

export const initialModels = {
  ...initialRMModel,
};
