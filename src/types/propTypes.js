import PropTypes from 'prop-types';

export const Exercise = PropTypes.shape({
  identifier: PropTypes.string,
  exerciseName: PropTypes.string,
  reps: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  sets: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  duration: PropTypes.number,
});

export const Technique = PropTypes.shape({
  identifier: PropTypes.string,
  sequence: PropTypes.number,
  name: PropTypes.string,
  exercises: PropTypes.arrayOf(Exercise),
});
