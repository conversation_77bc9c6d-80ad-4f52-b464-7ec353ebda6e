/** @type {Detox.DetoxConfig} */
module.exports = {
  testRunner: {
    args: {
      $0: 'jest',
      config: 'e2e/jest.config.js',
    },
    jest: {
      setupTimeout: 600000,
    },
  },
  apps: {
    'ios.debug': {
      type: 'ios.app',
      binaryPath: 'ios/build/Build/Products/Dev-Debug-iphonesimulator/nasm.app',
      build:
        'xcodebuild -workspace ios/nasm.xcworkspace -scheme nasm -configuration Dev-Debug -sdk iphonesimulator -derivedDataPath ios/build',
    },
    'ios.release': {
      type: 'ios.app',
      binaryPath: 'ios/build/Build/Products/Dev-iphonesimulator/nasm.app',
      build:
        'xcodebuild -workspace ios/nasm.xcworkspace -scheme nasm -configuration Dev -sdk iphonesimulator -derivedDataPath ios/build',
    },
    'android.debug': {
      type: 'android.apk',
      binaryPath:
        'android/app/build/outputs/apk/development/debug/app-development-debug.apk',
      // 'android/app/build/outputs/apk/development/debug/app-development-debug.apk',
      build:
        'cd android ; ./gradlew assembleDevelopmentDebug assembleAndroidTest -DtestBuildType=debug ; cd -',
      reversePorts: [8081],
    },
    'android.release': {
      type: 'android.apk',
      binaryPath:
        'android/app/build/outputs/apk/development/releaseE2E/app-development-releaseE2E.apk',
      testBinaryPath:
        'android/app/build/outputs/apk/androidTest/development/releaseE2E/app-development-releaseE2E-androidTest.apk',
      build:
        'npm run android-release-bundle && cd android && ./gradlew assembleReleaseE2E assembleAndroidTest -DtestBuildType=releaseE2E',
    },
  },
  devices: {
    simulator: {
      type: 'ios.simulator',
      device: {
        type: 'iPhone 14 Pro',
      },
    },
    attached: {
      type: 'android.attached',
      forceAdbInstall: true,
      device: {
        adbName: '.*',
      },
    },
    emulator: {
      type: 'android.emulator',
      forceAdbInstall: true,
      device: {
        avdName: 'Pixel_3a_API_29',
        // avdName: 'Pixel_4a_API_32',
      },
    },
  },

  // artifacts: {
  //   rootDir: '.artifacts',
  //   plugins: {
  //     instruments: { enabled: false },
  //     log: { enabled: true },
  //     uiHierarchy: 'enabled',
  //     screenshot: {
  //       shouldTakeAutomaticSnapshots: true,
  //       keepOnlyFailedTestsArtifacts: true,
  //       takeWhen: {
  //         testStart: true,
  //         testDone: true,
  //         appNotReady: true,
  //       },
  //     },
  //     video: {
  //       android: {
  //         bitRate: 4000000,
  //       },
  //       simulator: {
  //         codec: 'hevc',
  //       },
  //     },
  //   },
  // },

  configurations: {
    'ios.sim.debug': {
      device: 'simulator',
      app: 'ios.debug',
    },
    'ios.sim.release': {
      device: 'simulator',
      app: 'ios.release',
    },
    'android.att.debug': {
      device: 'attached',
      app: 'android.debug',
    },
    'android.att.release': {
      device: 'attached',
      app: 'android.release',
      // artifacts: {
      //   rootDir: '.artifacts/android',
      //   plugins: {
      //     instruments: 'all',
      //   },
      // },
    },
    'android.emu.debug': {
      device: 'emulator',
      app: 'android.debug',
    },
    'android.emu.release': {
      device: 'emulator',
      app: 'android.release',
    },
  },
};
