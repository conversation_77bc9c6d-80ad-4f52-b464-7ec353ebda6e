# 0.6.0

## Improvements

* Persist user login tokens so users can stay logged in after relaunching the app
* A user can now complete exercises in the past or future
* Added additional calendar controls including a jump to today and close button
* Calendar returns to the currently selected day after closing and re-opening
* Added program completion % to client dashboard
* Improved error message when attempting to invite a new account
* Simplified client goal options

## Bug Fixes

* Prevent the keyboard from covering the confirmation button on the forgot password screen
* Fix issue with gender picker not appearing on iOS
* Fix issue with workout completion % not updating after completing an exercise
* Fix an issue where a client could attempt to edit their own program
* Fix issue with icon graphics getting cut off on view all programs screen
* Fix issue with trainer dashboard header animation not returning to full transparency
* Fix issue with shadows on view assigned programs button

---

# 0.5.0

## New Features

* A trainer can see a list of all current and historical programs for a client
* A user can be informed when there are updated terms and conditions and require a user to accept them before continuing
* A user can be informed if there is a recommended updated version of the app avaialable
* A user can be required to update the app before continuing if there is a critical update

## Improvements

* Improved feedback when entering an invalid password during registration
* A client can see the percentage of recent programs completed from their dashboard
* Added profile Icon to client dashboard avatar to indicate that tapping it will navigate to the client profile
* Replaces the "Today's Program" that was inside the program block and includes "# Exercises" where the # is the amount of exercises included in the program that it's referencing
* Removes "last login" information from the client dashboard and profile to replace with program completion metric

---

# 0.4.0

## New Features

* A trainer can add or delete exercises while editing a program
* A client can enter and track their weight over time
* A trainer can view instructional videos to help guide the client while performing an assessment

## Improvements

* A trainer can change the name of a program
* Provides more information on password requirements during trainer sign up
* When uploading a profile picture a user can choose to take a new picture in addition to choosing a picture from their photo library
* When an exercise video ends it will restart at the beginning in a paused state
* When the client’s calendar is expanded, the view will scroll to the first workout card and hide the client’s profile information

## Bug Fixes

* Fixes various UI display issues on smaller screen sizes
* Fixes issues with login and provides better error messages on login failure
* Fixes exercise description text formatted incorrectly
* Fixes an issue where the client invitation button would not activate after filling in all the required fields
* Fixes an issue that caused the client profile to get stuck in a loading state

## Other

* Updated app screenshots for iPhone X

---

# v0.2.3

### General

* Fixed issues with account registration
* Improved navigation flows

### iOS

* Improved iPhone X UI support

### Android

* Fixed Android loading spinners
* Fixed issues with Android page headers
* Improved Android back handler support

---

# v0.1.2

## Description

This is the first stable release and includes a majority of the UI development. The purpose of this build is to model the UI elements with some simple example data. There are no web services integrated so most interactions will not have any visible effect, and are just present to demonstrate the look and feel of the UI.

## Features

You can view a video overview of the included features here:

* https://vimeo.com/*********
* Password: n@sm2017

See the test plan for a detailed description of the features available in this version:
https://docs.google.com/a/medlmobile.com/spreadsheets/d/1hobV49MpUXyqZ4P4VkjS3dM5gDOQVPnSMj-Wy7DPto8/edit?usp=sharing
