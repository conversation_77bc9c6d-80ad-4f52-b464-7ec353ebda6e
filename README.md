# NASM Mobile App

## Build Status
Develop Branch:

**Note: The project has migrated from AppCenter to Firebase App Distribution. Builds are now handled by GitHub Actions.**

GitHub Actions Status: [![React Native Build & Deploy](https://github.com/YOUR_ORG/nasm/actions/workflows/firebase-deploy.yml/badge.svg)](https://github.com/YOUR_ORG/nasm/actions/workflows/firebase-deploy.yml)

_Legacy AppCenter Status (deprecated):_  
iOS: 
[![Build status](https://build.appcenter.ms/v0.1/apps/4f328cb2-815c-41d5-8a78-a6bbffe092f7/branches/develop/badge)](https://appcenter.ms)  
Android: 
[![Build status](https://build.appcenter.ms/v0.1/apps/74be5242-1348-4d3e-b488-ce28d497061a/branches/develop/badge)](https://appcenter.ms)  

## Firebase App Distribution

This project now uses Firebase App Distribution for distributing builds to testers. Refer to [FIREBASE_SETUP.md](FIREBASE_SETUP.md) for setup instructions.

## Overview

This project is built with React Native to support iOS and Android. React-Native
is an open source framework developed by Facebook that provides a javascript API
to access the native methods of each respective platform. This allows us to
build a cross platform app from a unified codebase. Unlike other cross platform
frameworks such as Cordova or Sencha which render HTML in a web view, React
Native leverages each platform's native UI layers, resulting in an app that
looks, feels and performs like a native app.

You can learn more about React Native here:
https://facebook.github.io/react-native/

## Dependencies

### Node.js

You'll need to install Node.js and its package manager npm in order to set up
and run this project. We recommend using the lateset LTS version of Node which
can be found here: https://nodejs.org/en/

### Xcode

The latest stable release of Xcode is required to build and run the iOS
project

### Android Studio

Android studio is required to build and run the Android project

## Installation

After cloning the project repository, open the root of the project in a
terminal. To confirm you're in the project root you should have a package.json
file in your current working directory.

You can now install the remaining dependencies with the following terminal
command: `npm install`

After this is complete you will need enter the iOS directory
command: `cd ios`

There should now be a `Podfile` in your current directory.
You can now install the pods required to build the iOS project
command: `pod install`

## Running on simulator

### iOS

1.  Use Xcode to open the iOS workspace file located at /ios/nasm.xcworkspace
2.  Select a simulator and run the project.
    - Xcode will launch a separate terminal window to run the React packager
      which manages a javascript bundle necessary to run the app

### Android

1.  Use Android Studio to open the project located at /android
2.  Use AVD to boot an Android simulator
3.  Open a terminal at the root of the repository and run `npm start`
    - This will start the react packager which manages a javascript bundle
      necessary to run the app
4.  Build and run the project using Android Studio

## Running on device

Running on device is fairly straightforward on iOS but can be a bit more
involved process on Android. We recommend following the instructions provided
at: https://facebook.github.io/react-native/docs/running-on-device.html

## Debugging

To debug this project you will need to download the Flipper
desktop application: https://fbflipper.com/
and run it along side the project.

To debug Redux you will need to install the `redux-debugger` plugin.
1. open Flipper
2. click `Plugin Manager` on the left bar
3. go to the `Install Plugins` tab
4. search `redux-debugger`
5. install and restart

## Tests

### End-to-End

End to end UI automation tests are provided through Detox: https://github.com/wix/detox  
All tests are located in ./e2e/  
To run the tests:

```
npm install
detox build
detox test
```

## Upgrade Notes:

This project was recently upgraded to use React Native 0.50 along with all
dependencies. Below are notes for some of the dependencies which were not
updated.

## Build Instructions:

The release bundle identifier is 'com.nasm.edge'.
The android keystore can be found within the project directory at /android/keystore/
The password and alias values are in the readme file in that directory.

## Release Instructions:

1. create release branch named `release/vX.X.X`
2. make sure version number is set in the fallowing places
    1. JS
        1. package.json
    2. iOS
        1. nasm target
        2. OneSignalNotificationServiceExtension target
    3. Android
        1. app/build.gradle
3. develop branch should have the version number incremented to the next minor version
4. configure release branch on the fallowing appcenter projects (you can clone the develop branch configuration)
    1. NASM Edge iOS - Store Release
    2. NASM Edge Android - Store Release
    3. (optional for QA builds) NASM Edge iOS - Staging
    4. (optional for QA builds) NASM Edge Android - Staging
    5. (optional for PR checks) NASM nasm-ios-dev
    6. (optional for PR checks) NASM nasm-android-dev
5. build store release projects on appcenter
6. after builds finish you can distribute to store from appcenter
    1. iOS distribute to appstore connect users
    2. Android distribute to alpha
7. iOS should become available automatically after processing, android will need to be published manually on the playstore console
