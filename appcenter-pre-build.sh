#!/usr/bin/env bash

# Note: This script is kept for backward compatibility
# The project is now using GitHub Actions for builds with Firebase App Distribution
# See .github/workflows/firebase-deploy.yml and FIREBASE_SETUP.md for details

if [ "$APPCENTER_BUILD_ID" ]; then
  echo "Updating android build id"
  # Updating ids

  buildGradle=./android/app/build.gradle

  sed -i '' "s/versionCode .*$/versionCode $APPCENTER_BUILD_ID/g" $buildGradle
  echo 'Updated android versionCode:'
  grep 'versionCode .*$' $buildGradle
fi