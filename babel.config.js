const path = require('path');

module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    'transform-export-extensions',
    '@babel/plugin-syntax-export-namespace-from',
    'transform-inline-environment-variables',
    'react-native-reanimated/plugin',
    ['module-resolver', {
      root: ['.'],
      resolvePath(sourcePath, currentFile) {
        if (sourcePath === 'react-native' && !currentFile.includes('node_modules/react-native/') && !currentFile.includes('resolver/react-native')) {
          return path.resolve(__dirname, 'resolver/react-native');
        }
        return undefined;
      },
    }],
  ],
  sourceMaps: 'inline',
};
